<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="60">
    <Properties>
        <Property name="LOG_HOME">logs</Property>
        <Property name="MAX_FILE_SIZE">100MB</Property>
        <Property name="MAX_ARCHIVE_FILES">30</Property>
        <Property name="CONSOLE_PATTERN">%style{%d{yyyy-MM-dd HH:mm:ss.SSS}}{cyan} %highlight{%-5level} %style{[%t]}{magenta} %style{%logger{36}:%L}{blue} %n- %msg%n</Property>
        <Property name="FILE_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%t] %c{1.}:%L - %msg%n</Property>
    </Properties>

    <Appenders>

        <!-- 同步 Console -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${CONSOLE_PATTERN}"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
        </Console>

        <!-- 同步文件 appenders -->
        <RollingFile name="InfoFile" fileName="${LOG_HOME}/info.log"
                     filePattern="${LOG_HOME}/info-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Filters>
                <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <SizeBasedTriggeringPolicy size="${MAX_FILE_SIZE}"/>
            </Policies>
            <DefaultRolloverStrategy max="${MAX_ARCHIVE_FILES}" fileIndex="min"/>
        </RollingFile>

        <RollingFile name="WarnFile" fileName="${LOG_HOME}/warn.log"
                     filePattern="${LOG_HOME}/warn-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Filters>
                <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <SizeBasedTriggeringPolicy size="${MAX_FILE_SIZE}"/>
            </Policies>
            <DefaultRolloverStrategy max="${MAX_ARCHIVE_FILES}" fileIndex="min"/>
        </RollingFile>
        <RollingFile name="ErrorFile" fileName="${LOG_HOME}/error.log"
                     filePattern="${LOG_HOME}/error-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <SizeBasedTriggeringPolicy size="${MAX_FILE_SIZE}"/>
            </Policies>
            <DefaultRolloverStrategy max="${MAX_ARCHIVE_FILES}" fileIndex="min"/>
        </RollingFile>


        <RollingFile name="CronFile" fileName="${LOG_HOME}/cron.log"
                     filePattern="${LOG_HOME}/cron-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="${MAX_FILE_SIZE}"/>
            </Policies>
            <DefaultRolloverStrategy max="${MAX_ARCHIVE_FILES}" fileIndex="min"/>
        </RollingFile>

        <!-- 异步包装 -->
        <Async name="AsyncInfo">
            <AppenderRef ref="InfoFile"/>
        </Async>

        <Async name="AsyncWarn">
            <AppenderRef ref="WarnFile"/>
        </Async>

        <Async name="AsyncError">
            <AppenderRef ref="ErrorFile"/>
        </Async>

        <Async name="AsyncCron">
            <AppenderRef ref="CronFile"/>
        </Async>


    </Appenders>

    <Loggers>
        <Logger name="HL.TNOA.Crontab" level="debug" additivity="false">
            <AppenderRef ref="AsyncCron"/>
        </Logger>

        <!-- Root 分级异步输出 -->
        <Root level="info">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AsyncInfo"/>
            <AppenderRef ref="AsyncWarn"/>
            <AppenderRef ref="AsyncError"/>
        </Root>

    </Loggers>
</Configuration>
