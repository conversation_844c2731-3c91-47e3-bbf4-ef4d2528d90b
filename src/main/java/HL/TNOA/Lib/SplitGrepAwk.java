package HL.TNOA.Lib;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SplitGrepAwk {
    List<String> values = new ArrayList<>();

    public SplitGrepAwk(String value) {
        String[] valuearr = value.trim().split("\n");
        for (String _value : valuearr) {
            values.add(_value);
        }
    }

    public List<String> get(){
        return values;
    }

    public List<String> grep(String patt, boolean word, boolean reverse) {
        String regex;
        if (word) {
            regex = "\\s+" + patt + "\\s+";
        } else {
            regex = patt;
        }
        Pattern pattern = Pattern.compile(regex);
        return grep(pattern, reverse);
    }

    public List<String> grep(Pattern pattern, boolean reverse) {
        List<String> removelist = new ArrayList<>();
        for (String _values : values) {
            Matcher matcher = pattern.matcher(_values);
            if (reverse) {
                if (matcher.find()) {
                    removelist.add(_values);
                }
            } else {
                if (!matcher.find()) {
                    removelist.add(_values);
                }
            }
        }

        if (removelist.size() == 0) {
            return values;
        }

        for (String _remove : removelist) {
            values.remove(_remove);
        }
        return values;
    }

    public List<String> awk(int i) {
        List<String> newvalues = new ArrayList<>();
        for (String _value : values) {
            String[] _values = _value.split("\\s+");
            if (_values.length <= i) {
                continue;
            }
//            if(_values.length != 6) {
//                System.out.println("[" + _values.length + "] -->" + _value);
//                System.out.println("[" + _values.length + "] -->" + _values[i]);
//                for (String _xx : _values) {
//                    System.out.println(_xx);
//                }
//            }
            newvalues.add(_values[i].trim());
        }

        values = newvalues;
        return values;
    }
}
