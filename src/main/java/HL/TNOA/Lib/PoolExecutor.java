package HL.TNOA.Lib;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class PoolExecutor {
    public Logger logger = LoggerFactory.getLogger(this.getClass());
    public HashMap<Runnable, Boolean> thread_pools;
    public ThreadPoolExecutor executor;
    public int corePoolSize = 1;
    public int maximumPoolSize = 1;
    public PoolExecutor(HashMap<Runnable, Boolean> thread_pools){
        this.thread_pools = thread_pools;
    }
    public boolean start(){
        if(thread_pools == null || thread_pools.size() <= 0){
            logger.error("error. thread is null.");
            return false;
        }

        for(Runnable _pool: thread_pools.keySet()){
            if(thread_pools.get(_pool)){
                corePoolSize++;
            }
            else{
                maximumPoolSize++;
                if(maximumPoolSize > 3){
                    maximumPoolSize = 3;
                }
            }
        }

        if(maximumPoolSize < 2){
            maximumPoolSize = 2;
        }
        if(maximumPoolSize < corePoolSize) {
            maximumPoolSize = corePoolSize;
        }
        executor = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 10000,
                TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(maximumPoolSize),
                Executors.defaultThreadFactory(),
                (r, executor) -> {
                    while(true){
                        if(executor.getQueue().size() >= maximumPoolSize){
                            Lib.sleep(100L);
                            continue;
                        }

                        executor.execute(r);
                        break;
                    }
                }) {

            protected void beforeExecute(Thread t, Runnable r) {
            }

            protected void afterExecute(Runnable r, Throwable t) {
                if (thread_pools.containsKey(r) && thread_pools.get(r)) {
                    logger.error("thread " + r.getClass() + " except aborted. start again");
                    Lib.sleep(1);
                    executor.execute(r);
                }
            }

            protected void terminated() {
            }
        };

        for(Runnable _pool: thread_pools.keySet()){
            executor.execute(_pool);
        }

        return true;
    }

    public int getActiveCount(){
        return executor.getActiveCount();
    }

    public void close(){
        executor.shutdown();
    }

}
