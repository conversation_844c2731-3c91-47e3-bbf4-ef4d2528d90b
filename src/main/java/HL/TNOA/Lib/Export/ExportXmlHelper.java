package HL.TNOA.Lib.Export;

import HL.TNOA.Lib.ShellExec;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

public class ExportXmlHelper implements ExportInterface{
    private BufferedWriter bw = null;
    private String path = null;
    @Override
    public void init(String path) throws Exception {
        this.path = path;
        bw = new BufferedWriter(new FileWriter(path));
        bw.write("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<DATAS>\n");
    }

    @Override
    public void close() {
        try {
            bw.write("</DATAS>\n");
        } catch (Exception e) {
        }

        try {
            bw.close();
        } catch (Exception e) {
        }

        String cmd = "chmod 777 " + path;
        ShellExec.exec(cmd, 5, true);
    }

    public List<String> header;
    public HashMap<String, String>headername;
    @Override
    public void write_head(List<String> header,
                           HashMap<String, String>headername) throws Exception {
        this.header = header;
        this.headername = headername;
    }

    public void set_sheet_capacity_list(List<Integer> sheet_capacity_list) {
    }

    @Override
    public void write_data(JSONArray data) throws Exception {
        Iterator<Object> it = data.iterator();
        while(it.hasNext()) {
            JSONObject _one = (JSONObject)it.next();
            String line = "<DATA>";
            for (String _key : header) {
                if (_one.containsKey(_key)) {
                    line += "<" + headername.get(_key) + ">" + _one.get(_key) +
                            "</" + headername.get(_key) + ">";
                } else {
                    line += "<" + headername.get(_key) + "></" + headername.get(_key) + ">";
                }
            }

            line += "</DATA>\n";

            bw.write(line);
        }
    }

    @Override
    public void write_data_num(JSONArray data) throws Exception {

    }

    @Override
    public void write_data(List<HashMap<String, String>> data) throws Exception {
        for(HashMap<String, String> _one: data) {
            String line = "<DATA>";
            for (String _key : header) {
                if (_one.containsKey(_key)) {
                    line += "<" + headername.get(_key) + ">" + _one.get(_key) +
                            "</" + headername.get(_key) + ">";
                } else {
                    line += "<" + headername.get(_key) + "></" + headername.get(_key) + ">";
                }
            }

            line += "</DATA>\n";

            bw.write(line);
        }
    }
}
