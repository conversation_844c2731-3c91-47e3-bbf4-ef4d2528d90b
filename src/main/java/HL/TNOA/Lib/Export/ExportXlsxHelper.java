package HL.TNOA.Lib.Export;

import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.ShellExec;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

public class ExportXlsxHelper implements ExportInterface{
    public Logger logger = LoggerFactory.getLogger(getClass());
    public BufferedOutputStream tmp_os = null;
    public BufferedOutputStream xls_os = null;
    public XSSFWorkbook tmp_work = null;
    public SXSSFWorkbook xls_work = null;
    public File tmpfs_file = null;
    public String path = null;
    public void init(String path) throws Exception{
        if(!new File("tmp").exists()){
            new File("tmp").mkdir();
        }
        if (!new File(path).getParentFile().exists()){
            new File(path).getParentFile().mkdirs();
        }
        if (!new File(path).exists()){
            new File(path).createNewFile();
        }

        String tmpfs = "tmp/" + System.currentTimeMillis() + ".xlsx";
        tmpfs_file = new File(tmpfs);
        tmp_os = new BufferedOutputStream(new FileOutputStream(tmpfs_file));
        tmp_work = new XSSFWorkbook();
        tmp_work.write(tmp_os);

        xls_work = new SXSSFWorkbook(tmp_work, 1000);
        this.path = path;
    }

    public void writeStream(OutputStream outputStream) throws Exception{
        xls_work.write(outputStream);
    }

    public void close() {
        try {
            xls_os = new BufferedOutputStream(new FileOutputStream(path));
            xls_work.write(xls_os);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            if (tmp_os != null) {
                IOUtils.closeQuietly(tmp_os);
            }
            if (xls_os != null) {
                IOUtils.closeQuietly(xls_os);
            }
            if (tmp_work != null) {
                IOUtils.closeQuietly(tmp_work);
            }
            if (xls_work != null) {
                IOUtils.closeQuietly(xls_work);
            }

            if (tmpfs_file != null && tmpfs_file.exists()) {
                tmpfs_file.delete();
            }

            String cmd = "chmod 777 " + path;
            ShellExec.exec(cmd, 5, true);
        }
    }

    List<String> header = null;
    HashMap<String, String> headername = null;
    public int index = 0;
    public SXSSFSheet sheet = null;
    int sheet_num = 1;
    public int onesheet_max = 100000;
    public void write_head(List<String> header,
                           HashMap<String, String> headername){
        this.header = header;
        this.headername = headername;
    }

    private void create_sheet() {
        sheet = xls_work.createSheet(sheet_num + "");
        sheet_num++;
        index = 0;
        Row row = sheet.createRow(index++);
        if (header != null) {
            int cellnum = 0;
            for (String _key : header) {
                Cell cell = row.createCell(cellnum++);
                if (headername.containsKey(_key)) {
                    cell.setCellValue(headername.get(_key));
                } else {
                    cell.setCellValue(_key);
                }
            }
        }
    }

    public void write_data(JSONArray data) {
        if (sheet == null) {
            create_sheet();
        }

        Iterator<Object> it = data.iterator();
        while (it.hasNext()) {
            JSONObject _one = (JSONObject) it.next();
            if (index >= onesheet_max) {
                create_sheet();
            }

            Row row = sheet.createRow(index++);
            int cellnum = 0;
            for (String _key : header) {
                Cell cell = row.createCell(cellnum++);
                if (_one.containsKey(_key)) {
                    cell.setCellValue(_one.get(_key).toString());
                } else {
                    cell.setCellValue("");
                }
            }
        }
    }

    @Override
    public void write_data_num(JSONArray data) throws Exception {

        if (sheet == null) {
            create_sheet();
        }

        Iterator<Object> it = data.iterator();
        while (it.hasNext()) {
            JSONObject _one = (JSONObject) it.next();
            if (index >= onesheet_max) {
                create_sheet();
            }

            Row row = sheet.createRow(index++);
            int cellnum = 0;
            for (String _key : header) {
                int cellnnn=cellnum++;


                DataFormat df = xls_work.createDataFormat();
                if (_one.containsKey(_key)) {

                    String val= _one.getString(_key);

                    if((_key.contains("name")))
                    {
                        Cell cell_str=row.createCell(cellnnn);
                        cell_str.setCellValue(_one.getString(_key));
                    }else if(_key.contains("code"))
                    {
                        Cell cell_str=row.createCell(cellnnn);
                        cell_str.setCellValue(_one.getString(_key));
                    }
                    else if(isNumeric(val)&&val.contains("."))
                    {
                        Cell cell_doub=row.createCell(cellnnn);
                        CellStyle cellStyle_doub=xls_work.createCellStyle(); // 创建单元格样式
                        cellStyle_doub.setDataFormat(df.getFormat("0.000_ "));//保留两位小数点

                        cell_doub.setCellStyle(cellStyle_doub);
                        cell_doub.setCellValue(_one.getDoubleValue(_key));
                    }else if(isNumeric(val)){
                        Cell cell_int = row.createCell(cellnnn);
                        CellStyle cellStyle_int=xls_work.createCellStyle(); // 创建单元格样式
                        cellStyle_int.setDataFormat(df.getFormat("0"));//数据格式只显示整数"_ "

                        cell_int.setCellStyle(cellStyle_int);
                        cell_int.setCellValue(_one.getInteger(_key));
                    }else {
                        Cell cell_str=row.createCell(cellnnn);
                        cell_str.setCellValue(_one.getString(_key));
                    }


                } else {
                    Cell cell_int = row.createCell(cellnnn);
                    CellStyle cellStyle_int=xls_work.createCellStyle(); // 创建单元格样式
                    cellStyle_int.setDataFormat(df.getFormat("0"));//数据格式只显示整数"_ "

                    cell_int.setCellStyle(cellStyle_int);
                    cell_int.setCellValue(0);
                }
            }
        }

    }
    public static boolean isNumeric(String str) {
        if (str == null || str.length() == 0) {
            return false;
        }
        return str.matches("-?\\d+(\\.\\d+)?");
    }

    @Override
    public void write_data(List<HashMap<String, String>> data) throws Exception {
        if (sheet == null) {
            create_sheet();
        }

        for (HashMap<String, String> _one : data) {
            if (index >= onesheet_max) {
                create_sheet();
            }

            Row row = sheet.createRow(index++);
            int cellnum = 0;
            for (String _key : header) {
                Cell cell = row.createCell(cellnum++);
                if (_one.containsKey(_key)) {
                    cell.setCellValue(_one.get(_key));
                } else {
                    cell.setCellValue("");
                }
            }
        }
    }
}
