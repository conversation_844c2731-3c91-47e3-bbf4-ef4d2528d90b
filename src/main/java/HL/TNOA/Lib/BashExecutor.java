package HL.TNOA.Lib;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

public class BashExecutor {
    private Logger logger = LoggerFactory.getLogger(BashExecutor.class);
    public String exec(String cmd, int timeout, boolean printflag){
        if(cmd.indexOf("|") >= 0 || cmd.indexOf(";") >= 0 ||
                cmd.indexOf("'") >= 0 || cmd.indexOf(">") >= 0 ||
                cmd.indexOf("&") >= 0 || cmd.indexOf("-") >= 0){
            return exec(new String[]{"/bin/bash", "-c", cmd}, timeout,
                    printflag, null, null);
        }
        else {
            return exec(cmd.split("\\s+"), timeout,
                    printflag, null, null);
        }
    }

    public String exec(String[] cmd, int timeout, boolean printflag,
                       String directory, Map<String, String> env){
        try {
            ProcessBuilder builder = new ProcessBuilder();
            if(directory != null) {
                builder.directory(new File(directory));
            }
            else{
                builder.directory(new File(System.getProperty("user.dir")));
            }

            if(env != null){
                Map<String, String> _env = builder.environment();
                for(String _key: env.keySet()){
                    _env.put(_key, env.get(_key));
                }
            }
            builder.command(cmd);
            builder.redirectErrorStream(true);
            Process process = builder.start();
            StringBuilder result = new StringBuilder();
            InputStream is = process.getInputStream();
            try {
                boolean isexit = false;
                long endtime = -1;
                if (timeout > 0) {
                    endtime = System.currentTimeMillis() + 1000L * timeout;
                }

                while (true) {
                    if(!process.isAlive()){
                        isexit = true;
                    }
//                    try {
//                        process.exitValue();
//                        isexit = true;
//                    }
//                    catch (Exception e){
//                    }

                    if(is.available() > 0){
                        byte[] buf = new byte[is.available()];
                        is.read(buf);
                        String linebuf = new String(buf);
                        if(printflag){
                            System.out.print(linebuf);
                        }
                        else{
                            result.append(linebuf);
                        }
                    }

                    if(isexit || (endtime > 0 && System.currentTimeMillis() > endtime)){
                        if(System.currentTimeMillis() > endtime){
                            if(!isexit) {
                                logger.error("exec (" + Arrays.asList(cmd) + ") timeout ==> " + result);
                            }
                            if(result.length() > 0){
                                return result.toString().trim();
                            }

                            return null;
                        }
                        break;
                    }

                    if(timeout > 0){
                        Lib.sleep(10L);
                    }
                    else{
                        Lib.sleep(300L);
                    }
                }
            } catch (IOException e) {
                logger.error(Lib.getTrace(e));
            } finally {
                try {
                    is.close();
                }
                catch (Exception e){}

                process.destroy();
                process.waitFor(5, TimeUnit.SECONDS);
                process.destroy();
            }
            return result.toString().trim();
        } catch (Exception e2) {
            logger.error(Lib.getTrace(e2));
        }

        return null;
    }

    public boolean grep(String patt, String logs){
        if(logs == null || logs.trim().length() == 0){
            return false;
        }

        String[] logsarr = logs.trim().split("\n");
        for(String _logs: logsarr){
            if(Pattern.matches(patt, _logs)){
                return true;
            }
        }

        return false;
    }
}
