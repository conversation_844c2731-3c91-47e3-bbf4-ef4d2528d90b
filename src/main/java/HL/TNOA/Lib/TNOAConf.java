package HL.TNOA.Lib;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TNOAConf {
    private static Logger logger = LoggerFactory.getLogger(TNOAConf.class);

    private static volatile ConcurrentHashMap<String, Properties> Conf = null;
    private String priv =
            "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCkDJeyWWyjVVufiIkH66wV2VggsqWj0BbLfJ7MeXpoEYSs9" +
                    "+RBPKGIWCqpiOBGGTXLXoPEutDljDSeSOlkOzutCfYBNAkrxvfm3UmHwDDxLVZGJCZb3shNUXdg4" +
                    "+qq4iqrSuyzl8E06Ki0BntZ0ilg19SsSt1zyIn/q037j7Dc4nsvMKxxJs69iVxgrh+3dgcmCfGDYoVQhCtCR2+nKl" +
                    "+YK4VBu2cJb/w6Pg1T9mt6fQ+/H39epBe5I/i3wAbBz7ycUyEZFDz487yUNg" +
                    "/UmdZSDhA2cMuXlvcF4KVzES6bgP5NkHkuHJayXEUG+mayWva4dG4OsWYRhWONf7gbrprNAgMBAAECggEAfiBcZTgRqYZP" + "+lkMGfXdCbNf6UvQxCCR1gNLSCRBs098tUulx25pNx2qN0xQOjTZwjE5mACV6VahgezG3FQaLpEQ" + "/Z3XSzwfjvUlumROfK8BvM3AXDPtxlxTL0AsRRO8AJSLZgzP8/DI/BhbsBQ/0gA+t/SbTJCrYNGBFF9GeJu90yOv+4QWpYJh" + "/9n2XLvxLoUDl7he7AdzQIw7HlmtV0X3iAhpzWVb5toc2HcIB8QrpgRja+8f/ubmb0s0qgKgcXZVs" + "+LBgTERaRh9qYeMPvbHfGGNQadB88Xq3fmOKDoSMAffR2B14TchCBkhX3JIIZOcryjw8dFiEtc5moPTwQKBgQDtex" + "/M8XR0nmeGr6LhkqeYgwv9T+CcZWDhuNXvQi8HCT6HJNmNuHqBpBvyf2olROfeCi++gbeMxj4FtPFXRuH" + "/uDcbbkf1mrUqzU05AJXSY0Kvs0gZxRPjBX8eaW/lp6DPfvWsGmz" + "+UPsqPzIUIalWDOETiIKjCJGN08upGZEDMQKBgQCw14ldoCQ6vtKejMC6fWGeqXa7LVMQECYPU9185" + "/pYuzZiTHU8LALruhlogXrCNuHmbOrzLN7oFm7Z96MEbEOs2IgfcgYvs98HhAcsCopZfR/V" + "+1jkSaGEue2K5kQw6sUdsJKQUNp+wpuwSc0fEdN43nHTLRWyxy0n1UO2ZHcSXQKBgD6a79Y6bSX1QHuMP9dRrycQnQ" + "+JiEASpci7zoJFo1oqgLZl64hl8r3MgqWaG2R51tp9Ovrw/W96L42XluwYPqoIaVas6wF" + "/aHVBLDNzyQtuqXziOYa5orV9lHZy+1HCsC0jXiahzMv6k0we3tfZy" + "+doe8mHelD6iEyoYg9RMx5xAoGAOaO3CUY2KNOjsLvt4ZsWY3GW2oF2bmjlDt/l9F1kMuGqYzXKjHdH6G968p4pVf4veTyNA" + "//fE8SkEvVtM/bZxA+7powyyf+VtUxqTgeNeoeXBdbr7VS2iZ1UeI5" + "/MXaEEx5HVzSTUve5aSdU7UiRJfdTIGK0jNXwuOgBLwTl7p0CgYEA1Z6zy6Lyph8vlk0uPWkIRATMWZzNvAnxinR8AMgm6juudUyOYvsoqHVoRWhblo+oTAZ7gtqo7+d1uJr2FX4kmEPrD/at8isKrZaUofDbMU4dXlQq0brdOcFDng7QokwgtXy6jEDDj8KeS2dXhTIkeCiHVkGY5IlRix8c+6G+57o=";

    private String currentSection;
    private Properties current;


    public boolean init() {
        try {
            String ConfFileName = "Soa.ini";
            HashSet<String> ConfileSet = new HashSet<String>() {{

                add("./config/");
            }};
            File conffile = null;

            for (String _conf : ConfileSet) {
                logger.warn(_conf + ConfFileName);
                conffile = new File(_conf + ConfFileName);
                if (conffile.exists()) {
                    break;
                }
            }

            if (!conffile.exists() || conffile.length() < 10) {
                logger.error("Cannot find SOA config file.");
                logger.error("Cannot find SOA config file.");
                logger.error("Cannot find SOA config file.");
                return false;
            }

            Conf = new ConcurrentHashMap<>();

            BufferedReader reader = new BufferedReader(new FileReader(conffile));
            try {
                String line = reader.readLine();

                if (line != null) {
                    byte[] lineb = line.getBytes(StandardCharsets.UTF_8);
                    // byte[] l = Base64Utils.decode(lineb);
                    // line = RSASignature.decrypt(priv, l);
                    byte[] linen = new byte[lineb.length];
                    if (lineb.length > 3) {
                        int a1 = Integer.valueOf(String.format("%x", lineb[0]), 16);
                        int a2 = Integer.valueOf(String.format("%x", lineb[1]), 16);
                        int a3 = Integer.valueOf(String.format("%x", lineb[2]), 16);
                        int a4 = Integer.valueOf(String.format("%x", lineb[3]), 16);

                        if (a1 == 0xEF && a2 == 0xbb && a3 == 0xbf) {
                            System.arraycopy(lineb, 3, linen, 0, lineb.length - 3);
                            line = new String(linen);
                        }
                        if (a1 == 0xFE && a2 == 0xFF) {
                            System.arraycopy(lineb, 2, linen, 0, lineb.length - 2);
                            line = new String(linen);
                        }
                        if (a1 == 0xFF && a2 == 0xFE) {
                            System.arraycopy(lineb, 2, linen, 0, lineb.length - 2);
                            line = new String(linen);
                        }
                        if (a1 == 0x00 && a2 == 0x00 && a3 == 0xfe && a4 == 0xff) {
                            System.arraycopy(lineb, 4, linen, 0, lineb.length - 4);
                            line = new String(linen);
                        }
                        if (a1 == 0xff && a2 == 0xfe && a3 == 0x00 && a4 == 0x00) {
                            System.arraycopy(lineb, 4, linen, 0, lineb.length - 4);
                            line = new String(linen);
                        }
                    } else if (lineb.length > 2) {
                        int a1 = Integer.valueOf(String.format("%x", lineb[0]), 16);
                        int a2 = Integer.valueOf(String.format("%x", lineb[1]), 16);

                        if (a1 == 0xFE && a2 == 0xFF) {
                            System.arraycopy(lineb, 2, linen, 0, lineb.length - 2);
                            line = new String(linen);
                        }
                        if (a1 == 0xFF && a2 == 0xFE) {
                            System.arraycopy(lineb, 2, linen, 0, lineb.length - 2);
                            line = new String(linen);
                        }
                    }

                    line = line.trim();
                    if (line.length() >= 2 && line.charAt(0) != '#') {
                        parseLine(line);
                    }

                    while ((line = reader.readLine()) != null) {
                        line = line.trim();

                        lineb = line.getBytes(StandardCharsets.UTF_8);
                        // l = Base64Utils.decode(lineb);
                        // line = RSASignature.decrypt(priv, l);
                        //logger.warn(line);
                        if (line.length() >= 2 && line.charAt(0) != '#') {
                            parseLine(line);
                        }
                    }
                    reader.close();

                    if (current != null) {
                        Conf.put(currentSection, current);
                    }
                }
                return true;
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            } finally {
                if (reader != null) {
                    Lib.QuietClose(reader);
                }
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }
        logger.warn(Conf.toString());
        System.out.println(Conf.toString());
        return false;
    }

    private void parseLine(String line) throws Exception {
        line = line.trim();
        if (line.matches("\\[.*\\]")) {
            if (current != null) {
                Conf.put(currentSection, current);
            }
            currentSection = line.replaceFirst("\\[(.*)\\]", "$1");
            current = null;
        } else if (line.matches(".*=.*")) {
            int i = line.indexOf('=');
            String name = line.substring(0, i);
            String value = line.substring(i + 1);

            //解析变量
            Pattern pattern = Pattern.compile("\\{\\{[\\d\\w\\.]*\\}\\}");
            Matcher matcher = pattern.matcher(value);
            while (matcher.find()) {
                value = value.replace(matcher.group(), Conf.get("gobal").getProperty(matcher.group().substring(2,
                        matcher.group().length() - 2)));
            }

            if (current == null) {
                current = new Properties();
            }
            current.setProperty(name, value);
        }
    }

    public static Map<String, Properties> get() {
        if (Conf == null) {
            if (!new TNOAConf().init()) {
                return null;
            }
        }

        return Conf;
    }

    public static Properties get(String section) {
        if (Conf == null) {
            if (!new TNOAConf().init()) {
                return null;
            }
        }

        if (Conf.containsKey(section)) {
            return Conf.get(section);
        }

        return null;
    }

    public static String get(String section, String name) {
        if (Conf == null) {
            if (!new TNOAConf().init()) {
                return null;
            }
        }
        Properties p = Conf.get(section);
        if (p == null) {
            return null;
        }
        return p.getProperty(name);
    }

    public static int getInt(String section, String name) {
        if (Conf == null) {
            if (!new TNOAConf().init()) {
                return 0;
            }
        }
        Properties p = Conf.get(section);
        if (p == null) {
            return 0;
        }
        int back = Integer.parseInt(p.getProperty(name));
        return back;
    }

    public static boolean has(String section, String name) {
        if (Conf == null) {
            if (!new TNOAConf().init()) {
                return false;
            }
        }

        if (name == null) {
            return Conf.containsKey(section);
        } else {
            if (!Conf.containsKey(section)) {
                return false;
            } else {
                return Conf.get(section).containsKey(name);
            }
        }
    }
}
