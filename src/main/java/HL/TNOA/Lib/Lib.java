package HL.TNOA.Lib;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Closeable;
import java.io.File;
import java.net.Inet4Address;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.text.SimpleDateFormat;
import java.util.*;

public class Lib {
    private static Logger logger = LoggerFactory.getLogger(Lib.class);

    public static String getTrace(Throwable e) {
        String startwith = null;
        if (TNOAConf.get("basic", "exception.startwith").length() > 0
                && !TNOAConf.get("basic", "exception.startwith").equals("*")) {
            startwith = TNOAConf.get("basic", "exception.startwith");
        }
        String errorlog = e.toString();
        StackTraceElement[] trace = e.getStackTrace();
        for (StackTraceElement traceElement : trace) {
            if (startwith != null) {
                if (!traceElement.toString().startsWith(startwith)) {
                    continue;
                }
            }
            errorlog += "\n\t\t " + traceElement;
        }
        return errorlog;
    }

    private static int Os = -1;
    public static final int Linux = 0;
    public static final int Windows = 1;

    public static int getOs() {
        if (Os == -1) {
            Properties prop = System.getProperties();
            String os = prop.getProperty("os.name");

            if (os != null && os.toLowerCase().contains("linux")) {
                Os = Linux;
            } else {
                Os = Windows;
            }
        }
        return Os;
    }

    public static String getVersion() {
        Properties pro = new Properties();
        try {
            pro.load(Lib.class.getClassLoader()
                    .getResourceAsStream("META-INF/maven/HL.TNOA/TNOAServ/pom.properties"));
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }

        if (pro.containsKey("version")) {
            return pro.getProperty("version");
        }
        return "0.0.0";
    }

    public static Set<String> localips = null;

    public static Set<String> getLocalIps() {
        if (localips == null) {
            localips = new HashSet<>();
            try {
                //获取当前环境下的所有网卡
                Enumeration<?> netInterfaces = NetworkInterface.getNetworkInterfaces();
                while (netInterfaces.hasMoreElements()) {
                    NetworkInterface ni = (NetworkInterface) netInterfaces.nextElement();
                    //获取网卡下所有ip
                    Enumeration<?> cardipaddress = ni.getInetAddresses();
                    while (cardipaddress.hasMoreElements()) {//将网卡下所有ip地址取出
                        InetAddress ip = (InetAddress) cardipaddress.nextElement();
                        //过滤ipv6地址
                        if (ip instanceof Inet6Address) {
                            continue;
                        }
                        //返回ipv4地址
                        if (ip instanceof Inet4Address) {
                            localips.add(ip.getHostAddress());
                        }
                    }
                }
            } catch (Exception e) {
                logger.error(Lib.getTrace(e));
            }
        }

        return localips;
    }

    public static void sleep(int second) {
        try {
            Thread.sleep(second * 1000L);
        } catch (Exception e) {
        }
    }

    public static void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (Exception e) {
        }
    }

    public static void QuietClose(Closeable close) {
        try {
            close.close();
        } catch (Exception e) {
        }
    }

    /**
     * 获取当前时间戳
     *
     * @return timestamp
     */
    public static String getTimestamp() {
        return String.valueOf(new Date().getTime() / 1000);
    }

    public static String getTimeString(long timestamp) {
        return getTimeString("yyyy-MM-dd HH:mm:ss", timestamp);
    }

    public static String getTimeString(String format, long timestamp) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.format(new Date(timestamp));
    }

    private static String javapath = null;

    public static String getJavaPath() {
        if (javapath == null) {
            javapath = System.getenv("JAVA_HOME") +
                    File.separator + "bin" + File.separator;
        }

        return javapath;
    }

    private static String jpspath = null;

    public static String getJpspath() {
        if (jpspath == null) {
            jpspath = getJavaPath() + File.separator + "jps -lv ";
        }
        return jpspath;
    }

    public static String getRedirect() {
        return "|";
    }

    private static String awk = null;

    public static String getAwk() {
        if (awk == null) {
            awk = " awk ";
        }

        return awk;
    }

    public static String getAwk(int field) {
        return getAwk() + " '{print $" + field + "}'";
    }

    private static String find = null;

    public static String getFind() {
        if (find == null) {
            find = " grep ";
        }

        return find;
    }

    public static String getFind(String str) {
        return getFind() + "\"" + str + "\"";
    }

    public static String getNoFind(String str) {
        return getFind() + "/V\" " + str + "\"";
    }

    private static String kill = null;

    public static String getKill(String pid) {
        if (kill == null) {
            kill = "kill -9 ";
        }

        return kill + pid;
    }

}
