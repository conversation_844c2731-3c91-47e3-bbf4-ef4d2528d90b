package HL.TNOA.Lib.DataModel;//package HL.SCI.Lib.DataModule;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import HL.SCI.Lib.ErrNo;
//import HL.SCI.Lib.InfoModule.InfoModelHelper;
//import HL.SCI.Lib.Lib;
//import HL.SCI.Lib.SciConf;
//import hl.SCI.Httpd.Controller.InfoUtils;
//import hl.SCI.Httpd.javajwt.OaHttpRequest;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.io.BufferedReader;
//import java.io.BufferedWriter;
//import java.io.InputStreamReader;
//import java.io.OutputStreamWriter;
//import java.net.Socket;
//import java.text.SimpleDateFormat;
//import java.util.List;
//import java.util.Map;
//import java.util.Random;
//import java.util.Set;
//
//public class DataHlcmImpl implements DataModelHelper {
//	private Logger logger = LoggerFactory.getLogger(this.getClass().getName());
//	public final String PackEndWith = "HuA@#$lOnG";
//	public final String PackEndWithN = PackEndWith + "\r\n";
//	private Socket socket = null;
//	private BufferedReader br = null;
//	private BufferedWriter bw = null;
//	private int shard;
//	private int replicate;
//
//	@Override
//	public boolean init() throws Exception {
//		shard = SciConf.getInt("hlcm", "shard");
//		replicate = SciConf.getInt("hlcm", "replicate");
//
//		close();
//		socket = new Socket(SciConf.get("hlcm", "addr"), SciConf.getInt("hlcm", "port"));
//		socket.setSoTimeout(20000);
//		socket.setSendBufferSize(100000);
//		socket.setReceiveBufferSize(100000);
//		socket.setTcpNoDelay(true);
//
//		br = new BufferedReader(new InputStreamReader(socket.getInputStream()));
//		bw = new BufferedWriter(new OutputStreamWriter(socket.getOutputStream()));
//		if (tp_begin != null) {
//			hlcmhelper(tp_begin);
//		}
//		return true;
//	}
//
//	@Override
//	public boolean isClose() {
//		try {
//			JSONObject ret = hlcmhelper(null);
//			if (ret == null || !ret.containsKey("heartbeat")) {
//				return true;
//			}
//			return false;
//		} catch (Exception e) {
//			logger.warn(Lib.getTrace(e));
//			return true;
//		}
//	}
//
//	@Override
//	public void close() {
//		if (br != null) {
//			try {
//				br.close();
//			} catch (Exception e) {
//			}
//		}
//		if (bw != null) {
//			try {
//				bw.close();
//			} catch (Exception e) {
//			}
//		}
//		if (socket != null) {
//			try {
//				socket.close();
//			} catch (Exception e) {
//			}
//		}
//	}
//
//	public JSONObject jsonsend(JSONObject senddata) throws Exception {
//		bw.write(senddata.toString() + PackEndWithN);
//		bw.flush();
//		String readline = "";
//		while (true) {
//			String line = br.readLine();
//			readline += line;
//			if (readline.endsWith(PackEndWith)) {
//				JSONObject retjson = JSONObject
//						.parseObject(readline.substring(0, readline.length() - PackEndWith.length()));
//				return retjson;
//			}
//
//			readline += "\r\n";
//			if (readline.length() > 50000) {
//				break;
//			}
//		}
//		return null;
//	}
//
//	public JSONObject hlcmhelper(JSONObject hlcm) throws Exception {
//		if (hlcm == null) {
//			JSONObject heartbeat = new JSONObject();
//			heartbeat.put("heartbeat", "ok");
//			return jsonsend(heartbeat);
//		} else {
//			for (int _i = 0; _i < 2; _i++) {
//				try {
//					JSONObject operate = new JSONObject();
//					operate.put("operate", "H1");
//					operate.put(SciConf.get("hlcm", "data"), hlcm);
//					return jsonsend(operate);
//				} catch (Exception e) {
//					logger.error(Lib.getTrace(e));
//					init();
//				}
//			}
//		}
//		return null;
//	}
//
//	// 待完善
//	@Override
//	public void create_table_data_tables() throws Exception {
//		this.init();
//		JSONObject json = new JSONObject();
//		JSONObject data = new JSONObject();
//		json.put("operate", "create");
//
//		json.put("table", "data_tables");
//		json.put("shard", shard);
//		json.put("replicate", replicate);
//
//		// 动态
//		JSONObject _str = new JSONObject();
//		_str.put("type", "string");
//		_str.put("index", true);
//		data.put("*_str", _str);
//
//		JSONObject next_current_node = new JSONObject();
//		next_current_node.put("type", "string");
//		next_current_node.put("index", true);
//		data.put("next_current_node", next_current_node);
//
//		JSONObject flow_data = new JSONObject();
//		flow_data.put("type", "string");
//		flow_data.put("index", true);
//		data.put("flow_data", flow_data);
//
//		JSONObject _strs = new JSONObject();
//		_strs.put("type", "string");
//		_strs.put("index", true);
//		_strs.put("multiple", true);
//		data.put("*_strs", _strs);
//
//		JSONObject _int = new JSONObject();
//		_int.put("type", "int");
//		_int.put("index", true);
//		data.put("*_int", _int);
//
//		JSONObject _ints = new JSONObject();
//		_ints.put("type", "int");
//		_ints.put("index", true);
//		_ints.put("multiple", true);
//		data.put("*_ints", _ints);
//
//		JSONObject _long = new JSONObject();
//		_long.put("type", "long");
//		_long.put("index", true);
//		data.put("*_long", _long);
//
//		JSONObject _double = new JSONObject();
//		_double.put("type", "double");
//		_double.put("index", true);
//		data.put("*_double", _double);
//
//		JSONObject _date = new JSONObject();
//		_date.put("type", "datetime");
//		_date.put("index", true);
//		data.put("*_date", _date);
//
//		JSONObject over_time = new JSONObject();
//		over_time.put("type", "datetime");
//		over_time.put("index", true);
//		data.put("over_time", over_time);
//
//		JSONObject _dates = new JSONObject();
//		_dates.put("type", "datetime");
//		_dates.put("index", true);
//		_dates.put("multiple", true);
//		data.put("*_dates", _dates);
//
//		JSONObject _bool = new JSONObject();
//		_bool.put("type", "boolean");
//		_bool.put("index", true);
//		data.put("*_bool", _bool);
//
//		JSONObject _bools = new JSONObject();
//		_bools.put("type", "boolean");
//		_bools.put("index", true);
//		_bools.put("multiple", true);
//		data.put("*_bools", _bools);
//
//		JSONObject _text = new JSONObject();
//		_text.put("type", "text");
//		_text.put("index", true);
//		data.put("*_text", _text);
//
//		JSONObject _texts = new JSONObject();
//		_texts.put("type", "text");
//		_texts.put("index", true);
//		_texts.put("multiple", true);
//		data.put("*_texts", _texts);
//
//		JSONObject user_name = new JSONObject();
//		user_name.put("type", "string");
//		user_name.put("index", true);
//		data.put("user_name", user_name);
//
//		JSONObject user_id = new JSONObject();
//		user_id.put("type", "int");
//		user_id.put("index", true);
//		data.put("user_id", user_id);
//
//		JSONObject form_id = new JSONObject();
//		form_id.put("type", "long");
//		form_id.put("index", true);
//		data.put("form_id", form_id);
//
//		JSONObject approved_users = new JSONObject();
//		approved_users.put("type", "string");
//		approved_users.put("index", true);
//		data.put("approved_users", approved_users);
//
//		JSONObject next_users = new JSONObject();
//		next_users.put("type", "string");
//		next_users.put("index", true);
//		data.put("next_users", next_users);
//
//		JSONObject ndcopy_users = new JSONObject();
//		ndcopy_users.put("type", "string");
//		ndcopy_users.put("index", true);
//		data.put("ndcopy_users", ndcopy_users);
//
//		JSONObject readed_users = new JSONObject();
//		readed_users.put("type", "string");
//		readed_users.put("index", true);
//		data.put("readed_users", readed_users);
//
//		json.put("data", data);
//
//		JSONObject ret = hlcmhelper(json);
//		if (ret == null || ret.getIntValue("errno") != 0) {
//			logger.error("error:" + ret);
//		}
//		this.close();
//
//	}
//
//	//插入数据
//	@Override
//	public JSONObject insert_form(OaHttpRequest request) throws Exception {
//		InfoModelHelper info = request.openInfoImpl();
//		DataModelHelper dataimpl=request.openDataImpl();
//		JSONObject ret;
//			JSONObject json = new JSONObject();
//			JSONObject reqJsonObject = request.getRequestParams();
//			if (reqJsonObject.containsKey("form_id")) {
//				reqJsonObject.remove("form_id");
//			}
//			StringBuffer form_id = randoms();
//			long formid = Long.valueOf(form_id.substring(0));
//			reqJsonObject.put("form_id", formid);
//			String type = null;
//			String nextusers = null;
//			String nextusers2 = null;
//			String table_id = reqJsonObject.getString("table_id_long");
//			String sql = "select * from web_tables where table_id='" + table_id + "'";
//			List<JSONObject> list = info.query(sql);
//			if (list.size() == 0) {
//				return ErrNo.set(401000);
//			}
//			JSONObject flow_json = list.get(0).getJSONObject("flow_data");
//			JSONArray flow_list = flow_json.getJSONArray("list");
//			if (flow_list.size() <= 2) {
//				return ErrNo.set(401000);
//			}
//			if ("1".equals(reqJsonObject.getString("status_str"))) {
//				JSONObject sjson = flow_list.getJSONObject(1);
//				//安排next_users
//				nextusers = InfoUtils.getNextUsers(request, 0, "");
//				if ("approval".equals(sjson.getString("type"))) {
//					String next_current_node = sjson.getString("current_node");
//					reqJsonObject.put("next_users", nextusers);
//					reqJsonObject.put("next_current_node", next_current_node);
//					type = "approval";
//				}
//				if ("copy".equals(sjson.getString("type"))) {
//					reqJsonObject.put("ndcopy_users", nextusers);
//					type = "copy";
//					nextusers2 = InfoUtils.getNextUsers(request, 1, "");
//					reqJsonObject.put("next_users", nextusers2);
//					reqJsonObject.put("next_current_node", "1");
//				}
//			}
//			//	//创建一个数据,用于存数据
//			Object[] data = new Object[1];
//			data[0] = reqJsonObject;
//			json.put("operate", "insert");
//			json.put("table", "data_tables");
//			json.put("data", data);
//			json.put("commit", "true");
//			ret = dataimpl.hlcmhelper(json);
//			if (ret == null || ret.getIntValue("errno") != 0) {
//				logger.error("error:" + ret);
//				return ErrNo.set(ret, 0);
//			} else {
//				if ("approval".equals(type)) {
//					insert_notice_tab(request, formid, nextusers, type);
//				}
//				if ("copy".equals(type)) {
//					insert_notice_tab(request, formid, nextusers, type);
//					String type2 = "approval";
//					insert_notice_tab(request, formid, nextusers2, type2);
//				}
//			}
//			return ret;
//	}
//
//
//	@Override
//	public boolean exists_table(String tablename) throws Exception {
//		this.init();
//		JSONObject hlcm = new JSONObject();
//		hlcm.put("operate", "exist");
//		hlcm.put("table", tablename);
//		JSONObject ret = hlcmhelper(hlcm);
//		this.close();
//		if (ret != null && ret.getIntValue("errno") == 0 && ret.getBoolean("exist")) {
//			return true;
//
//		} else {
//			logger.error("error:" + ret);
//			return false;
//		}
//
//	}
//
//	@Override
//	public int delete_table(String tablename) throws Exception {
//		JSONObject hlcm = new JSONObject();
//		hlcm.put("operate", "drop");
//		hlcm.put("table", tablename);
//		this.init();
//		JSONObject ret = hlcmhelper(hlcm);
//		this.close();
//		if (ret != null && ret.getIntValue("errno") == 0 && ret.getBoolean("exist")) {
//			return 0;
//		} else {
//			logger.error("error:" + ret);
//			return -1;
//		}
//	}
//
//
//	JSONArray dataarr = null;
//	JSONObject tp_begin = null;
//
////	@Override
////	public boolean save_init(String tablename) throws Exception {
////		return save_init(tablename, 200);
////	}
//
////	@Override
////	public boolean save_init(String tablename, int syncMacCount) throws Exception {
////        String tablename;
////        try {
////            JSONObject tp_begin = new JSONObject();
////            tp_begin.put("operate", "tp_begin");
////            tp_begin.put("table", tablename);
////            tp_begin.put("syncCount", syncMacCount);
////
////            JSONObject ret = hlcmhelper(tp_begin);
////            if(ret != null && ret.getInt("errno") == 0){
////                return true;
////            }
////            else{
////                logger.error("error:" + ret);
////            }
////        }
////        catch (Exception e){
////            logger.error(GetTraceInfo.get(e));
////        }
////		return false;
////	}
//
////	@Override
////	public boolean save_data(JSONObject data) throws Exception {
////		if (dataarr == null) {
////			dataarr = new JSONArray();
////		}
////		dataarr.add(data);
////
////		if (dataarr != null && dataarr.size() >= 50) {
////			JSONObject hlcm = new JSONObject();
////			hlcm.put("operate", "tp_data");
////			hlcm.put("data", dataarr);
////
////			JSONObject ret = hlcmhelper(hlcm);
////			dataarr = null;
////			if (ret != null && ret.getIntValue("errno") == 0) {
////				return true;
////			} else {
////				logger.error("error:" + ret);
////			}
////		}
////
////		return true;
////	}
////
////	@Override
////	public boolean save_sync() throws Exception {
////		if (dataarr != null && dataarr.size() > 0) {
////			JSONObject hlcm = new JSONObject();
////			hlcm.put("operate", "tp_data");
////			hlcm.put("data", dataarr);
////
////			JSONObject ret = hlcmhelper(hlcm);
////			dataarr = null;
////			if (ret != null && ret.getIntValue("errno") == 0) {
////			} else {
////				logger.error("error:" + ret);
////			}
////		}
////
////		JSONObject hlcm = new JSONObject();
////		hlcm.put("operate", "tp_commit");
////
////		JSONObject ret = hlcmhelper(hlcm);
////		if (ret != null && ret.getIntValue("errno") == 0) {
////			return true;
////		} else {
////			logger.error("error:" + ret);
////		}
////
////		return false;
////	}
////
////	@Override
////	public void save_exit() {
////		try {
////			if (dataarr != null && dataarr.size() > 0) {
////				JSONObject hlcm = new JSONObject();
////				hlcm.put("operate", "tp_data");
////				hlcm.put("data", dataarr);
////
////				JSONObject ret = hlcmhelper(hlcm);
////				dataarr = null;
////				if (ret != null && ret.getIntValue("errno") == 0) {
////				} else {
////					logger.error("error:" + ret);
////				}
////			}
////
////			JSONObject hlcm = new JSONObject();
////			hlcm.put("operate", "tp_end");
////
////			JSONObject ret = hlcmhelper(hlcm);
////			if (ret != null && ret.getIntValue("errno") == 0) {
////			} else {
////				logger.error("error:" + ret);
////			}
////		} catch (Exception e) {
////			logger.error(Lib.getTrace(e));
////		}
////	}
//
//
//
//	public static StringBuffer randoms() {
//		Random r = new Random();
//		int randNum = r.nextInt(99) + 15;
//		int send[] = { 1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 12, randNum };
//		int temp1, temp2, temp3;
//		for (int i = 0; i < send.length; i++) {
//			temp1 = Math.abs(r.nextInt()) % (send.length - 1);
//			temp2 = Math.abs(r.nextInt()) % (send.length - 1);
//			if (temp1 != temp2) {
//				temp3 = send[temp1];
//				send[temp1] = send[temp2];
//				send[temp2] = temp3;
//			}
//		}
//		StringBuffer buf = new StringBuffer();
//		for (int i = 0; i < send.length; i++) {
//
//			buf.append(send[i]);
//		}
//		return buf;
//	}
//
//	@Override
//	public JSONObject query_form_by_formid(OaHttpRequest request, long form_id) throws Exception {
//		JSONObject reqJsonObject = request.getRequestParams();
//		Map<String, String[]> map = request.getParameterMap();
//		long formid = 0;
//		while (true) {
//			if (form_id != 0) {
//				formid = form_id;
//				break;
//			}
//			if (map == null || map.isEmpty()) {
//
//				if (reqJsonObject.containsKey("opt") && "approve".equals(reqJsonObject.getString("opt"))) {
//
//					formid = reqJsonObject.getLongValue("form_id");
//					break;
//				}
//
//				formid = reqJsonObject.getLongValue("form_id");
//				break;
//			}
//
//			if (reqJsonObject == null || reqJsonObject.isEmpty()) {
//				formid = Long.valueOf(map.get("form_id")[0]);
//				break;
//			}
//
//		}
//
//		JSONObject json = new JSONObject();
//		JSONObject where = new JSONObject();
//		json.put("operate", "select");
//		json.put("table", "data_tables");
//		where.put("opt", "equal");
//		where.put("key", "form_id");
//		where.put("value", formid);
//		json.put("where", where);
//		DataModelHelper dataimpl=request.openDataImpl();
//		JSONObject ret = dataimpl.hlcmhelper(json);
//		if (ret == null || ret.getIntValue("errno") != 0) {
//			logger.error("error:" + ret);
//		}
//		return ret;
//	}
//
//
//
//	@Override
//	public void create_table_notice_tables() throws Exception {
//		this.init();
//		JSONObject json = new JSONObject();
//		JSONObject data = new JSONObject();
//		json.put("operate", "create");
//		json.put("table", "notice_tables");
//		json.put("shard", shard);
//		json.put("replicate", replicate);
//		// 动态
//		JSONObject notice_id = new JSONObject();
//		notice_id.put("type", "long");
//		notice_id.put("index", true);
//		data.put("notice_id", notice_id);
//		JSONObject form_id = new JSONObject();
//		form_id.put("type", "long");
//		form_id.put("index", true);
//		data.put("form_id", form_id);
//		JSONObject ndsend_users = new JSONObject();
//		ndsend_users.put("type", "string");
//		ndsend_users.put("index", true);
//		data.put("ndsend_users", ndsend_users);
//		JSONObject content = new JSONObject();
//		content.put("type", "string");
//		content.put("index", true);
//		data.put("content", content);
//
//		JSONObject notice_time = new JSONObject();
//		notice_time.put("type", "string");
//		notice_time.put("index", true);
//		data.put("notice_time", notice_time);
//
//		JSONObject type = new JSONObject();
//		type.put("type", "string");
//		type.put("index", true);
//		data.put("type", type);
//
//		json.put("data", data);
//
//		JSONObject ret = hlcmhelper(json);
//		if (ret == null || ret.getIntValue("errno") != 0) {
//			logger.error("error:" + ret);
//		}
//		this.close();
//	}
//
//	@Override
//	public void insert_notice_tab(OaHttpRequest request, long form_id, String ndsend_users, String type)
//			throws Exception {
//		InfoModelHelper info=request.openInfoImpl();
//		DataModelHelper hlcm = request.openDataImpl();
//			java.util.Date date = new java.util.Date();
//			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//			String notice_time = sdf.format(date);
//			JSONObject result = hlcm.query_form_by_formid(request, form_id);
//			JSONArray data2 = result.getJSONArray("data");
//			JSONObject form_data = data2.getJSONObject(0);
//			//		String applicat_id = form_data.getString("user_id");
//			String name_str = form_data.getString("name_str");
//			JSONObject json = new JSONObject();
//			JSONObject reqJsonObject = new JSONObject();
//			StringBuffer notice_id = randoms();
//			long noticeid = Long.valueOf(notice_id.substring(0));
//			reqJsonObject.put("notice_id", noticeid);
//			reqJsonObject.put("form_id", form_id);
//			reqJsonObject.put("ndsend_users", ndsend_users);
//			reqJsonObject.put("type", type);
//			reqJsonObject.put("notice_time", notice_time);
//			//		boolean isAll, String ding_id, String message******JSONObject ding_notice_text
//			JSONObject usersjson = JSONObject.parseObject(ndsend_users);
//			Set<String> keys = usersjson.keySet();
//			String ding_id = "";
//			String sql = "";
//			List<JSONObject> ding;
//			JSONObject dingJson;
//			for (String key : keys) {
//				sql = "select ding_id from user where id='" + key + "'";
//				ding = info.query(sql);
//				dingJson = ding.get(0);
//				String dingid = dingJson.getString("ding_id");
//				dingid += ",";
//				ding_id += dingid;
//			}
//			ding_id = ding_id.substring(0, ding_id.length() - 1);
//			if ("result".equals(type)) {
//				String content = "你的" + name_str + "审批结果待查看";
//				reqJsonObject.put("content", content);
//				//			**********
////				DingApi.ding_notice_text(false, ding_id, content);
//			}
//			if ("approval".equals(type)) {
//				String content = name_str + "待审批";
//				reqJsonObject.put("content", content);
//				//			**********
////				DingApi.ding_notice_text(false, ding_id, content);
//			}
//			if ("copy".equals(type)) {
//				String content = name_str + "待阅读";
//				reqJsonObject.put("content", content);
//				//			**********
////				DingApi.ding_notice_text(false, ding_id, content);
//			}
//			Object[] data = new Object[1];
//			data[0] = reqJsonObject;
//			//
//			json.put("operate", "insert");
//			//
//			json.put("table", "notice_tables");
//			json.put("data", data);
//			json.put("commit", "true");
//			JSONObject ret = hlcm.hlcmhelper(json);
//			if (ret == null || ret.getIntValue("errno") != 0) {
//				logger.error("error:" + ret);
//			}
//
//	}
//
//	@Override
//	public void insert_flowdata_to_datatab(OaHttpRequest request, String orcurrent_node) throws Exception {
//		JSONObject reqJsonObject = request.getRequestParams();
//		DataModelHelper hlcm = request.openDataImpl();
//		long form_id0 = 0;
//		JSONObject form = hlcm.query_form_by_formid(request, form_id0);
//		JSONArray data3 = form.getJSONArray("data");
//		JSONObject form_data = data3.getJSONObject(0);
//		long form_id = reqJsonObject.getLongValue("form_id");
//		JSONObject data = new JSONObject();
//		JSONObject send = new JSONObject();
//		// 存flow_data
//		String result = reqJsonObject.getString("result");
//		String remarks = reqJsonObject.getString("remarks");
//		String username = reqJsonObject.getString("username");
//		String approver_time = reqJsonObject.getString("approver_time");
//		data.put("result", result);
//		data.put("remarks", remarks);
//		data.put("username", username);
//		data.put("approver_time", approver_time);
//		if (reqJsonObject.containsKey("opt") && "frontapprove".equals(reqJsonObject.getString("opt"))) {
//			data.put("opt", "frontapprove");
//		}
//		if (!form_data.containsKey("flow_data")) {
//			JSONArray xjsonArray = new JSONArray();
//			xjsonArray.add(data);
//			String flow_data = xjsonArray.toJSONString();
//			send.put("flow_data", flow_data);
//		}
//		// 存flow_data
//		else {
//			JSONArray jsonArray = form_data.getJSONArray("flow_data");
//			jsonArray.add(data);
//			String flow_data = jsonArray.toJSONString();
//			send.put("flow_data", flow_data);
//		}
//
//		// 存approved_users
//		JSONArray array = new JSONArray();
//		String approver_id = reqJsonObject.getString("approver_id");
//
//			if (!form_data.containsKey("approved_users")) {
//				JSONObject approved_users = new JSONObject();
//				if (!orcurrent_node.isEmpty() && orcurrent_node != null) {
//					JSONObject resut = new JSONObject();
//					resut.put(approver_id, result);
//					approved_users.put(orcurrent_node, resut);
//				}
//				approved_users.put(approver_id, username);
//				array.add(approved_users);
//				String approved_user = array.getString(0);
//				send.put("approved_users", approved_user);
//			} else {
//				JSONObject approved_users = form_data.getJSONObject("approved_users");
//				if (!orcurrent_node.isEmpty() && orcurrent_node != null) {
//					if (!approved_users.containsKey(orcurrent_node)) {
//						JSONObject resut = new JSONObject();
//						resut.put(approver_id, result);
//						approved_users.put(orcurrent_node, resut);
//					}
//					JSONObject currentnode=approved_users.getJSONObject(orcurrent_node);
//					currentnode.put(approver_id, result);
//					approved_users.remove(orcurrent_node);
//					approved_users.put(orcurrent_node, currentnode);
//				}
//				approved_users.put(approver_id, username);
//				array.add(approved_users);
//				String approved_user = array.getString(0);
//				send.put("approved_users", approved_user);
//			}
//
//		JSONObject json = new JSONObject();
//		JSONObject where = new JSONObject();
//		where.put("opt", "equal");
//		where.put("key", "form_id");
//		where.put("value", form_id);
//		json.put("operate", "update");
//		json.put("table", "data_tables");
//		json.put("where", where);
//		json.put("data", send);
//		json.put("commit", "true");
//		JSONObject ret = hlcm.hlcmhelper(json);
//		if (ret == null || ret.getIntValue("errno") != 0) {
//			logger.error("error:" + ret);
//		}
//	}
//
//	@Override
//	public void create_t_attendance() throws Exception {
//		// TODO Auto-generated method stub
//		this.init();
//		JSONObject json = new JSONObject();
//		JSONObject data = new JSONObject();
//		json.put("operate", "create");
//
//		json.put("table", "attendance");
//		json.put("shard", shard);
//		json.put("replicate", replicate);
//
//		// 动态
//		JSONObject id = new JSONObject();
//		id.put("type", "long");
//		id.put("index", true);
//		data.put("id", id);
//
//		JSONObject baseCheckTime = new JSONObject();
//		baseCheckTime.put("type", "long");
//		baseCheckTime.put("index", true);
//		data.put("baseCheckTime", baseCheckTime);
//
//		JSONObject checkType = new JSONObject();
//		checkType.put("type", "string");
//		checkType.put("index", true);
//		data.put("checkType", checkType);
//
//		JSONObject locationResult = new JSONObject();
//		locationResult.put("type", "string");
//		locationResult.put("index", true);
//		data.put("locationResult", locationResult);
//
//		JSONObject timeResult = new JSONObject();
//		timeResult.put("type", "string");
//		timeResult.put("index", true);
//		data.put("timeResult", timeResult);
//
//		JSONObject userCheckTime = new JSONObject();
//		userCheckTime.put("type", "long");
//		userCheckTime.put("index", true);
//		data.put("userCheckTime", userCheckTime);
//
//		JSONObject userId = new JSONObject();
//		userId.put("type", "string");
//		userId.put("index", true);
//		data.put("userId", userId);
//
//		JSONObject workDate = new JSONObject();
//		workDate.put("type", "long");
//		workDate.put("index", true);
//		data.put("workDate", workDate);
//		json.put("data", data);
//		JSONObject ret = hlcmhelper(json);
//		if (ret == null || ret.getIntValue("errno") != 0) {
//			logger.error("error:" + ret);
//		}
//		this.close();
//	}
//
//	@Override
//	public void create_t_schedules() throws Exception {
//		// TODO Auto-generated method stub
//			this.init();
//			JSONObject json = new JSONObject();
//			JSONObject data = new JSONObject();
//			json.put("operate", "create");
//
//			json.put("table", "schedules");
//			json.put("shard", shard);
//			json.put("replicate", replicate);
//
//			// 动态
//			JSONObject schedule_id = new JSONObject();
//			schedule_id.put("type", "long");
//			schedule_id.put("index", true);
//			data.put("schedule_id", schedule_id);
//
//			JSONObject user_id = new JSONObject();
//			user_id.put("type", "string");
//			user_id.put("index", true);
//			data.put("user_id", user_id);
//
//
//			JSONObject title = new JSONObject();
//			title.put("type", "string");
//			title.put("index", true);
//			data.put("title", title);
//
//
//			JSONObject starttime = new JSONObject();
//			starttime.put("type", "string");
//			starttime.put("index", true);
//			data.put("starttime", starttime);
//
//
//			JSONObject endtime = new JSONObject();
//			endtime.put("type", "string");
//			endtime.put("index", true);
//			data.put("endtime", endtime);
//
//
//			JSONObject location = new JSONObject();
//			location.put("type", "string");
//			location.put("index", true);
//			data.put("location", location);
//
//
//			JSONObject remark = new JSONObject();
//			remark.put("type", "string");
//			remark.put("index", true);
//			data.put("remark", remark);
//
//
//			JSONObject importance = new JSONObject();
//			importance.put("type", "string");
//			importance.put("index", true);
//			data.put("importance", importance);
//
//			JSONObject notice = new JSONObject();
//			notice.put("type", "string");
//			notice.put("index", true);
//			data.put("notice", notice);
//
//
//			json.put("data", data);
//			JSONObject ret = hlcmhelper(json);
//			if (ret == null || ret.getIntValue("errno") != 0) {
//				logger.error("error:" + ret);
//			}
//			this.close();
//	  }
//
//	@Override
//	public void create_t_publicizes() throws Exception {
//		this.init();
//		JSONObject json = new JSONObject();
//		JSONObject data = new JSONObject();
//		json.put("operate", "create");
//
//		json.put("table", "publicizes");
//		json.put("shard", shard);
//		json.put("replicate", replicate);
//
//		// 动态
//		JSONObject publicize_id = new JSONObject();
//		publicize_id.put("type", "long");
//		publicize_id.put("index", true);
//		data.put("publicize_id", publicize_id);
//
//		JSONObject sender = new JSONObject();
//		sender.put("type", "string");
//		sender.put("index", true);
//		data.put("sender", sender);
//
//
//		JSONObject receivers = new JSONObject();
//		receivers.put("type", "string");
//		receivers.put("index", true);
//		data.put("receivers", receivers);
//
//
//		JSONObject title = new JSONObject();
//		title.put("type", "string");
//		title.put("index", true);
//		data.put("title", title);
//
//
//		JSONObject send_time = new JSONObject();
//		send_time.put("type", "string");
//		send_time.put("index", true);
//		data.put("send_time", send_time);
//
//
//		JSONObject content = new JSONObject();
//		content.put("type", "string");
//		content.put("index", true);
//		data.put("content", content);
//
//
//		JSONObject status = new JSONObject();
//		status.put("type", "string");
//		status.put("index", true);
//		data.put("status", status);
//
//		JSONObject ishow = new JSONObject();
//		ishow.put("type", "string");
//		ishow.put("index", true);
//		data.put("ishow", ishow);
//
//		JSONObject file = new JSONObject();
//		file.put("type", "string");
//		file.put("index", true);
//		data.put("file", file);
//
//
//		JSONObject type = new JSONObject();
//		type.put("type", "string");
//		type.put("index", true);
//		data.put("type", type);
//
//
//		JSONObject readed_ids = new JSONObject();
//		readed_ids.put("type", "string");
//		readed_ids.put("index", true);
//		data.put("readed_ids", readed_ids);
//
//		json.put("data", data);
//
//		JSONObject ret = hlcmhelper(json);
//		if (ret == null || ret.getIntValue("errno") != 0) {
//			logger.error("error:" + ret);
//		}
//		this.close();
//	}
//
//	@Override
//	public void create_t_signin() throws Exception {
//		// TODO Auto-generated method stub
//		this.init();
//		JSONObject json = new JSONObject();
//		JSONObject data = new JSONObject();
//		json.put("operate", "create");
//
//		json.put("table", "signin");
//		json.put("shard", shard);
//		json.put("replicate", replicate);
//
//		// 动态
//		JSONObject checkin_time = new JSONObject();
//		checkin_time.put("type", "long");
//		checkin_time.put("index", true);
//		data.put("checkin_time", checkin_time);
//
//		JSONObject detail_place = new JSONObject();
//		detail_place.put("type", "string");
//		detail_place.put("index", true);
//		data.put("detail_place", detail_place);
//
//		JSONObject image_list = new JSONObject();
//		image_list.put("type", "string");
//		image_list.put("index", true);
//		data.put("image_list", image_list);
//
//		JSONObject remark = new JSONObject();
//		remark.put("type", "string");
//		remark.put("index", true);
//		data.put("remark", remark);
//
//		JSONObject userid = new JSONObject();
//		userid.put("type", "string");
//		userid.put("index", true);
//		data.put("userid", userid);
//
//		JSONObject place = new JSONObject();
//		place.put("type", "string");
//		place.put("index", true);
//		data.put("place", place);
//
//		JSONObject visit_user = new JSONObject();
//		visit_user.put("type", "string");
//		visit_user.put("index", true);
//		data.put("visit_user", visit_user);
//
//
//
//		json.put("data", data);
//		JSONObject ret = hlcmhelper(json);
//		if (ret == null || ret.getIntValue("errno") != 0) {
//			logger.error("error:" + ret);
//		}
//		this.close();
//	}
//
//
//}
