package HL.TNOA.Lib.DataModel;//package HL.SCI.Lib.DataModule;
//
//import HL.SCI.Lib.Lib;
//import HL.SCI.Lib.SciConf;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.*;
//import java.util.concurrent.locks.Lock;
//import java.util.concurrent.locks.ReentrantLock;
//
//public class DataModelPool implements Runnable{
//    private static Logger logger = LoggerFactory.getLogger(DataModelPool.class);
//    private static int maxpool = 15;
//    private static int ConnectTimeout = SciConf.getInt(
//            "basic", "database.pools");
//    private static List<DataModelHelper> idlePool = new ArrayList<>();
//    private static Map<DataModelHelper, Long> usedPool = new HashMap<>();
//    private static Lock lock = new ReentrantLock();
//
//    private static Thread thread = null;
//    private static volatile long thread_id = -1;
//
//    static {
//        thread_id = -1;
//        if(thread != null){
//            thread.interrupt();
//        }
//        DataModelPool infothread = new DataModelPool();
//        thread = new Thread(infothread);
//        thread_id = thread.getId();
//        thread.start();
//    }
//
//    public static DataModelHelper newOneConnect(){
//        try {
//            DataModelHelper datamodel = new DataHlcmImpl();
//            if (!datamodel.init()) {
//                datamodel.close();
//                return null;
//            }
//            return datamodel;
//        }
//        catch (Exception e){
//            logger.error(Lib.getTrace(e));
//            return null;
//        }
//    }
//
//    public static DataModelHelper getModel() {
//        int retrycount = 0;
//        while(true) {
//            lock.lock();
//            try {
//                if (idlePool.size() != 0) {
//                    DataModelHelper datamodel = idlePool.get(0);
//                    idlePool.remove(0);
//
//                    //判断连接是否关闭
//                    if(datamodel.isClose()){
////                        logger.error("datamodel is closed");
//                        datamodel.close();
//                    }
//                    else{
//                        usedPool.put(datamodel, new Date().getTime());
//                        return datamodel;
//                    }
//                }
//                else{
//                    DataModelHelper datamodel = newOneConnect();
//                    usedPool.put(datamodel, new Date().getTime());
//                    return datamodel;
//                }
//            } finally {
//                lock.unlock();
//            }
//
//            retrycount++;
//            if(retrycount > 10000) {
//                DataModelHelper datamodel = newOneConnect();
//                lock.lock();
//                try {
//                    usedPool.put(datamodel, new Date().getTime());
//                }
//                finally {
//                    lock.unlock();
//                }
//                return datamodel;
//            }
//        }
//    }
//
//    public static void putModel(DataModelHelper datamodel){
//        if(datamodel == null){
////            logger.error("this datamodel is null.");
//            return;
//        }
//        lock.lock();
//        try{
//            if(usedPool.containsKey(datamodel)){
//                usedPool.remove(datamodel);
//                if(idlePool.size() > maxpool){
//                    datamodel.close();
//                }
//                else {
//                    idlePool.add(datamodel);
//                }
//            }
//            else{
////                logger.error("this datamodel is not in pools.");
//                datamodel.close();
//            }
//        }
//        finally {
//            lock.unlock();
//        }
//    }
//
//    public void run() {
//        while(thread_id > 0 && thread_id == Thread.currentThread().getId()){
//            try {
//                SciConf.sleep(ConnectTimeout/3);
//                //logger.warn("[" + idlePool.size() + "]" + idlePool);
//                //logger.warn("[" + usedPool.size() + "]" + usedPool);
//                //如果一个小时都没有归还，close，然后重启一个
//                Set<DataModelHelper> pools = usedPool.keySet();
//                HashSet<DataModelHelper> newpools = new HashSet<>();
//                for (DataModelHelper _pools : pools) {
//                    newpools.add(_pools);
//                }
//
//                long nowtime = new Date().getTime();
//                for (DataModelHelper _pools : newpools) {
//
//                    if (usedPool.containsKey(_pools) &&
//                            (nowtime - usedPool.get(_pools)) > ConnectTimeout * 1000) {
//                        lock.lock();
//                        try {
////                            logger.error(_pools + " use timeout, close and reconnect.");
//                            usedPool.remove(_pools);
//                            try {
//                                _pools.close();
//                            } catch (Exception e) {
//                                logger.error(Lib.getTrace(e));
//                            }
//                        }
//                        finally {
//                            lock.unlock();
//                        }
//                    }
//                }
//
//                //检查idlePool中的连接有没有异常的。
//                List<DataModelHelper> newidles = new ArrayList<>();
//                for (DataModelHelper _idles : idlePool) {
//                    newidles.add(_idles);
//                }
//                for(DataModelHelper datamodel: newidles){
//                    if(idlePool.contains(datamodel) && datamodel.isClose()){
//                        idlePool.remove(datamodel);
//                        datamodel.close();
//                    }
//                }
//            }
//            catch (Exception e){
//                logger.error(Lib.getTrace(e));
//            }
//        }
//    }
//}
