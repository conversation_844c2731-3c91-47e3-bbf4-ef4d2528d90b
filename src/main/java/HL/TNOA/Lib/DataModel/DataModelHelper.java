package HL.TNOA.Lib.DataModel;


public interface DataModelHelper {
    boolean init() throws Exception;
    boolean isClose();
    void close();
//    JSONObject hlcmhelper(JSONObject hlcm) throws Exception;
//    void create_table_data_tables() throws Exception;
//    void create_table_notice_tables() throws Exception;
//    JSONObject insert_form(OaHttpRequest request ) throws Exception;
//    void insert_notice_tab(OaHttpRequest request,long form_id,String ndsend_users,String type)throws Exception;
//    void insert_flowdata_to_datatab(OaHttpRequest request, String orcurrent_node) throws Exception;
//    boolean exists_table(String tabletime)throws Exception;
//    int delete_table(String tablename)throws Exception;
//    JSONObject query_form_by_formid(OaHttpRequest request,long form_id) throws Exception;
//    void create_t_attendance() throws Exception;
//    void create_t_schedules() throws Exception;
//    void create_t_publicizes() throws Exception;
//    void create_t_signin() throws Exception;
//    void update_datatab_status(OaHttpRequest request ,String status) throws Exception;
//    void update_next_users(OaHttpRequest request,String next_users) throws Exception;
//    void update_next_current_node(OaHttpRequest request,String next_current_node) throws Exception;
//    void update_ndcopy_users(OaHttpRequest request,JSONObject ndcopyusers) throws Exception;
//    JSONObject query_noticetab_by_userid(OaHttpRequest request) throws Exception;
//    JSONObject query_noticetablist_by_userid(OaHttpRequest request) throws Exception;
//    JSONObject query_form_by_userid(OaHttpRequest request) throws Exception;
//    JSONObject query_history_form(OaHttpRequest request) throws Exception;
//    JSONObject query_readlist_by_userid(OaHttpRequest request) throws Exception;
//    JSONObject query_approvelist_by_userid(OaHttpRequest request) throws Exception;
//    JSONObject query_approvedlist_by_userid(OaHttpRequest request) throws Exception;
//    List<String> get_tables()throws Exception;
//    List<JSONObject> getData(String tablename,long starttime, long endtime,
//                             JSONObject params) throws Exception;
//    boolean save_init(String tablename) throws Exception;
//    boolean save_init(String tablename, int syncMacCount) throws Exception;
//    boolean save_data(JSONObject data) throws Exception;
//    boolean save_sync() throws Exception;
//    void save_exit();


}
