package HL.TNOA.Lib;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

public class GaussHelper {
    private Connection conn = null;
    private Statement stmt = null;
    private String url;
    private static Logger logger = LoggerFactory.getLogger(GaussHelper.class);

    static {
        try {
            Class.forName("org.postgresql.Driver");
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
        }
    }

    public GaussHelper(String key) {

        try {
            String host = TNOAConf.get(key, "addr");
            String port = TNOAConf.get(key, "port");
            String data = TNOAConf.get(key, "data");
            String user = TNOAConf.get(key, "user");
            String pass = TNOAConf.get(key, "pass");

            Class<?> clazz = Class.forName("org.postgresql.Driver");
            url = "jdbc:postgresql://" + host + ":" + port + "/" + data;
            logger.warn(url);
            logger.warn(user);
            Properties properties = new Properties();
            properties.put("user", user);
            properties.put("password", pass);
            properties.put("useUnicode", true);
            properties.put("characterEncoding", "utf8");
            properties.put("autoReconnect", true);
            properties.put("useSSL", true);
            properties.put("serverTimezone", "UTC");

            this.conn = DriverManager.getConnection(url, user, pass);
            this.stmt = conn.createStatement();

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));

        }
    }

    public void close() {
        if (stmt != null) {
            try {
                stmt.close();

            } catch (Exception e) {
                logger.warn(Lib.getTrace(e));
            }
        }

        if (conn != null) {
            try {
                conn.close();

                // logger.warn(DataSynServer.dbcon);
            } catch (Exception e) {
                logger.warn(Lib.getTrace(e));
            }
        }
    }

    public void update(String sql) throws Exception {
        try {
            stmt.executeUpdate(sql);
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            throw e;
        }
    }

    public List<JSONObject> query(String sql) throws Exception {
        try {
            List<JSONObject> list = new ArrayList<>();

            ResultSet rs = this.stmt.executeQuery(sql);
            try {
                ResultSetMetaData md = rs.getMetaData();
                int count = md.getColumnCount();

                while (rs.next()) {
                    JSONObject map = new JSONObject();
                    for (int i = 1; i <= count; i++) {

                        String colName = md.getColumnName(i);
                        if (map.containsKey(colName)) {
                            colName = colName + i;
                        }

                        if (rs.getString(i) == null) {
                            map.put(colName, "");
                        } else {
                            map.put(colName, rs.getString(i));
                        }
                    }
                    list.add(map);
                }
            } finally {
                rs.close();
            }

            return list;
        } catch (Exception e) {
            logger.warn(sql);
            throw e;
        }
    }

    public int query_count(String sql) throws Exception {
        int c = 0;
        try {

            ResultSet rs = this.stmt.executeQuery(sql);
            try {
                ResultSetMetaData md = rs.getMetaData();
                int count = md.getColumnCount();
                while (rs.next()) {

                    for (int i = 1; i <= count; i++) {
                        if (md.getColumnName(i).equals("count")) {
                            c = rs.getInt(i);
                            break;
                        }
                    }
                }
            } finally {
                rs.close();
            }

            return c;
        } catch (Exception e) {
            logger.warn(sql);
            throw e;
        }
    }

    public String query_one(String sql, String col) {
        String back = "";
        try {

            ResultSet rs = this.stmt.executeQuery(sql);
            try {
                ResultSetMetaData md = rs.getMetaData();
                int count = md.getColumnCount();
                while (rs.next()) {

                    for (int i = 1; i <= count; i++) {
                        if (md.getColumnName(i).equals(col)) {
                            back = rs.getString(i);
                            break;
                        }
                    }
                }
            } finally {
                rs.close();
            }

            return back;
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            logger.warn(sql);
            return "";
        }
    }

    public void insertBatch(List<String> dets, String sql) {

        long start = System.currentTimeMillis();
        logger.warn(String.valueOf(start));
        try {
            this.conn.setAutoCommit(false);
            PreparedStatement statement = conn.prepareStatement(sql);

            for (int i = 0; i < dets.size(); i++) {
                String[] ones = dets.get(i).split(",");

                for (int m = 0; m < ones.length; m++) {
                    statement.setString(m + 1, ones[m]);
                }

                statement.addBatch();
            }

            statement.executeBatch();
            statement.close();

            long end = System.currentTimeMillis();
            logger.warn(String.valueOf(end - start));

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {

            try {
                this.conn.close();
            } catch (SQLException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }

        }
    }

}
