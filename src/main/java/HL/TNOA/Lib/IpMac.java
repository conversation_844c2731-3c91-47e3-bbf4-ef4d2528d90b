package HL.TNOA.Lib;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.HashMap;
import java.util.Locale;

public class IpMac {
    private static HashMap<String, String> arp_tables = new HashMap<>();
    public static void flush(){
        try {
            BufferedReader br = new BufferedReader(
                    new FileReader("/proc/net/arp"));
            String line;

            while ((line = br.readLine()) != null) {
                try {
                    line = line.trim();
                    if (line.length() < 63) continue;
                    if (line.toUpperCase(Locale.US).contains("IP")) continue;
                    String ip = line.substring(0, 17).trim();
                    String mac = line.substring(41, 63).trim();
                    if (mac.contains("00:00:00:00:00:00")) continue;
                    arp_tables.put(ip, trans_mac(mac.toUpperCase()));
                } catch (Exception e) {
                }
            }
            br.close();

        } catch (Exception e) {
        }
    }

    public static String get_mac(String ipaddr) {
        if(arp_tables.containsKey(ipaddr)){
            return arp_tables.get(ipaddr);
        }
        return null;
    }


    private static String trans_mac(String mac){
        return mac.substring(0, 2) + "-" + mac.substring(3, 5) + "-" +
                mac.substring(6, 8) + "-" + mac.substring(9, 11) + "-" +
                mac.substring(12, 14) + "-" + mac.substring(15, 17);
    }
}
