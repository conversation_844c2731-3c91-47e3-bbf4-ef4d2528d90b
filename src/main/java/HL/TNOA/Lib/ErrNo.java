package HL.TNOA.Lib;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;

public class ErrNo {
    private static Logger logger = LoggerFactory.getLogger(ErrNo.class);
    protected static HashMap<Integer, String> errno = new HashMap<Integer, String>();

    static {
        try {
            //BufferedReader reader = new BufferedReader(new FileReader("conf/errno.properties"));
            BufferedReader reader = new BufferedReader(new InputStreamReader(
                    ErrNo.class.getClassLoader().getResourceAsStream("errno_ch.properties")));
            String line = reader.readLine();
            if(line != null) {
                byte[] lineb = line.getBytes(StandardCharsets.UTF_8);
                byte[] linen = new byte[lineb.length];
                if(lineb.length > 3) {
                    int a1 = Integer.valueOf(String.format("%x", lineb[0]), 16);
                    int a2 = Integer.valueOf(String.format("%x", lineb[1]), 16);
                    int a3 = Integer.valueOf(String.format("%x", lineb[2]), 16);
                    int a4 = Integer.valueOf(String.format("%x", lineb[3]), 16);

                    if (a1 == 0xEF && a2 == 0xbb && a3 == 0xbf) {
                        System.arraycopy(lineb, 3, linen, 0, lineb.length-3);
                        line = new String(linen);
                    }
                    if (a1 == 0xFE && a2 == 0xFF) {
                        System.arraycopy(lineb, 2, linen, 0, lineb.length-2);
                        line = new String(linen);
                    }
                    if (a1 == 0xFF && a2 == 0xFE) {
                        System.arraycopy(lineb, 2, linen, 0, lineb.length-2);
                        line = new String(linen);
                    }
                    if (a1 == 0x00 && a2 == 0x00 && a3 == 0xfe && a4 == 0xff) {
                        System.arraycopy(lineb, 4, linen, 0, lineb.length-4);
                        line = new String(linen);
                    }
                    if (a1 == 0xff && a2 == 0xfe && a3 == 0x00 && a4 == 0x00) {
                        System.arraycopy(lineb, 4, linen, 0, lineb.length-4);
                        line = new String(linen);
                    }
                }
                else if(lineb.length > 2) {
                    int a1 = Integer.valueOf(String.format("%x", lineb[0]), 16);
                    int a2 = Integer.valueOf(String.format("%x", lineb[1]), 16);

                    if (a1 == 0xFE && a2 == 0xFF) {
                        System.arraycopy(lineb, 2, linen, 0, lineb.length-2);
                        line = new String(linen);
                    }
                    if (a1 == 0xFF && a2 == 0xFE) {
                        System.arraycopy(lineb, 2, linen, 0, lineb.length-2);
                        line = new String(linen);
                    }
                }

                line = line.trim();
                if (line.length() >= 2 && line.charAt(0) != '#') {
                    parseLine(line);
                }

                while((line = reader.readLine()) != null)  {
                    line = line.trim();
                    if(line.length() >= 2 && line.charAt(0) != '#') {
                        parseLine(line);
                    }
                }
                reader.close();
            }
        }
        catch(Exception e) {
            logger.error(Lib.getTrace(e));
            System.exit(0);
        }
    }

    static void parseLine(String  line)  throws Exception{
        line = line.trim();
        try {
            if(line.matches(".*=.*"))
            {
                int i = line.indexOf('=');
                int key = Integer.parseInt(line.substring(0, i));
                String value = line.substring(i + 1);
                errno.put(key, value);
            }
            else {
                logger.error("errno.properties: this line format is error ==>" + line);
            }
        }
        catch(Exception e) {
            logger.error("errno.properties: this line exception ==>" + line);
            logger.error(Lib.getTrace(e));
        }
    }

    public static String get(int key) {
        if(errno.containsKey(key)) {
            return errno.get(key);
        }

        return null;
    }

    /**
     *
     * @param errno
     * @return
     */
    public static JSONObject set(int errno) {
        JSONObject json = new JSONObject();
        json.put("errno", errno);
        if (TNOAConf.get("basic", "errno.enable").equals("1")) {
            json.put("error", ErrNo.get(errno));
        }
        return json;
    }

    public static JSONObject set(JSONObject json, int errno) {
        if(json == null){
            json = new JSONObject();
        }
        json.put("errno", errno);
        if (TNOAConf.get("basic", "errno.enable").equals("1")) {
            json.put("error", ErrNo.get(errno));
        }
        return json;
    }

    public static JSONObject set(JSONObject json, int errno, String error) {
        if(json == null){
            json = new JSONObject();
        }
        json.put("errno", errno);
        if (TNOAConf.get("basic", "errno.enable").equals("1")) {
            json.put("error", urltrim(error));
        }
        return json;
    }

    private static String urltrim(String str) {
        String resStr = str;
        try {
            if (str.indexOf("\"") != -1) {
                resStr = str.replaceAll("\"", "");
            }
            if (str.indexOf("\\") != -1) {
                resStr = str.replaceAll("\\\\", "");
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }
        return resStr;
    }
}