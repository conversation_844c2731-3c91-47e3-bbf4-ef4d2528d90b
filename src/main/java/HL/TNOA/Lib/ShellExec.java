package HL.TNOA.Lib;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

public class ShellExec {
    public static Logger logger = LoggerFactory.getLogger(ShellExec.class);

    public static String nonexist = "No such file or directory";
    public static String exec(String cmd, int timeout, boolean printflag) {
        try {
            long ntimeout = -1;
            if(timeout > 0) {
                ntimeout = timeout * 1000;
            }
            long starttime = System.currentTimeMillis();

            Process pro = Runtime.getRuntime().exec(new String[] { "/bin/bash", "-c", cmd});

            try {
                StringBuffer sb = new StringBuffer();
                while (true) {
                    if (pro.getInputStream().available() > 0) {
                        byte[] buf = new byte[pro.getInputStream().available()];
                        pro.getInputStream().read(buf);
                        if (printflag) {
                            System.out.println(new String(buf).trim());
                        } else {
                            sb.append(new String(buf).trim() + "\n");
                        }
                    } else if (pro.getInputStream().available() < 0) {
                        break;
                    }

                    if (pro.getErrorStream().available() > 0) {
                        byte[] buf = new byte[pro.getErrorStream().available()];
                        pro.getErrorStream().read(buf);
                        if (printflag) {
                            System.out.println(new String(buf).trim());
                        } else {
                            sb.append(new String(buf).trim() + "\n");
                        }
                    } else if (pro.getErrorStream().available() < 0) {
                        break;
                    }

                    // 检查是否退出
                    try {
                        pro.exitValue();
                        break;
                    } catch (Exception e) {
                    }

                    // 检查是否timeout
                    if (ntimeout > 0) {
                        if ((System.currentTimeMillis() - starttime) > ntimeout) {
                            break;
                        }
                    }

                    Thread.sleep(10);
                }
                return sb.toString();
            } catch (Exception e1) {
                logger.error(Lib.getTrace(e1));
            } finally {
                pro.getOutputStream().close();
                pro.getInputStream().close();
                pro.getErrorStream().close();

                pro.destroy();
                pro.waitFor(5, TimeUnit.SECONDS);
                pro.destroy();
            }
        } catch (Exception e2) {
            logger.error(Lib.getTrace(e2));
            e2.printStackTrace();
        }

        return null;
    }
}
