package HL.TNOA.Lib.InfoModel;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

public interface InfoModelHelper {
    boolean init() throws Exception;

    boolean isClose();

    void close();

    void update(String sql) throws Exception;
    void update_byte(String sql,byte[] val,String colName) throws Exception;

    List<JSONObject> query(String sql) throws Exception;

    boolean checkTable(String table_name) throws Exception;

    int query_count(String sql) throws Exception;

    String query_one(String sql, String col);
}
