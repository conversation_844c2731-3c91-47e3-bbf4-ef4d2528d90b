package HL.TNOA.Lib.InfoModel;


import com.alibaba.fastjson.JSONObject;

import java.util.List;



public interface OracleModelHelper {
    boolean init() throws Exception;

    boolean isClose();

    void close();

    void update(String sql) throws Exception;

    List<JSONObject> query(String sql) throws Exception;

    boolean checkTable(String table_name) throws Exception;

    int query_count(String sql) throws Exception;

    String query_one(String sql, String col);
}
