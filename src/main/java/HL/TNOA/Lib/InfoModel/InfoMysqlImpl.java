package HL.TNOA.Lib.InfoModel;

import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.TNOAConf;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

public class InfoMysqlImpl implements InfoModelHelper {
    private static Logger logger = LoggerFactory.getLogger(InfoMysqlImpl.class);
    private Connection conn = null;
    private Statement stmt = null;
    private String url;

    static {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }
    }

    @Override
    public boolean init() throws Exception {
        String host = TNOAConf.get("mysql", "addr");
        String port = TNOAConf.get("mysql", "port");
        String data = TNOAConf.get("mysql", "data");
        String user = TNOAConf.get("mysql", "user");
        String pass = TNOAConf.get("mysql", "pass");

        Class<?> clazz = Class.forName("com.mysql.cj.jdbc.Driver");
        url = "jdbc:mysql://" + host + ":" + port + "/" + data;

        Properties properties = new Properties();
        properties.put("user", user);
        properties.put("password", pass);
        properties.put("useUnicode", true);
        properties.put("characterEncoding", "utf8");
        properties.put("autoReconnect", true);
        properties.put("useSSL", true);
        properties.put("serverTimezone", "UTC");

        this.conn = DriverManager.getConnection(url, properties);
        this.stmt = conn.createStatement();
        return true;
    }

    @Override
    public boolean isClose() {
        try {
            boolean ret = conn.isClosed();
            if (ret == true) {
                return ret;
            }
            String sql = "select 1 from dual";
            query(sql);
            return false;
        } catch (Exception e) {
            return true;
        }
    }

    @Override
    public void close() {
        if (stmt != null) {
            try {
                stmt.close();
            } catch (Exception e) {
                logger.warn(Lib.getTrace(e));
            }
        }

        if (conn != null) {
            try {
                conn.close();
            } catch (Exception e) {
                logger.warn(Lib.getTrace(e));
            }
        }
    }

    @Override
    public void update(String sql) throws Exception {
        try {
            stmt.executeUpdate(sql);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            throw e;
        }
    }

    @Override
    public void update_byte(String sql, byte[] val, String colName) throws Exception {
        PreparedStatement stmt = conn.prepareStatement(sql);
        stmt.setBytes(1, val);
        stmt.executeUpdate();
        stmt.close();

    }

    @Override
    public List<JSONObject> query(String sql) throws Exception {
        try {
            List<JSONObject> list = new ArrayList<>();

            ResultSet rs = this.stmt.executeQuery(sql);
            try {
                ResultSetMetaData md = rs.getMetaData();
                int count = md.getColumnCount();

                while (rs.next()) {
                    JSONObject map = new JSONObject();
                    for (int i = 1; i <= count; i++) {
                        int type = md.getColumnType(i);
                        String colName=md.getColumnName(i);
                        if(map.containsKey(colName))
                        {
                            colName=colName+i;
                        }
                        if (rs.getString(i) == null) {
                            map.put(colName, "");
                        } else {

                            map.put(colName, rs.getString(i));

                        }
                    }
                    list.add(map);
                }
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            } finally {
                rs.close();
            }

            return list;
        } catch (Exception e) {
            logger.error(sql);
            throw e;
        }
    }

    @Override
    public boolean checkTable(String table_name) throws Exception {
        String checkdatabase = "show databases like \"" + table_name + "\"";//判断数据库是否存在


        ResultSet resultSet = this.stmt.executeQuery(checkdatabase);
        if (resultSet.next()) {//若数据库存在
            return true;
        } else {
            return false;
        }


    }

    @Override
    public int query_count(String sql) throws Exception {
        int c = 0;
        try {

            ResultSet rs = this.stmt.executeQuery(sql);
            try {
                ResultSetMetaData md = rs.getMetaData();
                int count = md.getColumnCount();
                while (rs.next()) {

                    for (int i = 1; i <= count; i++) {
                        if (md.getColumnName(i).equals("count")) {
                            c = rs.getInt(i);
                            break;
                        }
                    }
                }
            } finally {
                rs.close();
            }

            return c;
        } catch (Exception e) {
            logger.error(sql);
            throw e;
        }
    }

    @Override
    public String query_one(String sql, String col) {
        String back = "";
        try {

            ResultSet rs = this.stmt.executeQuery(sql);
            try {
                ResultSetMetaData md = rs.getMetaData();
                int count = md.getColumnCount();
                while (rs.next()) {

                    for (int i = 1; i <= count; i++) {
                        if (md.getColumnName(i).equals(col)) {
                            back = rs.getString(i);
                            break;
                        }
                    }
                }
            } finally {
                rs.close();
            }

            return back;
        } catch (Exception e) {
            logger.error(sql);
            return "";
        }
    }


//	@Override
//	public boolean query_by_tableun(long table_un) throws Exception {
//		String sql = "select * from web_tables where table_un = '" + table_un + "'";
//		  ResultSet rs = this.stmt.executeQuery(sql);
//	        try {
//
//	               if (rs.next()) {
//	                  return true;
//	                }
//	              return false;
//
//	        }
//	        catch (Exception e){
//	            logger.error(sql);
//	            throw e;
//	        }
//		  finally {
//			  rs.close();
//		  }
//
//	}
//
//	@Override
//	public boolean query_old_by_tableun(long table_un) throws Exception {
//		String sql = "select * from web_tables_old where table_un = '" + table_un + "'";
//		  ResultSet rs = this.stmt.executeQuery(sql);
//	        try {
//
//	               if (rs.next()) {
//	                  return true;
//	                }
//	              return false;
//
//	        }
//	        catch (Exception e){
//	            logger.error(sql);
//	            throw e;
//	        }
//		  finally {
//			  rs.close();
//		  }
//	}

}
