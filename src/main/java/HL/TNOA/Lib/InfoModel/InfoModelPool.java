package HL.TNOA.Lib.InfoModel;

import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.TNOAConf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class InfoModelPool implements Runnable {
    private static int maxpool = 15;
    private static int ConnectTimeout = 120;
    static List<InfoModelHelper> idlePool = new ArrayList<>();
    private static Map<InfoModelHelper, Long> usedPool1 = new HashMap<>();
    private static Lock lock = new ReentrantLock();
    private static Logger logger = LoggerFactory.getLogger(InfoModelPool.class);
    private static String startwith = null;
    private static Thread thread = null;
    private static volatile long thread_id;
    public static Map<InfoModelHelper, String> usedCallStack = new HashMap<>();

    static {
        thread_id = -1;
        if (thread != null) {
            thread.interrupt();
        }

        String info_model = TNOAConf.get("basic", "info.model");
        if (TNOAConf.has(info_model, "maxpool")) {
            maxpool = Integer.parseInt(TNOAConf.get(info_model, "maxpool"));
        }
        if (TNOAConf.has(info_model, "timeout")) {
            ConnectTimeout = 600;
        }

        if (TNOAConf.get("basic", "exception.startwith").length() > 0 && !TNOAConf.get("basic",
                "exception.startwith").equals("*")) {
            startwith = TNOAConf.get("basic", "exception.startwith");
        }

        InfoModelPool infothread = new InfoModelPool();
        thread = new Thread(infothread);
        thread_id = thread.getId();
        thread.start();
    }

    public static InfoModelHelper newOneConnect() throws Exception {
        InfoModelHelper infomodel = new InfoMysqlImpl();
        if (!infomodel.init()) {
            infomodel.close();
            return null;
        }
        return infomodel;
    }

    public static String getCallStack() {
        Throwable exception = new Throwable();
        StackTraceElement[] stackTraceElements = exception.getStackTrace();
        String CallStack = "";
        if (stackTraceElements != null) {
            //前2个不要了
            int discard = 0;
            for (StackTraceElement element : stackTraceElements) {
                discard++;
                if (discard <= 2) {
                    continue;
                }
                if (startwith != null && !element.getClassName().startsWith(startwith)) {
                    continue;
                }
                CallStack += "[" + element.getClassName() + ":" + element.getLineNumber() + "]" + element.getMethodName() + "\t";
            }
        }
        return CallStack;
    }

    public static InfoModelHelper getModel() throws Exception {
        int retrycount = 0;
        while (true) {
            lock.lock();
            try {
                if (idlePool.size() != 0) {
                    InfoModelHelper infomodel = idlePool.get(0);
                    idlePool.remove(0);

                    //判断连接是否关闭
                    if (infomodel.isClose()) {
                        System.out.println("infomodel is closed");
                        infomodel.close();
                    } else {
                        usedPool1.put(infomodel, new Date().getTime());
                        usedCallStack.put(infomodel, getCallStack());
                        return infomodel;
                    }
                } else {
                    InfoModelHelper infomodel = newOneConnect();
                    usedPool1.put(infomodel, new Date().getTime());
                    usedCallStack.put(infomodel, getCallStack());
                    return infomodel;
                }
            } finally {
                lock.unlock();
            }

            retrycount++;
            if (retrycount > 10000) {
                InfoModelHelper infomodel = newOneConnect();
                lock.lock();
                try {
                    usedPool1.put(infomodel, new Date().getTime());
                    usedCallStack.put(infomodel, getCallStack());
                } finally {
                    lock.unlock();
                }
                return infomodel;
            }
        }
    }

    public static void putModel(InfoModelHelper infomodel) {
        if (infomodel == null) {
            //logger.error("this infomodel is null.");
            return;
        }
        lock.lock();
        try {
            if (usedPool1.containsKey(infomodel)) {
                usedPool1.remove(infomodel);
                usedCallStack.remove(infomodel);
                if (idlePool.size() > maxpool) {
                    infomodel.close();
                } else {
                    idlePool.add(infomodel);
                }
            } else {
                //System.out.println("this infomodel is not in pools.");
                infomodel.close();
            }
        } finally {
            lock.unlock();
        }
    }

    public void run() {
        while (thread_id > 0 && thread_id == Thread.currentThread().getId()) {
            try {
                Lib.sleep(ConnectTimeout / 3);
                //logger.warn("[" + idlePool.size() + "]" + idlePool);
                //logger.warn("[" + usedPool.size() + "]" + usedPool);
                //如果一个小时都没有归还，close，然后重启一个
                Set<InfoModelHelper> pools = usedPool1.keySet();
                HashSet<InfoModelHelper> newpools = new HashSet<>();
                for (InfoModelHelper _pools : pools) {
                    newpools.add(_pools);
                }

                long nowtime = new Date().getTime();
                for (InfoModelHelper _pools : newpools) {

                    if (usedPool1.containsKey(_pools) && (nowtime - usedPool1.get(_pools)) > ConnectTimeout * 1000L) {
                        lock.lock();
                        try {
                            logger.error(_pools + " use timeout, close and reconnect.");
                            logger.error("InfoMysqlImpl use timeout, than close --> " + usedCallStack.get(_pools));
                            usedPool1.remove(_pools);
                            usedCallStack.remove(_pools);
                            try {
                                _pools.close();
                            } catch (Exception e) {
                                logger.error(Lib.getTrace(e));
                            }
                        } finally {
                            lock.unlock();
                        }
                    }
                }

                //检查idlePool中的连接有没有异常的。
                List<InfoModelHelper> newidles = new ArrayList<>();
                for (InfoModelHelper _idles : idlePool) {
                    newidles.add(_idles);
                }
                for (InfoModelHelper mysql : newidles) {
                    if (idlePool.contains(mysql) && mysql.isClose()) {
                        idlePool.remove(mysql);
                        mysql.close();
                    }
                }
            } catch (Exception e) {
                logger.error(Lib.getTrace(e));
            }
        }
    }
}
