package HL.TNOA.Lib.InfoModel;

import HL.TNOA.Lib.Lib;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;


public class OracleModelPool implements Runnable {
	private static int maxpool = 15;
	private static int ConnectTimeout = 120;
	static List<OracleModelHelper> idlePool = new ArrayList<>();
	private static Map<OracleModelHelper, Long> usedPool = new HashMap<>();
	private static Lock lock = new ReentrantLock();
	private static Logger logger = LoggerFactory.getLogger(OracleModelPool.class);

	private static Thread thread = null;
	private static volatile long thread_id;

	static {
		thread_id = -1;
		if (thread != null) {
			thread.interrupt();
		}

		OracleModelPool infothread = new OracleModelPool();
		thread = new Thread(infothread);
		thread_id = thread.getId();
		thread.start();
	}

	public static OracleModelHelper newOneConnect() throws Exception {
		OracleModelHelper OracleModel = new OracleImpl();
		if (!OracleModel.init()) {
			OracleModel.close();
			return null;
		}
		return OracleModel;
	}

	public static OracleModelHelper getModel() throws Exception {
		int retrycount = 0;
		while (true) {
			lock.lock();
			try {
				if (idlePool.size() != 0) {
					OracleModelHelper OracleModel = idlePool.get(0);
					idlePool.remove(0);

					// 鍒ゆ柇杩炴帴鏄惁鍏抽棴
					if (OracleModel.isClose()) {
						logger.error("OracleModel is closed");
						OracleModel.close();
					} else {
						usedPool.put(OracleModel, new Date().getTime());
						return OracleModel;
					}
				} else {
					OracleModelHelper OracleModel = newOneConnect();
					usedPool.put(OracleModel, new Date().getTime());
					return OracleModel;
				}
			} finally {
				lock.unlock();
			}

			retrycount++;
			if (retrycount > 10000) {
				OracleModelHelper OracleModel = newOneConnect();
				lock.lock();
				try {
					usedPool.put(OracleModel, new Date().getTime());
				} finally {
					lock.unlock();
				}
				return OracleModel;
			}
		}
	}

	public static void putModel(OracleModelHelper OracleModel) {
		if (OracleModel == null) {
			// logger.error("this OracleModel is null.");
			return;
		}
		lock.lock();
		try {
			if (usedPool.containsKey(OracleModel)) {
				usedPool.remove(OracleModel);
				if (idlePool.size() > maxpool) {
					OracleModel.close();
				} else {
					idlePool.add(OracleModel);
				}
			} else {
				logger.error("this OracleModel is not in pools.");
				OracleModel.close();
			}
		} finally {
			lock.unlock();
		}
	}

	@Override
	public void run() {
		while (thread_id > 0 && thread_id == Thread.currentThread().getId()) {
			try {
				Thread.sleep(ConnectTimeout / 3);
				// logger.warn("[" + idlePool.size() + "]" + idlePool);
				// logger.warn("[" + usedPool.size() + "]" + usedPool);
				// 濡傛灉涓�涓皬鏃堕兘娌℃湁褰掕繕锛宑lose锛岀劧鍚庨噸鍚竴涓�
				Set<OracleModelHelper> pools = usedPool.keySet();
				HashSet<OracleModelHelper> newpools = new HashSet<>();
				for (OracleModelHelper _pools : pools) {
					newpools.add(_pools);
				}

				long nowtime = new Date().getTime();
				for (OracleModelHelper _pools : newpools) {

					if (usedPool.containsKey(_pools) && (nowtime - usedPool.get(_pools)) > ConnectTimeout * 1000L) {
						lock.lock();
						try {
							logger.error(_pools + " use timeout, close and reconnect.");
							usedPool.remove(_pools);
							try {
								_pools.close();
							} catch (Exception e) {
								logger.error(Lib.getTrace(e));
							}
						} finally {
							lock.unlock();
						}
					}
				}

				// 妫�鏌dlePool涓殑杩炴帴鏈夋病鏈夊紓甯哥殑銆�
				List<OracleModelHelper> newidles = new ArrayList<>();
				for (OracleModelHelper _idles : idlePool) {
					newidles.add(_idles);
				}
				for (OracleModelHelper mysql : newidles) {
					if (idlePool.contains(mysql) && mysql.isClose()) {
						idlePool.remove(mysql);
						mysql.close();
					}
				}
			} catch (Exception e) {
				logger.error(Lib.getTrace(e));
			}
		}
	}
}
