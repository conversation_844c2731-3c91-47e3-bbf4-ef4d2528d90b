package HL.TNOA.Lib;

import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class RIUtil1 {
    public static String enName = "1n2a3m4e";
    public static String enTele = "1t2e3l4e";
    public static String enDate = "1d2a3t4e";
    public static String enNum = "1n2u3m";
    public static String enTitle = "1t2i3t4l5e";
    public static String enContent = "1c2o3n4t5e6n7t";

    public static HashMap<String, JSONObject> dicts = new HashMap<String, JSONObject>();
    public static HashMap<String, JSONObject> kh_configs = new HashMap<String, JSONObject>();
    public static HashMap<String, JSONObject> users1 = new HashMap<>();
    public static JSONArray org_tree = new JSONArray();
    public static JSONArray login_times = new JSONArray();


    public static HashMap StringToList(String members) {
        HashMap back = new HashMap();
        // System.out.println("or->" + members);
        String m = members.replace("[", "").replace("]", "").replace("\"", "");
        //System.out.println("cut->" + m);
        String[] mm = m.split(",");
        if (mm.length > 0) {
            for (int i = 0; i < mm.length; i++) {
                String ms = mm[i].trim();
                if (ms.length() > 0) {
                    back.put(ms, "");
                }
            }
        }
        //System.out.println("S2L->" + back);
        return back;

    }

    public static HashMap PositionsToUsers(HashMap<String, String> poss, InfoModelHelper mysql) throws Exception {
        HashMap p = new HashMap();
        String sqls = "(";
        for (Map.Entry one : poss.entrySet()) {
            sqls = sqls + "position like '%" + one.getKey() + "%' or ";
        }
        sqls = sqls.substring(0, sqls.length() - 3) + ")";
        String sql = "select * ,decode(birth,'" + RIUtil.enDate + "') as birth,decode(tele_long,'" + RIUtil.enTele +
                "') as tele_long, name,decode(urgent,'" + RIUtil.enTele + "') " + "as urgent,decode(car_num,'" + RIUtil.enNum + "') as car_num from user where " + sqls + " and " + "isdelete=1 and status=1";
        // System.out.println("70->" + sql);
        List<JSONObject> list = mysql.query(sql);

        if (list.size() > 0) {

            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String id = one.getString("id");
                p.put(id, "");
            }
        }
        // logger.warn("p2u->" + p);
        return p;
    }

    public static JSONArray UseridToNames(HashMap<String, String> members) throws Exception {
        //System.out.println("86->" + members);
        JSONArray back = new JSONArray();

        for (Map.Entry<String, String> one : members.entrySet()) {
//            String id = one.getKey();
            String idNum = one.getKey();
            try {

                JSONObject u = users1.get(idNum);
                if (u != null) {

                    back.add(u);
                }
            } catch (Exception ex) {
                // System.out.println(Lib.getTrace(ex));
            }
        }


        return back;

    }


    public static HashMap<String, String> GroupsToUsers(String ids, InfoModelHelper mysql) throws Exception {
        String sql = "select members,positions from `group` where id in (" + ids.replace("[", "").replace("]", "") +
                ")";

        List<JSONObject> list = mysql.query(sql);
        HashMap<String, String> mm = new HashMap<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String mms = one.getString("members");
            if (mms.length() > 0) {
                mm.putAll(RIUtil.StringToList(mms));
            }
            String poss = one.getString("positions").replace("[", "").replace("]", "");

            if (poss.length() > 0) {
                mm.putAll(RIUtil.PositionsToUsers(RIUtil.StringToList(poss), mysql));
            }
        }

        return mm;

    }

    public static JSONArray GroupsToName(HashMap<String, String> groups, InfoModelHelper mysql) throws Exception {
        HashMap<String, JSONObject> groupList = new HashMap<>();
        String sql = "select id,group_name from `group` where isdelete=1";
        List<JSONObject> list = mysql.query(sql);
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            groupList.put(one.getString("id"), one);
        }
        // System.out.println(groupList.toString());
        JSONArray back = new JSONArray();
        for (Map.Entry<String, String> one : groups.entrySet()) {
            String id = one.getKey();
            try {
                back.add(groupList.get(id));
            } catch (Exception e) {
            }
        }

        return back;
    }

    public static List<String> HashToList(HashMap<String, String> datas) {
        List<String> back = new ArrayList<>();
        for (Map.Entry<String, String> one : datas.entrySet()) {
            back.add(one.getKey().trim());
        }
        return back;
    }

    //将时间转换为时间戳
    public static long dateToStamp(String s) throws Exception {
        String res;//设置时间格式，将该时间格式的时间转换为时间戳
        if (s.length() == 16) {
            s = s + ":00";
        }
        if (s.length() == 10) {
            s = s + " 00:00:00";
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = simpleDateFormat.parse(s);
        long time = date.getTime();

        return time;
    }

    //将时间戳转换为时间
    public static String stampToTime(long s) throws Exception {
        String res;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long lt = new Long(s);//将时间戳转换为时间
        Date date = new Date(lt);//将时间调整为yyyy-MM-dd HH:mm:ss时间样式
        res = simpleDateFormat.format(date);
        return res;
    }

    public static int dateToWeek(String datetime) {
        if (datetime.length() == 16) {
            datetime = datetime + ":00";
        }
        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        Date date;
        try {
            date = f.parse(datetime);
            cal.setTime(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        //一周的第几天
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) w = 0;
        return w;
    }

    public static String getLMonthEnd(String start_time) {//设置时间格式
        int[] monthdays = {0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
        String dates[] = start_time.split("-");
        int year = Integer.parseInt(dates[0]);
        int month = Integer.parseInt(dates[1]);
        String m = "";
        if (year % 4 == 0 && month == 2) {
            m = "0" + 2;
            return year + "-" + m + "-29 23:59:59";
        } else {
            if (month < 10) {
                m = "0" + month;
            } else {
                m = String.valueOf(month);
            }
            return year + "-" + m + "-" + monthdays[month] + " 23:59:59";
        }


    }

    /**
     * 获取本周的最后一天
     *
     * @param start_time
     * @return String
     */
    public static String getWeekEnd(String start_time) throws ParseException {

        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, cal.getActualMaximum(Calendar.DAY_OF_WEEK));
        cal.add(Calendar.DAY_OF_WEEK, 1);
        Date date = new Date(new SimpleDateFormat("yyyy-MM-dd").parse(start_time).getTime());

        Date time = cal.getTime();
        return new SimpleDateFormat("yyyy-MM-dd").format(time) + " 23:59:59";
    }

    public static String[] GetSelects(String selects) {
        String ss = selects.replace("{", "").replace("}", "");
        String[] s = ss.split(",");
        String back[] = new String[s.length];
        for (int i = 0; i < s.length; i++) {
            String[] one = s[i].split(":");
            back[i] = one[1];
        }
        return back;
    }

    public static String GetNextDateTime(String start_time, int days) throws ParseException {
        if (start_time.length() == 16) {
            start_time = start_time + ":00";
        }
        if (start_time.length() == 10) {
            start_time = start_time + " 00:00:00";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date sDate = sdf.parse(start_time);

        Calendar c = Calendar.getInstance();
        c.setTime(sDate);
        c.add(Calendar.DAY_OF_MONTH, days);        //利用Calendar 实现 Date日期+1天

        sDate = c.getTime();
        return sdf.format(sDate);

    }

    public static String GetNextDate(String startData, int days) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date sDate = sdf.parse(startData);

        Calendar c = Calendar.getInstance();
        c.setTime(sDate);
        c.add(Calendar.DAY_OF_MONTH, days);        //利用Calendar 实现 Date日期+1天

        sDate = c.getTime();
        return sdf.format(sDate);

    }

    public static String IdToName(String id, InfoModelHelper mysql, String colName, String table) throws Exception {
        String name = "";
        String sql = "select " + colName + " from " + table + " where isdelete=1 and id='" + id + "'";

        List<JSONObject> list = mysql.query(sql);
        if (list.size() > 0) {
            if (colName.contains("decode")) {
                String cols[] = colName.split(" as ");
                colName = cols[1];
            }
            name = list.get(0).getString(colName);
        }
        return name;
    }

    public static String getLastMonth() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-");
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date); // 设置为当前时间
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1); // 设置为上一个月
        date = calendar.getTime();
        String accDate = format.format(date) + "01";
        return accDate;
    }

    public static String getNextMonth(String time) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");

        try {
            Date date = format.parse(time);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date); // 设置为当前时间
            calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1); // 设置为下一个月
            date = calendar.getTime();
            return format.format(date) + "-01 00:00:00";
        } catch (Exception e) {
            return "";
        }
    }

    public static int get2DateBetween(String start, String end) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        cal.setTime(sdf.parse(start));
        long time1 = cal.getTimeInMillis();
        cal.setTime(sdf.parse(end));
        long time2 = cal.getTimeInMillis();
        long between_days = (time2 - time1) / (1000 * 3600 * 24);

        return Integer.parseInt(String.valueOf(between_days));
    }

    public static String getWeekStart(String d) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date(sdf.parse(d).getTime());
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int weekday = cal.get(Calendar.DAY_OF_WEEK) - 2;
        cal.add(Calendar.DATE, -weekday);
        sdf.format(cal.getTime());
        return sdf.format(cal.getTime());
    }

    public static int GetWeekNum(String str) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date date = format.parse(str);
            Calendar calendar = Calendar.getInstance();
            calendar.setFirstDayOfWeek(Calendar.MONDAY);
            calendar.setTime(date);
            return calendar.get(Calendar.WEEK_OF_YEAR);
        } catch (Exception exception) {
            System.out.println(Lib.getTrace(exception));
        }
        return 99;

    }

    public static String GetQuarterNum(String month) {
        try {
            if (month.equals("01") || month.equals("02") || month.equals("03")) {
                return "01";
            } else if (month.equals("04") || month.equals("05") || month.equals("06")) {
                return "02";
            } else if (month.equals("07") || month.equals("08") || month.equals("09")) {
                return "03";
            } else if (month.equals("10") || month.equals("11") || month.equals("12")) {
                return "04";
            } else {
                return "";
            }
        } catch (Exception e) {
            System.out.println(Lib.getTrace(e));
        }
        return "00";
    }

    public static String GetQuarterDate(String quarter, String year) {
        String start_time = "";
        String end_time = "";
        if (quarter.equals("01")) {
            start_time = year + "-01-01 00:00:00";
            end_time = year + "-03-31 23:59:59";
        }
        if (quarter.equals("02")) {
            start_time = year + "-04-01 00:00:00";
            end_time = year + "-06-30 23:59:59";
        }
        if (quarter.equals("03")) {
            start_time = year + "-07-01 00:00:00";
            end_time = year + "-09-30 23:59:59";
        }
        if (quarter.equals("04")) {
            start_time = year + "-10-01 00:00:00";
            end_time = year + "-12-31 23:59:59";
        }
        return start_time + "," + end_time;
    }

    //获取身份证信息
    public static JSONObject GetIDinfo(String id_num) {
        JSONObject back = new JSONObject();
        if (id_num.length() == 15) {
            back = identityCard15(id_num);
        } else if (id_num.length() == 18) {
            back = identityCard18(id_num);
        }
        return back;
    }

    /**
     * 18位身份证获取性别和年龄
     *
     * @param CardCode
     * @return
     * @throws Exception
     */
    public static JSONObject identityCard18(String CardCode) {
        JSONObject map = new JSONObject();
        try {

            // 得到年份
            String year = CardCode.substring(6).substring(0, 4);
            // 得到月份
            String month = CardCode.substring(10).substring(0, 2);
            //得到日
            String day = CardCode.substring(12).substring(0, 2);
            String sex;
            // 判断性别
            if (Integer.parseInt(CardCode.substring(16).substring(0, 1)) % 2 == 0) {
                sex = "1";
            } else {
                sex = "2";
            }
            // 得到当前的系统时间
            Date date = new Date();
            // 当前年份
            String currentYear = new SimpleDateFormat("yyyy-MM-dd").format(date).substring(0, 4);
            // 月份
            String currentMonth = new SimpleDateFormat("yyyy-MM-dd").format(date).substring(5, 7);
            //String currentdDay=format.format(date).substring(8,10);
            int age = 0;
            // 当前月份大于用户出身的月份表示已过生日
            if (Integer.parseInt(month) <= Integer.parseInt(currentMonth)) {
                age = Integer.parseInt(currentYear) - Integer.parseInt(year) + 1;
            } else {
                // 当前用户还没过生日
                age = Integer.parseInt(currentYear) - Integer.parseInt(year);
            }
            map.put("sex", sex);
            map.put("age", age);
            map.put("birth", year + "-" + month + "-" + day);
        } catch (Exception ex) {

        }
        return map;
    }

    /**
     * 15位身份证获取性别和年龄
     *
     * @param card
     * @return
     * @throws Exception
     */
    public static JSONObject identityCard15(String card) {

        JSONObject map = new JSONObject();
        try {
            //年份
            String year = "19" + card.substring(6, 8);
            //月份
            String yue = card.substring(8, 10);
            //日
            String day = card.substring(10, 12);
            String sex;
            if (Integer.parseInt(card.substring(14, 15)) % 2 == 0) {
                sex = "1";
            } else {
                sex = "2";
            }
            // 得到当前的系统时间
            Date date = new Date();
            //当前年份
            String currentYear = new SimpleDateFormat("yyyy-MM-dd").format(date).substring(0, 4);
            //月份
            String currentMonth = new SimpleDateFormat("yyyy-MM-dd").format(date).substring(5, 7);
            //String fday=format.format(date).substring(8,10);
            int age = 0;
            //当前月份大于用户出身的月份表示已过生日
            if (Integer.parseInt(yue) <= Integer.parseInt(currentMonth)) {
                age = Integer.parseInt(currentYear) - Integer.parseInt(year) + 1;
            } else {
                // 当前用户还没过生日
                age = Integer.parseInt(currentYear) - Integer.parseInt(year);
            }
            map.put("sex", sex);
            map.put("age", age);
            map.put("birth", year + "-" + yue + "-" + day);

            String sql = "select prefecture from label_location " + "where code='" + card.substring(0, 6) + "'";

        } catch (Exception ex) {
        }
        return map;
    }

    public static String RealDictNames(HashMap<String, String> ids) {
        String back = "";

        try {

            for (Map.Entry<String, String> one : ids.entrySet()) {
                String id = one.getKey();
                String names = "";
                if (dicts.containsKey(id)) {

                    names = dicts.get(id).getString("dict_name");

                    back = back + names + ",";
                }

            }
            back = back.substring(0, back.length() - 1);
        } catch (Exception ex) {

        }

        return back;
    }

    public static JSONArray RealDictNameList(HashMap<String, String> ids) {
        JSONArray back = new JSONArray();


        try {
            for (Map.Entry<String, String> one : ids.entrySet()) {
                String id = one.getKey();
                String names = "";
                JSONObject o = new JSONObject();

                if (dicts.containsKey(id)) {

                    o.put("id", id);

                    names = dicts.get(id).getString("dict_name");
                    o.put("name", names);
                    o.put("permission", dicts.get(id).get("permission"));
                    o.put("index_no", dicts.get(id).get("index_no"));
                    back.add(o);

                }
            }
        } catch (Exception ex) {

        }
        return back;
    }

    public static String GetJSid() {
        String back = "";

        for (Map.Entry<String, JSONObject> one : dicts.entrySet()) {
            JSONObject per = one.getValue();
            String id = one.getKey();
            String p = per.getString("permission");
            if (p.contains("J")) {
                back = id;
                break;
            }
        }
        return back;
    }

    public static String GetSZid(String unit, String pos_name) {
        InfoModelHelper mysql = null;
        String back = "";
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select id from dict where type=13 and DECODE(dict_name," + "'1n2a3m4e')='" + pos_name + "' "
                    + "and isdelete=1";
            back = mysql.query_one(sql, "id");
            System.out.println("sz-->" + sql);
            System.out.println("sz-->" + back);

        } catch (Exception ex) {

        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    public static JSONArray GetDictByType(int type) {
        JSONArray back = new JSONArray();
        for (Map.Entry<String, JSONObject> one : RIUtil.dicts.entrySet()) {
            int t = one.getValue().getInteger("type");
            if (t == type) {
                back.add(one.getValue());
            }
        }
        return back;

    }
    public static List<JSONObject> GetDictByTypeList(int type) {
        List<JSONObject> back = new ArrayList<>();
        for (Map.Entry<String, JSONObject> one : RIUtil.dicts.entrySet()) {
            int t = one.getValue().getInteger("type");
            if (t == type) {
                back.add(one.getValue());
            }
        }
        return back;

    }

    public static JSONArray GetDictByTypeFather(int type, String father_id) {
        JSONArray back = new JSONArray();
        for (Map.Entry<String, JSONObject> one : RIUtil.dicts.entrySet()) {
            int t = one.getValue().getInteger("type");
            String fid = one.getValue().getString("father_id");
            if (t == type && father_id.equals(fid)) {
                back.add(one.getValue());
            }
        }
        return back;

    }

    public static JSONArray GetDictByFather(String father_id) {
        JSONArray back = new JSONArray();
        for (Map.Entry<String, JSONObject> one : RIUtil.dicts.entrySet()) {
            //    int t = one.getValue().getInteger("type");
            String fid = one.getValue().getString("father_id");
            if (father_id.equals(fid)) {
                back.add(one.getValue());
            }
        }
        return back;

    }

    public static void main(String[] args) {
        System.out.println(Math.random());
    }


    public static String Chat2Unicode(String str) {
        String codes[] = {"0020", "0021", "0022", "0023", "0024", "0025", "0026", "0027", "0028", "0029", "002A",
                "002B", "002C", "002D", "002E", "002F", "0030", "0031", "0032", "0033", "0034", "0035", "0036", "0037"
                , "0038", "0039", "003A", "003B", "003C", "003D", "003E", "003F", "0040", "0041", "0042", "0043",
                "0044", "0045", "0046", "0047", "0048", "0049", "004A", "004B", "004C", "004D", "004E", "004F", "0500"
                , "0051", "0052", "0053", "0054", "0055", "0056", "0057", "0058", "0059", "005A", "005B", "005C",
                "005D", "005E", "005F", "0060", "0061", "0062", "0063", "0064", "0065", "0066", "0067", "0068", "0069"
                , "006A", "006B", "006C", "006D", "006E", "006F", "0070", "0071", "0072", "0073", "0074", "0075",
                "0076", "0077", "0078", "0079", "007A", "007B", "007C", "007D", "007E"};
        String unis[] = {" ", "!", "“", "#", "$", "%", "&", "'", "(", ")", "*", "+", ",", "-", ".", "/", "0", "1", "2"
                , "3", "4", "5", "6", "7", "8", "9", ":", ";", "<", "=", ">", "?", "@", "A", "B", "C", "D", "E", "F",
                "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
                "[", " ", "]", "^", "_", "`", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n",
                "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "{", "|", "}", "~"};
        HashMap<String, String> unicodes = new HashMap<>();
        for (int i = 0; i < codes.length; i++) {
            unicodes.put(unis[i], codes[i]);
        }
        DecimalFormat df = new DecimalFormat("0000");
        String back = "";
        try {
            back = unicodes.get(str);
            back = "\\\\u" + back;
        } catch (Exception ex) {

        }

        back = back.substring(1);

        return back;
    }

    public static void JsonInsert(JSONObject det, String table) {
        det.remove("opt");
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String keys = "";
            String values = "";
            for (Map.Entry<String, Object> one : det.entrySet()) {
                keys = keys + "`" + one.getKey() + "`,";
                values = values + "'" + one.getValue().toString() + "',";
            }


            String sql =
                    "insert into " + table + "(" + keys.substring(0, keys.length() - 1) + ") values(" + values.substring(0, values.length() - 1) + ")";

            System.out.println(sql);
            ;
            mysql.update(sql);


        } catch (Exception ex) {
            System.out.println(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    public static void JsonInsert_mysql(JSONObject det, String table, MysqlHelper mysql) {
        det.remove("opt");

        try {

            String keys = "";
            String values = "";
            for (Map.Entry<String, Object> one : det.entrySet()) {
                keys = keys + "`" + one.getKey() + "`,";
                values = values + "'" + one.getValue().toString() + "',";
            }


            String sql =
                    "insert into " + table + "(" + keys.substring(0, keys.length() - 1) + ") values(" + values.substring(0, values.length() - 1) + ")";

            System.out.println(sql);
            ;
            mysql.update(sql);


        } catch (Exception ex) {
            System.out.println(Lib.getTrace(ex));
        } finally {
            mysql.close();
        }
    }

    public static void UpdateSql(JSONObject det, String table, String id) {
        det.remove("opt");
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();

            String sql = "";
            for (Map.Entry<String, Object> one : det.entrySet()) {
                sql = sql + " " + one.getKey() + "='" + one.getValue() + "',";
            }


            String sqls = "update " + table + " set " + sql.substring(0, sql.length() - 1) + " where id='" + id + "'";

            System.out.println(sqls);
            ;
            mysql.update(sqls);


        } catch (Exception ex) {
            System.out.println(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    public static List<String> GetUsersByRole(String role_name, String token) {

        List<String> pos = new ArrayList<>();

        List<String> ids = new ArrayList<>();
        String unUrl = TNOAConf.get("HttpServ", "uni_url") + "/role";
        JSONObject det = new JSONObject();
        det.put("opt", "role_get");
        det.put("role_id", role_name);

        String back = "";
        System.out.println(back);
        JSONObject ret = JSONObject.parseObject(back);
        if (ret.containsKey("errno") && ret.getInteger("errno") == 0) {
            JSONObject dd = ret.getJSONArray("data").getJSONObject(0);
            String polices = dd.getString("police_id");
            pos = HashToList(RIUtil.StringToList(polices));


            for (int i = 0; i < pos.size(); i++) {
                String pid = pos.get(i);
                InfoModelHelper mysql = null;
                try {
                    mysql = InfoModelPool.getModel();
                    String sql = "select id from user where id_num='" + pid + "' and isdelete=1";
                    String uid = mysql.query_one(sql, "id");
                    ids.add(uid);

                } catch (Exception ex) {

                } finally {
                    InfoModelPool.putModel(mysql);
                }
            }
            System.out.println(ids);
            return ids;

        } else {
            return new ArrayList<>();
        }


    }


    public static void JsonInsert_ora(JSONObject det, String table, OracleHelper ora) {
        String sql = "";
        try {

            String keys = "";
            String values = "";
            for (Map.Entry<String, Object> one : det.entrySet()) {

                keys = keys + "\"" + one.getKey() + "\",";
                if (one.getValue() == null) {
                    values = values + "'',";
                } else {
                    String val = one.getValue().toString();
                    if (val.contains("TO_DATE") || val.contains("TO_CLOB")) {
                        values = values + val + ",";
                    } else {
                        values = values + "'" + val.replace("'", "") + "',";
                    }
                }
            }

            sql = "insert into \"" + table + "\" (" + keys.substring(0, keys.length() - 1) + ") values("
                    + values.substring(0, values.length() - 1) + ")";

            // System.out.println(sql);

            ora.update(sql);

        } catch (Exception ex) {
            System.out.println(sql);
            System.out.println(Lib.getTrace(ex));
        }

    }

    public static void UpdateSql_ID_ora(JSONObject det, String table, String id, OracleHelper ora, String col) {
        String sql = "";

        String sqls = "";
        try {

            for (Map.Entry<String, Object> one : det.entrySet()) {

                try {
                    String val = one.getValue().toString();
                    if (val != null) {
                        if (val.contains("TO_DATE") || val.contains("TO_CLOB")) {
                            sql = sql + " \"" + one.getKey() + "\" =" + val + ",";

                        } else {
                            sql = sql + " \"" + one.getKey() + "\" ='" + val.replace("'", "") + "',";
                        }
                    }
                } catch (Exception e) {
                    // TODO: handle exception
                }
            }

            sqls = "update \"" + table + "\" set " + sql.substring(0, sql.length() - 1) + " where \"" + col + "\"='"
                    + id + "'";

            ora.update(sqls);

        } catch (Exception ex) {
            System.out.println(sqls);
            System.out.println(Lib.getTrace(ex));
        }
    }

    public static HashMap<String, String> GetPolices() {
        HashMap<String, String> polices = new HashMap<String, String>();
        for (Map.Entry<String, JSONObject> one : users1.entrySet()) {
            JSONObject o = one.getValue();
            String Main = o.getString("isMain");
            if (Main.equals("0")) {
                String id_num = o.getString("id_num");
                if (id_num.length() > 0) {
                    polices.put(id_num, "");
                }
                String tele = o.getString("tele_long");
                if (tele.length() > 0) {
                    polices.put(tele, "");
                }
            }
        }

        return polices;
    }

    public static HashMap<String, JSONObject> GetUsers() {

        HashMap<String, JSONObject> users = new HashMap<>();
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select * from user where status=1 and isdelete=1 ";
            System.out.println(sql);
            List<JSONObject> list = mysql.query(sql);
            System.out.println(list.size() + "---->");
            System.out.println(list.get(4));
            for (int i = 0; i < list.size(); i++) {
                try {
                    JSONObject one = list.get(i);
                    if (!one.containsKey("name")) {
                        break;
                    }
                    if (!one.containsKey("tele_long")) {
                        one.put("tele_long", "");
                    }
                    if (!one.containsKey("position")) {
                        one.put("position", "");
                    } else {
                        String position = one.getString("position");

                        one.put("position_name", RealDictNameList(RIUtil.StringToList(position)));
                    }
                    if (!one.containsKey("org")) {
                        one.put("org", "");
                        one.put("org_name", "");
                    } else {
                        String org = one.getString("org");

                        one.put("org_name", RealDictNameList(RIUtil.StringToList(org)));
                    }

                    if (!one.containsKey("tele_sort")) {
                        one.put("tele_sort", "");
                    }
                    if (!one.containsKey("id_num")) {
                        one.put("id_num", "");
                    }
                    if (!one.containsKey("img")) {
                        one.put("img", "");
                    }
                    if (!one.containsKey("community")) {
                        one.put("community", "");
                    } else {
                        String community = one.getString("community");
                        one.put("community_name", RealDictNameList(RIUtil.StringToList(community)));
                    }
                    if (!one.containsKey("isMain")) {
                        one.put("isMain", "");
                    }
                    if (!one.containsKey("assType")) {
                        one.put("accType", "");
                        one.put("assType_name", "");
                    } else {
                        String accType = one.getString("assType");
                        one.put("assType_name", RealDictNameList(RIUtil.StringToList(accType)));
                    }
                    if (!one.containsKey("open_id")) {
                        one.put("open_id", "");
                    }
                    if (!one.containsKey("unit")) {
                        one.put("unit", "");
                        one.put("unit_name", "");
                    } else {

                        String unit = one.getString("unit");
                        if (unit.endsWith(",")) {
                            unit = unit.substring(0, unit.length() - 1);
                            one.put("unit", unit);
                        }
                        one.put("unit_name", RIUtil.dicts.get(unit));
                    }
                    if (!one.containsKey("car_num")) {
                        one.put("car_num", "");
                    }

                    users.put(one.getString("id"), one);
                } catch (Exception ex) {
                    System.out.println(Lib.getTrace(ex));
                }
            }

            JSONObject one = new JSONObject();
            one.put("name", "全警小喇叭");
            one.put("position", "");
            one.put("position_name", "");
            one.put("tele_long", "");
            one.put("tele_sort", "");
            one.put("img", "");
            one.put("community", "");
            one.put("community_name", "");
            one.put("isMain", "");
            one.put("assType", "");
            one.put("assType_name", "");
            one.put("onoff", "");
            one.put("onoff_time", "");
            one.put("isFamily", "");
            one.put("open_id", "");
            one.put("unit", "");
            one.put("unit_name", "");
            one.put("car_num", "");
            one.put("org", "");
            one.put("org_name", "");
            one.put("id_num", "");

            users.put("999999999", one);


        } catch (Exception ex) {
            System.out.println(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }
        return users;
    }
}
