package HL.TNOA.Lib.Redis;

import HL.TNOA.Lib.TNOAConf;
import redis.clients.jedis.Jedis;

public class RedisPools {
    private static String addr = "127.0.0.1";
    private static int port = Integer.parseInt(TNOAConf.get("redis", "port"));
    private static int timeout = Integer.parseInt(TNOAConf.get("redis", "timeout"));

    public static Jedis get(){
        Jedis redis = new Jedis(addr, port, timeout);
        return redis;
    }
}
