package HL.TNOA.Lib;

import com.alibaba.fastjson.JSONObject;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;


public class OracleHelper_M {
	private Connection conn = null;
	private Statement stmt = null;
	private String url;

	static {
		try {
			Class.forName("oracle.jdbc.driver.OracleDriver");
		} catch (Exception e) {
			System.out.println(e);
		}
	}

	public OracleHelper_M(String key) {

		try {
			String host = TNOAConf.get(key, "addr");
			String port = TNOAConf.get(key, "port");
			String data = TNOAConf.get(key, "data");
			String user = TNOAConf.get(key, "user");
			String pass = TNOAConf.get(key, "pass");

			Class.forName("oracle.jdbc.driver.OracleDriver");
			String url = "jdbc:oracle:thin:@" + host + ":" + port + ":" + data + "";

			conn = DriverManager.getConnection(url, user, pass);

			stmt = conn.createStatement();


		} catch (Exception e) {

			System.out.println(Lib.getTrace(e));

		}
	}

	public void close() {
		if (stmt != null) {
			try {
				stmt.close();

			} catch (Exception e) {
				System.out.println(Lib.getTrace(e));
			}
		}

		if (conn != null) {
			try {
				conn.close();

			} catch (Exception e) {
				System.out.println(Lib.getTrace(e));
			}
		}
	}

	public void update(String sql) throws Exception {
		try {
			stmt.executeUpdate(sql);
		} catch (Exception e) {
			System.out.println(Lib.getTrace(e));
			throw e;
		} finally {
			close();
		}
	}

	public List<JSONObject> query(String sql) throws Exception {
		try {
			List<JSONObject> list = new ArrayList<>();

			ResultSet rs = this.stmt.executeQuery(sql);
			try {
				ResultSetMetaData md = rs.getMetaData();
				int count = md.getColumnCount();

				while (rs.next()) {
					JSONObject map = new JSONObject();
					for (int i = 1; i <= count; i++) {
						if (rs.getString(i) == null) {
							map.put(md.getColumnName(i), "");
						} else {
							map.put(md.getColumnName(i), rs.getString(i));
						}
					}
					list.add(map);
				}
			} finally {
				rs.close();
			}

			return list;
		} catch (Exception e) {
			System.out.println(sql);
			throw e;
		} finally {
			close();
		}
	}

	public int query_count(String sql) throws Exception {
		int c = 0;
		try {

			ResultSet rs = this.stmt.executeQuery(sql);
			try {
				ResultSetMetaData md = rs.getMetaData();
				int count = md.getColumnCount();
				while (rs.next()) {

					for (int i = 1; i <= count; i++) {

						if (md.getColumnName(i).equals("count") || md.getColumnName(i).equals("COUNT")) {
							c = rs.getInt(i);
							break;
						}
					}
				}
			} finally {
				rs.close();
			}

			return c;
		} catch (Exception e) {
			System.out.println(sql);
			throw e;
		} finally {

		}
	}

	public String query_one(String sql, String col) {
		String back = "";
		try {

			ResultSet rs = this.stmt.executeQuery(sql);
			try {
				ResultSetMetaData md = rs.getMetaData();
				int count = md.getColumnCount();
				while (rs.next()) {

					for (int i = 1; i <= count; i++) {
						if (md.getColumnName(i).equals(col)) {
							back = rs.getString(i);
							break;
						}
					}
				}
			} finally {
				rs.close();
			}

			return back;
		} catch (Exception e) {
			System.out.println(sql);
			return "";
		} finally {
			close();
		}
	}

}
