package HL.TNOA.Crontab;

import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class Attendance implements Runnable {
    private static Logger logger = LoggerFactory.getLogger(Attendance.class);


    @Override
    public void run() {
        while (true) {
            try {
                if (new SimpleDateFormat("HH").format(new Date()).equals("09")) {
                    logger.warn("--Check.Attendace--");
                    DoAttendance();
                }

                Thread.sleep(1000 * 60 * 60);
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));

            }
        }

    }

    private void DoAttendance() throws ParseException {
        String yesterday = RIUtil.GetNextDateTime(new SimpleDateFormat("yyyy-MM-dd").format(new Date()), -1);
        yesterday = yesterday.substring(0, 10);
        InfoModelHelper mysql = null;

        try {
            mysql = InfoModelPool.getModel();
            String sqls = "select id, user_id,on_time from class_table where date='" + yesterday + "' and isCheck=1 " +
                    "and att_result=0 and isdelete=1";

            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                HashMap<String, String> laters = new HashMap<>();
                HashMap<String, String> noChecks = new HashMap<String, String>();

                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    String user_id = one.getString("user_id");

                    String on_time = one.getString("on_time");

                    String checkTime = yesterday + " " + on_time + ":59";

                    sqls = "select id from attendance where user_id='" + user_id + "' and (time<='" + checkTime + "' " +
                            "and time>='" + yesterday + " 00:00:00')";
                    List<JSONObject> ll = mysql.query(sqls);
                    if (ll.size() > 0) {
                        sqls = "update class_table set att_result=1 where id='" + id + "'";
                        mysql.update(sqls);

                    } else {

                        //chidao
                        sqls = "select id from attendance where user_id='" + user_id + "' and (time>'" + checkTime +
                                "' and time<='" + yesterday + " 23:59:59')";
                        ll = mysql.query(sqls);
                        logger.warn(sqls);
                        if (ll.size() > 0) {

                            sqls = "update class_table set att_result=2 where id='" + id + "'";
                            logger.warn(sqls);
                            mysql.update(sqls);
                            laters.put(user_id, "");
                        } else {
                            //queka
                            sqls = "update class_table set att_result=3 where id='" + id + "'";
                            mysql.update(sqls);
                            logger.warn(sqls);
                            noChecks.put(user_id, "");
                            // title = "[考勤]未打卡";
                            //content = RIUtil.IdToName(user_id, mysql, "
                            // name", "user") + "于" + yesterday + "未打卡，特此通报。";
                        }

                    }


                }
                String title = "";
                String content = "";
                String level = "-1紧急";
                String names = "";
                //迟到
                if (laters.size() > 0) {
                    title = "[考勤]迟到";


                    for (Map.Entry<String, String> a : laters.entrySet()) {
                        names = names + RIUtil.IdToName(a.getKey(), mysql, " " +
                                "name", "user") + ",";
                    }
                    content = names + "于" + yesterday + "迟到，特此通报。";


                    sqls = "insert brief (title,content," +
                            "level,create_user,accepter,claiming," +
                            "create_time,rela_id,type,isdelete)" +
                            "values(encode('" + title + "','" + RIUtil.enTitle + "'),encode('" + content + "','" + RIUtil.enContent + "')," +
                            "'" + level + "','" + 1 + "','" + RIUtil.HashToList(laters) + "','" + RIUtil.HashToList(laters) + "'," + "'" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "','" + yesterday.replace("-", "") + "1','" + 3 + "','" + 1 + "')";
                    logger.warn(sqls);
                    mysql.update(sqls);
                }
//未打卡
                if (noChecks.size() > 0) {
                    title = "[考勤]未打卡";

                    names = "";
                    for (Map.Entry<String, String> a : noChecks.entrySet()) {
                        names = names + RIUtil.IdToName(a.getKey(), mysql, "name", "user") + ",";
                    }
                    content = names + "于" + yesterday + "未打卡，特此通报。";

                    sqls = "insert brief (title,content," +
                            "level,create_user,accepter,claiming," +
                            "create_time,rela_id,type,isdelete)" +
                            "values(encode('" + title + "','" + RIUtil.enTitle + "'),encode('" + content + "','" + RIUtil.enContent + "')," +
                            "'" + level + "','" + 1 + "','" + RIUtil.HashToList(noChecks) + "','" + RIUtil.HashToList(noChecks) + "'," +
                            "'" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "','" + yesterday.replace("-", "") + "2','" + 3 + "','" + 1 + "')";
                    logger.warn(sqls);
                    mysql.update(sqls);
                }
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }


    }
}
