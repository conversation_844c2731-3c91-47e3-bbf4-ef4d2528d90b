package HL.TNOA.Crontab;

import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.MysqlHelper;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;


public class KillMysqlConn implements Runnable {
    private static Logger logger = LoggerFactory.getLogger(KillMysqlConn.class);

    @Override
    public void run() {
        while (true) {
            MysqlHelper mysql = null;
            try {
                mysql = new MysqlHelper("mysql");
                String sql = "show processlist";
                List<JSONObject> list = mysql.query(sql);
                logger.warn("---->" + list.size());

                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    try {
                        String db = one.getString("db");
                        String command = one.getString("Command");
                        if (db.equals("czoa") && command.equals("Sleep")) {
                            String host = one.getString("Host");
                            int time = one.getIntValue("Time");
                            if (host.startsWith("50.56.176.") || host.startsWith("************")) {

                                if (time > 180) {
                                    sql = "kill " + one.getString("Id");
                                    logger.warn(sql);
                                    // mysql.update(sql);
                                }
                            } else {
                                if (time > 300) {
                                    sql = "kill " + one.getString("Id");
                                     logger.warn(sql);
                                    mysql.update(sql);
                                }
                            }
                        }
                    } catch (Exception ex) {
                        logger.error(one.toString());
                        logger.error(Lib.getTrace(ex));
                    }


                }
                Thread.sleep(1000 * 20);
            } catch (Exception ex) {

                logger.error(Lib.getTrace(ex));
            } finally {
                mysql.close();
            }
        }
    }
}
