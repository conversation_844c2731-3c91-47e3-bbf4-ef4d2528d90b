package HL.TNOA.Crontab;


import HL.TNOA.Lib.*;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.wechat.ConnectWeChat;
import HL.TNOA.wechat.TestMsgLine;
import HL.TNOA.wechat.wechatMsgTemp;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.*;

public class Crontabs implements Runnable {
    private static Logger logger = LoggerFactory.getLogger(Crontabs.class);
    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static SimpleDateFormat sdf_s = new SimpleDateFormat("yyyy-MM-dd");


    @Override
    public void run() {
        //    initStatistic();

        while (true) {
            String hh = new SimpleDateFormat("HH").format(new Date());
            try {
                logger.warn("定时任务在运行");


                //发布公告 提醒事项
                publishNotice_Task();
                //撤销公告置顶 置新
                /// cancelNoticeTopNew();

                //临近事项提醒
                // AlarmNearTask();
                //创建提醒 公告
                createTaskNotice();

                //统计公告 事项 门禁 道闸
                //    StaticAdvance();

                //考核
                //   CheckStatic();
                //创建通报
                //   CreateBrief();

                //创建排班
                createClass();


//设备数据监控
                DeviceAlarm();

                //巡防临近 逾期提醒
                //  NoticePatrol();
                //memo通知
                MemoAlarm();
                publishDing();


                Thread.sleep(5 * 60 * 1000);

            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            }
        }

    }

    public static void publishDing() {
        logger.warn("--PUBLISH.DING--");

        InfoModelHelper mysql = null;

        try {
            String after5 = RIUtil.stampToTime(System.currentTimeMillis() + 1000 * 60 * 5);
            mysql = InfoModelPool.getModel();

            String sqls =
                    "select a.id,a.create_user,a.notice_time,a.is_notice,a.reading,a.readed,a.groups,a.users,a" +
                            ".comment_type,a.comment_select,a.isTop,a.isNew,a.top_date," + "a.new_date,a.type,a" +
                            ".isWechat,isMsg," + "a" + ".create_time,a.isBrief,a.img,a.isAll,a.mark," + "a.accessory,"
                            + "a.link,a" + ".check_time," + "a" + ".cycle,a" + ".cycle_days," + "a.cycle_seconds,a" + ".cycle_end,a.father_id,a.label,a" + ".unit,a.source,a" + ".title,a" + ".content,a" + ".subType from ding_notice  a where " + "is_notice=0 and isdelete=1 and " + "notice_time" + "<='" + after5 + "'";
            logger.warn(sqls);
            List<JSONObject> list = mysql.query(sqls);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    String groups = one.getString("groups");
                    String users = one.getString("users");
                    int isWechat = one.getInteger("isWechat");
                    int isMsg = one.getInteger("isMsg");
                    String readed = one.getString("readed");
                    int isBrief = one.getInteger("isBrief");
                    String unit = one.getString("unit");
                    String title = one.getString("title");
                    String link = one.getString("link");
                    logger.warn(link);
                    //logger.warn(readed);
                    HashMap<String, String> ed = RIUtil.StringToList(readed);
                    HashMap<String, String> uu = new HashMap<String, String>();
                    if (groups.length() > 0) {
                        uu.putAll(RIUtil.GroupsToUsers(groups, mysql));

                    }
                    if (users.length() > 0) {
                        uu.putAll(RIUtil.StringToList(users));

                    }
                    if (uu.size() == 0) {
                        for (Map.Entry<String, JSONObject> uone : RIUtil.users.entrySet()) {
                            String uid = uone.getKey();
                            JSONObject us = uone.getValue();
                            if (us.getString("unit").equals(unit) && !uid.equals("1")) {
                                uu.put(uid, "");
                            }

                        }
                    }


                    if (isWechat == 2 || isWechat == 0 || isMsg == 2 || isMsg == 0) {
                        for (Map.Entry<String, String> d : ed.entrySet()) {
                            String edid = d.getKey();
                            uu.remove(edid);
                        }


                    } else if (isWechat == 1 || isMsg == 1) {
                        readed = "";
                        sqls = "delete from comment where notice_id='" + id + "' and source=9";
                        mysql.update(sqls);
                        isBrief = 0;
                    }
                    // logger.warn(uu.toString());

                    sqls =
                            "update ding_notice set reading='" + RIUtil.HashToList(uu) + "',is_notice=1,readed='" + readed + "'," + "isBrief='" + isBrief + "' where id='" + id + "'";
                    mysql.update(sqls);


                    logger.warn("ischat->" + isWechat);
                    if (isWechat == 1) {
                        one.put("reading", RIUtil.HashToList(uu));
                        logger.warn(one.toString());

                        TestMsgLine send = new TestMsgLine();
                        String url = TNOAConf.get("HttpServ", "notice_url") + id;
                        logger.warn(url);
                        send.sendMSG("您有一条盯办任务，请查收:" + title + "-->" + url, RIUtil.HashToList(uu));
                    }


                }
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    private void MemoAlarm() {

        logger.warn("--MEMO.ALARM--");
        long curr = System.currentTimeMillis() + 5 * 1000 * 60;


        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");
            String clock = RIUtil.stampToTime(curr);
            String sql =
                    "select * from memo where length(end_time)>0 and end_time<='" + clock + "' and isdelete=1 " +
                            "and " + "is_notice=0 and create_time>='2024-11-01 00:00:00' and status=0";
            logger.warn(sql);
            List<JSONObject> list = mysql.query(sql);
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String title = one.getString("title");
                String create_user = one.getString("create_user");
                String id = one.getString("id");

                logger.warn(title + "-->" + create_user);

                TestMsgLine send = new TestMsgLine();
                List<String> uu = new ArrayList<>();
                uu.add(create_user);

                send.sendMSG("[警务日历]" + title, uu);
                sql = "update memo set is_notice=1 where id='" + id + "'";
                mysql.update(sql);
            }

        } catch (Exception ex) {

        } finally {
            mysql.close();
        }
    }

    private void DeviceAlarm() {
        logger.warn("--DEV.ALARM--");
        //type:1 钥匙柜12小时 2门禁1小时 3道闸6小时  9警情4小时
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            //门禁
            String sql = "select time from attendance order by time desc limit 1";
            String time = mysql.query_one(sql, "time");
            long t = RIUtil.dateToStamp(time);
            if (System.currentTimeMillis() - t > 1000 * 60 * 60) {
                sql = "select count(id) as count from device_alarm where type=2";
                int count = mysql.query_count(sql);
                if (count == 0) {
                    List<String> userList = new ArrayList<>();
                    userList.add("320402198706254324");
                    userList.add("320481199406186614");
                    userList.add("320404199103261019");


                    TestMsgLine send = new TestMsgLine();
                    send.sendMSG("已有1小时未收到门禁数据", userList);
                    sql = "insert into device_alarm (type,time)values('2','" + System.currentTimeMillis() + "')";
                    mysql.update(sql);
                }
            } else {
                sql = "delete from device_alarm where type=2";
                mysql.update(sql);
            }
            //道闸
            sql = "select time from gate_event order by time desc limit 1";
            time = mysql.query_one(sql, "time");
            t = RIUtil.dateToStamp(time);
            if (System.currentTimeMillis() - t > 1000 * 60 * 60 * 6) {
                sql = "select count(id) as count from device_alarm where type=3";
                int count = mysql.query_count(sql);
                if (count == 0) {
                    List<String> userList = new ArrayList<>();
                    userList.add("320402198706254324");
                    userList.add("320481199406186614");
                    userList.add("320404199103261019");


                    TestMsgLine send = new TestMsgLine();
                    send.sendMSG("已有6小时未收到道闸数据", userList);
                    sql = "insert into device_alarm (type,time)values('3','" + System.currentTimeMillis() + "')";
                    mysql.update(sql);
                }
            } else {
                sql = "delete from device_alarm where type=3";
                mysql.update(sql);

            }
            sql = "select time from key_event order by time desc limit 1";
            time = mysql.query_one(sql, "time");
            t = RIUtil.dateToStamp(time);
            if (System.currentTimeMillis() - t > 1000 * 60 * 60 * 12) {
                sql = "select count(id) as count from device_alarm where type=1";
                int count = mysql.query_count(sql);
                if (count == 0) {
                    List<String> userList = new ArrayList<>();
                    userList.add("320402198706254324");
                    userList.add("320481199406186614");
                    userList.add("320404199103261019");


                    TestMsgLine send = new TestMsgLine();
                    send.sendMSG("已有12小时未收到钥匙柜数据", userList);
                    sql = "insert into device_alarm (type,time)values('1','" + System.currentTimeMillis() + "')";
                    mysql.update(sql);
                }
            } else {
                sql = "delete from device_alarm where type=1";
                mysql.update(sql);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }


        //警情数据
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            String sql =
                    "select ZXGXSJ from DSJ_JQ WHERE XGSJ>='" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + " 00:00:00' ORDER BY ZXGXSJ DESC ";

            String time = mysql.query_one(sql, "ZXGXSJ");
            long t = RIUtil.dateToStamp(time);
            if (System.currentTimeMillis() - t > 1000 * 60 * 60 * 4) {
                sql = "select count(id) as count from device_alarm where type=9";
                int count = mysql.query_count(sql);
                if (count == 0) {
                    List<String> userList = new ArrayList<>();
                    /*userList.add("320404198701012513");
                    userList.add("32040219900112281X");*/
                    userList.add("411081199304051277");


                    TestMsgLine send = new TestMsgLine();
                    send.sendMSG("已有4小时未收到警情数据", userList);
                    sql = "insert into device_alarm (type,time)values('9','" + System.currentTimeMillis() + "')";
                    mysql.update(sql);
                }
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        } finally {

            ora_hl.close();
        }
    }

    private void NoticePatrol() {
        logger.warn("--Patrol--");

        InfoModelHelper mysql = null;
        String time = new SimpleDateFormat("yyyy-MM-dd HH:ss:mm").format(new Date());
        try {
            mysql = InfoModelPool.getModel();
            //逾期
            String sql = "select id,accepter,checked,address,start_time,end_time from patrol_task " + "where " +
                    "end_time<='" + time + "' and status=0 and isWechat=1 and isdelete=1";
            List<JSONObject> list = mysql.query(sql);
            logger.warn(list.toString());
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    HashMap<String, String> acc = new HashMap<>();
                    String accepter = one.getString("accepter");
                    acc.putAll(RIUtil.StringToList(accepter));
                    String checked = one.getString("checked");
                    acc.putAll(RIUtil.StringToList(checked));
                    String address = one.getString("address");
                    String start_time = one.getString("start_time");
                    String end_time = one.getString("end_time");
                    List<String> addss = RIUtil.HashToList(RIUtil.StringToList(address));
                    String address_name = "";
                    for (int a = 0; a < addss.size(); a++) {
                        address_name = address_name + RIUtil.IdToName(addss.get(i), mysql, "patrol_name",
                                "patrol_info") + " ";
                    }

                    //更新逾期状态
                    sql = "update patrol_task set status=2 where id='" + id + "'";
                    mysql.update(sql);

                    //通报
                    String title = "[云哨未完成]" + start_time.substring(5, 10) + address_name;

                    String content = "[云哨]" + address_name + "巡防:" + start_time + "-" + end_time + "任务未在规定时间内完成，特此通报！";
                    sql = "insert brief (title,content,level," + "create_user,accepter,create_time,rela_id," + "type,"
                            + "isdelete,claiming)" + "values(encode('" + title + "','" + RIUtil.enTitle + "'),encode" + "('" + content + "','" + RIUtil.enContent + "')," + "'-1紧急','" + 1 + "'," + "'" + RIUtil.HashToList(acc) + "','" + sdf.format(new Date()) + "','" + id + "'," + "'5','" + 1 + "','" + RIUtil.HashToList(acc) + "')";
                    mysql.update(sql);
                    logger.warn(sql);

                    String brief_id = "0";
                    sql = "select id from brief " + "where rela_id='" + id + "' and type='" + 5 + "' and " +
                            "accepter='" + RIUtil.HashToList(acc) + "' order by create_time desc limit 1";
                    logger.warn(sql);
                    brief_id = mysql.query_one(sql, "id");

                    for (int a = 0; a < acc.size(); a++) {
                        String user_id = acc.get(a);

                        String check_time = "";

                        sql = "insert brief_static(user_id,brief_id,brief_type,create_time," + "title,rela_id,d_time,"
                                + "finish_time)" + " values('" + user_id + "','" + brief_id + "','" + 2 + "','" + sdf.format(new Date()) + "'," + "encode('" + title + "','" + RIUtil.enTitle + "'),'" + id + "','" + one.getString("end_time") + "','" + check_time + "')";
                        mysql.update(sql);
                        if (title.length() > 16) {
                            title = title.substring(0, 15) + "...";
                        }

                        wechatMsgTemp.createMessage_brief(user_id, brief_id, title, "1", mysql);
                    }
                }
            }


            //临近通知
            sql =
                    "select id,accepter,checked,address,start_time,end_time from patrol_task where near_time<='" + time +
                            "'" + " and status=0 and isdelete=1";
            list = mysql.query(sql);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    HashMap<String, String> acc = new HashMap<>();
                    String accepter = one.getString("accepter");
                    acc.putAll(RIUtil.StringToList(accepter));
                    String checked = one.getString("checked");
                    acc.putAll(RIUtil.StringToList(checked));
                    String address = one.getString("address");
                    String start_time = one.getString("start_time");
                    String end_time = one.getString("end_time");

                    List<String> addss = RIUtil.HashToList(RIUtil.StringToList(address));
                    String address_name = "";
                    for (int a = 0; a < addss.size(); a++) {
                        address_name = address_name + RIUtil.IdToName(addss.get(a), mysql, "patrol_name",
                                "patrol_info") + " ";
                    }
                    String title = "[临近]" + start_time.substring(5, 10) + address_name;
                    if (title.length() > 16) {
                        title = title.substring(0, 15) + "...";
                    }
                    id = "lj_" + id;
                    // PatrolTaskController.createTaskMsg(accepter, id, mysql, title, start_time, end_time);
                }
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private void CommunityCheckNotice() {
        logger.warn("--CommunityCheckNotice--");
        int mark = 0;
        InfoModelHelper mysql = null;


        try {
            mysql = InfoModelPool.getModel();
            String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String lastDay = RIUtil.getLMonthEnd(today);

            int btw = RIUtil.get2DateBetween(today, lastDay);
            logger.warn(lastDay + "-" + today + "=" + btw);
            if (btw < 8) {
                String sql =
                        "select count(id)from msg_log where notice_id='cc_" + new SimpleDateFormat("yyMMddHH").format(new Date()) + "'";
                logger.warn(sql);
                int count = mysql.query_count(sql);
                if (count == 0) {
                    sql =
                            "select id,police,community, detail from community_check where month='" + new SimpleDateFormat(
                                    "yyMM").format(new Date()) + "' and community!='all'";
                    logger.warn(sql);
                    List<JSONObject> list = mysql.query(sql);
                    String content = "";
                    if (list.size() > 0) {
                        for (int i = 0; i < list.size(); i++) {
                            JSONObject one = list.get(i);
                            //logger.warn(one.toString());
                            String user_id = one.getString("police");
                            JSONArray details = one.getJSONArray("detail");
                            if (details != null && details.size() > 0) {
                                for (int d = 0; d < details.size(); d++) {
                                    JSONObject done = details.getJSONObject(d);
                                    String col_name = done.getString("col_cn");
                                    JSONArray keys = done.getJSONArray("keys");
                                    double data_goal = 0;
                                    double data_finish = 0;
                                    for (int k = 0; k < keys.size(); k++) {
                                        JSONObject kone = keys.getJSONObject(k);
                                        String name = kone.getString("name");
                                        if (name.contains("goal")) {
                                            String goal = kone.getString("data");
                                            if (goal.length() > 0) {
                                                data_goal = Double.parseDouble(goal);
                                            }
                                        }
                                        if (name.contains("finish")) {
                                            String finish = kone.getString("data");
                                            if (finish.length() > 0) {
                                                data_finish = Double.parseDouble(finish);
                                            }
                                        }
                                    }
                                    if (data_finish < data_goal) {
                                        content = content + col_name + " ";
                                    }

                                }

                                logger.warn(content);
                            }
                            if (content.length() > 0) {
                                content = content.substring(0, content.length() - 1);
                                if (content.length() > 9) {

                                    content = content.substring(0, 9);
                                }
                                List<String> acces = RIUtil.HashToList(RIUtil.StringToList(user_id));
                                for (int a = 0; a < acces.size(); a++) {
                                    String uid = acces.get(a);
                                    JSONObject accepter = new JSONObject();
                                    accepter.put("id", uid);
                                    accepter.put("open_id", RIUtil.IdToName(uid, mysql, "open_id", "user"));
                                    logger.warn(accepter.toString());


                                    String comment = "社区未完成 " + content + " 尽快完成";
                                    String notice_id = "cc_" + new SimpleDateFormat("yyMMddHH").format(new Date());
                                    wechatMsgTemp.createDingMsg("", comment, "1", 99, accepter.toString(), mysql,
                                            notice_id);
                                }
                            }
                        }
                    }
                }
            }

        } catch (Exception ex) {

        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    private void BirthdayNotice() {
        logger.warn("--BIRTH--");
        int mark = 0;
        String hh = new SimpleDateFormat("HH").format(new Date());
        if (hh.equals("08")) {

            InfoModelHelper mysql = null;
            try {
                mysql = InfoModelPool.getModel();
                String sql =
                        "select count(id) from msg_log where notice_id='b_" + new SimpleDateFormat("yyMMddHH") + "'";
                int count = mysql.query_count(sql);
                if (count == 0) {


                    String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
                    String tom = RIUtil.GetNextDate(today, 1);
                    String mmdd = tom.substring(5);
                    sql =
                            "select  name " + "from `user` where decode(birth," + "'" + RIUtil.enDate + "') like '%" + mmdd + "%' and isdelete=1 and status=1";
                    List<JSONObject> list = mysql.query(sql);
                    String bUsers = "";
                    if (list.size() > 0) {
                        for (int i = 0; i < list.size(); i++) {
                            bUsers = bUsers + list.get(i).getString("name") + " ";
                        }
                        sql = "select id,open_id from `user` " + "where (position like '%01%' or position like " +
                                "'%02%'" + " or position like '%03%' " + "or position like '%04%' or position like " + "'%05%') and " + "isdelete=1 and status=1";
                        List<JSONObject> leaders = mysql.query(sql);
                        String notice_id = "b_" + new SimpleDateFormat("yyMMddHH").format(new Date());
                        String comment = "明天 " + bUsers + "过生日~";
                        wechatMsgTemp.createDingMsg("", comment, "1", 99, bUsers, mysql, notice_id);


                    }
                }


            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));

            } finally {
                InfoModelPool.putModel(mysql);
            }
        }
    }


    private void CheckStatic() {

        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select create_time from brief_static order by create_time  limit 1";
            String start_time = mysql.query_one(sql, "create_time");
            //logger.warn(start_time);
            if (start_time.length() == 0) {
                start_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            }
            String last_time = RIUtil.getLMonthEnd(start_time);
            //logger.warn(last_time);
            String end_time = RIUtil.getLMonthEnd(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            // logger.warn(end_time);
            while (!last_time.equals(end_time)) {
                String month = last_time.substring(2, 7).replace("-", "");
                sql = "select count(id) as count from check_static where time='" + month + "'";
                //logger.warn(sql);
                int count = mysql.query_count(sql);
                if (count == 0) {
                    sql = "select id from user where status=1 and isdelete=1 and id!='1' and position not like '%18%'";
                    List<JSONObject> list = mysql.query(sql);
                    for (int i = 0; i < list.size(); i++) {
                        JSONObject one = list.get(i);
                        String id = one.getString("id");
                        sql =
                                "select count(id) count from brief_static where user_id='" + id + "' and create_time" +
                                        ">='" + start_time + "' and create_time<='" + last_time + "'";
                        count = 100 - mysql.query_count(sql);

                        sql =
                                "insert check_static(user_id,point,time) values('" + id + "','" + count + "','" + month + "')";
                        mysql.update(sql);

                    }

                    //排名
                    sql = "select id,point,user_id  from check_static where time='" + month + "' order by point desc";
                    List<JSONObject> pp = mysql.query(sql);
                    int lastPoint = 0;
                    int lastRank = 1;
                    if (pp.size() > 0) {
                        for (int i = 0; i < pp.size(); i++) {
                            JSONObject one = pp.get(i);
                            String id = one.getString("id");
                            int point = one.getInteger("point");
                            String user_id = one.getString("user_id");
                            if (lastPoint == 0) {
                                lastPoint = point;
                                lastRank = 1;
                            }
                            if (lastPoint > point) {
                                lastPoint = point;
                                lastRank = lastRank + 1;
                            } else {
                                lastPoint = point;
                                lastRank = lastRank;
                            }
                            //notice
                            sql =
                                    "select count(id) as count from notice where (reading like '%" + user_id + "%' " +
                                            "or" + " " + "readed like '%" + user_id + "%') " + "and notice_time>='" + start_time + "' and " + "notice_time<='" + last_time + "' and isdelete=1";
                            int notice_all = mysql.query_count(sql);
                            logger.warn(sql);
                            sql =
                                    "select count(id) as count from brief_static where user_id='" + user_id + "' and " +
                                            "brief_type=1 and create_time>='" + start_time + "' and create_time<='" + last_time + "'";
                            int notice_not = mysql.query_count(sql);
                            if (notice_not >= notice_all) {
                                notice_not = notice_all;
                            }
                            //task
                            sql =
                                    "select count(id) as count from task where (accepter like '%" + user_id + "%' or " +
                                            "finished like '%" + user_id + "%' or checked like '%" + user_id + "%' ) "
                                            + "and" + " " + "start_time>='" + start_time + "' and start_time<='" + last_time + "' and" + " " + "isdelete" + "=1";
                            int task_all = mysql.query_count(sql);
                            logger.warn(sql);
                            sql =
                                    "select count(id) as count from brief_static where user_id='" + user_id + "' and " +
                                            "brief_type=2 and create_time>='" + start_time + "' and create_time<='" + last_time + "'";
                            int task_not = mysql.query_count(sql);
                            if (task_not >= task_all) {
                                task_not = task_all;
                            }

                            sql =
                                    "update check_static set rank='" + lastRank + "',notice_all='" + notice_all + "'," +
                                            "notice_not='" + notice_not + "',task_all='" + task_all + "',task_not='" + task_not +
                                            "'" + " where id='" + id + "'";
                            mysql.update(sql);

                        }
                    }
                }
                start_time = RIUtil.GetNextDate(last_time, 1);
                last_time = RIUtil.getLMonthEnd(start_time);
            }
            if (last_time.equals(end_time)) {
                String month = last_time.substring(2, 7).replace("-", "");
                sql = "delete from check_static where time='" + month + "'";
                mysql.update(sql);
                sql = "select id from user where status=1 and isdelete=1 and id!='1' and position not like '%18%'";
                List<JSONObject> list = mysql.query(sql);
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    sql =
                            "select count(id) count from brief_static where user_id='" + id + "' and create_time>='" + start_time + "' and create_time<='" + last_time + "'";
                    int count = 100 - mysql.query_count(sql);

                    sql = "insert check_static(user_id,point,time) values('" + id + "','" + count + "','" + month +
                            "')";
                    mysql.update(sql);

                }

                //排名
                sql = "select id,point,user_id  from check_static where time='" + month + "' order by point desc";
                List<JSONObject> pp = mysql.query(sql);
                int lastPoint = 0;
                int lastRank = 1;
                if (pp.size() > 0) {
                    for (int i = 0; i < pp.size(); i++) {
                        JSONObject one = pp.get(i);
                        String id = one.getString("id");
                        int point = one.getInteger("point");
                        String user_id = one.getString("user_id");
                        if (lastPoint == 0) {
                            lastPoint = point;
                            lastRank = 1;
                        }
                        if (lastPoint > point) {
                            lastPoint = point;
                            lastRank = lastRank + 1;
                        } else {
                            lastPoint = point;
                            lastRank = lastRank;
                        }
                        //notice
                        sql = "select count(id) as count from notice where (reading like '%" + user_id + "%' or " +
                                "readed like '%" + user_id + "%') " + "and notice_time>='" + start_time + "' and " +
                                "notice_time<='" + last_time + "'";
                        int notice_all = mysql.query_count(sql);
                        sql = "select count(id) as count from brief_static where user_id='" + user_id + "' and " +
                                "brief_type=1 and create_time>='" + start_time + "' and create_time<='" + last_time + "'";
                        int notice_not = mysql.query_count(sql);

                        //task
                        sql = "select count(id) as count from task where (accepter like '%" + user_id + "%' or " +
                                "finished like '%" + user_id + "%' or checked like '%" + user_id + "%' ) " + "and " + "start_time>='" + start_time + "' and start_time<='" + last_time + "'";
                        int task_all = mysql.query_count(sql);
                        sql = "select count(id) as count from brief_static where user_id='" + user_id + "' and " +
                                "brief_type=2 and create_time>='" + start_time + "' and create_time<='" + last_time + "'";
                        int task_not = mysql.query_count(sql);

                        sql = "update check_static set rank='" + lastRank + "',notice_all='" + notice_all + "'," +
                                "notice_not='" + notice_not + "',task_all='" + task_all + "',task_not='" + task_not + "' where id='" + id + "'";
                        mysql.update(sql);


                    }
                }
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private void StaticAdvance() {
        InfoModelHelper mysql = null;
        String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        try {
            mysql = InfoModelPool.getModel();
            String sql = "delete from static_info where date='" + today + "'";
            mysql.update(sql);

            String start_time = today + " 00:00:00";
            String end_time = today + " 23:59:59";
            sql =
                    "select count(id) as count from notice where is_notice=1 and isdelete=1 and (notice_time>='" + start_time + "' and notice_time<='" + end_time + "') ";
            int count = mysql.query_count(sql);
            //logger.warn(sql);
            sql = "insert static_info values('" + today + "','1','" + count + "')";
            //   logger.warn(sql);
            mysql.update(sql);

            sql =
                    "select count(id) as count from task where isPublish=1 and isdelete=1 and (start_time>='" + start_time + "' and start_time<='" + end_time + "') ";
            count = mysql.query_count(sql);
            // logger.warn(sql);
            sql = "insert static_info values('" + today + "','2','" + count + "')";
            //  logger.warn(sql);
            mysql.update(sql);

            sql =
                    "select count(id) as count from gate_event where direction=1 and isdelete=1 and (time>='" + start_time + "' and time<='" + end_time + "') ";
            count = mysql.query_count(sql);
            //  logger.warn(sql);
            sql = "insert static_info values('" + today + "','3','" + count + "')";
            //  logger.warn(sql);
            mysql.update(sql);
            sql =
                    "select count(id) as count from gate_event where direction=2 and isdelete=1 and (time>='" + start_time + "' and time<='" + end_time + "') ";
            count = mysql.query_count(sql);
            //  logger.warn(sql);
            sql = "insert static_info values('" + today + "','4','" + count + "')";
            //  logger.warn(sql);
            mysql.update(sql);

            sql = "select count(id) as count from attendance where dir=1 and (time>='" + start_time + "' and " +
                    "time<='" + end_time + "') ";
            count = mysql.query_count(sql);
            //   logger.warn(sql);
            sql = "insert static_info values('" + today + "','5','" + count + "')";
            //    logger.warn(sql);
            mysql.update(sql);

            sql = "select count(id) as count from attendance where dir=2 and (time>='" + start_time + "' and " +
                    "time<='" + end_time + "') ";
            count = mysql.query_count(sql);
            //  logger.warn(sql);
            sql = "insert static_info values('" + today + "','6','" + count + "')";
            //   logger.warn(sql);
            mysql.update(sql);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private void initStatistic() {
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select * from static_info ";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() == 0) {
                String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
                long t = RIUtil.dateToStamp(today);
                //通告
                sql = "select notice_time from notice where is_notice=1 and isdelete=1 order by notice_time limit 1";
                String start_time = mysql.query_one(sql, "notice_time").substring(0, 10);
                long start = RIUtil.dateToStamp(start_time);

                while (start <= t) {
                    start_time = start_time + " 00:00:00";
                    String end_time = start_time.substring(0, 10) + " 23:59:59";
                    sql = "select count(id) as count from notice where is_notice=1 and isdelete=1 and " +
                            "(notice_time>='" + start_time + "' and notice_time<='" + end_time + "') ";
                    int count = mysql.query_count(sql);
                    logger.warn(sql);
                    sql = "insert static_info values('" + start_time.substring(0, 10) + "','1','" + count + "')";
                    logger.warn(sql);
                    mysql.update(sql);

                    start_time = RIUtil.GetNextDate(start_time, 1);
                    start = RIUtil.dateToStamp(start_time);
                }

                //事项
                sql = "select start_time from task where isPublish=1 and isdelete=1 order by start_time limit 1";
                start_time = mysql.query_one(sql, "start_time").substring(0, 10);
                start = RIUtil.dateToStamp(start_time);

                while (start <= t) {
                    start_time = start_time + " 00:00:00";
                    String end_time = start_time.substring(0, 10) + " 23:59:59";
                    sql =
                            "select count(id) as count from task where isPublish=1 and isdelete=1 and (start_time>='" + start_time + "' and start_time<='" + end_time + "') ";
                    int count = mysql.query_count(sql);
                    sql = "insert static_info values('" + start_time.substring(0, 10) + "','2','" + count + "')";
                    logger.warn(sql);
                    mysql.update(sql);

                    start_time = RIUtil.GetNextDate(start_time, 1);
                    start = RIUtil.dateToStamp(start_time);
                }
                //道闸进
                sql = "select time from gate_event where direction=1 and isdelete=1 order by time limit 1";
                start_time = mysql.query_one(sql, "time").substring(0, 10);
                start = RIUtil.dateToStamp(start_time);

                while (start <= t) {
                    start_time = start_time + " 00:00:00";
                    String end_time = start_time.substring(0, 10) + " 23:59:59";
                    sql =
                            "select count(id) as count from gate_event where direction=1 and isdelete=1 and (time>='" + start_time + "' and time<='" + end_time + "') ";
                    int count = mysql.query_count(sql);
                    sql = "insert static_info values('" + start_time.substring(0, 10) + "','3','" + count + "')";
                    logger.warn(sql);
                    mysql.update(sql);

                    start_time = RIUtil.GetNextDate(start_time, 1);
                    start = RIUtil.dateToStamp(start_time);
                }
                //道闸出
                sql = "select time from gate_event where direction=2 and isdelete=1 order by time limit 1";
                start_time = mysql.query_one(sql, "time").substring(0, 10);
                start = RIUtil.dateToStamp(start_time);

                while (start <= t) {
                    start_time = start_time + " 00:00:00";
                    String end_time = start_time.substring(0, 10) + " 23:59:59";
                    sql =
                            "select count(id) as count from gate_event where direction=2 and isdelete=1 and (time>='" + start_time + "' and time<='" + end_time + "') ";
                    int count = mysql.query_count(sql);
                    sql = "insert static_info values('" + start_time.substring(0, 10) + "','4','" + count + "')";
                    logger.warn(sql);
                    mysql.update(sql);

                    start_time = RIUtil.GetNextDate(start_time, 1);
                    start = RIUtil.dateToStamp(start_time);
                }
                //门禁进
                sql = "select time from attendance where dir=1  order by time limit 1";
                start_time = mysql.query_one(sql, "time").substring(0, 10);
                start = RIUtil.dateToStamp(start_time);

                while (start <= t) {
                    start_time = start_time + " 00:00:00";
                    String end_time = start_time.substring(0, 10) + " 23:59:59";
                    sql = "select count(id) as count from attendance where dir=1 and (time>='" + start_time + "' and "
                            + "time<='" + end_time + "') ";
                    int count = mysql.query_count(sql);
                    sql = "insert static_info values('" + start_time.substring(0, 10) + "','5','" + count + "')";
                    logger.warn(sql);
                    mysql.update(sql);

                    start_time = RIUtil.GetNextDate(start_time, 1);
                    start = RIUtil.dateToStamp(start_time);
                }
                //门禁出
                sql = "select time from attendance where dir=2  order by time limit 1";
                start_time = mysql.query_one(sql, "time").substring(0, 10);
                start = RIUtil.dateToStamp(start_time);

                while (start <= t) {
                    start_time = start_time + " 00:00:00";
                    String end_time = start_time.substring(0, 10) + " 23:59:59";
                    sql = "select count(id) as count from attendance where dir=2 and (time>='" + start_time + "' and "
                            + "time<='" + end_time + "') ";
                    int count = mysql.query_count(sql);
                    sql = "insert static_info values('" + start_time.substring(0, 10) + "','6','" + count + "')";
                    logger.warn(sql);
                    mysql.update(sql);

                    start_time = RIUtil.GetNextDate(start_time, 1);
                    start = RIUtil.dateToStamp(start_time);
                }

            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }



    /*public static void GetGZHPeopleList() {
        String access_token = ConnectWeChat_gzh.GetAccessToken();
        HashMap<String, String> opens = new HashMap<>();
        HashMap<String, String> opens_bak = new HashMap<>();
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select open_id from gzh_temp";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject o = list.get(i);
                    String open_id = o.getString("open_id");
                    opens.put(open_id, "");
                    opens_bak.put(open_id, "");
                }
            }


            String users = new ConnectWeChat_gzh().GetUserList(access_token);
            JSONObject us = JSONObject.parseObject(users);
            JSONObject da = us.getJSONObject("data");
            JSONArray ids = da.getJSONArray("openid");
            //EmojiConverter emojiConverter = EmojiConverter.getInstance();
            for (int i = 0; i < ids.size(); i++) {
                String id = ids.getString(i);
                String info = new ConnectWeChat_gzh().GetUnion(id, access_token);
                if (!opens.containsKey(id)) {
                    JSONObject infos = JSONObject.parseObject(info);

                    String name = infos.getString("nickname").replace(" ", "");
                    logger.warn(name);

                    sql = "insert into gzh_temp(open_id,nick)VALUES('" + id + "','" + name + "')";
                    mysql.update(sql);

                }
                opens_bak.remove(id);

            }
            if (opens_bak.size() > 0) {
                for (Map.Entry<String, String> one : opens_bak.entrySet()) {
                    sql = "delete from gzh_temp where open_id='" + one.getKey() + "'";
                    mysql.update(sql);
                    logger.warn(sql);
                    sql = "update user set gzh_id='' where open_id='" + one.getKey() + "'";
                    mysql.update(sql);
                    logger.warn(sql);
                }
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }*/

    private void createClass() {
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String endTime = "";
            int year = Integer.parseInt(new SimpleDateFormat("yyyy").format(new Date())) + 2;
            String lastyear = year + "-12-31";
            String sql = "select * from class_group where start_date<'" + lastyear + "' and isdelete=1";
            logger.warn(sql);
            List<JSONObject> groups = mysql.query(sql);
            if (groups.size() > 0) {

                for (int g = 0; g < groups.size(); g++) {
                    JSONObject o = groups.get(g);
                    String class_id = o.getString("class_id");
                    String accepter = o.getString("users");
                    String start_date = o.getString("start_date");
                    endTime = RIUtil.GetNextDate(start_date, 30);
                    String group_id = o.getString("id");

                    sql =
                            "select *,decode(class_name,'" + RIUtil.enName + "') as class_name from class where id='" + class_id + "' and isdelete=1";
                    List<JSONObject> list = mysql.query(sql);
                    if (list.size() > 0) {
                        JSONObject one = list.get(0);
                        try {
                            String class_name = one.getString("class_name");
                            String on_time = one.getString("on_time");
                            String off_time = one.getString("off_time");
                            int cycle = one.getInteger("cycle");
                            String cycle_week = one.getString("cycle_week");
                            int class_type = one.getInteger("class_type");
                            int isCheck = one.getInteger("isCheck");

                            List<String> accList = RIUtil.HashToList(RIUtil.StringToList(accepter));
                            List<String> dataList = GetDateList(start_date, endTime, cycle, cycle_week);

                            for (int i = 0; i < dataList.size(); i++) {
                                String date = dataList.get(i);

                                for (int d = 0; d < accList.size(); d++) {
                                    String user_id = accList.get(d);

                                    String sqls =
                                            "select group_id from class_table where  date='" + date + "' and " +
                                                    "class_id='" + class_id + "' group by group_id";

                                    String gid = mysql.query_one(sqls, "group_id");
                                    //logger.warn(gid + "->" + sqls);
                                    if (gid.equals(group_id) || gid.length() == 0) {
                                        sqls = "select count(id) as count from class_table where  date='" + date +
                                                "'" + " and class_id='" + class_id + "' and user_id='" + user_id + "'";
                                        int count = mysql.query_count(sqls);
                                        if (count == 0) {
                                            sqls = "insert class_table (user_id,class_id,on_time,off_time,class_type,"
                                                    + "isCheck,date,isdelete,class_name,group_id)" + "values('" + user_id + "','" + class_id + "','" + on_time + "','" + off_time + "','" + class_type + "'," + "'" + isCheck + "','" + date + "','" + 1 + "',encode('" + class_name + "','" + RIUtil.enName + "'),'" + group_id + "')";
                                            // logger.warn(sqls);
                                            mysql.update(sqls);
                                        }
                                    }


                                }
                                endTime = date;
                            }
                        } catch (Exception e) {
                            logger.error(Lib.getTrace(e));
                        }

                        sql = "update class_group set start_date='" + endTime + "' where id='" + group_id + "'";
                        mysql.update(sql);
                    }
                }
            }
        } catch (Exception ex) {

            logger.error(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private static List<String> GetDateList(String start_date, String endTime, int cycle, String cycle_week) throws Exception {
        List<String> back = new ArrayList<>();
        back.add(start_date);
        long start = RIUtil.dateToStamp(start_date + " 00:00:00");
        long end = RIUtil.dateToStamp(endTime + " 00:00:00") - 1;
        long time = start;
        String timeStr = start_date;
        while (time < end) {
            if (cycle > 0) {
                time = time + cycle * 1000 * 60 * 60 * 24;
                if (time < end) {
                    timeStr = RIUtil.stampToTime(time);
                    //logger.warn(timeStr);
                    back.add(timeStr.substring(0, 10));
                }
            } else {
                time = time + 1 * 1000 * 60 * 60 * 24;
                if (time < end) {
                    timeStr = RIUtil.stampToTime(time);
                    int week = RIUtil.dateToWeek(timeStr);
                    if (week == 0) {
                        week = 7;
                    }

                    //logger.warn(timeStr + "-" + week + "-" + cycle_week);
                    if (cycle_week.contains(String.valueOf(week))) {
                        back.add(timeStr.substring(0, 10));
                    }
                }
            }

        }

        return back;
    }

    private void deleteLongLongAgo() {

        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = " delete from msg_log where notice_id in (select id from task where `status`=1 )and" +
                    "(`status`" + " = 1 or sendTimes = 3);";
            mysql.update(sql);
            sql =
                    "delete from msg_log where notice_id in (SELECT id from notice where is_notice = 1)and(`status` =" +
                            " " + "1"
                            + " or sendTimes = 3);";
            mysql.update(sql);
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }


    private void CreateBrief() {
        logger.warn("--GET.BRIEF--");
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            //统计未统计过的
            String sqls = "select id,type,accepter,create_time,decode(title,'" + RIUtil.enTitle + "') as title," +
                    "rela_id from brief where type in (1,2) order by create_time";
            List<JSONObject> ll = mysql.query(sqls);

            if (ll.size() > 0) {
                for (int i = 0; i < ll.size(); i++) {
                    JSONObject one = ll.get(i);
                    String id = one.getString("id");
                    String type = one.getString("type");
                    String accepter = one.getString("accepter");
                    String create_time = one.getString("create_time");
                    String title = one.getString("title");
                    String rela_id = one.getString("rela_id");
                    sqls = "select count(id) as count from brief_static where brief_id='" + id + "' and " +
                            "brief_type='" + type + "'";
                    int count = mysql.query_count(sqls);
                    if (count == 0) {
                        HashMap<String, String> accs = RIUtil.StringToList(accepter);

                        for (Map.Entry<String, String> a : accs.entrySet()) {
                            String user_id = a.getKey();
                            String d_time = "";
                            String check_time = "";
                            if (type.equals("1")) {
                                sqls = "select notice_time from notice where id='" + rela_id + "'";
                                logger.warn(sqls);
                                String notice_time = mysql.query_one(sqls, "notice_time");
                                if (notice_time.length() > 5) {
                                    d_time = RIUtil.GetNextDateTime(notice_time, 1);
                                }
                                sqls = "select time from notice_check_log where rela_id='" + rela_id + "' and " +
                                        "user_id='" + user_id + "'";
                                check_time = mysql.query_one(sqls, "time");

                            }
                            if (type.equals("2")) {
                                sqls = "select end_time from task where id='" + rela_id + "'";
                                d_time = mysql.query_one(sqls, "end_time");

                                sqls =
                                        "select time from task_log where task_id='" + rela_id + "' and user_id='" + user_id + "' and task_type=2 order by time desc limit 1";
                                check_time = mysql.query_one(sqls, "time");

                            }

                            sqls = "insert brief_static(user_id,brief_id,brief_type,create_time,title,rela_id,d_time,"
                                    + "finish_time) " + "values('" + user_id + "','" + id + "','" + type + "','" + create_time + "',encode('" + title + "','" + RIUtil.enTitle + "'),'" + rela_id + "','" + d_time + "','" + check_time + "')";
                            mysql.update(sqls);
                            logger.warn(sqls);
                        }
                    }
                }
            }
            //task
            sqls = "select id,decode(title,'" + RIUtil.enTitle + "') as title,level,accepter,type,checked," +
                    "end_time,start_time,unit from task where end_time<='" + sdf.format(new Date()) + "' and " +
                    "isdelete=1 and isbrief=0 and (LENGTH(accepter)>2 or LENGTH(checked)>2);";
            List<JSONObject> list = mysql.query(sqls);
            logger.warn(sqls);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    HashMap<String, String> accepter = RIUtil.StringToList(one.getString("accepter"));
                    accepter.putAll(RIUtil.StringToList(one.getString("checked")));
                    List<String> as = RIUtil.HashToList(accepter);
                    String title = one.getString("title");
                    String level = one.getString("level");
                    String type = one.getString("type");
                    String unit = one.getString("unit");
                    logger.warn(type);
                    String start_time = one.getString("start_time");
                    if (type.equals("9999999")) {
                        title = "社区工作清单" + start_time.substring(5, 10);
                    }
                    logger.warn(title);
                    title = "[任务未完成]" + title;
                    String brType = "2";
                    logger.warn(TNOAConf.get("CrontabServ", "briefLevelId") + "->" + level);
                    if (TNOAConf.get("CrontabServ", "briefLevelId").contains(level)) {
                        brType = "4";
                    }
                    String content = "该任务未在:" + one.getString("end_time") + "之前完成，特此通报。";
                    sqls = "insert brief (title,content,level," + "create_user,accepter,create_time,rela_id," +
                            "type,isdelete,claiming,unit)" + "values(encode('" + title + "','" + RIUtil.enTitle + "')"
                            + "," + "encode('" + content + "','" + RIUtil.enContent + "'),'-1紧急','" + 1 + "'," + "'" + as + "','" + sdf.format(new Date()) + "','" + id + "'," + "'" + brType + "','" + 1 + "','" + as + "','" + unit + "')";
                    mysql.update(sqls);
                    logger.warn(sqls);

                    String brief_id = "0";
                    sqls = "select id from brief where rela_id='" + id + "' and type='" + brType + "' and " +
                            "accepter='" + as + "' order by create_time desc limit 1";
                    logger.warn(sqls);
                    brief_id = mysql.query_one(sqls, "id");

                    sqls = "update task_accepter set isbrief=1 where rela_id='" + id + "'";
                    mysql.update(sqls);
                    sqls = "update task set isbrief=1 where id='" + id + "'";
                    mysql.update(sqls);
                    logger.warn(sqls);
                    for (int a = 0; a < as.size(); a++) {
                        String user_id = as.get(a);
                        sqls =
                                "select time from task_log where task_id='" + id + "' and user_id='" + user_id + "' " + "and"
                                        + " task_type=2 order by time desc limit 1";
                        String check_time = mysql.query_one(sqls, "time");


                        sqls = "insert brief_static(user_id,brief_id,brief_type,create_time,title,rela_id,d_time," +
                                "finish_time,unit)" + " values('" + user_id + "','" + brief_id + "','" + 2 + "','" + sdf.format(new Date()) + "',encode('" + title + "','" + RIUtil.enTitle + "'),'" + id + "','" + one.getString("end_time") + "','" + check_time + "','" + unit + "')";
                        mysql.update(sqls);

                        wechatMsgTemp.createMessage_brief(user_id, brief_id, title, "1", mysql);
                    }

                }
            }
            //notice

            String nextTime = new SimpleDateFormat("yyyy-MM-dd HH:ss").format(new Date());

            sqls = "select a.*,decode(a.title,'" + RIUtil.enTitle + "') as title from notice a  where a" +
                    ".check_time<'" + nextTime + "' and length(a.reading)>5 and a.isdelete=1 and a" + ".is_notice=1 " + "and a" + ".isBrief=0 and type=1";
            logger.warn(sqls);
            list = mysql.query(sqls);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    String rela_id = id;
                    String title = one.getString("title");
                    String reading = one.getString("reading");
                    String end_time = one.getString("check_time");
                    String unit = one.getString("unit");
                    HashMap<String, String> accs = RIUtil.StringToList(reading);
                    String names = "";
                    for (Map.Entry<String, String> a : accs.entrySet()) {
                        names = names + RIUtil.IdToName(a.getKey(), mysql, "name", "user") + ",";
                    }
                    names = names.substring(0, names.length() - 1);
                    title = "[公告未阅读]" + title;
                    String content = "该公告未在:" + end_time + "之前阅读，未阅读人员名单如下：\n" + names;
                    sqls = "insert brief (title,content,level,create_user,accepter,create_time,rela_id,type,isdelete,"
                            + "unit)" + "values(encode('" + title + "','" + RIUtil.enTitle + "'),encode('" + content + "','" + RIUtil.enContent + "'),'-1紧急','" + 1 + "','" + reading + "','" + sdf.format(new Date()) + "','" + id + "','" + 1 + "','" + 1 + "','" + unit + "')";
                    mysql.update(sqls);
                    sqls = "update notice set isBrief=1 where id='" + id + "'";
                    mysql.update(sqls);

                    //加入统计
                    sqls = "select id from brief where rela_id='" + id + "' and type=1";
                    String brief_id = mysql.query_one(sqls, "id");

                    for (Map.Entry<String, String> a : accs.entrySet()) {
                        String user_id = a.getKey();
                        String d_time = "";
                        String check_time = "";

                        sqls = "select notice_time from notice where id='" + rela_id + "'";
                        logger.warn(sqls);
                        String notice_time = mysql.query_one(sqls, "notice_time");
                        if (notice_time.length() > 5) {
                            d_time = RIUtil.GetNextDateTime(notice_time, 1);
                        }
                        sqls =
                                "select time from notice_check_log where rela_id='" + rela_id + "' and user_id='" + user_id + "'";
                        check_time = mysql.query_one(sqls, "time");


                        sqls = "insert brief_static(user_id,brief_id,brief_type,create_time,title,rela_id,d_time," +
                                "finish_time,unit)" + " values('" + user_id + "','" + brief_id + "','" + 2 + "','" + sdf.format(new Date()) + "',encode('" + title + "','" + RIUtil.enTitle + "'),'" + rela_id + "','" + d_time + "','" + check_time + "','" + unit + "')";
                        mysql.update(sqls);
                        wechatMsgTemp.createMessage_brief(user_id, brief_id, title, "1", mysql);
                    }
                }


            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }


    }


    private void getAccessToken() {

        logger.warn("--GET.WECHAT.TOKEN--");
        InfoModelHelper mysql = null;

        try {
            mysql = InfoModelPool.getModel();
            String access[] = ConnectWeChat.GetAccessToken().split("\\|");

            long except = Long.parseLong(access[1]);
            logger.warn(System.currentTimeMillis() + "-" + except + "=" + (System.currentTimeMillis() - except));
            if (System.currentTimeMillis() - except > 6800 * 1000) {
                ConnectWeChat.GetAccessToken2h(mysql);
            }
            /*access = ConnectWeChat_gzh.GetAccessToken().split("|");

            except = Long.parseLong(access[1]);
            if (System.currentTimeMillis() - except > 6800 * 1000) {
                ConnectWeChat_gzh.GetAccessToken2h(mysql);
            }*/
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
          /*  try {
                ConnectWeChat.GetAccessToken2h(mysql);
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            }*/
        } finally {
            InfoModelPool.putModel(mysql);

        }
    }


    public static void publishNotice_Task() {
        logger.warn("--PUBLISH.NOTICE--");

        InfoModelHelper mysql = null;

        try {
            String after5 = RIUtil.stampToTime(System.currentTimeMillis() + 1000 * 60 * 5);
            mysql = InfoModelPool.getModel();

            String sqls =
                    "select a.id,a.create_user,a.notice_time,a.is_notice,a.reading,a.readed,a.groups,a.users,a" +
                            ".comment_type,a.comment_select,a.isTop,a.isNew,a.top_date," + "a.new_date,a.type,a" +
                            ".isWechat,isMsg,a.create_time,a.isBrief,a.img,a.isAll,a.mark," + "a.accessory,a.link,a" + ".check_time,a.cycle,a.cycle_days,a.cycle_seconds,a" + ".cycle_end,a.father_id,a.label,a" + ".unit,a.source,decode(a.title," + "'" + RIUtil.enTitle + "') as title,decode(a.content," + "'" + RIUtil.enContent + "')as content,a.subType from notice" + "  a where is_notice=0 " + "and isdelete=1 and notice_time<='" + after5 + "'";
            List<JSONObject> list = mysql.query(sqls);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    String groups = one.getString("groups");
                    String users = one.getString("users");
                    int isWechat = one.getInteger("isWechat");
                    int isMsg = one.getInteger("isMsg");
                    String readed = one.getString("readed");
                    String unit = one.getString("unit");
                    String title = one.getString("title");
                    HashMap<String, String> ed = RIUtil.StringToList(readed);
                    HashMap<String, String> uu = new HashMap<String, String>();
                    if (groups.length() > 0) {
                        uu.putAll(RIUtil.GroupsToUsers(groups, mysql));

                    }
                    if (users.length() > 0) {
                        uu.putAll(RIUtil.StringToList(users));

                    }

                    logger.warn(String.valueOf(uu));
                    if (uu.size() == 0) {

                        String sql = "select id from  user where unit '%" + unit + "%' and isdelete=1";
                        List<JSONObject> ulist = mysql.query(sql);
                        for (int u = 0; u < ulist.size(); u++) {
                            uu.put(ulist.get(u).getString("id"), "");

                        }
                    }
                    logger.warn(String.valueOf(uu));

                    if (isWechat == 2 || isWechat == 0 || isMsg == 2 || isMsg == 0) {
                        for (Map.Entry<String, String> d : ed.entrySet()) {
                            String edid = d.getKey();
                            uu.remove(edid);
                        }


                    } else if (isWechat == 1 || isMsg == 1) {
                        readed = "";
                        sqls = "delete from comment where notice_id='" + id + "' and source=1";
                        mysql.update(sqls);
                    }

                    sqls =
                            "update notice set reading='" + RIUtil.HashToList(uu) + "',readed='" + readed + "' where "
                                    + "id"
                                    + "='" + id + "'";

                    mysql.update(sqls);

                    logger.warn("ischat->" + isWechat);
                    if (isWechat == 1) {
                        one.put("reading", RIUtil.HashToList(uu));
                        TestMsgLine send = new TestMsgLine();
                        send.sendMSG("您有一条通知通报，请查收:" + title, RIUtil.HashToList(uu));
                    }


                }
                sqls =
                        "update notice set is_notice=1 where is_notice=0 and isdelete=1 and notice_time<='" + after5 + "'";
                mysql.update(sqls);
                logger.warn(sqls);

            }

            logger.warn("--PUBLISH.TASK--");
            sqls = "select *,decode(title,'" + RIUtil.enTitle + "') as title,decode(content,'" + RIUtil.enContent +
                    "') as content," + "type " + "from task where isPublish=0 and isdelete=1 and start_time<='" + after5 + "'";

            list = mysql.query(sqls);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    String groups = one.getString("groups");
                    logger.warn(groups);
                    String users = one.getString("users");
                    String create_user = one.getString("create_user");
                    int isWechat = one.getInteger("isWechat");
                    int isMsg = one.getInteger("isMsg");
                    String type = one.getString("type");
                    String finished = one.getString("finished");
                    String checked = one.getString("checked");
                    HashMap<String, String> ck = RIUtil.StringToList(checked);
                    HashMap<String, String> ed = RIUtil.StringToList(finished);
                    HashMap<String, String> accepter = new HashMap<>();
                    if (users.length() > 0) {
                        accepter.putAll(RIUtil.StringToList(users));
                    }
                    if (groups.length() > 0) {
                        accepter.putAll(RIUtil.GroupsToUsers(groups, mysql));
                    }
                    if (users.length() == 0 && groups.length() == 0) {
                        accepter.putAll(RIUtil.StringToList(create_user));
                    }
                    if (isWechat == 2 || isWechat == 0 || isMsg == 2 || isMsg == 0) {
                        for (Map.Entry<String, String> d : ed.entrySet()) {
                            String edid = d.getKey();
                            accepter.remove(edid);
                        }
                        for (Map.Entry<String, String> d : ck.entrySet()) {
                            String edid = d.getKey();
                            accepter.remove(edid);
                        }

                    } else if (isWechat == 1 || isMsg == 1) {
                        finished = "";
                        checked = "";
                        sqls = "delete from comment where notice_id='" + id + "' and source=2";
                        mysql.update(sqls);
                        sqls = "delete from task_log where task_id='" + id + "'";
                        mysql.update(sqls);
                    }

                    List<String> accepters = RIUtil.HashToList(accepter);

                    if (type.equals("9999999")) {
                        checked = accepters.toString();
                        accepters = new ArrayList<String>();
                    }
                    sqls =
                            "update task set accepter='" + accepters + "',finished='" + finished + "',checked='" + checked + "' where id='" + id + "'";
                    mysql.update(sqls);
                    one.put("accepter", accepters);
                    for (int a = 0; a < accepters.size(); a++) {
                        sqls =
                                "insert task_turning (task_id,user_id,time,status)values('" + id + "','" + accepters.get(a) + "','" + sdf.format(new Date()) + "',1)";
                        mysql.update(sqls);
                    }


                    if (isWechat == 1 || isWechat == 2) {
                        wechatMsgTemp.createMessage_task(one, mysql, isWechat, type);
                    }
                    if (isMsg == 1 || isMsg == 2) {
                        sendMsg(one, mysql, isMsg, 2);

                    }


                    //添加人员分表
                    sqls = "delete from task_accepter where rela_id='" + id + "'";
                    mysql.update(sqls);

                    for (Map.Entry<String, String> o : accepter.entrySet()) {

                        sqls = "INSERT INTO `task_accepter` ( `rela_id`, `user_id`, `status`, `start_time`, " +
                                "`end_time`, `near_time`) " + "VALUES ( '" + id + "', '" + o.getKey() + "',  '0', '" + one.getString("start_time") + "', '" + one.getString("end_time") + "', '" + one.getString("near_time") + "');";
                        mysql.update(sqls);
                    }


                }
                sqls = "update task set isPublish=1 where isPublish=0 and isdelete=1 and start_time<='" + after5 + "'";
                mysql.update(sqls);
                logger.warn(sqls);
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    private static void sendMsg(JSONObject one, InfoModelHelper mysql, int isMsg, int type) throws Exception {

        String id = one.getString("id");
        String title = one.getString("title");
        String reading = "";

        String msg = "您有一条公告待阅读,请至小程序查看。";
        if (type == 1) {
            reading = one.getString("reading");
            msg = "您有一条[公告]" + title + "待阅读,请至小程序查看。";
        } else if (type == 2) {
            reading = one.getString("accepter");
            msg = "您有一条[事项]" + title + "待办,请至小程序查看。";
        } else {
            msg = "您有一条消息待阅读,请至小程序查看。";
        }


        List<String> userList = RIUtil.HashToList(RIUtil.StringToList(reading));
        logger.warn(userList.toString());
        if (userList.size() > 0) {
            for (int a = 0; a < userList.size(); a++) {
                try {
                    String reader = userList.get(a);


                    int send = 1;
                    if (isMsg == 2) {
                        String s = "select count(id)as count from msg_log where accepter='" + reader + "' and " +
                                "notice_id='" + id + "' and type=" + type + " and msgType=2";
                        logger.warn(s);
                        int count = mysql.query_count(s);
                        logger.warn("count->" + count);
                        if (count > 0) {
                            send = 0;
                        } else {
                            send = 1;
                        }
                    }
                    if (send == 1) {
                        String sql =
                                "insert msg_log(notice_id,accepter,data,type,status,msgType)" + "values('" + one.getString("id") + "','" + userList.get(a) + "',encode('" + msg + "','" + RIUtil.enContent + "'),'" + type + "',0,2)";
                        mysql.update(sql);
                        logger.warn(sql);
                    }


                } catch (Exception e) {
                    logger.warn(Lib.getTrace(e));
                }
            }
        }
    }


    private void cancelNoticeTopNew() {
        logger.warn("--CANCEL.TOP.NEW--");
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();

            String sqls = "select id,notice_time,isTop,isNew,top_date,new_date from notice where (isTop=1 or isNew=1)"
                    + " and isdelete=1 and type=1";
            List<JSONObject> list = mysql.query(sqls);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    String notice_time = one.getString("notice_time");
                    int isTop = one.getInteger("isTop");

                    int top_date = one.getInteger("top_date");

                    if (isTop == 1) {
                        if (RIUtil.dateToStamp(notice_time) + top_date * 86400000 <= System.currentTimeMillis()) {
                            isTop = 0;
                        }
                    }

                    sqls = "update notice set isTop=" + isTop + "  where id='" + id + "'";
                    mysql.update(sqls);

                }
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }


    }


    private void createTaskNotice() {
        logger.warn("--CREATE.TASK--");
        InfoModelHelper mysql = null;

        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "select id from task where father_id is null and isdelete=1 and cycle_end>='" + sdf.format(new Date()) + "' and cycle>0";
            List<JSONObject> list = mysql.query(sqls);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    sqls =
                            "select id from task where father_id='" + list.get(i).getString("id") + "' order by id " + "desc"
                                    + " limit 1";
                    //logger.warn(sqls);
                    List<JSONObject> ll = mysql.query(sqls);
                    String id = "";
                    if (ll.size() > 0) {
                        id = ll.get(0).getString("id");
                    } else {
                        id = list.get(i).getString("id");
                    }

                    MakeCycleTask(id, 10);
                }
            }
            logger.warn("--create.notice--");
            sqls =
                    "select id from notice where father_id=0 and isdelete=1 and cycle_end>='" + sdf.format(new Date()) + "' "
                            + "and cycle>0";
            list = mysql.query(sqls);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    sqls =
                            "select id from notice where father_id='" + list.get(i).getString("id") + "' order by id " +
                                    "desc limit 1";
                    // logger.warn(sqls);
                    List<JSONObject> ll = mysql.query(sqls);
                    String id = "";
                    if (ll.size() > 0) {
                        id = ll.get(0).getString("id");
                    } else {
                        id = list.get(i).getString("id");
                    }

                    MakeCycleNotice(id, 10, mysql);


                }
            }


            logger.warn("--create.ding--");
            sqls =
                    "select id from ding_notice where father_id=0 and isdelete=1 and cycle_end>='" + sdf.format(new Date()) + "' " + "and cycle>0";
            list = mysql.query(sqls);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    sqls =
                            "select id from ding_notice where father_id='" + list.get(i).getString("id") + "' order " + "by" + " id " + "desc limit 1";
                    // logger.warn(sqls);
                    List<JSONObject> ll = mysql.query(sqls);
                    String id = "";
                    if (ll.size() > 0) {
                        id = ll.get(0).getString("id");
                    } else {
                        id = list.get(i).getString("id");
                    }

                    //  MakeCycleDing(id, 10, mysql);
                }
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    private static void MakeCycleDing(String id, int count, InfoModelHelper mysql) throws Exception {
        String father_id = id;
        String sql = "select notice_time,check_time,cycle_end,cycle,cycle_days,cycle_seconds,source from ding_notice "
                + "where" + " " + "id='" + father_id + "'";
        List<JSONObject> list = mysql.query(sql);
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String notice_time = one.getString("notice_time");
            long start_time_long = RIUtil.dateToStamp(notice_time);
            String cycle_end = one.getString("cycle_end");
            String check_time = one.getString("check_time");
            int cycle_days = one.getInteger("cycle_days");
            int cycle = one.getInteger("cycle");
            int cycle_seconds = one.getInteger("cycle_seconds");
            int mark = 0;
            while (start_time_long < RIUtil.dateToStamp(cycle_end)) {
                // logger.warn(start_time + "->" + end_time);
                if (cycle != 4 && cycle != 7) {
                    notice_time = RIUtil.GetNextDateTime(notice_time, cycle_days);


                } else if (cycle == 4) {//每月
                    String d[] = notice_time.split(" ");
                    String date = d[0];
                    String time = d[1];
                    String[] days = date.split("-");
                    int month = Integer.parseInt(days[1]);
                    int day = Integer.parseInt(days[2]);
                    int year = Integer.parseInt(days[0]);
                    if (day > 28) {
                        day = 28;
                    }
                    if (month < 12) {
                        month = month + 1;
                    } else {
                        month = 1;
                        year = year + 1;
                    }
                    String monthStr = "";
                    if (month < 10) {
                        monthStr = "0" + month;
                    } else {
                        monthStr = String.valueOf(month);
                    }
                    String dayStr = "";
                    if (day < 10) {
                        dayStr = "0" + day;
                    } else {
                        dayStr = String.valueOf(day);
                    }
                    notice_time = year + "-" + monthStr + "-" + dayStr + " " + time;


                } else if (cycle == 7)//每年
                {
                    String d[] = notice_time.split(" ");
                    String date = d[0];
                    String time = d[1];
                    String[] days = date.split("-");
                    int month = Integer.parseInt(days[1]);
                    int day = Integer.parseInt(days[2]);
                    int year = Integer.parseInt(days[0]);
                    notice_time = (year + 1) + "-" + days[1] + "-" + days[2] + " " + time;
                }
                start_time_long = RIUtil.dateToStamp(notice_time);
                if (start_time_long < RIUtil.dateToStamp(cycle_end)) {
                    long end_time_long = start_time_long + cycle_seconds;
                    check_time = RIUtil.stampToTime(end_time_long);

                    // logger.warn(start_time + "->" + end_time);

                    String sqls = "insert ding_notice (title,content,create_user,notice_time,is_notice,reading," +
                            "readed," + "groups,users,comment_type,comment_select," + "isTop,isNew,top_date,new_date,"
                            + "type," + "create_time,isdelete,isWechat,isMsg,img,isAll,accessory,link,check_time," +
                            "cycle," + "cycle_days,cycle_seconds,cycle_end,father_id,source,temp_id)" + "select " +
                            "title,content," + "create_user,'" + notice_time + "' as notice_time,'0' as is_notice," + "'','',groups,users," + "comment_type,comment_select," + "isTop,isNew,top_date," + "new_date,type,'" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' as create_time,isdelete,isWechat,isMsg,img,isAll,accessory,link,'" + check_time + "' as check_time," + "cycle,cycle_days,cycle_seconds,cycle_end,'" + father_id + "' as" + " father_id,source,temp_id from ding_notice where id='" + father_id + "'";
                    logger.warn(sqls);
                    mysql.update(sqls);
                    if (count != -1) {//全部
                        mark++;
                        if (mark > count) {
                            break;
                        }
                    }
                }
            }
        }

    }

    private static void MakeCycleNotice(String id, int count, InfoModelHelper mysql) throws Exception {

        String sql =
                "select notice_time,check_time,cycle_end,cycle,cycle_days,cycle_seconds,father_id from notice " +
                        "where " + "id='" + id + "'";
        List<JSONObject> list = mysql.query(sql);
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String notice_time = one.getString("notice_time");
            long start_time_long = RIUtil.dateToStamp(notice_time);
            String cycle_end = one.getString("cycle_end");
            String check_time = one.getString("check_time");
            String father_id = one.getString("father_id");
            int cycle_days = one.getInteger("cycle_days");
            int cycle = one.getInteger("cycle");
            int cycle_seconds = one.getInteger("cycle_seconds");
            int mark = 0;
            while (start_time_long < RIUtil.dateToStamp(cycle_end)) {
                // logger.warn(start_time + "->" + end_time);
                if (cycle != 4 && cycle != 7) {
                    notice_time = RIUtil.GetNextDateTime(notice_time, cycle_days);


                } else if (cycle == 4) {//每月
                    String d[] = notice_time.split(" ");
                    String date = d[0];
                    String time = d[1];
                    String[] days = date.split("-");
                    int month = Integer.parseInt(days[1]);
                    int day = Integer.parseInt(days[2]);
                    int year = Integer.parseInt(days[0]);
                    if (day > 28) {
                        day = 28;
                    }
                    if (month < 12) {
                        month = month + 1;
                    } else {
                        month = 1;
                        year = year + 1;
                    }
                    String monthStr = "";
                    if (month < 10) {
                        monthStr = "0" + month;
                    } else {
                        monthStr = String.valueOf(month);
                    }
                    String dayStr = "";
                    if (day < 10) {
                        dayStr = "0" + day;
                    } else {
                        dayStr = String.valueOf(day);
                    }
                    notice_time = year + "-" + monthStr + "-" + dayStr + " " + time;


                } else if (cycle == 7)//每年
                {
                    String d[] = notice_time.split(" ");
                    String date = d[0];
                    String time = d[1];
                    String[] days = date.split("-");
                    int month = Integer.parseInt(days[1]);
                    int day = Integer.parseInt(days[2]);
                    int year = Integer.parseInt(days[0]);
                    notice_time = (year + 1) + "-" + days[1] + "-" + days[2] + " " + time;
                }
                start_time_long = RIUtil.dateToStamp(notice_time);
                if (start_time_long < RIUtil.dateToStamp(cycle_end)) {
                    long end_time_long = start_time_long + cycle_seconds;
                    check_time = RIUtil.stampToTime(end_time_long);

                    // logger.warn(start_time + "->" + end_time);

                    String sqls = "insert notice (title,content,create_user,notice_time,is_notice,reading,readed," +
                            "groups,users,comment_type,comment_select," + "isTop,isNew,top_date,new_date,type," +
                            "create_time," + "isdelete,isWechat,isMsg,img,isAll,accessory,link,check_time," + "cycle,"
                            + "cycle_days," + "cycle_seconds,cycle_end,father_id,unit,source)" + "select title," +
                            "content," +
                            "create_user," + "'" + notice_time + "'" + " as notice_time,'0' as is_notice,'',''," +
                            "groups,users," + "comment_type,comment_select," + "isTop," + "isNew,top_date,new_date," + "type,'" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' as " + "create_time,isdelete,isWechat,isMsg,img,isAll,accessory,link,'" + check_time + "' as " + "check_time," + "cycle,cycle_days,cycle_seconds,cycle_end,'" + father_id + "' as " + "father_id,unit,source from notice where id='" + father_id + "'";
                    //logger.warn(sqls);
                    mysql.update(sqls);
                    if (count != -1) {//全部
                        mark++;
                        if (mark > count) {
                            break;
                        }
                    }
                }
            }
        }

    }


    private void AlarmNearTask() {
        logger.warn("--LJTX--");
        InfoModelHelper mysql = null;

        try {

            mysql = InfoModelPool.getModel();

            String sqls = "select *,decode(title,'" + RIUtil.enTitle + "') as title,type from task " + "where " +
                    "(isWechat=1 " + "or isWechat=2) and isdelete=1 and isPublish=1 and near_time<='" + sdf.format(new Date()) + "' and " + "status=0 and type!=22222";
            logger.warn(sqls);
            List<JSONObject> list = mysql.query(sqls);
            //logger.warn(list.toString());
            if (list.size() > 0) {

                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    one.put("id", one.getString("id") + "LJTX");
                    String title = one.getString("title");
                    if (title.length() > 10) {
                        title = title.substring(0, 10);
                    }
                    title = "[临近提醒]" + title;
                    String type = one.getString("type");

                    one.put("title", title);
                    one.put("isWechat", 1);
                    logger.warn(one.getString("id") + "->" + title);
                    sqls = "update task set status=2 where id='" + one.getString("id").replace("LJTX", "") + "'";
                    mysql.update(sqls);

                    wechatMsgTemp.createMessage_task(one, mysql, 1, type);
                }
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }
    }


    public static void MakeCycleTask(String id, int count) {
        InfoModelHelper mysql = null;
        String sql = "";
        String title = "";
        String content = "";
        int level = 0;
        int type = 0;
        String start_time = "";
        String end_time = "";
        int cycle = 0;
        int cycle_days = 0;
        long cycle_seconds = 0;
        String father_id = "";
        String cycle_end = "";
        String users = "";
        String groups = "";
        String accepter = "";
        int status = 0;
        int isPublish = 0;
        String create_user = "";
        String create_time = "";
        int isdelete = 1;
        int isWeChat = 0;
        int isMsg = 0;
        String near_time = "";
        String isFA = "0";
        String isSub = "0";
        try {

            mysql = InfoModelPool.getModel();
            sql = "select *,decode(title,'" + RIUtil.enTitle + "') as title,decode(content,'" + RIUtil.enContent +
                    "') as content," + "type " + "from task where id='" + id + "' and isdelete=1 and cycle>0";
            //logger.warn(sql);
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                JSONObject one = list.get(0);
                title = one.getString("title");
                content = one.getString("content");
                level = one.getInteger("level");
                type = one.getInteger("type");
                start_time = one.getString("start_time");
                long start_time_long = RIUtil.dateToStamp(start_time);
                end_time = one.getString("end_time");
                father_id = one.getString("father_id");
                if (father_id.length() == 0) {
                    father_id = id;
                }

                cycle = one.getInteger("cycle");
                cycle_days = one.getInteger("cycle_days");
                cycle_seconds = one.getLong("cycle_seconds");

                cycle_end = one.getString("cycle_end");
                near_time = one.getString("near_time");
                long nearend = RIUtil.dateToStamp(near_time) - RIUtil.dateToStamp(start_time);

                users = one.getString("users");
                groups = one.getString("groups");
                accepter = one.getString("accepter");
                isWeChat = one.getInteger("isWechat");
                isMsg = one.getInteger("isMsg");
                status = 0;
                isPublish = 0;
                create_user = one.getString("create_user");

                isdelete = one.getInteger("isdelete");

                isFA = one.getString("isFA");
                isSub = one.getString("isSub");
                int mark = 0;
                while (start_time_long < RIUtil.dateToStamp(cycle_end)) {
                    // logger.warn(start_time + "->" + end_time);
                    if (cycle != 4 && cycle != 7) {
                        start_time = RIUtil.GetNextDateTime(start_time, cycle_days);


                    } else if (cycle == 4) {//每月
                        String d[] = start_time.split(" ");
                        String date = d[0];
                        String time = " 00:00:00";
                        try {
                            time = d[1];
                        } catch (Exception ex) {
                            start_time = start_time.trim() + " " + time;
                        }
                        String[] days = date.split("-");
                        int month = Integer.parseInt(days[1]);
                        int day = Integer.parseInt(days[2]);
                        int year = Integer.parseInt(days[0]);
                        if (day > 28) {
                            day = 28;
                        }
                        if (month < 12) {
                            month = month + 1;
                        } else {
                            month = 1;
                            year = year + 1;
                        }
                        String monthStr = "";
                        if (month < 10) {
                            monthStr = "0" + month;
                        } else {
                            monthStr = String.valueOf(month);
                        }
                        String dayStr = "";
                        if (day < 10) {
                            dayStr = "0" + day;
                        } else {
                            dayStr = String.valueOf(day);
                        }
                        start_time = year + "-" + monthStr + "-" + dayStr + " " + time;


                    } else if (cycle == 7)//每年
                    {
                        String d[] = start_time.split(" ");
                        String date = d[0];
                        String time = d[1];
                        String[] days = date.split("-");
                        int month = Integer.parseInt(days[1]);
                        int day = Integer.parseInt(days[2]);
                        int year = Integer.parseInt(days[0]);
                        start_time = (year + 1) + "-" + days[1] + "-" + days[2] + " " + time;
                    }
                    start_time_long = RIUtil.dateToStamp(start_time.trim());
                    if (start_time_long < RIUtil.dateToStamp(cycle_end)) {
                        long end_time_long = start_time_long + cycle_seconds;
                        end_time = RIUtil.stampToTime(end_time_long);
                        long near_time_long = start_time_long + nearend;
                        near_time = RIUtil.stampToTime(near_time_long);
                        create_time = RIUtil.stampToTime(System.currentTimeMillis());
                        // logger.warn(start_time + "->" + end_time);
                        id = String.valueOf(UUID.randomUUID());
                        String sqls = "insert task (father_id,title," + "content,level,type,start_time," + "end_time,"
                                + "cycle,cycle_days,cycle_end,users,groups,status," + "isPublish,create_user," +
                                "create_time," + "isdelete," + "cycle_seconds,accepter," + "isWechat,isMsg,near_time,"
                                + "isFA,isSub)" + "values" + "('" + father_id + "',encode('" + title + "','" + RIUtil.enTitle + "')," + "encode('" + content + "'," + "'" + RIUtil.enContent + "'),'" + level + "','" + type + "','" + start_time + "'" + ",'" + end_time + "','" + cycle + "'," + "'" + cycle_days + "','" + cycle_end + "','" + users + "','" + groups + "'," + "'" + status + "','" + isPublish + "'," + "'" + create_user + "','" + create_time + "','" + isdelete + "'," + "'" + cycle_seconds + "','" + accepter + "'," + "'" + isWeChat + "','" + isMsg + "','" + near_time + "','" + isFA + "','" + isSub + "') ";
                        //logger.warn(sqls);
                        mysql.update(sqls);
                        if (count != -1) {//全部
                            mark++;
                            if (mark > count) {
                                break;
                            }
                        }
                    }
                }


            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));

        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

}
