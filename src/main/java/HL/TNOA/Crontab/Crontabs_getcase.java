package HL.TNOA.Crontab;

import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.Lib.TNOAConf;
import HL.TNOA.wechat.HttpConnection;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.*;

public class Crontabs_getcase implements Runnable {
    private static Logger logger = LoggerFactory.getLogger(Crontabs_getcase.class);
    private static SimpleDateFormat s = new SimpleDateFormat("yyyyMMdd");
    private static SimpleDateFormat d = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private HashMap<String, JSONObject> xzls = new HashMap<>();
    private HashMap<String, String> xz_user = new HashMap<>();

    @Override
    public void run() {


        while (true) {
            try {
                initleaders();
                SimpleDateFormat sdf1 = new SimpleDateFormat("HH");
                SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMddHHmmss");
                String time = sdf1.format(new Date());
                if (time.equals("00")) {
                    logger.warn("获取案件线程开始工作啦~~~~~~~~~~~~~~~");

                    for (Map.Entry<String, JSONObject> one : xzls.entrySet()) {
                        String gadm = one.getKey();
                        JSONObject xz = one.getValue();
                        logger.warn(xz.toString());
                        GetCase(gadm, xz);
                        GetCaseObject(xz);
                    }


                    logger.warn("获取案件线程完成工作啦~~~~~~~~~~~~~~~");
                }
                Thread.sleep(60 * 1000 * 60);
            } catch (Exception ex) {

            }
        }
    }

    private void initleaders() {
        String xz_leader = TNOAConf.get("HttpServ", "xz_leader");
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql =
                    "select unit,police_id,id_num, tele_long,decode" +
                            " name from  user where position  like " + "'%" + xz_leader + "%' group by unit;";

            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    logger.warn(one.toString());
                    String unit = one.getString("unit");
                    String id_num = one.getString("id_num");
                    String name = one.getString("name");
                    String tele = one.getString("tele_long");
                    //logger.warn(RIUtil.dicts.toString());

                    JSONObject dict = RIUtil.dicts.get(unit);
                    // logger.warn(dict.toString());
                    String dict_name = dict.getString("dict_name");
                    String gadm = dict.getString("gadm");

                    JSONObject xz = new JSONObject();

                    xz.put("dyrdwbm", gadm);
                    xz.put("dyrdwmc", dict_name);
                    xz.put("dyrip", "***********");
                    xz.put("dyyy", "智慧派出所警务打处功能案件调用");
                    xz.put("dyrlxfs", tele);
                    xz.put("dyrxm", name);
                    xz.put("dyrgmsfhm", id_num);
                    xz.put("unit", unit);

                    xzls.put(gadm, xz);
                }
            } else {
               /* JSONObject xz = new JSONObject();

                xz.put("dyrdwbm", "320402580000");
                xz.put("dyrdwmc", "翠竹派出所");
                xz.put("dyrip", "***********");
                xz.put("dyyy", "智慧派出所警务打处功能案件调用");
                xz.put("dyrlxfs", "13951212059");
                xz.put("dyrxm", "易鑫");
                xz.put("dyrgmsfhm", "362201199505150613");
                xz.put("unit", "089783e5-aad6-4207-8709-f8b3cbbbfdbc");

                xzls.put("320402580000", xz);*/
            }

            sql = "select id, name ,unit from user where status=1 and " +
                    "isdelete=1 and " + "isMain=0";
            List<JSONObject> list1 = mysql.query(sql);
            if (list1.size() > 0) {
                for (int i = 0; i < list1.size(); i++) {
                    JSONObject one = list1.get(i);
                    String id = one.getString("id");
                    String name = one.getString("name");
                    String unit = one.getString("unit");

                    xz_user.put(name + "," + unit, id);
                }
            }


        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    private void GetCaseStatus(String ajbh, String id, JSONObject xz) {
        InfoModelHelper mysql = null;
        try {
//            logger.warn("获取案件状态方法起到作用啦");
            String condition = "AJBH='" + ajbh + "'";
            mysql = InfoModelPool.getModel();
            JSONObject one = new JSONObject();
            JSONObject two = new JSONObject();

            one.put("fwbh", "S-320400230000-0100-00307");
            one.put("lx", "02");
            one.put("ds", "3204");
            one.put("type", "0");
            two.put("condition", condition);
            two.put("requiredItems", "AJBH,DJSJ,ZBDRXM,AJMC,AJFAB,AJLB,AJZT,ZBDW,AJDL");
            two.put("pageSize", "10");
            two.put("pageNum", "1");
            one.put("required", two);

            one.put("realInfo", xz);
            logger.warn(one.toString());
            String post = HttpConnection.post("http://50.56.88.176:18888/serverInterface/dataserver" +
                    "/getParamAndContent", one);
            JSONObject url = JSONObject.parseObject(post);
            String total = url.getString("total");
            JSONArray results = url.getJSONArray("results");
            JSONObject jsonObject = results.getJSONObject(0);
            String ajzt = jsonObject.getString("AJZT");
            int status = 0;
            if (ajzt.contains("不立案") || ajzt.contains("当场处罚") || ajzt.contains("处罚") || ajzt.contains("结案") || ajzt.contains("撤销案") || ajzt.contains("终止案件调查") || ajzt.contains("简易治安调解")) {
                status = 1;
            } else {
                status = 0;
            }
            String sql = "update case_info set status=" + status + ",ajzt='" + ajzt + "' where id='" + id + "'";
            mysql.update(sql);

        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private void GetCaseObject(JSONObject xz) {
        try {
            InfoModelHelper mysql = InfoModelPool.getModel();
            String unit = xz.getString("unit");
            String sql = "select id,case_number from case_info where status=0 and unit='" + unit + "'";
            List<JSONObject> query = mysql.query(sql);
            for (int i = 0; i < query.size(); i++) {
                JSONObject object = query.get(i);
                String id = object.getString("id");
                String case_number = object.getString("case_number");
                xz.remove("unit");
                GetCaseObjectByUrl(id, case_number, xz);
                //GetCaseStatus(case_number, id, xz);
            }
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
        }

    }

    private void GetCaseObjectByUrl(String id, String case_number, JSONObject xz) {
        InfoModelHelper mysql = null;
        try {
//            logger.warn("获取嫌疑人方法起到作用啦");
            String sql = "";
            int page = 1;
            String condition = " AJBH='" + case_number + "'";
            while (true) {
                JSONObject one = new JSONObject();
                JSONObject two = new JSONObject();

                one.put("fwbh", "S-320400230000-0100-00008");
                one.put("lx", "02");
                one.put("ds", "3204");
                one.put("type", "0");
                two.put("condition", condition);
                two.put("requiredItems", "AJBH,GMSFHM,XM,LXDH");
                two.put("pageSize", "10");
                two.put("pageNum", String.valueOf(page));
                one.put("required", two);

                one.put("realInfo", xz);
                logger.warn(one.toString());
                String post = HttpConnection.post("http://50.56.88.176:18888/serverInterface/dataserver" +
                        "/getParamAndContent", one);
                JSONObject url = JSONObject.parseObject(post);
                String total = url.getString("total");
                JSONArray results = url.getJSONArray("results");
                mysql = InfoModelPool.getModel();
//                sql = "delete from case_object where case_id='" + id + "'";
//                mysql.update(sql);
                if (results.size() > 0) {
                    for (int i = 0; i < results.size(); i++) {
                        JSONObject js = results.getJSONObject(i);
                        String id_card = js.getString("GMSFHM");
                        String phone = js.getString("LXDH");
                        String name = js.getString("XM");
                        sql = "select id from case_object where case_id='" + id + "' and decode(id_num,'" + RIUtil.enNum + "')='" + id_card + "'";
                        List<JSONObject> query = mysql.query(sql);
                        if (query.size() == 0) {
                            sql = "insert into case_object (case_id,obj_name,tele,id_num) values('" + id + "',encode" + "('" + name + "','" + RIUtil.enName + "')," + "encode('" + phone + "','" + RIUtil.enTele + "'),encode('" + id_card + "','" + RIUtil.enNum + "'))";
                            mysql.update(sql);
                        }

                    }
                }

                Integer integer = Integer.valueOf(total);
                if (integer != 10) {
                    break;
                }
                page++;
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private void GetCase(String code, JSONObject xz) {
        InfoModelHelper mysql = null;
        try {
//            logger.warn("GetCase方法有起到作用哦");
            mysql = InfoModelPool.getModel();
            String sql = "";
            String unit = "";
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMddHHmmss");
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.add(Calendar.DATE, -1);
            String time = s.format(cal.getTime());
            String start_time = time + "000000";
            String end_time = time + "235959";
            logger.warn(start_time);
            logger.warn(end_time);
            String condition = "ZBDW like '%" + code + "%' and DJSJ>='" + start_time + "' and DJSJ<='" + end_time + "'";
            int page = 1;
            while (true) {
                JSONObject one = new JSONObject();
                JSONObject conditions = new JSONObject();
                JSONObject xz_leader = new JSONObject();
                one.put("fwbh", "S-320400230000-0100-00307");
                one.put("lx", "02");
                one.put("ds", "3204");
                one.put("type", "0");
                conditions.put("condition", condition);
                conditions.put("requiredItems", "AJBH,DJSJ,ZBDRXM,AJMC,AJFAB,AJLB,AJZT,ZBDW,AJDL");
                conditions.put("pageSize", "10");
                conditions.put("pageNum", String.valueOf(page));
                one.put("required", conditions);

                one.put("realInfo", xz);
                logger.warn(one.toString());
                String post = HttpConnection.post("http://50.56.88.176:18888/serverInterface/dataserver" +
                        "/getParamAndContent", one);
                JSONObject url = JSONObject.parseObject(post);
                String total = url.getString("total");
                JSONArray results = url.getJSONArray("results");
                sql = "select id from dict where gadm='" + code + "'";
                unit = mysql.query_one(sql, "id");
                if (results.size() > 0) {
                    for (int i = 0; i < results.size(); i++) {
                        JSONObject o = results.getJSONObject(i);
                        String type = "1";
                        String ajbh = o.getString("AJBH");
                        String ajmc = o.getString("AJMC");
                        String zbdw = o.getString("ZBDW");
                        String zbrxm = o.getString("ZBRXM");
                        if (xz_user.containsKey(zbrxm + "," + unit)) {
                            zbrxm = xz_user.get(zbrxm + "," + unit);
                        }

                        String ajzt = o.getString("AJZT");
                        String ajlb = o.getString("AJLB");
                        String djsj = o.getString("DJSJ");
                        String ajdl = o.getString("AJDL");
                        if (ajdl.contains("行政")) {
                            type = "1";
                        } else if (ajdl.contains("刑事")) {
                            type = "2";
                        } else {
                            type = "3";
                        }
                        sql = "select id from case_info where unit='" + zbdw + "' and case_number='" + ajbh + "'";
                        List<JSONObject> query = mysql.query(sql);
                        if (query.size() == 0) {
                            Date parse = sdf2.parse(djsj);
                            String format = d.format(parse);
                            sql = "insert into case_info (case_number,case_name,ajzt,create_user,create_time,unit," + "form,type,accepter) values('" + ajbh + "',encode('" + ajmc + "','" + RIUtil.enTitle + "')" + ",'" + ajzt + "'," + "'" + zbrxm + "','" + format + "','" + unit + "','" + ajlb + "','" + type + "','" + zbrxm + "')";
                            logger.warn(sql);
                            mysql.update(sql);
                        }

                    }

                }
                Integer integer = Integer.valueOf(total);
                if (integer != 10) {
                    break;
                }
                page++;
            }
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }
}
