package HL.TNOA.Crontab;

import HL.TNOA.Lib.Lib;
import HL.TNOA.Start.HeartBeat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CrontabServ {
    private static Logger logger = LoggerFactory.getLogger(CrontabServ.class);

    public static void main(String[] args) {
        try {
            HeartBeat heartbeat = new HeartBeat();
            heartbeat.heartbeat_init(CrontabServ.class.getName());
            // new Thread(new initBasicInfo()).start();
            new Thread(new Crontabs()).start();
            new Thread(new KillMysqlConn()).start();

            while (true) {
                heartbeat.heartbeat();
                Lib.sleep(100);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }
    }
}
