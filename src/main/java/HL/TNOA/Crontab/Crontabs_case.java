package HL.TNOA.Crontab;

import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.wechat.wechatMsgTemp;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public class Crontabs_case implements Runnable {
    private static Logger logger = LoggerFactory.getLogger(Crontabs_case.class);
    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static SimpleDateFormat sdf_s = new SimpleDateFormat("yyyy-MM-dd");

    @Override
    public void run() {
        while (true) {
            try {
                //待侦
                //DaiZhenNotice();
                //在侦30天提醒
                //ZaiZhenNotice();
                //case_step 中循环
                //StepCycleNotice();
                //case_step 中单独一次提醒
                // StepNotice();
                //case_list
                ListNotice();
                Thread.sleep(5 * 60 * 1000);
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            }
        }
    }

    private void ListNotice() {
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select a.id,a.case_id,DECODE(b.case_name,'1t2i3t4l5e') as case_name,a.notice_time,a.cycle_seconds," +
                    "b.accepter,b.create_user," + "decode(a.list_name,'" + RIUtil.enName + "') as list_name " +
                    "from case_list a LEFT JOIN case_info b on a.case_id =b.id " +
                    "where a.isdelete=1 and b.isdelete=1 and a.notice_time<='" + sdf.format(new Date()) + "' " +
                    "and LENGTH(a.finish_time)=0 and cycle_seconds>0";
            logger.warn("--CASE.List--" + sql);
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    String accepter = one.getString("accepter");
                    String create_user = one.getString("create_user");
                    String case_id = one.getString("case_id");
                    HashMap<String, String> accs = RIUtil.StringToList(accepter);
                    accs.put(create_user, "");
                    String case_name = one.getString("case_name");
                    if (case_name.length() > 15) {
                        case_name = case_name.substring(0, 15);
                    }
                    String list_name = one.getString("list_name");
                    if (list_name.length() > 12) {
                        list_name = list_name.substring(0, 12);
                    }

                    String logs = "清单:" + list_name + " 提醒";
                   wechatMsgTemp.createMessage_case(mysql, case_name, logs, "1", accs, case_id);

                    String notice_time = one.getString("notice_time");
                    int cycle_seconds = one.getInteger("cycle_seconds");
                    long nt = RIUtil.dateToStamp(notice_time);
                    nt = nt + cycle_seconds;
                    notice_time = RIUtil.stampToTime(nt);

                    sql = "update case_list set notice_time='" + notice_time + "' where id='" + id + "'";
                    logger.warn(sql);
                    mysql.update(sql);

                }
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private void ZaiZhenNotice() {

        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select a.id,DECODE(b.case_name,'" + RIUtil.enTitle + "') as case_name,DECODE(a.obj_name,'" + RIUtil.enName + "')as obj_name," + "a.near_date,a.end_date,b.accepter,b.create_user " + "from case_object a left JOIN case_info b on a.case_id=b.id " + "where current_index!=99 and near_date<='" + sdf.format(new Date()) + "' and a.isdelete=1 and b.isdelete=1 and b.type=1";
            logger.warn("--CASE.ZZ--" + sql);
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    String accepter = one.getString("accepter");
                    String create_user = one.getString("create_user");
                    HashMap<String, String> accs = RIUtil.StringToList(accepter);
                    accs.put(create_user, "");
                    String case_name = one.getString("case_name");
                    if (case_name.length() > 15) {
                        case_name = case_name.substring(0, 15);
                    }

                    String obj_name = one.getString("obj_name");

                    String logs = obj_name + ":在侦 临期提醒";
                    wechatMsgTemp.createMessage_case(mysql, case_name, logs, "1", accs, id);

                    String near_time = one.getString("near_date");
                    near_time = RIUtil.GetNextDate(near_time, 1) + " 09:00:00";
                    sql = "update case_object set near_date='" + near_time + "' where id='" + id + "'";
                    mysql.update(sql);

                }
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private void StepNotice() {

        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select case_id,obj_id,index_no,a.id,DECODE(b.case_name,'" + RIUtil.enTitle + "') as case_name,b.accepter,b.create_user " + "from case_step a LEFT JOIN case_info b on a.case_id=b.id " + "where a.isnotice=1 and notice_date<='" + sdf.format(new Date()) + "' and a.isdelete=1 and b.isdelete=1";
            logger.warn("--CASE.Notice--" + sql);
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");

                    String accepter = one.getString("accepter");
                    String create_user = one.getString("create_user");
                    HashMap<String, String> accs = RIUtil.StringToList(accepter);
                    accs.put(create_user, "");
                    String case_name = one.getString("case_name");
                    if (case_name.length() > 15) {
                        case_name = case_name.substring(0, 15);
                    }
                    String obj_id = one.getString("obj_id");
                    sql = "select decode(obj_name,'" + RIUtil.enName + "') as obj_name from case_object where id='" + obj_id + "'";
                    String obj_name = mysql.query_one(sql, "obj_name");
                    sql = "select step_name from case_step_model where index_no='" + one.getString("index_no") + "'";
                    String step_name = mysql.query_one(sql, "step_name");
                    String logs = obj_name + ":" + step_name + " 提醒";
                    wechatMsgTemp.createMessage_case(mysql, case_name, logs, "1", accs, id);


                    sql = "update case_step set isnotice='0' where id='" + id + "'";
                    mysql.update(sql);

                }
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private void StepCycleNotice() {

        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();

            String sql = "select case_id,obj_id,index_no,a.id,DECODE(b.case_name,'" + RIUtil.enTitle + "') as case_name,b.accepter,b.create_user," + "a.cycle,a.cycle_notice " + "from case_step a LEFT JOIN case_info b on a.case_id=b.id " + "where a.cycle>0 and a.cycle_notice<='" + sdf.format(new Date()) + "' and a.isdelete=1 and b.isdelete=1";
            logger.warn("--CASE.cycle--" + sql);
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    int cycle = one.getInteger("cycle");
                    String cycle_notice = one.getString("cycle_notice");
                    String accepter = one.getString("accepter");
                    String create_user = one.getString("create_user");
                    HashMap<String, String> accs = RIUtil.StringToList(accepter);
                    accs.put(create_user, "");
                    String case_name = one.getString("case_name");
                    if (case_name.length() > 15) {
                        case_name = case_name.substring(0, 15);
                    }
                    String obj_id = one.getString("obj_id");
                    sql = "select decode(obj_name,'" + RIUtil.enName + "') as obj_name from case_object where id='" + obj_id + "'";
                    String obj_name = mysql.query_one(sql, "obj_name");
                    sql = "select step_name from case_step_model where index_no='" + one.getString("index_no") + "'";
                    String step_name = mysql.query_one(sql, "step_name");
                    String logs = obj_name + ":" + step_name + " 提醒";
                    wechatMsgTemp.createMessage_case(mysql, case_name, logs, "1", accs, id);

                    cycle_notice = RIUtil.GetNextDate(cycle_notice, cycle) + " 09:00:00";

                    sql = "update case_step set cycle_notice='" + cycle_notice + "' where id='" + id + "'";
                    mysql.update(sql);

                }
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private void DaiZhenNotice() {
        logger.warn("--CASE.DZ--");
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select id,cycle,cycle_notice,decode(case_name,'" + RIUtil.enTitle + "') as case_name,create_user,accepter " + "from case_info where cycle>0 and cycle_notice<='" + sdf.format(new Date()) + "' and isdelete=1";
            logger.warn("--CASE.DZ--" + sql);
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    int cycle = one.getInteger("cycle");
                    String cycle_notice = one.getString("cycle_notice");
                    String accepter = one.getString("accepter");
                    String create_user = one.getString("create_user");
                    HashMap<String, String> accs = RIUtil.StringToList(accepter);
                    accs.put(create_user, "");
                    String case_name = one.getString("case_name");
                    if (case_name.length() > 15) {
                        case_name = case_name.substring(0, 15);
                    }
                    String logs = case_name + " 待侦 提醒";
                    wechatMsgTemp. createMessage_case(mysql, case_name, logs, "1", accs, id);

                    cycle_notice = RIUtil.GetNextDate(cycle_notice, cycle) + " 09:00:00";

                    sql = "update case_info set cycle_notice='" + cycle_notice + "' where id='" + id + "'";
                    mysql.update(sql);

                }
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }




}
