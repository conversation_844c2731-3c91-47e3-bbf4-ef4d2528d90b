package HL.TNOA.Tools;

import HL.TNOA.Lib.BashExecutor;
import HL.TNOA.Lib.ErrNo;

import java.io.BufferedReader;
import java.io.InputStreamReader;

public class CheckErrno {
    public static void main(String[] args){
        try {
            BashExecutor bash = new BashExecutor();
            BufferedReader reader = new BufferedReader(new InputStreamReader(
                    ErrNo.class.getClassLoader().getResourceAsStream("errno_ch.properties")));
            while (true) {
                String line = reader.readLine();
                if (line == null) {
                    break;
                }

                if (line.trim().length() == 0) {
                    continue;
                }

                if(line.startsWith("#")){
                    continue;
                }

                //System.out.println(line);

                String cmd = "grep " + line.substring(0, 6) + " ../Httpd/ -rnw|grep ErrNo.set";
                String logs = bash.exec(cmd, 20, false);
                if(logs.trim().length() == 0){
                    System.out.println(line.substring(0, 6));
                }
                //System.out.println(cmd + " --> " + logs);
            }

            reader.close();
        }
        catch (Exception e){
            e.printStackTrace();
        }
    }
}
