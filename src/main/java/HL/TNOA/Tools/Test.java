package HL.TNOA.Tools;

import HL.TNOA.Lib.SM2.KeyUtils;
import HL.TNOA.Lib.SM2.Sm2Utils;

import java.util.Base64;

public class Test {

    private static String testStr = "fanny";

    static java.security.PublicKey publicKey = null;
    static java.security.PrivateKey privateKey = null;

    public static void main(String[] args) throws Exception {
        //生成公私钥对
        String[] keys = KeyUtils.generateSmKey();

        System.out.println("原始字符串：" + testStr);
        System.out.println("公钥：" + keys[0]);
        publicKey = KeyUtils.createPublicKey(keys[0]);

        System.out.println("私钥：" + keys[1]);
        privateKey = KeyUtils.createPrivateKey(keys[1]);

        System.out.println("");


        byte[] encrypt = Sm2Utils.encrypt(testStr.getBytes(), publicKey);
        String encryptBase64Str = Base64.getEncoder().encodeToString(encrypt);
        System.out.println("加密数据：" + encryptBase64Str);

        byte[] decode = Base64.getDecoder().decode(encryptBase64Str);
        byte[] decrypt = Sm2Utils.decrypt(decode, privateKey);
        System.out.println("解密数据：" + new String(decrypt));

        byte[] sign = Sm2Utils.signByPrivateKey(testStr.getBytes(), privateKey);
        System.out.println("数据签名：" + Base64.getEncoder().encodeToString(sign));

        boolean b = Sm2Utils.verifyByPublicKey(testStr.getBytes(), publicKey, sign);
        System.out.println("数据验签：" + b);


    }


}
