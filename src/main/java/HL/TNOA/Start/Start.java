package HL.TNOA.Start;

import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.PoolExecutor;
import HL.TNOA.Lib.TNOAConf;
import HL.TNOA.Start.EnvCheck.Env;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;

public class Start {
    private static Logger logger = LoggerFactory.getLogger(Start.class);

    public static void main(String[] args) {

        try {
            if (TNOAConf.get() == null) {
                logger.error("Config file is empty");
                logger.error("Config file is empty");
                logger.error("Config file is empty");
                logger.error("Config file is empty");
                logger.error("Config file is empty");
            }

            //检测依赖环境
            Env env = new Env();
            env.CheckEnv();

            HashMap<Runnable, Boolean> thread_pools = new HashMap<>();
            //进程监控
            thread_pools.put(new WifiDeamon(), true);
            //心跳管理
            thread_pools.put(new HeartBeat(), true);

            //基础信息获取

            PoolExecutor executor = new PoolExecutor(thread_pools);
            executor.start();

            logger.warn("TNOA start success. Version " + Lib.getVersion());
        } catch (Exception e) {

            logger.error(Lib.getTrace(e));
            System.exit(-1);
        }
    }
}
