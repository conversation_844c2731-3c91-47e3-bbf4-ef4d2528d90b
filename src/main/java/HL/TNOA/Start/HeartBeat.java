package HL.TNOA.Start;

import HL.TNOA.Lib.BashExecutor;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.SplitGrepAwk;
import HL.TNOA.Lib.TNOAConf;
import com.alibaba.fastjson.JSONObject;
import io.netty.bootstrap.Bootstrap;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.DelimiterBasedFrameDecoder;
import io.netty.handler.codec.string.StringDecoder;
import io.netty.handler.codec.string.StringEncoder;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.handler.timeout.IdleStateHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.sql.Connection;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class HeartBeat extends ChannelInboundHandlerAdapter implements Runnable {
    private static Logger logger = LoggerFactory.getLogger(HeartBeat.class);
    ///心跳检测的相关代码
    public static volatile HashMap<Integer, Long> heartbeat = new HashMap<>();
    public static int process_heardbeat_port = 20001;
    public static HashMap<Connection, String> myConns = new HashMap<>();
    public static ByteBuf[] HlDelimiter = new ByteBuf[]{Unpooled.wrappedBuffer(new byte[]{'\r', '\n'}),
            Unpooled.wrappedBuffer(new byte[]{'\n'}),};

    @Override
    public void run() {
        if (TNOAConf.has("basic", "heardbeat.port")) {
            process_heardbeat_port = Integer.parseInt(TNOAConf.get("basic", "heardbeat.port"));
        }

        //启动检测心跳进程
        Thread thread_heatbeatcheck = new Thread(new HeartbeatCheck());
        thread_heatbeatcheck.setName("thread_heatbeatcheck");
        thread_heatbeatcheck.start();

        //启动心跳端口
        while (true) {
            try {
                EventLoopGroup bossGroup = new NioEventLoopGroup(1);
                EventLoopGroup workerGroup = new NioEventLoopGroup(1);
                try {
                    ServerBootstrap b = new ServerBootstrap();
                    b.group(bossGroup, workerGroup).channel(NioServerSocketChannel.class).childHandler(new ChannelInitializer<SocketChannel>() {
                                protected void initChannel(SocketChannel ch) throws Exception {
                                    ch.pipeline().addLast(new IdleStateHandler(400, 0, 0, TimeUnit.SECONDS));
                                    ch.pipeline().addLast(new DelimiterBasedFrameDecoder(200, HlDelimiter));
                                    ch.pipeline().addLast("decoder", new StringDecoder());
                                    ch.pipeline().addLast("encoder", new StringEncoder());
                                    ch.pipeline().addLast(new HeartBeat());

                                }
                            }).option(ChannelOption.SO_BACKLOG, 128)
                            //.option(ChannelOption.TCP_NODELAY, true)
                            //.option(ChannelOption.SO_KEEPALIVE, true)
                            .childOption(ChannelOption.SO_KEEPALIVE, true).childOption(ChannelOption.TCP_NODELAY, true);

                    logger.warn("heardbeat server " + process_heardbeat_port + " start success.");

                    // 绑定端口，开始接收进来的连接
                    ChannelFuture f = b.bind(process_heardbeat_port).sync();

                    // 等待服务器 socket 关闭 。
                    // 在这个例子中，这不会发生，但你可以优雅地关闭你的服务器。
                    f.channel().closeFuture().sync();
                } finally {
                    workerGroup.shutdownGracefully();
                    bossGroup.shutdownGracefully();
                    Lib.sleep(5);
                }
            } catch (Exception e) {
                logger.error(Lib.getTrace(e));
            }
        }
    }

    public class HeartbeatCheck implements Runnable {
        @Override
        public void run() {
            while (true) {
                try {
                    long nowtime = new Date().getTime();
                    boolean hasbreak = false;
                    for (int pid : heartbeat.keySet()) {
                        //30秒没有接收到心跳，关闭
                        //logger.warn(pid + " ---> " +  (nowtime - heartbeat.get(pid)));
                        if ((nowtime - heartbeat.get(pid)) > 300000) {
                            killproc(pid);
                            heartbeat.remove(pid);
                            hasbreak = true;
                            break;
                        }
                    }
                    // logger.warn(myConns.toString());
                    if (!hasbreak) {
                        Lib.sleep(3);
                    }


                } catch (Exception e) {
                    logger.error(Lib.getTrace(e));
                }
            }
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        logger.error(Lib.getTrace(cause));
        ctx.close();
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        //heartbeat.put(((String) msg).trim(), new Date().getTime());
        //logger.warn((String)msg);
        JSONObject json = JSONObject.parseObject((String) msg);
        if (!json.containsKey("heartbeat") || json.getString("heartbeat").trim().length() == 0) {
            return;
        }

        heartbeat.put(json.getIntValue("heartbeat"), new Date().getTime());
        //logger.warn(heartbeat.toString());
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            IdleState state = ((IdleStateEvent) evt).state();
            if (state == IdleState.READER_IDLE) {
                //定时发送心跳
                //Send(new JSONObject());
                logger.error("heartbeat timeout.exit....");
                ctx.close();
            }
        } else {
            super.userEventTriggered(ctx, evt);
        }
    }

    public static Channel channel = null;
    public static String processname = null;

    public class HeartbeatClient extends ChannelInboundHandlerAdapter implements Runnable {
        public HeartbeatClient(String _processname) {
            if (processname == null) {
                processname = _processname;
            }

            if (TNOAConf.has("basic", "heardbeat.port")) {
                process_heardbeat_port = Integer.parseInt(TNOAConf.get("basic", "heardbeat.port"));
            }
        }

        @Override
        public void run() {
            int retry = 0;
            while (true) {
                try {
                    EventLoopGroup bossgroup = new NioEventLoopGroup(1);
                    try {
                        Bootstrap b = new Bootstrap();
                        b.group(bossgroup).channel(NioSocketChannel.class).option(ChannelOption.TCP_NODELAY, true).option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 1000).handler(new ChannelInitializer<SocketChannel>() {
                            @Override
                            public void initChannel(SocketChannel ch) throws Exception {
                                ch.pipeline().addLast(new IdleStateHandler(0, 400, 0, TimeUnit.SECONDS));
                                ch.pipeline().addLast(new DelimiterBasedFrameDecoder(2000, HlDelimiter));
                                ch.pipeline().addLast("decoder", new StringDecoder());
                                ch.pipeline().addLast("encoder", new StringEncoder());
                                ch.pipeline().addLast(new HeartbeatClient(null));
                            }
                        });

                        ChannelFuture f = b.connect("127.0.0.1", process_heardbeat_port).sync();
                        logger.warn("heartbeat server success ==> " + processname);

                        retry = 0;
                        channel = f.channel();
                        //注册
                        String name = ManagementFactory.getRuntimeMXBean().getName();
                        String benpid = name.split("@")[0];
                        JSONObject regedit = new JSONObject();

                        regedit.put("opt", "heartbeat");
                        regedit.put("pid", benpid);

                        channel.writeAndFlush(regedit.toString() + "\r\n");

                        f.channel().closeFuture().sync();
                    } finally {
                        bossgroup.shutdownGracefully();
                    }
                } catch (Exception e) {
                    logger.error(Lib.getTrace(e));
                    retry++;
                }

                if (retry == 3) {
                    logger.warn("retry connect heartbeat status bus 3 timers, exit.");
                    System.exit(-1);
                }

                logger.warn("reconnect heartbeat status bus again.");
                Lib.sleep(300L);
            }
        }

        @Override
        public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
            if (evt instanceof IdleStateEvent) {
                IdleState state = ((IdleStateEvent) evt).state();
                if (state == IdleState.WRITER_IDLE) {
                    //定时发送心跳
                    logger.error(processname + " heartbeat timeout.exit....");
                    System.exit(-1);
                }
            } else {
                super.userEventTriggered(ctx, evt);
            }
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            logger.error(Lib.getTrace(cause));
            ctx.close();
        }

        @Override
        public void channelRead(ChannelHandlerContext ctx, Object msg) {
            logger.warn(msg.toString());
        }
    }

    public static void killproc(int pid) {
        logger.warn("stop idle process " + pid);
        BashExecutor bash = new BashExecutor();
        for (int kk = 0; kk < 100; kk++) {
            String cmd = "kill -9 " + pid;
            bash.exec(cmd, 20, false);

            cmd = "ps -ef";
            String ret = bash.exec(cmd, 20, false);
            SplitGrepAwk grepawk = new SplitGrepAwk(ret);
            grepawk.awk(1);
            List<String> list = grepawk.grep(pid + "", true, false);
            if (list.size() == 0) {
                return;
            }
        }
    }

    public void heartbeat_init(String processname) {
        new Thread(new HeartbeatClient(processname)).start();
        //wait tcp connect ok
        for (int i = 0; i < 300; i++) {
            if (channel != null) {
                break;
            }
            Lib.sleep(10L);
        }
        heartbeat();
    }

    //不会退出了
    public void heartbeating() {
        while (true) {
            Lib.sleep(10);
            heartbeat();
        }
    }

    public static void heartbeat() {
        RuntimeMXBean bean = ManagementFactory.getRuntimeMXBean();
        // Get start time
        long startTime = bean.getStartTime();
        long nowtime = new Date().getTime();

        Calendar currentTime = Calendar.getInstance();
        currentTime.setTime(new Date());
        int currentHour = currentTime.get(Calendar.HOUR_OF_DAY);

        //7天，重启一次。
        long reboot_day = 30;
        if (TNOAConf.has("basic", "proc.reboot.time")) {
            reboot_day = Long.parseLong(TNOAConf.get("basic", "proc.reboot.time"));
        }
        long proc_reboot_time = 3600000L * 24 * reboot_day;

        if ((nowtime - startTime) > proc_reboot_time && currentHour == 3) {
            logger.error(reboot_day + " days reboot.");
            System.exit(0);
        }

        String name = ManagementFactory.getRuntimeMXBean().getName();
        String benpid = name.split("@")[0];
        JSONObject json = new JSONObject();
        json.put("heartbeat", benpid);

        int retry = 5;
        do {
            if (channel != null) {
                channel.writeAndFlush(json.toString() + "\r\n");
                return;
            }

            logger.warn(processname + " ---> retry " + retry + ";json --> " + json.toString());
            StackTraceElement[] ss = new Throwable().getStackTrace();
            for (StackTraceElement _ss : ss) {
                logger.warn(_ss.getClassName() + ":" + _ss.getLineNumber());
            }

            retry--;
            if (retry == 0) {
                System.exit(0);
            }
            Lib.sleep(300L);
        } while (true);
    }
}
