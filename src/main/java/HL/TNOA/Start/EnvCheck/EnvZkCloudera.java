package HL.TNOA.Start.EnvCheck;//package HL.Wifi.Start.EnvCheck;
//
//import HL.Wifi.Lib.ExecuteShell;
//import org.apache.log4j.Logger;
//
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
//public class EnvZkCloudera {
//    private static Logger logger = Logger.getLogger(EnvZkCloudera.class);
//
//    public static int check_all(){
//        String zkhost = Env.getzk();
//        String[] zkhosts = zkhost.trim().split(",");
//        boolean zkcheck = false;
//        for(String _zkhost: zkhosts){
//            String[] _zkhosts = _zkhost.split(":");
//            if(_zkhosts.length != 2){
//                continue;
//            }
//            String zkip = _zkhosts[0];
//            String zkport = _zkhosts[1];
//
//            String cmd = "echo conf|nc " + zkip + " " + zkport + "|grep maxClientCnxns";
//            String execlog = ExecuteShell.exec(cmd, 10, false);
//            Pattern patt = Pattern.compile("maxClientCnxns=(\\d+)");
//            Matcher matcher = patt.matcher(execlog.trim());
//            if (matcher.find()) {
//                int maxClientCnxns = Integer.parseInt(matcher.group(1));
//                if(maxClientCnxns < 200){
//                    logger.error("zookeeper的最大连接数据过低，应该配置为500.");
//                    logger.error("zookeeper的最大连接数据过低，应该配置为500.");
//                    logger.error("zookeeper的最大连接数据过低，应该配置为500.");
//                    logger.error("zookeeper的最大连接数据过低，应该配置为500.");
//                    logger.error("zookeeper的最大连接数据过低，应该配置为500.");
//                    System.exit(1);
//                }
//                else{
//                    zkcheck = true;
//                    break;
//                }
//            }
//        }
//
//        if(!zkcheck){
//            logger.error("没有可用的zookeeper进程.");
//            logger.error("没有可用的zookeeper进程.");
//            logger.error("没有可用的zookeeper进程.");
//            logger.error("没有可用的zookeeper进程.");
//            logger.error("没有可用的zookeeper进程.");
//            logger.error("没有可用的zookeeper进程.");
//        }
//
//        return 0;
//    }
//}
