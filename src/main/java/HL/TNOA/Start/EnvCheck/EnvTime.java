package HL.TNOA.Start.EnvCheck;//package HL.SCI.Start.EnvCheck;
//
//import HL.SCI.Lib.ExecuteShell;
//import HL.SCI.Lib.GetTraceInfo;
//import HL.SCI.Start.EnvAll;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.text.SimpleDateFormat;
//import java.util.Date;
//
//public class EnvTime {
//    private static Logger logger = LoggerFactory.getLogger(EnvTime.class);
//
//    public static int check(){
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        try {
//            String cmd = "hostname";
//            String hostname = ExecuteShell.exec(cmd, 5, false).trim();
//            for (String _host : EnvAll.get_ssh().keySet()) {
//                if (_host.equals(hostname)) {
//                    continue;
//                }
//                cmd = "date \"+%F %T\"";
//
//                //如果命令的执行时间操作2秒，该结果不做参考。
//                long execstarttime = new Date().getTime();
//                String datetime = EnvAll.get_ssh(_host).exec(cmd, 5, false);
//                long hosttime = new Date().getTime();
//                if ((hosttime - execstarttime) < 2000L) {
//                    long agenttime = sdf.parse(datetime).getTime();
//                    if (Math.abs((agenttime - hosttime)) > 10000) {
//                        cmd = "date -s \"" + sdf.format(new Date()) + "\"";
//                        logger.error("master time is " + sdf.format(new Date()));
//                        logger.error("host " + _host + " time is " + datetime);
//                        logger.error("host " + _host + " time is error.change.");
//                        EnvAll.get_ssh(_host).exec(cmd, 5, false);
//                    }
//                }
//            }
//        }
//        catch (Exception e){
//            logger.warn(GetTraceInfo.get(e));
//        }
//        return 0;
//    }
//
//}
