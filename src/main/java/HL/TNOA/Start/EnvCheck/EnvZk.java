package HL.TNOA.Start.EnvCheck;//package HL.Wifi.Start.EnvCheck;
//
//import HL.Wifi.Lib.GetTraceInfo;
//import HL.Wifi.Lib.SSHHelper;
//import HL.Wifi.Lib.WifiConf;
//import HL.Wifi.Start.EnvAll;
//import org.apache.log4j.Logger;
//
//import java.io.BufferedReader;
//import java.io.File;
//import java.io.FileReader;
//import java.util.HashMap;
//
//public class EnvZk {
//    private Logger logger = Logger.getLogger(this.getClass().getName());
//
//    public int check(){
//        if(check_zk(sshs.get(WifiConf.get("solr.zk.addr")),
//                WifiConf.get("solr.zk.filepath"), WifiConf.get("solr.zk.port")) < 0){
//            return start_zk(sshs.get(WifiConf.get("solr.zk.addr")),
//                    WifiConf.get("solr.zk.filepath"), WifiConf.get("solr.zk.port"));
//        }
//
//        return 0;
//    }
//
//    public int check_zk(SSHHelper ssh, String path, String port){
//        try {
//            //查看进程是否存在
//            String cmd = "ps -ef|grep QuorumPeerMain|grep java|grep -v grep|grep \"" + path + "\" -w";
//            String sshlogs = ssh.exec(cmd, 5, false);
//            if (sshlogs.trim().length() == 0) {
//                logger.warn("zookeeper process " + path + " ;port:" + port + " is not exist.");
//                return -1;
//            }
//            //查看端口是否存在
//            cmd = "netstat -nltp|grep " + port + " -w";
//            sshlogs = ssh.exec(cmd, 5, false);
//            if (sshlogs.trim().length() == 0) {
//                logger.warn("zookeeper port " + path + " ;port:" + port + " is not exist.");
//                return -1;
//            }
//
//            return 0;
//        }
//        catch (Exception e){
//            logger.error(GetTraceInfo.get(e));
//            return -1;
//        }
//    }
//
//    public int start_zk(SSHHelper ssh, String path, String port){
//        try {
//            logger.error("start zookeeper " + path + " > " + port);
//
//            //查看data目录是否存在
//            String datapath = WifiConf.get("solr.zk.filepath") + "/data";
//            String logspath = WifiConf.get("solr.zk.filepath") + "/logs";
//            String conffile = WifiConf.get("solr.zk.filepath") + "/conf/zoo.cfg";
//
//            File file = new File(datapath);
//            if(!file.exists()){
//                file.mkdir();
//            }
//
//            file = new File(logspath);
//            if(!file.exists()){
//                file.mkdir();
//            }
//
//            //生成配置文件
//            String cmd = "echo \"clientPort=" + WifiConf.get("solr.zk.port") + "\" > " + conffile;
//            ssh.exec(cmd, 5, false);
//
//            cmd = "echo \"dataDir="+ datapath +"\" >> " + conffile;
//            ssh.exec(cmd, 5, false);
//
//            cmd = "echo \"dataLogDir="+ logspath +"\" >> " + conffile;
//            ssh.exec(cmd, 5, false);
//
//
//            File sampleFile = new File("config/zoo.cfg");
//            BufferedReader br = new BufferedReader(new FileReader(sampleFile));
//            try{
//                while(true) {
//                    String line = br.readLine();
//                    if (line == null) {
//                        break;
//                    }
//
//                    if (line.trim().length() == 0) {
//                        continue;
//                    }
//
//                    cmd = "echo \""+ line +"\" >> " + conffile;
//                    ssh.exec(cmd, 5, false);
//                }
//            }
//            finally {
//                br.close();
//            }
//
//            String sshlogs;
//            //查看进程是否存在，存在kill
//            for (int _i = 100; _i > 0; _i--) {
//                cmd = "ps -ef|grep QuorumPeerMain|grep java|grep -v grep|grep \"" + path + "\" -w";
//                sshlogs = ssh.exec(cmd, 5, false);
//                if(sshlogs.trim().length() > 0){
//                    for(String _log: sshlogs.trim().split("\n")){
//                        if(_log.length() == 0){
//                            continue;
//                        }
//                        cmd = "kill -9 " + _log;
//                        ssh.exec(cmd, 5, false);
//                    }
//                    WifiConf.sleep(1);
//                }
//                else{
//                    break;
//                }
//            }
//
//            //启动线程
//            String zkbin = path + "/bin/zkServer.sh";
//            cmd = "ls " + zkbin;
//            sshlogs = ssh.exec(cmd, 5, false);
//            if(sshlogs.indexOf(EnvAll.nonexist) >= 0){
//                logger.error("zookeeper in path " + path + " is not exist");
//                return -1;
//            }
//
//            cmd = zkbin + " start";
//            sshlogs = ssh.exec(cmd, 30, false);
//            boolean startok = false;
//            if(sshlogs.indexOf("STARTED") < 0){
//                logger.error("start zookeeper fail.path=" + path + ";port=" + port + " ==> " + sshlogs);
//                return -1;
//            }
//
//            return check_zk(ssh, path, port);
//        }
//        catch (Exception e){
//            logger.error(GetTraceInfo.get(e));
//            return -1;
//        }
//    }
//}
