package HL.TNOA.Start.EnvCheck;

import HL.TNOA.Lib.BashExecutor;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Start.Start;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.HashMap;
import java.util.regex.Pattern;

public class Env {
    private Logger logger = LoggerFactory.getLogger(Env.class);

    public void CheckEnv() {
        if (Lib.getOs() == Lib.Linux) {
            check_floder_file();

            //检测java
            check_java();

            //检测wifi进程
            check_process();

            //检测sysctl文件
            check_sysctl();
        }

        EnvRedis redis = new EnvRedis();
        redis.check();
    }

    public void check_java() {
        //获取java版本
//        String javaversion = System.getProperty("java.version");
//        if (!(javaversion.startsWith("1.8.") ||
//                javaversion.startsWith("12"))) {
//            logger.error("java is not 1.8, please update to 1.8");
//            System.exit(0);
//        }

        if (System.getenv("JAVA_HOME") == null ||
                System.getenv("JAVA_HOME").length() < 5) {
            logger.warn("No JAVA_HOME environment variables configured.");
            System.exit(0);
        }

        //获取java默认编码格式
        if (!System.getProperty("file.encoding").equals("UTF-8")) {
            logger.error("The default encoding format is not set as UTF-8");
            logger.error("添加环境变量：JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8");
            logger.error("可以通过export命令设置，或这修改配置文件/etc/environment");
            //System.exit(0);
        }
    }

    public void check_floder_file() {
        if (Lib.getOs() == Lib.Linux) {
            BashExecutor bash = new BashExecutor();
            String cmd = "mkdir -p /var/log/sci";
            bash.exec(cmd, 20, false);
            cmd = "chmod 777 bins/* -R";
            bash.exec(cmd, 20, false);
        }
    }

    public void check_process() {
        //该程序不能重复启动
        String cmd = Lib.getJpspath() +
                Lib.getRedirect() + Lib.getFind(Start.class.getName()) +
                Lib.getRedirect() + Lib.getAwk(1);
        logger.warn(cmd);
        BashExecutor bash = new BashExecutor();
        String pid = bash.exec(cmd, 20, false);
        if (pid != null && pid.length() >= 2 && pid.trim().split("\n").length >= 2) {
            String[] pids = pid.trim().split("\n");
            String logs = "processid is ";
            for (String _pid : pids) {
                logs += _pid + " ";
            }
            logger.warn(logs);
            logger.error("sci deamon is started.");
            System.exit(0);
        }

        //停止wifi有关程序
        for (int i = 0; i < 10; i++) {
            cmd = Lib.getJpspath() +
                    Lib.getRedirect() + Lib.getFind("HL.SCI") +
                    Lib.getRedirect() + Lib.getNoFind("sun.tools.jps.Jps") +
                    Lib.getRedirect() + Lib.getNoFind(Start.class.getName()) +
                    Lib.getRedirect() + Lib.getAwk(1);
            //logger.warn(cmd);
            String log = bash.exec(cmd, 10, false);
            if (log.length() < 2) {
                break;
            }
            logger.warn(cmd + " --> " + log);
            String[] logs = log.split("\n");
            for (String _logs : logs) {
                cmd = Lib.getKill(_logs);
                logger.error(cmd);
                bash.exec(cmd, 5, false);
            }
        }
    }

//    public Set<String> get_all_serv(){
//        if (cl_all_serv == null) {
//            cl_all_serv = new HashSet<>();
//            //String hostname = ExecuteShell.exec("hostname",
//            //        5, false).trim();
//            //cl_all_serv.add(InetAddress.getByName("hostname").getHostAddress());
//            String[] clusterall = WifiConf.get("clusterall").split(",");
//            for (String _clusterall : clusterall) {
//                String ip = EnvAll.getIpByName(_clusterall);
//                if(!EnvAll.getLocalIps().contains(ip)){
//                    cl_all_serv.add(ip);
//                }
//                else{
//                    cl_all_serv.add("127.0.0.1");
//                }
//            }
//        }
//        return cl_all_serv;
//    }
//
//    private void check_agent_serv(String host, String localhost, String hostname){
//        if(host.equals(localhost)){
//            cl_agent_serv.add(hostname);
//            return;
//        }
//
//        cl_agent_serv.add(host);
//    }
//
//    public Set<String> get_agent_serv() throws Exception{
//        if(cl_agent_serv == null){
//            String localhost = "localhost";
//            String hostname = ExecuteShell.exec("hostname",
//                    5, false).trim();
//            cl_agent_serv = new HashSet<>();
//
//            MysqlHelper mysql = new MysqlHelper();
//            try {
//                String sql = "select NAME from scm.HOSTS";
//                //logger.warn(MysqlExec.query(sql));
//                List<HashMap<String, String>> data = mysql.query(sql);
//                for(HashMap<String, String> one: data){
//                    check_agent_serv(one.get("NAME"), localhost, hostname);
//                }
//            }
//            catch (Exception e){
//                logger.error(GetTraceInfo.get(e));
//            }
//            finally {
//                mysql.close();
//            }
//
//            //logger.warn("cl_agent_serv ====> "+ cl_agent_serv);
//        }
//
//        return cl_agent_serv;
//    }

    public void check_sysctl() {
        HashMap<String, String> sysctl_list = new HashMap<String, String>() {{
            put("vm.swappiness", "0");
            put("vm.max_map_count", "262144");
            put("net.ipv4.tcp_syncookies", "1");
            put("net.ipv4.tcp_tw_reuse", "1");
            put("net.ipv4.tcp_fin_timeout", "10");
            put("net.ipv4.tcp_keepalive_time", "1200");
            put("net.ipv4.tcp_max_syn_backlog", "8192");
            put("net.ipv4.tcp_max_tw_buckets", "5000");
            put("net.ipv4.tcp_tw_recycle", "1");
        }};

        BufferedReader br = null;
        try {
            br = new BufferedReader(new FileReader("/etc/sysctl.conf"));
            while (true) {
                String line = br.readLine();
                if (line == null) {
                    break;
                }

                line = line.trim();
                if (line.length() == 0 || line.charAt(0) == '#') {
                    continue;
                }

                for (String _key : sysctl_list.keySet()) {
                    String pattern = "^" + _key + "\\s*=\\s*" + sysctl_list.get(_key);
                    boolean isMatch = Pattern.matches(pattern, line);
                    if (isMatch) {
                        sysctl_list.remove(_key);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            Lib.QuietClose(br);
        }

        if (sysctl_list.size() == 0) {
            return;
        }

        BashExecutor bash = new BashExecutor();
        logger.warn("will set sysctl --> " + sysctl_list);
        for (String _key : sysctl_list.keySet()) {
            //删除现有的
            String cmd = "sed -i '/" + _key + "/d' /etc/sysctl.conf";
            bash.exec(cmd, 20, false);

            cmd = "sed -i '$a\\" + _key + " = " + sysctl_list.get(_key) + "' /etc/sysctl.conf";
            bash.exec(cmd, 20, false);
        }

        String cmd = "sysctl -p";
        bash.exec(cmd, 20, false);
    }

//    public void check_mysql_items(){
//        try {
//            MysqlHelper mysql = new MysqlHelper();
//            try {
//                //table all_count
//                String sql = "select * from all_count";
//                List<HashMap<String, String>> data = mysql.query(sql);
//                HashSet<String> containkeys = new HashSet<String>(){
//                    {
//                        add("count");
//                        add("ap_count");
//                        add("mt_count");
//                        add("4g_count");
//                        add("etc_count");
//                        add("bt_count");
//                        add("filter1");
//                        add("filter2");
//                        add("filter3");
//                        add("filter4");
//                    }
//                };
//                for (HashMap<String, String> one : data) {
//                    containkeys.remove(one.get("id"));
//                }
//
//                for(String _keys: containkeys){
//                    sql = "INSERT INTO all_count(id, all_count) VALUES ('"+_keys+"', 0)";
//                    logger.warn(sql);
//                    mysql.update(sql);
//                }
//
//                //table suspect_type
//                sql = "select * from suspect_type";
//                data = mysql.query(sql);
//                String[] values = {"枪", "赌", "毒", "爆", "火", "盗", "抢",
//                        "黑", "访", "稳", "逃", "骗", "恐", "前"};
//                if (data.size() == 0) {
//                    for (int i = 0; i < values.length; i++) {
//                        sql = "INSERT INTO suspect_type (id, type) VALUES (" + (i + 1) + ", '" + values[i] + "')";
//                        logger.warn(sql);
//                        mysql.update(sql);
//                    }
//                }
//
//                //table statistical_item
//                sql = "select * from statistical_item";
//                data = mysql.query(sql);
//
//                HashMap<String, String> items_need = new HashMap<>();
//                //4G
//                items_need.put("4g_count", "去重的数据总量");
//                items_need.put("4g_count_all", "不去重的数据总量");
//                items_need.put("4g_device", "需要统计的设备");
//                items_need.put("4g_process", "1->正在统计，0->统计完成");
//                items_need.put("4g_result", "统计结果，如果统计出现异常，结果存在这里");
//                items_need.put("4g_type", "");
//                items_need.put("4g_start", "web显示统计开始时间");
//                items_need.put("4g_end", "web显示统计结束时间");
//
//                //basic
//                items_need.put("basic_ad", "");
//                items_need.put("basic_ap", "");
//                items_need.put("basic_imei", "");
//                items_need.put("basic_imsi", "");
//                items_need.put("basic_mt", "");
//                items_need.put("basic_nb", "");
//                items_need.put("basic_qq", "");
//                items_need.put("basic_taobao", "");
//                items_need.put("basic_wechat", "");
//                items_need.put("basic_weibo", "");
//                items_need.put("basic_time", "");
//                items_need.put("basic_process", "");
//                items_need.put("basic_result", "");
//
//                //导出的相关操作
//                items_need.put("db_exp_list", "{\"list\":[]}或" +
//                        "{\"\":\"starttime\":\"\",\"endtime\":\"\"}");
//                items_need.put("db_exp_process", "1->导出进行中，0->停止");
//                items_need.put("db_exp_params",
//                        "{\"addr\":\"\", \"port\":\"\",\"username\":\"\", \"password\":\"\"}" +
//                                "或者{\"path\":\"\"}");
//                items_need.put("db_exp_result", "{\"error\":\"\"} 或 {\"success\"：[]}");
//                items_need.put("db_exp_type", "导出模式，0->列表模式，1->时间, 2->ftp列表，3->ftp时间");
//
//                //高级统计1
//                items_need.put("s1_count", "去重的数据总量");
//                items_need.put("s1_count_all", "不去重的数据总量");
//                items_need.put("s1_device", "需要统计的设备");
//                items_need.put("s1_process", "1->正在统计，0->统计完成");
//                items_need.put("s1_result", "统计结果，如果统计出现异常，结果存在这里");
//                items_need.put("s1_start", "web显示统计开始时间");
//                items_need.put("s1_end", "web显示统计结束时间");
//                items_need.put("s1_type", "");
//                items_need.put("s1_ssid", "");
//
//                //高级统计2
//                items_need.put("s2_count", "去重的数据总量");
//                items_need.put("s2_count_all", "不去重的数据总量");
//                items_need.put("s2_device", "需要统计的设备");
//                items_need.put("s2_process", "1->正在统计，0->统计完成");
//                items_need.put("s2_result", "统计结果，如果统计出现异常，结果存在这里");
//                items_need.put("s2_start", "web显示统计开始时间");
//                items_need.put("s2_end", "web显示统计结束时间");
//                items_need.put("s2_type", "");
//                items_need.put("s2_ssid", "");
//
//                //高级统计3
//                items_need.put("s3_count", "去重的数据总量");
//                items_need.put("s3_count_all", "不去重的数据总量");
//                items_need.put("s3_device", "需要统计的设备");
//                items_need.put("s3_process", "1->正在统计，0->统计完成");
//                items_need.put("s3_result", "统计结果，如果统计出现异常，结果存在这里");
//                items_need.put("s3_start", "web显示统计开始时间");
//                items_need.put("s3_end", "web显示统计结束时间");
//                items_need.put("s3_type", "");
//                items_need.put("s3_ssid", "");
//
//                Set<String> nameset = new HashSet<>();
//                for (HashMap<String, String> one : data) {
//                    nameset.add(one.get("name"));
//                }
//
//                for (String _names : items_need.keySet()) {
//                    if (!nameset.contains(_names)) {
//                        sql = "INSERT INTO statistical_item (name, value, notes) VALUES " +
//                                "('" + _names + "', '0', '" + items_need.get(_names) + "')";
//                        logger.warn(sql);
//                        mysql.update(sql);
//                    }
//                }
//
//                //user表
//                sql = "select * from user";
//                data = mysql.query(sql);
//                if (data.size() == 0) {
//                    sql = "INSERT INTO user(id, username, auth_key, password_hash, " +
//                            "password_reset_token, email, status, created_at, updated_at, " +
//                            "account) VALUES (1, 'admin', 'SANLDdhTgOZojbAJuBJHf6toAGvg5-0a', " +
//                            "'$2y$13$xA368wiI0j126ErG2932O.UHxUZNc1Dmfze3lnQR9NDZF0rGPc7wm', " +
//                            "'5kITAS5fSQug8NVOr1h8x2CUSRRCMgSXT70hr+vvm0A= nxRPZG7vbyP0tYYMl6RppPvEyGWRo1tf" +
//                            "+PhDNVc7dxs=', '', 10, " + (new Date().getTime() / 1000) +
//                            ", " + (new Date().getTime() / 1000) + ", 0)";
//                    logger.warn(sql);
//                    mysql.update(sql);
//                }
//
////                //rt_speed
////                sql = "select * from rt_speed where device_id='HLUPDATETIMEHL'";
////                data = mysql.query(sql);
////                if (data.size() == 0) {
////                    sql = "INSERT INTO rt_speed(`device_id`, `time`) " +
////                            "VALUES ('HLUPDATETIMEHL', '0')";
////                    logger.warn(sql);
////                    mysql.update(sql);
////                }
//
//                //表：group_renting
//                HashMap<String, String>value_map = new HashMap<>();
//                HashMap<String, String>desc_map = new HashMap<>();
//                value_map.put("onoff", "0");
//                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//                value_map.put("collect_endtime", sdf.format(new Date()));
//                long time = new Date().getTime()-90L*24*3600000L;
//                value_map.put("collect_starttime", sdf.format(new Date(time)));
//                value_map.put("calc_starttime", "6");
//                value_map.put("calc_endtime", "10");
//                value_map.put("ap_maxcount", "30");
//                value_map.put("ap_mincount", "10");
//                value_map.put("ex_ssid_list", "");
//                value_map.put("data_size", "0,0");
//                value_map.put("result", "");
//                value_map.put("reismove", "0");
//
//                desc_map.put("onoff", "启停，1计算中，0初始状态,2计算完成");
//                desc_map.put("collect_starttime", "计算开始时间");
//                desc_map.put("collect_endtime", "计算结束时间");
//                desc_map.put("calc_starttime", "每日计算开始时间");
//                desc_map.put("calc_endtime", "每日计算结束时间");
//                desc_map.put("ap_maxcount", "热点下最大连接数");
//                desc_map.put("ap_mincount", "热点下最小连接数");
//                desc_map.put("ex_ssid_list", "排查的热点");
//                desc_map.put("data_size", "数据量");
//                desc_map.put("result", "结果");
//                desc_map.put("reismove", "移动热点重新计算");
//
//                for(String _key: value_map.keySet()){
//                    sql = "select * from group_renting where name='"+ _key +"'";
//                    List<HashMap<String, String>> list = mysql.query(sql);
//                    if(list.size() == 0){
//                        sql = "INSERT INTO group_renting(`name`, `value`, `describe`) " +
//                                "VALUES ('"+ _key +"','" + value_map.get(_key) + "','"+
//                                desc_map.get(_key)+"')";
//                        logger.warn(sql);
//                        mysql.update(sql);
//                    }
//                }
//
//                //表：models_fixed_person
//                value_map = new HashMap<>();
//                desc_map = new HashMap<>();
//                value_map.put("onoff", "0");
//                sdf = new SimpleDateFormat("yyyy-MM-dd");
//                value_map.put("collect_endtime", sdf.format(new Date()));
//                time = new Date().getTime()-90L*24*3600000L;
//                value_map.put("collect_starttime", sdf.format(new Date(time)));
//                value_map.put("calc_starttime", "20");
//                value_map.put("calc_endtime", "8");
//                value_map.put("ex_ssid_list", "");
//                value_map.put("in_ssid_list", "");
//                value_map.put("data_size", "0,0");
//                value_map.put("result", "");
//                value_map.put("isap", "0");
//                value_map.put("min_appear", "-1");
//                value_map.put("max_appear", "-1");
//                value_map.put("device_list", "");
//                value_map.put("pseudo", "1");
//
//                desc_map.put("onoff", "启停，1计算中，0初始状态,2计算完成");
//                desc_map.put("collect_starttime", "计算开始时间");
//                desc_map.put("collect_endtime", "计算结束时间");
//                desc_map.put("calc_starttime", "每日计算开始时间");
//                desc_map.put("calc_endtime", "每日计算结束时间");
//                desc_map.put("ex_ssid_list", "如果是AP，排除的AP热点列表");
//                desc_map.put("in_ssid_list", "如果是AP，包含的AP热点列表");
//                desc_map.put("data_size", "数据量");
//                desc_map.put("result", "结果");
//                desc_map.put("isap", "是否计算AP，还是MT");
//                desc_map.put("min_appear", "最少出现的次数，默认最最少的2/3，最少2次");
//                desc_map.put("max_appear", "最多出现的次数");
//                desc_map.put("device_list", "将要计算的设备列表");
//                desc_map.put("pseudo", "是否过滤虚假mac");
//
//                for(String _key: value_map.keySet()){
//                    sql = "select * from models_fixed_person where name='"+ _key +"'";
//                    List<HashMap<String, String>> list = mysql.query(sql);
//                    if(list.size() == 0){
//                        sql = "INSERT INTO models_fixed_person(`name`, `value`, `describe`) " +
//                                "VALUES ('"+ _key +"','" + value_map.get(_key) + "','"+
//                                desc_map.get(_key)+"')";
//                        logger.warn(sql);
//                        mysql.update(sql);
//                    }
//                }
//
//                //表：models_relation_collide
//                value_map = new HashMap<>();
//                desc_map = new HashMap<>();
//                value_map.put("devs_distinct", "3");
//                desc_map.put("devs_distinct", "待计算数据，一天至少要经过的设备个数");
//
//                value_map.put("devs_distrance", "1500");
//                desc_map.put("devs_distrance", "待计算数据，一天经过的2个最远设备，最小的距离");
//
//                value_map.put("oneday_maxdatacount", "1000");
//                desc_map.put("oneday_maxdatacount", "一天最大数据量，如果超过丢弃该天数据");
//
//                value_map.put("time_distince", "60");
//                desc_map.put("time_distince", "计算关联时，前后时间间隔，单位秒");
//
//                value_map.put("cacl_days", "14");
//                desc_map.put("cacl_days", "计算关联是，从数据库获取数据的天数");
//
//                value_map.put("output_mindevcount", "2");
//                desc_map.put("output_mindevcount", "关联出来的数据，最少经过的设备个数");
//
//                value_map.put("onedev_minappear_count", "4");
//                desc_map.put("onedev_minappear_count", "一起出现的天数，认定为有关联");
//
//                value_map.put("section_min_time_interval", "90");
//                desc_map.put("section_min_time_interval",
//                        "活动区域最短时间间隔,前后超过这个时间，认定不同的活动区域，单位分钟");
//
//                value_map.put("save_max_time", "365");
//                desc_map.put("save_max_time", "计算出来的数据，存储时间天数");
//
//                value_map.put("max_interrupt_time", "2");
//                desc_map.put("max_interrupt_time", "合并存储的数据时，中断多长时间认为是连续的，单位天");
//
//                value_map.put("section_20m_count", "1");
//                desc_map.put("section_20m_count",
//                        "单个活动区域，一起出现20分钟及以上，这样的活动区域多少次，认为同行，默认1次");
//
//                value_map.put("section_15m_count", "2");
//                desc_map.put("section_15m_count",
//                        "单个活动区域，一起出现15分钟及以上，这样的活动区域多少次，认为同行，默认2次");
//
//                value_map.put("section_10m_count", "3");
//                desc_map.put("section_10m_count",
//                        "单个活动区域，一起出现10分钟及以上，这样的活动区域多少次，认为同行，默认3次");
//
//                value_map.put("section_0m_count", "4");
//                desc_map.put("section_0m_count",
//                        "单个活动区域，一起出现，这样的活动区域多少次，认为同行，默认4次");
//
//                value_map.put("input_data_type", "ap,etc,imsi,bt");
//                desc_map.put("input_data_type", "输入数据的类型，支持的类型ap,etc,imsi,bt,car");
//
//                value_map.put("output_data_type", "ap,etc,imsi,bt");
//                desc_map.put("output_data_type", "输出数据的类型，支持的类型ap,etc,imsi,bt,car");
//
//                boolean haserror = false;
//                for(String _key: value_map.keySet()){
//                    sql = "select * from models_relation_collide where name='"+ _key +"'";
//                    List<HashMap<String, String>> list = mysql.query(sql);
//                    if(list.size() == 0){
//                        haserror = true;
//                        sql = "INSERT INTO models_relation_collide(`name`, `value`, `describe`) " +
//                                "VALUES ('"+ _key +"','" + value_map.get(_key) + "','"+
//                                desc_map.get(_key)+"')";
//                        logger.warn(sql);
//                        mysql.update(sql);
//                    }
//                }
//                if(haserror) {
//                    sql = "truncate models_relation_collide";
//                    mysql.update(sql);
//                    for (String _key : value_map.keySet()) {
//                        sql = "INSERT INTO models_relation_collide(`name`, `value`, `describe`) " +
//                                "VALUES ('" + _key + "','" + value_map.get(_key) + "','" +
//                                desc_map.get(_key) + "')";
//                        logger.warn(sql);
//                        mysql.update(sql);
//                    }
//                }
//
//                //表：area_code
//                try {
//                    BufferedReader br = new BufferedReader(
//                            new FileReader("config/mysql/area_code.txt"));
//                    try {
//                        for(int i=0;i<1;i++) {
//                            String countstr = br.readLine();
//                            if (countstr == null || countstr.trim().length() == 0) {
//                                break;
//                            }
//                            int count = Integer.parseInt(countstr);
//                            sql = "select count(*) from area_code";
//                            List<HashMap<String, String>> list = mysql.query(sql);
//                            if (list == null || list.size() == 0){
//                                break;
//                            }
//
//                            int mysqlcount = Integer.parseInt(list.get(0).get("count(*)"));
//                            if(count == mysqlcount){
//                                break;
//                            }
//
//                            sql = "truncate area_code";
//                            mysql.update(sql);
//
//                            while(true){
//                                String line = br.readLine();
//                                if(line == null){
//                                    break;
//                                }
//
//                                if(line.trim().length() < 3){
//                                    continue;
//                                }
//
//                                logger.warn(line);
//                                mysql.update(line);
//                            }
//                        }
//                    } finally {
//                        br.close();
//                    }
//                }
//                catch (Exception e){
//                    logger.error(GetTraceInfo.get(e));
//                }
//            }
//            finally {
//                mysql.close();
//            }
//        }
//        catch (Exception e){
//            logger.error(GetTraceInfo.get(e));
//        }
//    }

//    public void check_direction(){
//        String cmd = "rm -rf ./tmp/*";
//        ExecuteShell.exec(cmd, 5, true);
//        String sql = "truncate export_item";
//        MysqlHelper mysql = MysqlPool.newOneConnect();
//        try{
//            mysql.update(sql);
//        }
//        catch (Exception e){
//            logger.error(GetTraceInfo.get(e));
//        }
//        finally {
//            mysql.close();
//        }
//    }
//
//    public void clear_redis(){
//        try{
//            RedisHelper redis = new RedisHelper("Status");
//            try {
//                redis.flushAll();
//            }
//            finally {
//                redis.close();
//            }
//
//            redis = new RedisHelper("Statistics");
//            try {
//                redis.flushAll();
//            }
//            finally {
//                redis.close();
//            }
//        }
//        catch (Exception e){
//        }
//    }
}
