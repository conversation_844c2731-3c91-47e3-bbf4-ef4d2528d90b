package HL.TNOA.Start.EnvCheck;//package HL.SCI.Start.EnvCheck;
//
//import HL.SCI.Lib.GetTraceInfo;
//import HL.SCI.Lib.MysqlModel.MysqlHelper;
//import HL.SCI.Lib.MysqlModel.MysqlPool;
//import HL.SCI.Lib.SSHHelper;
//import HL.SCI.Lib.WifiConf;
//import HL.SCI.Start.EnvAll;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.lang.management.ManagementFactory;
//import java.util.HashMap;
//import java.util.HashSet;
//import java.util.List;
//
//public class EnvMysql{
//    private Logger logger = LoggerFactory.getLogger(this.getClass().getName());
//
//    public int CheckProcess() {
//        try {
//            SSHHelper ssh = EnvAll.get_ssh(WifiConf.get("mysql.host"));
//            if(ssh == null){
//                logger.error("get mysql host ssh handler fail.");
//                logger.error("get mysql host ssh handler fail.");
//                logger.error("get mysql host ssh handler fail.");
//                return -1;
//            }
//
//            for (int i = 0; i < 3; i++) {
//                String cmd = "ps -ef|grep mysqld|grep -v grep";
//                String execlog = ssh.exec(cmd, 5, false);
//                if (execlog.trim().length() >= 1) {
//                    return 0;
//                }
//
//                WifiConf.sleep(3);
//            }
//        }
//        catch (Exception e){
//            logger.error(GetTraceInfo.get(e));
//        }
//        return -1;
//    }
//
//    public int CheckPort(){
//        SSHHelper ssh = EnvAll.get_ssh(WifiConf.get("mysql.host"));
//        if(ssh == null){
//            logger.error("get mysql host ssh handler fail.");
//            logger.error("get mysql host ssh handler fail.");
//            logger.error("get mysql host ssh handler fail.");
//            return -1;
//        }
//
//        try{
//            for (int i = 0; i < 3; i++) {
//                String cmd = "netstat -nltp|grep "+ WifiConf.get("mysql.port") + " -w";
//                String execlog = ssh.exec(cmd, 5, false);
//                if (execlog.trim().length() >= 1) {
//                    return 0;
//                }
//
//                WifiConf.sleep(3);
//            }
//            return 0;
//        }
//        catch (Exception e){
//            logger.error(GetTraceInfo.get(e));
//            return -1;
//        }
//    }
//
//    public int CheckLogin(){
//        for(int _i=0;_i<3;_i++) {
//            try {
//                SSHHelper ssh = EnvAll.get_ssh(WifiConf.get("mysql.host"));
//                if(ssh == null){
//                    logger.error("get mysql host ssh handler fail.");
//                    logger.error("get mysql host ssh handler fail.");
//                    logger.error("get mysql host ssh handler fail.");
//                    WifiConf.sleep(2);
//                    //continue;
//                }
//
//                MysqlHelper mysql = MysqlPool.getModel();
//                try {
//                    List<HashMap<String, String>> processlist = mysql.query("show processlist");
//                    if (processlist.size() == 0) {
//                        WifiConf.sleep(1);
//                        continue;
//                    }
//
//                    long MaxConnections = 550;
//                    List<HashMap<String, String>> maxconns =
//                            mysql.query("show variables like 'max_connections'");
//                    if (maxconns.size() == 0) {
//                        WifiConf.sleep(1);
//                        continue;
//                    }
//                    if (maxconns.size() == 1) {
//                        HashMap<String, String> tmp = maxconns.get(0);
//                        if (tmp.containsKey("VARIABLE_VALUE")) {
//                            MaxConnections = Long.parseLong(tmp.get("VARIABLE_VALUE"));
//                        } else if (tmp.containsKey("Value")) {
//                            MaxConnections = Long.parseLong(tmp.get("Value"));
//                        } else {
//                            logger.error("get mysql VARIABLE_VALUE fail.");
//                            logger.error("get mysql VARIABLE_VALUE fail.");
//                            logger.error("get mysql VARIABLE_VALUE fail.");
//                            WifiConf.sleep(1);
//                            continue;
//                        }
//                    }
//
//                    int AlarmThreshold = Integer.parseInt(WifiConf.get("AlarmThreshold"));
//                    if (processlist.size() >= (MaxConnections * 100 / AlarmThreshold)) {
//                        //kill所有进程，等待所有进程重连。
//                        String name = ManagementFactory.getRuntimeMXBean().getName();
//                        String benpid = name.split("@")[0];
//                        String cmd;
//                        cmd = "netstat -ntp|grep ESTABLISHED|grep " +
//                                benpid + " -w|grep " +
//                                WifiConf.get("mysql.port") +
//                                " -w|awk '{print $4}'|grep -v " +
//                                WifiConf.get("mysql.port") + " -w|awk -F: '{print $2}'";
//
//                        String execlog = ssh.exec(cmd, 5, false);
//                        if (execlog.trim().length() >= 1) {
//                            HashSet<String> excllogs = new HashSet<>();
//                            for (String _execlogs : execlog.trim().split("\n")) {
//                                excllogs.add(_execlogs);
//                            }
//
//                            if (excllogs.size() > 0) {
//                                for (HashMap<String, String> _proclist : processlist) {
//                                    String hostname = _proclist.get("Host");
//                                    if (hostname.trim().length() == 0) {
//                                        continue;
//                                    }
//                                    if (_proclist.get("Id").trim().length() == 0) {
//                                        continue;
//                                    }
//
//                                    String[] hostnames = hostname.trim().split(":");
//                                    if (hostnames.length != 2) {
//                                        continue;
//                                    }
//
//                                    if (excllogs.contains(hostnames[1])) {
//                                        continue;
//                                    }
//
//                                    String killsql = "kill " + _proclist.get("Id");
//                                    mysql.update(killsql);
//                                }
//                            }
//                        }
//                    }
//                }
//                finally {
//                    MysqlPool.putModel(mysql);
//                }
//
//                return 0;
//            } catch (Exception e) {
//                logger.error(GetTraceInfo.get(e));
//            }
//
//            WifiConf.sleep(1);
//        }
//        return -1;
//    }
//
//    public int RestartMysql(){
//        try {
//            SSHHelper ssh = EnvAll.get_ssh(WifiConf.get("mysql.host"));
//            if(ssh == null){
//                logger.error("get mysql host ssh handler fail.");
//                logger.error("get mysql host ssh handler fail.");
//                logger.error("get mysql host ssh handler fail.");
//                return -1;
//            }
//
//            logger.error("reboot mysql");
//            String cmd = "/etc/init.d/mysql restart";
//            ssh.exec(cmd, 300, false);
//
//            boolean checkok = false;
//            for (int _i = 300; _i > 0; _i--) {
//                logger.warn("CheckProcess");
//                if (CheckProcess() >= 0) {
//                    checkok = true;
//                    break;
//                }
//            }
//
//            if (!checkok) {
//                logger.warn("start mysql fail.");
//                return -1;
//            }
//
//            checkok = false;
//            for (int _i = 300; _i > 0; _i--) {
//                logger.warn("CheckPort");
//                if (CheckPort() >= 0) {
//                    checkok = true;
//                    break;
//                }
//            }
//            if (!checkok) {
//                logger.warn("start mysql port fail.");
//                return -1;
//            }
//
//            checkok = false;
//            for (int _i = 300; _i > 0; _i--) {
//                logger.warn("CheckLogin");
//                if (CheckLogin() >= 0) {
//                    checkok = true;
//                    break;
//                }
//            }
//
//            if (!checkok) {
//                logger.warn("login mysql port fail.");
//                return -1;
//            }
//
//            return 0;
//        }
//        catch (Exception e){
//            logger.error(GetTraceInfo.get(e));
//            return -1;
//        }
//    }
//}
