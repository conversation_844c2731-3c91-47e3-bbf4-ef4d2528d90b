package HL.TNOA.Start.EnvCheck;

import HL.TNOA.Lib.BashExecutor;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.Redis.RedisPools;
import HL.TNOA.Lib.TNOAConf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

import java.io.File;

public class EnvRedis {
    private static Logger logger = LoggerFactory.getLogger(EnvRedis.class);

    public int check() {
        String redis_port = "20002";
        if (TNOAConf.has("redis", "port")) {
            redis_port = TNOAConf.get("redis", "port");
        }
        if (check_redis(redis_port) < 0) {
            String errlog = "(系统探测)统计redis异常";
            if (start_redis(redis_port) < 0) {
                return -1;
            }
        }
        return 0;
    }

    public static int check_redis(String port){
        BashExecutor bash = new BashExecutor();
        for(int _i=0;_i<3;_i++) {
            try {
                //进程
                if(Lib.Windows != Lib.getOs()) {
                    String cmd = "ps -ef|grep redis|grep -v grep|grep " + port;
                    String sshlogs = bash.exec(cmd, 5, false);
                    if (sshlogs.trim().length() == 0) {
                        logger.error("redis " + port + " process is not start," +
                                cmd + " ---> " + sshlogs);
                        Lib.sleep(200L);
                        continue;
                    }
                }

                //端口
                String cmd = "netstat -ntlp|grep redis|grep " + port;
                String sshlogs = bash.exec(cmd, 5, false);
                if (sshlogs.trim().length() == 0) {
                    logger.error("redis " + port + " port is not start,"
                            + cmd + " ---> " + sshlogs);
                    Lib.sleep(200L);
                    continue;
                }

                //连接性测试
                Jedis jedis = RedisPools.get();
                jedis.close();

                return 0;
            } catch (Exception e) {
                logger.error(Lib.getTrace(e));
            }
            Lib.sleep(1);
        }
        return -1;
    }

    public static int start_redis(String port){
        try {
            logger.error("start redis " + port);
            BashExecutor bash = new BashExecutor();
            //查看进程是否存在，存在kill
            String cmd;
            String sshlogs;
            if(Lib.Windows != Lib.getOs()) {
                cmd = "ps -ef|grep -v grep|grep redis|grep " + port + "|awk '{print $2}'";
                sshlogs = bash.exec(cmd, 10, false);
                if (sshlogs.length() > 0) {
                    for (String _kill : sshlogs.trim().split("\n")) {
                        if (_kill.trim().length() == 0) {
                            continue;
                        }
                        cmd = "kill -9 " + _kill;
                        bash.exec(cmd, 5, false);
                    }
                }
            }

            String redisconf = System.getProperty("user.dir") + "/bins/redis_"+port.trim()+".conf";
            String redisexe = System.getProperty("user.dir") +  "/bins/redis-server";
            if(Lib.Windows == Lib.getOs()){
                redisexe = System.getProperty("user.dir") +  "/bins/redis-server.exe";
            }

            if (!new File(redisconf).exists()) {
                logger.error("redis " + port + " config " + redisconf + " file is not exist");
                return -1;
            }

            if (!new File(redisexe).exists()) {
                logger.error("redis " + port + " exe " + redisexe + " file is not exist");
                return -1;
            }

            String cmdchmod = "chmod 777 " + System.getProperty("user.dir") + "/bins/*";
            bash.exec(cmdchmod, 10, false);

            //启动
            cmd = redisexe + " " + redisconf;
            //logger.warn(cmd);
            sshlogs = bash.exec(cmd, 10, false);
            //if (sshlogs.trim().length() > 0) {
                logger.error(cmd + " ==> " + sshlogs);
            //    return -1;
            //}

            return check_redis(port);
        }
        catch (Exception e){
            logger.error(Lib.getTrace(e));
            return -1;
        }
    }
}
