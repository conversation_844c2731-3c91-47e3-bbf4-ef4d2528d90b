package HL.TNOA.Start;

import HL.TNOA.Lib.BashExecutor;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.TNOAConf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

public class WifiDeamon implements Runnable {
	private static Logger logger = LoggerFactory.getLogger(WifiDeamon.class);

    public void run() {
        Map<String, Properties> conf = TNOAConf.get();
        Set<Thread> threadset = new HashSet<>();
        for(String _key:conf.keySet()) {
            if (!("server".equals(conf.get(_key).getProperty("type")) &&
                    "true".equals(conf.get(_key).getProperty("start")))) {
                continue;
            }

            try {
                Thread thread = new Thread(() -> {
                    Properties procProp = conf.get(_key);
                    String process = _key;
                    String cmd;
                    String ret;
                    while (true) {
                        try {
                            String wifips = Lib.getJpspath() +
                                    Lib.getRedirect() + Lib.getFind(conf.get(process).getProperty("class")) +
                                    Lib.getRedirect() + Lib.getAwk(1);
                            BashExecutor bash = new BashExecutor();

                            ret = bash.exec(wifips, 10, false);
                            String allurl = "./*:libs/libs/*:libs/hllibs/*";

                            String args = "-Xmx128m -Xms128m";
                            if (procProp.containsKey("args") &&
                                    procProp.getProperty("args").trim().length() > 0) {
                                args = procProp.getProperty("args");
                            }
                            //if(Lib.Windows != Lib.getOs()){
                            //args += " -XX:OnOutOfMemoryError=\"./shells/kill.sh ";
                            //}
                            if (ret == null || ret.trim().length() <= 1) {
                                cmd = "java " + args +
                                        " -cp " + allurl +
                                        " " + procProp.getProperty("class");

                                logger.warn("start " + process);
                                //logger.warn(cmd);
                                bash.exec(cmd, -1, true);
                            } else {
                                KillAllProc(ret);
                            }
                        } catch (Exception e) {
                            logger.error(Lib.getTrace(e));
                        } finally {
                            Lib.sleep(3);
                        }
                    }
                });
                thread.setName(_key);
                thread.start();
                threadset.add(thread);
            } catch (Exception e) {
                logger.error(Lib.getTrace(e));
            }
        }


        //等待所有进程退出
        while(true){
            for(Thread _thread: threadset){
                if(!_thread.isAlive()){
                    logger.error("has thread exit, wait reboot");
                    System.exit(-1);
                }
            }
            Lib.sleep(20);
        }
	}

	public static void KillAllProc(Object procs){
	    String[] proc;
	    if(procs instanceof String){
            proc = ((String)procs).trim().split("\n");
        }
        else if(procs instanceof String[]) {
            proc = (String[]) procs;
        }
        else{
            return;
        }

        if (proc.length >= 1) {
            String cmd = "kill -9 ";
            if(Lib.Windows == Lib.getOs()){
                cmd = "taskkill /F /PID ";
            }

            BashExecutor bash = new BashExecutor();

            for (int i = 0; i < proc.length; i++) {
                cmd += proc[i];
            }
            bash.exec(cmd, 10, false);
            logger.warn("cmd=" + cmd);

            Lib.sleep(100L);
        }
    }
}
