package HL.TNOA.wechat;

import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.TNOAConf;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.LoggerFactory;

import java.util.List;

public class ConnectWeChat {
    private static String appid = TNOAConf.get("wechat", "appid");
    private static String secret = TNOAConf.get("wechat", "secret");
    private static org.slf4j.Logger logger = LoggerFactory.getLogger(ConnectWeChat.class);


    public static String GetAccessToken2h(InfoModelHelper mysql) throws Exception {
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appid + "&secret="
                + secret;
        String we_back = HttpConnection.http_get(url);

        JSONObject back = JSONObject.parseObject(we_back);
        // logger.warn(back.toString());
        String access_token = "";
        String access = "|0";
        try {
            access_token = back.getString("access_token");
            String sql = "delete from wechat_access_token where id=1";
            mysql.update(sql);
            long time = System.currentTimeMillis();
            access = access_token + "|" + time;
            sql = "insert wechat_access_token(id,access_token,time)values(1,'" + access_token + "','" + time + "')";
            mysql.update(sql);
        } catch (Exception ex) {
            logger.error(we_back);
        }

        return access;
    }

    public static void getTemp(InfoModelHelper mysql) {

        String url = "https://api.weixin.qq.com/wxaapi/newtmpl/gettemplate?access_token=" + GetAccessToken();
        String we_back = HttpConnection.http_get(url);

        JSONObject one = JSONObject.parseObject(we_back);
        try {
            JSONArray datas = one.getJSONArray("data");
            String sql = "truncate wechat_temp";
            mysql.update(sql);
            if (datas.size() > 0) {
                for (int i = 0; i < datas.size(); i++) {
                    JSONObject o = datas.getJSONObject(i);
                    String tempid = o.getString("priTmplId");
                    String title = o.getString("title");


                    sql = "insert wechat_temp (name,temp_id,temp_data)values('" + title + "','" + tempid + "','')";
                    mysql.update(sql);

                }
            }
        } catch (Exception ex) {
            logger.error(we_back);
        }
    }

    public static String GetAccessToken() {
        InfoModelHelper mysql = null;
        String access_token = "";
        try {
            mysql = InfoModelPool.getModel();
            String sqls = "select * from wechat_access_token where id=1";
            List<JSONObject> list = mysql.query(sqls);
            if (list.size() > 0) {
                access_token = list.get(0).getString("access_token") + "|" + list.get(0).getString("time");
            } else {
                //GetAccessToken2h(mysql);
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }
        // logger.warn(access_token);
        return access_token;

    }


    public static JSONObject sendMsg(JSONObject data) {

        String ak[] = GetAccessToken().split("\\|");


        String url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=" + ak[0];
        // logger.warn(data.toString());
        String back = HttpConnection.post(url, data);
        //logger.warn("send->" + data.toString());
        // logger.error("back->" + back);
        JSONObject b = JSONObject.parseObject(back);
        return b;

    }

    public static String GetOpenId(String js_code) {
        String open_id = "";
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + appid + "&secret=" + secret + "&js_code=" + js_code + "&grant_type=authorization_code";
        String we_back = HttpConnection.http_get(url);
        // System.out.println(we_back);
        JSONObject b = JSONObject.parseObject(we_back);
        if (b.containsKey("openid")) {
            open_id = b.getString("openid");
        }
        return open_id;
    }

    public static String GetSession(String js_code) {
        String session_key = "";
        String open_id = "";
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + appid + "&secret=" + secret + "&js_code=" + js_code + "&grant_type=authorization_code";
        String we_back = HttpConnection.http_get(url);
        // System.out.println(we_back);
        JSONObject b = JSONObject.parseObject(we_back);
        if (b.containsKey("session_key") && b.containsKey("openid")) {
            session_key = b.getString("session_key");
            open_id = b.getString("openid");
        }
        return session_key + "," + open_id;
    }

}
