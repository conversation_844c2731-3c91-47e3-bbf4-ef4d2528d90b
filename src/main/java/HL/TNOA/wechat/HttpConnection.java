package HL.TNOA.wechat;


import HL.TNOA.Lib.Lib;
import com.alibaba.fastjson.JSONObject;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;


public class HttpConnection {


    public static String http_get_x(String url, String tokenName, String token) {
        try {
            OkHttpClient client = new OkHttpClient();
            Request request = new Request.Builder().url(url).get().addHeader(tokenName, token).build();

            Response res = client.newCall(request).execute();

            String back = res.body().string();
            //  System.out.println(back + "-->");
            return back;

        } catch (Exception ex) {
            System.out.println(Lib.getTrace(ex));
        }
        return "";
    }

    public static String http_get(String url) {
        String result = null;
        try {
            InputStream is = httpGet(url);
            BufferedReader in = new BufferedReader(new InputStreamReader(is, "UTF-8"));
            StringBuffer buffer = new StringBuffer();
            String line = "";
            while ((line = in.readLine()) != null) {
                buffer.append(line);
            }
            result = buffer.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return result;
    }

    public static String http_get_token(String url, String token) {
        String result = null;
        try {
            InputStream is = httpGet(url);
            BufferedReader in = new BufferedReader(new InputStreamReader(is, "UTF-8"));
            StringBuffer buffer = new StringBuffer();
            String line = "";
            while ((line = in.readLine()) != null) {
                buffer.append(line);
            }
            result = buffer.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return result;
    }

    private static InputStream httpGetJsonToken(String url, String token) {
        InputStream is = null;

        PrintWriter out = null;
        try {

            URL u;
            u = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) u.openConnection();
            conn.setRequestProperty("Content-Type", "application/json");
            // conn.setRequestProperty("Content-Type",
            // "application/json;charset=UTF-8");
            // conn.setRequestProperty("Accept-Charset", "UTF-8");
            conn.setRequestProperty("X-Access-Token", token);
            conn.setConnectTimeout(5 * 1000);
            conn.setReadTimeout(10 * 1000);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestMethod("GET");
			/*OutputStreamWriter outWriter = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
			out = new PrintWriter(outWriter);

			out.print();
			out.flush();
			out.close();*/


            is = conn.getInputStream();
        } catch (Exception e) {

            e.printStackTrace();
        }

        return is;
    }

    public static String http_get_head(String url, String headName, String token) {
        String result = null;
        try {
            InputStream is = httpGetHead(url, headName, token);
            BufferedReader in = new BufferedReader(new InputStreamReader(is, "UTF-8"));
            StringBuffer buffer = new StringBuffer();
            String line = "";
            while ((line = in.readLine()) != null) {
                buffer.append(line);
            }
            result = buffer.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return result;
    }

    private static InputStream httpGetHead(String url, String headName, String token) {
        InputStream is = null;

        PrintWriter out = null;
        try {

            URL u;
            u = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) u.openConnection();
            conn.setRequestProperty("Content-Type", "application/json");
            // conn.setRequestProperty("Content-Type",
            // "application/json;charset=UTF-8");
            // conn.setRequestProperty("Accept-Charset", "UTF-8");
            conn.setRequestProperty(headName, token);
            conn.setConnectTimeout(5 * 1000);
            conn.setReadTimeout(10 * 1000);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestMethod("GET");
            /*
             * OutputStreamWriter outWriter = new OutputStreamWriter(conn.getOutputStream(),
             * "UTF-8"); out = new PrintWriter(outWriter);
             *
             * out.print(); out.flush(); out.close();
             */

            is = conn.getInputStream();
        } catch (Exception e) {

            e.printStackTrace();
        }

        return is;
    }

    private static InputStream httpGet(String url) {
        InputStream is = null;

        PrintWriter out = null;
        try {

            URL u;
            u = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) u.openConnection();
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            // conn.setRequestProperty("Content-Type",
            // "application/json;charset=UTF-8");
            // conn.setRequestProperty("Accept-Charset", "UTF-8");
            // conn.setRequestProperty("contentType", "UTF-8");
            conn.setConnectTimeout(5 * 1000);
            conn.setReadTimeout(10 * 1000);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestMethod("GET");
			/*OutputStreamWriter outWriter = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
			out = new PrintWriter(outWriter);

			out.print();
			out.flush();
			out.close();*/


            is = conn.getInputStream();
        } catch (Exception e) {

            e.printStackTrace();
        }

        return is;
    }

    public static String post_head(String unUrl, String token, JSONObject data, String tokenName) {
        BufferedReader reader = null;
        HttpURLConnection connection = null;
        OutputStreamWriter out = null;
        try {
            URL url = new URL(unUrl);// 创建连接
            // System.out.println(url);
            connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
            connection.setRequestMethod("POST"); // 设置请求方式

            connection.setRequestProperty("Content-Type", "application/json"); // 设置发送数据的格式
            connection.setRequestProperty(tokenName, token);

            connection.connect();
            // 一定要用BufferedReader 来接收响应， 使用字节来接收响应的方法是接收不到内容的
            out = new OutputStreamWriter(connection.getOutputStream(), "UTF-8"); // utf-8编码
            // System.out.println(data.toString());
            out.append(data.toString());
            out.flush();
            out.close();
            // 读取响应
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            String line;
            String res = "";
            while ((line = reader.readLine()) != null) {
                res += line;
            }

            return res;
        } catch (IOException e) {
            System.out.println(e);

        } finally {
            if (connection != null) {
                connection.disconnect();
            }
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception ex) {

                }
            }
            if (out != null) {
                try {
                    out.flush();
                    out.close();
                } catch (Exception ex) {

                }
            }

        }
        JSONObject err = new JSONObject();
        err.put("errno", 0);
        err.put("error", "error");
        return err.toString(); // 自定义错误信息
    }

    public static String post(String strURL, JSONObject data) {
        BufferedReader reader = null;
        HttpURLConnection connection = null;
        OutputStreamWriter out = null;
        try {
            URL url = new URL(strURL);// 创建连接
            //System.out.println(url);
            connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
            connection.setRequestMethod("POST"); // 设置请求方式

            connection.setRequestProperty("Content-Type", "application/json"); // 设置发送数据的格式

            connection.connect();
            // 一定要用BufferedReader 来接收响应， 使用字节来接收响应的方法是接收不到内容的
            out = new OutputStreamWriter(connection.getOutputStream(), "UTF-8"); // utf-8编码
            //System.out.println(data.toString());
            out.append(data.toString());
            out.flush();
            out.close();
            // 读取响应
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            String line;
            String res = "";
            while ((line = reader.readLine()) != null) {
                res += line;
            }

            return res;
        } catch (IOException e) {
            System.out.println(e);

        } finally {
            if (connection != null) {
                connection.disconnect();
            }
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception ex) {

                }
            }
            if (out != null) {
                try {
                    out.flush();
                    out.close();
                } catch (Exception ex) {

                }
            }

        }
        return "error"; // 自定义错误信息
    }

    public static String post_str(String strURL, String data) {
        BufferedReader reader = null;
        HttpURLConnection connection = null;
        OutputStreamWriter out = null;
        try {
            URL url = new URL(strURL);// 创建连接
            //System.out.println(url);
            connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
            connection.setRequestMethod("POST"); // 设置请求方式

            connection.setRequestProperty("Content-Type", "application/json"); // 设置发送数据的格式

            connection.connect();
            // 一定要用BufferedReader 来接收响应， 使用字节来接收响应的方法是接收不到内容的
            out = new OutputStreamWriter(connection.getOutputStream(), "UTF-8"); // utf-8编码
            //System.out.println(data.toString());
            out.append(data.toString());
            out.flush();
            out.close();
            // 读取响应
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            String line;
            String res = "";
            while ((line = reader.readLine()) != null) {
                res += line;
            }

            return res;
        } catch (IOException e) {
            System.out.println(e);

        } finally {
            if (connection != null) {
                connection.disconnect();
            }
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception ex) {

                }
            }
            if (out != null) {
                try {
                    out.flush();
                    out.close();
                } catch (Exception ex) {

                }
            }

        }
        return "error"; // 自定义错误信息
    }

    /*public static JSONObject post_urlencoded(String url, JSONObject params_json) {
        HttpClientContext context = HttpClientContext.create();
        BasicCookieStore cookie = new BasicCookieStore();
        context.setCookieStore(cookie);

        CloseableHttpClient httpclient = null;
        CloseableHttpResponse response = null;
        try {
            // 创建client
            RequestConfig requestConfig = RequestConfig //请求设置
                    .custom()
                    .setConnectionRequestTimeout(30000) //从连接池获取连接超时时间
                    .setConnectTimeout(30000) //连接超时时间
                    .setSocketTimeout(30000).build(); //套接字超时时间

            HttpClientBuilder builder = HttpClients.custom();
            builder.setDefaultRequestConfig(requestConfig);
            builder.setMaxConnTotal(5); //设置最大连接数

            httpclient = builder.build();
            HttpPost request = new HttpPost(url);
//            setHeader(request);

            // 配置参数
//            if(params_str != null) {
//                HttpEntity entity = new StringEntity(params(), Charset.forName("UTF-8"));
//                request.setEntity(entity);
//            }
//            else if(params_json != null) {

            List<BasicNameValuePair> formparams = new ArrayList<>();
            for (String _key : params_json.keySet()) {
                formparams.add(new BasicNameValuePair(_key, params_json.getString(_key)));
            }
            UrlEncodedFormEntity params_entity = new UrlEncodedFormEntity(formparams, "UTF-8");
            request.setEntity(params_entity);
//            }

            //发送请求并获取返回结果
            // logger.warn(cookie.getCookies().toString());
            response = httpclient.execute(request, context);
            int status = response.getStatusLine().getStatusCode();
            if (status == HttpStatus.SC_OK || status == HttpStatus.SC_MOVED_TEMPORARILY) {
                if (status != HttpStatus.SC_OK) {
                    System.out.println("HttStatus is SC_MOVED_TEMPORARILY -> " + status);
                }
//                setResponseHeader(url, response.getAllHeaders());
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    System.out.println(EntityUtils.toString(entity, "UTF-8"));
                }
            } else {
                System.out.println("response -> " + response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (Exception e) {
                }
            }
            if (httpclient != null) {
                try {
                    httpclient.close();
                } catch (Exception e) {
                }
            }
        }
        return null;
    }
*/

    public static String post_urlencoded(String strURL, String sbParams) {
        HttpURLConnection con = null;
        OutputStreamWriter osw = null;
        BufferedReader br = null;
        StringBuffer resultBuffer = null;
        // 发送请求
        try {
            URL url = new URL(strURL);
            con = (HttpURLConnection) url.openConnection();
            con.setRequestMethod("POST");
            con.setDoOutput(true);
            con.setDoInput(true);

            con.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            con.setRequestProperty("Accept", "*/*");
            if (sbParams != null && sbParams.length() > 0) {
                osw = new OutputStreamWriter(con.getOutputStream(), "UTF-8");
                osw.write(sbParams.substring(0, sbParams.length() - 1));
                osw.flush();
            }
            // 读取返回内容
            resultBuffer = new StringBuffer();

            int contentLength = 0;
            try {
                contentLength = Integer.parseInt(con.getHeaderField("Content-Length"));
            } catch (NumberFormatException e) {
                contentLength = 1;
            }

            if (contentLength > 0) {
                br = new BufferedReader(new InputStreamReader(con.getInputStream(), "UTF-8"));
                String temp;
                while ((temp = br.readLine()) != null) {
                    resultBuffer.append(temp);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (osw != null) {
                try {
                    osw.close();
                } catch (IOException e) {
                    osw = null;
                    throw new RuntimeException(e);
                } finally {
                    if (con != null) {
                        con.disconnect();
                        con = null;
                    }
                }
            }
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    br = null;
                    throw new RuntimeException(e);
                } finally {
                    if (con != null) {
                        con.disconnect();
                        con = null;
                    }
                }
            }
        }

        return resultBuffer.toString();
    }

    public static String post_urlencoded_gbk(String strURL, String sbParams) {
        HttpURLConnection con = null;
        OutputStreamWriter osw = null;
        BufferedReader br = null;
        StringBuffer resultBuffer = null;
        // 发送请求
        try {
            URL url = new URL(strURL);
            con = (HttpURLConnection) url.openConnection();
            con.setRequestMethod("POST");
            con.setDoOutput(true);
            con.setDoInput(true);

            con.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            con.setRequestProperty("Accept", "*/*");
            if (sbParams != null && sbParams.length() > 0) {
                osw = new OutputStreamWriter(con.getOutputStream(), "GBK");
                osw.write(sbParams.substring(0, sbParams.length() - 1));
                osw.flush();
            }
            // 读取返回内容
            resultBuffer = new StringBuffer();

            int contentLength = 0;
            try {
                contentLength = Integer.parseInt(con.getHeaderField("Content-Length"));
            } catch (NumberFormatException e) {
                contentLength = 1;
            }

            if (contentLength > 0) {
                br = new BufferedReader(new InputStreamReader(con.getInputStream(), "GBK"));
                String temp;
                while ((temp = br.readLine()) != null) {
                    resultBuffer.append(temp);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (osw != null) {
                try {
                    osw.close();
                } catch (IOException e) {
                    osw = null;
                    throw new RuntimeException(e);
                } finally {
                    if (con != null) {
                        con.disconnect();
                        con = null;
                    }
                }
            }
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    br = null;
                    throw new RuntimeException(e);
                } finally {
                    if (con != null) {
                        con.disconnect();
                        con = null;
                    }
                }
            }
        }

        return resultBuffer.toString();
    }

    public static String post_token(String unUrl, String token, JSONObject data, String ip) {
        BufferedReader reader = null;
        HttpURLConnection connection = null;
        OutputStreamWriter out = null;
        try {
            URL url = new URL(unUrl);// 创建连接
            //System.out.println(url);
            connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
            connection.setRequestMethod("POST"); // 设置请求方式

            //设置超时
            connection.setConnectTimeout(30000);
//            connection.setReadTimeout(30000);

            connection.setRequestProperty("Content-Type", "application/json"); // 设置发送数据的格式
            connection.setRequestProperty("token", token);
            connection.setRequestProperty("project-id", "RK0005");

            connection.setRequestProperty("X-Real-IP", ip);

            connection.connect();
            // 一定要用BufferedReader 来接收响应， 使用字节来接收响应的方法是接收不到内容的
            out = new OutputStreamWriter(connection.getOutputStream(), "UTF-8"); // utf-8编码
            //System.out.println(data.toString());
            out.append(data.toString());
            out.flush();
            out.close();
            // 读取响应
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            String line;
            String res = "";
            while ((line = reader.readLine()) != null) {
                res += line;
            }

            return res;
        } catch (IOException e) {
            System.out.println(e);

        } finally {
            if (connection != null) {
                connection.disconnect();
            }
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception ex) {

                }
            }
            if (out != null) {
                try {
                    out.flush();
                    out.close();
                } catch (Exception ex) {

                }
            }

        }
        JSONObject err = new JSONObject();
        err.put("errno", 0);
        err.put("error", "error");
        return err.toString(); // 自定义错误信息
    }

    public static String post_tokenX(String unUrl, String token, JSONObject data) {
        BufferedReader reader = null;
        HttpURLConnection connection = null;
        OutputStreamWriter out = null;
        try {
            URL url = new URL(unUrl);// 创建连接
            //System.out.println(url);
            connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
            connection.setRequestMethod("POST"); // 设置请求方式

            //设置超时
            connection.setConnectTimeout(5000);
//            connection.setReadTimeout(30000);

            connection.setRequestProperty("Content-Type", "application/json"); // 设置发送数据的格式
            connection.setRequestProperty("X-Access-Token", token);


            connection.connect();
            // 一定要用BufferedReader 来接收响应， 使用字节来接收响应的方法是接收不到内容的
            out = new OutputStreamWriter(connection.getOutputStream(), "UTF-8"); // utf-8编码
            //System.out.println(data.toString());
            out.append(data.toString());
            out.flush();
            out.close();
            // 读取响应
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            String line;
            String res = "";
            while ((line = reader.readLine()) != null) {
                res += line;
            }

            return res;
        } catch (IOException e) {
            System.out.println(e);

        } finally {
            if (connection != null) {
                connection.disconnect();
            }
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception ex) {

                }
            }
            if (out != null) {
                try {
                    out.flush();
                    out.close();
                } catch (Exception ex) {

                }
            }

        }
        JSONObject err = new JSONObject();
        err.put("errno", 0);
        err.put("error", "error");
        return err.toString(); // 自定义错误信息
    }



   /* public static void main(String[] args) {
        String appid = "wxef33743a6a26330e";
        String secret = "f758f090b696889789a94ffde68bb769";
        //String js_code = "053N5h000RdK3M1SSN200axn5F3N5h07";

        // String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + appid + "&secret=" + secret +
        "&js_code=" + js_code + "&grant_type=authorization_code";
        //认证
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appid +
        "&secret=" + secret;
        String we_back = HttpConnection.http_get(url);
        System.out.println(we_back);

        String touse = "";
        String template_id = "";
        String data = "";
        url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=ACCESS_TOKEN&touse=" + touse +
                "&template_id=" + template_id + "& data = "+data+ " ";
    }*/

}
