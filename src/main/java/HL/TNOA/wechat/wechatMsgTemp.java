package HL.TNOA.wechat;

import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.Lib.TNOAConf;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.*;


public class wechatMsgTemp {
    private static Logger logger = LoggerFactory.getLogger(wechatMsgTemp.class);

    //公告
    public static void createMessage_notice1(JSONObject one, InfoModelHelper mysql, int isWechat, int type) throws Exception {
        logger.warn(one.toString());
        String id = one.getString("id");
        String reading = one.getString("reading");
        String create_user = one.getString("create_user");
        create_user = RIUtil.IdToName(create_user, mysql, " name", "user");
        String create_time = one.getString("create_time");
        String tp = one.getString("type");
        String title = one.getString("title");
        if (title.length() > 15) {
            title = title.substring(0, 15) + "...";
        }
        String t = "通知公告";


        List<String> userList = RIUtil.HashToList(RIUtil.StringToList(reading));
        logger.warn(userList.toString());
        if (userList.size() > 0 && !tp.equals("9")) {//9：工作动态-》不用通知
            if ("7".equals(tp)) {//警情
                t = "警情";

            } else if ("6".equals(tp)) {//信息报送
                t = "信息报送";
            } else if ("2".equals(tp)) {

                t = "党建";
            }
            t = t + ":" + title;
            TestMsgLine send = new TestMsgLine();
            send.sendMSG(t, userList);
        }

    }

    //事项
    public static void createMessage_task(JSONObject one, InfoModelHelper mysql, int isWechat, String type) throws Exception {
        logger.warn(one.toString());
        String id = one.getString("id");
        String reading = one.getString("accepter");

        String title = one.getString("title");

        if (title.length() > 15) {
            title = title.substring(0, 15) + "...";
        }
        String t = "事项";

        List<String> userList = RIUtil.HashToList(RIUtil.StringToList(reading));

        if (userList.size() > 0) {
            if (type.equals("6"))//巡防任务
            {
                t = "巡防任务";

            } else if (type.equals("22222"))//指令核查
            {
                t = "指令核查";

            } else if (type.equals("9999999"))//
            {
                t = "社区工作清单";

            }
            t = t + ":" + title;

            TestMsgLine send = new TestMsgLine();
            send.sendMSG(t, userList);
        }

    }

    //留言
    public static void createDingMsg(String notice_id, String comment, String comment_user, int source,
                                     String accepters, InfoModelHelper mysql, String comment_id) throws Exception {

        HashMap<String, String> accList = RIUtil.StringToList(accepters);
        String content = "";
        String title = "";


        TestMsgLine send = new TestMsgLine();
        send.sendMSG(content, RIUtil.HashToList(accList));
    }

    //打处
    public static void sendNotice(InfoModelHelper mysql, String opt_user, String create_time, String logs,
                                  String case_id) throws Exception {
        String sql = "select accepter,create_user,decode(case_name,'" + RIUtil.enTitle + "') as case_name from " +
                "case_info where id='" + case_id + "'";
        List<JSONObject> list = mysql.query(sql);
        if (list.size() > 0) {
            JSONObject one = list.get(0);
            String title = one.getString("case_name");

            if (title.length() > 15) {
                title = title.substring(0, 15) + "...";
            }
            if (logs.length() > 15) {
                logs = logs.substring(0, 15) + "...";
            }
            String accepter = one.getString("accepter");
            HashMap<String, String> notices = RIUtil.StringToList(accepter);
            notices.put(one.getString("create_user"), "");
            for (Map.Entry<String, String> o : notices.entrySet()) {
                String user = o.getKey();
                JSONObject msg = new JSONObject();
                msg.put("template_id", TNOAConf.get("wechat", "aj_temp"));
                msg.put("page", "pages_sub1/case/detail?id=" + case_id + "&jump=1");
                msg.put("miniprogram_state", TNOAConf.get("wechat", "miniprogram_state"));
                JSONObject data = new JSONObject();
                JSONObject value = new JSONObject();
                value.put("value", title);
                data.put("thing2", value);

                value = new JSONObject();
                value.put("value", logs);
                data.put("thing4", value);
                value = new JSONObject();
                value.put("value", RIUtil.IdToName(opt_user, mysql, " name",
                        "user"));
                data.put("thing6", value);

                value = new JSONObject();
                value.put("value", create_time);
                data.put("time18", value);
                msg.put("data", data);

                sql = "select open_id from user where id='" + user + "'";
                String touser = mysql.query_one(sql, "open_id");
                msg.put("touser", touser);
                msg.put("data", data);

                // logger.warn(msg.toString());
                sql = "insert msg_log(notice_id,accepter,data,type,status,msgType)" + "values('" + case_id + "','" + user + "',encode('" + msg + "','" + RIUtil.enContent + "'),'4',0,1)";
                mysql.update(sql);

            }

            TestMsgLine send = new TestMsgLine();
            send.sendMSG("警务打处", RIUtil.HashToList(notices));
        }

    }

    //通报d
    public static void createMessage_brief(String user_id, String brief_id, String title, String opt_user,
                                           InfoModelHelper mysql) throws Exception {
        String open_id = "";//RIUtil.IdToName(user_id, mysql, "open_id", "user");
        try {


            TestMsgLine send = new TestMsgLine();
            List<String> users = new ArrayList<>();
            users.add(user_id);
            send.sendMSG("通报:" + title, users);

        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
        }

    }

    //打处
    public static void createMessage_case(InfoModelHelper mysql, String title, String logs, String opt_user,
                                          HashMap<String, String> accs, String case_id) throws Exception {
        for (Map.Entry<String, String> o : accs.entrySet()) {
            String user = o.getKey();
            JSONObject msg = new JSONObject();
            msg.put("template_id", TNOAConf.get("wechat", "aj_temp"));
            msg.put("page", "pages_sub1/case/detail?id=" + case_id + "&jump=1");
            msg.put("miniprogram_state", TNOAConf.get("wechat", "miniprogram_state"));
            JSONObject data = new JSONObject();
            JSONObject value = new JSONObject();
            value.put("value", title);
            data.put("thing2", value);

            value = new JSONObject();
            value.put("value", logs);
            data.put("thing4", value);
            value = new JSONObject();
            value.put("value", RIUtil.IdToName(opt_user, mysql, " name", "user"
            ));
            data.put("thing6", value);

            value = new JSONObject();
            value.put("value", new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
            data.put("time18", value);
            msg.put("data", data);

            String sql = "select open_id from user where id='" + user + "'";
            String touser = mysql.query_one(sql, "open_id");
            msg.put("touser", touser);
            msg.put("data", data);


            sql = "insert msg_log(notice_id,accepter,data,type,status,msgType)" + "values('" + case_id + "','" + user + "',encode('" + msg + "','" + RIUtil.enContent + "'),'4',0,1)";
            mysql.update(sql);

        }
        TestMsgLine send = new TestMsgLine();

        send.sendMSG("警务打处：" + title, RIUtil.HashToList(accs));
    }

    //短信
    public static void sendMsg(JSONObject one, InfoModelHelper mysql, int isMsg, int type) throws Exception {

        String id = one.getString("id");
        String title = one.getString("title");
        String reading = one.getString("reading");
        String msg = "";
        if (type == 1) {
            reading = one.getString("reading");
            msg = "您有一条[公告]" + title + "待阅读,请至小程序查看。";
        } else if (type == 2) {
            reading = one.getString("accepter");
            msg = "您有一条[事项]" + title + "待办,请至小程序查看。";
        } else {
            msg = "您有一条消息待阅读,请至小程序查看。";
        }

        List<String> userList = RIUtil.HashToList(RIUtil.StringToList(reading));
        logger.warn(userList.toString());
        if (userList.size() > 0) {
            for (int a = 0; a < userList.size(); a++) {
                try {
                    String reader = userList.get(a);


                    int send = 1;
                    if (isMsg == 2) {
                        String s = "select count(id)as count from msg_log where accepter='" + reader + "' and " +
                                "notice_id='" + id + "' and type=" + type + " and msgType=2";
                        logger.warn(s);
                        int count = mysql.query_count(s);
                        logger.warn("count->" + count);
                        if (count > 0) {
                            send = 0;
                        } else {
                            send = 1;
                        }
                    }
                    if (send == 1) {
                        String sql =
                                "insert msg_log(notice_id,accepter,data,type,status,msgType)" + "values('" + one.getString("id") + "','" + userList.get(a) + "',encode('" + msg + "','" + RIUtil.enContent + "'),'" + type + "',0,2)";
                        mysql.update(sql);
                        logger.warn(sql);
                    }


                } catch (Exception e) {
                    logger.warn(Lib.getTrace(e));
                }
            }
        }
    }


}
