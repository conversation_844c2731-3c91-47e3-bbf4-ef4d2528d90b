package HL.TNOA.wechat;

import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;


public class TestMsgLine {
    private static String applicationId = "32040000000003202308020926020064";
    private static String appSecret = "5372593aba9a43899b7c740398ad65f6";
    private static Logger logger = LoggerFactory.getLogger(TestMsgLine.class);

    public static String sendMSG(String content, List<String> accepter) {
        HashMap<String, JSONObject> users = RIUtil.GetUsers();

        String recs = "";
        for (int i = 0; i < accepter.size(); i++) {
            logger.warn(accepter.get(i));
            try {


                String idnum = accepter.get(i);
                if (idnum.length() > 0) {
                    recs = recs + idnum + ",";
                }
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            }
        }

        // 1. 获取公钥

        String uuid = String.valueOf(UUID.randomUUID());

        String url = "http://50.56.90.82:8080/api/thirdParty/getPublicKeyInfoByApplicationIdAndUuid?applicationId="
                + applicationId + "&uuid=" + uuid;
        String back = HttpConnection.http_get(url);
        logger.warn(back);
        JSONObject ret = JSONObject.parseObject(back);
        if (ret.containsKey("code") && ret.getInteger("code") == 0) {
            JSONObject data = ret.getJSONObject("data");
            String pubKey = data.getString("publicKey");
            String salt = data.getString("salt");

            // 2 登录
            String appSecretBase64 = Base64.getEncoder().encodeToString(appSecret.getBytes());

            // 例如 学员的身份证号码为 320323199507296012
            String userName = "411081199304051277";

            String prefixPassword = userName.substring(6);

            String suffixStr = "";
            if (userName.toUpperCase().contains("X")) {
                suffixStr = userName.substring(userName.length() - 7, userName.length() - 1);
            } else {
                suffixStr = userName.substring(userName.length() - 6);
            }

            char[] appSecretBase64Array = appSecretBase64.toCharArray();

            StringBuilder suffixPassword = new StringBuilder(salt); // 中间加盐

            for (int i = 0; i < suffixStr.length(); i++) {
                suffixPassword.append(appSecretBase64Array[Integer.parseInt(suffixStr.substring(i, i + 1))]);
            }
            String systemValidatedPassword = prefixPassword + suffixPassword.toString();
            logger.warn("systemValidatedPassword   " + systemValidatedPassword);

            String sm2Password = SmUtil.sm2(null, pubKey)
                    .encryptHex(systemValidatedPassword.getBytes(), KeyType.PublicKey)
                    // 加密后，密文前面会有04，需要去掉
                    .substring(2);

            logger.warn(sm2Password);
            JSONObject det = new JSONObject();
            det.put("userName", userName);
            det.put("password", sm2Password);
            det.put("applicationId", applicationId);
            det.put("uuid", uuid);

            String strURL = "http://50.56.90.82:8080/api/thirdParty/loginByThirdParty";
            back = HttpConnection.post(strURL, det);
            logger.warn(back);

            ret = JSONObject.parseObject(back);
            if (ret.containsKey("code") && ret.getInteger("code") == 0) {
                data = ret.getJSONObject("data");

                String sendMessageTokenValue = data.getString("sendMessageTokenValue");
                String tokenName = data.getString("sendMessageTokenName");
                String uu = "http://50.56.90.83:2002/messagebus/thirdPartyMessage" +
                        "/getBusinessSystemNameListByApplicationId?applicationId="
                        + applicationId;

                String bb = HttpConnection.http_get_head(uu, sendMessageTokenValue, tokenName);
                logger.warn(bb);
                ret = JSONObject.parseObject(bb);
                if (ret.containsKey("code") && ret.getInteger("code") == 0) {
                    data = ret.getJSONObject("data");

                    String businessSystemNameList = data.getString("businessSystemNameList").replace("[", "")
                            .replace("]", "").replace("\"", "");
                    String u = "http://50.56.90.83:2002/messagebus/thirdPartyMessage/sendMessage";
                    det = new JSONObject();
                    det.put("applicationId", applicationId);
                    det.put("receiveType", "01");
                    det.put("title", "全警基础");
                    det.put("content", content);
                    det.put("receiveDeviceType", "02");
                    det.put("needJump", "0");
                    det.put("businessSystemName", businessSystemNameList);
                    det.put("receiveUserIds", recs);

                    String b = HttpConnection.post_head(u, sendMessageTokenValue, det, tokenName);
                    logger.warn(b);
                    return b;
                }
            }

        }
        return "2";
    }


}
