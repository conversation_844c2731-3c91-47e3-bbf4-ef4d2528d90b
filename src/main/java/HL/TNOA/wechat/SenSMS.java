package HL.TNOA.wechat;

import HL.TNOA.Lib.TNOAConf;
import com.alibaba.fastjson.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class SenSMS {
    public static void main(String[] args) throws Exception {
        String text = "请查收111";
        String phone = "17301516842";
        sendSMS(text, phone);
    }

    public static JSONObject sendSMS(String text, String phone) throws Exception {

        Date date = Calendar.getInstance().getTime();
        String messageContent = "请查收" + text;
        System.out.println(messageContent);
        // String messageContent=this.text;
//        String scheduleTime = new SimpleDateFormat("yyyyMMddHHmmss").format(date);
        String serialNumber = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(date) + getThreeRandomNumbers();

        JSONObject params = new JSONObject();
        params.put("SpCode", "267912");// 企业编号
        params.put("LoginName", "cz_zhzf"); // 登录用户名
        params.put("Password", "0d22991718f9edcd76fdc6cdbc79da53");// 登录用户密码
        //短信内容, 最大480个字（短信内容要求的编码为gbk）
        params.put("MessageContent", messageContent);
        //短信类型，1=验证码、2=通知、3=广告 （没传或者非1、2、3系统会默认为4=其他）
        params.put("MessageType", "2");
        //手机号码(多个号码用”,”分隔)，最多1000个号码
        params.put("UserNumber", phone.replace(";", ","));
        //流水号，20位数字，唯一 （规则自定义,建议时间格式精确到毫秒）
        params.put("SerialNumber", serialNumber);
        //预约发送时间，格式:yyyyMMddhhmmss,如‘20090901010101’ 立即发送请填空（预约时间要写当前时间5分钟之后的时间，若预约时间少于5分钟，则即时发送）
        params.put("ScheduleTime", "");
        //接入号扩展号（默认不填，扩展号为数字，扩展位数由当前所配的接入号长度决定，整个接入号最长4位）
        params.put("ExtendAccessNum", "");
        // params.put("templateId", "2061012169554");
        /*
         * 提交时检测方式
         * 为1时，提交号码中有效的号码仍正常发出短信，无效的号码在返回参数faillist中列出
         * 不为1或不传时，提交号码中只要有无效的号码，所有的号码都不发短信，所有的号码在返回参数faillist中列出
         */
        params.put("f", "1");


        return httpd("https://api.ums86.com:9600/sms/Api/Send.do", params);
    }

    private static int getThreeRandomNumbers() {
        return new java.util.Random().nextInt(900) + 100;
    }

    public static JSONObject httpd(String url, JSONObject map) throws Exception {
        HashMap<String, String> pars = new HashMap<>();
        String pstr = "";

        for (Map.Entry<String, Object> one : map.entrySet()) {
            String key = one.getKey();
            String value = one.getValue().toString();

            pars.put(key, value);
            pstr = pstr + key + "=" + value + "&";
        }
        //pstr = pstr.substring(0, pstr.length() - 1);
        System.out.println(pstr);

        String back = HttpConnection.post_urlencoded_gbk(url, pstr);
        System.out.println(back);
        JSONObject one = new JSONObject();

        if (back.contains("result=0")) {

            one.put("ok", "ok");
            return one;
        } else {
            one.put("errno", "2");
            return one;
        }

    }

    public static JSONObject sendMsg(JSONObject data) {
        String ip = TNOAConf.get("sjt_temp", "notice_ip");

        String url = "http://"+ip+"/message/v1.0.0/task/send";

        String post = HttpConnection.post(url, data);
        JSONObject back = new JSONObject();
        if(post.contains("请求成功")){
            back.put("ok", "ok");
            return back;
        }else{
            back.put("errno", "2");
            return back;
        }

    }
}
