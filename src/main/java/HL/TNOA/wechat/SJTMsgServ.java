package HL.TNOA.wechat;

import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SJTMsgServ {
    private static String url = "http://*************:99/macp/messageService/lxsend";
    private static String appid = "a5itsU6SFxQllgZhjBfD";
    private static String secret = "a5itsU6SFxQllgZhjBfDih37LRHg7KpidumipFcyYBYvaGMB1lzWwUpyTl16NN5p";
    private static String msgtype = "text";
    private static Logger logger = LoggerFactory.getLogger(SJTMsgServ.class);

    public String sjtMsgSend(String content, List<String> accepter) {
        try {

            String touser = "";
            content = "[全警基础]您有一条" + content + ",请至APP查收！";
            logger.warn(content);
            int mark = 1;
            for (int i = 0; i < accepter.size(); i++) {
                System.out.println(accepter.get(i));
                try {
                    String idnum = RIUtil.users.get(accepter.get(i)).getString("id_num");
                    if (idnum.length() > 0) {
                        touser = touser + idnum + ",";
                    }
                } catch (Exception ex) {
                    logger.error(Lib.getTrace(ex));
                }


                mark++;
                if ((mark == 20 || accepter.size() < 20) && touser.length() > 1) {

                    touser = touser.substring(0, touser.length() - 1);
                    JSONObject msg = params_msg(content, touser);
                    httpd(url, msg);
                    mark = 1;
                    touser = "";
                }

            }
        } catch (Exception ex) {

        }
        return "0k";
    }

    private JSONObject params_msg(String content, String touser) {


        JSONObject params = new JSONObject();
        params.put("appid", appid);
        params.put("secret", secret);


        params.put("msgtype", msgtype);
        params.put("content", content);
        params.put("touser", touser);

        return params;
    }

    private String httpd(String url, JSONObject map) {
        HashMap<String, String> pars = new HashMap<>();
        String pstr = "";

        for (Map.Entry<String, Object> one : map.entrySet()) {
            String key = one.getKey();
            String value = one.getValue().toString();

            pars.put(key, value);
            pstr = pstr + key + "=" + value + "&";
        }
        logger.warn(pstr);

        String back = "";
        back = HttpConnection.post_urlencoded(url, pstr);
        logger.warn(back);

        return back;


    }

}
