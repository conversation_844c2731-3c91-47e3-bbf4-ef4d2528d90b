package HL.TNOA.Alarm;//package HL.SCI.Alarm;
//
//import java.text.SimpleDateFormat;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Random;
//
//import HL.SCI.Lib.Lib;
//import HL.SCI.Lib.MysqlModel.MysqlPool;
//import HL.SCI.Lib.RedisHelper;
//import HL.SCI.Lib.WifiProc;
//import HL.SCI.Start.WifiDeamon;
//import HL.SCI.Start.WifiRunStatus;
//import HL.SCI.Lib.GetTraceInfo;
//import HL.SCI.Lib.MysqlModel.MysqlHelper;
//import org.json.JSONObject;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//public class AlarmNotice {
//    private static Logger logger = LoggerFactory.getLogger(AlarmNotice.class);
//    private int alarmtype = -1;
//    private String alarmtel = null;
//    private String log = null;
//    private int userid = -1;
//    private String time = null;
//
//    private boolean isSmsSystem = true;
//    public static int WebPushPort = 16041;
//    public JSONObject sendjson;
//
//    static {
//        if(WifiProc.has("SmsServ", "WebPushPort")){
//            WebPushPort = WifiProc.getInt("SmsServ", "WebPushPort");
//        }
//    }
//
//	public AlarmNotice(String log) {
//		this.log = log;
//
//        sendjson = new JSONObject();
//
//        if (alarmtype == -1) {
//            sendjson.put("type", 3);
//        } else {
//            sendjson.put("type", this.alarmtype);
//        }
//
//        if (time == null) {
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            sendjson.put("time", sdf.format(new Date()));
//        }
//        else {
//            sendjson.put("time", this.time);
//        }
//
//        sendjson.put("log", log);
//        sendjson.put("userid", userid);
//    }
//
//    public AlarmNotice(JSONObject data, String alarmtel) {
//        this.sendjson = data;
//        this.alarmtel = alarmtel;
//        this.log = data.getString("log");
//        isSmsSystem = false;
//    }
//
//    /*
//    alarmtype --> 1 布控消息
//    alarmtype --> 2 禁区消息
//    alarmtype --> 3 系统消息
//     */
//    public AlarmNotice(int alarmtype, String alarmtel, String time,  String log,
//                       int userid, boolean isSmsSystem) {
//        this.alarmtype = alarmtype;
//        this.alarmtel = alarmtel;
//        this.time = time;
//        this.log = log;
//        this.userid = userid;
//        this.isSmsSystem = isSmsSystem;
//
//        sendjson = new JSONObject();
//
//        if (alarmtype == -1) {
//            sendjson.put("type", 3);
//        } else {
//            sendjson.put("type", this.alarmtype);
//        }
//
//        if (time == null) {
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            sendjson.put("time", sdf.format(new Date()));
//        } else {
//            sendjson.put("time", this.time);
//        }
//
//        sendjson.put("log", log);
//        sendjson.put("userid", userid);
//    }
//
//    public void sent() {
//        if (log == null) {
//            return;
//        }
//        //websocket
//        try {
//            //如果程序没有正常启动，是无法推送websock
//            if (!WifiRunStatus.GetRunStatus(WifiDeamon.WifiStop)) {
//                try {
//                    RedisHelper redis = new RedisHelper("Status");
//                    try{
//                        if(redis.hlen("Alarm") >= 10000){
//                            redis.del("Alarm");
//                        }
//                        Random random = new Random();
//                        String time = (random.nextInt(8888) + 1000) + "" + new Date().getTime();
//                        redis.hset("Alarm", time, sendjson.toString());
//                    }
//                    catch (Exception e){
//                        logger.error(GetTraceInfo.get(e));
//                    }
//                    finally {
//                        Lib.quiet_close(redis);
//                    }
//                } catch (Exception e) {
//                    logger.error(GetTraceInfo.get(e));
//                }
//            }
//
//            //短信发送
//            try {
//                if (Sms.SmsEnable) {
//                    if (this.alarmtel == null || this.alarmtel.length() == 0) {
//                        if (isSmsSystem) {
//                            String smssql = "select * from blacklist_info where alarm_keys='system'";
//                            MysqlHelper mysql = MysqlPool.getModel();
//                            try {
//                                List<HashMap<String, String>> smslist = mysql.query(smssql);
//                                if (smslist.size() != 0 && smslist.get(0).get("alarm_tel") != null
//                                        && smslist.get(0).get("alarm_tel").length() >= 5) {
//                                    Sms.sendsms(smslist.get(0).get("alarm_tel"), log);
//                                }
//                            }
//                            finally {
//                                MysqlPool.putModel(mysql);
//                            }
//                        }
//                    } else {
//                        if (this.alarmtel.length() > 4) {
//                            Sms.sendsms(this.alarmtel, log);
//                        }
//                    }
//                }
//            } catch (Exception e) {
//                logger.error(GetTraceInfo.get(e));
//            }
//        } catch (Exception ex) {
//            logger.error(GetTraceInfo.get(ex));
//        }
//    }
//}
