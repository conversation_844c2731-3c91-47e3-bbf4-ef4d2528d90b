package HL.TNOA.Alarm;//package HL.SCI.Alarm;
//
//import java.io.BufferedReader;
//import java.io.InputStreamReader;
//import java.io.OutputStream;
//import java.net.InetSocketAddress;
//import java.net.Socket;
//import java.net.SocketTimeoutException;
//
//import HL.SCI.Lib.WifiProc;
//import org.json.JSONObject;
//
//import HL.SCI.Lib.GetTraceInfo;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//public class Sms {
//	private static Logger logger = LoggerFactory.getLogger(Sms.class);
//	public static boolean SmsEnable = false;
//	public static String SmsServer = "127.0.0.1";
//	public static int SmsPort = 16040;
//
//	static {
//		if(WifiProc.has("SmsServ", "start") &&
//				"true".equals(WifiProc.get("SmsServ", "start").trim())){
//			SmsEnable = true;
//
//			if(WifiProc.has("SmsServ", "SmsServer")) {
//				SmsServer = WifiProc.get("SmsServ", "SmsServer");
//			}
//
//			if(WifiProc.has("SmsServ", "SmsPort")) {
//				SmsPort = WifiProc.getInt("SmsServ", "SmsPort");
//			}
//		}
//	}
//	//sms json格式，如
//	//JSONObject json = new JSONObject();
//	//手机号码
//	//json.put("tel", "123456789");
//	//短信内容
//	//json.put("sms", "xxxxxxxxxxxxxx");
//
//	public static JSONObject sendsms(String alarmtel, String log) {
//		try {
//			if(SmsEnable) {
//				Socket sock = new Socket();
//				sock.setSoTimeout(1000);
//				sock.connect(new InetSocketAddress(SmsServer, SmsPort), 1000);
//				OutputStream os = sock.getOutputStream();
//				BufferedReader br = new BufferedReader(new InputStreamReader(sock.getInputStream()));
//
//				try {
//					JSONObject sms = new JSONObject();
//					sms.put("tel", alarmtel);
//					sms.put("sms", log);
//					os.write((sms.toString()+"\r\n").getBytes());
//					logger.warn("sms:" + sms);
//					String buff = br.readLine();
//					if(buff == null) {
//						JSONObject retjson = new JSONObject();
//						retjson.put("error", "尚未收到短信服务器反馈信息");
//						return retjson;
//					}
//					return new JSONObject(buff);
//				}
//				finally {
//					br.close();
//					os.close();
//					sock.close();
//				}
//			}
//		}
//		catch (SocketTimeoutException e){
//			logger.warn("短信服务器尚未开启");
//			JSONObject retjson = new JSONObject();
//			retjson.put("error", "短信服务器尚未开启。");
//			return retjson;
//		}
//		catch(Exception e) {
//			logger.warn(GetTraceInfo.get(e));
//			JSONObject retjson = new JSONObject();
//			retjson.put("error", GetTraceInfo.get(e));
//			return retjson;
//		}
//		return null;
//
//	}
//}
