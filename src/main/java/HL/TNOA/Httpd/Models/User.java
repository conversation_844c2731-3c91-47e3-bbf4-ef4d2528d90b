package HL.TNOA.Httpd.Models;

import java.io.Serializable;

public class User implements Serializable {
    String id = "";
    String police_id = "";
    String id_num = "";
    String open_id = "";
    String name = "";
    String unit = "";
    String position = "";
    String tele_long = "";
    String tele_sort = "";
    String tele_home = "";
    String address_home = "";
    String pwd = "";
    String status = "";
    String role = "";

    @Override
    public String toString() {
        return "User [id=" + id + ", open_id=" + open_id + ", police_id=" + police_id + ", id_num=" + id_num
                + ", name=" + name + ", unit=" + unit + ", position=" + position + ", tele_long=" + tele_long
                + ", tele_sort=" + tele_sort + ", tele_home=" + tele_home + ", address_home=" + address_home + ", status=" + status + ", role="
                + role + ",pwd=" + pwd + "]";

    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPolice_id() {
        return police_id;
    }

    public void setPolice_id(String police_id) {
        this.police_id = police_id;
    }

    public String getId_num() {
        return id_num;
    }

    public void setId_num(String id_num) {
        this.id_num = id_num;
    }

    public String getOpen_id() {
        return open_id;
    }

    public void setOpen_id(String open_id) {
        this.open_id = open_id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getTele_long() {
        return tele_long;
    }

    public void setTele_long(String tele_long) {
        this.tele_long = tele_long;
    }

    public String getTele_sort() {
        return tele_sort;
    }

    public void setTele_sort(String tele_sort) {
        this.tele_sort = tele_sort;
    }

    public String getTele_home() {
        return tele_home;
    }

    public void setTele_home(String tele_home) {
        this.tele_home = tele_home;
    }

    public String getAddress_home() {
        return address_home;
    }

    public void setAddress_home(String address_home) {
        this.address_home = address_home;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }
}
