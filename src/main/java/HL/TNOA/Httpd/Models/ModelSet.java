package HL.TNOA.Httpd.Models;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;

public class ModelSet {
    private static Logger logger = LoggerFactory.getLogger(ModelSet.class);

    /**
     * 利用反射，将JSON数据填充至实例中
     *
     * @param model 目标实体类实例
     * @param json  数据JSON
     * @throws Exception
     */

    public static void set(Object model, JSONObject json) throws Exception {
        Class clz = model.getClass();

        Method[] methods = clz.getMethods();
        for (Method _method : methods) {
            if (!_method.getName().startsWith("set")) {
                continue;
            }
            for (String _key : json.keySet()) {
                String _newkey = _key.toLowerCase().replaceAll("_", "");
                int count = _method.getParameterCount();
                if (count != 1) {
                    throw new Exception("配置参数" + _method.getName() + "错误");
                }

                Class<?>[] types = _method.getParameterTypes();

                if (_newkey.equalsIgnoreCase(_method.getName().substring(3))) {
                    if (types[0].getName().equals(String.class.getName())) {
                        _method.invoke(model, json.getString(_key));
                    } else if (types[0].getName().equals(int.class.getName()) || types[0].getName().equals(Integer.class.getName())) {
                        _method.invoke(model, json.getIntValue(_key));
                    } else if (types[0].getName().equals(long.class.getName()) || types[0].getName().equals(Long.class.getName())) {
                        _method.invoke(model, json.getLongValue(_key));
                    } else {
                        throw new Exception("参数" + _method.getName() + "类型错误" + types[0].getName());
                    }
                }
            }
        }
    }
}
