package HL.TNOA.Httpd.Models;


import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.IpMac;
import HL.TNOA.Lib.Lib;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;

public class UserLog {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    //1=>系统日志 2=>操作日志 3=>意见反馈
    public String TYPE_SYSTEM = "1";
    public String TYPE_OPERATE = "2";
    public String TYPE_FROMUSER = "3";

    public void log(InfoModelHelper infomodel, String user_id,
                    String log, String type, String ipaddr) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String sql = "insert into user_log(user_id, time, log, type, ipaddr, mac,source) values" +
                    " ('" + user_id + "','" + sdf.format(new Date()) + "'," +
                    "'" + log + "','" + type + "','" + ipaddr + "','" +
                    IpMac.get_mac(ipaddr) + "',2)";
            infomodel.update(sql);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }
    }
}
