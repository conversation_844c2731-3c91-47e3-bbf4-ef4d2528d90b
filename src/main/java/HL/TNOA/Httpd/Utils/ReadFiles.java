package HL.TNOA.Httpd.Utils;


import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.TNOAConf;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.ooxml.POIXMLDocument;
import org.apache.poi.ooxml.extractor.POIXMLTextExtractor;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.ArrayList;
import java.util.List;


public class ReadFiles {
    private static Logger logger = LoggerFactory.getLogger(ReadFiles.class);

    public static void main(String[] args) {
        String back = getContent("101");
        //  System.out.println(back);
        String[] lines = back.split("\\##");


    }

    private static String GetKeyWords(String org_text, String suf) {
        String[] cs = org_text.split("^");
        int suflen = suf.length();
        String comm = "";

        for (int c = 1; c < cs.length; c++) {

            try {
                for (int s = 0; s < suflen; s++) {

                    comm = comm + cs[c];
                    c++;

                }

                if (comm.contains(suf)) {

                    //  System.out.println(c);
                    String dealStr = org_text.substring(c, c + 10);
                    System.out.println(dealStr);

                } else {
                    c = c - suflen;
                }

            } catch (Exception ex) {

            }
            comm = "";

        }

        return "";
    }

    public static String getContent(String file_id) {
        InfoModelHelper mysql = null;
        String back = "";
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select * from upload where id='" + file_id + "'";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                JSONObject one = list.get(0);
                String file_name = TNOAConf.get("file", "img_path") + one.getString("file_path") + one.getString(
                        "file_name");
                logger.warn(file_name);
                //file_name = "store_54b5fa062bc5c9be2145065dbfcaa6f18acb9092f6d41232236f210cb6f9cda2_0907081419.doc";
                if (file_name.toLowerCase().endsWith("xls") || file_name.toLowerCase().endsWith("xlsx")) {
                    back = ReadExcel(file_name);
                    // System.out.println(back);
                } else if (file_name.toLowerCase().endsWith("doc") || file_name.toLowerCase().endsWith("docx")) {
                    back = ReadDoc(file_name);
                    //System.out.println(back);
                } else if (file_name.toLowerCase().endsWith("txt")) {
                    back = ReadTxt(file_name);
                }
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    public static ArrayList<String> getContentList(String file_id) {
        InfoModelHelper mysql = null;
        ArrayList<String> back = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select * from upload where id='" + file_id + "'";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                JSONObject one = list.get(0);
                String file_name = TNOAConf.get("file", "img_path") + one.getString("file_path") + one.getString(
                        "file_name");
                logger.warn(file_name);
                //file_name = "store_54b5fa062bc5c9be2145065dbfcaa6f18acb9092f6d41232236f210cb6f9cda2_0907081419.doc";
                if (file_name.toLowerCase().endsWith("xls") || file_name.toLowerCase().endsWith("xlsx")) {
                    back = ReadExcelList(file_name);
                    // System.out.println(back);
                }
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static String ReadTxt(String file_name) throws IOException {
        BufferedReader br = null;
        String pa = "";
        try {
            br = new BufferedReader(new FileReader(new File(file_name)));
            String line = "";
            while ((line = br.readLine()) != null) {

                pa = pa + line.trim() + "#";

            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            if (br != null) {
                br.close();
            }
        }
        return pa;
    }

    private static String ReadDoc(String filePath) {
        // filePath="store_54b5fa062bc5c9be2145065dbfcaa6f18acb9092f6d41232236f210cb6f9cda2_0907081419.doc";
        List<String> linList = new ArrayList<String>();
        String buffer = "";
        String back = "";
        try {
            if (filePath.endsWith(".doc")) {
                InputStream is = new FileInputStream(new File(filePath));
                WordExtractor ex = new WordExtractor(is);
                buffer = ex.getText();
                ex.close();

                if (buffer.length() > 0) {
                    //使用回车换行符分割字符串
                    String[] arry = buffer.split("\\r\\n");
                    for (String string : arry) {
                        back = back + string.trim().replaceAll("[<>()\\s'\"\\/]", "");
                    }
                }
            } else if (filePath.endsWith(".docx")) {
                OPCPackage opcPackage = POIXMLDocument.openPackage(filePath);
                POIXMLTextExtractor extractor = new XWPFWordExtractor(opcPackage);
                buffer = extractor.getText();
                extractor.close();

                if (buffer.length() > 0) {
                    //使用换行符分割字符串
                    String[] arry = buffer.split("\\n");
                    for (String string : arry) {
                        back = back + string.trim().replaceAll("[<>()\\s'\"\\/]", "");
                    }
                }
            } else {
                return "";
            }

            return back;
        } catch (Exception e) {
            System.out.print("error---->" + filePath);
            e.printStackTrace();
            return "";
        }


    }

    private static String ReadExcel(String excelPath) {
        String con = "";

        try {
            //String encoding = "GBK";
            File excel = new File(excelPath);
            if (excel.isFile() && excel.exists()) {   //判断文件是否存在

                String[] split = excel.getName().split("\\.");  //.是特殊字符，需要转义！！！！！


                // Workbook book = Workbook.getWorkbook(new File("D:\\excel\\Shirley.xls"), workbookSettings);
                Workbook wb;
                //根据文件后缀（xls/xlsx）进行判断
                if ("xls".equals(split[1].toLowerCase())) {
                    try {
                        FileInputStream fis = new FileInputStream(excel);   //文件流对象
                        wb = new HSSFWorkbook(fis);
                    } catch (Exception ex) {
                        wb = new XSSFWorkbook(excel);
                    }
                } else if ("xlsx".equals(split[1].toLowerCase())) {
                    wb = new XSSFWorkbook(excel);
                } else {
                    System.out.println("文件类型错误!");
                    return "";
                }

                //开始解析
                int sheetsCount = wb.getNumberOfSheets();
                System.out.println(sheetsCount);
                for (int i = 0; i < sheetsCount; i++) {
                    Sheet sheet = wb.getSheetAt(i);     //读取sheet 0

                    int firstRowIndex = sheet.getFirstRowNum();
                    int lastRowIndex = sheet.getLastRowNum();
                    //System.out.println("firstRowIndex: " + firstRowIndex);
                    // System.out.println("lastRowIndex: " + lastRowIndex);
                    Row firstRow = sheet.getRow(i);
                    int firstCellIndex = 0;
                    int lastCellIndex = 0;
                    if (firstRow != null) {
                        firstCellIndex = firstRow.getFirstCellNum();
                        lastCellIndex = firstRow.getLastCellNum();
                    }else {
                        System.out.println("文件数据为空!");
                        break;
                    }
                    for (int rIndex = firstRowIndex+1; rIndex <= lastRowIndex; rIndex++) {   //遍历行
                        //System.out.println("rIndex: " + rIndex);
                        Row row = sheet.getRow(rIndex);
                        if (row != null) {
                            for (int cIndex = firstCellIndex; cIndex < lastCellIndex; cIndex++) {   //遍历列

                                Cell cell = row.getCell(cIndex, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                                if (cell != null) {
                                    if (cell.toString().trim().length() > 0) {
                                        String str = cell.toString().trim();
                                        con = con + str.trim() + "|";
                                    }else {
                                        con = con + " |";
                                    }
                                }else {
                                    con = con + " |";
                                }
                            }
                            con = con + "#";
                        }
                    }
                }
            } else {
                System.out.println("找不到指定的文件");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // System.out.println(con.toString());
        return con;
    }

    private static ArrayList<String> ReadExcelList(String excelPath) {

        ArrayList<String> arr = new ArrayList<>();
        try {

            //String encoding = "GBK";
            File excel = new File(excelPath);
            if (excel.isFile() && excel.exists()) {   //判断文件是否存在

                String[] split = excel.getName().split("\\.");  //.是特殊字符，需要转义！！！！！


                // Workbook book = Workbook.getWorkbook(new File("D:\\excel\\Shirley.xls"), workbookSettings);
                Workbook wb;
                //根据文件后缀（xls/xlsx）进行判断
                if ("xls".equals(split[1].toLowerCase())) {
                    try {
                        FileInputStream fis = new FileInputStream(excel);   //文件流对象
                        wb = new HSSFWorkbook(fis);
                    } catch (Exception ex) {
                        wb = new XSSFWorkbook(excel);
                    }
                } else if ("xlsx".equals(split[1].toLowerCase())) {
                    wb = new XSSFWorkbook(excel);
                } else {
                    System.out.println("文件类型错误!");
                    return null;
                }

                //开始解析
                int sheetsCount = wb.getNumberOfSheets();
                System.out.println(sheetsCount);
                for (int i = 0; i < sheetsCount; i++) {
                    String con = "";
                    Sheet sheet = wb.getSheetAt(i);     //读取sheet 0

                    int firstRowIndex = sheet.getFirstRowNum();
                    int lastRowIndex = sheet.getLastRowNum();
                    //System.out.println("firstRowIndex: " + firstRowIndex);
                    // System.out.println("lastRowIndex: " + lastRowIndex);
                    Row firstRow = sheet.getRow(i);
                    int firstCellIndex = 0;
                    int lastCellIndex = 0;
                    if (firstRow != null) {
                        firstCellIndex = firstRow.getFirstCellNum();
                        lastCellIndex = firstRow.getLastCellNum();
                    }else {
                        System.out.println("文件数据为空!");
                        break;
                    }
                    for (int rIndex = firstRowIndex+1; rIndex <= lastRowIndex; rIndex++) {   //遍历行
                        //System.out.println("rIndex: " + rIndex);
                        Row row = sheet.getRow(rIndex);
                        if (row != null) {
                            for (int cIndex = firstCellIndex; cIndex < lastCellIndex; cIndex++) {   //遍历列

                                Cell cell = row.getCell(cIndex, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                                if (cell != null) {
                                    if (cell.toString().trim().length() > 0) {
                                        String str = cell.toString().trim();
                                        con = con + str.trim() + "|";
                                    }else {
                                        con = con + " |";
                                    }
                                }else {
                                    con = con + " |";
                                }
                            }
                            con = con + "#";
                        }
                    }
                    arr.add(con);
                }
            } else {
                System.out.println("找不到指定的文件");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // System.out.println(con.toString());
        return arr;
    }
}
