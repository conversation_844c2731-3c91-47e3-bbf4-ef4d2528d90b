package HL.TNOA.Httpd.Utils;//package HL.TNOA.Httpd.Utils;
//
//import HL.TNOA.Lib.InfoModel.InfoModelHelper;
//import HL.TNOA.Lib.InfoModel.InfoModelPool;
//import HL.TNOA.Lib.Lib;
//import HL.TNOA.Lib.RIUtil;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.scheduling.annotation.EnableScheduling;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.text.SimpleDateFormat;
//import java.util.*;
//
//@Component
//@EnableScheduling
//public class ExamineSchedule {
//    private static Logger logger = LoggerFactory.getLogger(ExamineSchedule.class);
//
////    @Scheduled(cron ="0 0 0 1 * ?")
////    @Scheduled(cron ="0 0/2 * * * ?")
//    public void initExamine() {
//        try {
//            //统计分数
//            countScoreDet();
//            //月排名
//            monthRank();
//            //年排名
//            yearRank();
//            System.out.println("====================================================结束");
//        }catch (Exception ex) {
//            logger.error(Lib.getTrace(ex));
//        }
//    }
//
//    private void countScoreDet() {
//        InfoModelHelper mysql = null;
//
//        String time = "";
//        List<JSONObject> configs_zrq = new ArrayList<>();
//        List<JSONObject> configs_pcs = new ArrayList<>();
//        List<JSONObject> configs_fj = new ArrayList<>();
//        List<JSONObject> zrqs = null;
//        List<JSONObject> pcss = null;
//        List<JSONObject> fjs = null;
//        List<JSONObject> dets = null;
//
//        try {
//            mysql = InfoModelPool.getModel();
//
//            String s = "";
//            s = "select type,name,value from kh_control where type = 'kh_total'";
//            List<JSONObject> control = mysql.query(s);
//
//            String pid = "";
//
//            for (int i = 0; i < control.size(); i++) {
//                JSONObject c = control.get(i);
//                String name = c.getString("name");
//                String value = c.getString("value");
//                if ("total_plan".equals(name)) {
//                    pid = value;
//                }
//                if ("total_month".equals(name)) {
//                    time = value;
//                }
//            }
//
//            s = "select * from kh_plan where id = '"+pid+"'";
//            List<JSONObject> list = mysql.query(s);
//
//            System.out.println("进入分数统计--------------------------》");
//
//            JSONObject plan = list.get(0);
//
//            String fjConfig = plan.getString("fj_config");
//            String[] split = fjConfig.split(",");
//            String c1 = "";
//            for (int i = 0; i < split.length; i++) {
//                c1 = c1 + "'" + split[i] + "',";
//            }
//            if (c1.length()>0) {
//                s = "select * from kh_config where id in ("+c1.substring(0,c1.length()-1)+")";
//                configs_fj = mysql.query(s);
//            }
//
//
//            String pcsConfig = plan.getString("pcs_config");
//            String[] split2 = pcsConfig.split(",");
//            String c2 = "";
//            for (int i = 0; i < split2.length; i++) {
//                c2 =  c2 + "'" + split2[i] + "',";
//            }
//            if (c2.length()>0) {
//                s = "select * from kh_config where id in ("+c2.substring(0,c2.length()-1)+")";
//                configs_pcs = mysql.query(s);
//            }
//
//
//            String zrqConfig = plan.getString("zrq_config");
//            String[] split3 = zrqConfig.split(",");
//            String c3 = "";
//            for (int i = 0; i < split3.length; i++) {
//                c3 = c3 +"'" + split3[i] + "',";
//            }
//            if (c3.length()>0) {
//                s = "select * from kh_config where id in ("+c3.substring(0,c3.length()-1)+")";
//                configs_zrq = mysql.query(s);
//            }
//
//
//            String del = "delete from kh_res where month = '"+time+"'";
//            logger.warn("删除" + time + "数据：" + del);
//            mysql.update(del);
//
//            String sql ="";
//            //获取所有的责任区
//            sql = "select * from dict where type = 26 and isdelete='1' and is_kh = '1'";
//            zrqs = mysql.query(sql);
//
//            //获取所有的派出所
//            sql = "select * from dict where type = 25 and isdelete='1' and is_kh = '1'";
//            pcss = mysql.query(sql);
//
//            //获取所有的分局
//            sql = "select * from dict where type = 23 and isdelete='1' and is_kh = '1'";
//            fjs = mysql.query(sql);
//
//            //获取当月所有的明细
//            sql = "select * from kh_det where isdelete = '1' and time = '"+time+"'";
//            dets = mysql.query(sql);
//
//
//        }catch (Exception ex) {
//            logger.error(Lib.getTrace(ex));
//        } finally {
//            InfoModelPool.putModel(mysql);
//        }
//
//        try {
//            mysql = InfoModelPool.getModel();
//            String sql ="";
//
//            System.out.println("计算责任区分数！");
//            for (int i = 0; i < zrqs.size(); i++) {
//                JSONObject zrq = zrqs.get(i);
//                String orgId = zrq.getString("id");
//                String fatherId = zrq.getString("father_id");
//
//                //计算责任区总分
//                BigDecimal total = new BigDecimal(0);
//                String value = "";
//                for (int j = 0; j < configs_zrq.size(); j++) {
//                    JSONObject config = configs_zrq.get(j);
//                    String configId = config.getString("id");
//                    String point = config.getString("point");
//                    String full_mark = config.getString("full_mark");
//
//                    JSONObject det = getDetByConfigdAndOrgAndTime(dets, configId, orgId, time);
//                    BigDecimal score = new BigDecimal(0);
//                    if (det != null) {
//                        value = value + "('"+configId+"','"+det.getString("score")+"','"+orgId+"','"+time+"','"+fatherId+"','26'),";
//                        score = new BigDecimal(det.getString("score"));
//                    }else {
//
//                        if (point.equals("2")) {
//                            value = value + "('"+configId+"','"+full_mark+"','"+orgId+"','"+time+"','"+fatherId+"','26'),";
//                            score = new BigDecimal(full_mark);
//                        }else {
//                            value = value + "('"+configId+"','0','"+orgId+"','"+time+"','"+fatherId+"','26'),";
//                            score = new BigDecimal(0);
//                        }
//                    }
//                    total = total.add(score);
//                }
//                //插入所有明细得分
//                sql = "insert kh_res(config_id,score,pg_object,month,father_id,type) values" + value.substring(0,value.length()-1);
////                logger.warn(sql);
//                mysql.update(sql);
//                //总分插入结果表，总分config_id = 1
//                sql = "insert kh_res(config_id,score,pg_object,month,father_id,type) values('1','"+total+"','"+orgId+"','"+time+"','"+fatherId+"','26')";
////                logger.warn(sql);
//                mysql.update(sql);
//            }
//
//
//        } catch (Exception ex) {
//            logger.error(Lib.getTrace(ex));
//        } finally {
//            InfoModelPool.putModel(mysql);
//        }
//
//        //计算派出所得分
//        try {
//            mysql = InfoModelPool.getModel();
//
//            String sql ="";
//            System.out.println("计算派出所分数！");
//            for (int i = 0; i < pcss.size(); i++) {
//                JSONObject pcs = pcss.get(i);
//                String orgId = pcs.getString("id");
//                String fatherId = pcs.getString("father_id");
//
//                //计算责任区平均分
//                for (int j = 0; j < configs_zrq.size(); j++) {
//                    JSONObject config = configs_zrq.get(j);
//                    String configId = config.getString("id");
//
//                    //获取该指标的所有责任区分数并计算平均分
//                    sql = "select * from kh_res where month = '"+time+"' and father_id = '"+orgId+"' and config_id = '"+configId+"'";
//                    List<JSONObject> subList = mysql.query(sql);
//                    BigDecimal sub_total = new BigDecimal(0);
//                    for (int k = 0; k < subList.size(); k++) {
//                        JSONObject object = subList.get(k);
//                        BigDecimal score= new BigDecimal(object.getString("score"));
//                        sub_total = sub_total.add(score);
//                    }
//                    BigDecimal avg = new BigDecimal(0);
//                    if (subList.size()>0) {
//                        avg = sub_total.divide(new BigDecimal(subList.size()), 3, RoundingMode.HALF_UP);
//                    }
//
//                    String sqls = "insert kh_res(config_id,score,pg_object,month,father_id,type) values('"+configId+"','"+avg+"','"+orgId+"','"+time+"','"+fatherId+"','25')";
//                    mysql.update(sqls);
//
//                }
//
//                //计算派出所总分
//                BigDecimal total = new BigDecimal(0);
//                String value = "";
//                for (int j = 0; j < configs_pcs.size(); j++) {
//                    JSONObject config = configs_pcs.get(j);
//                    String configId = config.getString("id");
//                    String point = config.getString("point");
//                    String full_mark = config.getString("full_mark");
//
//                    JSONObject det = getDetByConfigdAndOrgAndTime(dets, configId, orgId, time);
//                    BigDecimal score = new BigDecimal(0);
//                    if (det != null) {
//                        value = value + "('"+configId+"','"+det.getString("score")+"','"+orgId+"','"+time+"','"+fatherId+"','25'),";
//                        score = new BigDecimal(det.getString("score"));
//                    }else {
//
//                        if (point.equals("2")) {
//                            value = value + "('"+configId+"','"+full_mark+"','"+orgId+"','"+time+"','"+fatherId+"','25'),";
//                            score = new BigDecimal(full_mark);
//                        }else {
//                            value = value + "('"+configId+"','0','"+orgId+"','"+time+"','"+fatherId+"','25'),";
//                            score = new BigDecimal(0);
//                        }
//                    }
//                    total = total.add(score);
//                }
//
//                //插入所有明细得分
//                sql = "insert kh_res(config_id,score,pg_object,month,father_id,type) values" + value.substring(0,value.length()-1);
////                logger.warn(sql);
//                mysql.update(sql);
//
//                //根据派出所查询所有责任区的总分
//                sql = "select * from kh_res where month = '"+time+"' and father_id = '"+orgId+"' and config_id = '1'";
////                logger.warn(sql);
//                List<JSONObject> subList = mysql.query(sql);
//
//                //计算派出所总分平均数
//                BigDecimal sub_total = new BigDecimal(0);
//                for (int j = 0; j < subList.size(); j++) {
//                    JSONObject object = subList.get(j);
//                    BigDecimal score= new BigDecimal(object.getString("score"));
//                    sub_total = sub_total.add(score);
//                }
//                BigDecimal avg = new BigDecimal(0);
//                if (subList.size()>0) {
//                    avg = sub_total.divide(new BigDecimal(subList.size()), 3, RoundingMode.HALF_UP);
//                }
//
//                //派出所总分
//                total = total.add(avg);
//
//                //总分插入结果表，总分config_id = 1
//                sql = "insert kh_res(config_id,score,pg_object,month,father_id,type) values('1','"+total+"','"+orgId+"','"+time+"','"+fatherId+"','25')";
////                logger.warn(sql);
//                mysql.update(sql);
//
//            }
//        } catch (Exception ex) {
//            logger.error(Lib.getTrace(ex));
//        } finally {
//            InfoModelPool.putModel(mysql);
//        }
//
//        try {
//            mysql = InfoModelPool.getModel();
//
//            String sql ="";
//            System.out.println("计算分局分数！");
//            for (int i = 0; i < fjs.size(); i++) {
//                JSONObject fj = fjs.get(i);
//                String orgId = fj.getString("id");
//                String fatherId = fj.getString("father_id");
//
//                //计算派出所平均分
//                for (int j = 0; j < configs_pcs.size(); j++) {
//                    JSONObject config = configs_pcs.get(j);
//                    String configId = config.getString("id");
//
//                    //获取该指标的所有责任区分数并计算平均分
//                    sql = "select * from kh_res where month = '"+time+"' and father_id = '"+orgId+"' and config_id = '"+configId+"'";
//                    List<JSONObject> subList = mysql.query(sql);
//                    BigDecimal sub_total = new BigDecimal(0);
//                    for (int k = 0; k < subList.size(); k++) {
//                        JSONObject object = subList.get(k);
//                        BigDecimal score= new BigDecimal(object.getString("score"));
//                        sub_total = sub_total.add(score);
//                    }
//                    BigDecimal avg = new BigDecimal(0);
//                    if (subList.size()>0) {
//                        avg = sub_total.divide(new BigDecimal(subList.size()), 3, RoundingMode.HALF_UP);
//                    }
//                    String sqls = "insert kh_res(config_id,score,pg_object,month,father_id,type) values('"+configId+"','"+avg+"','"+orgId+"','"+time+"','"+fatherId+"','23')";
//                    mysql.update(sqls);
//
//                }
//
//                //计算责任区平均分
//                for (int j = 0; j < configs_zrq.size(); j++) {
//                    JSONObject config = configs_zrq.get(j);
//                    String configId = config.getString("id");
//
//                    //获取该指标的所有责任区分数并计算平均分
//                    sql = "select * from kh_res where month = '"+time+"' and father_id = '"+orgId+"' and config_id = '"+configId+"'";
//                    List<JSONObject> subList = mysql.query(sql);
//                    BigDecimal sub_total = new BigDecimal(0);
//                    for (int k = 0; k < subList.size(); k++) {
//                        JSONObject object = subList.get(k);
//                        BigDecimal score= new BigDecimal(object.getString("score"));
//                        sub_total = sub_total.add(score);
//                    }
//                    BigDecimal avg = new BigDecimal(0);
//                    if (subList.size()>0) {
//                        avg = sub_total.divide(new BigDecimal(subList.size()), 3, RoundingMode.HALF_UP);
//                    }
//                    String sqls = "insert kh_res(config_id,score,pg_object,month,father_id,type) values('"+configId+"','"+avg+"','"+orgId+"','"+time+"','"+fatherId+"','23')";
//                    mysql.update(sqls);
//
//                }
//
//                //计算分局总分
//                BigDecimal total = new BigDecimal(0);
//                String value = "";
//                for (int j = 0; j < configs_fj.size(); j++) {
//                    JSONObject config = configs_fj.get(j);
//                    String configId = config.getString("id");
//                    String point = config.getString("point");
//                    String full_mark = config.getString("full_mark");
//
//                    JSONObject det = getDetByConfigdAndOrgAndTime(dets, configId, orgId, time);
//                    BigDecimal score = new BigDecimal(0);
//                    if (det != null) {
//                        value = value + "('"+configId+"','"+det.getString("score")+"','"+orgId+"','"+time+"','"+fatherId+"','23'),";
//                        score = new BigDecimal(det.getString("score"));
//                    }else {
//
//                        if (point.equals("2")) {
//                            value = value + "('"+configId+"','"+full_mark+"','"+orgId+"','"+time+"','"+fatherId+"','23'),";
//                            score = new BigDecimal(full_mark);
//                        }else {
//                            value = value + "('"+configId+"','0','"+orgId+"','"+time+"','"+fatherId+"','23'),";
//                            score = new BigDecimal(0);
//                        }
//                    }
//                    total = total.add(score);
//                }
//
//                //插入所有明细得分
//                sql = "insert kh_res(config_id,score,pg_object,month,father_id,type) values" + value.substring(0,value.length()-1);
////                logger.warn(sql);
//                mysql.update(sql);
//
//                //根据分局查询所属派出所的总分
//                sql = "select * from kh_res where month = '"+time+"' and father_id = '"+orgId+"' and config_id = '1'";
////                logger.warn(sql);
//                List<JSONObject> subList = mysql.query(sql);
//
//                //计算派出所总分平均数
//                BigDecimal sub_total = new BigDecimal(0);
//                for (int j = 0; j < subList.size(); j++) {
//                    JSONObject object = subList.get(j);
//                    BigDecimal score= new BigDecimal(object.getString("score"));
//                    sub_total = sub_total.add(score);
//                }
//                BigDecimal avg = new BigDecimal(0);
//                if (subList.size()>0) {
//                    avg = sub_total.divide(new BigDecimal(subList.size()), 3, RoundingMode.HALF_UP);
//                }
//
//                //分局总分
//                total = total.add(avg);
//
//                //总分插入结果表，总分config_id = 1
//                sql = "insert kh_res(config_id,score,pg_object,month,father_id,type) values('1','"+total+"','"+orgId+"','"+time+"','"+fatherId+"','23')";
////                logger.warn(sql);
//                mysql.update(sql);
//
//
//            }
//
//        } catch (Exception ex) {
//            logger.error(Lib.getTrace(ex));
//        } finally {
//            InfoModelPool.putModel(mysql);
//            System.out.println("分数统计结束--------------------------》");
//        }
//
//    }
//
//    private void monthRank() {
//        InfoModelHelper mysql = null;
//
//        String month = "";
//        String time = "";
//        List<JSONObject> list = null;
//        HashMap<String, Integer> hiss = new HashMap<>();
//        try {
//            mysql = InfoModelPool.getModel();
//
//            String sql = "select type,name,value from kh_control where type = 'kh_total'";
//            List<JSONObject> control = mysql.query(sql);
//
//            String pid = "";
//
//            for (int i = 0; i < control.size(); i++) {
//                JSONObject c = control.get(i);
//                String name = c.getString("name");
//                String value = c.getString("value");
//                if ("total_plan".equals(name)) {
//                    pid = value;
//                }
//                if ("total_month".equals(name)) {
//                    time = value;
//                }
//            }
//
//            sql = "select * from kh_plan where id = '"+pid+"'";
//            List<JSONObject> p = mysql.query(sql);
//
//            JSONObject plan = p.get(0);
//
//            System.out.println("进入分数排名--------------------------》");
//
//            String con = "";
//            String fjConfig = plan.getString("fj_config");
//            String[] fjs = fjConfig.split(",");
//            for (int j = 0; j < fjs.length; j++) {
//                con = con + "'" + fjs[j] + "',";
//            }
//
//            String pcsConfig = plan.getString("pcs_config");
//            String[] pcss = pcsConfig.split(",");
//            for (int j = 0; j < pcss.length; j++) {
//                con = con + "'" + pcss[j] + "',";
//            }
//
//            String zrqConfig = plan.getString("zrq_config");
//            String[] zrqs = zrqConfig.split(",");
//            for (int j = 0; j < zrqs.length; j++) {
//                con = con + "'" + zrqs[j] + "',";
//            }
//
//
//            String s = "select * from kh_config where id in ("+con.substring(0,con.length()-1)+")";
//            list = mysql.query(s);
//
//            month=time;
//            String lmonth = new SimpleDateFormat("yyyy-MM").format(new SimpleDateFormat("yyyyMM").parse(time)) + "-01";
//            lmonth = RIUtil.GetNextDate(lmonth, -10);
//            lmonth = lmonth.substring(0, lmonth.length() - 3).replace("-", "");
//
//            sql = "select config_id,pg_object,rank from kh_res where month='" + lmonth + "' ";
//            List<JSONObject> l1 = mysql.query(sql);
//
//            if (l1.size() > 0) {
//                for (int i = 0; i < l1.size(); i++) {
//                    JSONObject one = l1.get(i);
//                    String cid = one.getString("config_id");
//                    String pobj = one.getString("pg_object");
//                    int rank = one.getIntValue("rank");
//
//                    hiss.put(cid + "_" + pobj, rank);
//                }
//            }
//            System.out.println("上个月数据=============  " + hiss.size());
//
//        } catch (Exception e) {
//            // TODO: handle exception
//            e.printStackTrace();
//        } finally {
//            InfoModelPool.putModel(mysql);
//        }
//
//        try {
//            if (list.size() > 0) {
//                JSONObject total = new JSONObject();
//                total.put("id","1");
//                total.put("kh_name","总分");
//                list.add(total);
//                for (int i = 0; i < list.size(); i++) {
//
//                    System.out.println("------------------------------>" + i);
//                    JSONObject one = list.get(i);
//
//                    String id = one.getString("id");
//
//                    try {
//                        mysql = InfoModelPool.getModel();
//
//                        // total
//                        // rank
//                        // 23 fj
//
//                        int rank = 0;
//                        int rmark = 0;
//                        double lscore = -1;
//                        String sql = "select id,score,type,father_id,config_id,pg_object,rank from kh_res where config_id='"
//                                + id + "' and type=23 and month='" + month + "' order by score desc";
//                        List<JSONObject> ranks = mysql.query(sql);
//
//                        String s = "select score from kh_res where config_id='" + id + "' and type=23 and month='" + month
//                                + "' group by score order by score desc";
//                        HashMap<Double, Integer> scoreLevel = GetScoreLevel(s, mysql);
//
//
//                        for (int r = 0; r < ranks.size(); r++) {
//                            JSONObject rone = ranks.get(r);
//
//                            double score = rone.getDouble("score");
//
//                            String rid = rone.getString("id");
//                            if (score != lscore) {
//                                rank = rmark;
//                                rank++;
//                                lscore = score;
//                                rmark = rank;
//                            } else {
//                                rmark++;
//                            }
//                            JSONObject rr = new JSONObject();
//                            try {
//                                int level = scoreLevel.get(score);
//                                rr.put("rank_level", level);
//                            } catch (Exception ex) {
//                                rr.put("rank_level", 0);
//                            }
//                            rr.put("rank", rank);
//                            rr.put("rank_fj", -1);
//                            rr.put("rank_pcs", -1);
//                            rr.putAll(GetHB(rone, hiss));
//                            updatesql(rr, "kh_res", mysql, rid);
//
//                        }
//                    } catch (Exception e) {
//                        // TODO: handle exception
//                        e.printStackTrace();
//                    } finally {
//                        InfoModelPool.putModel(mysql);
//                    }
//                    try {
//                        mysql = InfoModelPool.getModel();
//
//                        // 25 pcs
//                        int rank = 0;
//                        int rmark = 0;
//                        double lscore = -1;
//                        String sql = "select id,score,type,father_id,config_id,pg_object,rank from kh_res where config_id='"
//                                + id + "' and type=25 and month='" + month + "' order by score desc";
//                        List<JSONObject> ranks = mysql.query(sql);
//
//                        String s = "select score from kh_res where config_id='" + id + "' and type=25 and month='" + month
//                                + "' group by score order by score desc";
//                        HashMap<Double, Integer> scoreLevel = GetScoreLevel(s, mysql);
//
//                        for (int r = 0; r < ranks.size(); r++) {
//                            JSONObject rone = ranks.get(r);
//
//                            double score = rone.getDouble("score");
//                            String rid = rone.getString("id");
//                            if (score != lscore) {
//                                rank = rmark;
//                                rank++;
//                                lscore = score;
//                                rmark = rank;
//                            } else {
//                                rmark++;
//                            }
//                            JSONObject rr = new JSONObject();
//                            rr.put("rank", rank);
//                            try {
//                                int level = scoreLevel.get(score);
//                                rr.put("rank_level", level);
//                            } catch (Exception ex) {
//                                rr.put("rank_level", 0);
//                            }
//                            rr.put("rank_pcs", -1);
//                            rr.putAll(GetHB(rone, hiss));
//                            updatesql(rr, "kh_res", mysql, rid);
//                        }
//                    } catch (Exception e) {
//                        // TODO: handle exception
//                        e.printStackTrace();
//                    } finally {
//                        InfoModelPool.putModel(mysql);
//                    }
//                    try {
//                        mysql = InfoModelPool.getModel();
//
//                        // 25 pcs ->fenju
//
//                        String sql = "select id from dict where type=23 and isdelete='1' and is_kh = '1'";
//                        List<JSONObject> fjs = mysql.query(sql);
//                        for (int f = 0; f < fjs.size(); f++) {
//                            JSONObject fone = fjs.get(f);
//                            String fid = fone.getString("id");
//
//                            int rank = 0;
//                            int rmark = 0;
//                            double lscore = -1;
//                            sql = "select id,score,type,father_id,config_id,pg_object,rank from kh_res where config_id='"
//                                    + id + "' and type=25 and father_id='" + fid + "' and month='" + month
//                                    + "' order by score desc";
//                            List<JSONObject> ranks = mysql.query(sql);
//
//                            for (int r = 0; r < ranks.size(); r++) {
//                                JSONObject rone = ranks.get(r);
//
//                                double score = rone.getDouble("score");
//                                String rid = rone.getString("id");
//                                if (score != lscore) {
//                                    rank = rmark;
//                                    rank++;
//                                    lscore = score;
//                                    rmark = rank;
//                                } else {
//                                    rmark++;
//                                }
//                                // System.out.println(score + "**" + lscore + "**" + rank);
//                                JSONObject rr = new JSONObject();
//                                rr.put("rank_fj", rank);
//
//                                rr.putAll(GetHB(rone, hiss));
//                                updatesql(rr, "kh_res", mysql, rid);
//                            }
//
//                        }
//                    } catch (Exception e) {
//                        // TODO: handle exception
//                        e.printStackTrace();
//                    } finally {
//                        InfoModelPool.putModel(mysql);
//                    }
//                    try {
//                        mysql = InfoModelPool.getModel();
//
//                        // 26zrq
//                        int rank = 0;
//                        int rmark = 0;
//                        double lscore = -1;
//                        String sql = "select id,score,type,father_id,config_id,pg_object,rank from kh_res where config_id='"
//                                + id + "' and type=26 and month='" + month + "' order by score desc";
//                        List<JSONObject> ranks = mysql.query(sql);
//                        String s = "select score from kh_res where config_id='" + id + "' and type=26 and month='" + month
//                                + "' group by score order by score desc";
//                        HashMap<Double, Integer> scoreLevel = GetScoreLevel(s, mysql);
//                        for (int r = 0; r < ranks.size(); r++) {
//                            JSONObject rone = ranks.get(r);
//
//                            double score = rone.getDouble("score");
//                            String rid = rone.getString("id");
//                            if (score != lscore) {
//                                rank = rmark;
//                                rank++;
//                                lscore = score;
//                                rmark = rank;
//                            } else {
//                                rmark++;
//                            }
//                            JSONObject rr = new JSONObject();
//                            rr.put("rank", rank);
//
//                            try {
//                                int level = scoreLevel.get(score);
//                                rr.put("rank_level", level);
//                            } catch (Exception ex) {
//                                rr.put("rank_level", 0);
//                            }
//                            rr.putAll(GetHB(rone, hiss));
//                            updatesql(rr, "kh_res", mysql, rid);
//                        }
//                    } catch (Exception e) {
//                        // TODO: handle exception
//                        e.printStackTrace();
//                    } finally {
//                        InfoModelPool.putModel(mysql);
//                    }
//                    try {
//                        mysql = InfoModelPool.getModel();
//
//                        // 26 zrq ->fenju
//
//                        String sql = "select id from dict where type=23 and isdelete=1 and is_kh = '1'";
//                        List<JSONObject> fjs = mysql.query(sql);
//                        for (int f = 0; f < fjs.size(); f++) {
//                            JSONObject fone = fjs.get(f);
//                            String fid = fone.getString("id").substring(0, 6);
//
//                            int rank = 0;
//                            int rmark = 0;
//                            double lscore = -1;
//                            sql = "select id,score,type,father_id,config_id,pg_object,rank from kh_res where config_id='"
//                                    + id + "' and type=26 and father_id like '%" + fid + "%' and month='" + month
//                                    + "' order by score desc";
//                            List<JSONObject> ranks = mysql.query(sql);
//
//                            for (int r = 0; r < ranks.size(); r++) {
//                                JSONObject rone = ranks.get(r);
//
//                                double score = rone.getDouble("score");
//                                String rid = rone.getString("id");
//                                if (score != lscore) {
//                                    rank = rmark;
//                                    rank++;
//                                    lscore = score;
//                                    rmark = rank;
//                                } else {
//                                    rmark++;
//                                }
//                                // System.out.println(score + "**" + lscore + "**" + rank);
//                                JSONObject rr = new JSONObject();
//                                rr.put("rank_fj", rank);
//                                rr.putAll(GetHB(rone, hiss));
//                                updatesql(rr, "kh_res", mysql, rid);
//                            }
//
//                        }
//                    } catch (Exception e) {
//                        // TODO: handle exception
//                        e.printStackTrace();
//                    } finally {
//                        InfoModelPool.putModel(mysql);
//                    }
//                    try {
//                        mysql = InfoModelPool.getModel();
//
//                        // 26 zrq ->pcs
//
//                        String sql = "select id from dict where type=25 and isdelete=1";
//                        List<JSONObject> fjs = mysql.query(sql);
//                        for (int f = 0; f < fjs.size(); f++) {
//                            JSONObject fone = fjs.get(f);
//                            String fid = fone.getString("id");
//
//                            int rank = 0;
//                            int rmark = 0;
//                            double lscore = -1;
//                            sql = "select id,score,type,father_id,config_id,pg_object,rank from kh_res where config_id='"
//                                    + id + "' and type=26 and father_id ='" + fid + "' and month='" + month
//                                    + "' order by score desc";
//                            List<JSONObject> ranks = mysql.query(sql);
//
//                            for (int r = 0; r < ranks.size(); r++) {
//                                JSONObject rone = ranks.get(r);
//
//                                double score = rone.getDouble("score");
//                                String rid = rone.getString("id");
//                                if (score != lscore) {
//                                    rank = rmark;
//                                    rank++;
//                                    lscore = score;
//                                    rmark = rank;
//                                } else {
//                                    rmark++;
//                                }
//                                // System.out.println(score + "**" + lscore + "**" + rank);
//                                JSONObject rr = new JSONObject();
//                                rr.put("rank_pcs", rank);
//                                rr.putAll(GetHB(rone, hiss));
//                                updatesql(rr, "kh_res", mysql, rid);
//                            }
//
//                        }
//
//                    } catch (Exception e) {
//                        // TODO: handle exception
//                        e.printStackTrace();
//                    } finally {
//                        InfoModelPool.putModel(mysql);
//                    }
//
//                }
//
//
//            }
//        }catch (Exception e) {
//            // TODO: handle exception
//            e.printStackTrace();
//        } finally {
//            InfoModelPool.putModel(mysql);
//            System.out.println("分数排名结束--------------------------》");
//        }
//
//    }
//
//    private void yearRank() {
//        InfoModelHelper mysql = null;
//        String time = "";
//        String year = "";
//        int msize = 0;
//
//        List<JSONObject> list = new ArrayList<>();
//        HashMap<String, Integer> hiss = new HashMap<>();
//
//
//        try {
//            mysql = InfoModelPool.getModel();
//            String sql = "select type,name,value from kh_control where type = 'kh_total'";
//            List<JSONObject> control = mysql.query(sql);
//
//            String pid = "";
//
//            for (int i = 0; i < control.size(); i++) {
//                JSONObject c = control.get(i);
//                String name = c.getString("name");
//                String value = c.getString("value");
//                if ("total_plan".equals(name)) {
//                    pid = value;
//                }
//                if ("total_month".equals(name)) {
//                    time = value;
//                }
//            }
//
//            sql = "select * from kh_plan where id = '"+pid+"'";
//            List<JSONObject> p = mysql.query(sql);
//
//            JSONObject plan = p.get(0);
//
//            System.out.println("进入年度排名--------------------------》");
//
//            String con = "";
//            String fjConfig = plan.getString("fj_config");
//            String[] fjs = fjConfig.split(",");
//            for (int j = 0; j < fjs.length; j++) {
//                con = con + "'" + fjs[j] + "',";
//            }
//
//            String pcsConfig = plan.getString("pcs_config");
//            String[] pcss = pcsConfig.split(",");
//            for (int j = 0; j < pcss.length; j++) {
//                con = con + "'" + pcss[j] + "',";
//            }
//
//            String zrqConfig = plan.getString("zrq_config");
//            String[] zrqs = zrqConfig.split(",");
//            for (int j = 0; j < zrqs.length; j++) {
//                con = con + "'" + zrqs[j] + "',";
//            }
//
//
//            String s = "select * from kh_config where id in ("+con.substring(0,con.length()-1)+")";
//            list = mysql.query(s);
//
//            year = new SimpleDateFormat("yyyy").format(new SimpleDateFormat("yyyyMM").parse(time));
//
//            sql = "delete from kh_res_t where year='" + year + "'";
//            mysql.update(sql);
//
//            sql = "select `month` from kh_res where month like '%" + year + "%' GROUP BY `month`";
//            List<JSONObject> ll = mysql.query(sql);
//            msize = ll.size();
//
//            int y = Integer.parseInt(year) - 1;
//            sql = "select config_id,pg_object,rank from kh_res_t where year='" + y + "' ";
//            List<JSONObject> l1 = mysql.query(sql);
//            if (l1.size() > 0) {
//                for (int i = 0; i < l1.size(); i++) {
//
//
//
//                    JSONObject one = l1.get(i);
//                    String cid = one.getString("config_id");
//                    String pobj = one.getString("pg_object");
//                    int rank = one.getIntValue("rank");
//
//                    hiss.put(cid + "_" + pobj, rank);
//                }
//            }
//
//        } catch (Exception e) {
//            // TODO: handle exception
//            e.printStackTrace();
//        } finally {
//            InfoModelPool.putModel(mysql);
//        }
//
//        try {
//            if (list.size() > 0) {
//                JSONObject total = new JSONObject();
//                total.put("id","1");
//                total.put("kh_name","总分");
//                list.add(total);
//                for (int i = 0; i < list.size(); i++) {
//
//                    System.out.println("------------------------------>" + i);
//
//                    JSONObject one = list.get(i);
//
//                    String id = one.getString("id");
//
//                    try {
//                        mysql = InfoModelPool.getModel();
//
//                        String sql = "select  sum(score)/" + msize + " as score,config_id,pg_object,a.type,a.father_id "
//                                + "from kh_res a left join dict b on a.pg_object=b.id where config_id='" + id
//                                + "' and month like '%" + year + "%' group by pg_object";
//
////                        System.out.println(sql);
//                        // 删除本年已有数据
//
//                        List<JSONObject> scores = mysql.query(sql);
//                        if (scores.size() > 0) {
//                            for (int s = 0; s < scores.size(); s++) {
//                                JSONObject score = scores.get(s);
//                                score.put("year", year);
//                                // hb
//                                score = GetHB2(score, hiss);
//
//                                JsonInser(score, "kh_res_t", mysql);
//
//                            }
//                        }
//
//                        // total
//                        // rank
//                        // 23 fj
//
////                        System.out.println("23");
//                        int rank = 0;
//                        int rmark = 0;
//                        double lscore = -1;
//                        sql = "select id,score,type,father_id from kh_res_t where config_id='" + id
//                                + "' and type=23 and year='" + year + "' order by score desc";
//                        List<JSONObject> ranks = mysql.query(sql);
//                        String s = "select score from kh_res_t where config_id='" + id + "' and type=23 and year='" + year
//                                + "' group by score desc";
//                        HashMap<Double, Integer> scoreLevel = GetScoreLevel(s, mysql);
//
//                        for (int r = 0; r < ranks.size(); r++) {
//                            JSONObject rone = ranks.get(r);
//
//                            double score = rone.getDouble("score");
//                            // System.out.println(score + "**" + lscore);
//                            String rid = rone.getString("id");
//                            if (score != lscore) {
//                                rank = rmark;
//                                rank++;
//                                lscore = score;
//                                rmark = rank;
//                            } else {
//                                rmark++;
//                            }
//                            JSONObject rr = new JSONObject();
//                            rr.put("rank", rank);
//                            rr.put("rank_fj", -1);
//                            rr.put("rank_pcs", -1);
//                            try {
//                                int level = scoreLevel.get(score);
//                                rr.put("rank_level", level);
//                            } catch (Exception ex) {
//                                rr.put("rank_level", 0);
//                            }
//                            rr.putAll(GetHB2(rone, hiss));
//                            updatesql(rr, "kh_res_t", mysql, rid);
//
//                        }
//
//                        // 25 pcs
////                        System.out.println("25-all");
//                        rank = 0;
//                        rmark = 0;
//                        lscore = -1;
//                        sql = "select id,score,type,father_id ,config_id,pg_object from kh_res_t where config_id='" + id
//                                + "' and type=25 and year='" + year + "' order by score desc";
//                        ranks = mysql.query(sql);
//                        s = "select score from kh_res_t where config_id='" + id + "' and type=25 and year='" + year
//                                + "' group by score desc";
//                        scoreLevel = GetScoreLevel(s, mysql);
//
//                        for (int r = 0; r < ranks.size(); r++) {
//                            JSONObject rone = ranks.get(r);
//
//                            double score = rone.getDouble("score");
//                            String rid = rone.getString("id");
//                            if (score != lscore) {
//                                rank = rmark;
//                                rank++;
//                                lscore = score;
//                                rmark = rank;
//                            } else {
//                                rmark++;
//                            }
//                            // System.out.println(score + "**" + lscore + "**" + rank);
//                            JSONObject rr = new JSONObject();
//                            rr.put("rank", rank);
//
//                            rr.put("rank_pcs", -1);
//                            try {
//                                int level = scoreLevel.get(score);
//                                rr.put("rank_level", level);
//                            } catch (Exception ex) {
//                                rr.put("rank_level", 0);
//                            }
//                            rr.putAll(GetHB2(rone, hiss));
//                            updatesql(rr, "kh_res_t", mysql, rid);
//
//                        }
//
//                        // 25 pcs ->fenju
//
//                        sql = "select id from dict where type=23 and isdelete=1 and is_kh = '1'";
//                        List<JSONObject> fjs = mysql.query(sql);
//                        for (int f = 0; f < fjs.size(); f++) {
//                            JSONObject fone = fjs.get(f);
//                            String fid = fone.getString("id");
//
////                            System.out.println("25-fj-" + fid);
//                            rank = 0;
//                            rmark = 0;
//                            lscore = -1;
//                            sql = "select id,score,type,father_id,rank from kh_res_t where config_id='" + id
//                                    + "' and type=25 and father_id='" + fid + "' and year='" + year
//                                    + "' order by score desc";
////                            System.out.println(sql);
//                            ranks = mysql.query(sql);
//
//                            for (int r = 0; r < ranks.size(); r++) {
//                                JSONObject rone = ranks.get(r);
//
//                                double score = rone.getDouble("score");
//                                String rid = rone.getString("id");
//                                if (score != lscore) {
//                                    rank = rmark;
//                                    rank++;
//                                    lscore = score;
//                                    rmark = rank;
//                                } else {
//                                    rmark++;
//                                }
//
//                                JSONObject rr = new JSONObject();
//                                rr.put("rank_fj", rank);
//                                rr.putAll(GetHB2(rone, hiss));
//                                updatesql(rr, "kh_res_t", mysql, rid);
//
//                            }
//
//                        }
//
//                        // 26zrq
////                        System.out.println("26-all");
//                        rank = 0;
//                        rmark = 0;
//                        lscore = -1;
//                        sql = "select id,score,type,father_id,config_id,pg_object from kh_res_t where config_id='" + id
//                                + "' and type=26 and year='" + year + "' order by score desc";
//                        ranks = mysql.query(sql);
//                        s = "select score from kh_res_t where config_id='" + id + "' and type=25 and year='" + year
//                                + "' group by score desc";
//                        scoreLevel = GetScoreLevel(s, mysql);
//                        for (int r = 0; r < ranks.size(); r++) {
//                            JSONObject rone = ranks.get(r);
//
//                            double score = rone.getDouble("score");
//                            String rid = rone.getString("id");
//                            if (score != lscore) {
//                                rank = rmark;
//                                rank++;
//                                lscore = score;
//                                rmark = rank;
//                            } else {
//                                rmark++;
//                            }
//
//                            JSONObject rr = new JSONObject();
//                            rr.put("rank", rank);
//                            try {
//                                int level = scoreLevel.get(score);
//                                rr.put("rank_level", level);
//                            } catch (Exception ex) {
//                                rr.put("rank_level", 0);
//                            }
//                            rr.putAll(GetHB2(rone, hiss));
//                            updatesql(rr, "kh_res_t", mysql, rid);
//
//                        }
//
//                        // 26 zrq ->fenju
//
//                        sql = "select id from dict where type=23 and isdelete=1 and is_kh = '1'";
//                        fjs = mysql.query(sql);
//                        for (int f = 0; f < fjs.size(); f++) {
//                            JSONObject fone = fjs.get(f);
//                            String fid = fone.getString("id").substring(0, 6);
//
////                            System.out.println("26-fj-" + fid);
//                            rank = 0;
//                            rmark = 0;
//                            lscore = -1;
//                            sql = "select id,score,type,father_id,config_id,pg_object from kh_res_t where config_id='" + id
//                                    + "' and type=26 and father_id like '%" + fid + "%' and year='" + year
//                                    + "' order by score desc";
////                            System.out.println(sql);
//                            ranks = mysql.query(sql);
//
//                            for (int r = 0; r < ranks.size(); r++) {
//                                JSONObject rone = ranks.get(r);
//
//                                double score = rone.getDouble("score");
//                                String rid = rone.getString("id");
//                                if (score != lscore) {
//                                    rank = rmark;
//                                    rank++;
//                                    lscore = score;
//                                    rmark = rank;
//                                } else {
//                                    rmark++;
//                                }
//
//                                JSONObject rr = new JSONObject();
//                                rr.put("rank_fj", rank);
//                                rr.putAll(GetHB2(rone, hiss));
//                                updatesql(rr, "kh_res_t", mysql, rid);
//
//                            }
//
//                        }
//                        // 26 zrq ->pcs
//
//                        sql = "select id from dict where type=25 and isdelete=1 and is_kh = '1'";
//                        fjs = mysql.query(sql);
//                        for (int f = 0; f < fjs.size(); f++) {
//                            JSONObject fone = fjs.get(f);
//                            String fid = fone.getString("id");
//
////                            System.out.println("26-pcs-" + fid);
//                            rank = 0;
//                            rmark = 0;
//                            lscore = -1;
//                            sql = "select id,score,type,father_id from kh_res_t where config_id='" + id
//                                    + "' and type=26 and father_id ='" + fid + "' and year='" + year
//                                    + "' order by score desc";
////                            System.out.println(sql);
//                            ranks = mysql.query(sql);
//
//                            for (int r = 0; r < ranks.size(); r++) {
//                                JSONObject rone = ranks.get(r);
//
//                                double score = rone.getDouble("score");
//                                String rid = rone.getString("id");
//                                if (score != lscore) {
//                                    rank = rmark;
//                                    rank++;
//                                    lscore = score;
//                                    rmark = rank;
//                                } else {
//                                    rmark++;
//                                }
//
//                                JSONObject rr = new JSONObject();
//                                rr.put("rank_pcs", rank);
//                                rr.putAll(GetHB2(rone, hiss));
//                                updatesql(rr, "kh_res_t", mysql, rid);
//
//                            }
//
//                        }
//
//                    } catch (Exception e) {
//                        // TODO: handle exception
//                        e.printStackTrace();
//                    } finally {
//                        InfoModelPool.putModel(mysql);
//                    }
//
//                }
//
//
//            }
//        }catch (Exception e) {
//            // TODO: handle exception
//            e.printStackTrace();
//        } finally {
//            InfoModelPool.putModel(mysql);
//            System.out.println("年度排名结束--------------------------》");
//        }
//
//    }
//
//    private static JSONObject GetHB2(JSONObject score, HashMap<String, Integer> hiss) {
//        String cobj = score.getString("config_id") + "_" + score.getString("pg_object");
//        JSONObject ret = score;
//        ret.put("hb", 0);
//        if (hiss.containsKey(cobj)) {
//            int hsRank = hiss.get(cobj);
//            int rank = score.getIntValue("rank");
//            ret.put("hb", hsRank - rank);
//
//        }
//        return ret;
//    }
//
//    public static void JsonInser(JSONObject det, String table, InfoModelHelper mysql) {
//        String keys = "";
//        String values = "";
//
//        try {
//            for (Map.Entry<String, Object> one : det.entrySet()) {
//                keys = keys + "`" + one.getKey() + "`,";
//                values = values + "'" + one.getValue().toString() + "',";
//            }
//            String sql = "insert into " + table + " (" + keys.substring(0, keys.length() - 1) + ") values("
//                    + values.substring(0, values.length() - 1) + ")";
//            // logger.warn(sql);
//            mysql.update(sql);
//        } catch (Exception e) {
//            logger.warn(Lib.getTrace(e));
//        }
//
//    }
//
//    public static HashMap<Double, Integer> GetScoreLevel(String s, InfoModelHelper mysql) throws Exception {
//        HashMap<Double, Integer> scoreLevel = new HashMap<>();
//        List<JSONObject> ss = mysql.query(s);
//        if (ss.size() == 1) {
//            double sc = ss.get(0).getDoubleValue("score");
//            scoreLevel.put(sc, 0);
//        } else if (ss.size() == 2) {
//            double sc = ss.get(0).getDoubleValue("score");
//            scoreLevel.put(sc, 1);
//            sc = ss.get(1).getDoubleValue("score");
//            scoreLevel.put(sc, -1);
//        } else {
//            int c = ss.size() / 3;
//
//            for (int a = 0; a < c; a++) {
//                double sc = ss.get(a).getDoubleValue("score");
//                scoreLevel.put(sc, 1);
//            }
//
//            for (int a = ss.size() - 1; a >= ss.size() - c; a--) {
//                double sc = ss.get(a).getDoubleValue("score");
//                scoreLevel.put(sc, -1);
//            }
//
//        }
//        return scoreLevel;
//    }
//
//    public static void updatesql(JSONObject det, String table, InfoModelHelper mysql, String id) {
//        String sql = "";
//        try {
//
//            for (Map.Entry<String, Object> one : det.entrySet()) {
//                sql = sql + one.getKey() + "='" + one.getValue() + "',";
//            }
//
//            String sqls = "update " + table + " set " + sql.substring(0, sql.length() - 1) + "  where id='" + id + "'";
//            mysql.update(sqls);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//    }
//
//    public static JSONObject GetHB(JSONObject score, HashMap<String, Integer> hiss) {
//        String cobj = score.getString("config_id") + "_" + score.getString("pg_object");
//        JSONObject ret = new JSONObject();
//        ret.put("hb", 0);
//        if (hiss.containsKey(cobj)) {
//            int hsRank = hiss.get(cobj);
//            int rank = score.getIntValue("rank");
//            ret.put("hb", hsRank - rank);
//
//            if (hsRank != rank && score.getString("pg_object").endsWith("000000")) {
//                System.out.println(hsRank + "---" + rank);
//            }
//        }
//
//        return ret;
//    }
//
//    private static JSONObject getDetByConfigdAndOrgAndTime(List<JSONObject> arrayList, String id, String org,
//                                                           String time) {
//
//        for (int i = 0; i < arrayList.size(); i++) {
//            JSONObject object = arrayList.get(i);
//            String config_id = object.getString("config_id");
//            String org_id = object.getString("org_id");
//            String t = object.getString("time");
//            if (config_id.equals(id) && org_id.equals(org) && t.equals(time)) {
//                return object;
//            }
//        }
//        return null;
//    }
//}
