package HL.TNOA.Httpd.Utils;

import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.TNOAConf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;

public class MakeWaters {
    private static Logger logger = LoggerFactory.getLogger(MakeWaters.class);

    public static String makeWaterimg(int ww, int wh, String date, String time, String local, String file_path,
                                      String unit_name) throws Exception {
        logger.warn(TNOAConf.get("file", "img_path") + "namew/patrol_water.png");
        File file = new File(TNOAConf.get("file", "img_path") + "namew/patrol_water.png");
        BufferedImage image;
        try {
            image = ImageIO.read(file);

            // 创建画笔
            Graphics2D pen = image.createGraphics();
            // 设置画笔颜色为白色

            pen.setColor(Color.RED);
            // 设置画笔字体样式为微软雅黑，斜体，文字大小为20px
            pen.setFont(new Font("宋体", Font.BOLD, 25));
            // 写上水印文字和坐标
            pen.drawString(date, 110, 170);

            // pen.setColor(new Color(59, 101, 159));
            // 设置画笔字体样式为微软雅黑，斜体，文字大小为20px
            pen.setFont(new Font("宋体", Font.BOLD, 20));
            // 写上水印文字和坐标
            pen.drawString(time, 275, 170);
            pen.drawString(local, 110, 125);
            logger.warn(local);
            pen.setFont(new Font("宋体", Font.BOLD, 30));
            pen.setColor(Color.BLUE);
            // 写上水印文字和坐标
            if (unit_name.length() == 2) {
                pen.drawString(unit_name, 160, 243);
            } else if (unit_name.length() == 3) {
                pen.drawString(unit_name, 130, 243);
            } else {
                pen.drawString(unit_name, 95, 243);
            }
            logger.warn(unit_name);
            // 创建新图片文件
            String t = String.valueOf(System.currentTimeMillis());
            file = new File(file_path + "water_" + t + ".png");
            // 将处理好的图片数据写入到新图片文件中
            FileOutputStream fos = new FileOutputStream(file);
            image = ImageUtils.resizeImageOne(image, ww, wh);
            ImageIO.write(image, "png", fos);
            return "water_" + t;
        } catch (IOException e) {
            logger.error(Lib.getTrace(e));
        }
        return "";
    }


    public static void markImageByIcon(String iconPath, String srcImgPath, String targerPath, Integer degree, int y) {
        OutputStream os = null;
        try {

            Image srcImg = ImageIO.read(new File(srcImgPath));

            BufferedImage buffImg = new BufferedImage(srcImg.getWidth(null), srcImg.getHeight(null),
                    BufferedImage.TYPE_INT_RGB);

            // 1、得到画笔对�?
            Graphics2D g = buffImg.createGraphics();

            // 2、设置对线段的锯齿状边缘处理
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);

            g.drawImage(srcImg.getScaledInstance(srcImg.getWidth(null), srcImg.getHeight(null), Image.SCALE_SMOOTH),
                    0, 0, null);

            // 4、水印图片的路径 水印图片�?般为gif或�?�png的，这样可设置�?�明�?
            ImageIcon imgIcon = new ImageIcon(iconPath + ".png");

            // 5、得到Image对象�?
            Image img = imgIcon.getImage();

            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, 1));

            // 6、水印图片的位置

            g.drawImage(img, 5, y, null);
            System.out.print(5 + " " + y);

            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
            // 7、释放资�?
            g.dispose();

            // 8、生成图�?
            os = new FileOutputStream(targerPath);
            ImageIO.write(buffImg, "JPG", os);

            System.out.println("图片完成添加水印图片");

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != os) os.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

}
