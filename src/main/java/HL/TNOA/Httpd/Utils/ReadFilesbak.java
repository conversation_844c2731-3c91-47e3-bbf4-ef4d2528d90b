package HL.TNOA.Httpd.Utils;

import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.TNOAConf;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.ooxml.POIXMLDocument;
import org.apache.poi.ooxml.extractor.POIXMLTextExtractor;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.ArrayList;
import java.util.List;


public class ReadFilesbak {
    private static Logger logger = LoggerFactory.getLogger(ReadFilesbak.class);

    public static void main(String[] args) {
        String back = getContent("14535");
        //  System.out.println(back);
    }

    public static String getContent(String file_id) {
        InfoModelHelper mysql = null;
        String back = "";
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select * from upload where id='" + file_id + "'";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                JSONObject one = list.get(0);
                String file_name = TNOAConf.get("file", "img_path") + one.getString("file_path") + one.getString(
                        "file_name");
                //file_name = "store_54b5fa062bc5c9be2145065dbfcaa6f18acb9092f6d41232236f210cb6f9cda2_0907081419.doc";
                if (file_name.endsWith("xls") || file_name.endsWith("xlsx")) {
                    back = ReadExcel(file_name);
                    // System.out.println(back);
                } else if (file_name.endsWith("doc") || file_name.endsWith("docx")) {
                    back = ReadDoc(file_name);
                    //System.out.println(back);
                } else if (file_name.endsWith("txt")) {
                    back = ReadTxt(file_name);
                }

            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static String ReadTxt(String file_name) throws IOException {
        BufferedReader br = null;
        String pa = "";
        try {
            br = new BufferedReader(new FileReader(new File(file_name)));
            String line = "";
            while ((line = br.readLine()) != null) {

                pa = pa + line.trim().replaceAll("[<>()\\s'\"\\/]", "");

            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            if (br != null) {
                br.close();
            }
        }
        return pa;
    }

    private static String ReadDoc(String filePath) {
        // filePath="store_54b5fa062bc5c9be2145065dbfcaa6f18acb9092f6d41232236f210cb6f9cda2_0907081419.doc";
        List<String> linList = new ArrayList<String>();
        String buffer = "";
        String back = "";
        try {
            if (filePath.endsWith(".doc")) {
                InputStream is = new FileInputStream(new File(filePath));
                WordExtractor ex = new WordExtractor(is);
                buffer = ex.getText();
                ex.close();

                if (buffer.length() > 0) {
                    //使用回车换行符分割字符串
                    String[] arry = buffer.split("\\r\\n");
                    for (String string : arry) {
                        back = back + string.trim().replaceAll("[<>()\\s'\"\\/]", "");
                    }
                }
            } else if (filePath.endsWith(".docx")) {
                OPCPackage opcPackage = POIXMLDocument.openPackage(filePath);
                POIXMLTextExtractor extractor = new XWPFWordExtractor(opcPackage);
                buffer = extractor.getText();
                extractor.close();

                if (buffer.length() > 0) {
                    //使用换行符分割字符串
                    String[] arry = buffer.split("\\n");
                    for (String string : arry) {
                        back = back + string.trim().replaceAll("[<>()\\s'\"\\/]", "");
                    }
                }
            } else {
                return "";
            }

            return back;
        } catch (Exception e) {
            System.out.print("error---->" + filePath);
            e.printStackTrace();
            return "";
        }


    }

    private static String ReadExcel(String excelPath) {
        String con = "";

        try {
            //String encoding = "GBK";
            File excel = new File(excelPath);
            if (excel.isFile() && excel.exists()) {   //判断文件是否存在

                String[] split = excel.getName().split("\\.");  //.是特殊字符，需要转义！！！！！
                Workbook wb;
                //根据文件后缀（xls/xlsx）进行判断
                if ("xls".equals(split[1])) {
                    FileInputStream fis = new FileInputStream(excel);   //文件流对象
                    wb = new HSSFWorkbook(fis);
                } else if ("xlsx".equals(split[1])) {
                    wb = new XSSFWorkbook(excel);
                } else {
                    System.out.println("文件类型错误!");
                    return "";
                }

                //开始解析
                int sheetsCount = wb.getNumberOfSheets();
                System.out.println(sheetsCount);
                for (int i = 0; i < sheetsCount; i++) {
                    Sheet sheet = wb.getSheetAt(i);     //读取sheet 0

                    int firstRowIndex = sheet.getFirstRowNum() + 1;   //第一行是列名，所以不读
                    int lastRowIndex = sheet.getLastRowNum();
                    //System.out.println("firstRowIndex: " + firstRowIndex);
                    // System.out.println("lastRowIndex: " + lastRowIndex);

                    for (int rIndex = firstRowIndex; rIndex <= lastRowIndex; rIndex++) {   //遍历行
                        //System.out.println("rIndex: " + rIndex);
                        Row row = sheet.getRow(rIndex);
                        if (row != null) {
                            int firstCellIndex = row.getFirstCellNum();
                            int lastCellIndex = row.getLastCellNum();
                            for (int cIndex = firstCellIndex; cIndex < lastCellIndex; cIndex++) {   //遍历列
                                Cell cell = row.getCell(cIndex);
                                if (cell != null) {
                                    if (cell.toString().trim().length() > 0) {
                                        System.out.println(cell.toString());
                                        con = con + cell.toString().trim().replaceAll("[<>()\\s'\"\\/]", "");
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                System.out.println("找不到指定的文件");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return con;
    }
}
