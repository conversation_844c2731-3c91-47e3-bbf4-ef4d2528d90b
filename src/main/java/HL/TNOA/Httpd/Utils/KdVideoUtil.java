package HL.TNOA.Httpd.Utils;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;


/**
 * 科达工具类
 */
@Slf4j
public class KdVideoUtil {

    public static void main(String[] args) {
        // 测试
        String viidVideo = getViidVideo("J3204125425071780002", "2025-07-17 03:29:03", "2025-07-17 05:29:03");
        System.out.println(viidVideo);
    }


    private static final Cache<String, String> TOKEN_CACHE = CacheUtil.newTimedCache(86000);

    private static final String TOKEN_KEY = "jwt_token";

    private static final String BASE_URL = "http://50.56.94.181/ty-cvf-openapi/openapi/v1.1/";

    private static final String CLIENT_ID = "czadmin";

    private static final String CLIENT_SECRET = "czga@123";

    public static String getToken() {
        String cacheToken = TOKEN_CACHE.get(TOKEN_KEY);
        if (cacheToken != null) {
            log.info("科达获取缓存token");
            return cacheToken;
        }
        String token = requestNewToken();
        TOKEN_CACHE.put(TOKEN_KEY, token, 86000);
        return token;
    }


    private static String requestNewToken() {
        JSONObject params = new JSONObject();
        params.put("clientId", CLIENT_ID);
        params.put("clientSecrict", CLIENT_SECRET);
        HttpResponse execute = HttpUtil.createPost(BASE_URL + "oauth/token")
                .body(params.toString())
                .timeout(1000 * 10)
                .execute();
        String body = execute.body();
        log.info("科达执法记录仪token:{}", body);

        JSONObject response = JSONObject.parseObject(body);
        String token = response.getJSONObject("result").getString("jwt_token");
        execute.close();
        return token;
    }


    /**
     * 查询对应警情视频
     * @param jjbh 接警编号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 响应
     */
    public static String getViidVideo(String jjbh,
                                      String startTime,
                                      String endTime) {
        String token = getToken();
        HttpResponse execute = HttpUtil.createGet(BASE_URL + "viid/video")
                .header("Authorization", "Bearer " + token)
                .form("pageNo", 1)
                .form("pageSize", 100)
                .form("startTime", startTime)
                .form("endTime", endTime)
                .form("caseEventIds", jjbh)
                .timeout(1000 * 30)
                .execute();
        String body = execute.body();
        log.debug("科达执法记录仪查询视频：{}", body);
        execute.close();
        return body;
    }


}
