package HL.TNOA.Httpd.javajwt;


import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.Lib;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

@ControllerAdvice
public class GlobalExceptionHandler {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    @ResponseBody
    @ExceptionHandler(Exception.class)
    @PassToken
    public Object handleException(TNOAHttpRequest request, Exception e) {
    	request.closeInfoImpl();
        request.closeDataImpl();
        String msg = e.getMessage();
        if (msg == null || msg.equals("")) {
            msg = "000002";
        }

        logger.error(Lib.getTrace(e));
        return ErrNo.set(Integer.parseInt(msg));
    }
}
