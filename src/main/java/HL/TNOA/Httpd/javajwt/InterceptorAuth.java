package HL.TNOA.Httpd.javajwt;


import HL.TNOA.Httpd.Models.User;
import HL.TNOA.Lib.TNOAConf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Map;

@Configurable
public class InterceptorAuth implements HandlerInterceptor {
    private Logger logger = LoggerFactory.getLogger(InterceptorAuth.class);

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object object) throws Exception {
        try {
//            logger.warn(InterceptorAuth.class.getSimpleName() + "-->preHandle");
            if (!TNOAHttpRequest.class.equals(request.getClass())) {
                return true;
            }
            TNOAHttpRequest req = (TNOAHttpRequest) request;

            // 如果不是映射到方法直接通过
            if (!(object instanceof HandlerMethod)) {
                return false;
            }

            HandlerMethod handlerMethod = (HandlerMethod) object;
            Method method = handlerMethod.getMethod();
            //检查是否有passtoken注释，有则跳过认证
            if (method.isAnnotationPresent(PassToken.class)) {
                PassToken passToken = method.getAnnotation(PassToken.class);
                if (passToken.required()) {
                    return true;
                }
            }

            String token = request.getHeader("token");
            String time = request.getHeader("time");

            //检查有没有需要用户权限的注解

            // 获取url token
            for (int i = 0; i < 1; i++) {
                if (token == null) {
                    Map<String, String[]> map = request.getParameterMap();
                    if (map.containsKey("token") && map.get("token")[0].length() != 0) {
                        token = map.get("token")[0];
                        break;
                    }
                    throw new RuntimeException("200006");
                }
            }


            if (TNOAConf.get("basic", "debug").equals("true")) {
                if (token != null && token.equals("HuALoNg_3edc1234")) {
                    return true;
                }
            }
            // 获取 token 中的 user id
            AuthorizationApi author = new AuthorizationApi();
            // User user = author.checkIdAuthor(req, token);
            User user = null;
            req.setUser(user);
            return true;
        } finally {
            if (response.getHeader("Access-Control-Allow-Origin") == null) {
                response.addHeader("Access-Control-Allow-Origin", "*");
            }
            // response.addHeader("Access-Control-Allow-Origin", "*");
            if (response.getHeader("Access-Control-Expose-Headers") == null) {
                response.addHeader("Access-Control-Expose-Headers", "Authorization");
            }
            if (response.getHeader("Access-Control-Allow-Headers") == null) {
                response.addHeader("Access-Control-Allow-Headers", "token");
            }
            if (response.getHeader("Access-Control-Allow-Methods") == null) {
                response.addHeader("Access-Control-Allow-Methods",
                        "GET,POST,OPTIONS,PUT,DELETE,PATCH");
            }

            response.addHeader("Access-Control-Max-Age", "3600");

            response.setHeader("Access-Control-Allow-Credentials", "true");


        }
    }
}
