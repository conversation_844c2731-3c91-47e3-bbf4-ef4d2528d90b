package HL.TNOA.Httpd.javajwt;

import HL.TNOA.Httpd.Models.User;
import HL.TNOA.Lib.IpMac;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.Redis.RedisPools;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Date;
import java.util.Random;

public class AuthorizationApi {
    private Logger logger = LoggerFactory.getLogger(AuthorizationApi.class);

    public void setLoginAuthor(TNOAHttpRequest request, HttpServletResponse response, User user) {
        String token = getToken(user, true);
        // 配置redis
        Jedis redis = RedisPools.get();
        try {
            // 先删除原来的，重新配置
            redis.del(user.getId() + "");
            redis.hset(user.getId(), "ipaddr", request.getRemoteAddr());
            String mac = IpMac.get_mac(request.getRemoteAddr());
            if (mac != null && mac.trim().length() == 17) {
                redis.hset(user.getId(), "mac", mac);
            }
            redis.hset(user.getId(), "agent", request.getHeader("User-Agent"));
            redis.hset(user.getId(), "newtoken", token);
            redis.hset(user.getId(), "newtokentime", new Date().getTime() + "");

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            ObjectOutputStream out = new ObjectOutputStream(byteArrayOutputStream);
            try {
                out.writeObject(user);
                redis.hset(user.getId(), "user", Base64.encodeBase64String(byteArrayOutputStream.toByteArray()));
            } finally {
                out.close();
                byteArrayOutputStream.close();
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            redis.close();
        }

        response.addHeader("Authorization", token);
    }

    public void setLoginAuthorUni(TNOAHttpRequest request, HttpServletResponse response, User user, String token) {
        Jedis redis = RedisPools.get();
        try {
            // 配置redis

            String user_id = JWT.decode(token).getAudience().get(0);
            //String user_id = String.valueOf(System.currentTimeMillis());
            logger.warn(user_id);

            // 先删除原来的，重新配置
            redis.del(user_id + "");
            redis.hset(user.getId(), "ipaddr", request.getRemoteAddr());
            String mac = IpMac.get_mac(request.getRemoteAddr());
            if (mac != null && mac.trim().length() == 17) {
                redis.hset(user.getId(), "mac", mac);
            }
            redis.hset(user.getId(), "agent", request.getHeader("User-Agent"));
            redis.hset(user.getId(), "newtoken", token);
            redis.hset(user.getId(), "newtokentime", new Date().getTime() + "");

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            ObjectOutputStream out = new ObjectOutputStream(byteArrayOutputStream);
            try {
                out.writeObject(user);
                redis.hset(user_id, "user", Base64.encodeBase64String(byteArrayOutputStream.toByteArray()));
            } finally {
                out.close();
                byteArrayOutputStream.close();
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            redis.close();
        }

        response.addHeader("Authorization", token);
    }

    public void setTokenAuthor(TNOAHttpRequest request, HttpServletResponse response, User user) {
        String token = getToken(user, true);
        //配置redis
        Jedis redis = RedisPools.get();
        try {
            if (redis.hexists(user.getId(), "newtoken") && redis.hexists(user.getId(), "newtokentime")) {
                redis.hset(user.getId(), "oldtoken", redis.hget(user.getId(), "newtoken"));
                redis.hset(user.getId(), "oldtokentime", redis.hget(user.getId(), "newtokentime"));
            }

            redis.hset(user.getId(), "newtoken", token);
            redis.hset(user.getId(), "newtokentime", new Date().getTime() + "");
        } finally {
            redis.close();
        }

        response.addHeader("Authorization", token);
    }

    public String setRefeshTokenAuthor(TNOAHttpRequest request, HttpServletResponse response, User user) {

        String userid = "";
        String token = request.getHeader("token");
        //	logger.warn("=====Token.110====" + user.getId() + "===>" + token.substring(token.length() - 5, token
        //	.length()));
       /* try {
            userid = JWT.decode(token).getAudience().get(0);
        } catch (JWTDecodeException j) {
            return token;
        }

        if (userid == null || userid.trim().length() == 0) {
            return setTokenAuthor_delay(request, response, user, token);
        }

        // 检测ipaddr，mac
        Jedis redis = RedisPools.get();
        try {
            if (!redis.exists(userid)) {
                return setTokenAuthor_delay(request, response, user, token);
            }

            String keyId = user.getId();

            // 和新token一样
            long now_date = new Date().getTime();
            if (redis.hexists(keyId, "newtoken") && token.equals(redis.hget(keyId, "newtoken"))) {

                if (!redis.hexists(keyId, "newtokentime")) {
                    logger.warn("=====Token.sameNew===1====>");
                    return setTokenAuthor_delay(request, response, user, token);
                }
                long newtokentime = Long.parseLong(redis.hget(keyId, "newtokentime"));
                // 5分钟超时
                long newtokentimeout = 120 * 60 * 1000;
                if ((now_date - newtokentime) > newtokentimeout) {
                    logger.warn("=====Token.sameNew====2===>");
                    return setTokenAuthor_delay(request, response, user, token);
                }
                return setTokenAuthor_delay(request, response, user, token);
            }
            // 和旧token一样
            else if (redis.hexists(keyId, "oldtoken") && token.equals(redis.hget(keyId, "oldtoken"))) {
                logger.warn("=====Token.sameOld=======>");
                if (!redis.hexists(keyId, "newtokentime")) {

                    return setTokenAuthor_delay(request, response, user, token);
                }
                if (!redis.hexists(keyId, "oldtokentime")) {

                    return setTokenAuthor_delay(request, response, user, token);
                }
                long newtokentime = Long.parseLong(redis.hget(keyId, "newtokentime"));
                long oldtokentime = Long.parseLong(redis.hget(keyId, "oldtokentime"));
                long oldtokentimeout = 121 * 60 * 1000;
                if ((now_date - newtokentime) > 3000 || (now_date - oldtokentime) > oldtokentimeout) {
                    return setTokenAuthor_delay(request, response, user, token);
                }
            } else {

                return setTokenAuthor_delay(request, response, user, token);
            }
            redis.hset(keyId, "newtokentime", new Date().getTime() + "");
            redis.hset(keyId, "oldtokentime", new Date().getTime() + "");
        } catch (Exception ex) {
            //   logger.error(Lib.getTrace(ex));
        } finally {
            redis.close();
        }*/
        return token;
    }

    public String setTokenAuthor_delay(TNOAHttpRequest request, HttpServletResponse response, User user, String token) {

        //logger.warn("=====Token.87====" + user.getId() + "===>" + token.substring(token.length() - 5, token.length
        // ()));
        // 配置redis
        Jedis redis = RedisPools.get();
        try {
            if (redis.hexists(user.getId(), "newtoken") && redis.hexists(user.getId(), "newtokentime")) {
                redis.hset(user.getId(), "oldtoken", redis.hget(user.getId(), "newtoken"));
                redis.hset(user.getId(), "oldtokentime", redis.hget(user.getId(), "newtokentime"));
            }

            redis.hset(user.getId(), "newtoken", token);
            redis.hset(user.getId(), "newtokentime", new Date().getTime() + "");
        } finally {
            redis.close();
        }

        response.addHeader("Authorization", token);
        return token;
    }

    public User checkIdAuthor(TNOAHttpRequest request, String token) throws Exception {
        String userid = null;
        try {
            userid = JWT.decode(token).getAudience().get(0);
        } catch (JWTDecodeException j) {
            // throw new RuntimeException("201001");
        }

        if (userid == null || userid.trim().length() == 0) {
            //  throw new RuntimeException("201001");
        }

        // 检测ipaddr，mac
        Jedis redis = RedisPools.get();
        try {
            if (!redis.exists(userid)) {
                //  throw new RuntimeException("201001");
            }

            String base64user = redis.hget(userid, "user");
            if (base64user == null || base64user.trim().length() < 5) {
                redis.del(userid);
                //  throw new RuntimeException("201001");
            }

            User user = null;
            ByteArrayInputStream byteArrayInputStream = null;
            ObjectInputStream objectInputStream = null;
            byteArrayInputStream = new ByteArrayInputStream(Base64.decodeBase64(base64user));
            objectInputStream = new ObjectInputStream(byteArrayInputStream);
            try {
                user = (User) objectInputStream.readObject();
            } catch (Exception e) {
                // throw new RuntimeException("201001");
            } finally {
                if (objectInputStream != null) {
                    try {
                        objectInputStream.close();
                    } catch (Exception e) {
                    }
                }
                if (byteArrayInputStream != null) {
                    try {
                        byteArrayInputStream.close();
                    } catch (Exception e) {
                    }
                }
            }

            // 和登陆的ipaddr是否一致
            if (!request.getRemoteAddr().equals(redis.hget(user.getId(), "ipaddr"))) {
                // redis.del(user.id);
                //throw new RuntimeException("201001");
            }

            // 和登陆的浏览器是否一致
            if (!request.getHeader("User-Agent").equals(redis.hget(user.getId(), "agent"))) {
                // redis.del(user.id);
                //throw new RuntimeException("201001");
            }

            // 和新token一样
            long now_date = new Date().getTime();
            if (redis.hexists(user.getId(), "newtoken") && token.equals(redis.hget(user.getId(), "newtoken"))) {
                if (!redis.hexists(user.getId(), "newtokentime")) {
                    // throw new RuntimeException("201001");
                }
                long newtokentime = Long.parseLong(redis.hget(user.getId(), "newtokentime"));
                // 5分钟超时
                long newtokentimeout = 120 * 60 * 1000;
                if ((now_date - newtokentime) > newtokentimeout) {
                    // throw new RuntimeException("201001");
                }
            }
            // 和旧token一样
            else if (redis.hexists(user.getId(), "oldtoken") && token.equals(redis.hget(user.getId(), "oldtoken"))) {
                if (!redis.hexists(user.getId(), "newtokentime")) {
                    throw new RuntimeException("201001");
                }
                if (!redis.hexists(user.getId(), "oldtokentime")) {
                    //   throw new RuntimeException("201001");
                }
                long newtokentime = Long.parseLong(redis.hget(user.getId(), "newtokentime"));
                long oldtokentime = Long.parseLong(redis.hget(user.getId(), "oldtokentime"));
                long oldtokentimeout = 121 * 60 * 1000;
                if ((now_date - newtokentime) > 3000 || (now_date - oldtokentime) > oldtokentimeout) {
                    //  throw new RuntimeException("201001");
                }
            } else {
                // throw new RuntimeException("201001");
            }

            return user;
        } finally {
            redis.close();
        }
    }

    public void delAuthor(String id) {
        Jedis redis = RedisPools.get();
        try {
            redis.del(id);
        } finally {
            redis.close();
        }
    }

    public String GenerateRandom(int strlen) {
        String chars = "abcdefghigklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ123456789";
        int len = chars.length();

        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < strlen; i++) {
            sb.append(chars.charAt(random.nextInt(len)));
        }

        return sb.toString();
    }

    public String getToken(User user, boolean useid) {
        Date iatDate = new Date();

        String token;
        JWTCreator.Builder token_builder = JWT.create();

        token_builder.withAudience(user.getId());

        token_builder.withIssuedAt(iatDate);
        token = token_builder.sign(Algorithm.HMAC256(GenerateRandom(8)));

        return token;
    }

    //密码相关
    private String generateHash(String salt, String password) throws Exception {
        MessageDigest digest;
        digest = MessageDigest.getInstance("SHA-256");
        digest.reset();
        digest.update(Base64.decodeBase64(salt));
        byte[] btPass = digest.digest(password.getBytes(StandardCharsets.UTF_8));
        digest.reset();
        btPass = digest.digest(btPass);
        return Base64.encodeBase64String(btPass);
    }

    public String getPassword(String password) {
        try {
            Random r = new SecureRandom();
            byte[] salt = new byte[32];
            r.nextBytes(salt);
            String saltBase64 = Base64.encodeBase64String(salt);
            return generateHash(saltBase64, password) + " " + saltBase64;
        } catch (Exception e) {
            return null;
        }
    }

    public boolean checkPassword(String password_hash, String password) {

        try {
            String[] hashs = password_hash.split("\\s");
            if (hashs.length != 2) {
                return false;
            }

            if (generateHash(hashs[1], password).equals(hashs[0])) {
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return false;
        }
    }

}
