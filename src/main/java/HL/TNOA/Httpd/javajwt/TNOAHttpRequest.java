package HL.TNOA.Httpd.javajwt;


import HL.TNOA.Httpd.Models.User;
import HL.TNOA.Lib.DataModel.DataModelHelper;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.MysqlHelper;
import HL.TNOA.Lib.RIUtil1;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class TNOAHttpRequest extends HttpServletRequestWrapper {
    private Logger logger = LoggerFactory.getLogger(getClass());
    private byte[] bytes;
    private WrappedServletInputStream wrappedServletInputStream;
    private String currentUrl;
    private String currIP;

    public TNOAHttpRequest(HttpServletRequest request) throws IOException {
        super(request);
        // 读取输入流里的请求参数，并保存到bytes里
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        int n;
        byte[] buffer = new byte[4096];
        for (; -1 != (n = request.getInputStream().read(buffer)); ) {
            output.write(buffer, 0, n);
        }
        bytes = output.toByteArray();

        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
        this.wrappedServletInputStream = new WrappedServletInputStream(byteArrayInputStream);
        reWriteInputStream();
        currentUrl = request.getRequestURI();

        currIP = request.getHeader("X-Forwarded-For");

        //   logger.warn(currIP);


    }

    /**
     * 把参数重新写进请求里
     */
    public void reWriteInputStream() {
        wrappedServletInputStream.setStream(new ByteArrayInputStream(bytes != null ? bytes : new byte[0]));
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        return wrappedServletInputStream;
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(wrappedServletInputStream));
    }

    private class WrappedServletInputStream extends ServletInputStream {
        public void setStream(InputStream stream) {
            this.stream = stream;
        }

        private InputStream stream;

        public WrappedServletInputStream(InputStream stream) {
            this.stream = stream;
        }

        @Override
        public int read() throws IOException {
            return stream.read();
        }

        @Override
        public boolean isFinished() {
            return true;
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setReadListener(ReadListener readListener) {
        }
    }

    @Override
    public String getHeader(String v) {

        String header = super.getHeader(v);

        if (header == null || "".equals(header)) {
            return header;
        }
        return CleanXSS(header);
    }

    @Override
    public String getParameter(String v) {
        String param = super.getParameter(v);
        if (param == null || "".equals(param)) {
            return param;
        }
        return CleanXSS(param);
    }

    @Override
    public String[] getParameterValues(String v) {
        String[] values = super.getParameterValues(v);
        if (values == null) {
            return null;
        }

        // 富文本内容不过滤
        if ("remarks".equals(v)) {
            return values;
        }

        int length = values.length;
        String[] resultValues = new String[length];
        for (int i = 0; i < length; i++) {
            // 过滤特殊字符
            resultValues[i] = CleanXSS(values[i]);
            if (!(resultValues[i]).equals(values[i])) {
                logger.warn("SQL注入过滤器 => 过滤前：{" + values[i] + "} => 过滤后：{" + resultValues[i] + "}");
            }
        }
        return resultValues;
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        Map<String, String[]> values = super.getParameterMap();
        if (values == null) {
            return null;
        }
        Map<String, String[]> result = new HashMap<>();
        for (String key : values.keySet()) {
            String encodedKey = CleanXSS(key);
            int count = values.get(key).length;
            String[] encodedValues = new String[count];
            for (int i = 0; i < count; i++) {
                encodedValues[i] = CleanXSS(values.get(key)[i]);
            }
            result.put(encodedKey, encodedValues);
        }
        return result;
    }

    private String CleanXSS(String valueP) {

        String badStr = "select|update|or|delete|insert|truncate|char|into|substr|ascii|declare|exec|count|master" +
                "|into|drop|execute|char|declare|sitename|xp_cmdshell|like|from|grant|use|group_concat" +
                "|column_name|information_schema.columns|table_schema|union|where|order|by|1=1|1 = 1";
        boolean matches = valueP.matches(badStr);
        if (matches) {

            logger.warn("SQL非法注入");
            return ErrNo.set(null, 2, "sql非法").toString();
        } else {
            return valueP;
        }
    }

    /////////////////////////////////////////////////////////////
    private JSONObject reqdata = null;

    public JSONObject getRequestParams() throws Exception {
        if (bytes == null) {
            return null;
        }
        if (reqdata == null) {
            JSONObject _reqdata = JSONObject.parseObject(new String(bytes));
            reqdata = new JSONObject();
            reqdata.put("X-Real-IP", currIP);
            for (String _key : _reqdata.keySet()) {
                reqdata.put(CleanXSS(_key), CleanXSS(_reqdata.get(_key).toString()));
            }
        }
        //  logger.warn(reqdata.toString());
        AddLog(reqdata, currentUrl, currIP);
        //     logger.warn(reqdata.toString());
        return reqdata;
    }

    private void AddLog(JSONObject reqdata, String url, String currIP) {
        //  logger.warn(reqdata.toString());
        MysqlHelper mysql = null;
        // MysqlHelper my_sso=null;
        if (reqdata.containsKey("opt_user") && reqdata.getString("opt_user").length() > 10) {
            String opt_user = reqdata.getString("opt_user");
            if (opt_user.length() > 10) {
                try {

                    //    my_sso = new MysqlHelper("mysql_sso");
                    String opt = reqdata.getString("opt");


                    String police_id = RIUtil1.users1.get(opt_user).getString("police_id");
                    String real_opt_user = reqdata.getString("real_opt_user");
                    //  logger.warn(reqdata.toString());
                    //logger.warn(real_opt_user);
                    String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:sss").format(new Date());
                    String sql =
                            "select ipaddr from user_log where (id_card='" + real_opt_user + "' or police_id='" + police_id + "') order by " + "time desc " + "limit 1";
                    //  logger.warn(sql);
                    //  String ipp = my_sso.query_one(sql, "ipaddr");
           /* reqdata.remove("opt");
            reqdata.remove("opt_user");
            reqdata.remove("real_opt_user");*/
                    String ipp = currIP;
                    String req = String.valueOf(reqdata);
                    if (req.length() > 3999) {
                        req = req.substring(0, 3999);
                    }
                    mysql = new MysqlHelper("mysql_zxqc");
                    sql = "insert into user_opt_log (opt,opt_user,real_opt_user,time,data,ip,url) values('" + opt +
                            "','" + opt_user + "'," + "'" + real_opt_user + "','" + time + "','" + req + "','" + ipp + "','" + url + "')";
                    //logger.warn(sql);
                    mysql.update(sql);


                } catch (Exception ex) {
                    logger.error(Lib.getTrace(ex));
                } finally {
                    mysql.close();
                    //  my_sso.close();
                }
            }
        }
    }

    private User user;

    public void setUser(User user) {
        this.user = user;
    }

    public User getUser() {
        return user;
    }

    private InfoModelHelper infomodel = null;

    public InfoModelHelper openInfoImpl() throws Exception {
        if (infomodel == null) {
            infomodel = InfoModelPool.getModel();
        }
        return infomodel;
    }

    public void closeInfoImpl() {
        if (infomodel != null) {
            InfoModelPool.putModel(infomodel);
            infomodel = null;
        }
    }

    private DataModelHelper datamodel = null;

    public DataModelHelper openDataImpl() throws Exception {
//        if (datamodel == null) {
        //datamodel = DataModelPool.getModel();
//        }

        return datamodel;
    }

    public void closeDataImpl() {
        if (datamodel != null) {
            //DataModelPool.putModel(datamodel);
            datamodel = null;
        }
    }
}
