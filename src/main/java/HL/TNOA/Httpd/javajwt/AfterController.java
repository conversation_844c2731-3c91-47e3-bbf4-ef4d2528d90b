package HL.TNOA.Httpd.javajwt;

import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.TNOAConf;
import HL.TNOA.wechat.HttpConnection;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

@ControllerAdvice
public class AfterController implements ResponseBodyAdvice {
    private Logger logger = LoggerFactory.getLogger(this.getClass().getName());

    @Override
    public boolean supports(MethodParameter methodParameter, Class aClass) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter methodParameter, MediaType mediaType, Class aClass,
                                  ServerHttpRequest request, ServerHttpResponse response) {
//        if(body == null){
//            return body;
//        }
//        String bodystr = body.toString();
//        if(bodystr.length() > 500){
//            logger.warn("[" + Thread.currentThread().getId() + "]" +  bodystr.substring(0, 500));
//        }
//        else{
//            logger.warn("[" + Thread.currentThread().getId() + "]" + bodystr);
//        }


        String token = String.valueOf(request.getHeaders().get("token")).replace("[", "").replace("]", "");


        if (token != null && token.length() > 5 && !token.equals("fefc7677-53fc-4378-9e78-f4e934b44ff4_hl1234!@#$")) {
            String unUrl = TNOAConf.get("HttpServ", "uni_url") + "/token";

            String ip = "************";
            try {

                ip = String.valueOf(request.getHeaders().get("X-Forwarded-For"));
                //  logger.warn(ip);

            } catch (Exception ex) {
                logger.warn(Lib.getTrace(ex));
            }
            //  logger.warn(ip);
            if (ip != null) {
                ip = ip.replace("[", "").replace("]", "");
                //   logger.warn(ip);
                String back = HttpConnection.post_token(unUrl, token, new JSONObject(), ip);
                //  System.out.println(back);
                try {
                    JSONObject bac = JSONObject.parseObject(back);
                    if (bac.containsKey("errno") && (bac.getInteger("errno") == 0 || bac.getInteger("errno") == 200)) {
                        String bod = String.valueOf(body);

                        return body;
                    } else {
                        logger.warn(token);
                        return bac;
                    }
                } catch (Exception ex) {
                    logger.error(Lib.getTrace(ex));
                    return back;
                }
            }
        }
   //     logger.warn(body.toString());
        return body;

    }
}
