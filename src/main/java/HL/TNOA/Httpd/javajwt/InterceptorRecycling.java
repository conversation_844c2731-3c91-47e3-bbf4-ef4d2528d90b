package HL.TNOA.Httpd.javajwt;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Configurable
public class InterceptorRecycling implements HandlerInterceptor {
    private Logger logger = LoggerFactory.getLogger(this.getClass().getName());

    @Override
    public void afterCompletion(HttpServletRequest request,
                                HttpServletResponse response, Object handler,
                                Exception ex) throws Exception {
        if (TNOAHttpRequest.class.equals(request.getClass())) {
            TNOAHttpRequest req = (TNOAHttpRequest) request;
            req.closeInfoImpl();
            req.closeDataImpl();
        }

    }
}
