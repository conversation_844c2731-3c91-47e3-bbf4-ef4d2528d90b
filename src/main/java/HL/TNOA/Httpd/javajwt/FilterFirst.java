package HL.TNOA.Httpd.javajwt;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Order(1)
@WebFilter(filterName = "filterFirst", urlPatterns = "/*")
public class FilterFirst implements Filter {
    private Logger logger = LoggerFactory.getLogger(this.getClass().getName());

    @Override
    public void init(FilterConfig filterConfig) {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String u = "1";
        String ruri = "";
        try {
            // logger.warn("********" + request.getRequestURI() + "*************");
            ruri = request.getRequestURI().trim();

            try {

                java.net.URL url = new java.net.URL(request.getHeader("referer"));
                u = url.getHost().trim();

            } catch (Exception ex) {
                //logger.warn(Lib.getTrace(ex));

            }
            //logger.warn("ruri->" + ruri);
            //logger.warn("u->" + u);
            if (u.equals("1")) {
                if (!ruri.startsWith("/web")
                        && !ruri.startsWith("/push_work")
                        && !ruri.startsWith("/preview")
                        && !ruri.startsWith("/download")
                        && !ruri.startsWith("/")
                        && !ruri.startsWith("/dict")
                        && !ruri.startsWith("/token")
                        && !ruri.startsWith("/position")
                        && !ruri.startsWith("/camera")
                        && !ruri.startsWith("/task")
                        && !ruri.startsWith("/notice")
                        && !ruri.startsWith("/task")
                        && !ruri.startsWith("/case")
                        && !ruri.startsWith("/sec_element")
                        && !ruri.startsWith("/position")
                        && !ruri.startsWith("/patrol")
                        && !ruri.startsWith("/noticefication")
                        && !ruri.startsWith("/patrol")
                        && !ruri.startsWith("/code")
                        && !ruri.startsWith("/login_app")
                        && !ruri.startsWith("/noticefication")
                        && !ruri.startsWith("/patrol")
                        && !ruri.startsWith("/code")
                        && !ruri.startsWith("/video")
                        && !ruri.startsWith("/sub_config")
                        && !ruri.startsWith("/anbao_info")
                        && !ruri.startsWith("/memo")
                        && !ruri.startsWith("/data/sm2/sendData")
                        && !ruri.endsWith(".m3u8") && !ruri.endsWith(".ts") && !ruri.endsWith(".mp4")) {
                    HttpServletResponse response = (HttpServletResponse) servletResponse;
                    response.setStatus(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
                    logger.warn(ruri + "->XXXX");
                    return;
                }
            } else {
                if (!u.equals("servicewechat.com") && !u.equals("127.0.0.1") && !u.startsWith("192.") && !u.contains(
                        "hualong") && !u.equals("localhost") && !u.startsWith("50.") && !u.startsWith("20.") && !u.startsWith("qjjcgzpt") && !u.contains("czx.js")) {

                    // filterChain.doFilter(null, null);
                    HttpServletResponse response = (HttpServletResponse) servletResponse;
                    response.setStatus(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
                    logger.warn(u + "->xxxx");
                    logger.warn(ruri + "->XXXX");
                    return;
                }
            }

        } catch (Exception ex) {
            //logger.warn(Lib.getTrace(ex));
        }

        //logger.warn(request.getRequestURI() + " --> " + request.getMethod());

        if ("GET".equals(request.getMethod()) && (request.getRequestURI().startsWith("/web/")) || request.getRequestURI().equals("/")) {
            filterChain.doFilter(servletRequest, servletResponse);
        } else {
            TNOAHttpRequest requestWrapper = new TNOAHttpRequest((HttpServletRequest) servletRequest);
            filterChain.doFilter(requestWrapper, servletResponse);
        }


    }

    @Override
    public void destroy() {
    }
}
