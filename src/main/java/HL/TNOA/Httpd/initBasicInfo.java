package HL.TNOA.Httpd;

import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.MysqlHelper;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static HL.TNOA.Lib.RIUtil.RealDictNameList;
import static HL.TNOA.Lib.RIUtil.org_tree;


public class initBasicInfo implements Runnable {
    private static Logger logger = LoggerFactory.getLogger(initBasicInfo.class);

    @Override
    public void run() {

        while (true) {

            try {


                initDict();
                initUsers();
                initKHConfig();
                //logger.warn(RIUtil.users.toString());
                //logger.warn(RIUtil.dicts.toString());
                initOrgTree();
                // initLoginTimes();
                Thread.sleep(1000 * 60 * 10);

            } catch (Exception ex) {
                logger.warn(Lib.getTrace(ex));

            }
        }
    }

    private void initLoginTimes() {
        MysqlHelper mysql_sso = null;
        try {
            mysql_sso = new MysqlHelper("mysql_sso");
            String sql =
                    "select * from user_login where time='" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + "'";
            List<JSONObject> list = mysql_sso.query(sql);
            JSONArray counts = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                JSONObject det = new JSONObject();
                det.put("id", "jwt_login");
                det.put("label", "警务通登录人次");
                det.put("value", one.getInteger("jwt_login"));
                counts.add(det);


                det = new JSONObject();
                det.put("id", "jwt_distinct");
                det.put("label", "警务通登录人数");
                det.put("value", one.getInteger("jwt_distinct"));
                counts.add(det);

                det = new JSONObject();
                det.put("label", "民警数字证书登录人次");
                det.put("id", "mj_login");
                det.put("value", one.getInteger("zs_login"));
                counts.add(det);

                det = new JSONObject();
                det.put("label", "民警数字证书登录人数");
                det.put("id", "mj_distinct");
                det.put("value", one.getInteger("zs_distinct"));
                counts.add(det);

                det = new JSONObject();
                det.put("label", "辅警数字证书登录人次");
                det.put("id", "fj_login");
                det.put("value", one.getInteger("fj_login"));
                counts.add(det);


                det = new JSONObject();
                det.put("label", "辅警数字证书登录人数");
                det.put("id", "fj_distinct");
                det.put("value", one.getInteger("fj_distinct"));
                counts.add(det);

                det = new JSONObject();
                det.put("id", "time");
                det.put("label", "更新时间");
                det.put("value", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                counts.add(det);


            }
            RIUtil.login_times = counts;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            mysql_sso.close();
        }
    }

    private void initOrgTree() {

        logger.warn("TREE->>");
        MysqlHelper mysql = null;
        try {

            mysql = new MysqlHelper("mysql");
            String sql = "select id,dict_name,remark,type,father_id,permission,color,is_kh from dict where " +
                    "isdelete=1" + " order" + " " + "by index_no";
            List<JSONObject> list = mysql.query(sql);
            JSONArray tw1 = new JSONArray();

            JSONObject one = new JSONObject();
            one.put("id", "320400000000");
            one.put("father_id", "0");
            one.put("dict_name", "常州市公安局");
            tw1.add(one);


            List<JSONObject> tw2 = GetDictByType(list, 22);

            Collections.sort(tw2, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("id");
                    b = o2.getInteger("id");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });

            List<JSONObject> tw3 = GetDictByType(list, 23);
            Collections.sort(tw3, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("id");
                    b = o2.getInteger("id");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });
            List<JSONObject> tw4 = GetDictByType(list, 24);
            Collections.sort(tw4, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("id");
                    b = o2.getInteger("id");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });
            List<JSONObject> tw5 = GetDictByType(list, 25);
            Collections.sort(tw5, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("id");
                    b = o2.getInteger("id");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });
            List<JSONObject> tw6 = GetDictByType(list, 26);
            Collections.sort(tw6, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("id");
                    b = o2.getInteger("id");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });


            JSONArray t1 = new JSONArray();
            for (int i = 0; i < tw1.size(); i++) {
                JSONObject o1 = tw1.getJSONObject(i);

                String id1 = o1.getString("id");

                JSONArray t2 = new JSONArray();
                for (int a = 0; a < tw2.size(); a++) {
                    JSONObject o2 = tw2.get(a);
                    String father_id = o2.getString("father_id");
                    String id2 = o2.getString("id");
                    if (father_id.equals(id1)) {

                        JSONArray t3 = new JSONArray();
                        for (int b = 0; b < tw4.size(); b++) {
                            JSONObject o3 = tw4.get(b);
                            String faid = o3.getString("father_id");
                            if (faid.equals(id2)) {
                                t3.add(o3);
                            }
                        }

                        o2.put("dets", t3);
                        t2.add(o2);
                    }

                }
                for (int a = 0; a < tw3.size(); a++) {
                    JSONObject o2 = tw3.get(a);
                    String father_id = o2.getString("father_id");
                    String id2 = o2.getString("id");
                    if (father_id.equals(id1)) {

                        JSONArray t3 = new JSONArray();

                        for (int b = 0; b < tw4.size(); b++) {
                            JSONObject o3 = tw4.get(b);
                            String faid = o3.getString("father_id");
                            if (faid.equals(id2)) {
                                t3.add(o3);
                            }
                        }

                       /* o2.put("dets", t3);
                        t2.add(o2);*/
                        for (int b = 0; b < tw5.size(); b++) {
                            JSONObject o3 = tw5.get(b);
                            String faid = o3.getString("father_id");
                            String id3 = o3.getString("id");

                            if (faid.equals(id2)) {
                                JSONArray t4 = new JSONArray();
                                for (int c = 0; c < tw6.size(); c++) {
                                    try {
                                        JSONObject o4 = tw6.get(c);
                                        //   System.out.println(o4);
                                        String fid = o4.getString("father_id");
                                        //    System.out.println(fid);
                                        if (fid.equals(id3)) {
                                            t4.add(o4);
                                        }
                                    } catch (Exception ex) {
                                    }
                                }
                                o3.put("dets", t4);
                                //System.out.println(o3);
                                t3.add(o3);
                            }
                        }
                        o2.put("dets", t3);

                        t2.add(o2);
                    }
                }
                o1.put("dets", t2);
                t1.add(o1);

            }

            org_tree = t1;

            logger.warn("org_tree-->" + org_tree.size());
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        } finally {
            mysql.close();
        }

    }

    private List<JSONObject> GetDictByType(List<JSONObject> list, int type) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {

            JSONObject one = list.get(i);
            int t = one.getInteger("type");

            if (t == type) {
                back.add(one);
            }


        }
        return back;
    }

    public static void initUsers() {
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select * from user where status=1 and isdelete=1 ";
            System.out.println(sql);
            List<JSONObject> list = mysql.query(sql);
            System.out.println(list.size() + "---->");
            System.out.println(list.get(4));
            for (int i = 0; i < list.size(); i++) {
                try {
                    JSONObject one = list.get(i);
                    if (!one.containsKey("name")) {
                        break;
                    }
                    if (!one.containsKey("tele_long")) {
                        one.put("tele_long", "");
                    }
                    if (!one.containsKey("position")) {
                        one.put("position", "");
                    } else {
                        String position = one.getString("position");

                        one.put("position_name", RealDictNameList(RIUtil.StringToList(position)));
                    }
                    if (!one.containsKey("org")) {
                        one.put("org", "");
                        one.put("org_name", "");
                    } else {
                        String org = one.getString("org");

                        one.put("org_name", RealDictNameList(RIUtil.StringToList(org)));
                    }

                    if (!one.containsKey("tele_sort")) {
                        one.put("tele_sort", "");
                    }
                    if (!one.containsKey("id_num")) {
                        one.put("id_num", "");
                    }
                    if (!one.containsKey("img")) {
                        one.put("img", "");
                    }
                    if (!one.containsKey("community")) {
                        one.put("community", "");
                    } else {
                        String community = one.getString("community");
                        one.put("community_name", RealDictNameList(RIUtil.StringToList(community)));
                    }
                    if (!one.containsKey("isMain")) {
                        one.put("isMain", "");
                    }
                    if (!one.containsKey("assType")) {
                        one.put("accType", "");
                        one.put("assType_name", "");
                    } else {
                        String accType = one.getString("assType");
                        one.put("assType_name", RealDictNameList(RIUtil.StringToList(accType)));
                    }
                    if (!one.containsKey("open_id")) {
                        one.put("open_id", "");
                    }
                    if (!one.containsKey("unit")) {
                        one.put("unit", "");
                        one.put("unit_name", "");
                    } else {

                        String unit = one.getString("unit").replace("，", ",").split(",")[0];

                        one.put("unit", unit);

                        one.put("unit_name", RIUtil.dicts.get(unit));
                    }
                    if (!one.containsKey("car_num")) {
                        one.put("car_num", "");
                    }

                    RIUtil.users.put(one.getString("id"), one);
                } catch (Exception ex) {
                    logger.error(Lib.getTrace(ex));
                }
            }

            JSONObject one = new JSONObject();
            one.put("name", "全警小喇叭");
            one.put("position", "");
            one.put("position_name", "");
            one.put("tele_long", "");
            one.put("tele_sort", "");
            one.put("img", "");
            one.put("community", "");
            one.put("community_name", "");
            one.put("isMain", "");
            one.put("assType", "");
            one.put("assType_name", "");
            one.put("onoff", "");
            one.put("onoff_time", "");
            one.put("isFamily", "");
            one.put("open_id", "");
            one.put("unit", "");
            one.put("unit_name", "");
            one.put("car_num", "");
            one.put("org", "");
            one.put("org_name", "");
            one.put("id_num", "");

            RIUtil.users.put("999999999", one);
            logger.warn("init.users->" + RIUtil.users.size());
            // logger.warn(RIUtil.users.toString());

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    public static void initDict() {
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql =
                    "select id,dict_name,permission,color," + "father_id,index_no,type,gadm,remark,is_kh,memo from " +
                            "dict " + "where " + "isdelete=1 and type is not null order by type,index_no,id";
            System.out.println(sql);
            List<JSONObject> list = mysql.query(sql);
            System.out.println(list.size());
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);

                    if (!one.containsKey("dict_name")) {

                        break;
                    }
                    if (!one.containsKey("permission")) {
                        one.put("permission", "");
                    }
                    if (!one.containsKey("memo")) {
                        one.put("memo", "");
                    }
                    if (!one.containsKey("color")) {
                        one.put("color", "");
                    }
                    if (!one.containsKey("father_id")) {
                        one.put("father_id", "");
                    }
                    if (!one.containsKey("index_no")) {
                        one.put("index_no", "99");
                    }
                    if (!one.containsKey("type")) {
                        one.put("type", "");
                    }
                    if (!one.containsKey("gadm")) {
                        one.put("gadm", "");
                    }
                    if (!one.containsKey("remark")) {
                        one.put("remark", "");
                    }

                    RIUtil.dicts.put(one.getString("id"), one);

                }
                logger.warn("init.dict->" + RIUtil.dicts.size());
            }

        } catch (Exception ex) {
            System.out.println(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    public static void initKHConfig() {
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
//            String sql = "select * from kh_config where isdelete=1 ";
            String sql = "select * from kh_config  ";

            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    if (!one.containsKey("id")) {
                        break;
                    }
                    if (!one.containsKey("kh_name")) {
                        one.put("kh_name", "");
                    }
                    if (!one.containsKey("pg_rules")) {
                        one.put("pg_rules", "");
                    }
                    if (!one.containsKey("point")) {
                        one.put("point", "");
                    }
                    if (!one.containsKey("full_mark")) {
                        one.put("full_mark", "");
                    }
                    if (!one.containsKey("static_type")) {
                        one.put("static_type", "");
                    }
                    if (!one.containsKey("real_table")) {
                        one.put("real_table", "");
                    }
                    if (!one.containsKey("real_field")) {
                        one.put("real_field", "");
                    }

                    RIUtil.kh_configs.put(one.getString("id"), one);

                }
                logger.warn("init.kh_config->" + RIUtil.kh_configs.size());
            }

        } catch (Exception ex) {

        } finally {
            InfoModelPool.putModel(mysql);
        }
    }
}
