
package HL.TNOA.Httpd.Proxy;


import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.TNOAConf;
import org.eclipse.jetty.server.Server;
import org.eclipse.jetty.servlet.ServletContextHandler;
import org.eclipse.jetty.servlet.ServletHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServlet;


public class ProxyServer implements Runnable {
    private static Logger logger = LoggerFactory.getLogger(ProxyServer.class);

    @Override
    public void run() {
        try {

            Server server = new Server(TNOAConf.getInt("HttpServ", "proxy"));
            ServletContextHandler servletContextHandler = new ServletContextHandler(server, "/");

            HttpServlet proxyServlet = new MyProxyServlet_http();

            ServletHolder servletHolder = new ServletHolder();
            servletHolder.setServlet(proxyServlet);
            servletHolder.setInitParameter("targetUri", "http://************:8000");
            servletContextHandler.addServlet(servletHolder, "/*");

            logger.warn("/s");
           /* proxyServlet = new MyProxyServlet();
            servletHolder = new ServletHolder();
            servletHolder.setServlet(proxyServlet);
            servletHolder.setInitParameter("targetUri", "https://mmbiz.qpic.cn/mmbiz_gif");
            servletContextHandler.addServlet(servletHolder, "/mmbiz_gif/*");
            logger.warn("/mmbiz_gif");
            proxyServlet = new MyProxyServlet();
            servletHolder = new ServletHolder();
            servletHolder.setServlet(proxyServlet);
            servletHolder.setInitParameter("targetUri", "http://mmbiz.qpic.cn/mmbiz_jpg");
            servletContextHandler.addServlet(servletHolder, "/mmbiz_jpg/*");
            logger.warn("/mmbiz_jpg");
            proxyServlet = new MyProxyServlet();
            servletHolder = new ServletHolder();
            servletHolder.setServlet(proxyServlet);
            servletHolder.setInitParameter("targetUri", "https://mpvideo.qpic.cn");
            servletContextHandler.addServlet(servletHolder, "/*");
            logger.warn("/mpvideo");*/
            server.start();


            logger.warn("proxy->" + TNOAConf.getInt("HttpServ", "proxy"));
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
        }
    }
}

