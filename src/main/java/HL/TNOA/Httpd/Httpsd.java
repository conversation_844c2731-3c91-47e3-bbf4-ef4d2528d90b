package HL.TNOA.Httpd;

import HL.TNOA.Httpd.javajwt.InterceptorAuth;
import HL.TNOA.Httpd.javajwt.InterceptorRecycling;
import HL.TNOA.Lib.TNOAConf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

import javax.servlet.http.HttpServletResponse;

@ServletComponentScan
@SpringBootApplication
@Configuration
@Controller
@EnableScheduling
@CrossOrigin(origins = "*")
public class Httpsd extends WebMvcConfigurationSupport {
    private static Logger logger = LoggerFactory.getLogger(Httpsd.class);
    public static int httpport = 80;
//    public static int httspport = 443;

    public static void main(String[] args) {
        new Thread(new initBasicInfo(), "缓存任务一").start();
        new Thread(new initBasicInfo1(), "缓存任务一").start();



        if (TNOAConf.has("HttpServ", "httpport")) {
            httpport = Integer.parseInt(TNOAConf.get("HttpServ", "httpport"));
        }
       /* if (TNOAConf.has("HttpServ", "httpsport")) {
            httspport = Integer.parseInt(TNOAConf.get("HttpServ", "httpsport"));
        }*/

        String[] arg = {"--server.port=" + httpport};
        //SpringApplication.run(RestfulServ.class, args);
        String proper = "spring.config.location=classpath:/applicationRestful.yaml";
        //String proper = "spring.config.location=applicationRestful.yaml";
        SpringApplicationBuilder builder = new SpringApplicationBuilder(Httpsd.class);
        builder.properties(proper);
        builder.run(arg);
    }

    @RequestMapping(produces = "text/html", path = {"/"})
    public String reindex(HttpServletResponse response) {
        response.addHeader("Content-Type", "text/html");
        return "redirect:/web/index.html";
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //添加拦截器
        /*registry.addInterceptor(new NorsInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("/web/**").excludePathPatterns("/sources/**")
                .excludePathPatterns("/error");*/
        registry.addInterceptor(new InterceptorAuth())
                .addPathPatterns("/**")
                .excludePathPatterns("/web/**").excludePathPatterns("/h5/**").
                excludePathPatterns("/sources/**").excludePathPatterns("/images/**")
                .excludePathPatterns("/error");
        registry.addInterceptor(new InterceptorRecycling())
                .addPathPatterns("/**")
                .excludePathPatterns("/web/**").excludePathPatterns("/sources/**").excludePathPatterns("/images/**").excludePathPatterns("/h5/**")
                .excludePathPatterns("/error");
        super.addInterceptors(registry);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/web/**")
                .addResourceLocations("file:./web/");
        registry.addResourceHandler("/sources/**").addResourceLocations("file:" + TNOAConf.get("file",
                "sou_path") +
                "");
        registry.addResourceHandler("/images/**").addResourceLocations("file:" + TNOAConf.get("file",
                "img_path") + "");
        registry.addResourceHandler("/h5/**").addResourceLocations("file:./h5/");
        super.addResourceHandlers(registry);
    }
}
