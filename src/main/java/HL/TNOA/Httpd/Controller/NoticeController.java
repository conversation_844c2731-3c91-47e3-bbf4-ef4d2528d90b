package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.wechat.TestMsgLine;
import HL.TNOA.wechat.wechatMsgTemp;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mysql.cj.util.StringUtils;
import okhttp3.*;
import org.eclipse.jetty.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static HL.TNOA.Lib.RIUtil.RealDictNames;

@RestController
public class NoticeController {
    private static Logger logger = LoggerFactory.getLogger(NoticeController.class);
    // private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static String ip = "";

    @RequestMapping(method = {RequestMethod.POST}, path = {"/notice"})
    //@PassToken
    public static JSONObject get_notice(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        ip = request.getRemoteAddr();
        String opt = "";

        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("notice_get")) {
                return getNotice(data);
            } else if (opt.equals("notice_get_list")) {
                //logger.warn("notice--->" + data.toString());
                return getNoticeList(data);
            } else if (opt.equals("notice_create")) {
                logger.warn("notice--->" + data.toString());
                return createNotice(data, request);
            } else if (opt.equals("notice_up_reader")) {
                logger.warn("notice--->" + data.toString());
                return updateReader(data);
            } else if (opt.equals("notice_get_reading")) {
                return GetReading(data);
            } else if (opt.equals("notice_delete")) {
                return deleteNotice(data);
            } else if (opt.equals("notice_comment")) {
                logger.warn("notice--->" + data.toString());
                return comment(data);
            } else if (opt.equals("notice_delete_comment")) {
                return deleteComment(data);
            } else if (opt.equals("notice_up_comment")) {
                logger.warn("notice--->" + data.toString());
                return upComment(data);
            } else if (opt.equals("notice_update")) {
                logger.warn("notice--->" + data.toString());
                return updateNotice(data, request);
            } else if (opt.equals("notice_getSwiper")) {
                return getSwiper(data);
            } else if (opt.equals("notice_ding_comment")) {
                logger.warn("notice--->" + data.toString());
                return dingComment(data);
            } else if (opt.equals("notice_ding_unread")) {
                logger.warn("notice--->" + data.toString());
                return dingUnread(data);
            } else if ("notice_main".equals(opt)) {
                //logger.warn("notice--->" + data.toString());
                return NoticeMain(data);
            } else if ("notice_xxbs_static".equals(opt)) {
                return Notice_xxbs_static(data);
            } else if (opt.equals("notice_count")) {
                return NoticeCount(data);
            } else if (opt.equals("notice_sta")) {
                return noticeSta(data);
            } else if (opt.equals("notice_type_sta")) {
                return noticeTypeSta(data);
            } else {
                return ErrNo.set(432003);
            }
        } else {
            return ErrNo.set(432003);
        }
    }

    private static JSONObject noticeTypeSta(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String start_time = "";
        String end_time = "";
        String where = "1=1";
        String sql = "";
        String type = "";
        String unit = "";
        if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
            start_time = data.getString("start_time");
            where += " and create_time > '" + start_time + " '";
        }
        if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
            end_time = data.getString("end_time");
            where += " and create_time < '" + end_time + "'";
        }
        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
            where += " and type = '" + type + "'";
        }
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
        }
        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");
            String unit_type = RIUtil.dicts.get(unit).getString("type");
            if (unit_type.equals("22") || unit_type.equals("23") || unit_type.equals("24") || unit_type.equals("27") || unit_type.equals("28") || unit_type.equals("21")) {
                unit = unit.substring(0, 6);
            }
            if (unit_type.equals("25") || unit_type.equals("26")) {
                unit = unit.substring(0, 8);
            }
            sql =
                    "select id , decode(title,'" + RIUtil.enTitle + "') as title ,decode(`content`,'" + RIUtil.enContent +
                            "') as content ,create_user,isTop,isNew,notice_time,readed,reading,label,type,subType," + "cycle,link," + "old_author  " + " from notice" + " where isdelete = 1 and unit like '" + unit + "%' and " + where + " order by isTop desc," + "isNew " + "desc,notice_time desc ";
            logger.warn("sql===" + sql);
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                back.put("data", RelaInfoList(list, mysql, ""));
                sql =
                        "select count(id) " + " from notice" + " where isdelete = 1 and unit like '" + unit + "%' " +
                                "and" + " " + where + " ";
                back.put("count", mysql.query_count(sql));
            } else {
                data.put("data", list);
                data.put("count", 0);
            }
            return back;
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            mysql.close();
        }

    }

    private static JSONObject noticeSta(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        String start_time = "";
        String end_time = "";
        String where = "1=1";
        String sql = "";

        if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
            start_time = data.getString("start_time");
            where += " and create_time > '" + start_time + " '";
        }
        if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
            end_time = data.getString("end_time");
            where += " and create_time < '" + end_time + "'";
        }
        String opt_user = "";
        String unit = "";
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        }
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
        } else {
            return ErrNo.set(401001);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            //获取层级

            String unit_type = RIUtil.dicts.get(unit).getString("type");

            String unit_use = RIUtil.dicts.get(unit).getString("id");
            if (unit_type.equals("22") || unit_type.equals("27") || unit_type.equals("21")) {
                unit_use = unit.substring(0, 4);
            }
            if (unit_type.equals("24") || unit_type.equals("23")) {
                unit_use = unit.substring(0, 6);
            }
            if (unit_type.equals("26") || unit_type.equals("25")) {
                unit_use = unit.substring(0, 8);
            }

            //获取下级

            sql = "select id , dict_name , type from dict where id like '" + unit_use + "%'  and type in (21,23,25)";
            //and (dict_name like '%"+"分局"+"' or dict_name like '%"+"派出所"+"' or dict_name like '%"+"责任区"+"')  ";

            List<JSONObject> low_units = mysql.query(sql);


//            if (unit_type.equals("21") || unit_type.equals("22") || unit_type.equals("27")) {//||unit_type.equals
//            ("22")
//                JSONObject jsonObject = new JSONObject();
//                jsonObject.put("dict_name", "市局本级");
//                jsonObject.put("id", unit);
//                low_units.add(jsonObject);
//            }
//            if (unit_type.equals("23") || unit_type.equals("24")) {
//                JSONObject jsonObject = new JSONObject();
//                jsonObject.put("dict_name", "分局本级");
//                jsonObject.put("id", unit);
//                low_units.add(jsonObject);
//            }

            HashMap<String, JSONObject> datas = new HashMap<>();


            for (JSONObject low_unit : low_units) {

                JSONObject sta_data = new JSONObject();
                sta_data.put("unit_name", low_unit.getString("dict_name"));
                sta_data.put("unit_id", low_unit.getString("id"));
                sta_data.put("TOT", 0);
                sta_data.put("PTOT", 0);
                sta_data.put("PC", 0);
                sta_data.put("PCD", 0);
                sta_data.put("TZTB", 0);
                sta_data.put("TB", 0);
                sta_data.put("DB", 0);

                String unit_id = low_unit.getString("id");
                //处理
                String unit_id_data = null;

                if (unit_type.equals("23") || unit_type.equals("24") || unit_type.equals("25") || unit_type.equals(
                        "26")) {
                    unit_id_data = unit_id.substring(0, 8);
                }

                if (unit_type.equals("21") || unit_type.equals("22") || unit_type.equals("27")) {
                    unit_id_data = unit_id.substring(0, 6);
                }

                if (!datas.containsKey(unit_id_data)) {
                    datas.put(unit_id_data, sta_data);
                }
            }
            sql = " select unit,reading,readed,users,type,check_time,id from  notice where " + where + " and " +
                    "isdelete=1  "; //and unit = '"+low_unit.getString("id")+"'

            List<JSONObject> notice_list = mysql.query(sql);
            for (int i = 0; i < notice_list.size(); i++) {
                JSONObject one = notice_list.get(i);
                String id = one.getString("id");
                //做处理
                String usub = one.getString("unit");
                String dict_type = RIUtil.dicts.get(usub).getString("type");

                if (dict_type.equals("24") || dict_type.equals("28")) {
                    usub = usub.substring(0, 6) + "00";
                }
                if (dict_type.equals("26")) {
                    usub = usub.substring(0, 8);
                }
                String usub_data = null;

                if (!StringUtils.isNullOrEmpty(usub)) {

                    if (unit_type.equals("23") || unit_type.equals("24") || unit_type.equals("25") || unit_type.equals("26")) {
                        usub = usub.substring(0, 8);
                    }
                    if (unit_type.equals("21") || unit_type.equals("22")) {
                        usub = usub.substring(0, 6);
                    }
                }

                if (datas.containsKey(usub)) {
                    JSONObject det = datas.get(usub);
                    int tot = det.getIntValue("TOT");
                    tot++;
                    det.put("TOT", tot);
                    int ptot = 0;
                    try {
                        String users = one.getString("users");
                        ptot = det.getIntValue("PTOT");
                        ptot = ptot + users.split(",").length;
                    } catch (Exception e) {
                        // TODO: handle exception
                    }
                    det.put("PTOT", ptot);

                    String readed = one.getString("readed");
                    int pc = 0;
                    try {
                        int p = det.getIntValue("PC");
                        pc = p + readed.split(",").length;

                    } catch (Exception e) {
                        // TODO: handle exception
                    }

                    det.put("PC", pc);

                    String ctime = one.getString("check_time");
                    if (ctime.length() < 19) {
                        ctime = ctime + ":00";
                    }
                    int pcd = 0;

                    try {
                        sql = "select count(id) as count from notice_check_log where rela_id='" + id + "' and " +
                                "time>'" + ctime + "'";

                        int pcdd = mysql.query_count(sql);
                        pcd = det.getIntValue("PCD") + pcdd;

                    } catch (Exception e) {
                        // TODO: handle exception
                    }
                    det.put("PCD", pcd);

                    int tztb = 0;
                    String type = one.getString("type");

                    if (type.contains("11a1")) {
                        tztb = det.getIntValue("TZTB");
                        tztb++;

                    }
                    det.put("TZTB", tztb);
                    int tb = 0;
                    if (type.contains("11a3")) {
                        tb = det.getIntValue("TB");
                        tb++;

                    }
                    det.put("TB", tb);

                    int db = 0;
                    if (type.equals("04f9ce6f-1e7a-4696-93c7-12521583f15c")) {
                        db = det.getIntValue("DB");
                        db++;

                    }
                    det.put("DB", db);

                    datas.put(usub, det);
                }
            }

            List list = new ArrayList();
            for (Map.Entry<String, JSONObject> dd : datas.entrySet()) {
                JSONObject d = dd.getValue();
                list.add(dd.getValue());

            }

            back.put("data", list);
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            mysql.close();
            InfoModelPool.putModel(mysql);
        }

        return back;
    }

    private static JSONObject NoticeCount(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String opt_user = "";
            String unit = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");

            }

            String sql = "select count(a.id) as count,a.type,dict_name from notice a left join dict b on a.type=b.id "
                    + "where" + " " + "reading like '%" + opt_user + "%' and a.isdelete=1 " + "group by a.type";
            logger.warn(sql);
            List<JSONObject> list = mysql.query(sql);
            List<JSONObject> rets = new ArrayList<>();

            if (list.size() > 0) {
                rets.addAll(list);
            }
            sql = "select count(a.id) as count from notice a " + "where reading like '%" + opt_user + "%' and a" +
                    ".isdelete=1 ";
            logger.warn(sql);
            int count = mysql.query_count(sql);

            JSONObject det = new JSONObject();
            det.put("dict_name", "信息管理");
            det.put("count", count);
            rets.add(det);

            //任务盯办

            sql = "select count(id) as count from ding_notice where reading like '%" + opt_user + "%' and isdelete=1";
            int c = mysql.query_count(sql);
            det = new JSONObject();
            det.put("dict_name", "盯办任务");
            det.put("count", c);
            rets.add(det);

            OracleHelper ora_hl = null;
            count = 0;
            try {
                ora_hl = new OracleHelper("ora_hl");

                String exeid = GetQQBUserId(opt_user);

                sql =
                        "select count(*) as count from HL.QQB_TASK_DET where RECEIVE_OBJECT_ID='" + exeid + "'" + " " + "and"
                                + " " + "status=2 and deleted =0 ";
                logger.warn(sql);
                count = ora_hl.query_count(sql);

            } catch (Exception ex) {
                logger.warn(Lib.getTrace(ex));
                return ErrNo.set(null, 2, Lib.getTrace(ex));

            } finally {
                ora_hl.close();
            }
            det = new JSONObject();
            det.put("dict_name", "任务中心");
            det.put("count", count);
            rets.add(det);

            back.put("data", rets);
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static String GetQQBUserId(String user_id) {
        MysqlHelper qqb_user = null;
        String exeid = "";
        try {
            qqb_user = new MysqlHelper("mysql_qqb_user");

            String sql = "select id from auth.SYS_AUTH_USER where idcard_no='" + user_id + "' and deleted=0 and " +
                    "(IS_TEMPORARY  is null or IS_TEMPORARY <>1) and type>=0 order by type limit 1";
            exeid = qqb_user.query_one(sql, "ID");

        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
        } finally {
            qqb_user.close();


        }
        return exeid;
    }

    private static JSONObject Notice_xxbs_static(JSONObject data) {
        String user_id = "";
        String start_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + " 00:00:00";
        String end_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + " 23:59:59";

        JSONObject back = ErrNo.set(0);
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
            }


            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date") + " 00:00:00";
            }
            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date") + " 23:59:59";
            }
            String sql = "select create_user from notice " + "where type=6 and create_time>='" + start_date + "' and "
                    + "create_time<='" + end_date + "' " + "and isdelete=1 group by create_user";
            //logger.warn(sql);
            List<JSONObject> ulist = mysql.query(sql);
            List<JSONObject> datas = new ArrayList<>();
            if (ulist.size() > 0) {
                for (int u = 0; u < ulist.size(); u++) {
                    JSONObject uone = ulist.get(u);
                    String uid = uone.getString("create_user");
                    sql =
                            "select count(id) as count from notice " + "where type=6 and create_time>='" + start_date + "' " + "and create_time<='" + end_date + "' " + "and isdelete=1 and create_user='" + uid + "'";
                    //logger.warn(sql);
                    int count = mysql.query_count(sql);

                    //根据uid查身份证号
                    sql = "select id_num from user where id = '" + uid + "' ";
                    String idNum = mysql.query_one("id_num", sql);

                    uone.put("user", RIUtil1.users1.get(idNum));
                    uone.put("count", count);
                    datas.add(uone);

                }

                //排序
                Collections.sort(datas, (JSONObject o1, JSONObject o2) -> {

                    int a = o1.getInteger("count");
                    int b = o2.getInteger("count");

                    // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                    if (a < b) {  //降序排列，升序改成a>b
                        return 1;
                    } else if (a == b) {
                        return 0;
                    } else {
                        return -1;
                    }
                });
            }
            back.put("data", datas);

            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }


    }

    private static JSONObject NoticeMain(JSONObject data) {
        String id = "";

        String content = "";
        String create_user = "";
        String is_notice = "1";
        String isTop = "";
        String isNew = "";
        String type = "1";
        String order = " a.isTop desc,a.notice_time desc";
        String user_id = "";
        int limit = 20;
        int page = 1;
        String sql = "";
        String label = "";

        String create_time_start = "";
        String create_time_end = "";

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
            sql = sql + " a.id ='" + id + "' and ";
        }

        if (data.containsKey("content") && data.getString("content").length() > 0) {
            content = data.getString("content");
            sql =
                    sql + " (decode(a.title,'" + RIUtil.enTitle + "') like'%" + content + "%' or decode(a.content,'" + RIUtil.enContent + "') like'%" + content + "%') and";
        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
            sql = sql + " a.create_user= '" + create_user + "' and ";
        }
        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");
            sql = sql + " (a.reading like '%" + user_id + "%' or a.readed like '%" + user_id + "%') and ";
        }

        if (data.containsKey("is_notice") && data.getString("is_notice").length() > 0) {
            is_notice = data.getString("is_notice");
        }
        sql = sql + " a.is_notice='" + is_notice + "' and ";
        if (data.containsKey("isTop") && data.getString("isTop").length() > 0) {
            isTop = data.getString("isTop");
            sql = sql + " a.isTop='" + isTop + "' and ";
        }
        if (data.containsKey("isNew") && data.getString("isNew").length() > 0) {
            isNew = data.getString("isNew");
            sql = sql + " a.isNew='" + isNew + "' and ";
        }

        if (data.containsKey("label") && data.getString("label").length() > 0) {
            label = data.getString("label");
            sql = sql + " a.label ='" + label + "' and ";
        }
        if (data.containsKey("order") && data.getString("order").length() > 0) {
            order = data.getString("order").replace("|", " ");

        }

        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
            sql = sql + " a.type='" + type + "' and ";
        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
            sql = sql + " create_user='" + create_user + "' and ";
        }
        if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
            create_time_start = data.getString("create_time_start").replace("|", " ");
            sql = sql + " create_time>='" + create_time_start + "' and ";
        }
        if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
            create_time_end = data.getString("create_time_end").replace("|", " ");
            sql = sql + " create_time<='" + create_time_end + "' and ";
        }
        if (data.containsKey("limit") && data.getString("limit").length() > 0) {
            limit = data.getInteger("limit");

        }
        if (data.containsKey("page") && data.getString("page").length() > 0) {
            page = data.getInteger("page");

        }

        MysqlHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = new MysqlHelper("mysql");
            String sqls =
                    "select a.id,decode(a.content,'" + RIUtil.enContent + "') as content,decode(a.title,'" + RIUtil.enTitle + "') as title,a.create_user,a.isTop,a.isNew,a.notice_time,a.readed,a.label,a.unit from notice a where cycle!=1 and " + sql + " a.isdelete=1  order by " + order + " limit " + limit + " offset " + limit * (page - 1);


            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            sqls =
                    "select a.id,decode(a.content,'" + RIUtil.enContent + "') as content,decode(a.title,'" + RIUtil.enTitle + "') as title,a.create_user,a.isTop,a.isNew,a.notice_time,a.readed,a.label,a.unit from notice a where (cycle=1 and notice_time>='" + today + " 00:00:00' and notice_time<='" + today + " 23:59:59') " + "and " + sql + " a.isdelete=1  order by " + order + " limit " + limit + " offset " + limit * (page - 1);

            List<JSONObject> list1 = new ArrayList<>();
            list1 = mysql.query(sqls);

            list.addAll(list1);

            if (list.size() > 0) {
                back.put("data", RelaInfoList(list, mysql, ""));
                sqls = "select count(id) as count from notice a where 1=1 and " + sql + " a.isdelete=1";

                back.put("count", mysql.query_count(sqls));

            } else {
                back.put("data", list);
                back.put("count", 0);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 432004, Lib.getTrace(e));
        } finally {
            mysql.close();
        }
        return back;


    }

    private static JSONObject dingUnread(JSONObject data) {
        String notice_id = "";
        String accepters = "";
        String opt_user = "";
        int source = 0;
        if (data.containsKey("notice_id") && data.getString("notice_id").length() > 0) {
            notice_id = data.getString("notice_id");
        } else {
            // return ErrNo.set(432024);
        }
        if (data.containsKey("accepters") && data.getString("accepters").length() > 0) {
            accepters = data.getString("accepters");
        } else {
            return ErrNo.set(432024);
        }

        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(432024);
        }
        if (data.containsKey("source") && data.getString("source").length() > 0) {
            source = data.getInteger("source");
        } else {
            //return ErrNo.set(432024);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            JSONObject back = ErrNo.set(0);
            String comment = "您有一条通知请尽快签收";
            List<String> userList = new ArrayList<>();
            userList.add(accepters);


            TestMsgLine send = new TestMsgLine();
            send.sendMSG(comment, userList);


            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    public static JSONObject dingComment(JSONObject data) {
        String comment = "";
        String accepter = "";
        String real_id = "";
        String type = "";
        String opt_user = "";

        if (data.containsKey("comment") && data.getString("comment").length() > 0) {
            comment = data.getString("comment");
        } else {
            return ErrNo.set(432024);
        }
        if (data.containsKey("real_id") && data.getString("real_id").length() > 0) {
            real_id = data.getString("real_id");
        } else {
            return ErrNo.set(432024);
        }
        if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
            accepter = data.getString("accepter");
        } else {
            return ErrNo.set(432024);
        }

        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(432024);
        }
        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
        } else {
            return ErrNo.set(432024);
        }


        List<String> userList = new ArrayList<>();
        String[] accs = accepter.split(",");
        for (int i = 0; i < accs.length; i++) {
            userList.add(accs[i]);
            JSONObject det = data;
            det.remove("real_opt_user");
            det.put("accepter", accs[i]);
            System.out.println(det);
            RIUtil.JsonInsert(det, "ding_msg");
        }

        TestMsgLine send = new TestMsgLine();
        send.sendMSG(comment, userList);


        return ErrNo.set(0);


    }


    private static JSONObject getSwiper(JSONObject data) {

        InfoModelHelper mysql = null;

        try {
            mysql = InfoModelPool.getModel();
            JSONObject back = ErrNo.set(0);
            int type = 0;
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getInteger("type");
            }

            String sqls = "select a.id,a.rela_id,a.isMain,b.type " + "from upload a left join notice b " + "on a" +
                    ".rela_id=b.id  where a.isMain=1 and b.type='" + type + "'";
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", GetRelaTitle(list, mysql));
            } else {
                back.put("data", list);
            }
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }


    }

    private static Object GetRelaTitle(List<JSONObject> list, InfoModelHelper mysql) throws Exception {

        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String rela_id = one.getString("rela_id");
            String title = RIUtil.IdToName(rela_id, mysql, "decode(title,'" + RIUtil.enTitle + "') as title", "notice");
            one.put("title", title);
            back.add(one);
        }

        return back;
    }

    private static JSONObject updateNotice(JSONObject data, TNOAHttpRequest request) throws Exception {
        String id = "";
        String title = "";
        String content = "";
        String groups = "";
        String users = "";
        String comment_type = "1";
        String comment_select = "";
        int sendWechat = 0;
        int sendMsg = 0;
        String notice_time = "";
        String img = "";
        String isTop = "";
        String top_date = "";
        String type = "";
        String subType = "";
        String sql = "";
        String accessory = "";
        String link = "";
        String check_time = "";
        String unit = "";
        String create_unit = "";
        String source = "";
        int cycle = 0;
        int cycle_days = 0;
        long cycle_seconds = 0;
        String cycle_end = "";
        int isAll = 0;
        String label = "";

        if (data.containsKey("is_all") && data.getString("is_all").length() > 0) {
            isAll = data.getInteger("is_all");
        }
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(432006);
        }


        if (data.containsKey("title") && data.getString("title").length() > 0) {
            title = data.getString("title");
            sql = sql + "title=encode('" + title + "','" + RIUtil.enTitle + "'),";
        }
        if (data.containsKey("content") && data.getString("content").length() > 0) {
            content = data.getString("content");
            sql = sql + "content=encode('" + content + "','" + RIUtil.enContent + "'),";
        }
        if (data.containsKey("img") && data.getString("img").length() > 0) {
            img = data.getString("img");
            sql = sql + "img='" + img + "',";
        }
        if (data.containsKey("source") && data.getString("source").length() > 0) {
            source = data.getString("source");
            sql = sql + "source='" + source + "',";
        }

        if (data.containsKey("notice_time") && data.getString("notice_time").length() > 0) {
            notice_time = data.getString("notice_time").replace("|", " ");

            sql = sql + "notice_time='" + notice_time + "',";
        }

        if (data.containsKey("groups") && data.getString("groups").length() > 0) {
            groups = data.getString("groups");
            sql = sql + "groups='" + groups + "',";
        }
        if (data.containsKey("users") && data.getString("users").length() > 0) {
            users = data.getString("users");
            sql = sql + "users='" + users + "',";
        }

        if (data.containsKey("label") && data.getString("label").length() > 0) {
            label = data.getString("label");
            sql = sql + "label='" + label + "',";
        }
        if (data.containsKey("comment_type") && data.getString("comment_type").length() > 0) {
            comment_type = data.getString("comment_type");
            sql = sql + "comment_type='" + comment_type + "',";
        }
        if (data.containsKey("comment_select") && data.getString("comment_select").length() > 0) {
            comment_select = data.getString("comment_select");
            sql = sql + "comment_select='" + comment_select + "',";
        }
        if (data.containsKey("isTop") && data.getString("isTop").length() > 0) {
            isTop = data.getString("isTop");
            sql = sql + "isTop='" + isTop + "',";
        }

        if (data.containsKey("top_date") && data.getString("top_date").length() > 0) {
            top_date = data.getString("top_date");
            sql = sql + "top_date='" + top_date + "',";
        }

        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
            sql = sql + "type='" + type + "',";
        }


        if (data.containsKey("subType") && data.getString("subType").length() > 0) {
            subType = data.getString("subType");
            sql = sql + " subType='" + subType + "' , ";
        }

        if (data.containsKey("img") && data.getString("img").length() > 0) {
            img = data.getString("img");
            sql = sql + "img='" + img + "',";
        }


        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            int tt = RIUtil.dicts.get(unit).getIntValue("type");
            if (tt == 22 || tt == 27 || tt == 21) {//支队
                unit = "320400000000";
            } else if (tt == 23 || tt == 24 || tt == 28) {
                unit = unit.substring(0, 6) + "000000";
            } else if (tt == 25 || tt == 26) {
                unit = unit.substring(0, 8) + "0000";
            }
            sql = sql + "unit='" + unit + "',";
        }
        if (data.containsKey("accessory")) {
            accessory = data.getString("accessory");
            sql = sql + "accessory='" + accessory + "',";
        }
        if (data.containsKey("check_time") && data.getString("check_time").length() > 0) {

            check_time = data.getString("check_time").replace("|", " ");
            sql = sql + "check_time='" + check_time + "' ,";
        }

        if (data.containsKey("cycle") && data.getString("cycle").length() > 0) {
            cycle = data.getInteger("cycle");
            if (cycle > 0) {
                String date_time[] = notice_time.split(" ");

                String date = date_time[0];

                if (check_time.length() == 0) {
                    check_time = date + " 23:59:59";
                }
                if (cycle == 8) {
                    if (data.containsKey("cycle_days") && data.getString("cycle_days").length() > 0) {
                        cycle_days = data.getInteger("cycle_days");
                    } else {
                        return ErrNo.set(null, 2, "startTime bigger than endTime");
                    }
                } else {
                    cycle_days = TaskController.cycle_days_list[cycle];
                }


            } else {
                cycle_end = check_time;
            }

        }
        cycle_seconds = RIUtil.dateToStamp(check_time) - RIUtil.dateToStamp(notice_time);
        //logger.warn(RIUtil.dateToStamp(end_time) + "-" + RIUtil.dateToStamp(start_time) + "=" + cycle_seconds);
        if (cycle_seconds < 0) {
            logger.error("startTime bigger than endTime");
            return ErrNo.set(null, 2, "startTime bigger than endTime");
        }
        sql = sql + " cycle='" + cycle + "' , ";
        sql = sql + " cycle_days='" + cycle_days + "' , ";
        sql = sql + " cycle_seconds='" + cycle_seconds + "' , ";
        //  sql = sql + " is_notice=0,";


        if (data.containsKey("cycle_end") && data.getString("cycle_end").length() > 0) {
            cycle_end = data.getString("cycle_end").replace("|", " ");
            sql = sql + " cycle_end='" + cycle_end + "' , ";
        }
        if (data.containsKey("link") && data.getString("link").length() > 0) {
            link = data.getString("link");
            sql = sql + " link='" + link + "' , ";
        }
        int allp = 1;
        InfoModelHelper mysql = null;

        try {
            mysql = InfoModelPool.getModel();
            HashMap<String, String> uu = new HashMap<>();
            if (users.length() == 0 && groups.length() == 0) {
                String s = "select id from user where isdelete=1 and status=1 and position not like '%18%' and " +
                        "(unit='" + unit + "' or org='" + unit + "')";
                List<JSONObject> list = mysql.query(s);
                for (int i = 0; i < list.size(); i++) {
                    users = users + list.get(i).getString("id") + ",";
                }
                users = users.substring(0, users.length() - 1);
                sql = sql + "users='" + users + "',";
            } else {
                allp = 0;
            }

            sql = sql + "isAll='" + allp + "', is_notice=1, ";


            String sqls = "update notice set " + sql + " isdelete=1 where id='" + id + "'";
            logger.warn(sqls);
            mysql.update(sqls);

            if (groups.length() > 0) {
                uu.putAll(RIUtil.GroupsToUsers(groups, mysql));

            }
            if (users.length() > 0) {
                uu.putAll(RIUtil.StringToList(users));

            }
            if (uu.size() == 0) {
                for (Map.Entry<String, JSONObject> uone : RIUtil.users.entrySet()) {
                    String uid = uone.getKey();
                    JSONObject us = uone.getValue();
                    if (us.getString("unit").equals(unit) && !uid.equals("1")) {
                        uu.put(uid, "");
                    }

                }
            }


            if (isAll == 1) {
                sql = "select father_id from notice where id='" + id + "'";
                List<JSONObject> list = mysql.query(sql);
                if (list.size() > 0) {
                    String father_id = list.get(0).getString("father_id");
                    if (father_id == null || father_id.length() == 0) {
                        father_id = id;
                    }

                    sql = "delete from notice  where father_id='" + father_id + "' and id>'" + id + "' and is_notice=0";
                    logger.warn(sql);
                    mysql.update(sql);
                }
            }


            if (link.length() > 1) {
                String token = request.getHeader("token");
                String[] links = link.split(",");
                for (int l = 0; l < links.length; l++) {
                    sql = "select rela_id from upload where id='" + links[l] + "'";
                    String rela_id = mysql.query_one(sql, "rela_id");


                    String url = TNOAConf.get("HttpServ", "online_url") + "share";
                    logger.warn(url);
                    try {
                        JSONObject det = new JSONObject();
                        det.put("token", token);
                        det.put("fileId", rela_id);
                        det.put("sfzhList", RIUtil.HashToList(uu));
                        logger.warn(det.toString());

                        OkHttpClient client = new OkHttpClient().newBuilder().build();

                        MediaType mediaType = MediaType.parse("application/json");

                        RequestBody body = RequestBody.create(mediaType, det.toString());


                        Request ret = new Request.Builder().url(url).method("POST", body).addHeader("Content" +
                                "-Type", "application/json").build();

                        Response response = client.newCall(ret).execute();

                        String back = response.body().string();
                        logger.warn(back + "-->");


                    } catch (Exception ex) {
                        logger.error(Lib.getTrace(ex));
                    }


                }
            }
           /* if (sendWechat == 1 || sendWechat == 2) {
                sql = "delete from notice_check_log where rela_id='" + id + "'";
                mysql.update(sql);
                sql = "delete from comment where notice_id='" + id + "' and source=1";
                mysql.update(sql);
            }
            String token = request.getHeader("token");
            // TaskController.publishNotice_Task(token);*/

        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);

    }

    private static JSONObject upComment(JSONObject data) throws Exception {
        String id = "";
        String opt_user = "";
        String comment = "";

        JSONObject back = ErrNo.set(0);
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(432021);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(432021);
        }
        if (data.containsKey("comment") && data.getString("comment").length() > 0) {
            comment = data.getString("comment");
        } else {
            return ErrNo.set(432021);
        }
        InfoModelHelper mysql = null;
        HashMap<String, String> uu = new HashMap<>();
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";

            sql =
                    "update comment set comment=encode('" + comment + "','" + RIUtil.enContent + "') where id='" + id + "' " + "and isdelete=1";

            mysql.update(sql);

            UserLog userLog = new UserLog();
            userLog.log(mysql, data.getString("opt_user"), "更新评论：" + id, userLog.TYPE_OPERATE, ip);
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(432022);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject deleteComment(JSONObject data) throws Exception {
        String id = "";
        String opt_user = "";
        int isManager = 0;
        JSONObject back = ErrNo.set(0);

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(432018);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(432018);
        }
        if (data.containsKey("isManager") && data.getString("isManager").length() > 0) {
            isManager = data.getInteger("isManager");
        }
        InfoModelHelper mysql = null;
        HashMap<String, String> uu = new HashMap<>();
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            if (isManager == 1) {
                sql = "update comment set comment='该评论已被管理员删除',delete_time='" + new SimpleDateFormat("yyyy-MM-dd " +
                        "HH:mm:ss").format(new Date()) + "',delete_user='" + opt_user + "' where id='" + id + "'";
            } else {
                sql =
                        "update comment set isdelete=2,delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "',delete_user='" + opt_user + "' where id='" + id + "'";
            }
            mysql.update(sql);

            UserLog userLog = new UserLog();
            userLog.log(mysql, data.getString("opt_user"), "删除评论：" + id, userLog.TYPE_OPERATE, ip);
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(432019);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject comment(JSONObject data) throws Exception {

        String notice_id = "";
        String comment = "";
        String comment_user = "";
        String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String father_comment_id = "";
        String comment_type = "";
        String isdelete = "1";
        String source = "";


        if (data.containsKey("notice_id") && data.getString("notice_id").length() > 0) {
            notice_id = data.getString("notice_id");
        }
        if (data.containsKey("comment") && data.getString("comment").length() > 0) {
            comment = data.getString("comment");
        }
        if (data.containsKey("comment_user") && data.getString("comment_user").length() > 0) {
            comment_user = data.getString("comment_user");
        } else {
            if (data.containsKey("opt_user")) {
                comment_user = data.getString("opt_user");
            }
        }
        if (data.containsKey("create_time") && data.getString("create_time").length() > 0) {
            create_time = data.getString("create_time").replace("|", " ");
        }
        if (data.containsKey("father_comment_id") && data.getString("father_comment_id").length() > 0) {
            father_comment_id = data.getString("father_comment_id");
        }
        if (data.containsKey("comment_type") && data.getString("comment_type").length() > 0) {
            comment_type = data.getString("comment_type");
        }
        if (data.containsKey("source") && data.getString("source").length() > 0) {
            source = data.getString("source");
        }


        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            if (comment_type.equals("2")) {
                String s =
                        "select id from comment where notice_id='" + notice_id + "' and comment_user='" + comment_user + "' "
                                + "and isdelete =1 and source='" + source + "' and comment_type=2";
                List<JSONObject> l = mysql.query(s);
                if (l.size() > 0) {
                    return ErrNo.set(432020);
                }
            }

            String sql = "insert comment (notice_id,comment,comment_user,create_time," + "father_comment_id," +
                    "comment_type,isdelete,source)" + "values('" + notice_id + "',encode('" + comment + "','" + RIUtil.enContent + "'),'" + comment_user + "','" + create_time + "'," + "'" + father_comment_id + "','" + comment_type + "','" + isdelete + "','" + source + "') ";
            mysql.update(sql);
            //  logger.warn(sql);

            sql = "select id from comment where notice_id='" + notice_id + "'and comment_user='" + comment_user + "' "
                    + "and create_time='" + create_time + "' and source='" + source + "' order by create_time desc " +
                    "limit 1";
            //  logger.warn(sql);

            String id = mysql.query_one(sql, "id");
            back.put("comment_id", id);

            if (source.equals("1") || source.equals("3")) {
                sql = "select reading,readed,comment_type from notice where id='" + notice_id + "'";
                List<JSONObject> relist = mysql.query(sql);
                if (relist.size() > 0) {
                    JSONObject one = relist.get(0);
                    String reading = one.getString("reading");
                    String readed = one.getString("readed");
                    String co_type = one.getString("comment_type");
                    // logger.warn(co_type + "-" + comment_type);
                    if (co_type.equals("1") || comment_type.equals("2")) {
                        HashMap<String, String> ing = RIUtil.StringToList(reading);
                        HashMap<String, String> ed = RIUtil.StringToList(readed);

                        ing.remove(comment_user);
                        ed.put(comment_user, "");

                        reading = RIUtil.HashToList(ing).toString();
                        readed = RIUtil.HashToList(ed).toString();

                        sql =
                                "update notice set reading='" + reading + "',readed='" + readed + "' where id='" + notice_id + "'";
                        mysql.update(sql);
                        sql =
                                "insert notice_check_log(user_id,rela_id,time)values('" + comment_user + "','" + notice_id + "','" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "')";
                        mysql.update(sql);
                    }

                }

                if ("3".equals(source)) {
                    sql = "select create_user from brief where id='" + notice_id + "'";
                    String create_user = mysql.query_one(sql, "create_user");
                    wechatMsgTemp.createDingMsg(notice_id, "提起申诉", comment_user, 3, create_user, mysql, id);
                }
            }

            //关联事项完成
            if (comment_type.equals("3") && source.equals("2")) {
                sql = "select isSub,subType from task where id='" + notice_id + "'";
                List<JSONObject> tlist = mysql.query(sql);
                JSONObject tone = tlist.get(0);
                int isSub = tone.getInteger("isSub");
                if (isSub == 1) {

                    JSONObject doUpStatus = new JSONObject();
                    doUpStatus.put("id", notice_id);
                    doUpStatus.put("user_id", comment_user);
                    doUpStatus.put("opt_user", comment_user);
                    doUpStatus.put("status", 2);
                    // logger.warn(doUpStatus.toString());
                    return TaskController.upStatus(doUpStatus);

                }
            }
            //关联每日巡查提交
            if (comment_type.equals("3") && source.equals("4")) {

                JSONObject doUpStatus = new JSONObject();
                doUpStatus.put("id", notice_id);
                doUpStatus.put("opt_user", comment_user);
                doUpStatus.put("status", 2);
                doUpStatus.put("opt", "update_daily_patrol_status");
                return DailyPatrolController.updateDailyPatrolStatus(doUpStatus, ip);

            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 432009, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject deleteNotice(JSONObject data) throws Exception {
        String id = "";
        String opt_user = "";
        int isAll = 0;
        JSONObject back = ErrNo.set(0);
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(432016);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(432016);
        }

        if (data.containsKey("is_all") && data.getString("is_all").length() > 0) {
            isAll = data.getInteger("is_all");
        }
        InfoModelHelper mysql = null;
        HashMap<String, String> uu = new HashMap<>();
        try {
            mysql = InfoModelPool.getModel();
            if (isAll == 0) {
                String sql =
                        "update notice set isdelete=2,delete_time='" + new SimpleDateFormat("yyyy-MM-dd " + "HH" +
                                ":mm:ss").format(new Date()) + "',delete_user='" + opt_user + "' where id='" + id + "'";
                mysql.update(sql);
            }
            else {
                String sql = "select father_id from notice where id='" + id + "'";
                List<JSONObject> list = mysql.query(sql);
                if (list.size() > 0) {
                    String father_id = list.get(0).getString("father_id");
                    if (father_id == null || father_id.length() == 0 || father_id.equals("0")) {
                        father_id = id;
                    }

                    sql =
                            "update notice set isdelete=2,delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "',delete_user='" + opt_user + "' where ((father_id='" + father_id + "' or id='" + id + "') )";
                    logger.warn(sql);
                    mysql.update(sql);
                    sql = "select check_time from notice where father_id='" + father_id + "' and isdelete=1 order by "
                            + "check_time desc limit 1";
                    list = mysql.query(sql);
                    if (list.size() > 0) {
                        String end_time = list.get(0).getString("check_time");
                        sql =
                                "update notice set cycle_end='" + end_time + "' where father_id='" + father_id + "' " + "and"
                                        + " isdelete=1";
                        mysql.update(sql);
                    }
                }
            }

            String sql = "update upload set isMain=0 where rela_id='" + id + "'";
            mysql.update(sql);

            UserLog userLog = new UserLog();
            userLog.log(mysql, data.getString("opt_user"), "删除公告：" + id, userLog.TYPE_OPERATE, ip);
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject GetReading(JSONObject d) throws Exception {
        String id = "";
        JSONObject back = ErrNo.set(0);
        JSONObject data = d;
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(432014);
        }
        InfoModelHelper mysql = null;
        HashMap<String, String> uu = new HashMap<>();
        try {
            mysql = InfoModelPool.getModel();

            String sql = "select reading from notice where id='" + id + "' and isdelete=1 and is_notice=1";

            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                JSONObject one = mysql.query(sql).get(0);
                JSONArray readingList = new JSONArray();
                String reading = one.getString("reading");
                // logger.warn(reading);
                if (reading.length() > 0) {
                    readingList = UseridToNames(RIUtil.StringToList(reading));

                }
                back.put("reading", readingList);
            } else {
                return ErrNo.set(432015);
            }
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject updateReader(JSONObject data) throws Exception {
        String id = "";
        String user_id = "";

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(432011);
        }
        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id").trim();
        } else {
            return ErrNo.set(432011);
        }
        JSONObject back = ErrNo.set(0);

        InfoModelHelper mysql = null;
        HashMap<String, String> uu = new HashMap<>();
        try {
            mysql = InfoModelPool.getModel();

            String sql = "select reading,readed from notice where id='" + id + "' and isdelete=1 and is_notice=1";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {

                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    HashMap<String, String> reading = RIUtil.StringToList(one.getString("reading"));
                    HashMap<String, String> readed = RIUtil.StringToList(one.getString("readed"));
                    logger.warn("reading->" + reading.toString());
                    logger.warn("readed->" + readed.toString());
                    HashMap<String, String> readingn = new HashMap<>();
                    if (reading.size() > 0) {
                        for (Map.Entry<String, String> oner : reading.entrySet()) {

                            String uid = oner.getKey().trim();
                            if (uid.equals(user_id)) {
                                readingn.remove(uid);
                                readed.put(user_id, "");
                            } else {
                                readingn.put(uid, "");
                            }
                            //  logger.warn(reading.size() + "." + readingn.size());
                        }
                        //  logger.warn("reading->" + readingn.toString());
                        //logger.warn("readed->" + readed.toString());
                        sql =
                                "update notice set reading='" + RIUtil.HashToList(readingn) + "',readed='" + RIUtil.HashToList(readed) + "' where id='" + id + "'";
                        mysql.update(sql);
                        sql =
                                "insert notice_check_log(user_id,rela_id,time)values('" + user_id + "','" + id + "'," + "'" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "')";
                        mysql.update(sql);

                    }
                }
                JSONObject gg = new JSONObject();
                gg.put("id", id);
                JSONObject bb = GetReading(gg);
                JSONArray reading = bb.getJSONArray("reading");
                back.put("reading", reading);

            } else {
                return ErrNo.set(432013);
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;

    }

    private static JSONObject createNotice(JSONObject data, TNOAHttpRequest request) throws Exception {
        String title = "";
        String content = "";
        String create_user = "";
        String notice_time = "";
        String is_notice = "0";
        String reading = "";

        String groups = "";
        String users = "";
        String comment_type = "1";
        String comment_select = "";
        String isTop = "0";
        String isNew = "1";
        String top_date = "1";
        String new_date = "1";
        String type = "";
        String subType = "";
        String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String isdelete = "1";
        String link = "";
        int sendWechat = 0;
        int sendMsg = 0;
        String img = "";
        String accessory = "";
        int cycle = 0;
        int cycle_days = 0;
        long cycle_seconds = 0;
        String cycle_end = "";
        String label = "";
        String check_time = "";
        String unit = "";
        String source = "1";
        String old_author = "";
        if (data.containsKey("title") && data.getString("title").length() > 0) {
            title = data.getString("title");
        }
        if (data.containsKey("content") && data.getString("content").length() > 0) {
            content = data.getString("content");
        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
        }
        if (data.containsKey("notice_time") && data.getString("notice_time").length() > 0) {
            notice_time = data.getString("notice_time").replace("|", " ");

        } else {
            notice_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        }
        long t = RIUtil.dateToStamp(notice_time);
        t = t + 1000 * 60 * 60 * 24;
        check_time = RIUtil.stampToTime(t);
        if (data.containsKey("groups") && data.getString("groups").length() > 0) {
            groups = data.getString("groups");
        }
        if (data.containsKey("users") && data.getString("users").length() > 0) {
            users = data.getString("users");
        }
        if (data.containsKey("comment_type") && data.getString("comment_type").length() > 0) {
            comment_type = data.getString("comment_type");
        }
        if (data.containsKey("comment_select") && data.getString("comment_select").length() > 0) {
            comment_select = data.getString("comment_select");
        }
        if (data.containsKey("isTop") && data.getString("isTop").length() > 0) {
            isTop = data.getString("isTop");
        }
        if (data.containsKey("isNew") && data.getString("isNew").length() > 0) {
            isNew = data.getString("isNew");
        }
        if (data.containsKey("top_date") && data.getString("top_date").length() > 0) {
            top_date = data.getString("top_date");
        }
        if (data.containsKey("new_date") && data.getString("new_date").length() > 0) {
            new_date = data.getString("new_date");
        }
        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
        }
        if (data.containsKey("subType") && data.getString("subType").length() > 0) {
            subType = data.getString("subType");
        }
        if (data.containsKey("isWechat") && data.getString("isWechat").length() > 0) {
            sendWechat = data.getInteger("isWechat");
        }
        if (data.containsKey("isMsg") && data.getString("isMsg").length() > 0) {
            sendMsg = data.getInteger("isMsg");
        }
        if (data.containsKey("img") && data.getString("img").length() > 0) {
            img = data.getString("img");
        }
        if (data.containsKey("accessory") && data.getString("accessory").length() > 0) {
            accessory = data.getString("accessory");
        }
        if (data.containsKey("link") && data.getString("link").length() > 0) {
            link = data.getString("link");
        }
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            int tt = RIUtil.dicts.get(unit).getIntValue("type");
            if (tt == 22 || tt == 27 || tt == 21) {//支队
                unit = "320400000000";
            } else if (tt == 23 || tt == 24 || tt == 28) {
                unit = unit.substring(0, 6) + "000000";
            } else if (tt == 25 || tt == 26) {
                unit = unit.substring(0, 8) + "0000";
            }
        }

        if (data.containsKey("label") && data.getString("label").length() > 0) {
            label = data.getString("label");
        }

        if (data.containsKey("source") && data.getString("source").length() > 0) {
            source = data.getString("source");
        }
        if (data.containsKey("old_author") && data.getString("old_author").length() > 0) {
            old_author = data.getString("old_author");
        }
        if (data.containsKey("check_time") && data.getString("check_time").length() > 0) {
            check_time = data.getString("check_time").replace("|", " ");

        } else {
            long ct = RIUtil.dateToStamp(notice_time) + 24 * 60 * 60 * 1000;
            check_time = RIUtil.stampToTime(ct);
        }
        if (data.containsKey("cycle_end") && data.getString("cycle_end").length() > 0) {
            cycle_end = data.getString("cycle_end").replace("|", " ");
        } else {
            int year = Integer.parseInt(new SimpleDateFormat("yyyy").format(new Date())) + 2;
            cycle_end = year + "-12-31 23:59:59";
        }
        if (data.containsKey("cycle") && data.getString("cycle").length() > 0) {
            cycle = data.getInteger("cycle");
            if (cycle > 0) {
                String date_time[] = notice_time.split(" ");

                String date = date_time[0];

                if (check_time.length() == 0) {
                    check_time = date + " 23:59:59";
                }
                if (cycle == 8) {
                    if (data.containsKey("cycle_days") && data.getString("cycle_days").length() > 0) {
                        cycle_days = data.getInteger("cycle_days");
                    } else {
                        return ErrNo.set(433012);
                    }
                } else {
                    cycle_days = TaskController.cycle_days_list[cycle];
                }


            } else {
                cycle_end = check_time;
            }

        }
        cycle_seconds = RIUtil.dateToStamp(check_time) - RIUtil.dateToStamp(notice_time);
        logger.warn(RIUtil.dateToStamp(check_time) + "-" + RIUtil.dateToStamp(notice_time) + "=" + cycle_seconds);
        if (cycle_seconds < 0) {
            logger.error("startTime bigger than endTime");
            return ErrNo.set(null, 2, "startTime bigger than endTime");
        }

        int isAll = 1;
        InfoModelHelper mysql = null;
        HashMap<String, String> uu = new HashMap<>();
        try {
            mysql = InfoModelPool.getModel();

            // JSONArray userList = UseridToNames(uu, mysql, 1);

            if (users.length() == 0 && groups.length() == 0) {
                String sqlT = " and 1=1 ";
                if (!type.equals("5")) {
                    sqlT = " and length(isFamily)=0 ";
                }
                String s =
                        "select id from user where isdelete=1 and status=1 and (unit='" + unit + "' or org='" + unit + "') " + "and id!='1' and position not like '%18%'" + sqlT;
                List<JSONObject> list = mysql.query(s);
                for (int i = 0; i < list.size(); i++) {
                    users = users + list.get(i).getString("id") + ",";
                }
                if (users.length() > 1) {
                    users = users.substring(0, users.length() - 1);
                }
            } else {
                isAll = 0;
            }

            String sql = "insert notice (title,content,create_user,notice_time,is_notice,reading,readed,groups,users,"
                    + "comment_type,comment_select,isTop,isNew,top_date,new_date,type,create_time,isdelete," +
                    "isWechat," + "isMsg,img,isAll,accessory,link,check_time," + "cycle,cycle_days,cycle_seconds," +
                    "cycle_end," + "label," + "unit,source,subType,old_author)values(encode('" + title + "','" + RIUtil.enTitle + "')," + "encode('" + content + "','" + RIUtil.enContent + "'),'" + create_user + "','" + notice_time + "','" + is_notice + "','','','" + groups + "','" + users + "','" + comment_type + "','" + comment_select + "'," + "'" + isTop + "','" + isNew + "','" + top_date + "','" + new_date + "','" + type + "','" + create_time + "','" + isdelete + "','" + sendWechat + "','" + sendMsg + "','" + img + "','" + isAll + "','" + accessory + "','" + link + "','" + check_time + "'," + "'" + cycle + "','" + cycle_days + "','" + cycle_seconds + "','" + cycle_end + "','" + label + "','" + unit + "','" + source + "','" + subType + "','" + old_author + "') ";
            mysql.update(sql);
            sql = "select id from notice where decode(title,'" + RIUtil.enTitle + "')='" + title + "' and create_user"
                    + "='" + create_user + "' and create_time='" + create_time + "'";
            List<JSONObject> list = mysql.query(sql);
            String id = list.get(0).getString("id");
            if (img.length() > 0) {
                JSONArray imgs = JSONArray.parseArray(img);
                for (int i = 0; i < imgs.size(); i++) {
                    JSONObject one = imgs.getJSONObject(i);
                    String img_id = one.getString("id");

                    int isMain = one.getInteger("isMain");

                    sql = "update upload set rela_id='" + id + "',isMain='" + isMain + "' where id='" + img_id + "'";
                    mysql.update(sql);
                }
            }
            if (cycle > 0) {
                MakeCycleNotice(id, 10, mysql);
            }
            String token = request.getHeader("token");
            TaskController.publishNotice_Task(token);
            if (sendWechat == 1) {
                TestMsgLine send = new TestMsgLine();
                String url = TNOAConf.get("HttpServ", "notice_url") + type+"__"+id;
                logger.warn(url);
                send.sendMSG("您有一条通知通报，请查收:" + title + "-->" + url, Arrays.asList(users.split(",")));
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 432001, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

    private static void MakeCycleNotice(String id, int count, InfoModelHelper mysql) throws Exception {
        String father_id = id;
        String sql = "select notice_time,check_time,cycle_end,cycle,cycle_days,cycle_seconds,source from notice " +
                "where" + " " + "id='" + father_id + "'";
        List<JSONObject> list = mysql.query(sql);
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String notice_time = one.getString("notice_time");
            long start_time_long = RIUtil.dateToStamp(notice_time);
            String cycle_end = one.getString("cycle_end");
            String check_time = one.getString("check_time");
            int cycle_days = one.getInteger("cycle_days");
            int cycle = one.getInteger("cycle");
            int cycle_seconds = one.getInteger("cycle_seconds");
            int mark = 0;
            while (start_time_long < RIUtil.dateToStamp(cycle_end)) {
                // logger.warn(start_time + "->" + end_time);
                if (cycle != 4 && cycle != 7) {
                    notice_time = RIUtil.GetNextDateTime(notice_time, cycle_days);


                } else if (cycle == 4) {//每月
                    String d[] = notice_time.split(" ");
                    String date = d[0];
                    String time = d[1];
                    String[] days = date.split("-");
                    int month = Integer.parseInt(days[1]);
                    int day = Integer.parseInt(days[2]);
                    int year = Integer.parseInt(days[0]);
                    if (day > 28) {
                        day = 28;
                    }
                    if (month < 12) {
                        month = month + 1;
                    } else {
                        month = 1;
                        year = year + 1;
                    }
                    String monthStr = "";
                    if (month < 10) {
                        monthStr = "0" + month;
                    } else {
                        monthStr = String.valueOf(month);
                    }
                    String dayStr = "";
                    if (day < 10) {
                        dayStr = "0" + day;
                    } else {
                        dayStr = String.valueOf(day);
                    }
                    notice_time = year + "-" + monthStr + "-" + dayStr + " " + time;


                } else if (cycle == 7)//每年
                {
                    String d[] = notice_time.split(" ");
                    String date = d[0];
                    String time = d[1];
                    String[] days = date.split("-");
                    int month = Integer.parseInt(days[1]);
                    int day = Integer.parseInt(days[2]);
                    int year = Integer.parseInt(days[0]);
                    notice_time = (year + 1) + "-" + days[1] + "-" + days[2] + " " + time;
                }
                start_time_long = RIUtil.dateToStamp(notice_time);
                if (start_time_long < RIUtil.dateToStamp(cycle_end)) {
                    long end_time_long = start_time_long + cycle_seconds;
                    check_time = RIUtil.stampToTime(end_time_long);

                    // logger.warn(start_time + "->" + end_time);

                    String sqls = "insert notice (title,content,create_user,notice_time,is_notice,reading,readed," +
                            "groups,users,comment_type,comment_select," + "isTop,isNew,top_date,new_date,type," +
                            "create_time,isdelete,isWechat,isMsg,img,isAll,accessory,link,check_time," + "cycle," +
                            "cycle_days,cycle_seconds,cycle_end,father_id,source,unit)" + "select title,content," +
                            "create_user,'" + notice_time + "' as notice_time,'0' as is_notice,'','',groups,users," +
                            "comment_type,comment_select," + "isTop,isNew,top_date,new_date,type,'" + new SimpleDateFormat(
                            "yyyy-MM-dd HH:mm:ss").format(new Date()) + "' as create_time,isdelete,isWechat,isMsg," + "img,isAll,accessory,link,'" + check_time + "' as check_time," + "cycle,cycle_days," + "cycle_seconds,cycle_end,'" + father_id + "' as father_id,source,unit from notice where" + " id='" + father_id + "'";
                    logger.warn(sqls);
                    mysql.update(sqls);
                    if (count != -1) {//全部
                        mark++;
                        if (mark > count) {
                            break;
                        }
                    }
                }
            }
        }

    }

    static JSONObject getNoticeList(JSONObject data) throws Exception {
        String id = "";

        String content = "";
        String create_user = "";
        String is_notice = "1";
        String isTop = "";
        String isNew = "";
        String isWechat = "";
        String type = "";

        String user_id = "";
        int limit = 20;
        int page = 1;
        String sql = "";
        String label = "";
        String unit = "";
        String create_unit = "";

        String create_time_start = "";
        String create_time_end = "";
        String opt_user = "";
        String isRead = "";//1未读 2已读
        int tt = -1;
        int isAll = 0;


        if (data.containsKey("isAll") && data.getString("isAll").length() > 0) {
            isAll = data.getInteger("isAll");
        }

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
            sql = sql + " a.id ='" + id + "' and ";
        }

        if (data.containsKey("content") && data.getString("content").length() > 0) {
            content = data.getString("content");
            sql =
                    sql + " (decode(a.title,'" + RIUtil.enTitle + "') like'%" + content + "%' or decode(a.content,'" + RIUtil.enContent + "') like'%" + content + "%') and";
        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
            sql = sql + " a.create_user= '" + create_user + "' and ";
        }
        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");
            // sql = sql + " (a.reading like '%" + user_id + "%' or a.readed like '%" + user_id + "%') and ";
        }

        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");

        }
        if (data.containsKey("isRead") && data.getString("isRead").length() > 0) {
            isRead = data.getString("isRead");
            if (isRead.equals("1")) {
                sql = sql + " a.reading like '%" + user_id + "%'  and ";
            } else {
                sql = sql + " a.readed like '%" + user_id + "%' and ";
            }
        }

        if (data.containsKey("is_notice") && data.getString("is_notice").length() > 0) {
            is_notice = data.getString("is_notice");
        }
        sql = sql + " a.is_notice='" + is_notice + "' and ";
        if (data.containsKey("isTop") && data.getString("isTop").length() > 0) {
            isTop = data.getString("isTop");
            sql = sql + " a.isTop='" + isTop + "' and ";
        }
        if (data.containsKey("isWechat") && data.getString("isWechat").length() > 0) {
            isWechat = data.getString("isWechat");
            sql = sql + " a.isWechat='" + isWechat + "' and ";
        }
        if (data.containsKey("isNew") && data.getString("isNew").length() > 0) {
            isNew = data.getString("isNew");
            sql = sql + " a.isNew='" + isNew + "' and ";
        }

        if (isAll == 1){
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                int t = RIUtil.dicts.get(unit).getIntValue("type");
                if (t == 22 || t == 27) {//支队
//                sql = sql + "a.unit = '320400000000' and ";
                } else if (t == 23 || t == 24 || t == 28) {//所在分局和派出所和市局
                    sql = sql + "(a.unit  ='" + unit.substring(0, 6) + "000000' or a.unit='320400000000' or b.father_id = '"+unit.substring(0, 6)+"000000') and ";
                } else if (t == 25 || t == 26) {//所在派出所和所在分局和市局
                    sql = sql + "(a.unit = '" + unit.substring(0, 8) + "0000' or a.unit = '" + unit.substring(0, 6) +
                            "000000' or a.unit='320400000000') and ";
                }
            }
        }
        else {
            if (data.containsKey("create_unit") && data.getString("create_unit").length() > 0) {
                create_unit = data.getString("create_unit");
                tt = RIUtil.dicts.get(create_unit).getIntValue("type");
                if (tt == 22 || tt == 27 || tt == 21) {//支队
                    create_unit = "320400000000";
                    sql = sql + " a.unit like concat('%','" + create_unit.substring(0, 6) + "','%') and";
                } else if (tt == 23 || tt == 24 || tt == 28) {
                    create_unit = create_unit.substring(0, 6) + "000000";
                    sql = sql + " a.unit like concat('%','" + create_unit.substring(0, 6) + "','%') and";
                } else if (tt == 25 || tt == 26) {
                    create_unit = create_unit.substring(0, 8) + "0000";
                    sql = sql + " a.unit like concat('%','" + create_unit.substring(0, 8) + "','%') and";
                }

            }

            if (StringUtil.isBlank(create_unit) && data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                if (user_id.length() > 0) {
                    int t = RIUtil.dicts.get(unit).getIntValue("type");
                    if (t == 22 || t == 27) {//支队
                        sql = sql + "a.unit = '320400000000' and ";
                    } else if (t == 23 || t == 24 || t == 28) {
                        sql = sql + "(a.unit  ='" + unit.substring(0, 6) + "000000' or a.unit='320400000000') and ";
                    } else if (t == 25 || t == 26) {
                        sql =
                                sql + "(a.unit = '" + unit.substring(0, 8) + "0000' or a.unit = '" + unit.substring(0, 6) +
                                        "000000' or a.unit='320400000000') and ";
                    }
                    logger.warn(sql);
                }

            }
        }


        if (data.containsKey("label") && data.getString("label").length() > 0) {
            label = data.getString("label");
            sql = sql + " a.label ='" + label + "' and ";
        }

        String order = " case when (a.reading like concat ('%','"+ opt_user +"','%')) then 2 else 1 end desc, a.isTop desc,a.notice_time desc";

        if (data.containsKey("order") && data.getString("order").length() > 0) {
            order = data.getString("order").replace("|", " ");

        }

        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
            sql = sql + " a.type='" + type + "' and ";
        }
        String subType = "";
        if (data.containsKey("subType") && data.getString("subType").length() > 0) {
            subType = data.getString("subType");
            sql = sql + " a.subType='" + subType + "' and ";
        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
            sql = sql + " a.create_user='" + create_user + "' and ";
        }
        if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
            create_time_start = data.getString("create_time_start").replace("|", " ");
            sql = sql + " a.create_time>='" + create_time_start + "' and ";
        }
        if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
            create_time_end = data.getString("create_time_end").replace("|", " ");
            sql = sql + " a.create_time<='" + create_time_end + "' and ";
        }
        if (data.containsKey("limit") && data.getString("limit").length() > 0) {
            limit = data.getInteger("limit");

        }
        if (data.containsKey("page") && data.getString("page").length() > 0) {
            page = data.getInteger("page");

        }


        MysqlHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = new MysqlHelper("mysql");
            String sqls =
                    "select a.id,decode(a.content,'" + RIUtil.enContent + "') as content," +
                            "decode(a.title,'" + RIUtil.enTitle + "') as title," +
                            "a.create_user,a.isTop,a.isNew,a.notice_time,a.readed,a.reading,a.label,a.type,a.subType,a.cycle,a.link ,a.old_author,a.unit,b.type,b.father_id " +
                            "from notice a left join dict b on a.unit = b.id where 1=1  and " + sql + " a.isdelete=1  order by " + order ;

            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            //logger.warn(sqls);
            if (list.size() > 0) {
                if (isAll != 1){
                    if (create_unit.length() > 0) {
                        int finalTt = tt;
                        logger.warn(String.valueOf(finalTt));
                        list = list.stream().filter(ding_notice -> {
                            ding_notice.put("create_unit", "");
                            int t = 1;
                            if (ding_notice.containsKey("unit") && StringUtil.isNotBlank(ding_notice.getString("unit"))) {
                                if (RIUtil.dicts.get(ding_notice.getString("unit")).containsKey("type")) {
                                    t = RIUtil.dicts.get(ding_notice.getString("unit")).getIntValue("type");
                                }
                            }
                            if ((t == 22 || t == 27 || t == 21) && (finalTt == 22 || finalTt == 27 || finalTt == 21)) {//支队
                                return true;
                            } else if ((t == 23 || t == 24 || t == 28) && (finalTt == 23 || finalTt == 24 || finalTt == 28)) {
                                return true;
                            } else if ((t == 25 || t == 26) && (finalTt == 25 || finalTt == 26)) {
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());
                    }
                }

                for (JSONObject jsonObject : list) {
                    sqls =
                            "select * from notice_favo where favo_user = '" + opt_user + "' and notice_id = '" + jsonObject.getString("id") + "'";
                    List<JSONObject> query = mysql.query(sqls);
                    if (query.size() > 0) jsonObject.put("favo", 1);
                    else jsonObject.put("favo", 0);
                }
                back.put("count", list.size());
                list = list.stream().skip((page - 1) * limit).limit(limit).
                        collect(Collectors.toList());
                back.put("data", RelaInfoList(list, mysql, opt_user));





            } else {
                back.put("data", list);
                back.put("count", 0);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 432004, Lib.getTrace(e));
        } finally {
            mysql.close();
        }
        return back;


    }

    private static Object RelaInfoList(List<JSONObject> list, MysqlHelper mysql, String opt_user) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String id = one.getString("id");
            //create_user
            String create_user = one.getString("create_user");
//            String sql = "select id_num from user where id = '" + create_user + "' ";
//            String idNum = mysql.query_one("id_num", sql);
            one.put("create_user", RIUtil1.users1.get(create_user));

            String old_author = one.getString("old_author");

            try {
                one.put("old_author", RIUtil1.users1.get(old_author));
            } catch (Exception ex) {
                one.put("old_author", new JSONObject());
            }
            //已读
            String readed = one.getString("readed");
            if (readed.length() > 0) {
                one.put("readed", UseridToNames(RIUtil.StringToList(readed)));
            }

            //未读
            String reading = one.getString("reading");
            if (reading.length() > 0) {
                JSONArray readingJ = UseridToNames(RIUtil.StringToList(reading));
                one.put("reading", readingJ);
            } else {
                one.put("reading", new JSONArray());
            }


            String content = one.getString("content");
            if (content.length() > 300) {
                content = content.substring(0, 299);
            }
            one.put("content", content);

            //标签
            if (one.containsKey("label")) {
                String label = one.getString("label");
                if (label.length() > 0) {

                    String labelOne = RealDictNames(RIUtil.StringToList(label));

                    one.put("label", labelOne);
                    one.put("label_id", label);
                } else {
                    one.put("label", "");
                    one.put("label_id", "");
                }
            } else {
                one.put("label", "");
                one.put("label_id", "");
            }

            if (one.containsKey("unit")) {
                String unit = one.getString("unit");
                one.put("unit_name", RealDictNames(RIUtil.StringToList(unit)));
            } else {
                one.put("unit_name", "");
            }


            if (one.containsKey("type")) {
                String type = one.getString("type");
                one.put("type", RIUtil.dicts.get(type));
            } else {
                one.put("type_name", new JSONObject());
            }

            if (one.containsKey("subType") && StringUtil.isNotBlank(one.getString("subType"))) {
                String subType = one.getString("subType");
                one.put("subType", RIUtil.dicts.get(subType));
            } else {
                one.put("subType_name", new JSONObject());
            }


            //关联ding
            int count = 0;
            if (opt_user != null && opt_user.length() > 0) {
                String sql =
                        "select count(id) as count from ding_msg where readed=0 and real_id='" + id + "' and " +
                                "accepter" + "='" + opt_user + "'";
                count = mysql.query_count(sql);

            }
            one.put("ding", count);

            //reading count
//            String reading = one.getString("reading");
//            if (reading.length() > 0) {
//                one.put("reading", UseridToNames(RIUtil.StringToList(reading)).size());
//            }
            //comment

            String sql = "select count(id) as count from comment where notice_id='" + id + "' and isdelete=1 ";
            count = mysql.query_count(sql);

            one.put("comment", count);

            back.add(one);

        }
        return back;
    }

    public static JSONObject getNotice(JSONObject data) throws Exception {
        String id = "";

        String content = "";
        String create_user = "";
        String is_notice = "1";
        String isTop = "";
        String isNew = "";
        String type = "1";

        String sql = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
            sql = sql + " a.id ='" + id + "' and ";
        }

        if (data.containsKey("content") && data.getString("content").length() > 0) {
            content = data.getString("content");
            sql = sql + " (decode(a.title,'" + RIUtil.enTitle + "') like'%" + content + "%' " + "or decode(a.content,"
                    + "'" + RIUtil.enContent + "') like'%" + content + "%') and";
        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
            sql = sql + " a.create_user like '%" + create_user + "%' and ";
        }

        if (data.containsKey("is_notice") && data.getString("is_notice").length() > 0) {
            is_notice = data.getString("is_notice");
        }
        sql = sql + " a.is_notice='" + is_notice + "' and ";
        if (data.containsKey("isTop") && data.getString("isTop").length() > 0) {
            isTop = data.getString("isTop");
            sql = sql + " a.isTop='" + isTop + "' and ";
        }
        if (data.containsKey("isNew") && data.getString("isNew").length() > 0) {
            isNew = data.getString("isNew");
            sql = sql + " a.isNew='" + isNew + "' and ";
        }

        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
            sql = sql + " a.type='" + type + "' and ";
        }

        String opt_user = "";
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");

        }

        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "select a.id,a.create_user,a.notice_time,a.is_notice,a.reading,a.readed,a.groups,a.users,a" +
                            ".comment_type,a.comment_select,a.isTop,a.isNew,a.top_date,a.new_date,a.type,a.isWechat," + "isMsg,a" +
                            ".create_time,a.isBrief,a.img,a.isAll,a.mark,a.accessory,a.link,a.check_time,a" +
                            ".cycle,a.cycle_days," + "a.cycle_seconds,a.cycle_end,a.father_id,a.label,a.unit,a" +
                            ".source," + "decode(a.title," + "'" + RIUtil.enTitle + "')as " + "title," + "decode(a" +
                            ".content,'" + RIUtil.enContent + "')" + "as content,a.subType ,a.old_author from " +
                            "notice a " + "where " + "1=1 and " + sql + " a" + ".isdelete=1  order by a.isTop " +
                            "desc,a.isNew desc,a.notice_time desc";

            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            //logger.warn(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));

            } else {
                back.put("data", list);

            }

            sql = "update ding_msg set readed=1 where  real_id='" + id + "' and " + "accepter='" + opt_user + "'";
            mysql.update(sql);


        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 432004, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;


    }

    private static List<JSONObject> RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            //待阅读人
            String users = one.getString("users");
            if (users.length() > 3) {
                JSONArray userList = UseridToNames(RIUtil.StringToList(users));
                one.put("users", userList);
            }
            //待阅读圈层
            String groups = one.getString("groups");
            // logger.warn(groups);
            if (groups.length() > 0) {
                JSONArray groupList = RIUtil.GroupsToName(RIUtil.StringToList(groups), mysql);
                one.put("groups", groupList);
            }
            //已读
            String readed = one.getString("readed");
            if (readed.length() > 0) {
                JSONArray readedJ = UseridToNames(RIUtil.StringToList(readed));
                one.put("readed", readedJ);
            } else {
                one.put("readed", new JSONArray());
            }
            //未读
            String reading = one.getString("reading");
            if (reading.length() > 0) {
                logger.warn(reading);
                JSONArray readingJ = UseridToNames(RIUtil.StringToList(reading));
                one.put("reading", readingJ);
            } else {
                one.put("reading", new JSONArray());
            }

            //评论
            if (one.getInteger("comment_type") == 1) {
                one.put("comment_text", GetCommentText(one.getString("id"), mysql, 1, 1));
                one.put("comment_selects", "");
            } else if (one.getInteger("comment_type") == 2) {
                JSONArray comms = GetCommentText(one.getString("id"), mysql, 1, 1);
                comms.addAll(GetCommentText(one.getString("id"), mysql, 1, 2));
                one.put("comment_text", comms);
                one.put("comment_selects", GetCommentSelect(one.getString("id"), mysql, 1));
            }

            if (one.containsKey("type")) {
                String type = one.getString("type");
                one.put("type", RIUtil.dicts.get(type));
            } else {
                one.put("type_name", new JSONObject());
            }

            if (one.containsKey("subType")) {
                String subType = one.getString("subType");
                one.put("subType", RIUtil.dicts.get(subType));
            } else {
                one.put("subType_name", new JSONObject());
            }


            //create_user  身份证号
            String create_user = one.getString("create_user");

//            String sql = "select id_num from user where id = '" + create_user + "' ";
//            String idNum = mysql.query_one("id_num", sql);
            one.put("create_user", RIUtil1.users1.get(create_user));


            String old_author = one.getString("old_author");

            try {
                one.put("old_author", RIUtil1.users1.get(old_author));
            } catch (Exception ex) {
                one.put("old_author", new JSONObject());
            }
            //accessory
            String accessory = one.getString("accessory");
            JSONArray access = new JSONArray();
            if (accessory.length() > 0) {

                HashMap<String, String> accesss = RIUtil.StringToList(accessory);
                for (Map.Entry<String, String> a : accesss.entrySet()) {
                    String d = a.getKey();

                    String sql = "select file_name,rela_id from upload where id='" + d + "'";
                    logger.warn(sql);
                    List<JSONObject> dets = mysql.query(sql);
                    JSONObject aone = new JSONObject();
                    if (dets.size() > 0) {
                        JSONObject det = dets.get(0);

                        aone.put("file_id", d);
                        String file_name = det.getString("file_name");
                        file_name = file_name.substring(14);
                        aone.put("file_name", file_name);
                        aone.put("rela_id", det.getString("rela_id"));
                    } else {


                        aone.put("file_id", d);
                        aone.put("file_name", "");
                        aone.put("rela_id", "");
                    }
                    access.add(aone);
                }
            }
            one.put("accessory", access);
            String link = one.getString("link");
            JSONArray links = new JSONArray();
            if (link.length() > 0) {

                HashMap<String, String> accesss = RIUtil.StringToList(link);
                for (Map.Entry<String, String> a : accesss.entrySet()) {
                    String d = a.getKey();
                    String sql = "select file_name,rela_id from upload where id='" + d + "'";
                    logger.warn(sql);
                    List<JSONObject> dets = mysql.query(sql);
                    JSONObject aone = new JSONObject();
                    if (dets.size() > 0) {
                        JSONObject det = dets.get(0);

                        aone.put("file_id", d);
                        String file_name = det.getString("file_name");
                        file_name = file_name.substring(14);
                        aone.put("file_name", file_name);
                        aone.put("rela_id", det.getString("rela_id"));
                    } else {


                        aone.put("file_id", d);
                        aone.put("file_name", "");
                        aone.put("rela_id", "");
                    }
                    links.add(aone);
                }
            }
            one.put("link", links);

            //img
            String img = one.getString("img");
            JSONArray imgs = new JSONArray();
            if (img.length() > 0) {

                JSONArray imgss = JSONArray.parseArray(img);
                for (int a = 0; a < imgss.size(); a++) {
                    JSONObject ione = imgss.getJSONObject(a);
                    String d = ione.getString("id");
                    String name = RIUtil.IdToName(d, mysql, "file_name", "upload");
                    ione.put("file_name", name);
                    String path = RIUtil.IdToName(d, mysql, "file_path", "upload");
                    ione.put("file_path", path);
                    imgs.add(ione);
                }
            }
            one.put("img", imgs);
            //签收时见
            String sql = "select * from notice_check_log where rela_id='" + one.getString("id") + "' group by " +
                    "user_id" + " order by time desc";
            List<JSONObject> checks = mysql.query(sql);
            List<JSONObject> clogs = new ArrayList<>();
            if (checks.size() > 0) {
                for (int c = 0; c < checks.size(); c++) {
                    JSONObject cone = checks.get(c);
                    String user_id = cone.getString("user_id");
//                    sql = "select id_num from user where id = '" + user_id + "' ";
//                    idNum = mysql.query_one("id_num", sql);
                    cone.put("user", RIUtil1.users1.get(user_id));
                    clogs.add(cone);
                }
                one.put("check_log", clogs);
            } else {
                one.put("check_log", new ArrayList<>());
            }

            //标签
            if (one.containsKey("label")) {
                String label = one.getString("label");
                if (label.length() > 0) {

                    String labelOne = RealDictNames(RIUtil.StringToList(label));

                    one.put("label", labelOne);
                    one.put("label_id", label);
                } else {
                    one.put("label", "");
                    one.put("label_id", "");
                }
            } else {
                one.put("label", "");
                one.put("label_id", "");
            }

            if (one.containsKey("unit")) {
                String unit = one.getString("unit");
                one.put("unit_name", RealDictNames(RIUtil.StringToList(unit)));
            } else {
                one.put("unit_name", "");
            }
            back.add(one);

        }
        return back;

    }

    public static JSONArray UseridToNames(HashMap<String, String> members) throws Exception {
        //logger.warn(members.toString());
        JSONArray back = new JSONArray();
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();

            for (Map.Entry<String, String> one : members.entrySet()) {
                String idNum = one.getKey();

//            String sql = "select id_num from user where id = '" + id + "' ";
//            String idNum = mysql.query_one("id_num", sql);


                JSONObject u = RIUtil1.users1.get(idNum);
                // logger.warn(idNum + "->-" + u.toString());
                if (u != null) {
                    back.add(u);
                }

            }
        } catch (Exception ex) {

        } finally {
            InfoModelPool.putModel(mysql);
        }


        return back;

    }

    public static JSONArray GetCommentText(String id, InfoModelHelper mysql, int source, int commentType) throws Exception {
        JSONArray back = new JSONArray();
        String order = " ";
        if (source == 1) {
            order = " desc";
        }
        String sql =
                "select a.id,decode(a.comment,'" + RIUtil.enContent + "') as comment,b.name," + "a.create_time," + "b" +
                        ".position,tele_long,b.tele_sort,b.tele_home,b.police_id," + "a.comment_user,a.create_time," + "b" + ".img," +
                        "a" + ".comment_type,b.isMain,a.isdelete " + "from comment a left join user b on a" +
                        ".comment_user=b" +
                        ".id_num " + "where a.notice_id='" + id + "' and (a.isdelete=1 or a" + ".isdelete=3) " + "and" +
                        " a.source=" + source + " " + "and comment_type='" + commentType + "'" + " order by  " + "a" +
                        ".create_time" + order;
        logger.warn(sql);
        List<JSONObject> list = mysql.query(sql);
        if (list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String pos = one.getString("position");
                one.put("position_names", RealDictNames(RIUtil.StringToList(pos)));

                String comment = one.getString("comment");
                String[] comms = comment.split("\\|,");

                JSONArray comments = new JSONArray();
                for (int c = 0; c < comms.length; c++) {
                    sql = "select file_name from upload where id='" + comms[c] + "'";
                    String fileName = mysql.query_one(sql, "file_name");
                    JSONObject cone = new JSONObject();
                    if (fileName != null && fileName.length() > 0) {
                        cone.put("file_id", comms[c]);
                        sql = "select file_path from upload where id='" + comms[c] + "'";
                        String filePath = mysql.query_one(sql, "file_path");
                        cone.put("file_path", filePath);
                        cone.put("file_name", fileName);
                        comments.add(cone);
                    } else {
                        cone.put("file_id", comms[c]);
                        cone.put("file_name", "");
                        comments.add(cone);
                    }

                }
                one.put("submits", comments);

                back.add(one);
            }
        }
        return back;
    }

    public static Object GetCommentSelect(String id, InfoModelHelper mysql, int source) throws Exception {
        JSONArray back = new JSONArray();
        String sql = "";
        if (source == 1) {
            sql = "select comment_select from notice where id='" + id + "'";
        } else {
            sql = "select comment_select from task where id='" + id + "'";
        }
        //logger.warn(sql);
        List<JSONObject> list = mysql.query(sql);
        String comment_select = list.get(0).getString("comment_select");
        try {
            String selects[] = RIUtil.GetSelects(comment_select);

            for (int i = 0; i < selects.length; i++) {
                JSONObject one = new JSONObject();
                String ss =
                        "select count(id) as count from comment " + "where decode(comment,'" + RIUtil.enContent + "')"
                                + " " + "like '%" + selects[i] + "%' " + "and isdelete=1 and notice_id='" + id + "' " +
                                "and " +
                                "source=" + source + " and comment_type=2";
                //logger.warn(ss);
                list = mysql.query(ss);
                int count = list.get(0).getIntValue("count");

                one.put(selects[i], count);
                back.add(one);
            }
        } catch (Exception ex) {

        }


        //logger.warn(back.toString());
        return back;
    }
}
