package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.Lib.TNOAConf;
import HL.TNOA.wechat.ConnectWeChat;
import HL.TNOA.wechat.HttpConnection;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class PosRolDepController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());

    @RequestMapping(method = {RequestMethod.POST}, path = {"/position"})
    //@PassToken
    public JSONObject get_position(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_org_tree")) {
                return getOrgTree(request);
            } else if (opt.equals("check_txt")) {
                return getCheckText(data);
            } else if (opt.equals("get_org_next")) {
                return GetOrgNext(data);
            } else if (opt.equals("dataService")) {
                return DataService(data);
            } else if (opt.equals("get_bj")) {
                return GetBJ(data);
            } else if (opt.equals("get_mapstyle")) {
                return GetMapStyle();
            } else {
                return ErrNo.set(430001);
            }
        } else {
            return ErrNo.set(430001);
        }


    }

    private JSONObject GetMapStyle() {

        JSONObject back = new JSONObject();
        String firstFilePath = TNOAConf.get("file", "img_path") + "first.txt";
        String secondFilePath = TNOAConf.get("file", "img_path") + "second.txt";
        String all = TNOAConf.get("file", "img_path") + "mapstyle.json";
        System.out.println(all);
        String first = "";
        String seconde = "";
        String line = "";
        String ll = "";
        try {
            BufferedReader br = new BufferedReader(new FileReader(new File(all)));
            while ((line = br.readLine()) != null) {
                ll = line;
            }
            System.out.println(ll.length());
            back = JSONObject.parseObject(ll);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        }

        System.out.println(back.getString("message"));

        /*JSONObject back = new JSONObject();
        back.put("status", 0);
        back.put("message", "OK");
        JSONObject data = new JSONObject();
        JSONArray style = new JSONArray();
        JSONArray zero = new JSONArray();
        JSONArray f1 = JSONArray.parseArray(first);
        JSONArray f2 = JSONArray.parseArray(seconde);
        style.add(zero);
        style.add(f1);
        style.add(f2);
        data.put("style", style);
        back.put("data", data);*/
        return back;
    }


    private JSONObject GetBJ(JSONObject data) {
        JSONObject ret = ErrNo.set(0);
        String bh = "";
        if (data.containsKey("bh") && data.getString("bh").length() > 0) {
            bh = data.getString("bh").replace(",", "','");
        } else {
            return ErrNo.set(430003);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();

            String sql = "select * from org_bj where id in ('" + bh + "') order by id";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                ret.put("data", RealNextList(list));
            } else {
                ret.put("data", new JSONArray());

            }

            return ret;
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(430002);
        } finally {
            InfoModelPool.putModel(mysql);
        }


    }

    private List<JSONObject> RealNextList(List<JSONObject> list) {
        List<JSONObject> back = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {

            String next = "";
            JSONObject one = list.get(i);
            String id = one.getString("id");
            String type = RIUtil.dicts.get(id).getString("type");

            if (type.equals("21") || type.equals("22") || type.equals("27")) {
                JSONArray nexts = RIUtil.GetDictByType(23);

                for (int n = 0; n < nexts.size(); n++) {
                    next = next + nexts.getJSONObject(n).getString("id") + ",";
                }

            } else if (type.equals("23") || type.equals("24") || type.equals("25")) {
                JSONArray nexts = RIUtil.GetDictByFather(id);

                for (int n = 0; n < nexts.size(); n++) {
                    next = next + nexts.getJSONObject(n).getString("id") + ",";
                }
            } else if (type.equals("28")) {
                id = id.substring(0, 6) + "000000";
                JSONArray nexts = RIUtil.GetDictByFather(id);

                for (int n = 0; n < nexts.size(); n++) {
                    next = next + nexts.getJSONObject(n).getString("id") + ",";
                }
            }

            one.put("next", next);
            back.add(one);

        }
        return back;
    }

    private JSONObject DataService(JSONObject data) {
        String bh = "";
        if (data.containsKey("bh") && data.getString("bh").length() > 0) {
            bh = data.getString("bh");
        } else {
            return ErrNo.set(430003);
        }

        String url = TNOAConf.get("HttpServ", "dz_url") + bh;
        logger.warn(url);
        String back = HttpConnection.http_get(url);
        logger.warn(back);

        JSONObject ret = ErrNo.set(0);
        ret.put("data", back);
        return ret;


    }

    private JSONObject GetOrgNext(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String opt_user = data.getString("opt_user");

        String unit = "";
        try {

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");

            } else {
                JSONObject uone = RIUtil.users1.get(opt_user);
                System.out.println(opt_user);
                System.out.println(uone);
                String u = uone.getString("unit");
                System.out.println(u);
                unit = u.split(",")[0];
            }

            JSONObject done = RIUtil.dicts.get(unit);


            String type = "";
            try {
                type = done.getString("type");
            } catch (Exception ex) {
                type = "21";
            }
            String father_id = done.getString("father_id");
            if (father_id.length() < 5) {
                father_id = done.getString("id");
            }
            String id = done.getString("id");

            JSONArray next = new JSONArray();

            if (type.equals("21") || type.equals("22") || type.equals("27")) {
                next = RIUtil.GetDictByType(23);
            } else if (type.equals("23")) {
                next = RIUtil.GetDictByTypeFather(25, id);
            } else if (type.equals("25")) {
                next = RIUtil.GetDictByTypeFather(26, id);
            } else if (type.equals("24")) {
                next = RIUtil.GetDictByTypeFather(25, father_id);
            } else if (type.equals("28")) {
                father_id = father_id.substring(0, 6) + "000000";
                next = RIUtil.GetDictByTypeFather(25, father_id);
            }

            back.put("data", next);
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(430002);
        }


    }

    private JSONObject getOrgTree(TNOAHttpRequest request) {
        JSONObject back = ErrNo.set(0);
        back.put("data", RIUtil.org_tree);
        return back;


    }

    private JSONObject getCheckText(JSONObject data) {
        String date = "";
        String sql = "";
        String yymm = "";
        String start_time = "";
        String end_time = "";
        InfoModelHelper mysql = null;
        if (data.containsKey("start_time") && data.getString("start_time").length() > 0 && data.containsKey("end_time"
        ) && data.getString("end_time").length() > 0) {
            date = data.getString("start_time");
            start_time = date + " 00:00:00";
            end_time = data.getString("end_time") + " 23:59:59";
            yymm = date.replace("-", "").substring(2, 6);
        } else {
            return ErrNo.set(2);
        }

        initCheckStatic(date, start_time, end_time);
        BufferedWriter bw = null;
        try {
            mysql = InfoModelPool.getModel();
            sql = "select  name,point,time,rank,notice_all,notice_not," + "task_all,task_not " + "from check_static" + " " + "a" + " left join user b on a.user_id=b.id where time ='" + yymm + "' order by rank";
            logger.warn(sql);
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream("./zlxl_" + new SimpleDateFormat(
                        "yyyyMM").format(new Date()) + "_check.txt"), "utf-8"));
                bw.write("姓名|分数|排行|公告总数|未读数|事项总数|未完成数\n");
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String name = one.getString("name");
                    String point = one.getString("point");
                    String rank = one.getString("rank");
                    String notice_all = one.getString("notice_all");
                    String notice_not = one.getString("notice_not");
                    String task_all = one.getString("task_all");
                    String task_not = one.getString("task_not");

                    bw.write(name + "|" + point + "|" + rank + "|" + notice_all + "|" + notice_not + "|" + task_all + "|" + task_not + "\n");
                }

                if (bw != null) {
                    bw.flush();
                    bw.close();
                }
            }

            sql = "select  name,DECODE(a.title,'" + RIUtil.enTitle + "') as " + "title ,d_time,finish_time " + "from "
                    + "brief_static a left join user b on a.user_id=b.id " + "where a.create_time>='" + start_time +
                    "'" + " and a.create_time<='" + end_time + "' order by user_id";
            logger.warn(sql);
            list = mysql.query(sql);
            if (list.size() > 0) {
                bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream("./zlxl_" + new SimpleDateFormat(
                        "yyyyMM").format(new Date()) + "_check_detial.txt"), "utf-8"));
                bw.write("姓名|考核名称|应完成/签收时间|实际完成时间\n");
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String name = one.getString("name");
                    String title = one.getString("title");
                    String d_time = one.getString("d_time");
                    String finish_time = one.getString("finish_time");

                    bw.write(name + "|" + title + "|" + d_time + "|" + finish_time + "\n");
                }

                if (bw != null) {
                    bw.flush();
                    bw.close();
                }
            }
            return ErrNo.set(0);
        } catch (Exception ex) {
            return ErrNo.set(430001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private void initCheckStatic(String date, String start_time, String end_time) {
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql =
                    "delete from brief_static where create_time>='" + start_time + "' and create_time<='" + end_time + "'";
            mysql.update(sql);
            String time = date.replace("-", "").substring(2, 6);
            sql = "delete from  check_static where time='" + time + "'";
            mysql.update(sql);

            //明细
            String sqls = "select id,type,accepter,create_time,decode(title,'" + RIUtil.enTitle + "') as title," +
                    "rela_id,level from brief where type in (1,2,3,4) and create_time>='" + start_time + "' and " +
                    "create_time<='" + end_time + "' order by create_time";
            List<JSONObject> ll = mysql.query(sqls);
            if (ll.size() > 0) {
                for (int i = 0; i < ll.size(); i++) {
                    JSONObject one = ll.get(i);
                    String id = one.getString("id");
                    String type = one.getString("type");
                    String accepter = one.getString("accepter");
                    String create_time = one.getString("create_time");
                    String title = one.getString("title");
                    String rela_id = one.getString("rela_id");
                    String level = one.getString("level");
                    sqls = "select count(id) as count from brief_static where brief_id='" + id + "' and " +
                            "brief_type='" + type + "'";
                    int count = mysql.query_count(sqls);
                    if (count == 0) {
                        HashMap<String, String> accs = RIUtil.StringToList(accepter);

                        for (Map.Entry<String, String> a : accs.entrySet()) {
                            String user_id = a.getKey();
                            String d_time = "";
                            String check_time = "";
                            if (type.equals("1")) {
                                sqls = "select notice_time from notice where id='" + rela_id + "'";

                                String notice_time = mysql.query_one(sqls, "notice_time");
                                if (notice_time.length() > 5) {
                                    d_time = RIUtil.GetNextDateTime(notice_time, 1);
                                }
                                sqls = "select time from notice_check_log where rela_id='" + rela_id + "' and " +
                                        "user_id='" + user_id + "'";
                                check_time = mysql.query_one(sqls, "time");

                            }
                            if (type.equals("2")) {
                                sqls = "select end_time from task where id='" + rela_id + "'";
                                d_time = mysql.query_one(sqls, "end_time");

                                sqls = "select time from task_log where task_id='" + rela_id + "' and user_id='" + user_id + "' and task_type=2 " + "order by time desc limit 1";
                                check_time = mysql.query_one(sqls, "time");

                            }
                            if (type.equals("3")) {
                                if (title.contains("迟到")) {
                                    String create = create_time.substring(0, 10);
                                    String yest = RIUtil.GetNextDate(create, -1);
                                    sqls = "select time from attendance where user_id='" + user_id + "' and time " +
                                            "like '" + yest + "' order by time limit 1";
                                    check_time = mysql.query_one(sqls, "time");

                                } else {
                                    type = "8";

                                }
                            }

                            if (type.equals("4")) {
                                String level_name = RIUtil.IdToName(level, mysql,
                                        "decode(task_type_name,'" + RIUtil.enName + "')  as task_type_name",
                                        "task_type");
                                if (level_name.contains("分局")) {
                                    type = "5";
                                } else if (level_name.equals("市局")) {
                                    type = "6";
                                } else if (level_name.equals("省厅")) {
                                    type = "7";
                                }

                            }


                            sqls = "insert brief_static(user_id,brief_id,brief_type,create_time," + "title,rela_id," + "d_time,finish_time) " + "values('" + user_id + "','" + id + "','" + type + "','" + create_time + "'," + "encode('" + title + "','" + RIUtil.enTitle + "'),'" + rela_id + "','" + d_time + "','" + check_time + "')";
                            mysql.update(sqls);
                            logger.warn(sqls);

                        }
                    }
                }
            }


            sql = "select id from user where status=1 and isdelete=1 and id!='1'";
            List<JSONObject> list = mysql.query(sql);
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String id = one.getString("id");
                //1公告 2 事项 3 迟到 4 其他/派出所负面 扣1分
                sql = "select count(id) count from brief_static " + "where user_id='" + id + "' and create_time>='" + start_time + "' and create_time<='" + end_time + "' and (brief_type=1 or brief_type=2 or brief_type=4)";
                int count = 100 - mysql.query_count(sql);
                //5 分局负面 -2
                sql = "select count(id) count from brief_static " + "where user_id='" + id + "' and create_time>='" + start_time + "' and create_time<='" + end_time + "' and brief_type=5 ";
                count = count - mysql.query_count(sql) * 2;
                //6 市局负面和8旷工 -3
                sql = "select count(id) count from brief_static " + "where user_id='" + id + "' and create_time>='" + start_time + "' and create_time<='" + end_time + "' and (brief_type=6 ) ";
                count = count - mysql.query_count(sql) * 3;

                //7 省厅通报 -5
                sql = "select count(id) count from brief_static " + "where user_id='" + id + "' and create_time>='" + start_time + "' and create_time<='" + end_time + "' and (brief_type=7) ";
                count = count - mysql.query_count(sql) * 5;

                sql = "select count(id) count from brief_static " + "where user_id='" + id + "' and create_time>='" + start_time + "' and create_time<='" + end_time + "' and (brief_type=3) ";
                int late = mysql.query_count(sql);

                sql = "select count(id) count from brief_static " + "where user_id='" + id + "' and create_time>='" + start_time + "' and create_time<='" + end_time + "' and (brief_type=8) ";
                int not_check = mysql.query_count(sql);

                sql = "select count(id) count from brief_static " + "where user_id='" + id + "' and create_time>='" + start_time + "' and create_time<='" + end_time + "' and (brief_type>=4 and brief_type<=7) ";
                int imp = mysql.query_count(sql);
                sql = "insert check_static(user_id,point,time,late,not_check,imp) values('" + id + "','" + count +
                        "','" + time + "','" + late + "','" + not_check + "','" + imp + "')";
                mysql.update(sql);

            }

            //排名
            sql = "select id,point,user_id  from check_static where time='" + time + "' order by point desc";
            List<JSONObject> pp = mysql.query(sql);
            int lastPoint = 0;
            int lastRank = 1;
            if (pp.size() > 0) {
                for (int i = 0; i < pp.size(); i++) {
                    JSONObject one = pp.get(i);
                    String id = one.getString("id");
                    int point = one.getInteger("point");
                    String user_id = one.getString("user_id");
                    if (lastPoint == 0) {
                        lastPoint = point;
                        lastRank = 1;
                    }
                    if (lastPoint > point) {
                        lastPoint = point;
                        lastRank = lastRank + 1;
                    } else {
                        lastPoint = point;
                        lastRank = lastRank;
                    }
                    //notice
                    sql = "select count(id) as count from notice where (reading like '%" + user_id + "%' or readed " + "like '%" + user_id + "%') " + "and notice_time>='" + start_time + "' and " + "notice_time<='" + end_time + "' and isdelete=1";
                    int notice_all = mysql.query_count(sql);
                    logger.warn(sql);
                    sql = "select count(id) as count from brief_static " + "where user_id='" + user_id + "' and " +
                            "brief_type=1 and create_time>='" + start_time + "' " + "and create_time<='" + end_time + "'";
                    int notice_not = mysql.query_count(sql);
                    if (notice_not >= notice_all) {
                        notice_not = notice_all;
                    }
                    //task
                    sql = "select count(id) as count from task where (accepter like '%" + user_id + "%' or finished" + " " + "like '%" + user_id + "%' or checked like '%" + user_id + "%' ) " + "and " + "start_time>='" + start_time + "' and start_time<='" + end_time + "' and " + "isdelete=1";
                    int task_all = mysql.query_count(sql);
                    logger.warn(sql);
                    sql = "select count(id) as count from brief_static where user_id='" + user_id + "' and " +
                            "brief_type=2 and create_time>='" + start_time + "' and create_time<='" + end_time + "'";
                    int task_not = mysql.query_count(sql);
                    if (task_not >= task_all) {
                        task_not = task_all;
                    }

                    sql = "update check_static set rank='" + lastRank + "',notice_all='" + notice_all + "',notice_not"
                            + "='" + notice_not + "',task_all='" + task_all + "',task_not='" + task_not + "' where " + "id='" + id + "'";
                    mysql.update(sql);
                }
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private JSONObject getPosition(TNOAHttpRequest request) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = request.openInfoImpl();
            String sql = "select id,decode(pos_name,'" + RIUtil.enName + "') as pos_name from position where " +
                    "isdelete=1";

            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sql);
            if (list.size() > 0) {
                back.put("data", list);
                back.put("count", list.size());

            } else {
                back.put("data", list);
                back.put("count", 0);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(430002);
        } finally {

        }
        return back;
    }

    @RequestMapping(method = {RequestMethod.POST}, path = {"/wechat_temp"})
    @PassToken
    public Object get_temp(TNOAHttpRequest request) throws Exception {
        logger.warn("we.temp--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get")) {
                InfoModelHelper mysql = null;
                JSONObject back = ErrNo.set(0);
                try {
                    mysql = request.openInfoImpl();
                    String sqls = "select * from wechat_temp";
                    List<JSONObject> list = new ArrayList<>();
                    list = mysql.query(sqls);
                    back.put("data", list);
                    return back;


                } catch (Exception ex) {
                    logger.error(Lib.getTrace(ex));
                    return ErrNo.set(439003);
                } finally {

                }


            } else if (opt.equals("refresh")) {
                return refreshWechatTemp(request);
            } else {
                return ErrNo.set(439002);
            }
        } else {
            return ErrNo.set(439002);
        }


    }

    private JSONObject refreshWechatTemp(TNOAHttpRequest request) {

        InfoModelHelper mysql = null;

        try {
            mysql = request.openInfoImpl();

            ConnectWeChat.getTemp(mysql);
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(439004);
        } finally {

        }
        return ErrNo.set(0);
    }


    @RequestMapping(method = {RequestMethod.POST}, path = {"/test"})
    @PassToken
    public Object get_test(TNOAHttpRequest request) throws Exception {
        return GetMapStyle();

    }


}
