package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.Export.ExportInterface;
import HL.TNOA.Lib.Export.ExportXlsxHelper;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import static HL.TNOA.Httpd.Controller.StaticRhZfController.init;

//日志
//专项清查
@RestController
public class ZXQCController {

    private static Logger logger = LoggerFactory.getLogger(ZXQCController.class);

    @RequestMapping(method = {RequestMethod.POST}, path = {"/zxqc"})
    @PassToken
    private JSONObject getXd(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        String opt = data.getString("opt");
        if (opt.contains("get_xdgk")) {

            return getXDGK(data);

        } else if (opt.contains("get_xd_list")) {
            return GetXDList(data);
        } else if (opt.contains("get_xd_org")) {
            return GetXDOrg(data);
        } else if (opt.contains("get_xd_persons")) {
            return GetXDPersons(data);
        } else if (opt.contains("get_xd_6")) {
            return GetXD6(data);
        } else if (opt.contains("get_xd_det")) {
            return GetXDDet(data);
        } else if (opt.equals("get_xd_bin")) {
            return GetXDBin(data);
        } else if (opt.equals("get_zg_det")) {
            return GetZGDet(data);
        } else if (opt.equals("exp_fwyh")) {
            return ExpFwyh(data);
        } else if (opt.equals("exp_dwyh")) {
            return EXPDwyh(data, request);
        } else if (opt.equals("get_jly_gps")) {
            return GetJLYGPS(data, request);
        } else if (opt.equals("add_qczg")) {
            return AddQCZG(data);
        } else if (opt.equals("sta_qczg")) {
            return StaQCZG(data);
        } else if (opt.equals("get_qczg")) {
            return GetQCZGList(data);
        } else {
            return ErrNo.set(500001);
        }

    }

    public static void UpdateSql(JSONObject det, String table, int id, MysqlHelper mysql) {
        det.remove("opt");
        try {

            String sql = "";
            det.remove("opt_user");
            for (Map.Entry<String, Object> one : det.entrySet()) {
                sql = sql + " " + one.getKey() + "='" + one.getValue() + "',";
            }


            String sqls = "update " + table + " set " + sql.substring(0, sql.length() - 1) + " where id='" + id + "'";

            logger.warn(sqls);
            mysql.update(sqls);


        } catch (Exception ex) {
            System.out.println(Lib.getTrace(ex));
        } finally {
            mysql.close();
        }
    }

    private static JSONObject GetQCZGList(JSONObject data) {

        MysqlHelper mysql = null;
        JSONObject back = ErrNo.set(0);

        String sql = "";

        String SFZHM = "";
        String QCID = "";
        List<JSONObject> resList = new ArrayList<>();

        try {
            mysql = new MysqlHelper("mysql_zxqc");

            if (data.containsKey("SFZHM") && data.getString("SFZHM").length() > 0) {
                SFZHM = data.getString("SFZHM");
            } else {
                return ErrNo.set(606003);
            }
            if (data.containsKey("QCID") && data.getString("QCID").length() > 0) {
                QCID = data.getString("QCID");
            } else {
                return ErrNo.set(606003);
            }
            sql = "select * from zxqc_zg where SFZHM = '" + SFZHM + "' and QCID = '" + QCID + "' ";
            resList = mysql.query(sql);
            if (resList.size() > 0) {
                back.put("data", resList.get(0));
            } else {
                back.put("data", new JSONObject());
            }
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(606004);
        } finally {
            mysql.close();
        }
        return back;
    }

    private JSONObject StaQCZG(JSONObject data) {
        MysqlHelper mysql_zxqc = null;
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);

        String sql = "";
        String subSql = "";
        String pageSql = "";
        String fjSql = "";

        String QCID = "";
        String startTime = "";
        String endTime = "";
        String type = "";
        String jgType = "";
        String fj = "";
        String pcs = "";
        int isExp = 0;
        int limit = 20;
        int page = 1;
        int count = -1;
        int file_id = -1;

        List<JSONObject> fjExportArr = new ArrayList<>();
        List<JSONObject> pcsExportArr = new ArrayList<>();
        List<JSONObject> pcsList = new ArrayList<>();
        List<JSONObject> fjList = new ArrayList<>();
        List<JSONObject> jgList = new ArrayList<>();
        List<JSONObject> resList = new ArrayList<>();
        JSONObject res = new JSONObject();
        JSONObject countJs = new JSONObject();


        try {
            mysql_zxqc = new MysqlHelper("mysql_zxqc");
            mysql = InfoModelPool.getModel();

            if (data.containsKey("startTime") && data.getString("startTime").length() > 0) {
                startTime = data.getString("startTime");
                subSql += " and CREATE_TIME >= '" + startTime + "' ";
            }
            if (data.containsKey("endTime") && data.getString("endTime").length() > 0) {
                endTime = data.getString("endTime");
                subSql += " and CREATE_TIME <= '" + endTime + "' ";
            }

            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getIntValue("isExp");
            }

            if (data.containsKey("QCID") && data.getString("QCID").length() > 0) {
                QCID = data.getString("QCID");
                subSql += " and QCID in ('" + QCID.replace(",", "','") + "') ";
            }

            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
            }
            if (data.containsKey("fj") && data.getString("fj").length() > 0) {
                fj = data.getString("fj");
                fjSql = " and id like '" + fj.substring(0, 6) + "%' ";
            }
            if (data.containsKey("pcs") && data.getString("pcs").length() > 0) {
                pcs = data.getString("pcs");
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            pageSql = "  limit " + limit + " offset " + limit * (page - 1);

            //type: 0查分局/支队  1查派出所/大队  2查人  3 导出
            //分局支队共用FJ字段   派出所大队共用PCS字段
            if (type.equals("0")) {
                sql = "select * from dict where (type = '22' or type = '23') and isdelete = 1";
                jgList = mysql.query(sql);
                jgType = "FJ";
            } else if (type.equals("1")) {
                sql = "select * from dict where (type = '25' or type = '27') and isdelete = 1 " + fjSql;
                jgList = mysql.query(sql);
                jgType = "PCS";
            } else if (type.equals("3")) {
                sql = "select * from dict where (type = '23' or type = '22')  and isdelete = 1";
                fjList = mysql.query(sql);
                sql = "select * from dict where (type = '25' or type = '27') and isdelete = 1 ";
                pcsList = mysql.query(sql);
            }

            if (type.equals("0") || type.equals("1")) {

                for (int i = 0; i < jgList.size(); i++) {
                    JSONObject js = jgList.get(i);
                    String JGBH = js.getString("id");
                    String JGMC = js.getString("dict_name");
                    sql = "select count(*) as CDJL, sum(A1) as A1, sum(A2) as A2, sum(A3) as A3, sum(A4) as A4, sum" + "(A5) as A5, sum(A6) as A6, sum(A7) as A7, sum(A8) as A8, " + "sum(A9) as A9, sum(A10) as A10, sum(A11) as A11, sum(A12) as A12, sum(A13) as A13, sum" + "(A14) as A14, sum(A15) as A15, " + "sum(B1) as B1, sum(B2) as B2, sum(B3) as B3, sum(B4) as B4, sum(B5) as B5, sum(B6) as " + "B6, sum(B7) as B7, sum(B8) as B8, " + "sum(C1) as C1, sum(C2) as C2, sum(C3) as C3, sum(C4) as C4, sum(C5) as C5, sum(C6) as " + "C6, sum(C7) as C7, sum(C8) as C8, sum(C9) as C9 " + "from zxqc_zg where " + jgType + " = '" + JGBH + "' " + subSql;
                    //  logger.warn(sql);
                    List<JSONObject> list = mysql_zxqc.query(sql);


                    if (list.size() > 0) {
                        JSONObject one = list.get(0);
                        //筛选一遍  有数据的添加到结果列表中
                        if (one.getIntValue("CDJL") > 0) {
                            one.put("JGMC", JGMC);
                            one.put("JGBH", JGBH);
                            resList.add(one);
                        }
                    }
                }
                count = resList.size();


                //list分页
                // 计算需要显示的数据的起始索引和结束索引
                int startIndex = (page - 1) * limit;
                int endIndex = Math.min(startIndex + limit, resList.size());
                logger.warn(startIndex + "  " + endIndex);
                // 获取需要显示的数据
                resList = resList.subList(startIndex, endIndex);


                // 总计统计
                // 判断是否是根据支队查大队
                if ((fj).startsWith("00", 4)) {
                    subSql += " and JGBH like '" + fj.substring(0, 8) + "%' ";
                } else if (type.equals("1")) {
                    subSql += " and JGBH like '" + fj.substring(0, 6) + "%' ";
                }
                sql = "select count(*) as CDJL, sum(A1) as A1, sum(A2) as A2, sum(A3) as A3, sum(A4) as A4, sum(A5) " + "as A5, sum(A6) as A6, sum(A7) as A7, sum(A8) as A8, " + "sum(A9) as A9, sum(A10) as A10, sum(A11) as A11, sum(A12) as A12, sum(A13) as A13, sum(A14) " + "as A14, sum(A15) as A15, " + "sum(B1) as B1, sum(B2) as B2, sum(B3) as B3, sum(B4) as B4, sum(B5) as B5, sum(B6) as B6, " + "sum(B7) as B7, sum(B8) as B8, " + "sum(C1) as C1, sum(C2) as C2, sum(C3) as C3, sum(C4) as C4, sum(C5) as C5, sum(C6) as C6, " + "sum(C7) as C7, sum(C8) as C8, sum(C9) as C9 " + "from zxqc_zg where 1=1 " + subSql;
                List<JSONObject> countList = mysql_zxqc.query(sql);
                if (countList.size() > 0) {
                    countJs = countList.get(0);
                }

            } else if (type.equals("2")) {
                if (pcs.startsWith("00", 4)) {
                    sql = "select * from zxqc_zg where JGBH like '" + pcs.substring(0, 10) + "%'";
                    subSql += " and JGBH like '" + pcs.substring(0, 10) + "%' ";
                } else {
                    sql = "select * from zxqc_zg where JGBH like '" + pcs.substring(0, 8) + "%'";
                    subSql += " and JGBH like '" + pcs.substring(0, 8) + "%' ";
                }
                resList = mysql_zxqc.query(sql);
                count = resList.size();

                sql = "select count(*) as CDJL, sum(A1) as A1, sum(A2) as A2, sum(A3) as A3, sum(A4) as A4, sum(A5) " + "as A5, sum(A6) as A6, sum(A7) as A7, sum(A8) as A8, " + "sum(A9) as A9, sum(A10) as A10, sum(A11) as A11, sum(A12) as A12, sum(A13) as A13, sum(A14) " + "as A14, sum(A15) as A15, " + "sum(B1) as B1, sum(B2) as B2, sum(B3) as B3, sum(B4) as B4, sum(B5) as B5, sum(B6) as B6, " + "sum(B7) as B7, sum(B8) as B8, " + "sum(C1) as C1, sum(C2) as C2, sum(C3) as C3, sum(C4) as C4, sum(C5) as C5, sum(C6) as C6, " + "sum(C7) as C7, sum(C8) as C8, sum(C9) as C9 " + "from zxqc_zg where 1=1 " + subSql;
                logger.warn(sql);
                List<JSONObject> countList = mysql_zxqc.query(sql);
                if (countList.size() > 0) {
                    countJs = countList.get(0);
                }

                //填充姓名
                for (JSONObject one : resList) {
                    String idCard = one.getString("SFZHM");
                    String name = RIUtil1.users1.get(idCard).getString("name");
                    one.put("NAME", name);
                }

            } else if (type.equals("3") && isExp == 1) {

                for (int i = 0; i < fjList.size(); i++) {
                    JSONObject js = fjList.get(i);
                    String JGBH = js.getString("id");
                    String JGMC = js.getString("dict_name");
                    sql = "select count(*) as CDJL, sum(A1) as A1, sum(A2) as A2, sum(A3) as A3, sum(A4) as A4, sum" + "(A5) as A5, sum(A6) as A6, sum(A7) as A7, sum(A8) as A8, " + "sum(A9) as A9, sum(A10) as A10, sum(A11) as A11, sum(A12) as A12, sum(A13) as A13, sum" + "(A14) as A14,99(A15) as A15, " + "sum(B1) as B1, sum(B2) as B2, sum(B3) as B3, sum(B4) as B4, sum(B5) as B5, sum(B6) as " + "B6, sum(B7) as B7, sum(B8) as B8, " + "sum(C1) as C1, sum(C2) as C2, sum(C3) as C3, sum(C4) as C4, sum(C5) as C5, sum(C6) as " + "C6, sum(C7) as C7, sum(C8) as C8, sum(C9) as C9 " + "from zxqc_zg where FJ = '" + JGBH + "' " + subSql;
                    logger.warn(sql);
                    List<JSONObject> list = mysql_zxqc.query(sql);

                    if (list.size() > 0) {
                        JSONObject one = list.get(0);
                        if (one.getIntValue("CDJL") > 0) {
                            one.put("JGMC", JGMC);
                            one.put("JGBH", JGBH);
                            fjExportArr.add(one);
                        }
                    }
                }

                for (int i = 0; i < pcsList.size(); i++) {
                    JSONObject js = pcsList.get(i);
                    String JGBH = js.getString("id");
                    String JGMC = js.getString("dict_name");
                    sql = "select count(*) as CDJL, sum(A1) as A1, sum(A2) as A2, sum(A3) as A3, sum(A4) as A4, sum" + "(A5) as A5, sum(A6) as A6, sum(A7) as A7, sum(A8) as A8, " + "sum(A9) as A9, sum(A10) as A10, sum(A11) as A11, sum(A12) as A12, sum(A13) as A13, sum" + "(A14) as A14, sum(A15) as A15, " + "sum(B1) as B1, sum(B2) as B2, sum(B3) as B3, sum(B4) as B4, sum(B5) as B5, sum(B6) as " + "B6, sum(B7) as B7, sum(B8) as B8, " + "sum(C1) as C1, sum(C2) as C2, sum(C3) as C3, sum(C4) as C4, sum(C5) as C5, sum(C6) as " + "C6, sum(C7) as C7, sum(C8) as C8, sum(C9) as C9 " + "from zxqc_zg where PCS = '" + JGBH + "' " + subSql;
                    logger.warn(sql);
                    List<JSONObject> list = mysql_zxqc.query(sql);

                    if (list.size() > 0) {
                        JSONObject one = list.get(0);
                        if (one.getIntValue("CDJL") > 0) {
                            one.put("JGMC", JGMC);
                            one.put("JGBH", JGBH);
                            pcsExportArr.add(one);
                        }
                    }

                    sql = "select count(*) as CDJL, sum(A1) as A1, sum(A2) as A2, sum(A3) as A3, sum(A4) as A4, sum" + "(A5) as A5, sum(A6) as A6, sum(A7) as A7, sum(A8) as A8, " + "sum(A9) as A9, sum(A10) as A10, sum(A11) as A11, sum(A12) as A12, sum(A13) as A13, sum" + "(A14) as A14, sum(A15) as A15, " + "sum(B1) as B1, sum(B2) as B2, sum(B3) as B3, sum(B4) as B4, sum(B5) as B5, sum(B6) as " + "B6, sum(B7) as B7, sum(B8) as B8, " + "sum(C1) as C1, sum(C2) as C2, sum(C3) as C3, sum(C4) as C4, sum(C5) as C5, sum(C6) as " + "C6, sum(C7) as C7, sum(C8) as C8, sum(C9) as C9 " + "from zxqc_zg where 1=1 " + subSql;
                    List<JSONObject> countList = mysql_zxqc.query(sql);
                    if (countList.size() > 0) {
                        countJs = countList.get(0);
                        countJs.put("JGMC", "合计");
                    }
                }

                fjExportArr.add(countJs);
                pcsExportArr.add(countJs);
                JSONObject exportJson = new JSONObject();
                exportJson.put("fj", fjExportArr);
                exportJson.put("pcs", pcsExportArr);
                logger.warn("开始导出");
                file_id = exportTemplate2(exportJson).getIntValue("id");

            }

            res.put("resList", resList);
            res.put("countJs", countJs);
            res.put("file_id", file_id);

            back.put("data", res);
            back.put("count", count);
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(606002);
        } finally {
            InfoModelPool.putModel(mysql);
            mysql_zxqc.close();
        }
        return back;
    }

    private static JSONObject AddQCZG(JSONObject data) {
        MysqlHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String JGBH = "";
        String FJ = "";
        String PCS = "";
        int id = -1;

        try {
            mysql = new MysqlHelper("mysql_zxqc");

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getIntValue("id");
                String updateTime = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
                data.put("UPDATE_TIME", updateTime);
                UpdateSql(data, "zxqc_zg", id, mysql);
            } else {
                //判断是支队大队  还是分局派出所
                if (data.containsKey("JGBH") && data.getString("JGBH").length() > 0) {
                    JGBH = data.getString("JGBH");
                    if (JGBH.startsWith("00", 4)) {
                        FJ = JGBH.substring(0, 8) + "0000";
                        PCS = JGBH.substring(0, 10) + "00";
                    } else {
                        FJ = JGBH.substring(0, 6) + "000000";
                        PCS = JGBH.substring(0, 8) + "0000";
                    }
                    String CREATE_TIME = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                    data.put("FJ", FJ);
                    data.put("PCS", PCS);
                    data.put("CREATE_TIME", CREATE_TIME);
                }

                data.remove("opt");
                data.remove("opt_user");
                RIUtil.JsonInsert_mysql(data, "zxqc_zg", mysql);
            }


        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(606001);
        }
        return back;
    }


    private static JSONObject GetJLYGPS(JSONObject data, TNOAHttpRequest request) {
        MysqlHelper zxqc = null;
        JSONObject back = ErrNo.set(0);
        String orgs = "";
        String type = "16";
        try {
            zxqc = new MysqlHelper("mysql_zxqc");
            if (data.containsKey("orgs") && data.getString("orgs").length() > 0) {
                orgs = data.getString("orgs").replace(",", "','");
            } else {
                return ErrNo.set(500003);
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
            }
            String sql =
                    "select lat,lng,deviceId from zfjly_device_info where status_name='在线' and org in('" + orgs + "')"
                            + " and lat>20 and type='" + type + "'";

            List<JSONObject> list = zxqc.query(sql);
            if (list.size() > 0) {
                back.put("data", list);
            } else {
                back.put("data", new JSONArray());
            }

            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(500002);
        } finally {
            zxqc.close();
        }


    }

    private static JSONObject EXPDwyh(JSONObject data, TNOAHttpRequest request) {

        MysqlHelper zxqc = null;
        JSONObject back = ErrNo.set(0);
        String circle_ids = "  ";
        String token = request.getHeader("token");
        System.out.println("token->" + token);
        try {

            zxqc = new MysqlHelper("mysql_zxqc");
            if (data.containsKey("circle_ids") && data.getString("circle_ids").length() > 0) {
                circle_ids = data.getString("circle_ids").replace(",", "','");
            } else {
                return ErrNo.set(500003);
            }


            String sql =
                    "select YWKEY from static_zxqc_det where CIRCLE_ID in ('" + circle_ids + "') and BK>0 AND " +
                            "TYPE='单位' group by YWKEY";

            List<JSONObject> list = zxqc.query(sql);
            String yws = "";
            String id = "";
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    String yid = list.get(i).getString("YWKEY");
                    yws = yws + yid + ",";


                }
                yws = yws.substring(0, yws.length() - 1);


                String url = TNOAConf.get("Httpd", "dwyh") + "?faceKey=" + token;
                logger.warn(url);
                OkHttpClient client = new OkHttpClient();
                MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
                RequestBody body = RequestBody.create(mediaType, "jcrwbh=" + yws);
                System.out.println("jcrwbh=" + yws);
                Request req = new Request.Builder().url(url).post(body).addHeader("content-type",
                        "application/x-www" + "-form" + "-urlencoded").build();
                Response r = client.newCall(req).execute();

                if (r.isSuccessful()) {


                    byte[] buff = r.body().bytes();
                    String filePath = new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat(
                            "MM").format(new Date()) + "/";
                    String fileName = "单位检查隐患_" + System.currentTimeMillis() + ".xls";
                    String filepath = TNOAConf.get("file", "img_path") + filePath + fileName;
                    FileOutputStream out = new FileOutputStream(filepath);
                    out.write(buff);
                    out.close();

                    InfoModelHelper mysql = request.openInfoImpl();
                    try {
                        sql =
                                "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath +
                                        "'," + "'" + fileName + "',999)";

                        System.out.println(sql);
                        mysql.update(sql);
                        sql = "select * from upload where file_name='" + fileName + "' AND file_path='" + filePath +
                                "'";
                        List<JSONObject> result = mysql.query(sql);

                        JSONObject xJsonObject = result.get(0);
                        id = xJsonObject.getString("id");


                        logger.warn("id->" + id);

                        String endPoint = "http://10.34.251.34:50101";
                        String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                        String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                        String bucketName = "obs-qjjc-tyyh";
                        ObsServer obsServ = new ObsServer();
                        String obsFileName = "hl/" + filePath + fileName;
                        System.out.println(obsFileName);
                        boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, filepath);
                        logger.warn(obsFileName + "-->" + ret);
                    } catch (Exception ex) {
                        logger.error(Lib.getTrace(ex));
                    } finally {
                        InfoModelPool.putModel(mysql);
                    }

                }
            }
            back.put("file_id", id);


            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(500002);
        } finally {
            zxqc.close();
        }

    }

    private static JSONObject ExpFwyh(JSONObject data) {

        OracleHelper ora_bk_gl = null;
        MysqlHelper zxqc = null;
        JSONObject back = ErrNo.set(0);
        String circle_ids = "  ";

        try {
            zxqc = new MysqlHelper("mysql_zxqc");
            ora_bk_gl = new OracleHelper("ora_bk_gl");
            if (data.containsKey("circle_ids") && data.getString("circle_ids").length() > 0) {
                circle_ids = data.getString("circle_ids").replace(",", "','");
            } else {
                return ErrNo.set(500003);
            }

            HashMap<String, String> dicts = GetYHDIcts(ora_bk_gl);

            String sql = "select `KEY`,MC,BK,CZSJ,NAME  from static_zxqc_det where SUB='房屋隐患' AND OPT='新增' AND  " +
                    "CIRCLE_ID in ('" + circle_ids + "') " + "order BY CIRCLE_ID,CZSJ";
            List<JSONObject> list = zxqc.query(sql);

            if (list.size() > 0) {
                JSONArray dets = new JSONArray();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String bks[] = one.getString("BK").split(",");
                    String yhs = "";
                    for (int b = 0; b < bks.length; b++) {
                        String h[] = bks[b].split("\\|");
                        for (int j = 0; j < h.length; j++) {
                            String yh = dicts.get(h[j]);
                            yhs = yhs + yh + ":";
                        }
                        yhs = yhs + ";";
                    }
                    one.put("yhs", yhs);
                    one.remove("BK");
                    one.put("count", bks.length);
                    dets.add(one);
                }
                int ids = Export(dets);

                back.put("file_id", ids);
            } else {
                back.put("file_id", "");

            }

            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(500002);
        } finally {
            zxqc.close();
            ora_bk_gl.close();
        }
    }

    private static HashMap<String, String> GetYHDIcts(OracleHelper ora_bk_gl) {
        HashMap<String, String> dicts = new HashMap<>();
        try {
            String sql = "select ITEM_TEXT,ITEM_VALUE from CZQJ_YBDS.SYS_DICT_ITEM where DICT_ID>='303' AND " +
                    "DICT_ID<='313'";
            List<JSONObject> list = ora_bk_gl.query(sql);
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                dicts.put(one.getString("ITEM_VALUE"), one.getString("ITEM_TEXT"));
                System.out.println(one);

            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        }
        return dicts;
    }

    private static int Export(JSONArray datas) {


        String FileName = "房屋隐患_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        header.add("MC");
        headername.put("MC", "地址名称");
        header.add("yhs");
        headername.put("count", "隐患个数");
        header.add("count");
        headername.put("yhs", "隐患名称");
        header.add("NAME");
        headername.put("NAME", "录入人");
        header.add("CZSJ");
        headername.put("CZSJ", "录入时间");


        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
            exporthelper.write_data(datas);


            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql =
                        "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");

                try {
                    if (exporthelper != null) {
                        try {
                            exporthelper.close();
                        } catch (Exception ex) {

                        }

                    }
                } catch (Exception ex) {

                }

                logger.warn("id->" + id);
                String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;
                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
                logger.warn(obsFileName + "-->" + ret);
                logger.warn("id->" + id);
                return id;
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return 0;
        } finally {

            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }
        }
    }


    private static JSONObject GetZGDet(JSONObject data) {
        MysqlHelper zxqc = null;
        JSONObject back = ErrNo.set(0);
        String circle_ids = "  ";
        String key = " and 1=1 ";
        String police_id = " and 1=1";
        int limit = 20;
        int page = 1;
        String org = "";
        try {
            zxqc = new MysqlHelper("mysql_zxqc");
            if (data.containsKey("circle_ids") && data.getString("circle_ids").length() > 0) {
                circle_ids = data.getString("circle_ids").replace(",", "','");
            } else {
                return ErrNo.set(500003);
            }
            if (data.containsKey("key") && data.getString("key").length() > 0) {
                key = data.getString("key");
                if (key.length() == 1) {
                    key = "X" + key;
                }
                if (!key.equals("TOT")) {
                    key = " and MARK like '%" + key + "%' ";
                } else {
                    key = " and 1=1 ";
                }
            }
            if (data.containsKey("police_id") && data.getString("police_id").length() > 0) {
                police_id = data.getString("police_id");
                if (!police_id.equals("000000")) {
                    police_id = " and CZR='" + police_id + "' ";
                } else {
                    police_id = " and  1=1 ";
                }
            }
            if (data.containsKey("org") && data.getString("org").length() > 0) {
                org = data.getString("org").substring(0, 8);
                if (!org.equals("000000")) {
                    org = " and ORG like '%" + org + "%' ";
                } else {
                    org = " and  1=1 ";
                }
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            String sql =
                    "select * from static_zxqc_det where CIRCLE_ID in('" + circle_ids + "') " + key + police_id + org + " " + "order by CZSJ " + "desc  limit " + limit + " offset " + limit * (page - 1);
            logger.warn(sql);
            List<JSONObject> list = zxqc.query(sql);
            if (list.size() > 0) {
                back.put("data", RealInfouser(list));

            } else {
                back.put("data", new JSONArray());

            }
            sql = "select count(`KEY`) as count from static_zxqc_det where CIRCLE_ID in('" + circle_ids + "')" + key + police_id + org;
            int count = zxqc.query_count(sql);
            back.put("count", count);

            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(500002);
        } finally {
            zxqc.close();
        }

    }

    private static JSONObject GetXDBin(JSONObject data) {
        MysqlHelper zxqc = null;
        JSONObject back = ErrNo.set(0);
        String circle_ids = "";

        try {
            zxqc = new MysqlHelper("mysql_zxqc");
            if (data.containsKey("circle_ids") && data.getString("circle_ids").length() > 0) {
                circle_ids = data.getString("circle_ids").replace(",", "','");
            } else {
                return ErrNo.set(500003);
            }

            String sql = "select result from static_zxqc  where circle_id in('" + circle_ids + "') and " + "police_id"
                    + "='000000' ";
            List<JSONObject> list = zxqc.query(sql);
            int B = 0;
            int C = 0;
            int D = 0;
            int E = 0;
            int F = 0;
            int G = 0;
            int H = 0;
            int I = 0;
            int J = 0;
            int K = 0;
            int L = 0;
            int M = 0;
            int N = 0;
            int O = 0;
            int P = 0;
            int Q = 0;
            int R = 0;
            int S = 0;
            int T = 0;
            int U = 0;
            int V = 0;
            int W = 0;
            int X = 0;
            int Y = 0;
            int Z = 0;
            int AA = 0;
            int AB = 0;
            int AC = 0;
            int AD = 0;
            int AE = 0;
            int AF = 0;
            int AG = 0;
            int AH = 0;
            int AI = 0;
            int AJ = 0;
            int AK = 0;
            int AL = 0;
            int AM = 0;
            int AN = 0;
            int AO = 0;
            int AP = 0;
            int AQ = 0;
            int AR = 0;
            int AS = 0;
            int AT = 0;
            int AU = 0;
            int AV = 0;
            int AW = 0;
            int AX = 0;
            int AY = 0;
            int BB = 0;
            int BC = 0;
            int BD = 0;
            int BE = 0;
            int BF = 0;
            int BG = 0;
            int AZ = 0;
            int BA = 0;
            int BI = 0;
            int BJ = 0;
            int TOT = 0;


            if (list.size() > 0) {

                for (int i = 0; i < list.size(); i++) {
                    JSONObject res = list.get(i).getJSONObject("result");

                    B = B + res.getIntValue("B");
                    C = C + res.getIntValue("C");
                    D = D + res.getIntValue("D");
                    E = E + res.getIntValue("E");
                    F = F + res.getIntValue("F");
                    G = G + res.getIntValue("G");
                    H = H + res.getIntValue("H");
                    I = I + res.getIntValue("I");
                    J = J + res.getIntValue("J");
                    K = K + res.getIntValue("K");
                    L = L + res.getIntValue("L");
                    M = M + res.getIntValue("M");
                    N = N + res.getIntValue("N");
                    O = O + res.getIntValue("O");
                    P = P + res.getIntValue("P");
                    Q = Q + res.getIntValue("Q");
                    R = R + res.getIntValue("R");
                    S = S + res.getIntValue("S");
                    T = T + res.getIntValue("T");
                    U = U + res.getIntValue("U");
                    V = V + res.getIntValue("V");
                    W = W + res.getIntValue("W");
                    X = X + res.getIntValue("X");
                    Y = Y + res.getIntValue("Y");
                    Z = Z + res.getIntValue("Z");
                    AA = AA + res.getIntValue("AA");
                    AB = AB + res.getIntValue("AB");
                    AC = AC + res.getIntValue("AC");
                    AD = AD + res.getIntValue("AD");
                    AE = AE + res.getIntValue("AE");
                    AF = AF + res.getIntValue("AF");
                    AG = AG + res.getIntValue("AG");
                    AH = AH + res.getIntValue("AH");
                    AI = AI + res.getIntValue("AI");
                    AJ = AJ + res.getIntValue("AJ");
                    AK = AK + res.getIntValue("AK");
                    AL = AL + res.getIntValue("AL");
                    AM = AM + res.getIntValue("AM");
                    AN = AN + res.getIntValue("AN");
                    AO = AO + res.getIntValue("AO");
                    AP = AP + res.getIntValue("AP");
                    AQ = AQ + res.getIntValue("AQ");
                    AR = AR + res.getIntValue("AR");
                    AS = AS + res.getIntValue("AS");
                    AT = AT + res.getIntValue("AT");
                    AU = AU + res.getIntValue("AU");
                    AV = AV + res.getIntValue("AV");
                    AW = AW + res.getIntValue("AW");
                    AX = AX + res.getIntValue("AX");
                    AY = AY + res.getIntValue("AY");
                    BB = BB + res.getIntValue("BB");
                    BC = BC + res.getIntValue("BC");
                    BD = BD + res.getIntValue("BD");
                    BE = BE + res.getIntValue("BE");
                    BF = BF + res.getIntValue("BF");
                    BG = BG + res.getIntValue("BG");
                    AZ = AZ + res.getIntValue("AZ");
                    BA = BA + res.getIntValue("BA");
                    BI = BI + res.getIntValue("BI");
                    BJ = BJ + res.getIntValue("BJ");
                    TOT = TOT + res.getIntValue("TOT");


                }

            }

            JSONObject datas = new JSONObject();
            datas.put("B", B);
            datas.put("C", C);
            datas.put("D", D);
            datas.put("E", E);
            datas.put("F", F);
            datas.put("G", G);
            datas.put("H", H);
            datas.put("I", I);
            datas.put("J", J);
            datas.put("K", K);
            datas.put("L", L);
            datas.put("M", M);
            datas.put("N", N);
            datas.put("O", O);
            datas.put("P", P);
            datas.put("Q", Q);
            datas.put("R", R);
            datas.put("S", S);
            datas.put("T", T);
            datas.put("U", U);
            datas.put("V", V);
            datas.put("W", W);
            datas.put("X", X);
            datas.put("Y", Y);
            datas.put("Z", Z);
            datas.put("AA", AA);
            datas.put("AB", AB);
            datas.put("AC", AC);
            datas.put("AD", AD);
            datas.put("AE", AE);
            datas.put("AF", AF);
            datas.put("AG", AG);
            datas.put("AH", AH);
            datas.put("AI", AI);
            datas.put("AJ", AJ);
            datas.put("AK", AK);
            datas.put("AL", AL);
            datas.put("AM", AM);
            datas.put("AN", AN);
            datas.put("AO", AO);
            datas.put("AP", AP);
            datas.put("AQ", AQ);
            datas.put("AR", AR);
            datas.put("AS", AS);
            datas.put("AT", AT);
            datas.put("AU", AU);
            datas.put("AV", AV);
            datas.put("AW", AW);
            datas.put("AX", AX);
            datas.put("AY", AY);
            datas.put("BB", BB);
            datas.put("BC", BC);
            datas.put("BD", BD);
            datas.put("BE", BE);
            datas.put("BF", BF);
            datas.put("BG", BG);
            datas.put("AZ", AZ);
            datas.put("BA", BA);
            datas.put("BI", BI);
            datas.put("BJ", BJ);
            datas.put("TOT", TOT);


            back.put("data", datas);

            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(500002);
        } finally {
            zxqc.close();
        }

    }

    public static JSONObject GetXDDet(JSONObject data) {


        MysqlHelper zxqc = null;
        String circle_ids = "";
        String last_time = "";
        String timeSql = "  1=1 ";

        JSONObject back = ErrNo.set(0);
        try {

            zxqc = new MysqlHelper("mysql_zxqc");
            if (data.containsKey("circle_ids") && data.getString("circle_ids").length() > 0) {
                circle_ids = data.getString("circle_ids").replace(",", "','");
            } else {
                return ErrNo.set(500003);
            }


            if (data.containsKey("last_time") && data.getString("last_time").length() > 0) {
                last_time = data.getString("last_time");
                timeSql = "  CZSJ>'" + last_time + "'";


            }
            String sql = "select * from static_zxqc_det where " + timeSql + " and CIRCLE_ID IN('" + circle_ids + "') "
                    + "order by " + "CZSJ desc";
            List<JSONObject> list = zxqc.query(sql);
            if (list.size() > 0) {
                back.put("data", RealInfouser(list));
            } else {
                back.put("data", new ArrayList<>());
            }

            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(500002);
        } finally {

            zxqc.close();
        }

    }

    private static List<JSONObject> RealInfouser(List<JSONObject> list) {
        List<JSONObject> back = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String czr = one.getString("CZR");

            String org = "";
            try {
                org = one.getString("ORG").substring(0, 8) + "0000";
                org = RIUtil.dicts.get(org).getString("dict_name");
            } catch (Exception ex) {
                logger.warn(czr);
            }
            one.put("ORG", org);

            back.add(one);
        }
        return back;
    }

    private static JSONObject GetXD6(JSONObject data) {
        MysqlHelper zxqc = null;
        JSONObject back = ErrNo.set(0);
        String circle_ids = "";

        try {
            zxqc = new MysqlHelper("mysql_zxqc");
            if (data.containsKey("circle_ids") && data.getString("circle_ids").length() > 0) {
                circle_ids = data.getString("circle_ids").replace(",", "','");
            } else {
                return ErrNo.set(500003);
            }

            String sql = "select result from static_zxqc  where circle_id in('" + circle_ids + "') and " + "police_id"
                    + "='000000' ";
            List<JSONObject> list = zxqc.query(sql);
            int person = 0;
            int house = 0;
            int xc = 0;
            int dw = 0;
            int mdjf = 0;
            int zdry = 0;
            int total = 0;
            int yh = 0;
            if (list.size() > 0) {

                for (int i = 0; i < list.size(); i++) {
                    JSONObject res = list.get(i).getJSONObject("result");

                    person =
                            person + res.getIntValue("C") + res.getIntValue("E") + res.getIntValue("F") + res.getIntValue("H") + res.getIntValue("I") + res.getIntValue("G") + res.getIntValue("J");
                    house = house + res.getIntValue("R") + res.getIntValue("S") + res.getIntValue("U") + res.getIntValue("V") + res.getIntValue("B") + res.getIntValue("T") + res.getIntValue("W");
                    xc = xc + res.getIntValue("D");
                    yh = yh + res.getIntValue("X") + res.getIntValue("Y") + res.getIntValue("AA") + res.getIntValue(
                            "AC") + res.getIntValue("AE") + res.getIntValue("AG") + res.getIntValue("AI") + res.getIntValue("AK") + res.getIntValue("AM") + res.getIntValue("AO") + res.getIntValue("AQ") + res.getIntValue("AS") + res.getIntValue("AU") + res.getIntValue("AW") + res.getIntValue("AY") + res.getIntValue("BA") + res.getIntValue("BC") + res.getIntValue("BE") + res.getIntValue("BG") + res.getIntValue("BI") + res.getIntValue("BJ");
                    dw = dw + res.getIntValue("Z") + res.getIntValue("AB") + res.getIntValue("AD") + res.getIntValue(
                            "AF") + res.getIntValue("AH") + res.getIntValue("AJ") + res.getIntValue("AL") + res.getIntValue("AN") + res.getIntValue("AP") + res.getIntValue("AR") + res.getIntValue("AT") + res.getIntValue("AV") + res.getIntValue("AX") + res.getIntValue("AZ") + res.getIntValue("BB") + res.getIntValue("BD") + res.getIntValue("BF");
                    total = total + res.getIntValue("TOT");

                    zdry =
                            zdry + res.getIntValue("M") + res.getIntValue("N") + res.getIntValue("O") + res.getIntValue(
                                    "P") + res.getIntValue("Q");
                }

            }

            JSONObject datas = new JSONObject();
            datas.put("person", person);
            datas.put("house", house);
            datas.put("xc", xc);
            datas.put("dw", dw);
            datas.put("fxyh", yh);
            datas.put("total", total);
            datas.put("zdry", zdry);
            back.put("data", datas);
            System.out.println(back);

            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(500002);
        } finally {
            zxqc.close();
        }

    }

    private static JSONObject GetXDPersons(JSONObject data) {
        MysqlHelper zxqc = null;
        JSONObject back = ErrNo.set(0);
        String circle_ids = "";

        try {
            zxqc = new MysqlHelper("mysql_zxqc");
            if (data.containsKey("circle_ids") && data.getString("circle_ids").length() > 0) {
                circle_ids = data.getString("circle_ids").replace(",", "','");
            } else {
                return ErrNo.set(500003);
            }

            String sql = "select police_id,SUBSTR(org FROM 1 FOR 8) as org,police_name,circle_id from static_zxqc  " +
                    "where " + "circle_id in('" + circle_ids + "') and police_id!='000000' group by police_id  order " +
                    "by " + "police_id";
            List<JSONObject> list = zxqc.query(sql);
            if (list.size() > 0) {
                back.put("data", RealInfo_police(list, zxqc));

            } else {
                back.put("data", new JSONArray());
            }
            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(500002);
        } finally {
            zxqc.close();
        }

    }

    private static JSONObject GetXDOrg(JSONObject data) {
        MysqlHelper zxqc = null;
        JSONObject back = ErrNo.set(0);
        String circle_ids = "";

        try {
            zxqc = new MysqlHelper("mysql_zxqc");
            if (data.containsKey("circle_ids") && data.getString("circle_ids").length() > 0) {
                circle_ids = data.getString("circle_ids").replace(",", "','");
            } else {
                return ErrNo.set(500003);
            }

            String sql =
                    "select SUBSTR(org FROM 1 FOR 8) as orgd ,count(police_id) as count from static_zxqc where " +
                            "circle_id in ('" + circle_ids + "') and org!='000000000000' group by orgd ";
            List<JSONObject> list = zxqc.query(sql);
            if (list.size() > 0) {
                back.put("data", RealInfo_orgName(list, zxqc));

            } else {
                back.put("data", new JSONArray());
            }
            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(500002);
        } finally {
            zxqc.close();
        }

    }

    public static JSONObject GetXDList(JSONObject data) {
        MysqlHelper sso = null;
        MysqlHelper zxqc = null;
        JSONObject back = ErrNo.set(0);
        String start_time = "";
        String end_time = "";
        String unit = "";
        String usql = "";
        String circils = "";
        try {
            sso = new MysqlHelper("mysql_sso");
            zxqc = new MysqlHelper("mysql_zxqc");
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time");
            } else {
                start_time = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            }
            start_time = start_time + " 00:00:00";

            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time");
            } else {
                end_time = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            }
            end_time = end_time + " 23:59:59";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject done = RIUtil.dicts.get(unit);
                String type = done.getString("type");
                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    unit = unit.substring(0, 4);
                } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    unit = unit.substring(0, 6);

                } else if (type.equals("25")) {
                    unit = unit.substring(0, 8);
                } else if (type.equals("26")) {

                }

                String sql = "select circle_id from static_zxqc where org like '%" + unit + "%' group by circle_id";
                logger.warn(sql);
                List<JSONObject> list = zxqc.query(sql);
                if (list.size() > 0) {
                    for (int i = 0; i < list.size(); i++) {
                        JSONObject one = list.get(i);
                        circils = circils + one.getString("circle_id") + "','";
                    }

                }
                if (circils.length() > 3) {
                    usql = " and id in ('" + circils.substring(0, circils.length() - 3) + "')";
                } else {
                    usql = " and id in ('" + circils + "')";
                }
            }

            String sql =
                    "select id,name,create_time,start_time,end_time from circle where type=1 and is_delete=0 " + usql;
            List<JSONObject> list = GetDateBetween(sso, start_time, end_time, sql);
            //  logger.warn(list.toString());
            if (list.size() > 0) {
                back.put("data", RealInfo_org(list, zxqc));

            } else {
                back.put("data", new JSONArray());
            }
            // System.out.println(back);
            return back;

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(500002);
        } finally {
            sso.close();
            zxqc.close();
        }

    }

    private static List<JSONObject> RealInfo_org(List<JSONObject> list, MysqlHelper mysql2) throws Exception {
        List<JSONObject> back = new ArrayList<JSONObject>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String id = one.getString("id");
            String sql = "select SUBSTR(org FROM 1 FOR 8) as orgd from static_zxqc where circle_id ='" + id + "' " +
                    "and " + "org!='000000000000' group by orgd";
            // logger.warn(sql);
            List<JSONObject> ll = mysql2.query(sql);
            String orgs = "";
            if (ll.size() > 0) {
                for (int l = 0; l < ll.size(); l++) {

                    try {
                        String org = ll.get(l).getString("orgd") + "0000";
                        // logger.warn(org);
                        String org_name = RIUtil.dicts.get(org).getString("dict_name");
                        // logger.warn(org_name);
                        orgs = orgs + org_name + ",";
                    } catch (Exception ex) {
                        logger.error(Lib.getTrace(ex));
                    }
                }
                orgs = orgs.substring(0, orgs.length() - 1);
            }
            one.put("orgs", orgs);
            // System.out.println(one);
            back.add(one);

        }

        return back;
    }

    private static List<JSONObject> RealInfo_orgName(List<JSONObject> list, MysqlHelper mysql2) throws Exception {
        List<JSONObject> back = new ArrayList<JSONObject>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String org = one.getString("orgd") + "0000";
            String orgName = RIUtil.dicts.get(org).getString("dict_name");
            one.put("name", orgName);
            one.put("id", org);

            back.add(one);

        }

        return back;
    }

    private static List<JSONObject> RealInfo_police(List<JSONObject> list, MysqlHelper mysql2) throws Exception {
        List<JSONObject> back = new ArrayList<JSONObject>();
        MysqlHelper my_sso = null;
        MysqlHelper mysql = null;
        try {
            my_sso = new MysqlHelper("mysql_sso");
            mysql = new MysqlHelper("mysql");
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String police_id = one.getString("police_id");
                String orgName = "";
                String org = "";
                try {
                    org = one.getString("org") + "0000";
                    orgName = RIUtil.dicts.get(org).getString("dict_name");
                } catch (Exception ex) {
                    logger.warn(one.toString());

                }
                one.put("org_name", orgName);
                one.put("org", org);
                String sql = "select id from user where police_id='" + police_id + "'";
                String user_id = mysql.query_one(sql, "id");
                String circle_id = one.getString("circle_id");
                sql = "select start_time from circle where id='" + circle_id + "'";
                String startTime = my_sso.query_one(sql, "start_time");
                one.put("start_time", startTime);

                String tele = "";
                try {
                    tele = RIUtil.users.get(user_id).getString("tele_long");
                } catch (Exception ex) {
                    logger.warn(police_id + "," + user_id);
                }
                one.put("tele", tele);
                back.add(one);

            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            my_sso.close();
            mysql.close();
        }
        return back;
    }

    private static JSONObject getXDGK(JSONObject data) {
        MysqlHelper sso = null;
        MysqlHelper zxqc = null;
        JSONObject back = ErrNo.set(0);
        String date = "";
        String unit = "";
        String usql = " ";
        String circils = "";
        try {
            sso = new MysqlHelper("mysql_sso");
            ;
            zxqc = new MysqlHelper("mysql_zxqc");
            if (data.containsKey("date") && data.getString("date").length() > 0) {
                date = data.getString("date");
            } else {
                date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject done = RIUtil.dicts.get(unit);
                String type = done.getString("type");
                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    unit = unit.substring(0, 4);
                } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    unit = unit.substring(0, 6);

                } else if (type.equals("25")) {
                    unit = unit.substring(0, 8);
                } else if (type.equals("26")) {

                }

                String sql = "select circle_id from static_zxqc where org like '%" + unit + "%' group by circle_id";
                //logger.warn(sql);
                List<JSONObject> list = zxqc.query(sql);
                if (list.size() > 0) {
                    for (int i = 0; i < list.size(); i++) {
                        JSONObject one = list.get(i);
                        circils = circils + one.getString("circle_id") + "','";
                    }

                }
                if (circils.length() > 3) {
                    usql = " and id in ('" + circils.substring(0, circils.length() - 3) + "')";
                } else {
                    usql = " and id in ('" + circils + "')";
                }
            }

            // 总行动数
            String sql = "select count(id) as count from circle where type=1 and is_delete=0 " + usql;
            // logger.warn(sql);
            int count = sso.query_count(sql);
            JSONObject datas = new JSONObject();
            datas.put("total", count);

            // 本月
            String month = date.substring(0, 7);
            sql = "select count(id) as count from circle where type=1 and is_delete=0 and (start_time like '%" + month + "%' or end_time like '%" + month + "%') " + usql;
            count = sso.query_count(sql);
            datas.put("month", count);

            String week_start = RIUtil.getWeekStart(date) + " 00:00:00";
            String week_end = RIUtil.GetNextDate(week_start, +6) + " 23:59:59";


            sql = "select id,start_time,end_time from circle where type=1 and is_delete=0 " + usql;
            // logger.warn(sql);
            List<JSONObject> list = GetDateBetween(sso, week_start, week_end, sql);
            count = list.size();
            datas.put("week", count);

            sql = "select id,name,create_time,start_time,end_time from circle where type=1 and is_delete=0 " + usql;
            //logger.warn(sql);
            list = GetDateBetween(sso, date + " 00:00:00", date + " 23:59:59", sql);
            datas.put("today", list.size());

            //  System.out.println(datas);
            back.put("data", datas);
            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(500002);
        } finally {
            sso.close();
            zxqc.close();
        }

    }

    private static List<JSONObject> GetDateBetween(MysqlHelper mysql2, String week_start, String week_end, String sql) {
        List<JSONObject> back = new ArrayList<>();

        logger.warn(week_end + "-->" + week_start);
        try {
            long s = RIUtil.dateToStamp(week_start);
            long e = RIUtil.dateToStamp(week_end);
            List<JSONObject> list = mysql2.query(sql);
            // System.out.println(list.size());
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);


                    String st = one.getString("start_time");
                    long start = RIUtil.dateToStamp(st);
                    String et = one.getString("end_time");
                    long end = RIUtil.dateToStamp(et);

                    //logger.warn(one.toString());
                    if (((s >= start && s <= end) || (e >= start && e <= end)) || ((start >= s && start <= e) || (end >= s && end <= e))) {
                        // logger.warn(one.toString());

                        back.add(one);
                    }
                }
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        }
        // logger.warn(back.toString());
        return back;
    }

    private JSONObject exportTemplate2(JSONObject data) {

        String sql = "";
        FileOutputStream fos = null;
        SXSSFWorkbook sxssfWorkbook = null;
        InfoModelHelper info = null;
        try {
            info = InfoModelPool.getModel();
            sxssfWorkbook = new SXSSFWorkbook();

            CellStyle cellStyle = initCellStyle(sxssfWorkbook);
            cellStyle.setAlignment(HorizontalAlignment.CENTER);

//            //纵向显示
//            CellStyle cellStyle1 = initCellStyle(sxssfWorkbook);
//            cellStyle1.setAlignment(HorizontalAlignment.CENTER);
//            cellStyle1.setRotation((short) 255);

            JSONArray fj = data.getJSONArray("fj");
            JSONArray pcs = data.getJSONArray("pcs");
            final int treeLength1 = getTreeLength(pcs);
            final int treeLength2 = getTreeLength(fj);

            infoSheet(sxssfWorkbook, fj, "分局", treeLength2, cellStyle);
            infoSheet(sxssfWorkbook, pcs, "派出所", treeLength1, cellStyle);

            String FileName = "清查战果统计_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";
            String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;
            init(endPath);

            fos = new FileOutputStream(endPath);
            sxssfWorkbook.write(fos);

            String sqls =
                    "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "','" + FileName + "',999)";
            logger.warn(sqls);
            info.update(sqls);
            sql = "select * from upload where file_name='" + FileName + "'";
            List<JSONObject> result = info.query(sql);
            JSONObject xJsonObject = result.get(0);
            int id = xJsonObject.getIntValue("id");

            logger.warn("id->" + id);
            JSONObject back = new JSONObject();
            back.put("id", id);

            try {
                if (fos != null) {
                    try {
                        fos.close();
                    } catch (Exception ex) {

                    }

                }
                if (sxssfWorkbook != null) {
                    try {
                        sxssfWorkbook.close();

                    } catch (Exception ex) {

                    }
                }
            } catch (Exception ex) {

            }


            String endPoint = "http://10.34.251.34:50101";
            String ak = "Q7D3OUVIRBEZEB4RWVSJ";
            String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
            String bucketName = "obs-qjjc-tyyh";
            ObsServer obsServ = new ObsServer();
            String obsFileName = "hl/" + filePath + FileName;
            System.out.println(obsFileName);
            boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
            logger.warn(obsFileName + "-->" + ret);
            logger.warn("id->" + id);
            return back;

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(480012);
        } finally {
            InfoModelPool.putModel(info);
            try {
                if (sxssfWorkbook != null) {
                    sxssfWorkbook.dispose();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private static CellStyle initCellStyle(SXSSFWorkbook sxssfWorkbook) {
        //************** 样式一 *******************//
        CellStyle cellStyle = sxssfWorkbook.createCellStyle();
        cellStyle.setWrapText(true);
        //对齐方式
        //设置水平对齐方式
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        //设置垂直对齐方式
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //设置边框
//        cellStyle.setBorderTop(BorderStyle.THIN);
//        cellStyle.setBorderRight(BorderStyle.THIN);
//        cellStyle.setBorderBottom(BorderStyle.THIN);
//        cellStyle.setBorderLeft(BorderStyle.THIN);
        // 设置字体
        Font font = sxssfWorkbook.createFont();
        font.setFontName("Segoe UI");
        font.setFontHeightInPoints((short) 9);
        font.setBold(true);
        cellStyle.setFont(font);
        //************** 样式一 *******************//
        return cellStyle;
    }

    private static int getTreeLength(JSONArray tree) {
        int l = 0;
        int rl = 0;
        for (int i = 0; i < tree.size(); i++) {
            JSONObject o = tree.getJSONObject(i);
//            l = 1;
            rl = Math.max(rl, l);
            if (o.containsKey("next") && o.getJSONArray("next").size() > 0) {
                JSONArray next = o.getJSONArray("next");
                l = 2;
                rl = Math.max(rl, l);
                for (int j = 0; j < next.size(); j++) {
                    JSONObject two = next.getJSONObject(j);
                    if (two.containsKey("next") && two.getJSONArray("next").size() > 0) {
                        JSONArray next2 = two.getJSONArray("next");
                        l = 3;
                        rl = Math.max(rl, l);
                        for (int k = 0; k < next2.size(); k++) {
                            JSONObject three = next2.getJSONObject(k);
                            if (three.containsKey("next") && three.getJSONArray("next").size() > 0) {
                                rl = 4;
                            }
                        }
                    }
                }
            }
        }
        return rl;
    }

    private static void infoSheet(SXSSFWorkbook sxssfWorkbook, JSONArray arr, String name, int treeLength,
                                  CellStyle cellStyle) {
        // 获取SXSSFWorkbook实例
        Sheet sheet = sxssfWorkbook.createSheet(name);

        sheet.setColumnWidth(0, 240 * 20);

        CellStyle cellStyle1 = initCellStyle(sxssfWorkbook);
        cellStyle1.setAlignment(HorizontalAlignment.CENTER);
        cellStyle1.setRotation((short) 255);


        CellStyle cellStyle2 = initCellStyle(sxssfWorkbook);
        cellStyle2.setAlignment(HorizontalAlignment.CENTER);  // 设置水平居中
        cellStyle2.setVerticalAlignment(VerticalAlignment.CENTER);  // 设置垂直居中
        Font font = sxssfWorkbook.createFont();
        font.setFontHeightInPoints((short) 20);
        cellStyle2.setFont(font);

        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 33)); // 0-based index
        Row row1 = sheet.createRow(0);
        Cell cellA1 = row1.createCell(0);
        cellA1.setCellValue("清查行动战果统计");
        cellA1.setCellStyle(cellStyle2);


        sheet.addMergedRegion(new CellRangeAddress(1, 3, 0, 0)); // 0-based index
        row1 = sheet.createRow(1);
        Cell cellA2 = row1.createCell(0);
        cellA2.setCellValue("各辖市局、分局、有关警种");
        cellA2.setCellStyle(cellStyle1);

        sheet.addMergedRegion(new CellRangeAddress(1, 3, 1, 1)); // 0-based index
        Cell cellB2 = row1.createCell(1);
        cellB2.setCellValue("出动总警力（人次）");
        cellB2.setCellStyle(cellStyle1);

        sheet.addMergedRegion(new CellRangeAddress(1, 1, 2, 16)); // 0-based index
        Cell cellC2 = row1.createCell(2);
        cellC2.setCellValue("重点场所行业");
        cellC2.setCellStyle(cellStyle);

        sheet.addMergedRegion(new CellRangeAddress(2, 2, 2, 6)); // 0-based index
        Row row2 = sheet.createRow(2);
        Cell cellC3 = row2.createCell(2);
        cellC3.setCellValue("重点场所");
        cellC3.setCellStyle(cellStyle);

        sheet.addMergedRegion(new CellRangeAddress(2, 2, 7, 11)); // 0-based index
        Cell cellH3 = row2.createCell(7);
        cellH3.setCellValue("住宿业");
        cellH3.setCellStyle(cellStyle);

        sheet.addMergedRegion(new CellRangeAddress(2, 2, 12, 16)); // 0-based index
        Cell cellM3 = row2.createCell(12);
        cellM3.setCellValue("重点行业");
        cellM3.setCellStyle(cellStyle);

        Row row3 = sheet.createRow(3);

        Cell cellC4 = row3.createCell(2);
        cellC4.setCellValue("检查歌舞娱乐、洗浴按摩、私人影院、电竞酒店、剧本杀、密室逃脱等重点场所单位（家）");
        cellC4.setCellStyle(cellStyle1);

        Cell cellD4 = row3.createCell(3);
        cellD4.setCellValue("查处违规经营行为（起）");
        cellD4.setCellStyle(cellStyle1);

        Cell cellE4 = row3.createCell(4);
        cellE4.setCellValue("查处违法犯罪行为（起）");
        cellE4.setCellStyle(cellStyle1);

        Cell cellF4 = row3.createCell(5);
        cellF4.setCellValue("督促整改安全隐患（处）");
        cellF4.setCellStyle(cellStyle1);

        Cell cellG4 = row3.createCell(6);
        cellG4.setCellValue("关停公共娱乐服务场所（家）");
        cellG4.setCellStyle(cellStyle1);

        Cell cellH4 = row3.createCell(7);
        cellH4.setCellValue("检查旅馆、民宿、网约房、留宿浴室等住宿业单位（家）");
        cellH4.setCellStyle(cellStyle1);

        Cell cellI4 = row3.createCell(8);
        cellI4.setCellValue("查处违规经营行为（起）");
        cellI4.setCellStyle(cellStyle1);

        Cell cellJ4 = row3.createCell(9);
        cellJ4.setCellValue("查处违法犯罪行为（起）");
        cellJ4.setCellStyle(cellStyle1);

        Cell cellK4 = row3.createCell(10);
        cellK4.setCellValue("督促整改安全隐患（处）");
        cellK4.setCellStyle(cellStyle1);

        Cell cellL4 = row3.createCell(11);
        cellL4.setCellValue("依法处罚违规经营单位（家）");
        cellL4.setCellStyle(cellStyle1);

        Cell cellM4 = row3.createCell(12);
        cellM4.setCellValue("检查旗帜店、誊印社、自助打印等经营网点（家）");
        cellM4.setCellStyle(cellStyle1);

        Cell cellN4 = row3.createCell(13);
        cellN4.setCellValue("抽查印刷品（件）");
        cellN4.setCellStyle(cellStyle1);

        Cell cellO4 = row3.createCell(14);
        cellO4.setCellValue("查处违规经营行为（起）");
        cellO4.setCellStyle(cellStyle1);

        Cell cellP4 = row3.createCell(15);
        cellP4.setCellValue("收缴非法涉政敏感印刷品（件）");
        cellP4.setCellStyle(cellStyle1);

        Cell cellQ4 = row3.createCell(16);
        cellQ4.setCellValue("依法处罚违规经营单位（家）");
        cellQ4.setCellStyle(cellStyle1);


        sheet.addMergedRegion(new CellRangeAddress(1, 1, 17, 24)); // 0-based index
        Cell cellR2 = row1.createCell(17);
        cellR2.setCellValue("流动人口落脚点");
        cellR2.setCellStyle(cellStyle);

        sheet.addMergedRegion(new CellRangeAddress(1, 1, 25, 33)); // 0-based index
        Cell cellZ2 = row1.createCell(25);
        cellZ2.setCellValue("社会面巡逻防控");
        cellZ2.setCellStyle(cellStyle);

        sheet.addMergedRegion(new CellRangeAddress(2, 3, 17, 17)); // 0-based index
        Cell cellR3 = row2.createCell(17);
        cellR3.setCellValue("检查出（群）租房、网吧、农家乐、玉石市场、城乡结合部、城中村等重点场所和区域部位（家/处）");
        cellR3.setCellStyle(cellStyle1);

        sheet.addMergedRegion(new CellRangeAddress(2, 3, 18, 18)); // 0-based index
        Cell cellS3 = row2.createCell(18);
        cellS3.setCellValue("抓获流窜作案人员和在逃人员（人）");
        cellS3.setCellStyle(cellStyle1);

        sheet.addMergedRegion(new CellRangeAddress(2, 3, 19, 19)); // 0-based index
        Cell cellT3 = row2.createCell(19);
        cellT3.setCellValue("发现可能实施极端行为的危险分子、进京来省非访重点人员（人）");
        cellT3.setCellStyle(cellStyle1);

        sheet.addMergedRegion(new CellRangeAddress(2, 3, 20, 20)); // 0-based index
        Cell cellU3 = row2.createCell(20);
        cellU3.setCellValue("查处违规经营行为（起）");
        cellU3.setCellStyle(cellStyle1);

        sheet.addMergedRegion(new CellRangeAddress(2, 3, 21, 21)); // 0-based index
        Cell cellV3 = row2.createCell(21);
        cellV3.setCellValue("查缴违禁物品（件）");
        cellV3.setCellStyle(cellStyle1);


        // Merge cells from W3 to W4 and set value
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 22, 22)); // 0-based index
        Cell cellW3 = row2.createCell(22);
        cellW3.setCellValue("取缔行业场所(个)");
        cellW3.setCellStyle(cellStyle1);

        sheet.addMergedRegion(new CellRangeAddress(2, 3, 23, 23)); // 0-based index
        Cell cellX3 = row2.createCell(23);
        cellX3.setCellValue("抓获违法犯罪嫌疑人（名）");
        cellX3.setCellStyle(cellStyle1);

        sheet.addMergedRegion(new CellRangeAddress(2, 3, 24, 24)); // 0-based index
        Cell cellY3 = row2.createCell(24);
        cellY3.setCellValue("整改房屋建筑、消防等安全隐患（个）");
        cellY3.setCellStyle(cellStyle1);

        sheet.addMergedRegion(new CellRangeAddress(2, 3, 25, 25)); // 0-based index
        Cell cellZ3 = row2.createCell(25);
        cellZ3.setCellValue("投入警、辅力量（人次）");
        cellZ3.setCellStyle(cellStyle1);

        sheet.addMergedRegion(new CellRangeAddress(2, 3, 26, 26)); // 0-based index
        Cell cellAA3 = row2.createCell(26);
        cellAA3.setCellValue("盘查可疑人员（人次）");
        cellAA3.setCellStyle(cellStyle1);

        sheet.addMergedRegion(new CellRangeAddress(2, 3, 27, 27)); // 0-based index
        Cell cellAB3 = row2.createCell(27);
        cellAB3.setCellValue("留置可疑人员（人次）");
        cellAB3.setCellStyle(cellStyle1);

        sheet.addMergedRegion(new CellRangeAddress(2, 3, 28, 28)); // 0-based index
        Cell cellAC3 = row2.createCell(28);
        cellAC3.setCellValue("检查可疑车船（辆、艘）");
        cellAC3.setCellStyle(cellStyle1);

        sheet.addMergedRegion(new CellRangeAddress(2, 3, 29, 29)); // 0-based index
        Cell cellAD3 = row2.createCell(29);
        cellAD3.setCellValue("查缴违禁物品（件）");
        cellAD3.setCellStyle(cellStyle1);

        sheet.addMergedRegion(new CellRangeAddress(2, 3, 30, 30)); // 0-based index
        Cell cellAE3 = row2.createCell(30);
        cellAE3.setCellValue("查处违法犯罪案件（起）");
        cellAE3.setCellStyle(cellStyle1);

        sheet.addMergedRegion(new CellRangeAddress(2, 3, 31, 31)); // 0-based index
        Cell cellAF3 = row2.createCell(31);
        cellAF3.setCellValue("抓获违法犯罪嫌疑人（名）");
        cellAF3.setCellStyle(cellStyle1);

// Merge cells from AG3 to AG4 and set value
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 32, 32)); // 0-based index
        Cell cellAG3 = row2.createCell(32);
        cellAG3.setCellValue("处置突发事件（起）");
        cellAG3.setCellStyle(cellStyle1);

// Merge cells from AH3 to AH4 and set value
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 33, 33)); // 0-based index
        Cell cellAH3 = row2.createCell(33);
        cellAH3.setCellValue("消除的重大安全隐患（个）");
        cellAH3.setCellStyle(cellStyle1);

        Row head = sheet.createRow(4);
        Cell cell = head.createCell(treeLength);
        cell.setCellValue("机构名称");
        cell.setCellStyle(cellStyle);
        Cell cell2 = head.createCell(treeLength + 1);
        cell2.setCellValue("出动警力（人次）");
        cell2.setCellStyle(cellStyle);
        Cell cell3 = head.createCell(treeLength + 2);
        cell3.setCellValue("A1");
        cell3.setCellStyle(cellStyle);
        Cell cell4 = head.createCell(treeLength + 3);
        cell4.setCellValue("A2");
        cell4.setCellStyle(cellStyle);
        Cell cell5 = head.createCell(treeLength + 4);
        cell5.setCellValue("A3");
        cell5.setCellStyle(cellStyle);
        Cell cell6 = head.createCell(treeLength + 5);
        cell6.setCellValue("A4");
        cell6.setCellStyle(cellStyle);
        Cell cell7 = head.createCell(treeLength + 6);
        cell7.setCellValue("A5");
        cell7.setCellStyle(cellStyle);
        Cell cell8 = head.createCell(treeLength + 7);
        cell8.setCellValue("A6");
        cell8.setCellStyle(cellStyle);
        Cell cell9 = head.createCell(treeLength + 8);
        cell9.setCellValue("A7");
        cell9.setCellStyle(cellStyle);
        Cell cell10 = head.createCell(treeLength + 9);
        cell10.setCellValue("A8");
        cell10.setCellStyle(cellStyle);
        Cell cell11 = head.createCell(treeLength + 10);
        cell11.setCellValue("A9");
        cell11.setCellStyle(cellStyle);
        Cell cell12 = head.createCell(treeLength + 11);
        cell12.setCellValue("A10");
        cell12.setCellStyle(cellStyle);
        Cell cell13 = head.createCell(treeLength + 12);
        cell13.setCellValue("A11");
        cell13.setCellStyle(cellStyle);
        Cell cell14 = head.createCell(treeLength + 13);
        cell14.setCellValue("A12");
        cell14.setCellStyle(cellStyle);
        Cell cell15 = head.createCell(treeLength + 14);
        cell15.setCellValue("A13");
        cell15.setCellStyle(cellStyle);
        Cell cell16 = head.createCell(treeLength + 15);
        cell16.setCellValue("A14");
        cell16.setCellStyle(cellStyle);
        Cell cell17 = head.createCell(treeLength + 16);
        cell17.setCellValue("A15");
        cell17.setCellStyle(cellStyle);
        Cell cell18 = head.createCell(treeLength + 17);
        cell18.setCellValue("B1");
        cell18.setCellStyle(cellStyle);
        Cell cell19 = head.createCell(treeLength + 18);
        cell19.setCellValue("B2");
        cell19.setCellStyle(cellStyle);
        Cell cell20 = head.createCell(treeLength + 19);
        cell20.setCellValue("B3");
        cell20.setCellStyle(cellStyle);
        Cell cell21 = head.createCell(treeLength + 20);
        cell21.setCellValue("B4");
        cell21.setCellStyle(cellStyle);
        Cell cell22 = head.createCell(treeLength + 21);
        cell22.setCellValue("B5");
        cell22.setCellStyle(cellStyle);
        Cell cell23 = head.createCell(treeLength + 22);
        cell23.setCellValue("B6");
        cell23.setCellStyle(cellStyle);
        Cell cell24 = head.createCell(treeLength + 23);
        cell24.setCellValue("B7");
        cell24.setCellStyle(cellStyle);
        Cell cell25 = head.createCell(treeLength + 24);
        cell25.setCellValue("B8");
        cell25.setCellStyle(cellStyle);
        Cell cell26 = head.createCell(treeLength + 25);
        cell26.setCellValue("C1");
        cell26.setCellStyle(cellStyle);
        Cell cell27 = head.createCell(treeLength + 26);
        cell27.setCellValue("C2");
        cell27.setCellStyle(cellStyle);
        Cell cell28 = head.createCell(treeLength + 27);
        cell28.setCellValue("C3");
        cell28.setCellStyle(cellStyle);
        Cell cell29 = head.createCell(treeLength + 28);
        cell29.setCellValue("C4");
        cell29.setCellStyle(cellStyle);
        Cell cell30 = head.createCell(treeLength + 29);
        cell30.setCellValue("C5");
        cell30.setCellStyle(cellStyle);
        Cell cell31 = head.createCell(treeLength + 30);
        cell31.setCellValue("C6");
        cell31.setCellStyle(cellStyle);
        Cell cell32 = head.createCell(treeLength + 31);
        cell32.setCellValue("C7");
        cell32.setCellStyle(cellStyle);
        Cell cell33 = head.createCell(treeLength + 32);
        cell33.setCellValue("C8");
        cell33.setCellStyle(cellStyle);
        Cell cell34 = head.createCell(treeLength + 33);
        cell34.setCellValue("C9");
        cell34.setCellStyle(cellStyle);


        int flag = 4;
        for (int i = 0; i < arr.size(); i++) {
            JSONObject obj = arr.getJSONObject(i);
            int r = fillTreeData(sheet, obj, flag, 0, cellStyle);
            flag += r;
        }
    }

    private static int fillTreeData(Sheet sheet, JSONObject object, int rowStart, int colStart, CellStyle cellStyle) {
        String id = object.getString("id");

        // 获取开始行，如果不存在则创建一行
//        Row row = sheet.getRow(rowStart) == null ? sheet.createRow(rowStart) : sheet.getRow(rowStart);

//        // 在开始列创建一个单元格
//        Cell cell = row.createCell(colStart);
//
//        // 设置单元格的值为节点名称
//        cell.setCellValue(object.getString("kh_name"));
//        cell.setCellStyle(cellStyle);
//        // 检查节点是否有子节点

        Row head = sheet.createRow(rowStart);
        Cell cell = head.createCell(colStart);
        cell.setCellValue(object.getString("JGMC"));
        cell.setCellStyle(cellStyle);
        Cell cell2 = head.createCell(colStart + 1);
        cell2.setCellValue(object.getIntValue("CDJL"));
        cell2.setCellStyle(cellStyle);
        Cell cell3 = head.createCell(colStart + 2);
        cell3.setCellValue(object.getIntValue("A1"));
        cell3.setCellStyle(cellStyle);
        Cell cell4 = head.createCell(colStart + 3);
        cell4.setCellValue(object.getIntValue("A2"));
        cell4.setCellStyle(cellStyle);
        Cell cell5 = head.createCell(colStart + 4);
        cell5.setCellValue(object.getIntValue("A3"));
        cell5.setCellStyle(cellStyle);
        Cell cell6 = head.createCell(colStart + 5);
        cell6.setCellValue(object.getIntValue("A4"));
        cell6.setCellStyle(cellStyle);
        Cell cell7 = head.createCell(colStart + 6);
        cell7.setCellValue(object.getIntValue("A5"));
        cell7.setCellStyle(cellStyle);
        Cell cell8 = head.createCell(colStart + 7);
        cell8.setCellValue(object.getIntValue("A6"));
        cell8.setCellStyle(cellStyle);
        Cell cell9 = head.createCell(colStart + 8);
        cell9.setCellValue(object.getIntValue("A7"));
        cell9.setCellStyle(cellStyle);
        Cell cell10 = head.createCell(colStart + 9);
        cell10.setCellValue(object.getIntValue("A8"));
        cell10.setCellStyle(cellStyle);
        Cell cell11 = head.createCell(colStart + 10);
        cell11.setCellValue(object.getIntValue("A9"));
        cell11.setCellStyle(cellStyle);
        Cell cell12 = head.createCell(colStart + 11);
        cell12.setCellValue(object.getIntValue("A10"));
        cell12.setCellStyle(cellStyle);
        Cell cell13 = head.createCell(colStart + 12);
        cell13.setCellValue(object.getIntValue("A11"));
        cell13.setCellStyle(cellStyle);
        Cell cell14 = head.createCell(colStart + 13);
        cell14.setCellValue(object.getIntValue("A12"));
        cell14.setCellStyle(cellStyle);
        Cell cell15 = head.createCell(colStart + 14);
        cell15.setCellValue(object.getIntValue("A13"));
        cell15.setCellStyle(cellStyle);
        Cell cell16 = head.createCell(colStart + 15);
        cell16.setCellValue(object.getIntValue("A14"));
        cell16.setCellStyle(cellStyle);
        Cell cell17 = head.createCell(colStart + 16);
        cell17.setCellValue(object.getIntValue("A15"));
        cell17.setCellStyle(cellStyle);
        Cell cell18 = head.createCell(colStart + 17);
        cell18.setCellValue(object.getIntValue("B1"));
        cell18.setCellStyle(cellStyle);
        Cell cell19 = head.createCell(colStart + 18);
        cell19.setCellValue(object.getIntValue("B2"));
        cell19.setCellStyle(cellStyle);
        Cell cell20 = head.createCell(colStart + 19);
        cell20.setCellValue(object.getIntValue("B3"));
        cell20.setCellStyle(cellStyle);
        Cell cell21 = head.createCell(colStart + 20);
        cell21.setCellValue(object.getIntValue("B4"));
        cell21.setCellStyle(cellStyle);
        Cell cell22 = head.createCell(colStart + 21);
        cell22.setCellValue(object.getIntValue("B5"));
        cell22.setCellStyle(cellStyle);
        Cell cell23 = head.createCell(colStart + 22);
        cell23.setCellValue(object.getIntValue("B6"));
        cell23.setCellStyle(cellStyle);
        Cell cell24 = head.createCell(colStart + 23);
        cell24.setCellValue(object.getIntValue("B7"));
        cell24.setCellStyle(cellStyle);
        Cell cell25 = head.createCell(colStart + 24);
        cell25.setCellValue(object.getIntValue("B8"));
        cell25.setCellStyle(cellStyle);
        Cell cell26 = head.createCell(colStart + 25);
        cell26.setCellValue(object.getIntValue("C1"));
        cell26.setCellStyle(cellStyle);
        Cell cell27 = head.createCell(colStart + 26);
        cell27.setCellValue(object.getIntValue("C2"));
        cell27.setCellStyle(cellStyle);
        Cell cell28 = head.createCell(colStart + 27);
        cell28.setCellValue(object.getIntValue("C3"));
        cell28.setCellStyle(cellStyle);
        Cell cell29 = head.createCell(colStart + 28);
        cell29.setCellValue(object.getIntValue("C4"));
        cell29.setCellStyle(cellStyle);
        Cell cell30 = head.createCell(colStart + 29);
        cell30.setCellValue(object.getIntValue("C5"));
        cell30.setCellStyle(cellStyle);
        Cell cell31 = head.createCell(colStart + 30);
        cell31.setCellValue(object.getIntValue("C6"));
        cell31.setCellStyle(cellStyle);
        Cell cell32 = head.createCell(colStart + 31);
        cell32.setCellValue(object.getIntValue("C7"));
        cell32.setCellStyle(cellStyle);
        Cell cell33 = head.createCell(colStart + 32);
        cell33.setCellValue(object.getIntValue("C8"));
        cell33.setCellStyle(cellStyle);
        Cell cell34 = head.createCell(colStart + 33);
        cell34.setCellValue(object.getIntValue("C9"));
        cell34.setCellStyle(cellStyle);


        // 如果没有子节点，那么这个节点只占用一行
        return 1;
    }

}
