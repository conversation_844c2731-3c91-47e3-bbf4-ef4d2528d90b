package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.Export.ExportInterface;
import HL.TNOA.Lib.Export.ExportXlsxHelper;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

import static HL.TNOA.Httpd.Controller.JQZTController.GetPointLoc;

@RestController
public class ScreenPointController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/screen_point"})
    // @PassToken
    public JSONObject get_screenPoint(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        String token = request.getHeader("token");
        try {

            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("get_point_person")) {// 人房监测点
                    return GetPointPerson(data);

                } else if (opt.equals("get_point_person_level")) {// 人房监测点
                    return GetPointPersonLevel(data);

                } else if (opt.equals("get_point_unit")) {
                    return getPointUnit(data);
                } else if (opt.equals("get_point_unit_level")) {
                    return getPointUnitLevel(data);
                } else if (opt.equals("get_point_loc_table")) {
                    return GetPointLocTable(data, token);
                } else if (opt.equals("get_zdry_yd_point")) {
                    return GetZdryYdPoint(data);
                } else if (opt.equals("get_point_zdryyd_level")) {
                    return GetZdryYdPointLevel(data);
                } else if (opt.equals("get_point_zdryyd_table")) {
                    return GetZdryYdPointTable(data, request);
                } else if (opt.equals("get_point_zdryyd_notice")) {
                    return GetZdryYdPointNotice(data, request);
                } else {
                    return ErrNo.set(465009);
                }
            } else {
                return ErrNo.set(465009);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private JSONObject GetZdryYdPointNotice(JSONObject data, TNOAHttpRequest request) {
        String token = request.getHeader("token");
        JSONObject back = ErrNo.set(0);
        String lx = "";

        String qqb_id = data.getString("qqb_id");
        String lxsql = "";
        if (data.containsKey("lx") && data.getString("lx").length() > 0) {
            lx = data.getString("lx");
            lxsql = " and lx like '" + lx + "%'";
        }
        String point = "";
        if (data.containsKey("point") && data.getString("point").length() > 0) {
            point = data.getString("point");
            lxsql = " and lx = '" + point + "'";
        }

        String unit1 = data.getString("unit");
        String startTime = data.getString("start_time");
        String endTime = data.getString("end_time");
        String sfsx = "";
        try {
            sfsx = data.getString("sfsx");
        } catch (Exception ex) {

        }
        String usql = "";
        int type = RIUtil.dicts.get(unit1).getInteger("type");
        String unit = "";
        if (type == 21 || type == 22 || type == 27) {
            unit = unit1.substring(0, 4);
            usql = " 1=1 ";

        } else if (type == 23 || type == 24) {
            unit = unit1.substring(0, 6);
            usql = "  xgldw like '" + unit + "%'";
        } else if (type == 25) {
            unit = unit1.substring(0, 8);
            usql = "  xgldw like '" + unit + "%'";
        } else {
            unit = unit1;
            usql = "  xgldw = '" + unit + "'";
        }
        MysqlHelper my143 = null;
        try {
            if (sfsx.length() > 0) {
                my143 = new MysqlHelper("mysql_zxqc");
                String sql = "select id from dict  where (type=180 and id='" + sfsx + "') or (type=181 " + "and " +
                        "father_id='" + sfsx + "')";

                List<JSONObject> dicts = my143.query(sql);
                String xlsql = " ";
                for (int d = 0; d < dicts.size(); d++) {
                    JSONObject done = dicts.get(d);
                    xlsql = xlsql + " XJZLBXL like '" + done.getString("id") + "' or ";

                }

                if (xlsql.length() > 2) {
                    xlsql = xlsql.substring(0, xlsql.length() - 3);
                }
                usql = usql + " and (" + xlsql + ") ";
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            if (my143 != null) {
                my143.close();
            }
        }
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            String sql = "select a.*,b.* from hl.zdry_yd_point a left join hl.qqb_task_object3 b on a.objid=b" +
                    ".objid and a.bizvalue=b.bizvalue  where " + usql + lxsql + " and  yjsj>='" + startTime + "'  " + "and" + " yjsj<='" + endTime + "' order by yjsj desc " + " offset 0 rows fetch next 10 rows only";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);

            JSONArray dets = new JSONArray();

            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                JSONObject det = new JSONObject();

                String sfsxs = one.getString("XJZLBXL");
                String[] xls = sfsxs.split(",");
                String glxl = "";
                for (int a = 0; a < xls.length; a++) {
                    try {
                        glxl = glxl + RIUtil.dicts.get(xls[a]).getString("dict_name") + " ";
                    } catch (Exception ex) {
                        logger.error(xls[a]);
                    }
                }
                one.put("XJZLBXL", glxl);
                String gljb = "";

                try {
                    gljb = RIUtil.dicts.get("182-" + one.getString("ZDRYGLJB")).getString("dict_name");

                } catch (Exception ex) {
                    logger.error(one.getString("ZDRYGLJB"));

                }
                one.put("ZDRYGLJB", gljb);
                try {
                    one.put("XGLZRQDM", RIUtil.dicts.get(one.getString("XGLDW")).getString("remark"));
                } catch (Exception ex) {
                    one.put("XGLZRQDM", "");
                    logger.error(one.getString("XGLZRQDM"));
                }
                int status = 0;
                try {
                    status = one.getIntValue("STATUS");
                } catch (Exception ex) {

                }
                String exeid = one.getString("EXECUTORIDS");
                String opt = "";

                if (status == 0) {
                    one.put("OPT", "盯办");
                    opt = "盯办";
                } else if (status == 2 || status == 4) {
                    if (exeid.contains(qqb_id)) {
                        one.put("OPT", "去工作");
                        opt = "去工作";
                    } else {
                        one.put("OPT", "详情");
                        opt = "详情";
                    }

                } else {
                    one.put("OPT", "详情");
                    opt = "详情";
                }

                det.put("title", one.getString("XM") + " " + one.getString("GMSFZH"));
                det.put("content", one.getString("ZXZT") + " " + one.getString("FK"));
                det.put("time", one.getString("YJSJ"));
                det.put("img", one.getString("ZPLJ"));

                String labs = gljb + "," + glxl + "," + one.getString("YJLX");
                String colors = "#4cbb6c,#ff1515,#ff8e15";
                JSONArray labels = GetLabels(labs, colors);
                det.put("label", labels);


                JSONObject click = new JSONObject();
                if (opt.equals("去工作")) {
                    click.put("type", "go_task");
                    String url = TNOAConf.get("Httpd", "qqb_task_work") + "?token=" + token + "#/actionView" +
                            "?objData=";
                    JSONObject taskObj = new JSONObject();
                    taskObj.put("taskName", one.getString("TASK_NAME"));
                    taskObj.put("deliveryWay", one.getString("DELIVERY_WAY"));
                    taskObj.put("accessType", one.getString("ACCESS_TYPE"));
                    taskObj.put("status", one.getString("STATUS"));
                    taskObj.put("bizValue", one.getString("BIZVALUE"));
                    String link = one.getString("isLink");
                    String[] lks = link.split(",");
                    String detailId = "";
                    try {
                        detailId = lks[1];
                    } catch (Exception ex) {
                        detailId = one.getString("ID");
                    }
                    taskObj.put("taskDetailId", detailId);
                    taskObj.put("taskType", one.getString("TASK_DETAIL_TYPE"));
                    taskObj.put("bizName", one.getString("BIZNAME"));
                    taskObj.put("objId", one.getString("OBJID"));
                    taskObj.put("objectId", one.getString("OBJECT_ID"));
                    logger.warn(taskObj.toString());
                    String objData = StringToURL(taskObj.toString());
                    url = url + objData;

                    click.put("url", url);
                } else if (opt.equals("详情")) {
                    String objId = one.getString("OBJID");
                    String ysid = one.getString("OBJECT_ID").substring(0, 20);
                    String bizValue = one.getString("BIZVALUE");
                    click.put("type", "go_task");
                    String url = TNOAConf.get("Httpd", "qqb_task_det") + "?token=" + token + "&objId=" + objId +
                            "&ysId=" + ysid + "&bizValue=" + bizValue;
                    logger.warn(url);
                    click.put("url", url);
                } else {


                    click.put("type", "OPEN_DING_DIA");
                }
                det.put("ding", click);

                JSONObject dpclick = new JSONObject();
                dpclick.put("type", "dialog");
                JSONObject cols = new JSONObject();

                cols.put("unit", unit1);
                cols.put("lx", lx);
                cols.put("sfsx", sfsx);
                cols.put("qqb_id", qqb_id);
                cols.put("start_time", startTime);
                cols.put("end_time", endTime);

                JSONObject opts = GetOpts("/screen_point", "get_point_zdryyd_table", cols);
                dpclick.put("remark", opts);
                dpclick.put("permission", "table_page");
                dpclick.put("id", String.valueOf(UUID.randomUUID()));
                dpclick.put("label", "明细");
                det.put("dpclick", dpclick);


                dets.add(det);


            }

            back.put("data", dets);
            return back;
            //return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            ora_hl.close();

        }

    }


    private JSONObject GetZdryYdPointTable(JSONObject data, TNOAHttpRequest request) {
        String token = request.getHeader("token");
        JSONObject back = ErrNo.set(0);
        String lx = "";
        int page = data.getInteger("page");
        int limit = data.getInteger("limit");
        int isExp = data.getIntValue("isExp");
        String qqb_id = data.getString("qqb_id");
        String lxsql = "";
        if (data.containsKey("lx") && data.getString("lx").length() > 0) {
            lx = data.getString("lx");
            lxsql = " and lx like '" + lx + "%'";
        }
        String point = "";
        if (data.containsKey("point") && data.getString("point").length() > 0) {
            point = data.getString("point");
            lxsql = " and lx = '" + point + "'";
        }

        String unit1 = data.getString("unit");
        String startTime = data.getString("start_time");
        String endTime = data.getString("end_time");
        String sfsx = "";
        try {
            sfsx = data.getString("sfsx");
        } catch (Exception ex) {

        }
        String usql = "";
        int type = RIUtil.dicts.get(unit1).getInteger("type");
        String unit = "";
        if (type == 21 || type == 22 || type == 27) {
            unit = unit1.substring(0, 4);
            usql = " 1=1 ";

        } else if (type == 23 || type == 24) {
            unit = unit1.substring(0, 6);
            usql = "  xgldw like '" + unit + "%'";
        } else if (type == 25) {
            unit = unit1.substring(0, 8);
            usql = "  xgldw like '" + unit + "%'";
        } else {
            unit = unit1;
            usql = "  xgldw = '" + unit + "'";
        }
        MysqlHelper my143 = null;
        try {
            if (sfsx.length() > 0) {
                my143 = new MysqlHelper("mysql_zxqc");
                String sql = "select id from dict  where (type=180 and id='" + sfsx + "') or (type=181 " + "and " +
                        "father_id='" + sfsx + "')";

                List<JSONObject> dicts = my143.query(sql);
                String xlsql = " ";
                for (int d = 0; d < dicts.size(); d++) {
                    JSONObject done = dicts.get(d);
                    xlsql = xlsql + " XJZLBXL like '%" + done.getString("id") + "%' or ";

                }

                if (xlsql.length() > 2) {
                    xlsql = xlsql.substring(0, xlsql.length() - 3);
                }
                usql = usql + " and (" + xlsql + ") ";
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            if (my143 != null) {
                my143.close();
            }
        }
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            String sql = "select a.*,b.* from hl.zdry_yd_point a left join hl.qqb_task_object3 b on a.objid=b" +
                    ".objid and" + " a.bizvalue=b.bizvalue " + " where " + usql + lxsql + " and" + " yjsj>='" + startTime + "' " + "and yjsj<='" + endTime + "' order by yjsj desc " + " offset " + (page - 1) * limit + " " + "rows fetch next " + limit + " rows only";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            sql = "select count(1) as count from hl.zdry_yd_point where " + usql + lxsql + " and" + " yjsj>='" + startTime + "'" + " " + "and yjsj<='" + endTime + "'";
            int count = ora_hl.query_count(sql);
            String hds = "姓名,身份证号,预警类型,知悉状态,反馈,身份属性,管理级别,管理单位,预警时间,操作";
            String keys = "XM,GMSFZH,YJLX,ZXZT,FK,XJZLBXL,ZDRYGLJB,XGLDW,YJSJ,OPT";
            JSONArray heads = GetHeads(hds, keys);
            JSONArray dets = new JSONArray();
            JSONArray dds = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);

                String sfsxs = one.getString("XJZLBXL").replace("，", ",");
                String[] xls = sfsxs.split(",");
                String glxl = "";
                for (int a = 0; a < xls.length; a++) {
                    try {
                        glxl = glxl + RIUtil.dicts.get(xls[a]).getString("dict_name") + " ";
                    } catch (Exception ex) {
                        logger.error(xls[a]);
                    }
                }
                one.put("XJZLBXL", glxl);
                String gljbs = "";
                try {
                    String jbs[] = one.getString("ZDRYGLJB").split(",");
                    for (int a = 0; a < jbs.length; a++) {
                        gljbs = gljbs + RIUtil.dicts.get("182-" + one.getString("ZDRYGLJB")).getString("dict_name") + " ";
                    }
                    one.put("ZDRYGLJB", gljbs);
                } catch (Exception ex) {
                    logger.error(one.getString("ZDRYGLJB"));
                    one.put("ZDRYGLJB", "");
                }
                try {
                    one.put("XGLZRQDM", RIUtil.dicts.get(one.getString("XGLDW")).getString("remark"));
                } catch (Exception ex) {
                    one.put("XGLZRQDM", "");
                    logger.error(one.getString("XGLZRQDM"));
                }
                int status = one.getIntValue("STATUS");
                String exeid = one.getString("EXECUTORIDS");
                String opt = "";

                if (status == 0) {
                    one.put("OPT", "盯办");
                    opt = "盯办";
                } else if (status == 2 || status == 4) {
                    if (exeid.contains(qqb_id)) {
                        one.put("OPT", "去工作");
                        opt = "去工作";
                    } else {
                        one.put("OPT", "详情");
                        opt = "详情";
                    }

                } else {
                    one.put("OPT", "详情");
                    opt = "详情";
                }

                dds.add(one);
                JSONObject det = new JSONObject();
                String[] key = keys.split(",");
                for (int k = 0; k < key.length; k++) {
                    String c = key[k];
                    String v = "";

                    v = one.getString(c);


                    JSONObject val = new JSONObject();
                    val.put("value", v);

                    if (c.equals("GMSFZH")) {
                        JSONObject click = new JSONObject();

                        if (opt.equals("盯办")) {


                            click.put("type", "jump_zdry");
                            click.put("url", one.getString("GMSFZH"));
                        } else {
                            click.put("type", "go_task");
                            String objId = one.getString("OBJID");
                            String ysid = one.getString("OBJECT_ID").substring(0, 20);
                            String bizValue = one.getString("BIZVALUE");
                            click.put("type", "go_task");
                            String url =
                                    TNOAConf.get("Httpd", "qqb_task_det") + "?token=" + token + "&objId=" + objId +
                                            "&ysId=" + ysid + "&bizValue=" + bizValue;
                            logger.warn(url);
                            click.put("url", url);
                        }
                        val.put("click", click);
                    }
                    if (c.equals("OPT")) {

                        JSONObject click = new JSONObject();
                        if (opt.equals("去工作")) {
                            click.put("type", "go_task");
                            String url =
                                    TNOAConf.get("Httpd", "qqb_task_work") + "?token=" + token + "#/actionView" +
                                            "?objData=";
                            JSONObject taskObj = new JSONObject();
                            taskObj.put("taskName", one.getString("TASK_NAME"));
                            taskObj.put("deliveryWay", one.getString("DELIVERY_WAY"));
                            taskObj.put("accessType", one.getString("ACCESS_TYPE"));
                            taskObj.put("status", one.getString("STATUS"));
                            taskObj.put("bizValue", one.getString("BIZVALUE"));
                            String link = one.getString("isLink");
                            String[] lks = link.split(",");
                            String detailId = "";
                            try {
                                detailId = lks[1];
                            } catch (Exception ex) {
                                detailId = one.getString("ID");
                            }
                            taskObj.put("taskDetailId", detailId);
                            taskObj.put("taskType", one.getString("TASK_DETAIL_TYPE"));
                            taskObj.put("bizName", one.getString("BIZNAME"));
                            taskObj.put("objId", one.getString("OBJID"));
                            taskObj.put("objectId", one.getString("OBJECT_ID"));
                            logger.warn(taskObj.toString());
                            String objData = StringToURL(taskObj.toString());
                            url = url + objData;

                            click.put("url", url);
                        } else if (opt.equals("详情")) {
                            String objId = one.getString("OBJID");
                            String ysid = one.getString("OBJECT_ID").substring(0, 20);
                            String bizValue = one.getString("BIZVALUE");
                            click.put("type", "go_task");
                            String url =
                                    TNOAConf.get("Httpd", "qqb_task_det") + "?token=" + token + "&objId=" + objId +
                                            "&ysId=" + ysid + "&bizValue=" + bizValue;
                            logger.warn(url);
                            click.put("url", url);
                        } else {


                            click.put("type", "OPEN_DING_DIA");
                        }
                        val.put("ding", click);
                    }
                    det.put(c, val);
                }
                dets.add(det);


            }
            JSONObject datas = new JSONObject();
            datas.put("head", heads);
            datas.put("body", dets);
            datas.put("count", count);
            int fileId = -1;
            if (isExp == 1) {
                //本页
                fileId = ExportTables(dds, hds, keys, "ZDRYYD");

            } else if (isExp == 2) {
                //全部
                sql = "select a.* from hl.zdry_yd_point  where " + usql + lxsql + " and" + " yjsj>='" + startTime +
                        "'" + " " + "and" + " " + "yjsj<='" + endTime + "' order by yjsj desc ";
                logger.warn(sql);
                list = ora_hl.query(sql);
                dds = new JSONArray();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String sfsxs = one.getString("XJZLBXL");
                    String[] xls = sfsxs.split(",");
                    String glxl = "";
                    for (int a = 0; a < xls.length; a++) {
                        glxl = glxl + RIUtil.dicts.get(xls[a]).getString("dict_name") + " ";
                    }
                    one.put("XJZLBXL", glxl);
                    try {
                        one.put("ZDRYGLJB", RIUtil.dicts.get("182-" + one.getString("ZDRYGLJB")).getString("dict_name"
                        ));
                    } catch (Exception ex) {
                        logger.error(one.getString("ZDRYGLJB"));
                        one.put("ZDRYGLJB", "");
                    }
                    try {
                        one.put("XGLZRQDM", RIUtil.dicts.get(one.getString("XGLZRQDM")).getString("remark"));
                    } catch (Exception ex) {
                        one.put("XGLZRQDM", "");
                        logger.error(one.getString("XGLZRQDM"));
                    }
                    one.put("opt", "");
                    dds.add(one);
                }
                fileId = ExportTables(dds, hds, keys, "ZDRYYD");

            } else {

            }
            datas.put("file_id", fileId);
            back.put("data", datas);
            return back;
            //return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            ora_hl.close();

        }
    }

    private JSONObject GetZdryYdPoint(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit1 = data.getString("unit");
        String unit = "";
        String lx = data.getString("lx");
        int type = RIUtil.dicts.get(unit1).getInteger("type");
        String startTime = data.getString("start_time");
        String endTime = data.getString("end_time");
        String qqb_id = data.getString("qqb_id");
        String sfsx = "";
        try {
            sfsx = data.getString("sfsx");
        } catch (Exception ex) {

        }
        String usql = "";
        if (type == 21 || type == 22 || type == 27) {
            unit = unit1.substring(0, 4);
            usql = " 1=1 ";

        } else if (type == 23 || type == 24) {
            unit = unit1.substring(0, 6);
            usql = "  xgldw like '" + unit + "%'";
        } else if (type == 25) {
            unit = unit1.substring(0, 8);
            usql = "  xgldw like '" + unit + "%'";
        } else {
            unit = unit1;
            usql = "  xgldw = '" + unit + "'";
        }
        MysqlHelper my143 = null;
        try {
            if (sfsx.length() > 0) {
                my143 = new MysqlHelper("mysql_zxqc");
                String sql = "select id from dict  where (type=180 and id='" + sfsx + "') or (type=181 " + "and " +
                        "father_id='" + sfsx + "')";

                List<JSONObject> dicts = my143.query(sql);
                String xlsql = " ";
                for (int d = 0; d < dicts.size(); d++) {
                    JSONObject done = dicts.get(d);
                    xlsql = xlsql + " XJZLBXL like '" + done.getString("id") + "%' or ";

                }

                if (xlsql.length() > 2) {
                    xlsql = xlsql.substring(0, xlsql.length() - 3);
                }
                usql = usql + " and (" + xlsql + ") ";
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            if (my143 != null) {
                my143.close();
            }
        }
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            String sql =
                    "select count(1) as count ,lx from hl.zdry_yd_point where " + usql + " and lx like '" + lx + "%' "
                            + "and" + " yjsj>='" + startTime + "' and yjsj<='" + endTime + "'" + " group by lx";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            HashMap<String, String> cs = new HashMap<>();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                cs.put(one.getString("LX"), one.getString("COUNT"));
            }

            JSONArray dict16051 = RIUtil.GetDictByFather(lx);
            JSONArray dets = new JSONArray();
            for (int i = 0; i < dict16051.size(); i++) {
                JSONObject one = dict16051.getJSONObject(i);
                JSONObject det = new JSONObject();
                String title = one.getString("dict_name");
                String remark = one.getString("memo");
                String count = "0";
                try {
                    count = cs.get(one.getString("id"));
                    if (count == null) {
                        count = "0";
                    }
                } catch (Exception e) {

                }
                det.put("title", title);
                det.put("count", count);
                det.put("remark", remark);
                det.put("id", one.getString("id"));

                JSONObject click = new JSONObject();
                click.put("type", "dialog");
                int ran = i % 4;
                if (ran == 0) {
                    click.put("permission", "chat_bar");
                    click.put("id", String.valueOf(UUID.randomUUID()));
                } else if (ran == 1) {
                    click.put("permission", "chat_pie");
                    click.put("id", String.valueOf(UUID.randomUUID()));
                } else if (ran == 2) {
                    click.put("permission", "chat_bar_tran");//chat_bar_tran 类型无法下转
                    click.put("id", String.valueOf(UUID.randomUUID()));
                } else {
                    click.put("permission", "chat_rose");
                    click.put("id", String.valueOf(UUID.randomUUID()));
                }
                JSONObject cols = new JSONObject();

                cols.put("lx", lx);
                cols.put("sfsx", sfsx);
                cols.put("point", one.getString("id"));
                cols.put("unit", unit1);
                cols.put("start_time", startTime);
                cols.put("end_time", endTime);
                JSONObject opts = GetOpts("/screen_point", "get_point_zdryyd_level", cols);
                click.put("remark", opts);
                click.put("label", RIUtil.dicts.get(one.getString("id")).getString("dict_name"));


                det.put("click", click);


                JSONObject dpclick = new JSONObject();
                dpclick.put("type", "dialog");
                cols = new JSONObject();
                cols.put("point", one.getString("id"));
                cols.put("unit", unit1);
                cols.put("lx", lx);
                cols.put("sfsx", sfsx);
                cols.put("qqb_id", qqb_id);
                cols.put("start_time", startTime);
                cols.put("end_time", endTime);

                opts = GetOpts("/screen_point", "get_point_zdryyd_table", cols);
                dpclick.put("remark", opts);
                dpclick.put("permission", "table_page");
                dpclick.put("id", String.valueOf(UUID.randomUUID()));
                dpclick.put("label", RIUtil.dicts.get(one.getString("id")).getString("dict_name"));
                det.put("dpclick", dpclick);


                dets.add(det);

            }
            back.put("data", dets);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            ora_hl.close();

        }
    }

    private JSONObject GetZdryYdPointLevel(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String lx = data.getString("lx");
        String point = data.getString("point");


        String unit = data.getString("unit");
        int type = RIUtil.dicts.get(unit).getInteger("type");
        String startTime = data.getString("start_time");
        String endTime = data.getString("end_time");
        String sfsx = "";
        try {
            sfsx = data.getString("sfsx");
        } catch (Exception ex) {

        }
        String usql = " 1=1 ";
        String groupS = "";
        if (type == 21 || type == 22 || type == 27) {

            groupS = " substr(xgldw,1,6) ";


        } else if (type == 23 || type == 24) {

            groupS = " substr(xgldw,1,8) ";
            usql = usql + " and xgldw like '" + unit.substring(0, 6) + "%' ";
        } else if (type == 25 || type == 26) {
            usql = usql + " and xgldw like '" + unit.substring(0, 8) + "%' ";
            groupS = " xgldw ";
        } else {
            usql = usql + " and xgldw like '" + unit.substring(0, 8) + "%' ";
            groupS = " xgldw ";
        }

        MysqlHelper my143 = null;
        try {
            if (sfsx.length() > 0) {
                my143 = new MysqlHelper("mysql_zxqc");
                String sql = "select id from dict  where (type=180 and id='" + sfsx + "') or (type=181 " + "and " +
                        "father_id='" + sfsx + "')";

                List<JSONObject> dicts = my143.query(sql);
                String xlsql = " ";
                for (int d = 0; d < dicts.size(); d++) {
                    JSONObject done = dicts.get(d);
                    xlsql = xlsql + " XJZLBXL like '" + done.getString("id") + "%' or ";

                }

                if (xlsql.length() > 2) {
                    xlsql = xlsql.substring(0, xlsql.length() - 3);
                }
                usql = usql + " and (" + xlsql + ") ";
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            if (my143 != null) {
                my143.close();
            }
        }
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            String sql =
                    "select count(1) as count ," + groupS + " as code from hl.zdry_yd_point where " + usql + " " +
                            "and" + " lx " + "= '" + point + "' " + "and" + " yjsj>='" + startTime + "' and yjsj<='" + endTime + "'" + " group by " + groupS + " order by count desc";
            logger.warn(sql);
            JSONArray dets = new JSONArray();
            List<JSONObject> list = ora_hl.query(sql);
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String count = one.getString("COUNT");
                String code = one.getString("CODE");

                JSONObject det = new JSONObject();
                if (code.length() == 6) {
                    code = code + "000000";
                } else if (code.length() == 8) {
                    code = code + "0000";
                }
                try {
                    det.put("name", RIUtil.dicts.get(code).getString("dict_name"));
                } catch (Exception ex) {
                    logger.error(code);
                }
                det.put("count", count);
                JSONObject click = new JSONObject();
                click.put("type", "dialog1");
                JSONObject cols = new JSONObject();
                cols.put("point", point);
                cols.put("unit", code);
                cols.put("lx", lx);
                cols.put("sfsx", sfsx);
                cols.put("start_time", startTime);
                cols.put("end_time", endTime);
                cols.put("qqb_id", "$qqb_id$");
                JSONObject opts = GetOpts("/screen_point", "get_point_zdryyd_table", cols);
                click.put("remark", opts);
                click.put("permission", "table_page");
                click.put("id", String.valueOf(UUID.randomUUID()));
                click.put("label", RIUtil.dicts.get(point).getString("dict_name"));
                det.put("click", click);

                JSONObject dpclick = new JSONObject();
                dpclick.put("id", String.valueOf(UUID.randomUUID()));

                cols.put("unit", code);
                cols.put("point", point);
                cols.put("lx", lx);
                cols.put("start_time", startTime);
                cols.put("end_time", endTime);
                JSONObject opt = GetOpts("/screen_point", "get_point_zdryyd_level", cols);
                dpclick.put("opt", opt);
                dpclick.put("name", RIUtil.dicts.get(code).getString("dict_name"));
                dpclick.put("type", "next");
                dpclick.put("url", "/screen_point");


                det.put("dpclick", dpclick);
                dets.add(det);
            }
            back.put("data", dets);

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            ora_hl.close();

        }
    }

    private JSONObject getPointUnitLevel(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String lx = data.getString("lx");

        String subStr = "";
        String unit = "";
        String type = "";
        String uSql = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            type = RIUtil.dicts.get(unit).getString("type");
            if (type.equals("23") || type.equals("24") || type.equals("28")) {
                subStr = "substr(sszrq from 1 for 6) ";
                uSql = " and sszrq like '" + unit.substring(0, 6) + "%' ";
            } else if (type.equals("25") || type.equals("26")) {
                subStr = "sszrq ";
                uSql = " and sszrq like '" + unit.substring(0, 8) + "%' ";
            } else {
                subStr = "substr(SSZRQ from 1 for 6) ";

            }
        } else {
            return ErrNo.set(501001);
        }
        MysqlHelper my143 = null;
        try {
            my143 = new MysqlHelper("mysql_zxqc");
            String sql = "select count(1) as count ," + subStr + " as code from point_dw where " + "JCLX_XL" + " " +
                    "='" + lx + "' " + uSql + " group by " + subStr;
            logger.warn(sql);
            JSONArray dets = new JSONArray();
            List<JSONObject> list = my143.query(sql);
            logger.warn(list.get(0).toString());
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);

                String code = one.getString("code");
                if (code != null && code.length() > 2) {
                    if (code.length() == 6) {
                        code = code + "000000";
                    } else if (code.length() == 8) {
                        code = code + "0000";
                    } else {
                        code = code;
                    }
                    String name = RIUtil.dicts.get(code).getString("dict_name");
                    JSONObject click = new JSONObject();
                    click.put("type", "next");
                    click.put("url", "/screen_point");
                    click.put("name", RIUtil.dicts.get(code).getString("dict_name"));
                    click.put("id", String.valueOf(UUID.randomUUID()));
                    JSONObject cols = new JSONObject();
                    cols.put("lx", lx);
                    cols.put("unit", code);
                    cols.put("name", RIUtil.dicts.get(code).getString("dict_name"));
                    click.put("opt", GetOpts("/screen_point", "get_point_unit_level", cols));
                    if (!type.equals("26")) {
                        one.put("dpclick", click);
                    }
                    one.put("name", name);

                    JSONObject dpclick = new JSONObject();
                    dpclick.put("type", "dialog1");
                    dpclick.put("label", name + "_" + RIUtil.dicts.get("160-" + lx).getString("dict_name"));
                    cols = new JSONObject();
                    cols.put("point", lx);
                    cols.put("lx", lx);
                    cols.put("unit", code);
                    cols.put("name", name);
                    JSONObject opts = GetOpts("/screen_point", "get_point_loc_table", cols);
                    dpclick.put("remark", opts);
                    dpclick.put("permission", "table_page");
                    dpclick.put("id", String.valueOf(UUID.randomUUID()));
                    one.put("click", dpclick);

                    dets.add(one);
                }
            }
            back.put("data", dets);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            my143.close();

        }
    }


    private JSONObject getPointUnit(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper mysql = null;
        MysqlHelper my143 = null;
        String unit = "";
        String type = "";
        String gsUsql = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            type = RIUtil.dicts.get(unit).getString("type");
            if (type.equals("23") || type.equals("24") || type.equals("28")) {

                gsUsql = "sszrq like '" + unit.substring(0, 6) + "%'";
            } else if (type.equals("25")) {

                gsUsql = "sszrq like '" + unit.substring(0, 8) + "%'";
            } else if (type.equals("26")) {

                gsUsql = "sszrq = '" + unit + "'";
            } else {

                gsUsql = "1=1";
            }
        } else {
            return ErrNo.set(501001);
        }
        try {

            mysql = new MysqlHelper("mysql");

            my143 = new MysqlHelper("mysql_zxqc");
            HashMap<String, String> gsXls = new HashMap<>();
            List<JSONObject> gsList = new ArrayList<>();
            String sql = "";


            sql = "select count(id) as COUNT,jclx_xl from point_dw where " + gsUsql + " group by jclx_xl ";

            gsList = my143.query(sql);

            for (int i = 0; i < gsList.size(); i++) {
                JSONObject one = gsList.get(i);
//                    gsXls.put(one.getString("JCLX_XL"), one.getString("COUNT"));
                gsXls.put(one.getString("JCLX_XL"), one.getString("COUNT"));
            }


            String father_id = data.getString("father_id");

            sql = "select id,dict_name,memo from dict where isdelete=1 and father_id='" + father_id + "'";
            JSONArray roomChildList = RIUtil.GetDictByFather(father_id);
            //   logger.warn(roomChildList.toString());
            JSONArray dets = new JSONArray();
            int i = 0;
            for (int a = 0; a < roomChildList.size(); a++) {
                JSONObject one = roomChildList.getJSONObject(a);
                String name = one.getString("dict_name");
                String id = one.getString("id");
                String ids = id.replace("160-", "");
                String remark = one.getString("memo");
                String count = "0";
                if (gsXls.containsKey(ids)) {
                    count = gsXls.get(ids);
                }
                one.put("count", count);
                one.put("title", name);
                one.put("remark", remark);
                one.put("id", id);

                JSONObject click = new JSONObject();
                click.put("type", "dialog");
                int ran = i % 4;
                if (ran == 0) {
                    click.put("permission", "chat_bar");
                    click.put("id", String.valueOf(UUID.randomUUID()));
                } else if (ran == 1) {
                    click.put("permission", "chat_pie");
                    click.put("id", String.valueOf(UUID.randomUUID()));
                } else if (ran == 2) {
                    click.put("permission", "chat_bar_tran");//chat_bar_tran 类型无法下转
                    click.put("id", String.valueOf(UUID.randomUUID()));
                } else {
                    click.put("permission", "chat_pie_3d");
                    click.put("id", String.valueOf(UUID.randomUUID()));
                }
                JSONObject cols = new JSONObject();

                cols.put("lx", ids);
                cols.put("point", ids);
                cols.put("unit", unit);
                JSONObject opts = GetOpts("/screen_point", "get_point_unit_level", cols);
                click.put("remark", opts);
                click.put("label", RIUtil.dicts.get(id).getString("dict_name"));

                one.put("click", click);


                JSONObject dpclick = new JSONObject();
                dpclick.put("type", "dialog");
                cols = new JSONObject();
                cols.put("point", ids);
                cols.put("unit", unit);

                opts = GetOpts("/screen_point", "get_point_loc_table", cols);
                dpclick.put("remark", opts);
                dpclick.put("permission", "table_page");
                dpclick.put("id", String.valueOf(UUID.randomUUID()));
                one.put("dpclick", dpclick);

                if (name != null && name.length() > 0) {
                    dets.add(one);
                }
                i++;
            }
            back.put("data", dets);

            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            mysql.close();
            my143.close();
        }
    }


    private JSONObject GetPointPersonLevel(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String lx = "";
        OracleHelper ora_gl = null;
        String subStr = "";
        String unit = "";
        String type = "";
        String uSql = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            type = RIUtil.dicts.get(unit).getString("type");
            if (type.equals("23") || type.equals("24") || type.equals("28")) {
                subStr = "substr(sszrq,1,8) ";
                uSql = " and sszrq like '" + unit.substring(0, 6) + "%' ";
            } else if (type.equals("25") || type.equals("26")) {
                subStr = "sszrq ";
                uSql = " and sszrq like '" + unit.substring(0, 8) + "%' ";
            } else {
                subStr = "substr(sszrq,1,6) ";

            }
        } else {
            return ErrNo.set(501001);
        }
        lx = data.getString("lx");

        try {
            ora_gl = new OracleHelper("ora_bk_gl");
            String sql =
                    "select count(1) as count ," + subStr + " as code from czqj_ybds.tb_data_watch_point where " +
                            "jclx_xl" + " ='" + lx + "' " + uSql + " group by " + subStr;
            logger.warn(sql);
            JSONArray dets = new JSONArray();
            List<JSONObject> list = ora_gl.query(sql);
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                logger.warn(one.toString());
                String code = one.getString("CODE");
                if (code.startsWith("3204")) {
                    if (code.length() == 6) {
                        code = code + "000000";
                    } else if (code.length() == 8) {
                        code = code + "0000";
                    } else if (code.length() == 4) {

                        code = code + "00000000";
                    } else {
                        code = code;
                    }
                    logger.warn(code);
                    String name = code;
                    try {
                        name = RIUtil.dicts.get(code).getString("dict_name");
                    } catch (Exception e) {
                    }
                    JSONObject click = new JSONObject();
                    click.put("type", "next");
                    click.put("id", String.valueOf(UUID.randomUUID()));
                    click.put("url", "/screen_point");
                    JSONObject cols = new JSONObject();
                    cols.put("lx", lx);
                    cols.put("unit", code);
                    cols.put("name", name);

                    click.put("opt", GetOpts("/screen_point", "get_point_person_level", cols));
                    if (!type.equals("25") && !type.equals("26")) {
                        one.put("dpclick", click);
                    }
                    one.put("name", name);
                    one.put("count", one.getString("COUNT"));

                    JSONObject dpclick = new JSONObject();
                    dpclick.put("type", "dialog1");
                    cols = new JSONObject();
                    cols.put("point", lx);
//                    dpclick.put("lable", unit + "_" + RIUtil.dicts.get(lx).getString("dict_name"));
                    cols.put("unit", code);
                    cols.put("name", name);

                    JSONObject opts = GetOpts("/screen_point", "get_point_loc_table", cols);
                    dpclick.put("remark", opts);
                    dpclick.put("permission", "table_page");
                    dpclick.put("id", String.valueOf(UUID.randomUUID()));

                    one.put("click", dpclick);

                    dets.add(one);
                }
            }
            back.put("data", dets);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            ora_gl.close();

        }
    }

    private JSONObject GetPointPerson(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        MysqlHelper mysql = null;
        OracleHelper ora_gl = null;
        String unit = "";
        String type = "";
        String usql = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            type = RIUtil.dicts.get(unit).getString("type");
            if (type.equals("23") || type.equals("24") || type.equals("28")) {
                usql = "sszrq like '" + unit.substring(0, 6) + "%'";

                unit = unit.substring(0, 6) + "000000";
            } else if (type.equals("25")) {
                usql = "sszrq like '" + unit.substring(0, 8) + "%'";
                unit = unit.substring(0, 8) + "0000";
            } else if (type.equals("26")) {
                usql = "sszrq = '" + unit + "'";
                unit = unit;

            } else {
                usql = " 1=1 ";
                unit = unit.substring(0, 4) + "00000000";

            }
        } else {
            return ErrNo.set(501001);
        }
        String father_id = data.getString("father_id");
        try {

            mysql = new MysqlHelper("mysql");
            ora_gl = new OracleHelper("ora_bk_gl");
            HashMap<String, String> xls = new HashMap<>();
            String sql =
                    "select count(id) as count,JCLX_XL from czqj_ybds.tb_data_watch_point where " + usql + " " +
                            "group by " + "JCLX_XL";
            logger.warn(sql);
            List<JSONObject> list = ora_gl.query(sql);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    xls.put(one.getString("JCLX_XL"), one.getString("COUNT"));
                }
            }
            sql = "select id,dict_name,memo from dict where isdelete=1 and father_id='" + father_id + "'";
            List<JSONObject> roomChildList = mysql.query(sql);
            JSONArray dets = new JSONArray();
            int i = 0;
            for (JSONObject one : roomChildList) {

                String name = one.getString("dict_name");
                String id = one.getString("id");
                String ids = id.replace("160-", "");
                String remark = one.getString("memo");
                String count = "0";
                if (xls.containsKey(ids)) {
                    count = xls.get(ids);
                }
                one.put("count", count);
                one.put("title", name);
                one.put("remark", remark);
                one.put("id", id);
                JSONObject click = new JSONObject();
                click.put("type", "dialog");
                int ran = i % 4;
                if (ran == 0) {
                    click.put("permission", "chat_bar");
                } else if (ran == 1) {
                    click.put("permission", "chat_pie");
                } else if (ran == 2) {
                    click.put("permission", "chat_bar_tran");
                } else {
                    click.put("permission", "chat_pie_3d");
                }
                JSONObject cols = new JSONObject();

                cols.put("lx", ids);
                cols.put("unit", unit);
                JSONObject opts = GetOpts("/screen_point", "get_point_person_level", cols);
                click.put("remark", opts);
                click.put("label", RIUtil.dicts.get(id).getString("dict_name"));

                one.put("click", click);
                //双击明细列表
                JSONObject dpclick = new JSONObject();
                dpclick.put("type", "dialog");
                cols = new JSONObject();
                cols.put("point", ids);
                cols.put("unit", unit);

                opts = GetOpts("/screen_point", "get_point_loc_table", cols);
                dpclick.put("remark", opts);
                dpclick.put("label", RIUtil.dicts.get(id).getString("dict_name"));
                dpclick.put("permission", "table_page");
                one.put("dpclick", dpclick);


                if (name != null && name.length() > 0) {
                    dets.add(one);
                }
                i++;
            }
            back.put("data", dets);

            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));

        } finally {
            mysql.close();
            ora_gl.close();
        }
    }


    private JSONObject GetPointLocTable(JSONObject data, String token) {
        JSONObject back = ErrNo.set(0);

        try {
            String unit = data.getString("unit");

            int type = 0;
            try {
                type = RIUtil.dicts.get(unit).getInteger("type");
            } catch (Exception ex) {
                type = 26;
            }
            int orgType = 0;
            if (type == 21 || type == 22 || type == 27) {
                orgType = 2;
                unit = unit.substring(0, 4) + "00000000";

            } else if (type == 23 || type == 24 || type == 28) {
                unit = unit.substring(0, 6) + "000000";
                orgType = 4;
            } else if (type == 25) {
                orgType = 4;
                unit = unit.substring(0, 8) + "0000";
            } else {
                unit = unit;
                orgType = 4;
            }
            long t = System.currentTimeMillis() / 1000;
            int page = data.getInteger("page");
            int limit = data.getInteger("limit");
            String point = data.getString("point");


            if (point.startsWith("2")) { //房屋

                String hdn = "序号,房屋地址,产权人,证件号码,所属社区,操作";
                String keys = "num,dz,sjczrXm,sjczrZjhm,zrq,opt";
                JSONArray heads = GetHeads(hdn, keys);

                String url = "http://50.56.94.47/czqjpt-api/fwxx/fwSjjcdTjmx?_t=" + t + "&jclxXl=" + point +
                        "&orgCode" + "=" + unit + "&jclx=fw&pageNo=" + page + "&pageSize=" + limit;
                logger.warn(url);
                JSONObject rets = GetOkHttpGet(url, token);
                JSONArray bodys = new JSONArray();
                int count = 0;
                if (rets.containsKey("code") && rets.getInteger("code") == 200) {
                    JSONObject result = rets.getJSONObject("result");
                    count = result.getInteger("total");
                    if (count > 0) {
                        JSONArray recods = result.getJSONArray("records");

                        for (int i = 0; i < recods.size(); i++) {
                            JSONObject one = recods.getJSONObject(i);
                            JSONObject det = new JSONObject();

                            String num = String.valueOf(i + 1);
                            JSONObject val = new JSONObject();
                            val.put("value", num);
                            det.put("num", val);

                            String dz = one.getString("dz");
                            String dzid = one.getString("dzid");

                            val = new JSONObject();
                            val.put("value", dz);
                            JSONObject click = new JSONObject();
                            click.put("type", "jump_house");
                            click.put("url", dzid);
                            val.put("click", click);
                            det.put("dz", val);

                            String sjczrXm = one.getString("sjczrXm");
                            val = new JSONObject();
                            val.put("value", sjczrXm);
                            det.put("sjczrXm", val);

                            String sjczrZjhm = one.getString("sjczrZjhm");
                            val = new JSONObject();
                            val.put("value", sjczrZjhm);
                            click = new JSONObject();
                            click.put("type", "jump_person");
                            click.put("url", sjczrZjhm);
                            val.put("click", click);
                            det.put("sjczrZjhm", val);

                            String zrq = one.getString("sszrqmc");
                            val = new JSONObject();
                            val.put("value", zrq);
                            det.put("sszrqmc", val);

                            //  one.getJSONArray("cqrs").getJSONObject(0).getString("syfwId");
                            val = new JSONObject();
                            val.put("value", "操作");
                            click = new JSONObject();
                            click.put("type", "jump_house_opt");
                            click.put("url", dzid);
                            val.put("click", click);
                            det.put("opt", val);

                            bodys.add(det);

                        }


                    }


                } else {
                    return ErrNo.set(null, 2, rets.getString("message"));
                }

                JSONObject dets = new JSONObject();
                dets.put("head", heads);
                dets.put("body", bodys);
                dets.put("count", count);

                back.put("data", dets);


                return back;

            } else if (point.startsWith("3")) {//r人口
                if (point.equals("3005")) {//境外人员照片
                    String hdn = "序号,姓名,人员类别,证件类型,证件号码,联系电话,国籍（地区）,居住地址,所属单位,修改时间,操作";
                    String keys =
                            "num,xm,syrkgllbdm_dictText,cyzjdm_dictText,zjhm,lxdh,gjhdqdm_dictText,sjjzdDzmc," +
                                    "sjgsdwmc,czsj,opt";
                    JSONArray heads = GetHeads(hdn, keys);

                    String url =
                            "http://50.56.94.47/czqjpt-api/jwry/list?t=" + t + "&sjgsdwdm=" + unit + "&wcjzp=1" +
                                    "&pageNo=" + page + "&pageSize=" + limit;

                    logger.warn(url);
                    JSONObject rets = GetOkHttpGet(url, token);
                    JSONArray bodys = new JSONArray();
                    int count = 0;
                    if (rets.containsKey("code") && rets.getInteger("code") == 200) {
                        JSONObject result = rets.getJSONObject("result");
                        count = result.getInteger("total");
                        if (count > 0) {
                            JSONArray recods = result.getJSONArray("records");

                            for (int i = 0; i < recods.size(); i++) {
                                JSONObject one = recods.getJSONObject(i);
                                JSONObject det = new JSONObject();

                                String[] key = keys.split(",");
                                for (int k = 0; k < key.length; k++) {
                                    String c = key[k];
                                    String v = "";
                                    if (c.equals("num")) {
                                        v = String.valueOf(i + 1);
                                    } else if (c.equals("opt")) {
                                        v = "操作";
                                    } else {
                                        v = one.getString(c);
                                    }

                                    JSONObject val = new JSONObject();
                                    val.put("value", v);
                                    if (c.equals("opt")) {
                                        JSONObject click = new JSONObject();
                                        click.put("type", "jump_jwry");
                                        click.put("url", one.getString("id"));
                                        val.put("click", click);
                                    }
                                    det.put(c, val);
                                }
                                bodys.add(det);
                            }
                        }
                    } else {
                        return ErrNo.set(null, 2, rets.getString("message"));
                    }

                    JSONObject dets = new JSONObject();
                    dets.put("head", heads);
                    dets.put("body", bodys);
                    dets.put("count", count);
                    back.put("data", dets);


                } else if (point.equals("3006")) {
                    String hdn = "序号,姓名,人员类别,证件类型,证件号码,联系电话,国籍（地区）,居住地址,所属单位,修改时间,操作";
                    String keys =
                            "num,xm,syrkgllbdm_dictText,cyzjdm_dictText,zjhm,lxdh,gjhdqdm_dictText,sjjzdDzmc," +
                                    "sjgsdwmc,czsj,opt";
                    JSONArray heads = GetHeads(hdn, keys);

                    String url = "http://50.56.94.47/czqjpt-api/jwry/list?t=" + t + "&sjgsdwdm=" + unit + "&sjjzdyc=1"
                            + "&pageNo=" + page + "&pageSize=" + limit;

                    logger.warn(url);
                    JSONObject rets = GetOkHttpGet(url, token);
                    JSONArray bodys = new JSONArray();
                    int count = 0;
                    if (rets.containsKey("code") && rets.getInteger("code") == 200) {
                        JSONObject result = rets.getJSONObject("result");
                        count = result.getInteger("total");
                        if (count > 0) {
                            JSONArray recods = result.getJSONArray("records");

                            for (int i = 0; i < recods.size(); i++) {
                                JSONObject one = recods.getJSONObject(i);
                                JSONObject det = new JSONObject();

                                String[] key = keys.split(",");
                                for (int k = 0; k < key.length; k++) {
                                    String c = key[k];
                                    String v = "";
                                    if (c.equals("num")) {
                                        v = String.valueOf(i + 1);
                                    } else if (c.equals("opt")) {
                                        v = "操作";
                                    } else {
                                        v = one.getString(c);
                                    }

                                    JSONObject val = new JSONObject();
                                    val.put("value", v);
                                    if (c.equals("opt")) {
                                        JSONObject click = new JSONObject();
                                        click.put("type", "jump_jwry");
                                        click.put("url", one.getString("id"));
                                        val.put("click", click);
                                    }
                                    det.put(c, val);
                                }
                                bodys.add(det);
                            }
                        }
                    } else {
                        return ErrNo.set(null, 2, rets.getString("message"));
                    }

                    JSONObject dets = new JSONObject();
                    dets.put("head", heads);
                    dets.put("body", bodys);
                    dets.put("count", count);
                    back.put("data", dets);
                } else {

                    String hdn = "序号,人口类型,地址状态,姓名,性别,民族,联系电话,公民身份号码,出生日期,现住地,所属社区,操作";
                    String keys = "num,rklx_dictText,dzzt_dictText,xm,xbdm_dictText,mzdm_dictText,lxdh,zjhm,csrq," +
                            "sjjzdDzbm,zrq,opt";
                    JSONArray heads = GetHeads(hdn, keys);

                    String url = "http://50.56.94.47/czqjpt-api/syrk/rkSjjcdTjmx?pageNo=" + page + "&_t=" + t + "" +
                            "&jclxXl" + "=" + point + "&orgCode=" + unit + "&pageSize=" + limit;
                    logger.warn(url);
                    JSONObject rets = GetOkHttpGet(url, token);
                    JSONArray bodys = new JSONArray();
                    int count = 0;
                    if (rets.containsKey("code") && rets.getInteger("code") == 200) {
                        JSONObject result = rets.getJSONObject("result");
                        count = result.getInteger("total");
                        if (count > 0) {
                            JSONArray recods = result.getJSONArray("records");

                            for (int i = 0; i < recods.size(); i++) {
                                JSONObject one = recods.getJSONObject(i);
                                JSONObject det = new JSONObject();

                                String num = String.valueOf(i + 1);
                                JSONObject val = new JSONObject();
                                val.put("value", num);
                                det.put("num", val);

                                val = new JSONObject();
                                val.put("value", one.getString("csrq"));
                                det.put("csrq", val);

                                val = new JSONObject();
                                val.put("value", one.getString("sjjzdDzmc"));
                                det.put("sjjzdDzbm", val);


                                val = new JSONObject();
                                val.put("value", one.getString("sjgsdwmc"));
                                det.put("zrq", val);


                                val = new JSONObject();
                                val.put("value", one.getString("lxdh"));
                                det.put("lxdh", val);

                                val = new JSONObject();
                                val.put("value", one.getString("mzdm_dictText"));
                                det.put("mzdm_dictText", val);

                                val = new JSONObject();
                                val.put("value", one.getString("xbdm_dictText"));
                                det.put("xbdm_dictText", val);

                                val = new JSONObject();
                                val.put("value", one.getString("rklx_dictText"));
                                det.put("rklx_dictText", val);

                                val = new JSONObject();
                                val.put("value", one.getString("dzzt_dictText"));
                                det.put("dzzt_dictText", val);

                                val = new JSONObject();
                                val.put("value", one.getString("xm"));
                                det.put("xm", val);

                                String sjczrXm = one.getString("sjczrXm");
                                val = new JSONObject();
                                val.put("value", sjczrXm);
                                det.put("sjczrXm", val);

                                String zjhm = one.getString("zjhm");
                                val = new JSONObject();
                                val.put("value", zjhm);
                                JSONObject click = new JSONObject();
                                click.put("type", "jump_person");
                                click.put("url", zjhm);
                                val.put("click", click);
                                det.put("zjhm", val);

                                String zrq = one.getString("sszrqmc");
                                val = new JSONObject();
                                val.put("value", zrq);
                                det.put("sszrqmc", val);

                                val = new JSONObject();
                                val.put("value", "操作");
                                click = new JSONObject();
                                click.put("type", "jump_person_opt");
                                click.put("url", zjhm);
                                val.put("click", click);
                                det.put("opt", val);

                                bodys.add(det);

                            }


                        }


                    } else {
                        return ErrNo.set(null, 2, rets.getString("message"));
                    }

                    JSONObject dets = new JSONObject();
                    dets.put("head", heads);
                    dets.put("body", bodys);
                    dets.put("count", count);
                    back.put("data", dets);
                }

                return back;
            } else if (point.startsWith("1")) {//标准地址
                if (!point.contains("1002")) {

                    String hdn = "序号,所属单位,地址名称,状态,操作";
                    String keys = "num,czdwmc,dz,dzzt_dictText,opt";
                    JSONArray heads = GetHeads(hdn, keys);

                    String url = "http://50.56.94.47/czqjpt-api/fwxx/fwSjjcdTjmx?_t=" + t + "&jclxXl=" + point +
                            "&orgCode" + "=" + unit + "&jclx=dz&column=createTime&order=desc&pageNo=" + page +
                            "&pageSize=" + limit;
                    logger.warn(url);
                    JSONObject rets = GetOkHttpGet(url, token);
                    JSONArray bodys = new JSONArray();
                    int count = 0;

                    if (rets.containsKey("code") && rets.getInteger("code") == 200) {
                        JSONObject result = rets.getJSONObject("result");
                        count = result.getInteger("total");
                        if (count > 0) {
                            JSONArray recods = result.getJSONArray("records");

                            for (int i = 0; i < recods.size(); i++) {
                                JSONObject one = recods.getJSONObject(i);
                                JSONObject det = new JSONObject();

                                String num = String.valueOf(i + 1);
                                JSONObject val = new JSONObject();
                                val.put("value", num);
                                det.put("num", val);

                                String dw = one.getString("pcsmc") + one.getString("hjzrqmc");
                                val = new JSONObject();
                                val.put("value", dw);
                                det.put("czdwmc", val);

                                String dzid = one.getString("dzid");
                                String dz = one.getString("dz");
                                if (point.contains("1004") || point.contains("1006")) {
                                    String x = one.getString("dzzbx");
                                    String y = one.getString("dzzby");
                                    dz = dz + "(" + x + "," + y + ")";
                                }
                                val = new JSONObject();
                                val.put("value", dz);
                                JSONObject click = new JSONObject();
                                click.put("type", "jump_house");
                                click.put("url", dzid);
                                val.put("click", click);
                                det.put("dz", val);

                                String dzzt_dictText = one.getString("dzzt_dictText");
                                val = new JSONObject();
                                val.put("value", dzzt_dictText);
                                det.put("dzzt_dictText", val);


                                val = new JSONObject();
                                val.put("value", "开始标注");
                                click = new JSONObject();
                                click.put("type", "jump_house");
                                click.put("url", dzid);
                                val.put("click", click);
                                det.put("opt", val);

                                bodys.add(det);

                            }

                        }


                    } else {
                        return ErrNo.set(null, 2, rets.getString("message"));
                    }
                    JSONObject dets = new JSONObject();
                    dets.put("head", heads);
                    dets.put("body", bodys);
                    dets.put("count", count);
                    back.put("data", dets);
                } else {

                    String hdn = "民警姓名,所属单位,走访成果,开始时间,结束时间,操作";
                    String keys = "czrxm,czdwmc,rhzfTotal,kssj,jssj,opt";
                    JSONArray heads = GetHeads(hdn, keys);
                    JSONArray bodys = new JSONArray();
                    int count = 0;
                    String url = "http://50.56.94.47/czqjpt-api/xsq/page?_t=" + t + "&sszrq=" + unit + "&sfyc=1";
                    JSONObject rets = GetOkHttpGet(url, token);

                    if (rets.containsKey("code") && rets.getInteger("code") == 200) {
                        JSONObject result = rets.getJSONObject("result");
                        count = result.getInteger("total");
                        if (count > 0) {
                            JSONArray recods = result.getJSONArray("records");

                            for (int i = 0; i < recods.size(); i++) {
                                JSONObject one = recods.getJSONObject(i);
                                JSONObject det = new JSONObject();

                                String key[] = keys.split(",");
                                for (int k = 0; k < key.length; k++) {
                                    String kk = key[k];
                                    JSONObject val = new JSONObject();


                                    if (kk.equals("opt")) {
                                        val.put("value", "轨迹");
                                        JSONObject click = new JSONObject();
                                        click.put("type", "jump");
                                        click.put("url",
                                                "http://ybls.qjjcgzpt.czx" + ".js/oauth2-app/login?redirect" +
                                                        "=/openPage/xsqGjMap&xsqId=" + one.getString("id") +
                                                        "&orgCode=" + unit + "&oauth2LoginToken=" + token);
                                        val.put("click", click);
                                    } else {
                                        val.put("value", one.getString(kk));
                                    }

                                    det.put(kk, val);
                                    bodys.add(det);
                                }


                            }
                        }
                        JSONObject dets = new JSONObject();
                        dets.put("head", heads);
                        dets.put("body", bodys);
                        dets.put("count", count);
                        back.put("data", dets);
                    } else {
                        return ErrNo.set(null, 2, rets.toString());
                    }


                }


                return back;

            } else if (point.startsWith("4")) { //单位
                String hdn = "序号,单位名称,所属单位,操作";
                String keys = "num,dwmc,ssdw,opt";
                JSONArray heads = GetHeads(hdn, keys);

                JSONObject rets = GetPointLoc(data);
                JSONArray bodys = new JSONArray();
                int count = 0;
                if (rets.containsKey("errno") && rets.getInteger("errno") == 0) {
                    JSONArray datas = rets.getJSONArray("data");
                    count = rets.getInteger("count");

                    int start = (page - 1) * limit;
                    int end = limit;
                    logger.warn(start + "->" + end);
                    for (int i = 0; i < datas.size(); i++) {
                        try {
                            JSONObject one = datas.getJSONObject(i);
                            JSONObject det = new JSONObject();

                            String num = String.valueOf(i + 1);
                            JSONObject val = new JSONObject();
                            val.put("value", num);
                            det.put("num", val);

                            String dw = RIUtil.dicts.get(one.getString("SSZRQ")).getString("remark");
                            val = new JSONObject();
                            val.put("value", dw);
                            det.put("ssdw", val);

                            String dzid = one.getString("ID");
                            String dwmc = one.getString("NAME");
                            val = new JSONObject();
                            val.put("value", dwmc);
                            JSONObject click = new JSONObject();
                            click.put("type", "jump_unit");
                            click.put("url", dzid);
                            val.put("click", click);
                            det.put("dwmc", val);


                            val = new JSONObject();
                            val.put("value", "操作");
                            click = new JSONObject();
                            click.put("type", "jump_unit");
                            click.put("url", dzid);
                            val.put("click", click);
                            det.put("opt", val);

                            bodys.add(det);


                        } catch (Exception ex) {

                        }
                    }


                } else {
                    return rets;
                }

                JSONObject dets = new JSONObject();
                dets.put("head", heads);
                dets.put("body", bodys);
                dets.put("count", count);
                back.put("data", dets);


                return back;

            } else {
                return ErrNo.set(505001);
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }


    private HashMap<String, String> GetDictNames(String id) {
        HashMap<String, String> rets = new HashMap<>();
        String[] ids = id.split(",");
        for (int i = 0; i < ids.length; i++) {
            String d = ids[i];
            String name = RIUtil.dicts.get(d).getString("dict_name");
            rets.put(d, name);


        }
        return rets;
    }

    private JSONObject GetOkHttpGet(String url, String token) {
        try {

            OkHttpClient client = new OkHttpClient().newBuilder().build();
            Request request =
                    new Request.Builder().url(url).method("GET", null).addHeader("X-Access-Token", token).build();
            Response response = client.newCall(request).execute();
            String res = response.body().string();
            JSONObject resj = JSONObject.parseObject(res);
            return resj;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            JSONObject back = new JSONObject();
            back.put("code", 301);
            back.put("message", Lib.getTrace(ex));
            return back;
        }
    }

    private int ExportTables(JSONArray datas, String head, String keys, String name) {


        String FileName = name + "_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        String[] heads = head.split(",");
        String key[] = keys.split(",");
        for (int i = 0; i < key.length; i++) {
            header.add(key[i]);
            headername.put(key[i], heads[i]);
        }


        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
//            for (int i = 0; i < datas.size(); i++) {
//                JSONObject jsonObject = datas.getJSONObject(i);
//                JSONArray jsonArray = jsonObject.getJSONArray("real_all");
            exporthelper.write_data(datas);
//            }

            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql = "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath +
                        "','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");

                logger.warn("-->" + id);
                try {
                    if (exporthelper != null) {
                        try {
                            exporthelper.close();
                        } catch (Exception ex) {

                        }

                    }
                } catch (Exception ex) {

                }

                String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;
                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
                logger.warn(obsFileName + "-->" + ret);
                logger.warn("id->" + id);
                return id;
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return 0;
        } finally {

            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }
        }
    }


    private JSONArray GetHeads(String headN, String hdKeys) {
        JSONArray rets = new JSONArray();
        String[] hdns = headN.split(",");
        String[] hdks = hdKeys.split(",");

        for (int i = 0; i < hdks.length; i++) {
            JSONObject det = new JSONObject();
            String key = hdks[i];
            String value = hdns[i];
            det.put("key", key);
            det.put("value", value);
            rets.add(det);

        }
        return rets;
    }

    private JSONObject GetOpts(String url, String opt, JSONObject cols) {

        JSONObject opts = new JSONObject();
        opts.put("url", url);
        opts.put("opt", opt);
        opts.put("opt_user", "$opt_user$");


        opts.putAll(cols);
        return opts;
    }

    private static String StringToURL(String s) {

        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            if (c >= 0 && c <= 255) {
                sb.append(c);
            } else {
                byte[] b;
                try {
                    b = String.valueOf(c).getBytes("utf-8");
                } catch (Exception ex) {
                    System.out.println(ex);
                    b = new byte[0];
                }
                for (int j = 0; j < b.length; j++) {
                    int k = b[j];
                    if (k < 0) k += 256;
                    sb.append("%" + Integer.toHexString(k).toUpperCase());
                }
            }
        }
        return sb.toString();

    }

    private JSONArray GetLabels(String labs, String colors) {
        JSONArray lables = new JSONArray();
        String lab[] = labs.split(",");
        String cols[] = colors.split(",");
        for (int i = 0; i < lab.length; i++) {
            String one = lab[i];
            if (one != null || one.length() > 0) {
                String color = cols[i];

                JSONObject label = new JSONObject();
                label.put("id", i);
                label.put("name", one);
                label.put("color", color);
                lables.add(label);
            }
        }
        return lables;
    }

}
