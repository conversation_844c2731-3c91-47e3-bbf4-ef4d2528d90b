package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.wechat.wechatMsgTemp;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@RestController
public class TaskApplyController {
    private static Logger logger = LoggerFactory.getLogger(TaskApplyController.class);
    private static String ip = "";

    @RequestMapping(method = {RequestMethod.POST}, path = {"/task_apply"})
    public static JSONObject get_task_apply(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        String opt = "";
        String ip = request.getRemoteAddr();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("task_apply_get")) {
                return getTaskApply(data);
            } else if (opt.equals("task_apply_create")) {
                logger.warn("task_apply--->" + data.toString());
                return createTaskApply(data, ip);
            } else if (opt.equals("task_apply_update")) {
                logger.warn("task_apply--->" + data.toString());
                return updateTaskApply(data, ip);
            } else if (opt.equals("task_apply_delete")) {
                return deleteTaskApply(data, ip);
            } else {
                return ErrNo.set(456009);
            }
        } else {
            return ErrNo.set(456009);
        }
    }

    //******CREATE*******
    public static JSONObject createTaskApply(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String create_user = "";
            String accepter = "";
            String content = "";
            String apply_type = "";
            String reply_type = "0";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String task_id = "";
            String isdelete = "1";
            String applyType = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                return ErrNo.set(456002);
            }
            if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
                accepter = data.getString("accepter");
            } else {
                return ErrNo.set(456002);
            }
            if (data.containsKey("task_id") && data.getString("task_id").length() > 0) {
                task_id = data.getString("task_id");
            } else {
                return ErrNo.set(456002);
            }
            if (data.containsKey("content") && data.getString("content").length() > 0) {
                content = data.getString("content");
            } else {
                return ErrNo.set(456002);
            }
            if (data.containsKey("apply_type") && data.getString("apply_type").length() > 0) {
                apply_type = data.getString("apply_type");
                if (apply_type.equals("1")) {
                    applyType = "延期";
                } else {
                    applyType = "取消";
                }
            } else {
                return ErrNo.set(456002);
            }
            String sqls = "select end_time from task where id='" + task_id + "'";
            String end_time = mysql.query_one(sqls, "end_time");
            long et = RIUtil.dateToStamp(end_time);
            if (et - System.currentTimeMillis() > 0) {
                sqls = "insert task_apply (create_user,accepter,content,apply_type,create_time,reply_type,isdelete," +
                        "task_id)" +
                        "values('" + create_user + "','" + accepter + "',encode('" + content + "','" + RIUtil.enContent + "'),'" + apply_type + "','" + create_time + "','" + reply_type + "','" + isdelete + "','" + task_id + "')";
                mysql.update(sqls);
                sqls = "select id from task_apply where task_id='" + task_id + "' and create_user='" + create_user +
                        "' and apply_type='" + apply_type + "' and create_time='" + create_time + "'";
                logger.warn(sqls);
                String id = mysql.query_one(sqls, "id");
                UserLog userlog = new UserLog();
                userlog.log(mysql, create_user, "创建事项申请", userlog.TYPE_OPERATE, remoteAddr);
                wechatMsgTemp.createDingMsg(task_id, "申请事项" + applyType, create_user, 2, accepter, mysql, id);
            } else {
                return JSONObject.parseObject("{\"errno\":456010,\"error\":\"事项申请操作失败，该事项已到期\"}");
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(456001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    public static JSONObject getTaskApply(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String reply_type = "";
            String task_id = "";
            String accepter = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id in ('" + id.replace(",", "','") + "') and ";
            }

            if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
                accepter = data.getString("accepter");
                sql = sql + " accepter='" + accepter + "' and ";
            }
            if (data.containsKey("reply_type") && data.getString("reply_type").length() > 0) {
                reply_type = data.getString("reply_type");
                sql = sql + " reply_type='" + reply_type + "' and ";
            }
            if (data.containsKey("task_id") && data.getString("task_id").length() > 0) {
                task_id = data.getString("task_id");
                sql = sql + " task_id='" + task_id + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select *,decode(content,'" + RIUtil.enContent + "') as content,decode(refuse,'" + RIUtil.enContent + "') as refuse from task_apply where 1=1 and " + sql + " isdelete=1 limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select id from task_apply where 1=1 and " + sql + " isdelete=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(456005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private static List<JSONObject> RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String create_user = one.getString("create_user");

            one.put("create_user", RIUtil.users.get(create_user));
            String accepter = one.getString("accepter");

            one.put("accepter", RIUtil.users.get(accepter));
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    public static JSONObject updateTaskApply(JSONObject data, String ip) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";

            String delay_time = "";

            String reply_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String reply_type = "";
            String refuse = "";
            String opt_user = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(456004);
            }

            String s = "select task_id,apply_type,create_user from task_apply where id='" + id + "'";
            List<JSONObject> list = mysql.query(s);
            String task_id = list.get(0).getString("task_id");
            String apply_type = list.get(0).getString("apply_type");
            String create_user = list.get(0).getString("create_user");
            if (data.containsKey("delay_time") && data.getString("delay_time").length() > 0) {
                delay_time = data.getString("delay_time");
                sql = sql + " delay_time='" + delay_time + "' , ";
            }

            if (data.containsKey("reply_type") && data.getString("reply_type").length() > 0) {
                reply_type = data.getString("reply_type");
                sql = sql + " reply_type='" + reply_type + "' , ";
            }
            if (data.containsKey("refuse") && data.getString("refuse").length() > 0) {
                refuse = data.getString("refuse");
                sql = sql + " refuse=encode( '" + refuse + "','" + RIUtil.enContent + "') ,";
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }

            if (apply_type.equals("1") && reply_type.equals("1") && delay_time.length() == 0) {
                logger.warn("无延期时间");
                return ErrNo.set(456004);
            }
            if (reply_type.equals("2") && refuse.length() == 0) {
                logger.warn("无拒绝理由");
                return ErrNo.set(456004);
            }
            String sqls = "select a.*,DECODE(b.title,'1t2i3t4l5e') as title from task_accepter a left join task b on " +
                    "a.rela_id=b.id " +
                    "where a.rela_id='" + task_id + "' and user_id='" + create_user + "' ";
            List<JSONObject> ll = mysql.query(sqls);
            String end_time = ll.get(0).getString("end_time");
            String start_time = ll.get(0).getString("start_time");
            String user_id = ll.get(0).getString("user_id");
            String title = ll.get(0).getString("title");
            String near_time = ll.get(0).getString("near_time");
            if (title.length() > 10) {
                title = title.substring(0, 10);
            }

            long et = RIUtil.dateToStamp(end_time);
            if (et - System.currentTimeMillis() > 0) {
                if (apply_type.equals("1") && reply_type.equals("1") && delay_time.length() > 0) {

                    //延期同意

                    String dt[] = delay_time.split(",");
                    int days = Integer.parseInt(dt[0]);
                    int hours = Integer.parseInt(dt[1]);
                    try {
                        near_time = dt[2].replace("|", " ");
                    } catch (Exception ex) {

                    }
                    long hm = (days * 24 + hours) * 60 * 60 * 1000;
                    long endT = RIUtil.dateToStamp(end_time);
                    long endTime = endT + hm;
                    end_time = RIUtil.stampToTime(endTime);
                    long nt = RIUtil.dateToStamp(near_time);
                    nt = nt + hm;
                    if (near_time.equals("")) {
                        near_time = RIUtil.stampToTime(nt);
                    }
                    sqls = "update task_accepter set end_time='" + end_time + "',near_time='" + near_time + "' where " +
                            "rela_id='" + task_id + "' and user_id='" + create_user + "' ";
                    logger.warn(sqls);
                    mysql.update(sqls);
                    wechatMsgTemp.createDingMsg(task_id, title + " 已延期", opt_user, 2, user_id, mysql, id);
                }
                if (apply_type.equals("1") && reply_type.equals("2") && delay_time.length() > 0) {
                    //延期拒绝
                    wechatMsgTemp.createDingMsg(task_id, title + " 延期驳回", opt_user, 2, create_user, mysql, id);
                }
                if (apply_type.equals("2") && reply_type.equals("1")) {
                    //取消同意
                    sqls = "update task_accepter set isdelete=2 where rela_id='" + task_id + "' and user_id='" + user_id + "'";
                    mysql.update(sqls);
                    //task
                    sqls = "select accepter,checked,finished from task where id='" + task_id + "'";
                    List<JSONObject> tasks = mysql.query(sqls);
                    String accepter = tasks.get(0).getString("accepter");
                    HashMap<String, String> accs = RIUtil.StringToList(accepter);
                    accs.remove(user_id);
                    accepter = RIUtil.HashToList(accs).toString();
                    String checked = tasks.get(0).getString("checked");
                    HashMap<String, String> chs = RIUtil.StringToList(checked);
                    chs.remove(user_id);
                    checked = RIUtil.HashToList(chs).toString();
                    String finished = tasks.get(0).getString("finished");
                    HashMap<String, String> fs = RIUtil.StringToList(finished);
                    fs.remove(user_id);
                    finished = RIUtil.HashToList(fs).toString();

                    sqls = "update task set accepter='" + accepter + "',checked='" + checked + "',finished='" + finished + "' where id='" + task_id + "'";
                    mysql.update(sqls);

                    wechatMsgTemp.createDingMsg(task_id, title + " 同意取消", create_user, 2, create_user, mysql, id);
                }

                if (apply_type.equals("2") && reply_type.equals("2") && refuse.length() > 0) {
                    //取消拒绝
                    wechatMsgTemp.createDingMsg(task_id, title + " 取消驳回", opt_user, 2, create_user, mysql, id);
                }

                sqls = "update task_apply set " + sql + " isdelete=1 , reply_time='" + reply_time + "'  where id='" + id + "'";
                mysql.update(sqls);
                logger.warn(sqls);
                UserLog userlog = new UserLog();
                userlog.log(mysql, opt_user, "更新事项申请", userlog.TYPE_OPERATE, ip);
            } else {
                wechatMsgTemp.createDingMsg(task_id, title + " 申请驳回 已超期", opt_user, 2, create_user, mysql, id);
                return ErrNo.set(456010);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(456003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    public static JSONObject deleteTaskApply(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(456008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(456008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update task_apply set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除事项申请", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(456007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

}
