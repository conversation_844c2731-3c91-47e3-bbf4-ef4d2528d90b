package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@RestController
public class CommunityCheckController {
    private static Logger logger = LoggerFactory.getLogger(CommunityCheckController.class);

    @RequestMapping(method = {RequestMethod.POST}, path = {"/comm_check"})
    @PassToken
    public static JSONObject getCommunityCheck(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        String clientIP = request.getRemoteAddr();
        String opt = "";
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_comm_check")) {
                return getCommCheck(data);
            } else if (opt.equals("create_comm_check")) {
                logger.warn("comm_check->" + data);
                return createCommCheck(data, clientIP);
            } else if (opt.equals("update_comm_check")) {
                logger.warn("comm_check->" + data);
                return updateCommCheck(data, clientIP);
            } else if (opt.equals("delete_comm_check")) {
                return deleteCommCheck(data, clientIP);
            } else if (opt.equals("get_comm_check_last")) {
                return GetCommCheckLast(data);
            } else if (opt.equals("get_comm_check_model")) {
                return getCommCheckModel(data);
            } else if (opt.equals("get_comm_check_1")) {
                return getCommCheck1(data);
            } else {
                return ErrNo.set(452009);
            }
        } else {
            return ErrNo.set(452009);
        }
    }

    private static JSONObject GetCommCheckLast(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select month from community_check where isdelete=1 order by month desc limit 1";
            String month = mysql.query_one(sql, "month");
            String cuMonth = new SimpleDateFormat("yyMM").format(new Date());
            String unit = "";

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");

            }
            if (month.length() > 0) {
                if (!month.equals(cuMonth)) {
                    sql =
                            "select id,community,police,month,rank from community_check where month='" + month + "' " + "and" + " unit='" + unit + "'" + " and isdelete=1";
                    logger.warn(sql);
                    List<JSONObject> list = mysql.query(sql);
                    back.put("data", RelaInfo(list, mysql));
                } else {
                    List<JSONObject> nn = new ArrayList<>();
                    back.put("data", nn);
                }
            } else {
                sql =
                        "select id,id as police, community,'' as month from user where (position like '%" + RIUtil.GetSZid(unit, "社区副所长") + "%' or position like '%" + RIUtil.GetSZid(unit, "社区民警") + "%') and isdelete=1 and status=1 and unit='" + unit + "'";
                logger.warn(sql);
                List<JSONObject> list = mysql.query(sql);
                back.put("data", RelaInfo(list, mysql));
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(452001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject createCommCheck(JSONObject req, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            JSONArray ds = req.getJSONArray("data");
            String create_user = "";
            String month = "";
            if (req.containsKey("opt_user") && req.getString("opt_user").length() > 0) {
                create_user = req.getString("opt_user");
            } else {
                logger.warn("opt_user");
                return ErrNo.set(452002);
            }

            String unit = "";

            if (req.containsKey("unit") && req.getString("unit").length() > 0) {
                unit = req.getString("unit");

            }
            if (ds.size() > 0) {
                for (int i = 0; i < ds.size(); i++) {
                    JSONObject data = ds.getJSONObject(i);
                    String police = "";

                    String community = "";

                    String rank = "-999";
                    String detail = "";
                    int total = 0;
                    String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

                    String isdelete = "1";
                    if (data.containsKey("police") && data.getString("police").length() > 0) {
                        police = data.getString("police");
                    } else {
                        logger.warn("police");
                        return ErrNo.set(452002);
                    }
                    if (data.containsKey("month") && data.getString("month").length() > 0) {
                        month = data.getString("month");
                    } else {
                        logger.warn("month");
                        return ErrNo.set(452002);
                    }
                    if (data.containsKey("community") && data.getString("community").length() > 0) {
                        community = data.getString("community");
                    } else {
                        logger.warn("community");
                        return ErrNo.set(452002);
                    }

                    if (data.containsKey("rank") && data.getString("rank").length() > 0) {
                        rank = data.getString("rank");
                    }

                    if (data.containsKey("detail") && data.getString("detail").length() > 0) {
                        detail = data.getString("detail");
                    }

                    String sqls =
                            "delete from community_check where police like '%" + police + "%' and month='" + month +
                                    "' and community='" + community + "'";
                    mysql.update(sqls);

                    sqls = "insert community_check (police,month,community,create_time,create_user,isdelete,total," + "rank," + "detail,unit) values ('" + police + "','" + month + "','" + community + "','" + create_time + "','" + create_user + "','" + isdelete + "','" + total + "'," + "'" + rank + "','" + detail + "','" + unit + "')";
                    mysql.update(sqls);


                }
            } else {
                logger.warn("kong");
                return ErrNo.set(452002);
            }
            //统计
            CheckALL(mysql, month, unit);

            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建社区考勤", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(452001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    public static void CheckALL(InfoModelHelper mysql, String month, String unit) {
        int isCacu = 0;
        try {
            HashMap<String, Integer> cals = new HashMap<>();


            HashMap<String, String> checks = GetChecks(mysql, unit);//jqj
            logger.warn(checks.toString());
            if (checks.size() > 0) {
                isCacu = 1;
            }


            String sqls =
                    "select count(id) as count from community_check where community='all' and month='" + month + "' " + "and unit='" + unit + "'";
            int count = mysql.query_count(sqls);
            if (count == 0) {
                sqls = "insert into community_check (month,community,unit) values ('" + month + "','all','" + unit +
                        "');";
                mysql.update(sqls);

            }
            sqls = "select id ,detail,community from community_check where month='" + month + "' and unit='" + unit + "'";
            List<JSONObject> list = mysql.query(sqls);
            if (list.size() > 0) {
                for (int a = 0; a < list.size(); a++) {
                    JSONObject one = list.get(a);
                    String community = one.getString("community");
                    if (community.equals("all")) {
                        continue;
                    }
                    String id = one.getString("id");
                    JSONArray details = one.getJSONArray("detail");
                    JSONArray dets = new JSONArray();
                    int total = 0;//--jqj-end
                    if (details != null && details.size() > 0) {

                        for (int d = 0; d < details.size(); d++) {
                            JSONObject done = details.getJSONObject(d);
                            String col = done.getString("col_name");
                            JSONArray keys = done.getJSONArray("keys");
                            //--jqj
                            String check = "*10";
                            JSONArray kkes = new JSONArray();
                            if (isCacu == 1) {

                                if (checks.containsKey(col)) {
                                    check = checks.get(col);
                                }

                            }
                            int goal = 0;
                            int finish = 0;
                            int use = 0;//--jqj-end
                            for (int k = 0; k < keys.size(); k++) {
                                JSONObject kone = keys.getJSONObject(k);

                                JSONObject df = new JSONObject();
                                if (kone.getInteger("type") == 1 || kone.getInteger("type") == 3) {
                                    String dstr = kone.getString("data");
                                    if (dstr.contains(".")) {
                                        String[] dd = dstr.split("\\.");
                                        dstr = dd[0];
                                    }
                                    int date = 0;
                                    if (dstr != null && dstr.length() > 0) {
                                        date = Integer.parseInt(dstr);
                                    }
                                    String name = kone.getString("name");
                                    if (cals.containsKey(col + "_" + name)) {


                                        int dcal = cals.get(col + "_" + name) + date;
                                        cals.put(col + "_" + name, dcal);
                                    } else {
                                        cals.put(col + "_" + name, date);
                                    }

                                    if (isCacu == 1) {
                                        //jqj-start

                                        if (check.equals("*10") && name.equals("goal")) {
                                            goal = date;

                                        }
                                        if ((check.equals("*10") || check.equals("+5") || check.equals("+1")) && name.equals("finish")) {
                                            finish = date;
                                        }
                                        if (check.equals("-5") && name.equals("use")) {
                                            use = date;
                                        }
                                        if (!name.equals("pro")) {
                                            kkes.add(kone);
                                        }

                                        //jqj-end
                                    }
                                }
                            }
                            if (isCacu == 1) {
                                //jqj
                                JSONObject kone = new JSONObject();
                                kone.put("isUse", 1);
                                kone.put("name", "pro");
                                kone.put("name_cn", "得分");
                                kone.put("type", 1);
                                double po = 0;
                                logger.warn(col + "->" + finish + "," + goal + "," + use + "--" + check);
                                if (check.equals("*10") && finish > 0 && goal > 0) {

                                    po = (double) finish / goal * 10;

                                    logger.warn(String.valueOf(po));
                                    if (po > 10) {
                                        po = 10;
                                    }

                                    total = total + (int) po;
                                }
                                if (check.equals("+5") && finish > 0) {

                                    po = (double) finish * 5;
                                    logger.warn(String.valueOf(po));
                                    total = total + (int) po;
                                }
                                if (check.equals("+1") && finish > 0) {

                                    po = (double) finish * 1;
                                    logger.warn(String.valueOf(po));
                                    total = total + (int) po;
                                }
                                if (check.equals("-5") && use > 0) {

                                    po = -(double) use * 5;
                                    logger.warn(String.valueOf(po));
                                    total = total + (int) po;
                                }
                                kone.put("data", (int) po);
                                kkes.add(kone);
                                logger.warn(kkes.toString());
                                done.put("keys", kkes);

                                dets.add(done);
                            }

                        }
                        if (isCacu == 1) {
                            sqls = "update community_check set detail='" + dets + "', total='" + total + "' where " + "id='" + id + "'";
                            mysql.update(sqls);//jqj-end
                        }
                    }
                }

            } else {
                sqls = "insert into community_check (month,community,unit) values ('" + month + "','all','" + unit +
                        "');";
                mysql.update(sqls);
            }

            sqls = "select * from comm_check_model where isdelete=1 and unit='" + unit + "'";
            List<JSONObject> models = mysql.query(sqls);
            JSONArray dets = new JSONArray();
            for (int m = 0; m < models.size(); m++) {
                JSONObject mone = models.get(m);
                String col = mone.getString("col_name");
                JSONArray heads = mone.getJSONArray("heads");
                JSONArray keys = new JSONArray();
                for (int k = 0; k < heads.size(); k++) {
                    JSONObject kone = heads.getJSONObject(k);
                    String name = kone.getString("name");
                    if (cals.containsKey(col + "_" + name)) {
                        int d = cals.get(col + "_" + name);
                        kone.put("data", d);

                    } else {
                        kone.put("data", 0);
                    }
                    keys.add(kone);
                }
                mone.put("keys", keys);
                mone.remove("heads");
                dets.add(mone);
            }

            sqls = "update community_check set detail='" + dets + "' where community='all' and month='" + month + "' "
                    + "and unit='" + unit + "'";
            mysql.update(sqls);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        }
    }

    private static HashMap<String, String> GetChecks(InfoModelHelper mysql, String unit) {
        HashMap<String, String> back = new HashMap<>();
        int mark = 0;
        try {
            String sql = "select col_name,`check` from comm_check_model where isdelete=1 and unit='" + unit + "'";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                mark = list.size();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    back.put(one.getString("col_name"), one.getString("check"));
                    if (one.getString("check").length() > 0) {
                        mark--;
                    }
                }
            }
            if (mark == list.size()) {
                back = new HashMap<>();
            }
            logger.warn(back.toString());
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
        }
        return back;
    }


    //******GET*******
    private static JSONObject getCommCheck(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String police = "";
            String month = "";
            String community = "";

            String opt_user = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("police") && data.getString("police").length() > 0) {
                police = data.getString("police");
                sql = sql + " police='" + police + "' and ";
            }
            if (data.containsKey("month") && data.getString("month").length() > 0) {
                month = data.getString("month");
                sql = sql + " month='" + month + "' and ";
            }
            if (data.containsKey("community") && data.getString("community").length() > 0) {
                community = data.getString("community");
                sql = sql + " community='" + community + "' and ";
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");

            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select * from community_check where 1=1 and " + sql + " isdelete=1 order by total desc  " +
                            "limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select id from community_check where 1=1 and " + sql + " isdelete=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(452005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject getCommCheck1(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String police = "";
            String month = "";
            String community = "";

            String opt_user = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("police") && data.getString("police").length() > 0) {
                police = data.getString("police");
                sql = sql + " police='" + police + "' and ";
            }
            if (data.containsKey("month") && data.getString("month").length() > 0) {
                month = data.getString("month");
                sql = sql + " month='" + month + "' and ";
            }
            if (data.containsKey("community") && data.getString("community").length() > 0) {
                community = data.getString("community");
                sql = sql + " community='" + community + "' and ";
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls = "select id,police,community,detail,rank,month from community_check where 1=1 and " + sql +
                    " " + "isdelete=1 order by total desc  limit " + limit + " offset " + limit * (page - 1);
            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo1(list, mysql));
                sqls = "select id from community_check where 1=1 and " + sql + " isdelete=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(452005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private static Object RelaInfo1(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String police = "";
            if (one.containsKey("police")) {
                police = one.getString("police");
            }
            if (police != null || police.length() > 0) {
                HashMap<String, String> pos = RIUtil.StringToList(police);
                one.put("police", RIUtil.users.get(pos));
            }
            String community = one.getString("community");
            one.put("community_name", RIUtil.RealDictNames(RIUtil.StringToList(community)));
            if (one.containsKey("create_user")) {
                String create_user = one.getString("create_user");
                one.put("create_user", RIUtil.users.get(create_user));
            }
            if (one.getString("month").length() == 0 && one.getString("police").length() < 5) {
                police = one.getString("id");
                one.put("police", RIUtil.users.get(police));
            }
            back.add(one);
        }
        return back;
    }

    //*****relainfo************
    private static List<JSONObject> RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            //logger.warn(one.toString());
            String police = "";
            if (one.containsKey("police")) {
                police = one.getString("police");
                if (police != null || police.length() > 0) {

                    one.put("police", RIUtil.UseridToNames(RIUtil.StringToList(police)));
                }
            } else {
                one.put("police", new JSONArray());
            }
            String community = one.getString("community");
            try {
                one.put("community_name", RIUtil.dicts.get(community).getString("dict_name"));
            } catch (Exception ex) {
                one.put("community_name", "");
            }
            if (one.containsKey("create_user")) {
                String create_user = one.getString("create_user");
                one.put("create_user", RIUtil.users.get(create_user));
            }
            if (one.getString("month").length() == 0 && one.getString("police").length() < 5) {
                police = one.getString("id");
                one.put("police", RIUtil.UseridToNames(RIUtil.StringToList(police)));
            }
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private static JSONObject updateCommCheck(JSONObject req, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            JSONArray ll = req.getJSONArray("data");
            logger.warn(ll.toString());
            String opt_user = "";
            String month = "";
            String unit = "";
            if (ll.size() > 0) {
                for (int i = 0; i < ll.size(); i++) {
                    JSONObject data = ll.getJSONObject(i);
                    String sql = "";
                    String id = "";
                    String police = "";

                    String community = "";

                    String detail = "";
                    String rank = "";

                    int total = 0;
                    if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                        opt_user = data.getString("opt_user");
                        unit = RIUtil.users.get(opt_user).getString("unit");

                    } else {
                        return ErrNo.set(452004);
                    }
                    if (data.containsKey("id") && data.getString("id").length() > 0) {
                        id = data.getString("id");

                    } else {
                        String m = new SimpleDateFormat("yyMM").format(new Date());
                        String cT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                        String s =
                                "insert community_check(month,create_time,create_user,unit) values('" + m + "','" + cT +
                                        "'," + "'" + opt_user + "','" + unit + "')";
                        mysql.update(s);
                        s = "select id from community_check where month='" + m + "' and create_time='" + cT + "' and "
                                + "create_user='" + opt_user + "' and unit='" + unit + "'";
                        id = mysql.query_one(s, "id");
                    }
                    if (data.containsKey("police") && data.getString("police").length() > 0) {
                        police = data.getString("police");
                        sql = sql + " police='" + police + "' , ";
                    }
                    if (data.containsKey("month") && data.getString("month").length() > 0) {
                        month = data.getString("month");
                        sql = sql + " month='" + month + "' , ";
                    }
                    if (data.containsKey("community") && data.getString("community").length() > 0) {
                        community = data.getString("community");
                        sql = sql + " community='" + community + "' , ";
                    }

                    if (data.containsKey("rank") && data.getString("rank").length() > 0) {
                        rank = data.getString("rank");
                        sql = sql + " rank='" + rank + "' , ";
                    }
                    String isdelete = "";
                    if (data.containsKey("is_delete") && data.getString("is_delete").length() > 0) {
                        isdelete = data.getString("is_delete");
                        sql = sql + " isdelete='" + isdelete + "' , ";
                    }
                    if (data.containsKey("detail") && data.getString("detail").length() > 0) {
                        detail = data.getString("detail");
                        sql = sql + " detail='" + detail + "' , ";
                    }
                    sql = sql + " total='" + total + "' , ";


                    String sqls = "update community_check set " + sql + " id='" + id + "' where id='" + id + "'";
                    mysql.update(sqls);
                }
            }
            logger.warn("ckckck");
            CheckALL(mysql, month, unit);

            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新社区考勤", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(452003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static String ChangeFomate(String detail, InfoModelHelper mysql) throws Exception {
        logger.warn(detail);
        HashMap<String, String> cals = new HashMap<>();


        JSONArray values = JSONArray.parseArray(detail);
        for (int i = 0; i < values.size(); i++) {
            JSONObject kone = values.getJSONObject(i);
            String key = kone.getString("keys");
            String data = kone.getString("data");

            cals.put(key, data);

        }
        logger.warn(cals.toString());
        String sqls = "select * from comm_check_model where isdelete=1";
        List<JSONObject> models = mysql.query(sqls);
        JSONArray dets = new JSONArray();
        for (int m = 0; m < models.size(); m++) {
            JSONObject mone = models.get(m);
            String col = mone.getString("col_name");
            JSONArray heads = mone.getJSONArray("heads");
            JSONArray keys = new JSONArray();
            for (int k = 0; k < heads.size(); k++) {
                JSONObject kone = heads.getJSONObject(k);
                String name = kone.getString("name");
                if (cals.containsKey(col + "_" + name)) {
                    String d = cals.get(col + "_" + name);

                    kone.put("data", d);

                } else {
                    kone.put("data", "");
                }
                keys.add(kone);
            }
            mone.put("keys", keys);
            mone.remove("heads");
            dets.add(mone);
        }
        logger.warn(dets.toString());
        return dets.toString();


    }

    //*********delete**********
    private static JSONObject deleteCommCheck(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(452008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(452008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update community_check set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除社区考勤", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(452007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

    public static JSONObject getCommCheckModel(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String unit = "";

        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");

        }

        try {
            mysql = InfoModelPool.getModel();
            String sql = "select * from comm_check_model where isdelete=1 and unit='" + unit + "' order by index_no";
            List<JSONObject> list = mysql.query(sql);
            back.put("data", list);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(452001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;

    }

}

