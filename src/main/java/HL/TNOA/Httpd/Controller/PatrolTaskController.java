package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.wechat.wechatMsgTemp;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public class PatrolTaskController {
    private static Logger logger = LoggerFactory.getLogger(PatrolTaskController.class);

    public static JSONObject updateTaskStatus(JSONObject data, String clientIP) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String id = "";
        String status = "1";
        String opt_user = "";

        try {
            mysql = InfoModelPool.getModel();
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(460004);
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");

            } else {
                return ErrNo.set(460004);
            }
            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getString("status");

            }
            String sql = "select accepter,checked from patrol_task where id='" + id + "'";
            List<JSONObject> list = mysql.query(sql);
            JSONObject one = list.get(0);
            String accepter = one.getString("accepter");
            String checked = one.getString("checked");
            if (checked == null) {
                checked = "";
            }
            String finished = one.getString("finished");
            if (finished == null) {
                finished = "";
            }
            if (status.equals("1")) {
                if (accepter.contains(opt_user)) {
                    HashMap<String, String> accs = RIUtil.StringToList(accepter);
                    accs.remove(opt_user);

                    HashMap<String, String> finishs = RIUtil.StringToList(finished);
                    finishs.put(opt_user, "");

                    accepter = String.valueOf(RIUtil.HashToList(accs));
                    finished = String.valueOf(RIUtil.HashToList(finishs));
                }
                if (checked.contains(opt_user)) {
                    HashMap<String, String> checks = RIUtil.StringToList(checked);
                    checks.remove(opt_user);
                    HashMap<String, String> finishs = RIUtil.StringToList(finished);
                    finishs.put(opt_user, "");

                    checked = String.valueOf(RIUtil.HashToList(checks));
                    finished = String.valueOf(RIUtil.HashToList(finishs));
                }
            }
            sql = "update patrol_task " +
                    "set accepter='" + accepter + "',checked='" + checked + "',finished='" + finished + "',status='" + status + "' where id='" + id + "'";
            logger.warn(sql);
            mysql.update(sql);
            String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            sql = "INSERT INTO task_log (task_id, task_type, time, opt_user,cancel_type,user_id) " + "VALUES ( '" + id + "', '" + status + "', '" + time + "', '" + opt_user + "','99','" + opt_user + "');";
            mysql.update(sql);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(460003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******CREATE*******
    public static JSONObject createPatrolTask(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String accepter = "";
            String start_time = "";
            String end_time = "";
            String near_time = "";
            String address = "";
            String isWechat = "1";
            String type = "0";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "1";
            if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
                accepter = data.getString("accepter");
            } else {
                logger.warn("accepter");
                return ErrNo.set(460002);
            }

            String unit = "";

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");

            }
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time").replace("|", " ");
                if (start_time.length() == 16) {
                    start_time = start_time + ":00";
                }
            } else {
                logger.warn("start");
                return ErrNo.set(460002);
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time").replace("|", " ");
                if (end_time.length() == 16) {
                    end_time = end_time + ":00";
                }
            } else {
                logger.warn("end");
                return ErrNo.set(460002);
            }
            if (data.containsKey("near_time") && data.getString("near_time").length() > 0) {
                near_time = data.getString("near_time").replace("|", " ");
                if (near_time.length() == 16) {
                    near_time = near_time + ":00";
                }
            } else {
                long start = RIUtil.dateToStamp(start_time);
                long end = RIUtil.dateToStamp(end_time);
                long bt = (end - start) / 10;
                long nt = start + bt;
                near_time = RIUtil.stampToTime(nt);
            }
            String address_name = "";
            if (data.containsKey("address") && data.getString("address").length() > 0) {
                address = data.getString("address");
                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(address));

                for (int i = 0; i < addss.size(); i++) {
                    address_name =
                            address_name + RIUtil.IdToName(addss.get(i), mysql, "patrol_name", "patrol_info") + " ";
                }

            } else {
                logger.warn("addres");
                return ErrNo.set(460002);
            }
            if (data.containsKey("isWechat") && data.getString("isWechat").length() > 0) {
                isWechat = data.getString("isWechat");
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                logger.warn("opt_user");
                return ErrNo.set(460002);
            }

            String sqls = "insert patrol_task (accepter,start_time,end_time,near_time,address," + "isWechat," +
                    "create_user,create_time,isdelete,type,unit)" + "values('" + accepter + "','" + start_time + "'," +
                    "'" + end_time + "','" + near_time + "','" + address + "'," + "'" + isWechat + "','" + create_user + "','" + create_time + "','" + isdelete + "','" + type + "','" + unit + "')";
            mysql.update(sqls);
            if (isWechat.equals("1")) {
                sqls =
                        "select id from patrol_task where create_time='" + create_time + "' and create_user='" + create_user + "' and address='" + address + "'";
                String task_id = mysql.query_one(sqls, "id");
                String title = start_time.substring(5, 10) + address;
                if (title.length() > 16) {
                    title = title.substring(0, 15) + "...";
                }
                JSONObject one = new JSONObject();
                one.put("id", task_id);
                one.put("accepter", accepter);
                one.put("title", title);
                one.put("start_time", start_time);
                one.put("end_time", end_time);


                wechatMsgTemp.createMessage_task(one, mysql, 1, "6");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建巡查任务", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(460001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    //******GET*******
    public static JSONObject getPatrolTask(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = -1;
            int page = -1;
            String id = "";
            String accepter = "";

            String address = "";
            String start_time = "";
            String end_time = "";
            String type = "";
            String status = "";
            String unit = "";

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " unit='" + unit + "' and ";
            }
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
                accepter = data.getString("accepter");
                sql = sql + " (accepter like '%" + accepter + "%' or checked like '%" + accepter + "%' or finished " +
                        "like '%" + accepter + "%') and ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type ='" + type + "' and ";
            }
            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getString("status");
                sql = sql + " status ='" + status + "' and ";
            }
            String create_user = "";
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + "create_user='" + create_user + "' and";
            }

            if (data.containsKey("address") && data.getString("address").length() > 0) {
                address = data.getString("address");
                String ass[] = address.split(",");
                String s = "(";
                for (int i = 0; i < ass.length; i++) {
                    s = s + " address like '%" + ass[i] + "%' or ";
                }
                s = s.substring(0, s.length() - 3) + ") and";
                sql = sql + s;
            }
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time").replace("|", " ");
                if (start_time.length() == 16) {
                    start_time = start_time + ":00";
                }
                sql = sql + " start_time >='" + start_time + "' and ";
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time").replace("|", " ");
                if (end_time.length() == 16) {
                    end_time = end_time + ":00";
                }
                sql = sql + " start_time <='" + end_time + "' and ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String limoff = "";
            if (limit > 0 && page >= 0) {
                limoff = "limit " + limit + " offset " + limit * (page - 1);
            }
            String sqls = "select * from patrol_task " +
                    "where 1=1 and " + sql + " isdelete=1  " +
                    "order by create_time desc " + limoff;
            List<JSONObject> list = new ArrayList<>();
            logger.warn(sqls);
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select id from patrol_task where 1=1 and " + sql + " isdelete=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(460005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    public static List<JSONObject> RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);

            String accepter = one.getString("accepter");
            JSONArray accepters = RIUtil.UseridToNames(RIUtil.StringToList(accepter));
            one.put("accepter", accepters);
            String checked = one.getString("checked");
            JSONArray checkeds = RIUtil.UseridToNames(RIUtil.StringToList(checked));
            one.put("checked", checkeds);
            String finished = one.getString("finished");

            JSONArray finisheds = RIUtil.UseridToNames(RIUtil.StringToList(finished));
            one.put("finished", finisheds);
            String create_user = one.getString("create_user");
            one.put("create_user", RIUtil.users.get(create_user));
            String address = one.getString("address");
            String ads[] = address.split(",");
            JSONArray addresses = new JSONArray();
            for (int a = 0; a < ads.length; a++) {
                JSONObject adone = new JSONObject();
                String sql = "select * from patrol_info where id='" + ads[a] + "'";
                adone.put("address_id", ads[a]);
                List<JSONObject> addlist = mysql.query(sql);
                if (addlist.size() > 0) {
                    adone.put("address", addlist.get(0));
                } else {
                    adone.put("address", new JSONObject());
                }
                addresses.add(adone);
            }
            one.put("address", addresses);

            //签收时间
            String sql = "select user_id,time, name " + "from task_log a " +
                    "left join user b on a.user_id=b.id" + " where task_id='" + one.getString("id") + "' and " +
                    "cancel_type='99' and `task_type`=1 order by time  ";
            //logger.warn(sql);
            one.put("check_time", mysql.query(sql));

            //扫码记录
            sql = "select * from patrol_log " + "where rela_id='" + one.getString("id") + "' order by time ";
            List<JSONObject> plist = mysql.query(sql);
            List<JSONObject> plogs = new ArrayList<>();
            if (plist.size() > 0) {
                for (int p = 0; p < plist.size(); p++) {
                    JSONObject pone = plist.get(p);
                    String user_id = pone.getString("user_id");
                    pone.put("user", RIUtil.users.get(user_id));
                    String patrol_id = pone.getString("patrol_id");
                    sql = "select * from patrol_info where id='" + patrol_id + "'";
                    List<JSONObject> pp = mysql.query(sql);
                    if (pp.size() > 0) {
                        pone.put("patrol_info", pp.get(0));
                    } else {
                        pone.put("patrol_info", new JSONObject());
                    }
                    plogs.add(pone);
                }
            }

            one.put("patrol_log", plogs);
            if (one.containsKey("unit")) {
                String unit = one.getString("unit");
                one.put("unit_name", RIUtil.RealDictNames(RIUtil.StringToList(unit)));
            } else {
                one.put("unit_name", "");
            }
            back.add(one);
        }
        return back;
    }

  /*  //******UPDATE*******
    private static JSONObject updatePatrolTask(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String accepter = "";
            String start_time = "";
            String end_time = "";
            String near_time = "";
            String address = "";
            String isWechat = "";
            String checked = "";
            String finished = "";
            String create_user = "";
            String create_time = "";
            String opt_user = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' , ";
            }
            if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
                accepter = data.getString("accepter");
                sql = sql + " accepter='" + accepter + "' , ";
            }
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time");
                sql = sql + " start_time='" + start_time + "' , ";
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time");
                sql = sql + " end_time='" + end_time + "' , ";
            }
            if (data.containsKey("near_time") && data.getString("near_time").length() > 0) {
                near_time = data.getString("near_time");
                sql = sql + " near_time='" + near_time + "' , ";
            }
            if (data.containsKey("address") && data.getString("address").length() > 0) {
                address = data.getString("address");
                sql = sql + " address='" + address + "' , ";
            }
            if (data.containsKey("isWechat") && data.getString("isWechat").length() > 0) {
                isWechat = data.getString("isWechat");
                sql = sql + " isWechat='" + isWechat + "' , ";
            }
            if (data.containsKey("checked") && data.getString("checked").length() > 0) {
                checked = data.getString("checked");
                sql = sql + " checked='" + checked + "' , ";
            }
            if (data.containsKey("finished") && data.getString("finished").length() > 0) {
                finished = data.getString("finished");
                sql = sql + " finished='" + finished + "' , ";
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' , ";
            }
            if (data.containsKey("create_time") && data.getString("create_time").length() > 0) {
                create_time = data.getString("create_time");
                sql = sql + " create_time='" + create_time + "' , ";
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            String sqls = "update patrol_task set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新巡查任务", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(460003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }*/

    //*********delete**********
    public static JSONObject deletePatrolTask(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(460008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(460008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update patrol_task set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除巡查任务", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(460007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

}
