package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.Export.ExportInterface;
import HL.TNOA.Lib.Export.ExportXlsxHelper;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class QQBTaskController {
    private static Logger logger = LoggerFactory.getLogger(QQBTaskController.class);
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/qqb_task"})
    // @PassToken
    public JSONObject get_qqb_task(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();


        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_my_task_gk")) {
                return GetMyTaskGKList(data);
            } else if (opt.equals("get_my_level")) {
                return GetMyLevel(data);
            } else if (opt.equals("get_zf_level")) {
                return GetLevelTask(data);
            } else if (opt.equals("get_task_list")) {
                return GetTaskList(data);
            } else if (opt.equals("create_label")) {

                return createDict(data);
            } else if (opt.equals("update_label")) {

                return updateDict(data);
            } else if (opt.equals("delete_label")) {

                return deleteDict(data);
            } else if (opt.equals("get_label_tree")) {
                return getDict(data);
            } else if (opt.equals("update_label_index")) {
                return UpdateLabelTree(data);
            } else if (opt.equals("get_type_tree")) {
                return GetTypeTree(data);
            } else if (opt.equals("update_follow")) {
                return UpdateFollow(data);
            } else if (opt.equals("get_follow")) {
                return GetFollow(data);
            } else if (opt.equals("get_follow_list")) {
                return GetFollowList(data);
            } else if (opt.equals("get_qqb_user")) {
                return GetQQBUser(data);
            } else if (opt.equals("get_task_zf_gk")) {
                return GetMyTaskGKListZF(data);
            } else {
                return ErrNo.set(501003);
            }
        } else {
            return ErrNo.set(501003);
        }

    }

    private JSONObject GetQQBUser(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper my_qqb_user = null;

        String unit = "";
        String name = "";
        String nSql = "";
        String limit = "";
        try {
            my_qqb_user = new MysqlHelper("mysql_qqb_user");
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(501001);
            }
            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");
                nSql = " and CHINESE_NAME LIKE '%" + name + "%'";
            }


            JSONObject done = RIUtil.dicts.get(unit);
            int type = done.getInteger("type");
            int t = 0;
            if (type == 21 || type == 22 || type == 27) {
                t = 1;
                unit = "320400";


            } else if (type == 23 || type == 24 || type == 28) {
                t = 2;
                unit = unit.substring(0, 6);
            } else if (type == 25) {
                t = 3;
            } else {
                t = 13;
                unit = unit.substring(0, 8);
            }
            String sql = "select ID,CHINESE_NAME,IDCARD_NO,DUTY from SYS_AUTH_USER where DELETED=0 and  " +
                    "(IS_TEMPORARY  is null or IS_TEMPORARY <>1) and DEPT_CODE  like '%" + unit + "%' and  type!=-1  "
                    + nSql + "  " + "ORDER BY " + "TYPE," + "POLICE_CODE " + limit;
            logger.warn(sql);
            List<JSONObject> list = my_qqb_user.query(sql);
            if (list.size() > 0) {
                back.put("data", list);
            } else {
                back.put("data", new JSONArray());
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            my_qqb_user.close();
        }
    }

    private JSONObject GetFollowList(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;


        String opt_user = "";
        String unit = "";

        try {
            ora_hl = new OracleHelper("ora_hl");
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(501001);
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");


            } else {
                return ErrNo.set(501001);
            }


            String units = "";
            JSONObject dets = RIUtil.dicts.get(unit);
            int t = dets.getInteger("type");
            if (t == 23 || t == 24 || t == 28) {
                unit = unit.substring(0, 6) + "000000";

            } else if (t == 25 || t == 26) {

                unit = unit.substring(0, 8) + "0000";

            } else {

                unit = "************";
            }


            String sql = "select TASK_ID from QQB_TASK_FOLLOW where unit  in ('" + unit + "')";
            List<JSONObject> list = ora_hl.query(sql);
            String ids = "";

            for (int i = 0; i < list.size(); i++) {
                ids = ids + list.get(i).getString("TASK_ID") + ",";
            }
            back.put("data", ids);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    static JSONObject GetFollow(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;


        String opt_user = "";

        String opt_type = "";
        String task_id;
        int limit = 10;
        int page = 1;
        String unit = "";
        String type = "";
        try {
            ora_hl = new OracleHelper("ora_hl");
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(501001);
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");


            } else {
                return ErrNo.set(501001);
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            String units = "";
            JSONObject dets = RIUtil.dicts.get(unit);
            int t = dets.getInteger("type");
            if (t == 23 || t == 24 || t == 28) {
                unit = unit.substring(0, 6) + "000000";

                type = "2";

                units = "************','" + unit;
            } else if (t == 25 || t == 26) {

                unit = unit.substring(0, 8) + "0000";
                type = "3";

                units = "************','" + unit.substring(0, 6) + "000000','" + unit;
            } else {


                units = "************";
                type = "1";
            }


            String sql =
                    "select * from QQB_TASK_FOLLOW where unit in ('" + units + "') order by type OFFSET" + " " + (page - 1) * limit + " " + "ROWS FETCH NEXT" + " " + limit + " " + "ROWS " + "ONLY";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            sql = "select count(1) as count from QQB_TASK_FOLLOW where unit in('" + units + "')";
            int count = ora_hl.query_count(sql);
            if (list.size() > 0) {

                back.put("data", RealFCount(list, ora_hl, unit, type));


            } else {
                back.put("data", new ArrayList<>());


            }
            back.put("count", count);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private static List<JSONObject> RealFCount(List<JSONObject> list, OracleHelper ora_hl, String unit, String type) {
        String sql = "";
        JSONObject done = RIUtil.dicts.get(unit);
        int t = done.getInteger("type");

        if (t == 21 || t == 22 || t == 27) {
            t = 1;
            unit = "************";
        } else if (t == 23 || t == 24 || t == 28) {
            sql = sql + " INSTR(FJ,'" + unit.substring(0, 6) + "')>0 and ";
            t = 2;
            unit = unit.substring(0, 6) + "000000";
        } else if (t == 25) {
            sql = sql + " INSTR(PCS,'" + unit.substring(0, 8) + "')>0 and ";
            t = 3;
            unit = unit.substring(0, 8) + "0000";
        } else {
            unit = unit.substring(0, 8) + "0000";
            sql = sql + " INSTR(ZRQ,'" + unit + "')>0 and ";
            t = 3;
        }
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String taskId = one.getString("TASK_ID").replace("|", "','");
            MysqlHelper mysql = null;
            try {
                mysql = new MysqlHelper("mysql");
                String sqls = "select remark from dict where id='" + taskId + "'";
                sqls = "select count,permission,remark from qqb_task_label_count a left join dict b on a.dict_id=b" + ".id" + "  " + "where a.unit='" + unit + "' and  dict_id='" + taskId + "'";
                logger.warn(sqls);
                List<JSONObject> dets = mysql.query(sqls);
                JSONObject det = dets.get(0);
                int count = det.getInteger("count");
                String per = det.getString("permission");
                JSONObject remark = det.getJSONObject("remark");
                if (per.equals(unit)) {
                    one.put("fix", 1);
                } else {
                    one.put("fix", 0);
                }
                one.put("remark", remark);
                one.put("count", count);
                back.add(one);
            } catch (Exception ex) {

            } finally {
                mysql.close();
            }

        }

        return back;


    }

    private JSONObject UpdateFollow(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        OracleHelper ora_hl = null;
        MysqlHelper mysql = null;


        String opt_user = "";

        String opt_type = "";
        String task_id;
        String unit = "";
        String type = "";
        try {
            ora_hl = new OracleHelper("ora_hl");
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(501001);
            }
            if (data.containsKey("opt_type") && data.getString("opt_type").length() > 0) {
                opt_type = data.getString("opt_type");
            } else {
                return ErrNo.set(501001);
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject dets = RIUtil.dicts.get(unit);
                int t = dets.getInteger("type");
                if (t == 23 || t == 24 || t == 28) {
                    unit = unit.substring(0, 6) + "000000";
                    type = "2";
                } else if (t == 25 || t == 26) {
                    unit = unit.substring(0, 8) + "0000";
                    type = "3";
                } else {
                    unit = "************";
                    type = "1";
                }
            } else {
                return ErrNo.set(501001);
            }

            if (data.containsKey("task_id") && data.getString("task_id").length() > 0) {
                task_id = data.getString("task_id");


            } else {
                return ErrNo.set(501001);
            }
            String sql = "";
            String tasks[] = task_id.split(",");
            for (int i = 0; i < tasks.length; i++) {
                task_id = tasks[i];
                if (opt_type.equals("add")) {

                    String taskName = "";
                    try {
                        mysql = new MysqlHelper("mysql");
                        sql = "select dict_name,father_id from dict where id='" + task_id + "'";
                        logger.warn(sql);
                        taskName = mysql.query_one(sql, "dict_name");
                        String fid = mysql.query_one(sql, "father_id");
                        sql = "select dict_name,father_id from dict where id='" + fid + "'";
                        logger.warn(sql);
                        taskName = mysql.query_one(sql, "dict_name") + "-" + taskName;
                        fid = mysql.query_one(sql, "father_id");
                        sql = "select dict_name,father_id from dict where id='" + fid + "'";
                        logger.warn(sql);
                        taskName = "【" + mysql.query_one(sql, "dict_name") + "】" + taskName;

                    } catch (Exception ex) {

                    } finally {
                        mysql.close();
                    }


                    sql = "delete from QQB_TASK_FOLLOW where OPT_USER='" + opt_user + "' AND TASK_ID='" + task_id + "'";
                    ora_hl.update(sql);

                    sql = "insert into QQB_TASK_FOLLOW(OPT_USER,TASK_ID,TASK_NAME,IMPORTANT_TYPE,UNIT,TYPE) " +
                            "VALUES ('" + opt_user + "','" + task_id + "','" + taskName + "','0', " + "'" + unit +
                            "'," + "'" + type + "')";
                    System.out.println(sql);

                    ora_hl.update(sql);
                } else {
                    sql = "delete from QQB_TASK_FOLLOW where UNIT='" + unit + "' AND TASK_ID='" + task_id + "'";
                    System.out.println(sql);
                    ora_hl.update(sql);
                }
            }

            return ErrNo.set(0);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private JSONObject GetTypeTree(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        MysqlHelper my_qqb_rwpt = null;
        HashMap<String, JSONObject> folders = new HashMap<>();
        HashMap<String, String> tasks = new HashMap<>();

        try {
            my_qqb_rwpt = new MysqlHelper("mysql_qqb_rwpt");

            String sql =
                    "select id,name,'6' as Link from FILE_FOLDER_TASK_LINK where DELETED=0 and (name not like " +
                            "'%测试%' AND name not like '%test%') union " + "select id,name,'1' as Link from " +
                            "FILE_FOLDER_TASK where DELETED=0 and (name not like '%测试%' AND" + " name not like " +
                            "'%test%') ";

            List<JSONObject> list = my_qqb_rwpt.query(sql);
            //    System.out.println(list);

            sql = "select a.id ,a.name,a.type ,a.FILE_FOLDER_TASK_ID from TASK a ,TASK_DETAIL b where a.ID=b.TASK_ID "
                    + "and " + "a.DELETED=0 and b.DELETED=0 and (a.NAME not like " + "'%测试%' AND " + " a.NAME not " + "like '%test%') AND LENGTH(FILE_FOLDER_TASK_ID)>0";

            List<JSONObject> tlist = my_qqb_rwpt.query(sql);

            for (int i = 0; i < tlist.size(); i++) {
                JSONObject one = tlist.get(i);
                String id = one.getString("ID");
                String name =
                        one.getString("NAME") + "," + one.getString("FILE_FOLDER_TASK_ID") + "_" + one.getString(
                                "TYPE");

                String ids = "";
                if (tasks.containsKey(name)) {
                    ids = tasks.get(name);
                }
                if (!ids.contains(id)) {

                    ids = ids + id + "|";

                    tasks.put(name, ids);
                }

            }

            //     System.out.println(tasks);

            JSONArray datas = new JSONArray();

            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                JSONObject done = new JSONObject();
                String id = one.getString("id") + "_" + one.getString("Link");

                JSONArray dets = new JSONArray();

                for (Map.Entry<String, String> det : tasks.entrySet()) {

                    JSONObject d = new JSONObject();
                    String name = det.getKey();

                    String ids = det.getValue();

                    if (ids.length() > 1) {
                        ids = ids.substring(0, ids.length() - 1);
                    }
                    if (name.contains(id)) {
                        // d.put(ids, name);
                        d.put("id", ids);
                        name = name.split(",")[0];
                        d.put("name", name);
                        dets.add(d);

                    }
                }
                done.put("id", id);
                done.put("name", one.getString("name"));


                done.put("dets", dets);

                datas.add(done);
            }

            //  System.out.println(datas);
            back.put("data", datas);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            my_qqb_rwpt.close();
        }
    }


    private JSONObject GetTaskList(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String user_id = "";
        String unit = "";

        String type = "170-unFin";

        String del_time_start = "";
        String del_time_end = "";
        String end_time_start = "";
        String end_time_end = "";
        String task_type = "";
        String fin_time_start = "";
        String fin_time_end = "";
        String status = "";
        String isFix = "";

        String d_day = "";
        String level = "";
        String task_name = "";
        String obj_name = "";
        String exeid = "";

        String order = " finish_time desc";

        OracleHelper ora_hl = null;
        String sql = "";

        int page = 1;
        int limit = 10;
        int isExp = 0;
        int model = 0;
        String qqb_id = "";
        String tableName = "QQB_TASK_OBJECT3";


        try {
            ora_hl = new OracleHelper("ora_hl");
            if (data.containsKey("qqb_id") && data.getString("qqb_id").length() > 0) {
                user_id = data.getString("qqb_id");

            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject done = RIUtil.dicts.get(unit);
                int t = done.getInteger("type");
                if (t == 21 || t == 22 || t == 27) {

                } else if (t == 23 || t == 24 || t == 28) {
                    sql = sql + " INSTR(FJ,'" + unit.substring(0, 6) + "')>0 and ";
                } else if (t == 25) {
                    sql = sql + " INSTR(PCS,'" + unit.substring(0, 8) + "')>0 and ";
                } else {
                    sql = sql + " INSTR(ZRQ,'" + unit + "')>0 and ";
                }
            } else {
                return ErrNo.set(501001);
            }
            String colSql = "";
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");

                if (type.contains("fin")) {
                    data.put("status", 1);
                }
                if (type.contains("unFin")) {

                    data.put("status", 2);
                }
            }


            logger.warn(data.toString());
            if (data.containsKey("EXECUTORIDS") && data.getString("EXECUTORIDS").length() > 0) {
                exeid = data.getString("EXECUTORIDS");

                sql = sql + " INSTR(EXECUTORIDS,'" + exeid + "')>0 and  ";

            }

            if (data.containsKey("del_time_start") && data.getString("del_time_start").length() > 0) {
                del_time_start = data.getString("del_time_start") + " 00:00:00";
                sql = sql + " DELIVERY_TIME>='" + del_time_start + "' and ";
            }
            if (data.containsKey("del_time_end") && data.getString("del_time_end").length() > 0) {
                del_time_end = data.getString("del_time_end") + " 23:59:59";
                sql = sql + " DELIVERY_TIME<='" + del_time_end + "' and ";
            }
            if (data.containsKey("end_time_start") && data.getString("end_time_start").length() > 0) {
                end_time_start = data.getString("end_time_start") + " 00:00:00";
                sql = sql + " WHEN_FINISH>='" + end_time_start + "' and ";
            }
            if (data.containsKey("end_time_end") && data.getString("end_time_end").length() > 0) {
                end_time_end = data.getString("end_time_end") + " 23:59:59";
                sql = sql + " WHEN_FINISH<='" + end_time_end + "' and ";
            }

            if (data.containsKey("fin_time_start") && data.getString("fin_time_start").length() > 0) {
                fin_time_start = data.getString("fin_time_start") + " 00:00:00";
                sql = sql + " finish_time>='" + fin_time_start + "' and ";
            }
            if (data.containsKey("fin_time_end") && data.getString("fin_time_end").length() > 0) {
                fin_time_end = data.getString("fin_time_end") + " 23:59:59";
                sql = sql + " finish_time<='" + fin_time_end + "' and ";
            }
            if (data.containsKey("task_type") && data.getString("task_type").length() > 0) {
                task_type = data.getString("task_type").replace("|", ",");

                String[] types = task_type.split(",");
                String s = "";
                for (int i = 0; i < types.length; i++) {
                    s = s + "task_id='" + types[i] + "' or ";
                }

                if (s.length() > 3) {
                    s = s.substring(0, s.length() - 3);
                }
                sql = sql + " (" + s + ") and ";
            }
            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getString("status");
                if (status.equals("1")) {
                    sql = sql + " (status =1 or status=3) and ";
                } else if (status.equals("2")) {
                    sql = sql + " (status =2 or status=4) and ";
                } else {
                    sql = sql + " status='" + status + "' and ";
                }
            }
            if (data.containsKey("order") && data.getString("order").length() > 0) {
                order = data.getString("order");

            }
            if (data.containsKey("isFix") && data.getString("isFix").length() > 0) {
                isFix = data.getString("isFix");
                if (isFix.equals("1")) {
                    sql = sql + " FIX=1 and ";
                } else {
                    sql = sql + " FIX=0 and ";
                }
            }

            if (data.containsKey("d_day") && data.getString("d_day").length() > 0) {
                d_day = data.getString("d_day");
                int dday = 0;
                try {
                    dday = Integer.parseInt(d_day);
                } catch (Exception e) {
                    return ErrNo.set(null, 2, Lib.getTrace(e));
                }
                String today = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

                sql = sql + " (when_finish<='" + RIUtil.GetNextDateTime(today, dday) + "' AND when_finish>='" + today + "')" + " and ";
            }
            if (data.containsKey("level") && data.getString("level").length() > 0) {
                level = data.getString("level");
                sql = sql + " important_type='" + level + "' and ";
            }
            if (data.containsKey("task_name") && data.getString("task_name").length() > 0) {
                task_name = data.getString("task_name");
                sql = sql + "INSTR( task_name,'" + task_name + "')>0 and ";
            }
            if (data.containsKey("obj_name") && data.getString("obj_name").length() > 0) {
                obj_name = data.getString("obj_name");
                sql = sql + "INSTR( BIZNAME,'" + obj_name + "')>0 and ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }
            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getInteger("isExp");
            }
            String opt_user = data.getString("opt_user");

            qqb_id = user_id;
            if (data.containsKey("model") && data.getString("model").length() > 0) {
                model = data.getInteger("model");
            }
            if (model == 1) {//柱状图

                JSONObject done = RIUtil.dicts.get(unit);
                String group = "";
                String groupSql = "";
                int t = done.getInteger("type");
                String u = "";
                if (t == 21 || t == 22 || t == 27) {
                    group = " FJ";
                    groupSql = "  ";
                    unit = "************";
                } else if (t == 23 || t == 24 || t == 28) {
                    group = " PCS";
                    u = unit.substring(0, 6);
                    unit = u + "000000";
                    groupSql = " and instr(FJ,'" + u + "')>0";
                } else if (t == 25 || t == 26) {
                    group = " ZRQ";
                    u = unit.substring(0, 8);
                    unit = u + "0000";
                    groupSql = " and instr(PCS,'" + u + "')>0";
                }

                String sqls = "select count(1) as count," + group + " as code from HL." + tableName + " WHERE " +
                        "DELETED=0 and " + colSql + " " + sql + "  " + "DELETED=0 group by " + group;


                logger.warn(sqls);

                List<JSONObject> list = ora_hl.query(sqls);
                back.put("data", GetRetList(list, group, unit));


            } else {
                String sqls =
                        "select * from HL." + tableName + " WHERE DELETED=0 and " + colSql + " " + sql + "  " +
                                "DELETED=0  OFFSET" + " " + (page - 1) * limit + " " + "ROWS " + "FETCH " + "NEXT" +
                                " " + limit + " " + "ROWS " + "ONLY";

                logger.warn(sqls);


                List<JSONObject> list = ora_hl.query(sqls);

                int count = 0;
                if (list.size() > 0) {
                    logger.warn(list.size() + "______>");
                    list = RealInfos(list, unit, qqb_id, ora_hl, order);

                    logger.warn(list.size() + "______>");
                    back.put("data", list);
                    sqls = "select * from HL." + tableName + " WHERE DELETED=0 and " + colSql + " " + sql + " " +
                            "DELETED=0";
                    count = ora_hl.query(sqls).size();
                    logger.warn(count + "______>");

                } else {
                    back.put("data", new JSONArray());
                }
                back.put("count", count);


                int file = -1;
                if (isExp == 1) {
                    file = GetFile(list);
                }
                back.put("file", file);

            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 501002, Lib.getTrace(ex));
        } finally {

            if (ora_hl != null) {
                ora_hl.close();
            }
        }


        return back;
    }

    private int GetFile(List<JSONObject> list) {

        JSONArray back = new JSONArray();
        for (int i = 0; i < list.size(); i++) {

            JSONObject one = list.get(i);

            back.add(one);

        }


        String FileName = "任务_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        header.add("TASK_NAME");
        headername.put("TASK_NAME", "任务名称");
        header.add("BIZNAME");
        headername.put("BIZNAME", "对象名称");
        header.add("DELIVERY_TIME");
        headername.put("DELIVERY_TIME", "下发时间");
        header.add("WHEN_FINISH");
        headername.put("WHEN_FINISH", "到期时间");
        header.add("FINISH_TIME");
        headername.put("FINISH_TIME", "完成时间");
        header.add("statusName");
        headername.put("statusName", "完成状态");
        header.add("unit");
        headername.put("unit", "责任单位");

        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
//            for (int i = 0; i < datas.size(); i++) {
//                JSONObject jsonObject = datas.getJSONObject(i);
//                JSONArray jsonArray = jsonObject.getJSONArray("real_all");
            exporthelper.write_data(back);
//            }

            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql = "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath +
                        "','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");

                logger.warn("id->" + id);

                try {
                    if (exporthelper != null) {
                        try {
                            exporthelper.close();
                        } catch (Exception ex) {

                        }

                    }
                } catch (Exception ex) {

                }

                String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;
                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
                logger.warn(obsFileName + "-->" + ret);
                logger.warn("id->" + id);
                return id;
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return 0;
        } finally {

            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }
        }
    }

    private List<JSONObject> RealInfos(List<JSONObject> list, String unit, String user_id, OracleHelper ora_hl,
                                       String order) {
        List<JSONObject> back = new ArrayList<>();

        MysqlHelper qqb_user = null;
        HashMap<String, String> unames = new HashMap<>();

        String isTower = "0";
        try {
            qqb_user = new MysqlHelper("mysql_qqb_user");

            //  管理员权限  派出所--》所长，教导员 记派出所以上都可以
            String sql = "select ID,CHINESE_NAME,TYPE from SYS_AUTH_USER where DELETED=0;";
            logger.warn(sql);
            List<JSONObject> users = qqb_user.query(sql);
            if (users.size() > 0) {
                for (int u = 0; u < users.size(); u++) {
                    JSONObject uone = users.get(u);
                    int type = uone.getInteger("TYPE");
                    unames.put(uone.getString("ID"), uone.getString("CHINESE_NAME"));
                    if (u == 0 || u == 10) {
                        logger.warn(uone.toString());
                    }

                    if (uone.getString("ID").equals(user_id) && type != 13) {
                        isTower = "1";
                    }
                }
            }

        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
        } finally {
            qqb_user.close();
        }


        logger.warn(String.valueOf(unames.size()));


        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            one.put("isTower", isTower);
            String exeids = one.getString("EXECUTORIDS");
            String task_id = one.getString("TASK_ID");
            try {
                qqb_user = new MysqlHelper("mysql_qqb_rwpt");
                String sql = "select id from rwpt.TASK_DETAIL where RECEIVE_OBJECT_ID ='" + user_id + "' and " +
                        "TASK_ID='" + task_id + "' and deleted=0";
                // logger.warn(sql);
                String detId = qqb_user.query_one(sql, "ID");
                if (exeids.contains(user_id)) {
                    one.put("isLink", 1);


                    if (user_id.length() > 0 && detId.length() > 0) {
                        one.put("isLink", user_id + "," + detId);
                    } else {
                        one.put("isLink", "");
                    }


                } else {
                    one.put("isLink", "");
                }
                one.put("ID", detId);
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
                one.put("isLink", "");

            } finally {
                qqb_user.close();
            }
            String accepters = "";
            if (!exeids.contains("xxx")) {
                //  logger.warn(exeids);
                String es[] = exeids.split("\\|");

                for (int a = 0; a < es.length; a++) {
                    if (unames.containsKey(es[a])) {//logger.warn(es[a]);
                        accepters = accepters + unames.get(es[a]) + ",";
                    }
                }

            }
            one.put("accepter", accepters);
            String unitName = "";
            try {
                if (one.containsKey("ZRQ") && one.getString("ZRQ").length() > 0) {
                    unit = one.getString("ZRQ").split("\\|")[0];
                    unitName = RIUtil.dicts.get(unit).getString("remark");
                } else if (one.containsKey("PCS") && one.getString("PCS").length() > 0) {
                    unitName = RIUtil.dicts.get(one.getString("PCS")).getString("remark");
                } else if (one.containsKey("FJ") && one.getString("FJ").length() > 0) {
                    unitName = RIUtil.dicts.get(one.getString("FJ")).getString("remark");
                }
            } catch (Exception ex) {

            }
            if (unitName.equals("常州市公安局")) {
                //  name = "市局";
            } else {
                unitName = unitName.replace("常州市公安局", "");
            }
            one.put("dict_name", unitName);
            one.put("unit", unitName);

            String statusName = "";
            if (one.containsKey("STATUS") && one.getString("STATUS").length() > 0) {
                int status = one.getInteger("STATUS");
                if (status == 1) {
                    statusName = "完成";
                } else if (status == 2) {
                    statusName = "未完成";
                } else if (status == 3) {
                    statusName = "逾期完成";
                } else if (status == 4) {
                    statusName = "逾期未完成";
                }
            }
            one.put("statusName", statusName);

            back.add(one);
        }

        String col = order.replace("desc", "").replace(" ", "");
        if (order.contains("desc")) {


            Collections.sort(back, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                long a = 0;
                long b = 0;

                try {
                    String ad = o1.getString(col);
                    a = RIUtil.dateToStamp(ad);
                    String bd = o2.getString(col);
                    b = RIUtil.dateToStamp(bd);
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a < b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });
        } else {
            Collections.sort(back, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                long a = 0;
                long b = 0;

                try {
                    String ad = o1.getString(col);
                    a = RIUtil.dateToStamp(ad);
                    String bd = o2.getString(col);
                    b = RIUtil.dateToStamp(bd);
                } catch (Exception ex) {

                }
                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });
        }


        return back;
    }

    private String GetSqlValue(String colSql, String user_id, String unit) {

        logger.warn(colSql);
        String exeid = user_id;
        String today = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String week_start = today;
        try {
            week_start = RIUtil.getWeekStart(today);
        } catch (Exception ex) {

        }
        String codename = "";
        String code = "";
        JSONObject done = RIUtil.dicts.get(unit);

        int t = done.getInteger("type");
        String u = "";
        if (t == 21 || t == 22 || t == 27) {
            codename = " FJ";
            code = unit.substring(0, 6) + "000000";
        } else if (t == 23 || t == 24 || t == 28) {
            codename = " FJ";
            code = unit.substring(0, 6) + "000000";
        } else if (t == 25) {
            codename = " PCS";
            code = unit.substring(0, 8) + "0000";
        } else {
            codename = " ZRQ";
            code = unit;
        }


        try {

            if (colSql.contains("#")) {
                String cols[] = colSql.split("#");
                String colN = cols[1];

                logger.warn(colN);
                String cc = colN.replace("$today$", today).replace("$week_start$", week_start);
                logger.warn(cc);

                String cs[] = cc.split(",");

                String col = RIUtil.GetNextDateTime(cs[0], Integer.parseInt(cs[1]));

                colSql = colSql.replace("#" + colN + "#", col);

            }
        } catch (Exception ex) {

        }

        colSql = colSql.replace("$exeid$", exeid).replace("$codename$", codename).replace("$code$", code);

        logger.warn(colSql);
        return colSql;

    }

    static JSONObject GetMyLevel(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String user_id = "";
        String unit = "";

        String type = "unFin";

        OracleHelper ora_hl = null;

        try {
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                user_id = data.getString("opt_user");
            } else {
                return ErrNo.set(501001);
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(501001);
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
            } else {
                return ErrNo.set(501001);
            }

            JSONObject done = RIUtil.dicts.get(unit);
            String group = "";
            String groupSql = "";
            int t = done.getInteger("type");
            String u = "";
            if (t == 21 || t == 22 || t == 27) {
                group = " FJ";
                groupSql = "  ";
                unit = "************";
            } else if (t == 23 || t == 24 || t == 28) {
                group = " PCS";
                u = unit.substring(0, 6);
                unit = u + "000000";
                groupSql = " and instr(FJ,'" + u + "')>0";
            } else if (t == 25 || t == 26) {
                group = " ZRQ";
                u = unit.substring(0, 8);
                unit = u + "0000";
                groupSql = " and instr(PCS,'" + u + "')>0";
            }


            String today = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            ora_hl = new OracleHelper("ora_hl");
            String sql = "";


            if (type.equals("unFin")) {
                //待办
                sql = "select count(1) as count, " + group + " as code from HL.QQB_TASK_OBJECT3 where  " + "(status=2"
                        + " or status=4) and  deleted =0 and fj is not null " + groupSql + " group by " + group;
                logger.warn(sql);
                List<JSONObject> list = ora_hl.query(sql);
                data.put("data", GetRetList(list, group, unit));

            } else if (type.equals("delay")) {

                //逾期
                sql = "select count(1) as count," + group + " as code from HL.QQB_TASK_OBJECT3 where  status=4 and  " + "deleted =0 and fj is not null  " + groupSql + " group by " + group;
                logger.warn(sql);
                List<JSONObject> list = ora_hl.query(sql);
                data.put("data", GetRetList(list, group, unit));


            } else if (type.equals("7down")) {

                //7天内下发的
                sql = "select count(1) as count," + group + " as code from HL.QQB_TASK_OBJECT3 where " +
                        "delivery_time>='" + RIUtil.GetNextDateTime(today, -7) + "' and deleted=0 and fj is not null "
                        + groupSql + " group by " + group;
                logger.warn(sql);
                List<JSONObject> list = ora_hl.query(sql);
                data.put("data", GetRetList(list, group, unit));
            } else if (type.equals("delayUnFix")) {
//责任未落实
                sql = "select count(1) as count," + group + " as code from HL.QQB_TASK_OBJECT3 where " + "FIX=1 and " + "(status=2 or status=4) and deleted=0 and fj is not null " + groupSql + " group by" + " " + group;
                logger.warn(sql);
                List<JSONObject> list = ora_hl.query(sql);
                data.put("data", GetRetList(list, group, unit));
            } else if (type.equals("near7")) {
                //7天到期
                sql = "select count(1) as count," + group + " as code from HL.QQB_TASK_OBJECT3 where " + "WHEN_FINISH"
                        + "<='" + RIUtil.GetNextDateTime(today, 7) + "' and status=2 and deleted =0 and fj is not " + "null " + groupSql + "group by " + group;
                logger.warn(sql);
                List<JSONObject> list = ora_hl.query(sql);
                data.put("data", GetRetList(list, group, unit));
            } else {
                return ErrNo.set(501001);
            }

            back.put("data", data);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 501002, Lib.getTrace(ex));
        } finally {

            ora_hl.close();

        }


        return back;
    }

    static JSONObject GetLevelTask(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String user_id = "";
        String unit = "";

        String type = "unFin";

        OracleHelper ora_hl = null;

        try {
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                user_id = data.getString("opt_user");
            } else {
                return ErrNo.set(501001);
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(501001);
            }


            JSONObject done = RIUtil.dicts.get(unit);
            String group = "";
            String groupSql = "";
            int t = done.getInteger("type");
            String u = "";
            if (t == 21 || t == 22 || t == 27) {
                group = " FJ";
                groupSql = "  ";
                unit = "************";
            } else if (t == 23 || t == 24 || t == 28) {
                group = " PCS";
                u = unit.substring(0, 6);
                unit = u + "000000";
                groupSql = " and instr(FJ,'" + u + "')>0";
            } else if (t == 25 || t == 26) {
                group = " ZRQ";
                u = unit.substring(0, 8);
                unit = u + "0000";
                groupSql = " and instr(PCS,'" + u + "')>0";
            }


            String today = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            ora_hl = new OracleHelper("ora_hl");
            String sql = "";

            String dets = "总数,完成,未完成";
            String ds[] = dets.split(",");
            String types = "total_level,fin_level,unFin_level";
            String tps[] = types.split(",");

            JSONArray datas = new JSONArray();

            for (int i = 0; i < ds.length; i++) {
                String d = ds[i];

                JSONObject det = new JSONObject();
                det.put("name", ds[i]);
                det.put("id", tps[i]);
                List<JSONObject> list = new ArrayList<>();
                if (tps[i].contains("total")) {
                    sql = "select count(1) as count, " + group + " as code from HL.QQB_TASK_OBJECT3 where  " + " " +
                            "deleted =0 and fj is not null " + groupSql + " group by" + " " + group;
                    logger.warn(sql);
                    list = ora_hl.query(sql);
                } else if (tps[i].contains("unFin")) {
                    sql = "select count(1) as count, " + group + " as code from HL.QQB_TASK_OBJECT3 where  " +
                            "(status=2" + " or status=4) and  deleted =0 and fj is not null " + groupSql + " group " + "by" + " " + group;
                    logger.warn(sql);
                    list = ora_hl.query(sql);
                } else if (tps[i].contains("fin")) {
                    sql = "select count(1) as count, " + group + " as code from HL.QQB_TASK_OBJECT3 where  " +
                            "(status=1" + " or status=3) and  deleted =0 and fj is not null " + groupSql + " group " + "by" + " " + group;
                    logger.warn(sql);
                    list = ora_hl.query(sql);
                }
                det.put("det", GetRetListIndex(list, group, unit));
                datas.add(det);


            }


            back.put("data", datas);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 501002, Lib.getTrace(ex));
        } finally {

            ora_hl.close();

        }


        return back;
    }

    private static List<JSONObject> GetRetList(List<JSONObject> list, String groupSql, String unit) {

        groupSql = groupSql.replace(" ", "");
        logger.warn(groupSql + "->" + unit);
        List<JSONObject> back = new ArrayList<>();

        JSONArray units = new JSONArray();
        int type = 0;
        if (groupSql.contains("FJ")) {
            units = RIUtil.GetDictByType(23);
        } else if (groupSql.contains("PCS")) {
            units = RIUtil.GetDictByTypeFather(25, unit);
        } else {
            units = RIUtil.GetDictByTypeFather(26, unit);
        }

        // logger.warn(units.toString());
        HashMap<String, Integer> us = new HashMap<>();

        for (int i = 0; i < units.size(); i++) {
            JSONObject uone = units.getJSONObject(i);
            us.put(uone.getString("id"), 0);
        }
        us.put(unit, 0);
        // logger.warn(us.toString());

        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String code = one.getString("CODE").trim();
            if (code != null && code.length() == 12) {
                // int count = one.getInteger("COUNT");

                int c = one.getInteger("COUNT");

                us.put(code, c);
            }

        }


        for (Map.Entry<String, Integer> uone : us.entrySet()) {
            String code = uone.getKey();
            int count = uone.getValue();

            JSONObject one = new JSONObject();
            one.put("id", code);
            try {
                String name = RIUtil.dicts.get(code).getString("remark");
                if (name.equals("常州市公安局")) {
                    //  name = "市局";
                } else {
                    name = name.replace("常州市公安局", "");
                }
                one.put("dict_name", name);
                one.put("count", count);
                one.put("index", RIUtil.dicts.get(code).getString("index_no"));
                one.put("cj", RIUtil.dicts.get(code).getString("type"));

                back.add(one);
            } catch (Exception ex) {
                logger.error(code);
            }


        }
        Collections.sort(back, (JSONObject o1, JSONObject o2) -> {

            long a = o1.getLong("count");
            long b = o2.getLong("count");

            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (a < b) {  //降序排列，升序改成a>b
                return 1;
            } else if (a == b) {
                return 0;
            } else {
                return -1;
            }
        });
        logger.warn(back.toString());

        return back;
    }


    private static List<JSONObject> GetRetListIndex(List<JSONObject> list, String groupSql, String unit) {

        groupSql = groupSql.replace(" ", "");
        logger.warn(groupSql + "->" + unit);
        List<JSONObject> back = new ArrayList<>();

        JSONArray units = new JSONArray();
        int type = 0;
        if (groupSql.contains("FJ")) {
            units = RIUtil.GetDictByType(23);
        } else if (groupSql.contains("PCS")) {
            units = RIUtil.GetDictByTypeFather(25, unit);
        } else {
            units = RIUtil.GetDictByTypeFather(26, unit);
        }

        // logger.warn(units.toString());
        HashMap<String, Integer> us = new HashMap<>();

        for (int i = 0; i < units.size(); i++) {
            JSONObject uone = units.getJSONObject(i);
            us.put(uone.getString("id"), 0);
        }
        us.put(unit, 0);
        // logger.warn(us.toString());

        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String code = one.getString("CODE").trim();
            if (code != null && code.length() == 12 && us.containsKey(code)) {
                // int count = one.getInteger("COUNT");

                int c = one.getInteger("COUNT");

                us.put(code, c);
            }

        }


        for (Map.Entry<String, Integer> uone : us.entrySet()) {
            String code = uone.getKey();
            int count = uone.getValue();

            JSONObject one = new JSONObject();
            one.put("id", code);
            try {
                String name = RIUtil.dicts.get(code).getString("dict_name");
                if (name.equals("常州市公安局")) {
                    //  name = "市局";
                } else {
                    name = name.replace("常州市公安局", "");
                }
                if (!name.contains("机场") && !name.contains("交通")) {
                    one.put("dict_name", name);
                    one.put("count", count);
                    one.put("index", RIUtil.dicts.get(code).getString("index_no"));
                    one.put("cj", RIUtil.dicts.get(code).getString("type"));

                    back.add(one);
                }
            } catch (Exception ex) {
                logger.error(code);
            }


        }
        Collections.sort(back, (JSONObject o1, JSONObject o2) -> {

            long a = o1.getLong("index");
            long b = o2.getLong("index");

            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (a > b) {  //降序排列，升序改成a>b
                return 1;
            } else if (a == b) {
                return 0;
            } else {
                return -1;
            }
        });
        logger.warn(back.toString());

        return back;
    }

    static JSONObject GetMyTaskGKList(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String user_id = "";
        String unit = "";


        OracleHelper ora_hl = null;

        try {
            if (data.containsKey("qqb_id") && data.getString("qqb_id").length() > 0) {
                user_id = data.getString("qqb_id");
            } else {
                return ErrNo.set(501001);
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(501001);
            }


            JSONObject ret = new JSONObject();


            String exeid = user_id;
            ora_hl = new OracleHelper("ora_hl");

            String today = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String sql = "select * from HL.QQB_TASK_OBJECT3 where  INSTR(EXECUTORIDS," + "'" + exeid + "')>0 and " +
                    "deleted =0 ";
            logger.warn(sql);
            List<JSONObject> query = ora_hl.query(sql);
            int unFin = 0;//待办
            int near7 = 0;//7天到期
            int delay = 0;//逾期
            int down7 = 0;//7天内下发的
            int fin = 0;//已完成
            for (int i = 0; i < query.size(); i++) {
                JSONObject one = query.get(i);
                int status = one.getInteger("STATUS");
                String whenFIn = one.getString("WHEN_FINISH");
                String deliveryTime = one.getString("DELIVERY_TIME");

                if (status == 2) {
                    unFin++;
                }
                if ((status == 2 || status == 4) && whenFIn.compareTo(RIUtil.GetNextDateTime(today, 7)) <= 0) {
                    near7++;
                }
                if (status == 4) {
                    delay++;
                }
                if (deliveryTime.compareTo(RIUtil.GetNextDate(today, -7) + " 00:00:00") >= 0) {
                    down7++;
                }
                if (status == 1 || status == 3) {
                    fin++;
                }
            }
            ret.put("unFin", unFin);
            ret.put("near7", near7);
            ret.put("delay", delay);
            ret.put("7down", down7);
            ret.put("fin", fin);
            back.put("data", ret);

//            //待办
//            sql = "select * from HL.QQB_TASK_OBJECT3 where  INSTR(EXECUTORIDS," + "'" + exeid + "')>0  and" + " " +
//            "status=2 and deleted =0 ";
//
//            int count = ora_hl.query(sql).size();
//
//            ret.put("unFin", count);
//            //7天到期
//            sql = "select * from HL.QQB_TASK_OBJECT3 where INSTR(EXECUTORIDS,'" + exeid + "')>0 and" + " " +
//            "when_finish<='" + RIUtil.GetNextDateTime(today, 7) + "' and (status=2 or status=4)" + " and " +
//            "deleted =0 ";
//            count = ora_hl.query(sql).size();
//            logger.warn(sql);
//            ret.put("near7", count);
//
//            //逾期
//            sql = "select * from HL.QQB_TASK_OBJECT3 where  INSTR(EXECUTORIDS,'" + exeid + "')>0 and" + " " +
//            "status=4 and deleted =0 ";
//            logger.warn(sql);
//            count = ora_hl.query(sql).size();
//
//            ret.put("delay", count);
//
//            //7天内下发的
//            sql = "select * from HL.QQB_TASK_OBJECT3 where INSTR(EXECUTORIDS,'" + exeid + "')>0 and" + " " +
//            "delivery_time>='" + RIUtil.GetNextDate(today, -7) + " 00:00:00" + "' and deleted = 0  ";
//            logger.warn(sql);
//            count = ora_hl.query(sql).size();
//
//            ret.put("7down", count);
//
//            //已完成
//            sql = "select * from HL.QQB_TASK_OBJECT3 where INSTR(EXECUTORIDS,'" + exeid + "')>0 and (status=1 or
//            status=3) and deleted=0  ";
//            logger.warn(sql);
//            count = ora_hl.query(sql).size();
//
//            ret.put("fin", count);
//
//            back.put("data", ret);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 501002, Lib.getTrace(ex));
        } finally {

            ora_hl.close();

        }


        return back;

    }

    static JSONObject GetMyTaskGKListZF(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String user_id = "";
        String unit = "";


        OracleHelper ora_hl = null;

        try {
            if (data.containsKey("qqb_id") && data.getString("qqb_id").length() > 0) {
                user_id = data.getString("qqb_id");
            } else {
                return ErrNo.set(501001);
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(501001);
            }


            JSONObject ret = new JSONObject();


            String exeid = user_id;
            ora_hl = new OracleHelper("ora_hl");

            long start = System.currentTimeMillis();
            String today1 = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String cWeek1 = RIUtil.getWeekStart(today1);
            //待办
            String sql =
                    "select count(1) as count from HL.QQB_TASK_OBJECT3 where  INSTR(EXECUTORIDS," + "'" + exeid + "')"
                            + ">0  and" + " " + "status=2 and deleted =0 ";
          /*  logger.warn(sql);
            int count = ora_hl.query_count(sql);

            ret.put("unFin", count);

            //任务总数
            sql = "select count(1) as count from HL.QQB_TASK_OBJECT3 where  INSTR(EXECUTORIDS," + "'" + exeid + "')"
            + ">0  and deleted =0 ";
            logger.warn(sql);
            count = ora_hl.query_count(sql);

            ret.put("total", count);

            //已完成
            sql = "select count(1) as count from HL.QQB_TASK_OBJECT3 where INSTR(EXECUTORIDS,'" + exeid + "')>0 and"
            + " " + "(status=1 or status=3) and deleted=0  ";
            logger.warn(sql);
            count = ora_hl.query_count(sql);

            ret.put("fin", count);

            //完成率
            double tot = ret.getDouble("total");
            double finP = count / tot * 100;
            ret.put("finP", String.format("%.2f", finP));


            //今日任务
            sql = "select count(1) as count from HL.QQB_TASK_OBJECT3 where INSTR(EXECUTORIDS,'" + exeid + "')>0 " +
                    "and when_finish>='" + today1 + " 00:00:00' and status=2" + " and " + "deleted " + "=0 ";
            count = ora_hl.query_count(sql);
            logger.warn(sql);
            ret.put("today", count);

            //本周任务


            sql = "select count(1) as count from HL.QQB_TASK_OBJECT3 where INSTR(EXECUTORIDS,'" + exeid + "')>0 " +
                    "and when_finish>='" + cWeek1 + " 00:00:00' and status=2" + " and " + "deleted " + "=0 ";
            count = ora_hl.query_count(sql);
            logger.warn(sql);
            ret.put("week", count);

            //逾期
            sql = "select count(1) as count from HL.QQB_TASK_OBJECT3 where  INSTR(EXECUTORIDS,'" + exeid + "')>0 and"
             + " " + "status=4 and deleted =0 ";
            logger.warn(sql);
            count = ora_hl.query_count(sql);

            ret.put("delay", count);*/

            long end = System.currentTimeMillis() - start;
            // System.out.println("--------->" + end);
            start = System.currentTimeMillis();
            sql = "select when_finish,status from HL.QQB_TASK_OBJECT3 where  INSTR(EXECUTORIDS,'" + exeid + "')>0 " + "and " + "deleted =0 ";
            List<JSONObject> list = ora_hl.query(sql);
            int t0day = 0;//今日
            int uf = 0;//未完成
            int wk = 0;//本周
            int dl = 0;//逾期
            int tt = 0;//总数
            int f = 0;//完成
            today1 = today1 + " 00:00:00";
            long ttdd = RIUtil.dateToStamp(today1);
            long wwkk = RIUtil.dateToStamp(cWeek1);

            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                int status = one.getInteger("STATUS");
                String whenFIn = one.getString("WHEN_FINISH");
                long wf = 9999999999999L;
                try {
                    wf = RIUtil.dateToStamp(whenFIn);
                } catch (Exception ex) {

                }

                tt++;
                if (status == 2 || status == 4) {
                    uf++;

                } else {
                    f++;
                }
                if (start == 4) {
                    dl++;
                }
                if (status == 2 && wf >= ttdd) {
                    t0day++;
                }
                if (status == 2 && wf >= wwkk) {
                    wk++;
                }


            }

            double finP = (double) f / (double) tt * 100;
            ret.put("finP", String.format("%.2f", finP) + "%");
            ret.put("week", wk);
            ret.put("delay", dl);
            ret.put("unFin", uf);
            ret.put("fin", f);
            ret.put("total", tt);
            ret.put("today", t0day);
            end = System.currentTimeMillis() - start;
            //  System.out.println("--------->" + end);
            back.put("data", ret);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 501002, Lib.getTrace(ex));
        } finally {
            if (ora_hl != null) {

                ora_hl.close();
            }

        }


        return back;

    }


    private String GetQQBUserId1(String unit, String user_id) {
        MysqlHelper qqb_user = null;
        String exeid = "";
        try {
            qqb_user = new MysqlHelper("mysql_qqb_user");

            JSONObject done = RIUtil.dicts.get(unit);
            int type = done.getInteger("type");
            int t = 0;
            if (type == 21 || type == 22 || type == 27) {
                t = 1;
            } else if (type == 23 || type == 24 || type == 28) {
                t = 2;
            } else if (type == 25) {
                t = 3;
            } else {
                t = 13;
            }

            String sql = "select id from auth.SYS_AUTH_USER where idcard_no='" + user_id + "' and deleted=0 and " +
                    "(IS_TEMPORARY  is null or IS_TEMPORARY <>1)  and " + "type=" + t;
            exeid = qqb_user.query_one(sql, "ID");

        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
        } finally {
            qqb_user.close();


        }
        return exeid;
    }

    //******UPDATE*******
    private JSONObject updateDict(JSONObject data) {

        JSONObject back = ErrNo.set(0);
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String father_id = "";

            String label_name = "";
            String opt_user = "";


            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(465004);
            }


            if (data.containsKey("label_name") && data.getString("label_name").length() > 0) {
                label_name = data.getString("label_name");
                sql = sql + " dict_name='" + label_name + "' , ";

            }


            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(465004);
            }
            String sqls = "update dict set " + sql + " isdelete=1  where id='" + id + "'";
            System.out.println(sqls);
            mysql.update(sqls);

            sql = "select * from dict where id='" + id + "'";
            List<JSONObject> list = mysql.query(sql);
            RIUtil.dicts.put(id, list.get(0));


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 465003, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private JSONObject deleteDict(JSONObject data) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(465008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(465008);
        }

        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();

            String sqls =
                    "update dict set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);

            RIUtil.dicts.remove(id);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 465007, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

    //******CREATE*******
    private JSONObject createDict(JSONObject data) {

        JSONObject back = ErrNo.set(0);
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();

            String father_id = "";
            String type = "175";
            String dict_name = "";
            String color = "";
            String index_no = "0";
            String permission = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "1";
            String is_show = "0";
            String remark = "";
            String unit = "";
            if (data.containsKey("father_id") && data.getString("father_id").length() > 0) {
                father_id = data.getString("father_id");
            } else {
                return ErrNo.set(465002);
            }

            if (data.containsKey("label_name") && data.getString("label_name").length() > 0) {
                dict_name = data.getString("label_name");
            } else {
                return ErrNo.set(465002);
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(465002);
            }


            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");

                JSONObject rem = JSONObject.parseObject(remark);
                rem.remove("limit");
                rem.remove("type");
                rem.remove("page");

                remark = rem.toString();
            }

            JSONObject done = RIUtil.dicts.get(unit);
            int t = done.getInteger("type");

            if (t == 23 || t == 24 || t == 28) {
                permission = unit.substring(0, 6) + "000000";
            } else if (t == 25 || t == 26) {
                permission = unit.substring(0, 8) + "0000";
            } else {
                permission = "************";
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            }
            String id = String.valueOf(UUID.randomUUID());
            String sqls = "insert dict (id,father_id,type," + "dict_name,color,is_show,index_no," + "permission," +
                    "create_user,create_time,isdelete,remark)" + "values('" + id + "','" + father_id + "','" + type + "'," + "'" + dict_name + "','" + color + "','" + is_show + "','" + index_no + "'," + "'" + permission + "','" + create_user + "','" + create_time + "','" + isdelete + "','" + remark + "')";
            mysql.update(sqls);
            String sql = "select * from dict where id='" + id + "'";
            List<JSONObject> list = mysql.query(sql);

            RIUtil.dicts.put(id, list.get(0));

            if (remark.length() > 2) {
                sqls = "insert into qqb_task_label_count(dict_id,count,unit) values('" + id + "','0','" + permission + "')";
                logger.warn(sqls);
                mysql.update(sqls);
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 465001, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private JSONObject getDict(JSONObject data) {

        JSONObject back = ErrNo.set(0);
        InfoModelHelper mysql1 = null;
        try {
            mysql1 = InfoModelPool.getModel();

            String sql = "";

            String id = "";
            String father_id = "";
            String type = "";
            String dict_name = "";
            String unit = "";

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(465002);
            }
            String permission;
            JSONObject done = RIUtil.dicts.get(unit);
            logger.warn(done.toString());
            int t = done.getInteger("type");

            String fName = "";
            String permss = "";

            String units = "";
            if (t == 23 || t == 24 || t == 28) {
                permission = unit.substring(0, 6) + "000000";

                father_id = "1,2";
                fName = "市局,分局";
                permss = "0,1";
                units = "************," + permission;
                unit = permission;
            } else if (t == 25 || t == 26) {

                permission = unit.substring(0, 8) + "0000";
                father_id = "1,2,3";
                fName = "市局,分局,派出所";
                permss = "0,0,1";
                units = "************," + unit.substring(0, 6) + "000000," + permission;
                unit = permission;
            } else {

                if (unit.contains("32040014")) {
                    father_id = "1,4";
                    fName = "市局,分类";
                    permss = "1,1";
                    units = "************,************";
                    unit = "************";
                } else {
                    father_id = "1";
                    fName = "市局";
                    permss = "1";
                    units = "************";
                    unit = "************";
                }
            }

            String fids[] = father_id.split(",");
            String fnames[] = fName.split(",");
            String perms[] = permss.split(",");
            String unitss[] = units.split(",");
            List<JSONObject> dicts = new ArrayList<>();

            String sqls = "select id,dict_name,remark,index_no,father_id,gadm from dict where type=175 and " +
                    "isdelete=1" + "  " + "order by index_no";
            logger.warn(sqls);
            dicts = mysql1.query(sqls);
            JSONArray trees = new JSONArray();
            for (int i = 0; i < fids.length; i++) {
                String fid = fids[i];
                int tot2 = 0;
                JSONObject fone = new JSONObject();
                fone.put("id", fid);
                fone.put("dict_name", fnames[i]);

                fone.put("level", 1);
                fone.put("isFinx", perms[i]);

                JSONArray fdets = new JSONArray();

                sqls = "select id,dict_name,remark,gadm from dict where type=175 and permission='" + unitss[i] + "'" + " " + "and " + "isdelete=1 and " + "father_id ='" + fid + "' order by index_no";
                logger.warn(sqls);
                List<JSONObject> ll = mysql1.query(sqls);

                if (ll.size() > 0) {

                    for (int a = 0; a < ll.size(); a++) {
                        JSONObject aone = ll.get(a);
                        String aid = aone.getString("id");

                        aone.put("level", 2);
                        aone.put("isFinx", perms[i]);

                        List<JSONObject> adets = new ArrayList<>();
                        int tot3 = 0;
                        for (int b = 0; b < dicts.size(); b++) {
                            JSONObject bone = dicts.get(b);
                            String bfid = bone.getString("father_id");
                            if (bfid.equals(aid)) {
                                String bid = bone.getString("id");
                                String bname = bone.getString("dict_name");
                                JSONObject cone = new JSONObject();
                                cone.put("id", bid);
                                cone.put("dict_name", bname);
                                cone.put("level", 3);


                                cone.put("isFinx", perms[i]);
                                cone.put("remark", bone.getString("remark"));
                                cone.put("index_no", bone.getString("index_no"));

                                //    System.out.println(cone);

                                sqls = "select a.id,a.dict_name,a.type,b.count,a.remark,a.index_no,father_id from " + "dict a left join qqb_task_label_count b on a.id=b.dict_id" + " where " + "a.type=175 and b.unit='" + unit + "' and a.isdelete=1";
                                // logger.warn(sqls);
                                List<JSONObject> dict175 = mysql1.query(sqls);


                                List<JSONObject> dets4 = new ArrayList<>();
                                int tot4 = 0;
                                for (int f = 0; f < dict175.size(); f++) {
                                    JSONObject one4 = dict175.get(f);
                                    String fid4 = one4.getString("father_id");
                                    if (fid4.equals(bid)) {

                                        String name4 = one4.getString("dict_name");
                                        JSONObject det4 = new JSONObject();
                                        det4.put("id", one4.getString("id"));
                                        det4.put("dict_name", name4);
                                        det4.put("level", 4);

                                        det4.put("isFinx", perms[i]);
                                        det4.put("remark", one4.getString("remark"));
                                        det4.put("index_no", one4.getString("index_no"));
                                        int c4 = 0;
                                        try {
                                            c4 = one4.getInteger("count");
                                            det4.put("count", c4);
                                        } catch (Exception ex) {
                                            det4.put("count", c4);
                                        }
                                        tot4 = tot4 + c4;
                                        tot3 = tot3 + c4;
                                        tot2 = tot2 + c4;
                                        //        System.out.println(det4);


                                        dets4.add(det4);
                                    }

                                }
                                Collections.sort(dets4, (JSONObject o1, JSONObject o2) -> {

                                    long d = o1.getLong("index_no");
                                    long e = o2.getLong("index_no");

                                    // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                                    if (d > e) {  //降序排列，升序改成a>b
                                        return 1;
                                    } else if (d == e) {
                                        return 0;
                                    } else {
                                        return -1;
                                    }
                                });
                                //    logger.warn(back.toString());

                                cone.put("count", tot4);
                                cone.put("dets", dets4);

                                adets.add(cone);
                            }

                        }
                        //     System.out.println(adets);

                        Collections.sort(adets, (JSONObject o1, JSONObject o2) -> {

                            long d = o1.getLong("index_no");
                            long b = o2.getLong("index_no");

                            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                            if (d > b) {  //降序排列，升序改成a>b
                                return 1;
                            } else if (d == b) {
                                return 0;
                            } else {
                                return -1;
                            }
                        });
                        //     logger.warn(back.toString());
                        aone.put("count", tot3);
                        aone.put("dets", adets);

                        fdets.add(aone);
                    }

                    fone.put("count", tot2);
                    fone.put("dets", fdets);
                } else {
                    fone.put("count", 0);
                    fone.put("dets", new JSONArray());
                }


                trees.add(fone);
            }


            back.put("data", trees);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 465005, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql1);
        }
        return back;
    }

    private JSONObject UpdateLabelTree(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String ids = "";

        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();

            if (data.containsKey("ids") && data.getString("ids").length() > 0) {
                ids = data.getString("ids");
            } else {
                return ErrNo.set(465004);
            }

            String idss[] = ids.split(",");

            for (int i = 0; i < idss.length; i++) {
                String id = idss[i];

                int index = i + 1;

                String sql = "update dict set index_no=" + index + " where id='" + id + "'";
                mysql.update(sql);


            }

            //initDict();


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 465003, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }

        return back;
    }

    public void initDict() {
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql =
                    "select id,dict_name,permission,color," + "father_id,index_no,type,gadm,remark from dict " +
                            "where " + "isdelete=1 and type is not null order by type,index_no,id";
            System.out.println(sql);
            List<JSONObject> list = mysql.query(sql);
            System.out.println(list.size());
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);

                    if (!one.containsKey("dict_name")) {

                        break;
                    }
                    if (!one.containsKey("permission")) {
                        one.put("permission", "");
                    }
                    if (!one.containsKey("color")) {
                        one.put("color", "");
                    }
                    if (!one.containsKey("father_id")) {
                        one.put("father_id", "");
                    }
                    if (!one.containsKey("index_no")) {
                        one.put("index_no", "99");
                    }
                    if (!one.containsKey("type")) {
                        one.put("type", "");
                    }
                    if (!one.containsKey("gadm")) {
                        one.put("gadm", "");
                    }
                    if (!one.containsKey("remark")) {
                        one.put("remark", "");
                    }

                    RIUtil.dicts.put(one.getString("id"), one);

                }
                logger.warn("init.dict->" + RIUtil.dicts.size());
            }

        } catch (Exception ex) {
            System.out.println(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }
    }
}
