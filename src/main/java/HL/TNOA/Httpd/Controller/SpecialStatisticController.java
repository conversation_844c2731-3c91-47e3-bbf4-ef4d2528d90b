package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jxl.Workbook;
import jxl.write.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class SpecialStatisticController {
    private static Logger logger = LoggerFactory.getLogger(SpecialStatisticController.class);
    // private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static String ip = "";

    @RequestMapping(method = {RequestMethod.POST}, path = {"/ss_static"})
    public static JSONObject getResult(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        logger.warn("sta_--->" + data.toString());
        ip = request.getRemoteAddr();
        String opt = "";
        InfoModelHelper mysql = null;
        try {
            mysql = request.openInfoImpl();

            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("static_qqb_task")) {
                    return StaticQQBTask(data, mysql);
                } else if (opt.equals("get_qqb_file_list")) {
                    return GetQQBFileList();
                } else if (opt.equals("static_qqb_task_change")) {
                    return StaticQQBTaskChange(data, mysql);
                } else if (opt.equals("static_zxqc")) {
                    return StaticZXQC(data);
                } else {
                    return ErrNo.set(446001);
                }
            } else {
                return ErrNo.set(446001);
            }
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private static JSONObject StaticZXQCTotal(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql_sso");
            String sql = "select a.*,b.result from circle a left join static_zxqc b on a.id =b.circle_id where b" +
                    ".police_id='000000' order by create_time";
            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(446002);
        } finally {
            mysql.close();


        }
    }

    private static JSONObject StaticZXQC(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        MysqlHelper zxqc = null;
        try {

            zxqc = new MysqlHelper("mysql_zxqc");
            String circle_id = "";
            int isExp = 0;
            if (data.containsKey("circle_id") && data.getString("circle_id").length() > 0) {
                circle_id = data.getString("circle_id").replace(",", "','");
            } else {
                return ErrNo.set(446003);
            }
            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getInteger("isExp");
            }
            String sql =
                    "select * from static_zxqc where circle_id in('" + circle_id + "') AND deleted=0 order by " +
                            "circle_id,org";
            List<JSONObject> ll = zxqc.query(sql);

            for (JSONObject one : ll) {
                String org = one.getString("org");
                JSONObject orgJs = RIUtil.dicts.get(org);
                if (orgJs != null && orgJs.containsKey("dict_name")) {
                    String orgName = orgJs.getString("dict_name");
                    one.put("orgName", orgName);
                } else {
                    one.put("orgName", "");
                }
            }

            int file_id = -1;
            if (ll.size() > 0) {
                List<JSONObject> list = AddResults(ll);
                logger.warn(list.toString());

                Collections.sort(list, (JSONObject o1, JSONObject o2) -> {
                    //转成JSON对象中保存的值类型
                    int a = 0;
                    int b = 0;

                    try {
                        int index = -1;
                        if (!o1.getString("org").contains("000000000000")) {
                            try {
                                index = RIUtil.dicts.get(o1.getString("org")).getInteger("index_no");
                            } catch (Exception ex) {

                            }
                            index = 99;
                        }
                        a = index;


                        index = -1;
                        if (!o2.getString("org").contains("000000000000")) {
                            try {
                                index = RIUtil.dicts.get(o2.getString("org")).getInteger("index_no");
                            } catch (Exception ex) {

                            }
                            index = 99;
                        }
                        b = index;
                    } catch (Exception ex) {
                        System.out.println(Lib.getTrace(ex));

                    }

                    // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                    if (a > b) {  //降序排列，升序改成a>b
                        return 1;
                    } else if (a == b) {
                        return 0;
                    } else {
                        return -1;
                    }
                });
                back.put("data", list);

                ll = zxqc.query(sql);
                List<JSONObject> orglist = AddResults_Org(ll);
                Collections.sort(orglist, (JSONObject o1, JSONObject o2) -> {
                    //转成JSON对象中保存的值类型
                    int a = 0;
                    int b = 0;

                    try {

                        // logger.warn(o1.getString("org"));
                        int index = -1;
                        if (!o1.getString("org").contains("000000000000")) {
                            try {
                                index = RIUtil.dicts.get(o1.getString("org")).getInteger("index_no");
                            } catch (Exception ex) {
                                index = 99;
                            }

                        }
                        a = index;

                        //logger.warn(o2.getString("org"));
                        index = -1;
                        if (!o2.getString("org").contains("000000000000")) {
                            try {
                                index = RIUtil.dicts.get(o2.getString("org")).getInteger("index_no");
                            } catch (Exception ex) {
                                index = 99;
                            }
                        }
                        b = index;
                    } catch (Exception ex) {
                        System.out.println(Lib.getTrace(ex));

                    }

                    // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                    if (a > b) {  //降序排列，升序改成a>b
                        return 1;
                    } else if (a == b) {
                        return 0;
                    } else {
                        return -1;
                    }
                });


                back.put("data_org", orglist);
                if (isExp == 1) {
                    file_id = CreateZXQCExcel(list, "专项清查", orglist);
                }
            } else {
                back.put("data", new JSONArray());
            }
            back.put("file_id", file_id);

            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(446002);
        } finally {
            zxqc.close();
        }

    }

    private static List<JSONObject> AddResults(List<JSONObject> list) {
        HashMap<String, JSONObject> dets = new HashMap<>();
        HashMap<String, JSONObject> jsons = new HashMap<>();
        List<JSONObject> back = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            JSONObject ones = list.get(i);
            String czr = ones.getString("police_id");
            JSONObject one = ones.getJSONObject("result");

            if (!jsons.containsKey(czr)) {
                jsons.put(czr, ones);
            }
            if (dets.containsKey(czr)) {
                int B = 0;
                int C = 0;
                int D = 0;
                int E = 0;
                int F = 0;
                int G = 0;
                int H = 0;
                int I = 0;
                int J = 0;
                int K = 0;
                int L = 0;
                int M = 0;
                int N = 0;
                int O = 0;
                int P = 0;
                int Q = 0;
                int R = 0;
                int S = 0;
                int T = 0;
                int U = 0;
                int V = 0;
                int W = 0;
                int X = 0;
                int Y = 0;
                int Z = 0;
                int AA = 0;
                int AB = 0;
                int AC = 0;
                int AD = 0;
                int AE = 0;
                int AF = 0;
                int AG = 0;
                int AH = 0;
                int AI = 0;
                int AJ = 0;
                int AK = 0;
                int AL = 0;
                int AM = 0;
                int AN = 0;
                int AO = 0;
                int AP = 0;
                int AQ = 0;
                int AR = 0;
                int AS = 0;
                int AT = 0;
                int AU = 0;
                int AV = 0;
                int AW = 0;
                int AX = 0;
                int AY = 0;
                int BB = 0;
                int BC = 0;
                int BD = 0;
                int BE = 0;
                int BF = 0;
                int BG = 0;
                int AZ = 0;
                int BA = 0;
                int TOT = 0;
                int BI = 0;
                int BJ = 0;

                JSONObject y = dets.get(czr);
                int yB = 0;
                int oB = 0;
                try {
                    yB = y.getIntValue("B");
                } catch (Exception ex) {
                }
                try {
                    oB = one.getIntValue("B");
                } catch (Exception ex) {
                }
                B = yB + oB;
                int yC = 0;
                int oC = 0;
                try {
                    yC = y.getIntValue("C");
                } catch (Exception ex) {
                }
                try {
                    oC = one.getIntValue("C");
                } catch (Exception ex) {
                }
                C = yC + oC;
                int yD = 0;
                int oD = 0;
                try {
                    yD = y.getIntValue("D");
                } catch (Exception ex) {
                }
                try {
                    oD = one.getIntValue("D");
                } catch (Exception ex) {
                }
                D = yD + oD;
                int yE = 0;
                int oE = 0;
                try {
                    yE = y.getIntValue("E");
                } catch (Exception ex) {
                }
                try {
                    oE = one.getIntValue("E");
                } catch (Exception ex) {
                }
                E = yE + oE;
                int yF = 0;
                int oF = 0;
                try {
                    yF = y.getIntValue("F");
                } catch (Exception ex) {
                }
                try {
                    oF = one.getIntValue("F");
                } catch (Exception ex) {
                }
                F = yF + oF;
                int yG = 0;
                int oG = 0;
                try {
                    yG = y.getIntValue("G");
                } catch (Exception ex) {
                }
                try {
                    oG = one.getIntValue("G");
                } catch (Exception ex) {
                }
                G = yG + oG;
                int yH = 0;
                int oH = 0;
                try {
                    yH = y.getIntValue("H");
                } catch (Exception ex) {
                }
                try {
                    oH = one.getIntValue("H");
                } catch (Exception ex) {
                }
                H = yH + oH;
                int yI = 0;
                int oI = 0;
                try {
                    yI = y.getIntValue("I");
                } catch (Exception ex) {
                }
                try {
                    oI = one.getIntValue("I");
                } catch (Exception ex) {
                }
                I = yI + oI;
                int yJ = 0;
                int oJ = 0;
                try {
                    yJ = y.getIntValue("J");
                } catch (Exception ex) {
                }
                try {
                    oJ = one.getIntValue("J");
                } catch (Exception ex) {
                }
                J = yJ + oJ;
                int yK = 0;
                int oK = 0;
                try {
                    yK = y.getIntValue("K");
                } catch (Exception ex) {
                }
                try {
                    oK = one.getIntValue("K");
                } catch (Exception ex) {
                }
                K = yK + oK;
                int yL = 0;
                int oL = 0;
                try {
                    yL = y.getIntValue("L");
                } catch (Exception ex) {
                }
                try {
                    oL = one.getIntValue("L");
                } catch (Exception ex) {
                }
                L = yL + oL;
                int yM = 0;
                int oM = 0;
                try {
                    yM = y.getIntValue("M");
                } catch (Exception ex) {
                }
                try {
                    oM = one.getIntValue("M");
                } catch (Exception ex) {
                }
                M = yM + oM;
                int yN = 0;
                int oN = 0;
                try {
                    yN = y.getIntValue("N");
                } catch (Exception ex) {
                }
                try {
                    oN = one.getIntValue("N");
                } catch (Exception ex) {
                }
                N = yN + oN;
                int yO = 0;
                int oO = 0;
                try {
                    yO = y.getIntValue("O");
                } catch (Exception ex) {
                }
                try {
                    oO = one.getIntValue("O");
                } catch (Exception ex) {
                }
                O = yO + oO;
                int yP = 0;
                int oP = 0;
                try {
                    yP = y.getIntValue("P");
                } catch (Exception ex) {
                }
                try {
                    oP = one.getIntValue("P");
                } catch (Exception ex) {
                }
                P = yP + oP;
                int yQ = 0;
                int oQ = 0;
                try {
                    yQ = y.getIntValue("Q");
                } catch (Exception ex) {
                }
                try {
                    oQ = one.getIntValue("Q");
                } catch (Exception ex) {
                }
                Q = yQ + oQ;
                int yR = 0;
                int oR = 0;
                try {
                    yR = y.getIntValue("R");
                } catch (Exception ex) {
                }
                try {
                    oR = one.getIntValue("R");
                } catch (Exception ex) {
                }
                R = yR + oR;
                int yS = 0;
                int oS = 0;
                try {
                    yS = y.getIntValue("S");
                } catch (Exception ex) {
                }
                try {
                    oS = one.getIntValue("S");
                } catch (Exception ex) {
                }
                S = yS + oS;
                int yT = 0;
                int oT = 0;
                try {
                    yT = y.getIntValue("T");
                } catch (Exception ex) {
                }
                try {
                    oT = one.getIntValue("T");
                } catch (Exception ex) {
                }
                T = yT + oT;
                int yU = 0;
                int oU = 0;
                try {
                    yU = y.getIntValue("U");
                } catch (Exception ex) {
                }
                try {
                    oU = one.getIntValue("U");
                } catch (Exception ex) {
                }
                U = yU + oU;
                int yV = 0;
                int oV = 0;
                try {
                    yV = y.getIntValue("V");
                } catch (Exception ex) {
                }
                try {
                    oV = one.getIntValue("V");
                } catch (Exception ex) {
                }
                V = yV + oV;
                int yW = 0;
                int oW = 0;
                try {
                    yW = y.getIntValue("W");
                } catch (Exception ex) {
                }
                try {
                    oW = one.getIntValue("W");
                } catch (Exception ex) {
                }
                W = yW + oW;
                int yX = 0;
                int oX = 0;
                try {
                    yX = y.getIntValue("X");
                } catch (Exception ex) {
                }
                try {
                    oX = one.getIntValue("X");
                } catch (Exception ex) {
                }
                X = yX + oX;
                int yY = 0;
                int oY = 0;
                try {
                    yY = y.getIntValue("Y");
                } catch (Exception ex) {
                }
                try {
                    oY = one.getIntValue("Y");
                } catch (Exception ex) {
                }
                Y = yY + oY;
                int yZ = 0;
                int oZ = 0;
                try {
                    yZ = y.getIntValue("Z");
                } catch (Exception ex) {
                }
                try {
                    oZ = one.getIntValue("Z");
                } catch (Exception ex) {
                }
                Z = yZ + oZ;
                int yAA = 0;
                int oAA = 0;
                try {
                    yAA = y.getIntValue("AA");
                } catch (Exception ex) {
                }
                try {
                    oAA = one.getIntValue("AA");
                } catch (Exception ex) {
                }
                AA = yAA + oAA;
                int yAB = 0;
                int oAB = 0;
                try {
                    yAB = y.getIntValue("AB");
                } catch (Exception ex) {
                }
                try {
                    oAB = one.getIntValue("AB");
                } catch (Exception ex) {
                }
                AB = yAB + oAB;
                int yAC = 0;
                int oAC = 0;
                try {
                    yAC = y.getIntValue("AC");
                } catch (Exception ex) {
                }
                try {
                    oAC = one.getIntValue("AC");
                } catch (Exception ex) {
                }
                AC = yAC + oAC;
                int yAD = 0;
                int oAD = 0;
                try {
                    yAD = y.getIntValue("AD");
                } catch (Exception ex) {
                }
                try {
                    oAD = one.getIntValue("AD");
                } catch (Exception ex) {
                }
                AD = yAD + oAD;
                int yAE = 0;
                int oAE = 0;
                try {
                    yAE = y.getIntValue("AE");
                } catch (Exception ex) {
                }
                try {
                    oAE = one.getIntValue("AE");
                } catch (Exception ex) {
                }
                AE = yAE + oAE;
                int yAF = 0;
                int oAF = 0;
                try {
                    yAF = y.getIntValue("AF");
                } catch (Exception ex) {
                }
                try {
                    oAF = one.getIntValue("AF");
                } catch (Exception ex) {
                }
                AF = yAF + oAF;
                int yAG = 0;
                int oAG = 0;
                try {
                    yAG = y.getIntValue("AG");
                } catch (Exception ex) {
                }
                try {
                    oAG = one.getIntValue("AG");
                } catch (Exception ex) {
                }
                AG = yAG + oAG;
                int yAH = 0;
                int oAH = 0;
                try {
                    yAH = y.getIntValue("AH");
                } catch (Exception ex) {
                }
                try {
                    oAH = one.getIntValue("AH");
                } catch (Exception ex) {
                }
                AH = yAH + oAH;
                int yAI = 0;
                int oAI = 0;
                try {
                    yAI = y.getIntValue("AI");
                } catch (Exception ex) {
                }
                try {
                    oAI = one.getIntValue("AI");
                } catch (Exception ex) {
                }
                AI = yAI + oAI;
                int yAJ = 0;
                int oAJ = 0;
                try {
                    yAJ = y.getIntValue("AJ");
                } catch (Exception ex) {
                }
                try {
                    oAJ = one.getIntValue("AJ");
                } catch (Exception ex) {
                }
                AJ = yAJ + oAJ;
                int yAK = 0;
                int oAK = 0;
                try {
                    yAK = y.getIntValue("AK");
                } catch (Exception ex) {
                }
                try {
                    oAK = one.getIntValue("AK");
                } catch (Exception ex) {
                }
                AK = yAK + oAK;
                int yAL = 0;
                int oAL = 0;
                try {
                    yAL = y.getIntValue("AL");
                } catch (Exception ex) {
                }
                try {
                    oAL = one.getIntValue("AL");
                } catch (Exception ex) {
                }
                AL = yAL + oAL;
                int yAM = 0;
                int oAM = 0;
                try {
                    yAM = y.getIntValue("AM");
                } catch (Exception ex) {
                }
                try {
                    oAM = one.getIntValue("AM");
                } catch (Exception ex) {
                }
                AM = yAM + oAM;
                int yAN = 0;
                int oAN = 0;
                try {
                    yAN = y.getIntValue("AN");
                } catch (Exception ex) {
                }
                try {
                    oAN = one.getIntValue("AN");
                } catch (Exception ex) {
                }
                AN = yAN + oAN;
                int yAO = 0;
                int oAO = 0;
                try {
                    yAO = y.getIntValue("AO");
                } catch (Exception ex) {
                }
                try {
                    oAO = one.getIntValue("AO");
                } catch (Exception ex) {
                }
                AO = yAO + oAO;
                int yAP = 0;
                int oAP = 0;
                try {
                    yAP = y.getIntValue("AP");
                } catch (Exception ex) {
                }
                try {
                    oAP = one.getIntValue("AP");
                } catch (Exception ex) {
                }
                AP = yAP + oAP;
                int yAQ = 0;
                int oAQ = 0;
                try {
                    yAQ = y.getIntValue("AQ");
                } catch (Exception ex) {
                }
                try {
                    oAQ = one.getIntValue("AQ");
                } catch (Exception ex) {
                }
                AQ = yAQ + oAQ;
                int yAR = 0;
                int oAR = 0;
                try {
                    yAR = y.getIntValue("AR");
                } catch (Exception ex) {
                }
                try {
                    oAR = one.getIntValue("AR");
                } catch (Exception ex) {
                }
                AR = yAR + oAR;
                int yAS = 0;
                int oAS = 0;
                try {
                    yAS = y.getIntValue("AS");
                } catch (Exception ex) {
                }
                try {
                    oAS = one.getIntValue("AS");
                } catch (Exception ex) {
                }
                AS = yAS + oAS;
                int yAT = 0;
                int oAT = 0;
                try {
                    yAT = y.getIntValue("AT");
                } catch (Exception ex) {
                }
                try {
                    oAT = one.getIntValue("AT");
                } catch (Exception ex) {
                }
                AT = yAT + oAT;
                int yAU = 0;
                int oAU = 0;
                try {
                    yAU = y.getIntValue("AU");
                } catch (Exception ex) {
                }
                try {
                    oAU = one.getIntValue("AU");
                } catch (Exception ex) {
                }
                AU = yAU + oAU;
                int yAV = 0;
                int oAV = 0;
                try {
                    yAV = y.getIntValue("AV");
                } catch (Exception ex) {
                }
                try {
                    oAV = one.getIntValue("AV");
                } catch (Exception ex) {
                }
                AV = yAV + oAV;
                int yAW = 0;
                int oAW = 0;
                try {
                    yAW = y.getIntValue("AW");
                } catch (Exception ex) {
                }
                try {
                    oAW = one.getIntValue("AW");
                } catch (Exception ex) {
                }
                AW = yAW + oAW;
                int yAX = 0;
                int oAX = 0;
                try {
                    yAX = y.getIntValue("AX");
                } catch (Exception ex) {
                }
                try {
                    oAX = one.getIntValue("AX");
                } catch (Exception ex) {
                }
                AX = yAX + oAX;
                int yAY = 0;
                int oAY = 0;
                try {
                    yAY = y.getIntValue("AY");
                } catch (Exception ex) {
                }
                try {
                    oAY = one.getIntValue("AY");
                } catch (Exception ex) {
                }
                AY = yAY + oAY;
                int yBB = 0;
                int oBB = 0;
                try {
                    yBB = y.getIntValue("BB");
                } catch (Exception ex) {
                }
                try {
                    oBB = one.getIntValue("BB");
                } catch (Exception ex) {
                }
                BB = yBB + oBB;
                int yBC = 0;
                int oBC = 0;
                try {
                    yBC = y.getIntValue("BC");
                } catch (Exception ex) {
                }
                try {
                    oBC = one.getIntValue("BC");
                } catch (Exception ex) {
                }
                BC = yBC + oBC;
                int yBD = 0;
                int oBD = 0;
                try {
                    yBD = y.getIntValue("BD");
                } catch (Exception ex) {
                }
                try {
                    oBD = one.getIntValue("BD");
                } catch (Exception ex) {
                }
                BD = yBD + oBD;
                int yBE = 0;
                int oBE = 0;
                try {
                    yBE = y.getIntValue("BE");
                } catch (Exception ex) {
                }
                try {
                    oBE = one.getIntValue("BE");
                } catch (Exception ex) {
                }
                BE = yBE + oBE;
                int yBF = 0;
                int oBF = 0;
                try {
                    yBF = y.getIntValue("BF");
                } catch (Exception ex) {
                }
                try {
                    oBF = one.getIntValue("BF");
                } catch (Exception ex) {
                }
                BF = yBF + oBF;
                int yBG = 0;
                int oBG = 0;
                try {
                    yBG = y.getIntValue("BG");
                } catch (Exception ex) {
                }
                try {
                    oBG = one.getIntValue("BG");
                } catch (Exception ex) {
                }
                BG = yBG + oBG;
                int yAZ = 0;
                int oAZ = 0;
                try {
                    yAZ = y.getIntValue("AZ");
                } catch (Exception ex) {
                }
                try {
                    oAZ = one.getIntValue("AZ");
                } catch (Exception ex) {
                }
                AZ = yAZ + oAZ;
                int yBA = 0;
                int oBA = 0;
                try {
                    yBA = y.getIntValue("BA");
                } catch (Exception ex) {
                }
                try {
                    oBA = one.getIntValue("BA");
                } catch (Exception ex) {
                }
                BA = yBA + oBA;
                int yTOT = 0;
                int oTOT = 0;
                try {
                    yTOT = y.getIntValue("TOT");
                } catch (Exception ex) {
                }
                try {
                    oTOT = one.getIntValue("TOT");
                } catch (Exception ex) {
                }
                TOT = yTOT + oTOT;

                int yBI = 0;
                int oBI = 0;
                try {
                    yBI = y.getIntValue("BI");
                } catch (Exception ex) {
                }
                try {
                    oBI = one.getIntValue("BI");
                } catch (Exception ex) {
                }
                BI = yBI + oBI;
                int yBJ = 0;
                int oBJ = 0;
                try {
                    yBJ = y.getIntValue("BJ");
                } catch (Exception ex) {
                }
                try {
                    oBJ = one.getIntValue("BJ");
                } catch (Exception ex) {
                }
                BJ = yBJ + oBJ;
                JSONObject det = new JSONObject();
                det.put("BI", BI);
                det.put("BJ", BJ);
                det.put("B", B);
                det.put("C", C);
                det.put("D", D);
                det.put("E", E);
                det.put("F", F);
                det.put("G", G);
                det.put("H", H);
                det.put("I", I);
                det.put("J", J);
                det.put("K", K);
                det.put("L", L);
                det.put("M", M);
                det.put("N", N);
                det.put("O", O);
                det.put("P", P);
                det.put("Q", Q);
                det.put("R", R);
                det.put("S", S);
                det.put("T", T);
                det.put("U", U);
                det.put("V", V);
                det.put("W", W);
                det.put("X", X);
                det.put("Y", Y);
                det.put("Z", Z);
                det.put("AA", AA);
                det.put("AB", AB);
                det.put("AC", AC);
                det.put("AD", AD);
                det.put("AE", AE);
                det.put("AF", AF);
                det.put("AG", AG);
                det.put("AH", AH);
                det.put("AI", AI);
                det.put("AJ", AJ);
                det.put("AK", AK);
                det.put("AL", AL);
                det.put("AM", AM);
                det.put("AN", AN);
                det.put("AO", AO);
                det.put("AP", AP);
                det.put("AQ", AQ);
                det.put("AR", AR);
                det.put("AS", AS);
                det.put("AT", AT);
                det.put("AU", AU);
                det.put("AV", AV);
                det.put("AW", AW);
                det.put("AX", AX);
                det.put("AY", AY);
                det.put("BB", BB);
                det.put("BC", BC);
                det.put("BD", BD);
                det.put("BE", BE);
                det.put("BF", BF);
                det.put("BG", BG);
                det.put("AZ", AZ);
                det.put("BA", BA);
                det.put("TOT", TOT);

                dets.put(czr, det);

            } else {
                dets.put(czr, one);
            }


        }

        for (Map.Entry<String, JSONObject> one : dets.entrySet()) {
            JSONObject ooo = one.getValue();
            ooo.remove("police_name");
            String key = one.getKey();
            JSONObject res = jsons.get(key);
            res.put("result", ooo);
            back.add(res);
        }
        return back;


    }

    private static List<JSONObject> AddResults_Org(List<JSONObject> list) {
        HashMap<String, JSONObject> dets = new HashMap<>();

        HashMap<String, JSONObject> jsons = new HashMap<>();
        List<JSONObject> back = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            JSONObject ones = list.get(i);
            ones.put("police_id", "");
            String org = "";
            try {
                org = ones.getString("org").substring(0, 8) + "0000";
            } catch (Exception EX) {

            }
            ones.put("org", org);
            JSONObject one = ones.getJSONObject("result");

            if (!jsons.containsKey(org)) {
                jsons.put(org, ones);
            }

            if (dets.containsKey(org)) {
                int B = 0;
                int C = 0;
                int D = 0;
                int E = 0;
                int F = 0;
                int G = 0;
                int H = 0;
                int I = 0;
                int J = 0;
                int K = 0;
                int L = 0;
                int M = 0;
                int N = 0;
                int O = 0;
                int P = 0;
                int Q = 0;
                int R = 0;
                int S = 0;
                int T = 0;
                int U = 0;
                int V = 0;
                int W = 0;
                int X = 0;
                int Y = 0;
                int Z = 0;
                int AA = 0;
                int AB = 0;
                int AC = 0;
                int AD = 0;
                int AE = 0;
                int AF = 0;
                int AG = 0;
                int AH = 0;
                int AI = 0;
                int AJ = 0;
                int AK = 0;
                int AL = 0;
                int AM = 0;
                int AN = 0;
                int AO = 0;
                int AP = 0;
                int AQ = 0;
                int AR = 0;
                int AS = 0;
                int AT = 0;
                int AU = 0;
                int AV = 0;
                int AW = 0;
                int AX = 0;
                int AY = 0;
                int BB = 0;
                int BC = 0;
                int BD = 0;
                int BE = 0;
                int BF = 0;
                int BG = 0;
                int AZ = 0;
                int BA = 0;
                int TOT = 0;
                int BI = 0;
                int BJ = 0;


                JSONObject y = dets.get(org);
                int yB = 0;
                int oB = 0;
                try {
                    yB = y.getIntValue("B");
                } catch (Exception ex) {
                }
                try {
                    oB = one.getIntValue("B");
                } catch (Exception ex) {
                }
                B = yB + oB;
                int yC = 0;
                int oC = 0;
                try {
                    yC = y.getIntValue("C");
                } catch (Exception ex) {
                }
                try {
                    oC = one.getIntValue("C");
                } catch (Exception ex) {
                }
                C = yC + oC;
                int yD = 0;
                int oD = 0;
                try {
                    yD = y.getIntValue("D");
                } catch (Exception ex) {
                }
                try {
                    oD = one.getIntValue("D");
                } catch (Exception ex) {
                }
                D = yD + oD;
                int yE = 0;
                int oE = 0;
                try {
                    yE = y.getIntValue("E");
                } catch (Exception ex) {
                }
                try {
                    oE = one.getIntValue("E");
                } catch (Exception ex) {
                }
                E = yE + oE;
                int yF = 0;
                int oF = 0;
                try {
                    yF = y.getIntValue("F");
                } catch (Exception ex) {
                }
                try {
                    oF = one.getIntValue("F");
                } catch (Exception ex) {
                }
                F = yF + oF;
                int yG = 0;
                int oG = 0;
                try {
                    yG = y.getIntValue("G");
                } catch (Exception ex) {
                }
                try {
                    oG = one.getIntValue("G");
                } catch (Exception ex) {
                }
                G = yG + oG;
                int yH = 0;
                int oH = 0;
                try {
                    yH = y.getIntValue("H");
                } catch (Exception ex) {
                }
                try {
                    oH = one.getIntValue("H");
                } catch (Exception ex) {
                }
                H = yH + oH;
                int yI = 0;
                int oI = 0;
                try {
                    yI = y.getIntValue("I");
                } catch (Exception ex) {
                }
                try {
                    oI = one.getIntValue("I");
                } catch (Exception ex) {
                }
                I = yI + oI;
                int yJ = 0;
                int oJ = 0;
                try {
                    yJ = y.getIntValue("J");
                } catch (Exception ex) {
                }
                try {
                    oJ = one.getIntValue("J");
                } catch (Exception ex) {
                }
                J = yJ + oJ;
                int yK = 0;
                int oK = 0;
                try {
                    yK = y.getIntValue("K");
                } catch (Exception ex) {
                }
                try {
                    oK = one.getIntValue("K");
                } catch (Exception ex) {
                }
                K = yK + oK;
                int yL = 0;
                int oL = 0;
                try {
                    yL = y.getIntValue("L");
                } catch (Exception ex) {
                }
                try {
                    oL = one.getIntValue("L");
                } catch (Exception ex) {
                }
                L = yL + oL;
                int yM = 0;
                int oM = 0;
                try {
                    yM = y.getIntValue("M");
                } catch (Exception ex) {
                }
                try {
                    oM = one.getIntValue("M");
                } catch (Exception ex) {
                }
                M = yM + oM;
                int yN = 0;
                int oN = 0;
                try {
                    yN = y.getIntValue("N");
                } catch (Exception ex) {
                }
                try {
                    oN = one.getIntValue("N");
                } catch (Exception ex) {
                }
                N = yN + oN;
                int yO = 0;
                int oO = 0;
                try {
                    yO = y.getIntValue("O");
                } catch (Exception ex) {
                }
                try {
                    oO = one.getIntValue("O");
                } catch (Exception ex) {
                }
                O = yO + oO;
                int yP = 0;
                int oP = 0;
                try {
                    yP = y.getIntValue("P");
                } catch (Exception ex) {
                }
                try {
                    oP = one.getIntValue("P");
                } catch (Exception ex) {
                }
                P = yP + oP;
                int yQ = 0;
                int oQ = 0;
                try {
                    yQ = y.getIntValue("Q");
                } catch (Exception ex) {
                }
                try {
                    oQ = one.getIntValue("Q");
                } catch (Exception ex) {
                }
                Q = yQ + oQ;
                int yR = 0;
                int oR = 0;
                try {
                    yR = y.getIntValue("R");
                } catch (Exception ex) {
                }
                try {
                    oR = one.getIntValue("R");
                } catch (Exception ex) {
                }
                R = yR + oR;
                int yS = 0;
                int oS = 0;
                try {
                    yS = y.getIntValue("S");
                } catch (Exception ex) {
                }
                try {
                    oS = one.getIntValue("S");
                } catch (Exception ex) {
                }
                S = yS + oS;
                int yT = 0;
                int oT = 0;
                try {
                    yT = y.getIntValue("T");
                } catch (Exception ex) {
                }
                try {
                    oT = one.getIntValue("T");
                } catch (Exception ex) {
                }
                T = yT + oT;
                int yU = 0;
                int oU = 0;
                try {
                    yU = y.getIntValue("U");
                } catch (Exception ex) {
                }
                try {
                    oU = one.getIntValue("U");
                } catch (Exception ex) {
                }
                U = yU + oU;
                int yV = 0;
                int oV = 0;
                try {
                    yV = y.getIntValue("V");
                } catch (Exception ex) {
                }
                try {
                    oV = one.getIntValue("V");
                } catch (Exception ex) {
                }
                V = yV + oV;
                int yW = 0;
                int oW = 0;
                try {
                    yW = y.getIntValue("W");
                } catch (Exception ex) {
                }
                try {
                    oW = one.getIntValue("W");
                } catch (Exception ex) {
                }
                W = yW + oW;
                int yX = 0;
                int oX = 0;
                try {
                    yX = y.getIntValue("X");
                } catch (Exception ex) {
                }
                try {
                    oX = one.getIntValue("X");
                } catch (Exception ex) {
                }
                X = yX + oX;
                int yY = 0;
                int oY = 0;
                try {
                    yY = y.getIntValue("Y");
                } catch (Exception ex) {
                }
                try {
                    oY = one.getIntValue("Y");
                } catch (Exception ex) {
                }
                Y = yY + oY;
                int yZ = 0;
                int oZ = 0;
                try {
                    yZ = y.getIntValue("Z");
                } catch (Exception ex) {
                }
                try {
                    oZ = one.getIntValue("Z");
                } catch (Exception ex) {
                }
                Z = yZ + oZ;
                int yAA = 0;
                int oAA = 0;
                try {
                    yAA = y.getIntValue("AA");
                } catch (Exception ex) {
                }
                try {
                    oAA = one.getIntValue("AA");
                } catch (Exception ex) {
                }
                AA = yAA + oAA;
                int yAB = 0;
                int oAB = 0;
                try {
                    yAB = y.getIntValue("AB");
                } catch (Exception ex) {
                }
                try {
                    oAB = one.getIntValue("AB");
                } catch (Exception ex) {
                }
                AB = yAB + oAB;
                int yAC = 0;
                int oAC = 0;
                try {
                    yAC = y.getIntValue("AC");
                } catch (Exception ex) {
                }
                try {
                    oAC = one.getIntValue("AC");
                } catch (Exception ex) {
                }
                AC = yAC + oAC;
                int yAD = 0;
                int oAD = 0;
                try {
                    yAD = y.getIntValue("AD");
                } catch (Exception ex) {
                }
                try {
                    oAD = one.getIntValue("AD");
                } catch (Exception ex) {
                }
                AD = yAD + oAD;
                int yAE = 0;
                int oAE = 0;
                try {
                    yAE = y.getIntValue("AE");
                } catch (Exception ex) {
                }
                try {
                    oAE = one.getIntValue("AE");
                } catch (Exception ex) {
                }
                AE = yAE + oAE;
                int yAF = 0;
                int oAF = 0;
                try {
                    yAF = y.getIntValue("AF");
                } catch (Exception ex) {
                }
                try {
                    oAF = one.getIntValue("AF");
                } catch (Exception ex) {
                }
                AF = yAF + oAF;
                int yAG = 0;
                int oAG = 0;
                try {
                    yAG = y.getIntValue("AG");
                } catch (Exception ex) {
                }
                try {
                    oAG = one.getIntValue("AG");
                } catch (Exception ex) {
                }
                AG = yAG + oAG;
                int yAH = 0;
                int oAH = 0;
                try {
                    yAH = y.getIntValue("AH");
                } catch (Exception ex) {
                }
                try {
                    oAH = one.getIntValue("AH");
                } catch (Exception ex) {
                }
                AH = yAH + oAH;
                int yAI = 0;
                int oAI = 0;
                try {
                    yAI = y.getIntValue("AI");
                } catch (Exception ex) {
                }
                try {
                    oAI = one.getIntValue("AI");
                } catch (Exception ex) {
                }
                AI = yAI + oAI;
                int yAJ = 0;
                int oAJ = 0;
                try {
                    yAJ = y.getIntValue("AJ");
                } catch (Exception ex) {
                }
                try {
                    oAJ = one.getIntValue("AJ");
                } catch (Exception ex) {
                }
                AJ = yAJ + oAJ;
                int yAK = 0;
                int oAK = 0;
                try {
                    yAK = y.getIntValue("AK");
                } catch (Exception ex) {
                }
                try {
                    oAK = one.getIntValue("AK");
                } catch (Exception ex) {
                }
                AK = yAK + oAK;
                int yAL = 0;
                int oAL = 0;
                try {
                    yAL = y.getIntValue("AL");
                } catch (Exception ex) {
                }
                try {
                    oAL = one.getIntValue("AL");
                } catch (Exception ex) {
                }
                AL = yAL + oAL;
                int yAM = 0;
                int oAM = 0;
                try {
                    yAM = y.getIntValue("AM");
                } catch (Exception ex) {
                }
                try {
                    oAM = one.getIntValue("AM");
                } catch (Exception ex) {
                }
                AM = yAM + oAM;
                int yAN = 0;
                int oAN = 0;
                try {
                    yAN = y.getIntValue("AN");
                } catch (Exception ex) {
                }
                try {
                    oAN = one.getIntValue("AN");
                } catch (Exception ex) {
                }
                AN = yAN + oAN;
                int yAO = 0;
                int oAO = 0;
                try {
                    yAO = y.getIntValue("AO");
                } catch (Exception ex) {
                }
                try {
                    oAO = one.getIntValue("AO");
                } catch (Exception ex) {
                }
                AO = yAO + oAO;
                int yAP = 0;
                int oAP = 0;
                try {
                    yAP = y.getIntValue("AP");
                } catch (Exception ex) {
                }
                try {
                    oAP = one.getIntValue("AP");
                } catch (Exception ex) {
                }
                AP = yAP + oAP;
                int yAQ = 0;
                int oAQ = 0;
                try {
                    yAQ = y.getIntValue("AQ");
                } catch (Exception ex) {
                }
                try {
                    oAQ = one.getIntValue("AQ");
                } catch (Exception ex) {
                }
                AQ = yAQ + oAQ;
                int yAR = 0;
                int oAR = 0;
                try {
                    yAR = y.getIntValue("AR");
                } catch (Exception ex) {
                }
                try {
                    oAR = one.getIntValue("AR");
                } catch (Exception ex) {
                }
                AR = yAR + oAR;
                int yAS = 0;
                int oAS = 0;
                try {
                    yAS = y.getIntValue("AS");
                } catch (Exception ex) {
                }
                try {
                    oAS = one.getIntValue("AS");
                } catch (Exception ex) {
                }
                AS = yAS + oAS;
                int yAT = 0;
                int oAT = 0;
                try {
                    yAT = y.getIntValue("AT");
                } catch (Exception ex) {
                }
                try {
                    oAT = one.getIntValue("AT");
                } catch (Exception ex) {
                }
                AT = yAT + oAT;
                int yAU = 0;
                int oAU = 0;
                try {
                    yAU = y.getIntValue("AU");
                } catch (Exception ex) {
                }
                try {
                    oAU = one.getIntValue("AU");
                } catch (Exception ex) {
                }
                AU = yAU + oAU;
                int yAV = 0;
                int oAV = 0;
                try {
                    yAV = y.getIntValue("AV");
                } catch (Exception ex) {
                }
                try {
                    oAV = one.getIntValue("AV");
                } catch (Exception ex) {
                }
                AV = yAV + oAV;
                int yAW = 0;
                int oAW = 0;
                try {
                    yAW = y.getIntValue("AW");
                } catch (Exception ex) {
                }
                try {
                    oAW = one.getIntValue("AW");
                } catch (Exception ex) {
                }
                AW = yAW + oAW;
                int yAX = 0;
                int oAX = 0;
                try {
                    yAX = y.getIntValue("AX");
                } catch (Exception ex) {
                }
                try {
                    oAX = one.getIntValue("AX");
                } catch (Exception ex) {
                }
                AX = yAX + oAX;
                int yAY = 0;
                int oAY = 0;
                try {
                    yAY = y.getIntValue("AY");
                } catch (Exception ex) {
                }
                try {
                    oAY = one.getIntValue("AY");
                } catch (Exception ex) {
                }
                AY = yAY + oAY;
                int yBB = 0;
                int oBB = 0;
                try {
                    yBB = y.getIntValue("BB");
                } catch (Exception ex) {
                }
                try {
                    oBB = one.getIntValue("BB");
                } catch (Exception ex) {
                }
                BB = yBB + oBB;
                int yBC = 0;
                int oBC = 0;
                try {
                    yBC = y.getIntValue("BC");
                } catch (Exception ex) {
                }
                try {
                    oBC = one.getIntValue("BC");
                } catch (Exception ex) {
                }
                BC = yBC + oBC;
                int yBD = 0;
                int oBD = 0;
                try {
                    yBD = y.getIntValue("BD");
                } catch (Exception ex) {
                }
                try {
                    oBD = one.getIntValue("BD");
                } catch (Exception ex) {
                }
                BD = yBD + oBD;
                int yBE = 0;
                int oBE = 0;
                try {
                    yBE = y.getIntValue("BE");
                } catch (Exception ex) {
                }
                try {
                    oBE = one.getIntValue("BE");
                } catch (Exception ex) {
                }
                BE = yBE + oBE;
                int yBF = 0;
                int oBF = 0;
                try {
                    yBF = y.getIntValue("BF");
                } catch (Exception ex) {
                }
                try {
                    oBF = one.getIntValue("BF");
                } catch (Exception ex) {
                }
                BF = yBF + oBF;
                int yBG = 0;
                int oBG = 0;
                try {
                    yBG = y.getIntValue("BG");
                } catch (Exception ex) {
                }
                try {
                    oBG = one.getIntValue("BG");
                } catch (Exception ex) {
                }
                BG = yBG + oBG;
                int yAZ = 0;
                int oAZ = 0;
                try {
                    yAZ = y.getIntValue("AZ");
                } catch (Exception ex) {
                }
                try {
                    oAZ = one.getIntValue("AZ");
                } catch (Exception ex) {
                }
                AZ = yAZ + oAZ;
                int yBA = 0;
                int oBA = 0;
                try {
                    yBA = y.getIntValue("BA");
                } catch (Exception ex) {
                }
                try {
                    oBA = one.getIntValue("BA");
                } catch (Exception ex) {
                }
                BA = yBA + oBA;
                int yTOT = 0;
                int oTOT = 0;
                try {
                    yTOT = y.getIntValue("TOT");
                } catch (Exception ex) {
                }
                try {
                    oTOT = one.getIntValue("TOT");
                } catch (Exception ex) {
                }
                TOT = yTOT + oTOT;

                int yBI = 0;
                int oBI = 0;
                try {
                    yBI = y.getIntValue("BI");
                } catch (Exception ex) {
                }
                try {
                    oBI = one.getIntValue("BI");
                } catch (Exception ex) {
                }
                BI = yBI + oBI;
                int yBJ = 0;
                int oBJ = 0;
                try {
                    yBJ = y.getIntValue("BJ");
                } catch (Exception ex) {
                }
                try {
                    oBJ = one.getIntValue("BJ");
                } catch (Exception ex) {
                }
                BJ = yBJ + oBJ;
                JSONObject det = new JSONObject();
                det.put("BI", BI);
                det.put("BJ", BJ);
                det.put("B", B);
                det.put("C", C);
                det.put("D", D);
                det.put("E", E);
                det.put("F", F);
                det.put("G", G);
                det.put("H", H);
                det.put("I", I);
                det.put("J", J);
                det.put("K", K);
                det.put("L", L);
                det.put("M", M);
                det.put("N", N);
                det.put("O", O);
                det.put("P", P);
                det.put("Q", Q);
                det.put("R", R);
                det.put("S", S);
                det.put("T", T);
                det.put("U", U);
                det.put("V", V);
                det.put("W", W);
                det.put("X", X);
                det.put("Y", Y);
                det.put("Z", Z);
                det.put("AA", AA);
                det.put("AB", AB);
                det.put("AC", AC);
                det.put("AD", AD);
                det.put("AE", AE);
                det.put("AF", AF);
                det.put("AG", AG);
                det.put("AH", AH);
                det.put("AI", AI);
                det.put("AJ", AJ);
                det.put("AK", AK);
                det.put("AL", AL);
                det.put("AM", AM);
                det.put("AN", AN);
                det.put("AO", AO);
                det.put("AP", AP);
                det.put("AQ", AQ);
                det.put("AR", AR);
                det.put("AS", AS);
                det.put("AT", AT);
                det.put("AU", AU);
                det.put("AV", AV);
                det.put("AW", AW);
                det.put("AX", AX);
                det.put("AY", AY);
                det.put("BB", BB);
                det.put("BC", BC);
                det.put("BD", BD);
                det.put("BE", BE);
                det.put("BF", BF);
                det.put("BG", BG);
                det.put("AZ", AZ);
                det.put("BA", BA);
                det.put("TOT", TOT);

                dets.put(org, det);

            } else {
                dets.put(org, one);
            }

        }
        for (Map.Entry<String, JSONObject> one : dets.entrySet()) {
            JSONObject ooo = one.getValue();
            ooo.remove("police_name");
            String key = one.getKey();
            JSONObject res = jsons.get(key);
            try {
                res.put("org_name", RIUtil.dicts.get(key).getString("dict_name"));
            } catch (Exception ex) {
                if (key.equals("000000000000")) {
                    res.put("org_name", "合计");
                }
            }

            res.put("result", ooo);
            System.out.println(res);
            back.add(res);
        }
        return back;


    }

    private static int CreateZXQCExcel(List<JSONObject> list, String circle_name, List<JSONObject> orglist) {
        int file_id = -1;
        try {

            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) +
                            "/";

            File oaupload1 = new File(TNOAConf.get("file", "img_path") + filePath);
            // logger.warn(SciConf.get("file", "upload_pic") + File.separator + filePath);
            //  这路径后面若需要能改成关联nas的路径
            if (!oaupload1.isDirectory()) {
                oaupload1.mkdirs();
            }
            // 1、创建关联磁盘文件

            String fileName = circle_name + "_" + System.currentTimeMillis() + ".xls";
            File excel = new File(TNOAConf.get("file", "img_path") + filePath + fileName);
            // 1、创建关联磁盘文件

            // 创建一个excel
            WritableWorkbook workbook = Workbook.createWorkbook(excel);

            // 2、创建一个Excel的工作表sheet
            WritableSheet sheet = workbook.createSheet("参战人员统计", 0);


            // 3、样式设置
            // 3.1、文字设置
            // 一种为bold加粗，一种为noBold不加粗。具体要设置其他样式可以点开WritableFont类参考
            WritableFont bold = new WritableFont(WritableFont.createFont("微软雅黑"), 12, WritableFont.BOLD);
            WritableFont noBold = new WritableFont(WritableFont.createFont("微软雅黑"), 12, WritableFont.NO_BOLD);

            // 3.2、设置标题样式
            WritableCellFormat titleFormate = new WritableCellFormat(bold);
            // 设置单元格中的内容水平方向居中、垂直方向居中、背景填充天蓝色、设置边框
            titleFormate.setAlignment(jxl.format.Alignment.CENTRE);
            titleFormate.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE);
            // titleFormate.setBackground(Colour.SKY_BLUE);
            titleFormate.setBorder(Border.ALL, BorderLineStyle.THIN);

            // 3.3设置正文内容样式，单元格样式控制对象
            WritableCellFormat textFormat = new WritableCellFormat(noBold);
            // 单元格中的内容水平方向居中、垂直方向居中、设置边框
            textFormat.setAlignment(Alignment.CENTRE);
            textFormat.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE);
            textFormat.setBorder(Border.ALL, BorderLineStyle.THIN);

            // 3.4、窗口冻结第一行
            sheet.getSettings().setVerticalFreeze(4);
            sheet.getSettings().setHorizontalFreeze(1);// 冻结 2列两行

            // 3.5、设置行高--第一行标题行
            // sheet.setRowView(0,500);

            // 3.6、设置列宽

            for (int i = 0; i < 59; i++) {
                sheet.setColumnView(0, 8);

            }
            // 3.7、对数据进行分组
            // 进行分组，2-3、5-6、8-9各为一组，并且默认是折叠true方式生成
            /*
             * sheet.setRowGroup(1, 2, true); sheet.setRowGroup(4, 5, true);
             * sheet.setRowGroup(7, 8, true);
             */

            // 4、构造表头
            setSheetHeader(sheet, titleFormate, circle_name);

            // 5、填充表头2
            setSheetData(sheet, textFormat, 1, "执行人");
            // 数据
            setSheetData2(sheet, textFormat, 5, list);

            // 参战单位
            WritableSheet sheet_org = workbook.createSheet("参战单位统计", 0);


            // 3.4、窗口冻结第一行
            sheet_org.getSettings().setVerticalFreeze(4);
            sheet_org.getSettings().setHorizontalFreeze(1);// 冻结 2列两行

            // 3.5、设置行高--第一行标题行
            // sheet_org.setRowView(0,500);

            // 3.6、设置列宽

            for (int i = 0; i < 59; i++) {
                sheet_org.setColumnView(0, 8);

            }
            // 3.7、对数据进行分组
            // 进行分组，2-3、5-6、8-9各为一组，并且默认是折叠true方式生成
            /*
             * sheet_org.setRowGroup(1, 2, true); sheet_org.setRowGroup(4, 5, true);
             * sheet_org.setRowGroup(7, 8, true);
             */

            // 4、构造表头
            setSheetHeader(sheet_org, titleFormate, circle_name);

            // 5、填充表头2
            setSheetData(sheet_org, textFormat, 1, "执行单位");
            // 数据
            setSheetData2_org(sheet_org, textFormat, 5, orglist);


            workbook.write();
            workbook.close();


            InfoModelHelper info = InfoModelPool.getModel();

            try {
                String sql = "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath +
                        "'," + "'" + fileName + "',999)";


                info.update(sql);
                sql = "select * from upload where file_name='" + fileName + "' AND file_path='" + filePath + "'";
                List<JSONObject> result = info.query(sql);

                JSONObject xJsonObject = result.get(0);
                file_id = xJsonObject.getInteger("id");
                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + fileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, TNOAConf.get("file",
                        "img_path") + filePath + fileName);
                logger.warn(obsFileName + "-->" + ret);

                logger.warn("id->" + file_id);
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            } finally {
                InfoModelPool.putModel(info);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }
        return file_id;
    }


    private static void setSheetData2(WritableSheet sheet, WritableCellFormat textFormat, int startRow,
                                      List<JSONObject> list) throws WriteException {


        NumberFormat nf = new NumberFormat("#");
        WritableCellFormat numLab = new WritableCellFormat(nf);
        // 单元格中的内容水平方向居中、垂直方向居中、设置边框
        numLab.setAlignment(Alignment.CENTRE);
        numLab.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE);
        numLab.setBorder(Border.ALL, BorderLineStyle.THIN);
        String orders = "police_name,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,AA,AB,AC,AD,AE,AF,AG,AH,AI,AJ,"
                + "AK,AL,AM,AN,AO,AP,AQ,AR,AS,AT,AU,AV,AW,AX,AY,BB,BC,BD,BE,BF,BG,AZ,BA,BI,BJ,TOT";
        String[] order = orders.split(",");

        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String police_name = one.getString("police_name");
            JSONObject result = one.getJSONObject("result");
            result.put("police_name", police_name);

            for (int o = 0; o < order.length; o++) {
                try {
                    String val = result.getString(order[o]);
                    if (order[o].equals("police_name")) {
                        Label label_20 = new Label(o, (4 + i), val, textFormat);
                        sheet.addCell(label_20);
                    } else {
                        double v = Double.parseDouble(val);
                        jxl.write.Number nLab = new jxl.write.Number(o, (4 + i), v, numLab);
                        sheet.addCell(nLab);
                    }
                } catch (Exception e) {
                    jxl.write.Number nLab = new jxl.write.Number(o, (4 + i), 0, numLab);
                    sheet.addCell(nLab);
                }
            }

        }

    }


    private static void setSheetData2_org(WritableSheet sheet, WritableCellFormat textFormat, int startRow,
                                          List<JSONObject> list) throws WriteException {


        NumberFormat nf = new NumberFormat("#");
        WritableCellFormat numLab = new WritableCellFormat(nf);
        // 单元格中的内容水平方向居中、垂直方向居中、设置边框
        numLab.setAlignment(Alignment.CENTRE);
        numLab.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE);
        numLab.setBorder(Border.ALL, BorderLineStyle.THIN);
        String orders =
                "org_name,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,AA,AB,AC,AD,AE,AF,AG,AH,AI,AJ," + "AK,AL," +
                        "AM,AN," + "AO,AP,AQ,AR,AS,AT,AU,AV,AW,AX,AY,BB,BC,BD,BE,BF,BG,AZ,BA,BI,BJ,TOT";
        String[] order = orders.split(",");

        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String org = "";
            String org_name = "";
            try {
                org = one.getString("org").substring(0, 8) + "0000";

                if (org.equals("000000000000")) {
                    org_name = "合计";
                } else {
                    org_name = RIUtil.dicts.get(org).getString("dict_name") + "," + org.substring(0, 6);
                }
            } catch (Exception ex) {

            }
            JSONObject result = one.getJSONObject("result");
            System.out.println();
            result.put("org_name", org_name);

            for (int o = 0; o < order.length; o++) {
                try {
                    String val = result.getString(order[o]);
                    if (order[o].equals("org_name")) {
                        Label label_20 = new Label(o, (4 + i), val, textFormat);
                        sheet.addCell(label_20);
                    } else {
                        double v = Double.parseDouble(val);
                        jxl.write.Number nLab = new jxl.write.Number(o, (4 + i), v, numLab);
                        sheet.addCell(nLab);
                    }
                } catch (Exception e) {
                    jxl.write.Number nLab = new jxl.write.Number(o, (4 + i), 0, numLab);
                    sheet.addCell(nLab);
                }
            }

        }

    }

    /**
     * 设置sheet的第一行标题样式和内容
     *
     * @param sheet        工作表
     * @param titleFormate 填充样式
     * @param circle_name
     * @throws WriteException 异常
     */
    private static void setSheetHeader(WritableSheet sheet, WritableCellFormat titleFormate, String circle_name) throws WriteException {
        // 构造表头
        // mergeCells(0, 0, 0, 0) 表示不合并； sheet.mergeCells(1,0,2,0)表示第2列和第3列合并成一列
        // Label label_20 = new Label(2, 0, "描述", cellFormat); 前面的数字表示第几列，第几行
        // 4.1、创建表数据

        // 4.2、合并单元格

        sheet.mergeCells(0, 0, 59, 0);
        Label label_20 = new Label(0, 0, circle_name + "专项清查战果统计表", titleFormate);
        sheet.addCell(label_20);

    }

    /**
     * 填充数据到excel
     *
     * @param sheet      工作表
     * @param textFormat 填充样式
     * @param startRow   填充行数索引
     * @throws WriteException 异常
     */
    private static void setSheetData(WritableSheet sheet, WritableCellFormat textFormat, int startRow, String head) throws WriteException {

        sheet.mergeCells(0, 1, 0, 3);
        Label label_20 = new Label(0, 1, head, textFormat);
        sheet.addCell(label_20);

        sheet.mergeCells(1, 1, 3, 1);
        Label label_1 = new Label(1, 1, "入户走访", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(1, 2, 1, 3);
        label_1 = new Label(1, 2, "上门数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(2, 2, 2, 3);
        label_1 = new Label(2, 2, "见面数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(3, 2, 3, 3);
        label_1 = new Label(3, 2, "宣传数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(4, 1, 6, 1);
        label_1 = new Label(4, 1, "流动人口", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(4, 2, 4, 3);
        label_1 = new Label(4, 2, "采集数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(5, 2, 5, 3);
        label_1 = new Label(5, 2, "更新数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(6, 2, 6, 3);
        label_1 = new Label(6, 2, "注销数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(7, 1, 9, 1);
        label_1 = new Label(7, 1, "寄住人口", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(7, 2, 7, 3);
        label_1 = new Label(7, 2, "采集数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(8, 2, 8, 3);
        label_1 = new Label(8, 2, "更新数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(9, 2, 9, 3);
        label_1 = new Label(9, 2, "注销数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(10, 1, 16, 1);
        label_1 = new Label(10, 1, "重点人员管控", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(10, 2, 10, 3);
        label_1 = new Label(10, 2, "新列管数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(11, 2, 11, 3);
        label_1 = new Label(11, 2, "撤管数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(12, 2, 16, 2);
        label_1 = new Label(12, 2, "落实管控措施数", textFormat);
        sheet.addCell(label_1);

        // 精神障碍患者 个人极端 重点上访 涉毒 刑事前科
        label_1 = new Label(12, 3, "精神障碍患者", textFormat);
        sheet.addCell(label_1);
        label_1 = new Label(13, 3, "个人极端", textFormat);
        sheet.addCell(label_1);
        label_1 = new Label(14, 3, "重点上访", textFormat);
        sheet.addCell(label_1);
        label_1 = new Label(15, 3, "涉毒", textFormat);
        sheet.addCell(label_1);
        label_1 = new Label(16, 3, "刑事前科", textFormat);
        sheet.addCell(label_1);

        // 实有房屋

        sheet.mergeCells(17, 1, 19, 1);
        label_1 = new Label(17, 1, "实有房屋", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(17, 2, 17, 3);
        label_1 = new Label(17, 2, "新增数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(18, 2, 18, 3);
        label_1 = new Label(18, 2, "更新数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(19, 2, 19, 3);
        label_1 = new Label(19, 2, "注销数", textFormat);
        sheet.addCell(label_1);

        // 出租房屋
        sheet.mergeCells(20, 1, 22, 1);
        label_1 = new Label(20, 1, "出租房屋", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(20, 2, 20, 3);
        label_1 = new Label(20, 2, "新增数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(21, 2, 21, 3);
        label_1 = new Label(21, 2, "更新数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(22, 2, 22, 3);
        label_1 = new Label(22, 2, "注销数", textFormat);
        sheet.addCell(label_1);

        // 房屋隐患
        sheet.mergeCells(23, 1, 24, 1);
        label_1 = new Label(23, 1, "房屋隐患", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(23, 2, 23, 3);
        label_1 = new Label(23, 2, "新增数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(24, 2, 24, 3);
        label_1 = new Label(24, 2, "整改数", textFormat);
        sheet.addCell(label_1);

        // 烧烤店
        sheet.mergeCells(25, 1, 26, 2);
        label_1 = new Label(25, 1, "烧烤店", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(25, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(26, 3, "隐患数", textFormat);
        sheet.addCell(label_1);

        // 消防单位检查
        sheet.mergeCells(27, 1, 32, 1);
        label_1 = new Label(27, 1, "消防单位检查", textFormat);
        sheet.addCell(label_1);

        // 三级
        sheet.mergeCells(27, 2, 28, 2);
        label_1 = new Label(27, 2, "三级", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(27, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(28, 3, "隐患数", textFormat);
        sheet.addCell(label_1);

        // 二级
        sheet.mergeCells(29, 2, 30, 2);
        label_1 = new Label(29, 2, "一般", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(29, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(30, 3, "隐患数", textFormat);
        sheet.addCell(label_1);

        // 小单位
        sheet.mergeCells(31, 2, 32, 2);
        label_1 = new Label(31, 2, "小单位", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(31, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(32, 3, "隐患数", textFormat);
        sheet.addCell(label_1);

        // 治安单位检查
        sheet.mergeCells(33, 1, 50, 1);
        label_1 = new Label(33, 1, "治安单位检查", textFormat);
        sheet.addCell(label_1);

        // 寄递业
        sheet.mergeCells(33, 2, 34, 2);
        label_1 = new Label(33, 2, "寄递业", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(33, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(34, 3, "隐患数", textFormat);
        sheet.addCell(label_1);

        // 酒吧

        sheet.mergeCells(35, 2, 36, 2);
        label_1 = new Label(35, 2, "酒吧", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(35, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(36, 3, "隐患数", textFormat);
        sheet.addCell(label_1);

        // 旅馆
        sheet.mergeCells(37, 2, 38, 2);
        label_1 = new Label(37, 2, "旅馆", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(37, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(38, 3, "隐患数", textFormat);
        sheet.addCell(label_1);
        // 典当行
        sheet.mergeCells(39, 2, 40, 2);
        label_1 = new Label(39, 2, "典当行", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(39, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(40, 3, "隐患数", textFormat);
        sheet.addCell(label_1);
        // 洗浴场所
        sheet.mergeCells(41, 2, 42, 2);
        label_1 = new Label(41, 2, "洗浴场所", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(41, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(42, 3, "隐患数", textFormat);
        sheet.addCell(label_1);
        // 歌舞娱乐场所
        sheet.mergeCells(43, 2, 44, 2);
        label_1 = new Label(43, 2, "歌舞娱乐场所", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(43, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(44, 3, "隐患数", textFormat);
        sheet.addCell(label_1);
        // 网约房
        sheet.mergeCells(45, 2, 46, 2);
        label_1 = new Label(45, 2, "网约房", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(45, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(46, 3, "隐患数", textFormat);
        sheet.addCell(label_1);
        // 密室逃脱
        sheet.mergeCells(47, 2, 48, 2);
        label_1 = new Label(47, 2, "密室逃脱", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(47, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(48, 3, "隐患数", textFormat);
        sheet.addCell(label_1);
        // 私人影院
        sheet.mergeCells(49, 2, 50, 2);
        label_1 = new Label(49, 2, "私人影院", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(49, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(50, 3, "隐患数", textFormat);
        sheet.addCell(label_1);
        // 足浴spa
        sheet.mergeCells(51, 2, 52, 2);
        label_1 = new Label(51, 2, "足浴spa", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(51, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(52, 3, "隐患数", textFormat);
        sheet.addCell(label_1);
        // 棋牌室
        sheet.mergeCells(53, 2, 54, 2);
        label_1 = new Label(53, 2, "棋牌室", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(53, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(54, 3, "隐患数", textFormat);
        sheet.addCell(label_1);
        // 网吧

        sheet.mergeCells(55, 2, 56, 2);
        label_1 = new Label(55, 2, "网吧", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(55, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(56, 3, "隐患数", textFormat);
        sheet.addCell(label_1);

        // 二手交易

        sheet.mergeCells(57, 2, 58, 2);
        label_1 = new Label(57, 2, "二手交易店", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(57, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(58, 3, "隐患数", textFormat);
        sheet.addCell(label_1);

        // qita
        sheet.mergeCells(59, 1, 60, 2);
        label_1 = new Label(59, 1, "其他单位检查", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(59, 3, "检查数", textFormat);
        sheet.addCell(label_1);

        label_1 = new Label(60, 3, "隐患数", textFormat);
        sheet.addCell(label_1);

        sheet.mergeCells(61, 1, 61, 3);
        label_20 = new Label(61, 1, "合计", textFormat);
        sheet.addCell(label_20);

    }


    private static JSONObject StaticQQBTaskChange(JSONObject data, InfoModelHelper mysql) {
        JSONObject back = ErrNo.set(0);

        OracleHelper ora = null;
        String unit = "";
        String file_id = "";
        String start_date = "";
        String end_date = "";
        String folder = " and 1=1 ";
        int limit = 5;
        int page = 1;
        int nextC = 1;
        int nextF = 1;
        int isExp = 0;

        JSONObject datas = new JSONObject();
        try {

            ora = new OracleHelper("ora_hl");

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                int type = uone.getInteger("type");
                if (type == 21 || type == 22 || type == 27) {
                    unit = "320400000000";
                    nextC = 1;
                    nextF = 1;
                } else if (type == 23 || type == 24 || type == 28) {
                    unit = unit.substring(0, 6) + "000000";
                    nextC = 0;
                    nextF = 1;
                } else {
                    unit = unit.substring(0, 8) + "0000";
                    nextC = 0;
                    nextF = 0;
                }
            } else {
                unit = "320400000000";
            }
            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date");
                long s = RIUtil.dateToStamp(start_date);
                if (s < System.currentTimeMillis()) {
                    start_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
                    start_date = RIUtil.GetNextDate(start_date, -1);
                }
            } else {
                start_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
                start_date = RIUtil.GetNextDate(start_date, -1);
            }
            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
                long e = RIUtil.dateToStamp(end_date);
                if (e > System.currentTimeMillis()) {
                    end_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
                }
            } else {
                end_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            }

            if (data.containsKey("file_id") && data.getString("file_id").length() > 0) {
                file_id = data.getString("file_id");
                folder = " and FILE_ID='" + file_id + "' ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getInteger("isExp");
            }
            String sql =
                    "select id,dict_name,type from dict  WHERE (id=" + unit + " or father_id='" + unit + "') " + "and"
                            + " " + "isdelete=1 " + " and id != '320400WB9431' and id != '320400WB9432' and id != " +
                            "'320400WB9900' and id != " + "'320400WB990Z' " + " order by static_index,id";
            List<JSONObject> list = mysql.query(sql);
            List<JSONObject> ll = new ArrayList<>();

            JSONObject title = new JSONObject();
            title.put("id", "task_name");
            title.put("dict_name", "任务名称");
            ll.add(title);
            title = new JSONObject();
            title.put("id", "task_detail");
            title.put("dict_name", "任务明细");
            ll.add(title);
            ll.addAll(list);

            datas.put("head", ll);

            // 数据

            sql =
                    "SELECT TASK_NAME FROM QQB_TASK_COUNT WHERE COUNT>0  " + folder + " GROUP BY TASK_NAME OFFSET " + (page - 1) * limit + " ROWS FETCH NEXT " + limit + " ROWS" + " ONLY";
            List<JSONObject> tasks = new ArrayList<>();
            if (page == 1) {

                JSONObject d = new JSONObject();

                d.put("TASK_NAME", "ALL");
                tasks.add(d);
            }
            tasks.addAll(ora.query(sql));


            JSONObject bbs = GetBBSChange(tasks, ora, unit, start_date, end_date, nextC, nextF);

            datas.put("body", bbs.getJSONArray("body"));

            sql = "SELECT count(TASK_NAME) as count FROM QQB_TASK_COUNT WHERE COUNT>0  " + folder + " ORDER BY " +
                    "FILE_ID," + "TASK_NAME";
            int count = ora.query_count(sql);
            datas.put("count", count);

            if (isExp == 1)//导出本页
            {
                file_id = createExcel(datas.getJSONArray("head"), bbs.getJSONArray("exes"), "任务变动概况_" + start_date +
                        "-" + end_date, 3);
                datas.put("file_id", file_id);

            } else if (isExp == 2)//导出全部
            {

                sql = "SELECT TASK_NAME FROM QQB_TASK_COUNT WHERE COUNT>0  " + folder + " GROUP BY TASK_NAME";
                tasks = new ArrayList<>();
                if (page == 1) {

                    JSONObject d = new JSONObject();

                    d.put("TASK_NAME", "ALL");
                    tasks.add(d);
                }
                tasks.addAll(ora.query(sql));
                bbs = GetBBSChange(tasks, ora, unit, start_date, end_date, nextC, nextF);
                file_id = createExcel(datas.getJSONArray("head"), bbs.getJSONArray("exes"), "任务变动概况_" + start_date +
                        "-" + end_date, 3);
                datas.put("file_id", file_id);
            } else {
                datas.put("file_id", -1);
            }

            back.put("data", datas);
            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(446002);
        } finally {

            ora.close();

        }


    }

    private static JSONObject GetBBSChange(List<JSONObject> tasks, OracleHelper ora, String unit, String start_date,
                                           String end_date, int c, int f) {
        JSONArray bodys = new JSONArray();
        JSONArray exes = new JSONArray();
        try {
            for (int i = 0; i < tasks.size(); i++) {
                JSONObject tone = tasks.get(i);


                String task_name = tone.getString("TASK_NAME");

                String sql = "";
                if (unit.contains("320400")) {
                    sql =
                            "select CODE,TOTAL,NAME,FIN,NEAR,DELAY,DELAYFIN,NEXT,TASK_NAME FROM QQB_TASK_STATIC_TOG " +
                                    "WHERE " + "TIME='" + start_date + "' AND (CODE='" + unit + "'  OR (FATHER_ID='" + unit + "' " + "AND" + " " + "NEXT" + "=" + f + " and type=23) or " + "(FATHER_ID='" + unit + "'" + " AND " + "NEXT" + "=0 and " + "type" + "=22)" + ")" + "AND TASK_NAME='" + task_name + "' ORDER BY " + "TYPE,CODE";
                } else {
                    sql = "select CODE,TOTAL,NAME,FIN,NEAR,DELAY,DELAYFIN,TASK_NAME FROM QQB_TASK_STATIC_TOG " +
                            "WHERE" + " " + "TIME='" + start_date + "' AND ((CODE='" + unit + "' and NEXT=" + c + ") "
                            + "OR "
                            + "(FATHER_ID" + "='" + unit + "' " + "AND " + "NEXT=" + f + ")) " + "AND " + "TASK_NAME" + "='" + task_name + "'" + " ORDER BY TYPE,CODE";
                }


                JSONArray childs = new JSONArray();

                List<JSONObject> starts = ora.query(sql);
                HashMap<String, Integer> first = new HashMap<>();
                for (int s = 0; s < starts.size(); s++) {
                    JSONObject sone = starts.get(s);
                    String code = sone.getString("CODE");
                    String tid = sone.getString("TASK_NAME");
                    String next = sone.getString("NEXT");

                    String key = code + "," + tid + "," + next;
                    if (sone.containsKey("TOTAL") && sone.getString("TOTAL").length() > 0) {
                        first.put(key + ",TOTAL", sone.getInteger("TOTAL"));
                    }

                    if (sone.containsKey("FIN") && sone.getString("FIN").length() > 0) {
                        first.put(key + ",FIN", sone.getInteger("FIN"));
                    }

                    if (sone.containsKey("DELAYFIN") && sone.getString("DELAYFIN").length() > 0) {
                        first.put(key + ",DELAYFIN", sone.getInteger("DELAYFIN"));
                    }

                }


                // end_date

                if (unit.contains("320400")) {
                    sql = "select CODE,TOTAL,NAME,FIN,NEAR,DELAY,DELAYFIN,NEXT,TASK_ID FROM QQB_TASK_STATIC_TOG " +
                            "WHERE " + "TIME='" + end_date + "' AND (CODE='" + unit + "'  OR (FATHER_ID='" + unit +
                            "' AND " + "NEXT" + "=" + f + " and type=23) or " + "(FATHER_ID='" + unit + "' AND " +
                            "NEXT=0" + " and " + "type=22)" + ")" + "AND TASK_NAME='" + task_name + "' ORDER BY TYPE,"
                            + "CODE";
                } else {
                    sql = "select CODE,TOTAL,NAME,FIN,NEAR,DELAY,DELAYFIN FROM QQB_TASK_STATIC_TOG " + "WHERE " +
                            "TIME='" + end_date + "' AND ((CODE='" + unit + "' and NEXT=" + c + ") OR " + "(FATHER_ID"
                            + "='" + unit + "' " + "AND " + "NEXT=" + f + ")) " + "AND " + "TASK_NAME" + "='" + task_name + "' ORDER BY TYPE,CODE";
                }

                List<JSONObject> ends = ora.query(sql);

                JSONObject cone = new JSONObject();
                cone.put("task_detail", "接收数");
                for (int d = 0; d < ends.size(); d++) {
                    JSONObject done = ends.get(d);
                    String code = done.getString("CODE");
                    String tid = done.getString("TASK_NAME");
                    String next = done.getString("NEXT");
                    String key = code + "," + tid + "," + next;
                    int total = 0;
                    if (first.containsKey(key + ",TOTAL")) {
                        total = first.get(key + ",TOTAL");
                    }

                    int tot = 0;
                    try {
                        tot = done.getInteger("TOTAL");
                        ;
                    } catch (Exception e) {
                        // TODO: handle exception
                    }

                    if (code.equals("320400000000") && next.equals("0")) {
                        code = code + "_" + next;
                        cone.put(code, tot - total);
                    } else {
                        cone.put(done.getString("CODE"), tot - total);
                    }

                    if (code.contains("3204000000")) {
                        //System.out.println(cone);
                    }
                }


                childs.add(cone);
                if (task_name.equals("ALL")) {
                    cone.put("task_name", "全部任务");
                } else {
                    cone.put("task_name", task_name);
                }
                exes.add(cone);

                cone = new JSONObject();
                cone.put("task_detail", "完成数");
                for (int d = 0; d < ends.size(); d++) {
                    JSONObject done = ends.get(d);
                    String code = done.getString("CODE");
                    String tid = done.getString("TASK_NAME");
                    String next = done.getString("NEXT");
                    String key = code + "," + tid + "," + next;
                    int total = 0;
                    if (first.containsKey(key + ",FIN")) {
                        total = first.get(key + ",FIN");
                    }

                    int tot = 0;
                    try {
                        tot = done.getInteger("FIN");
                        ;
                    } catch (Exception e) {
                        // TODO: handle exception
                    }

                    if (code.equals("320400000000") && next.equals("0")) {
                        code = code + "_" + next;
                        cone.put(code, tot - total);
                    } else {
                        cone.put(done.getString("CODE"), tot - total);
                    }

                }

                childs.add(cone);
                if (task_name.equals("ALL")) {
                    cone.put("task_name", "全部任务");
                } else {
                    cone.put("task_name", task_name);
                }
                exes.add(cone);

                cone = new JSONObject();
                cone.put("task_detail", "逾期完成数");
                for (int d = 0; d < ends.size(); d++) {
                    JSONObject done = ends.get(d);
                    String code = done.getString("CODE");
                    String tid = done.getString("TASK_NAME");
                    String next = done.getString("NEXT");
                    String key = code + "," + tid + "," + next;
                    int total = 0;
                    if (first.containsKey(key + ",DELAYFIN")) {
                        total = first.get(key + ",DELAYFIN");
                    }

                    int tot = 0;
                    try {
                        tot = done.getInteger("DELAYFIN");
                        ;
                    } catch (Exception e) {
                        // TODO: handle exception
                    }

                    if (code.equals("320400000000") && next.equals("0")) {
                        code = code + "_" + next;
                        cone.put(code, tot - total);
                    } else {
                        cone.put(done.getString("CODE"), tot - total);
                    }

                }

                childs.add(cone);
                if (task_name.equals("ALL")) {
                    cone.put("task_name", "全部任务");
                } else {
                    cone.put("task_name", task_name);
                }
                exes.add(cone);
                tone.put("children", childs);

                bodys.add(tone);
            }
        } catch (Exception exception) {
            System.out.println(Lib.getTrace(exception));
        }

        JSONObject back = new JSONObject();
        back.put("body", bodys);
        back.put("exes", exes);
        return back;
    }


    private static JSONObject GetQQBFileList() {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora = null;
        try {
            ora = new OracleHelper("oracle");
            String sql = "select FILE_ID,FILE_NAME from QQB_TASK_COUNT  where COUNT>0 GROUP BY FILE_ID,FILE_NAME " +
                    "order by " + "FILE_ID";
            logger.warn(sql);
            List<JSONObject> list = ora.query(sql);
            if (list.size() > 0) {
                back.put("data", RelaTaskHead(list));

            } else {
                back.put("data", new JSONArray());
            }
            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(446002);
        } finally {
            ora.close();


        }
    }

    private static JSONObject StaticQQBTask(JSONObject data, InfoModelHelper mysql) {
        JSONObject back = ErrNo.set(0);

        OracleHelper ora = null;
        String unit = "";
        String file_id = "";
        String date = "";
        String folder = " and 1=1 ";
        int limit = 3;
        int page = 1;
        int nextC = 1;
        int nextF = 1;
        int isExp = 0;

        JSONObject datas = new JSONObject();
        try {

            ora = new OracleHelper("ora_hl");

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                int type = uone.getInteger("type");
                if (type == 21 || type == 22 || type == 27) {
                    unit = "320400000000";
                    nextC = 1;
                    nextF = 1;
                } else if (type == 23 || type == 24 || type == 28) {
                    unit = unit.substring(0, 6) + "000000";
                    nextC = 0;
                    nextF = 1;
                } else {
                    unit = unit.substring(0, 8) + "0000";
                    nextC = 1;
                    nextF = 0;
                }
            } else {
                unit = "320400000000";
            }
            if (data.containsKey("date") && data.getString("date").length() > 0) {
                date = data.getString("date");
            } else {
                date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            }

            if (data.containsKey("file_id") && data.getString("file_id").length() > 0) {
                file_id = data.getString("file_id");
                folder = " and FILE_ID='" + file_id + "' ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getInteger("isExp");
            }
            String sql =
                    "select id,dict_name,type from dict  WHERE (id=" + unit + " or father_id='" + unit + "') " + "and"
                            + " " + "isdelete=1 " + " and id != '320400WB9431' and id != '320400WB9432' and id != " +
                            "'320400WB9900' and id != " + "'320400WB990Z' " + " order by static_index,id";
            List<JSONObject> list = mysql.query(sql);
            List<JSONObject> ll = new ArrayList<>();

            JSONObject title = new JSONObject();
            title.put("id", "task_name");
            title.put("dict_name", "任务名称");
            ll.add(title);
            title = new JSONObject();
            title.put("id", "task_detail");
            title.put("dict_name", "任务明细");
            ll.add(title);
            if (unit.contains("320400")) {
                title = new JSONObject();
                title.put("id", "320400000000_0");
                title.put("dict_name", "常州市公安局本级");
                ll.add(title);

            }


            ll.addAll(list);

            datas.put("head", ll);


            // 数据

            sql =
                    "SELECT TASK_NAME FROM QQB_TASK_COUNT WHERE COUNT>0  " + folder + " GROUP BY TASK_NAME OFFSET " + (page - 1) * limit + " ROWS FETCH NEXT " + limit + " ROWS" + " ONLY";
            List<JSONObject> tasks = new ArrayList<>();
            if (page == 1) {

                JSONObject d = new JSONObject();

                d.put("TASK_NAME", "ALL");
                tasks.add(d);
            }
            tasks.addAll(ora.query(sql));


            JSONObject bbs = GetBBS(tasks, ora, unit, date, nextC, nextF);
            // System.out.println(bodys);
            datas.put("body", bbs.getJSONArray("body"));
            sql = "SELECT TASK_NAME FROM QQB_TASK_COUNT WHERE COUNT>0  " + folder + " GROUP BY " + "TASK_NAME";
            List<JSONObject> couts = ora.query(sql);
            int count = couts.size();
            datas.put("count", count);

            if (isExp == 1)//导出本页
            {
                file_id = createExcel(datas.getJSONArray("head"), bbs.getJSONArray("exes"), "任务执行概况_" + date, 5);
                datas.put("file_id", file_id);

            } else if (isExp == 2)//导出全部
            {
                sql = "SELECT TASK_NAME FROM QQB_TASK_COUNT WHERE COUNT>0  " + folder + " GROUP BY TASK_NAME ";
                tasks = new ArrayList<>();


                JSONObject d = new JSONObject();

                d.put("TASK_NAME", "ALL");
                tasks.add(d);

                tasks.addAll(ora.query(sql));


                bbs = GetBBS(tasks, ora, unit, date, nextC, nextF);
                // System.out.println(bodys);
                datas.put("body", bbs.getJSONArray("body"));
                file_id = createExcel(datas.getJSONArray("head"), bbs.getJSONArray("exes"), "任务执行概况_" + date, 5);
                datas.put("file_id", file_id);
            } else {
                datas.put("file_id", -1);
            }

            back.put("data", datas);
            return back;


        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(446002);
        } finally {

            ora.close();


        }


    }

    private static JSONObject GetBBS(List<JSONObject> tasks, OracleHelper ora, String unit, String date, int c, int f) {
        JSONArray bodys = new JSONArray();
        JSONArray exes = new JSONArray();
        try {
            for (int i = 0; i < tasks.size(); i++) {
                JSONObject tone = tasks.get(i);

                String task_name = tone.getString("TASK_NAME");

                String sql = "";
                if (unit.contains("320400")) {
                    sql =
                            "select CODE,TOTAL,NAME,FIN,NEAR,DELAY,DELAYFIN,NEXT,TASK_NAME FROM QQB_TASK_STATIC_TOG " +
                                    "WHERE" + " " + "TIME='" + date + "' AND (CODE='" + unit + "'  OR (FATHER_ID='" + unit + "' AND "
                                    + "NEXT" + "=" + f + " and type=23) or " + "(FATHER_ID='" + unit + "' AND NEXT=0 "
                                    + "and " +
                                    "type" + "=22)" + ")" + "AND TASK_NAME='" + task_name + "' ORDER BY " + "TYPE,CODE";
                } else {
                    sql =
                            "select CODE,TOTAL,NAME,FIN,NEAR,DELAY,DELAYFIN,NEXT,TASK_NAME FROM QQB_TASK_STATIC_TOG " +
                                    "WHERE" + " " + "TIME='" + date + "' AND ((CODE='" + unit + "' and NEXT=" + c +
                                    ") OR " +
                                    "(FATHER_ID" + "='" + unit + "' " + "AND " + "NEXT=" + f + ")) " + "AND TASK_NAME" +
                                    "='" + task_name + "'" + " ORDER BY TYPE,CODE";
                }
//                logger.warn(sql);
                JSONArray childs = new JSONArray();

                List<JSONObject> dets = ora.query(sql);
                JSONObject cone = new JSONObject();
                cone.put("task_detail", "累计接收数");
                for (int d = 0; d < dets.size(); d++) {
                    JSONObject done = dets.get(d);
                    if (done.containsKey("NEXT")) {
                        String next = done.getString("NEXT");
                        String code = done.getString("CODE");
                        if (code.equals("320400000000") && next.equals("0")) {
                            code = code + "_" + next;
                            cone.put(code, done.getString("TOTAL"));
                        } else {
                            cone.put(done.getString("CODE"), done.getString("TOTAL"));
                        }
                    } else {
                        cone.put(done.getString("CODE"), done.getString("TOTAL"));
                    }
                }
                if (task_name.equals("ALL")) {
                    cone.put("task_name", "全部任务");
                    logger.warn(cone.toString());
                } else {
                    cone.put("task_name", task_name);
                }
                childs.add(cone);

                exes.add(cone);

                cone = new JSONObject();
                cone.put("task_detail", "累计完成数");
                for (int d = 0; d < dets.size(); d++) {
                    JSONObject done = dets.get(d);
                    if (done.containsKey("NEXT")) {
                        String next = done.getString("NEXT");
                        String code = done.getString("CODE");
                        if (code.equals("320400000000") && next.equals("0")) {
                            code = code + "_" + next;
                            cone.put(code, done.getString("FIN"));
                        } else {
                            cone.put(done.getString("CODE"), done.getString("FIN"));
                        }
                    } else {
                        cone.put(done.getString("CODE"), done.getString("FIN"));
                    }

                }
                if (task_name.equals("ALL")) {
                    cone.put("task_name", "全部任务");
                } else {
                    cone.put("task_name", task_name);
                }
                childs.add(cone);

                exes.add(cone);

                cone = new JSONObject();
                cone.put("task_detail", "即将逾期数(≤7天)");
                for (int d = 0; d < dets.size(); d++) {
                    JSONObject done = dets.get(d);
                    if (done.containsKey("NEXT")) {
                        String next = done.getString("NEXT");
                        String code = done.getString("CODE");
                        if (code.equals("320400000000") && next.equals("0")) {
                            code = code + "_" + next;
                            cone.put(code, done.getString("NEAR"));
                        } else {
                            cone.put(done.getString("CODE"), done.getString("NEAR"));
                        }
                    } else {
                        cone.put(done.getString("CODE"), done.getString("NEAR"));
                    }

                }
                if (task_name.equals("ALL")) {
                    cone.put("task_name", "全部任务");
                } else {
                    cone.put("task_name", task_name);
                }
                childs.add(cone);


                exes.add(cone);

                cone = new JSONObject();
                cone.put("task_detail", "逾期未完成数");

                for (int d = 0; d < dets.size(); d++) {
                    JSONObject done = dets.get(d);
                    if (done.containsKey("NEXT")) {
                        String next = done.getString("NEXT");
                        String code = done.getString("CODE");
                        if (code.equals("320400000000") && next.equals("0")) {
                            code = code + "_" + next;
                            cone.put(code, done.getString("DELAY"));
                        } else {
                            cone.put(done.getString("CODE"), done.getString("DELAY"));
                        }
                    } else {
                        cone.put(done.getString("CODE"), done.getString("DELAY"));
                    }

                }
                if (task_name.equals("ALL")) {
                    cone.put("task_name", "全部任务");
                } else {
                    cone.put("task_name", task_name);
                }
                childs.add(cone);

                exes.add(cone);
                cone = new JSONObject();
                cone.put("task_detail", "累计逾期完成数");
                for (int d = 0; d < dets.size(); d++) {
                    JSONObject done = dets.get(d);
                    if (done.containsKey("NEXT")) {
                        String next = done.getString("NEXT");
                        String code = done.getString("CODE");
                        if (code.equals("320400000000") && next.equals("0")) {
                            code = code + "_" + next;
                            cone.put(code, done.getString("DELAYFIN"));
                        } else {
                            cone.put(done.getString("CODE"), done.getString("DELAYFIN"));
                        }
                    } else {
                        cone.put(done.getString("CODE"), done.getString("DELAYFIN"));
                    }

                }
                if (task_name.equals("ALL")) {
                    cone.put("task_name", "全部任务");
                } else {
                    cone.put("task_name", task_name);
                }
                childs.add(cone);

                exes.add(cone);
                tone.put("children", childs);

                bodys.add(tone);
            }
        } catch (Exception exception) {
            logger.warn(Lib.getTrace(exception));
        }

        JSONObject back = new JSONObject();
        back.put("body", bodys);
        back.put("exes", exes);
        return back;
    }

    private static String createExcel(JSONArray heads, JSONArray exes, String name, int colCount) {
        String file_id = "-1";
        try {

            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) +
                            "/";

            File oaupload1 = new File(TNOAConf.get("file", "img_path") + filePath);
            // logger.warn(SciConf.get("file", "upload_pic") + File.separator + filePath);
            //  这路径后面若需要能改成关联nas的路径
            if (!oaupload1.isDirectory()) {
                oaupload1.mkdirs();
            }
            // 1、创建关联磁盘文件

            String fileName = name + "_" + System.currentTimeMillis() + ".xls";
            File excel = new File(TNOAConf.get("file", "img_path") + filePath + fileName);
            // 创建一个excel
            WritableWorkbook workbook = Workbook.createWorkbook(excel);

            // 2、创建一个Excel的工作表sheet
            WritableSheet sheet = workbook.createSheet("sheet", 0);

            // 3、样式设置
            // 3.1、文字设置
            // 一种为bold加粗，一种为noBold不加粗。具体要设置其他样式可以点开WritableFont类参考
            WritableFont bold = new WritableFont(WritableFont.createFont("微软雅黑"), 12, WritableFont.BOLD);
            WritableFont noBold = new WritableFont(WritableFont.createFont("微软雅黑"), 12, WritableFont.NO_BOLD);

            // 3.2、设置标题样式
            WritableCellFormat titleFormate = new WritableCellFormat(bold);
            // 设置单元格中的内容水平方向居中、垂直方向居中、背景填充天蓝色、设置边框
            titleFormate.setAlignment(jxl.format.Alignment.CENTRE);
            titleFormate.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE);
            // titleFormate.setBackground(Colour.SKY_BLUE);
            titleFormate.setBorder(Border.ALL, BorderLineStyle.THIN);
            titleFormate.setWrap(true);// 自动换行

            // 3.3设置正文内容样式，单元格样式控制对象
            WritableCellFormat textFormat = new WritableCellFormat(noBold);
            // 单元格中的内容水平方向居中、垂直方向居中、设置边框
            textFormat.setAlignment(Alignment.CENTRE);
            textFormat.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE);
            textFormat.setBorder(Border.ALL, BorderLineStyle.THIN);

            textFormat.setWrap(true);
            // 3.4、窗口冻结第一行
            sheet.getSettings().setVerticalFreeze(1);
            sheet.getSettings().setHorizontalFreeze(2);// 冻结 2列两行

            // 3.5、设置行高--第一行标题行
            // sheet.setRowView(0,500);

            // 3.6、设置列宽

            for (int i = 0; i < heads.size(); i++) {

                sheet.setColumnView(i, 18);

            }

            // 3.7、对数据进行分组
            // 进行分组，2-3、5-6、8-9各为一组，并且默认是折叠true方式生成
            // sheet.setRowGroup(1, 2, true);
            // sheet.setRowGroup(4, 5, true);
            // sheet.setRowGroup(7, 8, true);

            // 4、构造表头

            String[] hds = setSheetHeader(sheet, titleFormate, heads);

            // 第二行表头
            // setSheetHeader2(sheet, titleFormate, 1, heads.size());

            // 5、填充数据
            setSheetData(sheet, textFormat, 1, exes, hds, colCount);
            workbook.write();
            workbook.close();


            InfoModelHelper info = InfoModelPool.getModel();

            try {
                String sql = "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath +
                        "'," + "'" + fileName + "',999)";


                info.update(sql);
                sql = "select * from upload where file_name='" + fileName + "' AND file_path='" + filePath + "'";
                List<JSONObject> result = info.query(sql);

                JSONObject xJsonObject = result.get(0);
                file_id = xJsonObject.getString("id");

                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + fileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, TNOAConf.get("file",
                        "img_path") + filePath + fileName);
                logger.warn(obsFileName + "-->" + ret);

                logger.warn("id->" + file_id);
                Thread.sleep(1000);
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            } finally {
                InfoModelPool.putModel(info);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }

        return file_id;
    }

    private static Object RelaTaskHead(List<JSONObject> list) {
        List<JSONObject> back = new ArrayList<JSONObject>();

        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            if (one.containsKey("TASK_NAME")) {
                one.put("NAME", one.getString("TASK_NAME"));
                one.put("ID", one.getString("TASK_ID"));
            } else if (one.containsKey("FILE_NAME")) {
                one.put("NAME", one.getString("FILE_NAME"));
                one.put("ID", one.getString("FILE_ID"));
            }
            back.add(one);
        }
        return back;
    }

    private static List<JSONObject> RelaTask(List<JSONObject> lists) {
        List<JSONObject> back = new ArrayList<JSONObject>();
        HashMap<String, JSONObject> dets = new HashMap<>();
        for (int i = 0; i < lists.size(); i++) {
            JSONObject one = lists.get(i);
            String FIN = one.getString("FIN");
            String TOTAL = one.getString("TOTAL");
            String NEAR = one.getString("NEAR");
            String DELAY = one.getString("DELAY");
            String DELAYFIN = one.getString("DELAYFIN");
            String task_id = one.getString("TASK_ID");
            String code = one.getString("CODE");
            JSONObject ones = new JSONObject();
            ones.put(task_id + "_finish", FIN);
            ones.put(task_id + "_total", TOTAL);
            ones.put(task_id + "_near", NEAR);
            ones.put(task_id + "_delay", DELAY);
            ones.put(task_id + "_delayFin", DELAYFIN);
            if (dets.containsKey(code)) {
                JSONObject det = dets.get(code);
                det.putAll(ones);
                dets.put(code, det);
            } else {
                ones.put("FATHER_ID", one.getString("FAHTER_ID"));
                ones.put("TASK_ID", one.getString("TASK_ID"));
                ones.put("CODE", one.getString("CODE"));
                ones.put("NAME", one.getString("NAME"));
                ones.put("TYPE", one.getString("TYPE"));
                dets.put(code, ones);

            }
        }
        for (Map.Entry<String, JSONObject> d : dets.entrySet()) {
            back.add(d.getValue());
        }

        Collections.sort(back, (JSONObject o1, JSONObject o2) -> {

            long a = o1.getLong("CODE");
            long b = o2.getLong("CODE");

            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (a < b) {  //降序排列，升序改成a>b
                return 1;
            } else if (a == b) {
                return 0;
            } else {
                return -1;
            }
        });

        return back;

    }


    private static List<JSONObject> RelaTaskChange(List<JSONObject> lists, List<JSONObject> starts, String end,
                                                   String start) {
        List<JSONObject> back = new ArrayList<JSONObject>();
        HashMap<String, JSONObject> dets = new HashMap<>();
        for (int i = 0; i < lists.size(); i++) {
            JSONObject one = lists.get(i);


            int FIN = one.getInteger("FIN");
            int TOTAL = one.getInteger("TOTAL");

            int DELAY = one.getInteger("DELAY");

            String task_id = one.getString("TASK_ID");
            String code = one.getString("CODE");
            int NEXT = one.getInteger("NEXT");

            JSONObject sta = new JSONObject();
            for (int a = 0; a < starts.size(); a++) {
                JSONObject sone = starts.get(a);
                if (sone.getString("TASK_ID").equals(task_id) && sone.getString("CODE").equals(code)) {
                    sta = sone;
                    break;
                }
            }


            JSONObject ones = new JSONObject();
            if (sta.containsKey("TASK_ID")) {
                int f = sta.getInteger("FIN");
                int t = sta.getInteger("TOTAL");

                int d = sta.getInteger("DELAY");
                ones.put(task_id + "_finish", FIN - f);
                ones.put(task_id + "_total", TOTAL - t);

                ones.put(task_id + "_delay", DELAY - d);

            } else {
                ones.put(task_id + "_finish", FIN);
                ones.put(task_id + "_total", TOTAL);

                ones.put(task_id + "_delay", DELAY);

            }
            if (dets.containsKey(code)) {
                JSONObject det = dets.get(code);
                det.putAll(ones);
                dets.put(code, det);
            } else {
                ones.put("FATHER_ID", one.getString("FAHTER_ID"));
                ones.put("TASK_ID", one.getString("TASK_ID"));
                ones.put("CODE", one.getString("CODE"));
                ones.put("NAME", one.getString("NAME"));
                ones.put("TYPE", one.getString("TYPE"));
                dets.put(code, ones);

            }
        }
        for (Map.Entry<String, JSONObject> d : dets.entrySet()) {
            back.add(d.getValue());
        }

        Collections.sort(back, (JSONObject o1, JSONObject o2) -> {

            long a = o1.getLong("CODE");
            long b = o2.getLong("CODE");

            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (a < b) {  //降序排列，升序改成a>b
                return 1;
            } else if (a == b) {
                return 0;
            } else {
                return -1;
            }
        });

        return back;

    }

    /**
     * 设置sheet的第一行标题样式和内容
     *
     * @param sheet        工作表
     * @param titleFormate 填充样式
     * @return
     * @throws WriteException 异常
     */
    private static String[] setSheetHeader(WritableSheet sheet, WritableCellFormat titleFormate, JSONArray bodys) throws WriteException {
        // 构造表头
        // mergeCells(0, 0, 0, 0) 表示不合并； sheet.mergeCells(1,0,2,0)表示第2列和第3列合并成一列
        // Label label_20 = new Label(2, 0, "描述", cellFormat); 前面的数字表示第几列，第几行
        // 4.1、创建表数据
        Label label_00 = new Label(0, 0, "", titleFormate);
        sheet.addCell(label_00);
        // 4.2、合并单元格

        String[] hds = new String[bodys.size()];

        for (int i = 0; i < bodys.size(); i++) {

            JSONObject bone = bodys.getJSONObject(i);
            String name = bone.getString("dict_name");

            Label label_20 = new Label(i, 0, name, titleFormate);
            sheet.addCell(label_20);

            hds[i] = bone.getString("id");
        }

        return hds;

    }

    /**
     * 填充数据到excel
     *
     * @param sheet      工作表
     * @param textFormat 填充样式
     * @param startRow   填充行数索引
     * @param bodys
     * @param hds
     * @throws WriteException 异常
     */
    private static void setSheetData(WritableSheet sheet, WritableCellFormat textFormat, int startRow,
                                     JSONArray bodys, String[] hds, int ColCount) throws WriteException {
        // 填充第正文内容；假设只填充9条，其实是按照数据量决定，应该遍历数据依次填入

        NumberFormat nf = new NumberFormat("#");
        WritableCellFormat numLab = new WritableCellFormat(nf);
        // 单元格中的内容水平方向居中、垂直方向居中、设置边框
        numLab.setAlignment(Alignment.CENTRE);
        numLab.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE);
        numLab.setBorder(Border.ALL, BorderLineStyle.THIN);


        int row = 0;

        for (int i = 0; i < bodys.size(); i++, startRow++) {

            JSONObject bone = bodys.getJSONObject(i);

            logger.warn(bone.toString());
            for (int h = 0; h < hds.length; h++) {
                String col = hds[h];
                //  logger.warn(col);


                try {
                    double v = Double.parseDouble(bone.getString(col));
                    jxl.write.Number nLab = new jxl.write.Number(h, startRow, v, numLab);
                    sheet.addCell(nLab);
                } catch (Exception ex) {
                    Label label_02 = new Label(h, startRow, bone.getString(col), textFormat);
                    sheet.addCell(label_02);
                }
            }

            if (startRow % ColCount == 0) {

                //
                int one = startRow - ColCount + 1;

                sheet.mergeCells(0, one, 0, startRow);

            }
        }

    }

}