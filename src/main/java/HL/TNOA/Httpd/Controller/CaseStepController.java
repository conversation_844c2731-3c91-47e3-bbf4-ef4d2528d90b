package HL.TNOA.Httpd.Controller;

import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CaseStepController {
    private static Logger logger = LoggerFactory.getLogger(CaseStepController.class);

    public static JSONObject updateCaseStep(JSONObject data) {

        String case_id = "";
        String obj_id = "";
        String index = "";
        String select_id = "-1";
        String select_name = "";
        String values = "";
        String input = "";
        String radio = "-1";
        String date = "";
        String amount = "0.0";
        String radio2 = "-1";
        int isNotice = 0;
        String notice_date = "";
        String cycle_notice = "";
        String cycle = "0";
        String status = "0";
        String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String opt_user = "";
        String cycle_end = "";
        if (data.containsKey("case_id") && data.getString("case_id").length() > 0) {
            case_id = data.getString("case_id");
        } else {
            return ErrNo.set(457010);
        }
        if (data.containsKey("obj_id") && data.getString("obj_id").length() > 0) {
            obj_id = data.getString("obj_id");
        } else {
            return ErrNo.set(457010);
        }
        if (data.containsKey("index") && data.getString("index").length() > 0) {
            index = data.getString("index");
        } else {
            return ErrNo.set(457010);
        }
        if (data.containsKey("select_id") && data.getString("select_id").length() > 0) {
            select_id = data.getString("select_id");
        }
        if (data.containsKey("values") && data.getString("values").length() > 0) {
            values = data.getString("values");
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(457010);
        }
        if (data.containsKey("status") && data.getString("status").length() > 0) {
            status = data.getString("status");
        } else {
            return ErrNo.set(457010);
        }
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        JSONObject datas = new JSONObject();
        try {
            mysql = InfoModelPool.getModel();

            if (!"99".equals(index)) {
                if (values.length() > 0) {
                    JSONArray list = JSONArray.parseArray(values);
                    for (int l = 0; l < list.size(); l++) {
                        JSONObject v1 = list.getJSONObject(l);
                        if (select_id.contains(v1.getString("select_id"))) {
                            select_name = select_name + v1.getString("select_name") + "、";

                            JSONArray va = v1.getJSONArray("values");
                            for (int i = 0; i < va.size(); i++) {
                                JSONObject onev = va.getJSONObject(i);
                                String type = onev.getString("type");
                                String name = onev.getString("name");
                                String mark = onev.getString("mark");
                                String value = onev.getString("value");

                                if ("input".equals(type)) {

                                    if ("金额".equals(name)) {
                                        amount = value;

                                    } else {
                                        input = value;
                                    }
                                }
                                if ("radio".equals(type)) {

                                    if ("-1".equals(radio)) {
                                        radio = value;

                                    } else {
                                        radio2 = value;
                                    }

                                }
                                if ("date".equals(type)) {
                                    date = value;
                                }


                                if (mark.contains("cycle")) {
                                    String cyc[] = mark.split("_");
                                    cycle = cyc[1];
                                    if ("input".equals(cycle)) {
                                        cycle = input;
                                    }
                                    if (cyc.length == 3) {
                                        String d[] = cyc[2].split("\\.");
                                        if ("last".equals(d[0])) {
                                            int last_day = Integer.parseInt(d[1]);
                                            cycle_end = RIUtil.GetNextDate(date, Integer.parseInt(input) - 1);
                                            cycle_notice = RIUtil.GetNextDate(date, Integer.parseInt(input) - 7) + " " +
                                                    "09:00:00";


                                        } else if ("empty".equals(d[0]) && ("2".equals(radio) || "2".equals(radio2))) {
                                            if (date.length() == 0) {
                                                date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
                                            }
                                            cycle_notice = RIUtil.GetNextDate(date, 1) + " 09:00:00";
                                            cycle = "1";
                                        } else if ("empty".equals(d[0]) && ("1".equals(radio) || "1".equals(radio2))) {
                                            cycle = "0";
                                            cycle_notice = "";
                                        }


                                    } else {
                                        if (date.length() > 0) {
                                            cycle_notice = date + " 09:00:00";
                                        } else {
                                            cycle_notice =
                                                    RIUtil.GetNextDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()), 1) + " 09:00:00";
                                        }
                                    }


                                } else if (mark.startsWith("last")) {
                                    String[] las = mark.split("_");
                                    isNotice = 1;
                                    int last_day = Integer.parseInt(las[1]);
                                    notice_date = RIUtil.GetNextDate(date, Integer.parseInt(input) - last_day) + " " +
                                            "09:00:00";

                                } else if ("notice".equals(mark)) {
                                    isNotice = 1;
                                    notice_date = date + " 09:00:00";
                                }


                            }
                        }
                    }
                    String sqls = "select id from case_step where case_id='" + case_id + "' and obj_id='" + obj_id +
                            "' and index_no='" + index + "' and isdelete=1";
                    String id = mysql.query_one(sqls, "id");
                    if (id.length() > 0) {

                        sqls = "update case_step set select_id='" + select_id + "',`input`='" + input + "',`radio`='" + radio + "',`date`='" + date + "',isnotice='" + isNotice + "'," +
                                "time='" + time + "',opt_user='" + opt_user + "',`values`='" + values + "',amount='" + amount + "',radio2='" + radio2 + "' " +
                                ",notice_date='" + notice_date + "',cycle='" + cycle + "',cycle_notice='" + cycle_notice + "'" +
                                "where id='" + id + "'";


                    } else {
                        sqls = "INSERT INTO `case_step` ( `case_id`, `obj_id`, index_no, `select_id`, `input`, " +
                                "`radio`, " +
                                "`date`,  `isnotice`, `time`, `opt_user`, `values`,amount,radio2," +
                                "notice_date,cycle,cycle_notice) " +
                                "VALUES ('" + case_id + "', '" + obj_id + "', '" + index + "', '" + select_id + "', " +
                                "'" + input + "', '" + radio + "'," +
                                " '" + date + "', '" + isNotice + "', '" + time + "', '" + opt_user + "', '" + values + "','" + amount + "','" + radio2 + "'," +
                                "'" + notice_date + "','" + cycle + "','" + cycle_notice + "');";
                    }
                    //logger.warn(sqls);
                    mysql.update(sqls);
                    if ("3".equals(select_id) && "3".equals(index)) {
                        sqls = "update case_info set type=8 where id='" + case_id + "' ";
                        mysql.update(sqls);
                        logger.warn(sqls);
                    }
                    if ("2".equals(status)) {

                        //下一步
                        JSONObject next = CaseObjectController.GetNextStep(mysql, index, select_id);
                        String next_step_name = next.getString("step_name");

                        //更新当前状态
                        sqls = "update case_object set current_index='" + next.getString("index_no") + "' where " +
                                "case_id='" + case_id + "' and id='" + obj_id + "'";
                        mysql.update(sqls);

                        //判断是否完成
                        if ("99".equals(next.getString("index_no"))) {

                            sqls = "select count(id) as count from case_object where current_index!='99' and " +
                                    "case_id='" + case_id + "'";
                            int count = mysql.query_count(sqls);
                            sqls = "select count(id) from case_list where case_id='" + case_id + "' and isnull" +
                                    "(finish_time) and isdelete=1";
                            count = count + mysql.query_count(sqls);
                            if (count == 0) {
                                sqls = "update case_info set  status=1 where id='" + case_id + "' ";
                                mysql.update(sqls);
                            }
                        }

                        //取消之前所有步骤的提醒
                        sqls = "update case_step set isnotice=0 , cycle=0 where case_id='" + case_id + "' and " +
                                "obj_id='" + obj_id + "' and index_no!='" + index + "'";
                        mysql.update(sqls);
//102 802抓捕时间
                        if ("102".equals(index) || "802".equals(index)) {
                            String start = date;
                            String end_date = RIUtil.GetNextDate(date, 30);
                            String near_date = RIUtil.GetNextDate(date, 23) + " 09:00:00";

                            sqls = "update case_object set start_date='" + start + "',near_date='" + near_date + "'," +
                                    "end_date='" + end_date + "' where id='" + obj_id + "'";
                            mysql.update(sqls);
                        }


                        //log
                        sqls = "select step_name from case_step_model where index_no='" + index + "'";
                        String step_name = mysql.query_one(sqls, "step_name");
                        logger.warn(select_name);
                        if (select_name.length() > 1) {
                            select_name = select_name.substring(0, select_name.length() - 1);
                        }
                        List<String> logs = new ArrayList<>();
                        logs.add(step_name + ":" + select_name + "->" + next_step_name);
                        CaseController.addLogs(logs, mysql, case_id, index, obj_id, step_name + ":" + select_name +
                                "->" + next_step_name, opt_user);
                        datas.put("next", next);
                    }
                } else {
                    if ("2".equals(status)) {
                        String sqls =
                                "update case_object set current_index='" + index + "' where case_id='" + case_id + "'" +
                                        " and id='" + obj_id + "'";
                        mysql.update(sqls);


                        //log
                        sqls = "select step_name from case_step_model where index_no='" + index + "'";
                        String step_name = mysql.query_one(sqls, "step_name");
                        List<String> logs = new ArrayList<>();
                        logs.add("结案");
                        CaseController.addLogs(logs, mysql, case_id, index, obj_id, "结案", opt_user);
                        datas.put("next", new JSONObject());
                    }
                }


                back.put("data", datas);
                logger.warn(datas.toString());

            }

            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(457011);
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    public static JSONObject getCaseStep(JSONObject data) {

        String case_id = "";
        String obj_id = "";
        String index = "";

        String opt_user = "";
        if (data.containsKey("case_id") && data.getString("case_id").length() > 0) {
            case_id = data.getString("case_id");
        } else {
            return ErrNo.set(457010);
        }
        if (data.containsKey("obj_id") && data.getString("obj_id").length() > 0) {
            obj_id = data.getString("obj_id");
        }
        if (data.containsKey("index") && data.getString("index").length() > 0) {
            index = data.getString("index");
        } else {
            return ErrNo.set(457010);
        }

        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        }

        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            JSONObject one = new JSONObject();
            String sql = "select a.select_id,a.values as selects,b.step_name,b.multi from case_step a left join " +
                    "case_step_model b " +
                    "on a.index_no=b.index_no where a.case_id='" + case_id + "' and a.obj_id='" + obj_id + "' and a" +
                    ".index_no='" + index + "' and a.isdelete=1";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                one = list.get(0);
                if (one.containsKey("values")) {
                    JSONArray values = one.getJSONArray("values");
                    one.put("selects", values);
                    one.remove("values");
                }
            } else {
                sql = "select '' as select_id,selects,step_name,multi from case_step_model  where index_no='" + index + "'";
                list = mysql.query(sql);
                one = list.get(0);
            }
            back.put("data", one);
            logger.warn(one.toString());
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(457011);
        } finally {
            InfoModelPool.putModel(mysql);
        }


    }

    public static JSONObject backeCaseStep(JSONObject data) {

        String case_id = "";
        String obj_id = "";
        String index = "";
        String opt_user = "";
        int status = 1;
        String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

        if (data.containsKey("case_id") && data.getString("case_id").length() > 0) {
            case_id = data.getString("case_id");
        } else {
            return ErrNo.set(457010);
        }
        if (data.containsKey("obj_id") && data.getString("obj_id").length() > 0) {
            obj_id = data.getString("obj_id");
        } else {
            return ErrNo.set(457010);
        }
        if (data.containsKey("index") && data.getString("index").length() > 0) {
            index = data.getString("index");
        } else {
            return ErrNo.set(457010);
        }
        if (data.containsKey("status") && data.getString("status").length() > 0) {
            status = data.getInteger("status");
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(457010);
        }

        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sqls = "update case_step set isdelete=2 where case_id='" + case_id + "' and obj_id='" + obj_id +
                    "' and index_no='" + index + "' and isdelete=1";
            mysql.update(sqls);
            sqls = "select step_name from case_step_model where index_no='" + index + "'";
            String old_index_name = mysql.query_one(sqls, "step_name");
            sqls = "select a.select_id,a.values as selects,b.step_name,b.multi,a.index_no from case_step a left join" +
                    " case_step_model b " +
                    "on a.index_no=b.index_no where a.case_id='" + case_id + "' and a.obj_id='" + obj_id + "' and  " +
                    " a.isdelete=1 order by a.id desc limit 1";
            List<JSONObject> list = mysql.query(sqls);


            JSONObject datas = new JSONObject();
            String new_index = "";
            if (list.size() > 0) {
                JSONObject one = list.get(0);
                if (one.containsKey("values")) {
                    JSONArray values = one.getJSONArray("values");
                    one.put("selects", values);
                    one.remove("values");
                }
                datas.put("last", one);
                back.put("data", datas);
                new_index = one.getString("index_no");
                sqls = "update case_object set current_index='" + new_index + "' where case_id='" + case_id + "' and " +
                        "id='" + obj_id + "'";
            } else {
                sqls = "select type from case_info where id='" + case_id + "'";
                new_index = mysql.query_one(sqls, "type");

                sqls = "select '' as select_id,selects,step_name,multi,index_no from case_step_model  where " +
                        "index_no='" + new_index + "'";
                list = mysql.query(sqls);
                datas.put("last", list.get(0));
                back.put("data", datas);


                sqls = "update case_object set current_index='" + new_index + "' where case_id='" + case_id + "' and " +
                        "id='" + obj_id + "'";
            }
            mysql.update(sqls);


            //log
            sqls = "select step_name from case_step_model where index_no='" + new_index + "'";
            String step_name = mysql.query_one(sqls, "step_name");
            List<String> logs = new ArrayList<>();
            logs.add(old_index_name + ":撤回->" + step_name);
            CaseController.addLogs(logs, mysql, case_id, index, obj_id, old_index_name + ":撤回->" + step_name, opt_user);
            logger.warn(datas.toString());
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(457011);
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }
}
