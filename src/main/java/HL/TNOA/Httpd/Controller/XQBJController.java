package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class XQBJController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());

    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @RequestMapping(method = {RequestMethod.POST}, path = {"/xqbj"})
    @PassToken
    public JSONObject get_XIAOQU_XY(TNOAHttpRequest request) throws Exception {
        logger.warn("XIAOQU_XY--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_xqbj")) {
                return getXQBJ(data);
            } else if (opt.equals("update_xqbj")) {
                return updateXQBJ(data, request.getRemoteAddr());
            }
            if (opt.equals("get_dmdm")) {
                return getDMDM(data);
            }
            if (opt.equals("update_dmdm")) {
                return updateDMDM(data);
            }
            if (opt.equals("create_dmdm")) {
                return createDMDM(data);
            }
            if (opt.equals("delete_dmdm")) {
                return deleteDMDM(data);
            } else {
                return ErrNo.set(507009);
            }
        } else {
            return ErrNo.set(507009);
        }
    }

    private JSONObject deleteDMDM(JSONObject data) {
        OracleHelper ora_hl = null;
        JSONObject back = ErrNo.set(0);
        try {
            ora_hl = new OracleHelper("ora_hl");
            String dmdm = "";
            if (data.containsKey("dmdm") && data.getString("dmdm").length() > 0) {
                dmdm = data.getString("dmdm");
            } else {
                return ErrNo.set(507004);
            }
            String sql = "update HL.ADDRESS_DM set isdelete = 1 where DMDM='" + dmdm + "'";
            ora_hl.update(sql);

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();

        }
    }

    private JSONObject createDMDM(JSONObject data) {
        OracleHelper ora_hl = null;
        JSONObject back = ErrNo.set(0);
        try {
            ora_hl = new OracleHelper("ora_hl");
            String dmdm = "";
            String dmmc = "";
            String bj = "";
            String zrq = "";
            String pcs = "";
            String fj = "";
            String type = "";
            if (data.containsKey("dmmc") && data.getString("dmmc").length() > 0) {
                dmmc = data.getString("dmmc");
            } else {
                return ErrNo.set(507004);
            }
            if (data.containsKey("bj") && data.getString("bj").length() > 0) {
                bj = data.getString("bj");
            } else {
                return ErrNo.set(507004);
            }
            if (data.containsKey("zrq") && data.getString("zrq").length() > 0) {
                zrq = data.getString("zrq");
            } else {
                return ErrNo.set(507004);
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
            } else {
                return ErrNo.set(507004);
            }
            JSONObject zrqJson = RIUtil.dicts.get(zrq);
            if (!zrq.contains("|")) {

                if (zrqJson != null && zrqJson.containsKey("father_id")) {
                    pcs = zrqJson.getString("father_id");
                    JSONObject pcsJson = RIUtil.dicts.get(pcs);
                    if (pcsJson != null && pcsJson.containsKey("father_id")) {
                        fj = pcsJson.getString("father_id");
                        //如果fj是市局代码 则代表zrq是派出所代码
                        if (fj.equals("************")) {
                            pcs = zrq;
                            fj = pcs;
                            zrq = "";
                        }
                    }
                }

            } else {
                String[] zrqs = zrq.split("\\|");
                HashSet<String> pcsSet = new HashSet();
                HashSet<String> fjSet = new HashSet();
                for (int i = 0; i < zrqs.length; i++) {
                    JSONObject zrqJson1 = RIUtil.dicts.get(zrqs[i]);
                    if (zrqJson1 != null && zrqJson1.containsKey("father_id")) {
                        pcs = zrqJson1.getString("father_id");
                        JSONObject pcsJson = RIUtil.dicts.get(pcs);
                        pcsSet.add(pcs);
                        if (pcsJson != null && pcsJson.containsKey("father_id")) {
                            fj = pcsJson.getString("father_id");
                            //如果fj是市局代码 则代表zrq是派出所代码
                            if (fj.equals("************")) {
                                pcs = zrq;
                                fj = pcs;
                                zrq = "";
                            }
                            fjSet.add(fj);
                        }
                    }
                }
                pcs = String.join("|", pcsSet);
                fj = String.join("|", fjSet);
            }

            dmdm = UUID.randomUUID().toString().replace("-", "");
            String opt_user = data.getString("opt_user");

            String sql =
                    "insert into HL.ADDRESS_DM (DMDM,DMMC, BJ,ZRQ, PCS, FJ, OPT_USER,OPT_TIME, DMLX, ISDELETE) " +
                            "values ('" + dmdm + "','" + dmmc + "','" + bj + "','" + zrq + "','" + pcs + "','" + fj + "','" + opt_user + "','" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "', '" + type + "', '0')";
            ora_hl.update(sql);

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();

        }
    }

    private JSONObject updateDMDM(JSONObject data) {
        OracleHelper ora_hl = null;
        JSONObject back = ErrNo.set(0);
        try {
            ora_hl = new OracleHelper("ora_hl");
            String dmdm = "";
            String dmmc = "";
            String bj = "";
            String zrq = "";
            String pcs = "";
            String fj = "";
            if (data.containsKey("dmdm") && data.getString("dmdm").length() > 0) {
                dmdm = data.getString("dmdm");
            } else {
                return ErrNo.set(507004);
            }
            if (data.containsKey("bj") && data.getString("bj").length() > 0) {
                bj = data.getString("bj");
            }
            if (data.containsKey("zrq") && data.getString("zrq").length() > 0) {
                zrq = data.getString("zrq");
            }
            if (data.containsKey("zrq") && data.getString("zrq").length() > 0) {
                zrq = data.getString("zrq");
            }
            if (data.containsKey("dmmc") && data.getString("dmmc").length() > 0) {
                dmmc = data.getString("dmmc");
            }

            //zrq有可能是派出所代码或责任区代码
            JSONObject zrqJson = RIUtil.dicts.get(zrq);
            if (zrqJson != null && zrqJson.containsKey("father_id")) {
                pcs = zrqJson.getString("father_id");
                JSONObject pcsJson = RIUtil.dicts.get(pcs);
                if (pcsJson != null && pcsJson.containsKey("father_id")) {
                    fj = pcsJson.getString("father_id");
                    //如果fj是市局代码 则代表zrq是派出所代码
                    if (fj.equals("320400000000")) {
                        fj = pcs;
                        pcs = zrq;
                        zrq = "";
                    }
                }
            }

            String opt_user = data.getString("opt_user");

            String sql =
                    "update HL.ADDRESS_DM SET BJ='" + bj + "',opt_user='" + opt_user + "',opt_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "', ZRQ = '" + zrq + "', PCS = '" + pcs + "'," + " FJ = '" + fj + "', DMMC = ' " + dmmc + "' " + "where DMDM='" + dmdm + "'";
            logger.warn(sql);
            ora_hl.update(sql);

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();

        }
    }

    private JSONObject getDMDM(JSONObject data) {
        OracleHelper ora_hl = null;
        JSONObject back = ErrNo.set(0);
        try {
            ora_hl = new OracleHelper("ora_hl");
            String sql = "";
            int limit = 20;
            int page = 1;
            String query = "";

            String unit = "320400000000";
            if (data.containsKey("query") && data.getString("query").length() > 0) {
                query = data.getString("query");
                sql = sql + " and (DMMC LIKE '%" + query + "%' or DMTS like '%" + query + "%') ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                sql = sql + " and DMLX in('" + data.getString("type").replace(",", "','") + "' )";
            } else {
                sql = sql + " and (DMLX='02' or DMLX='03' or DMLX = '09') ";
            }
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                sql = sql + " and DMDM='" + data.getString("id") + "' ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");

            }
            JSONObject det = RIUtil.dicts.get(unit);
            String type = det.getString("type");
            if (type.equals("23") || type.equals("24") || type.equals("28")) {
                sql = sql + " and FJ LIKE '" + unit.substring(0, 6) + "%' ";
            } else if (type.equals("25")) {
                sql = sql + " and PCS LIKE '" + unit.substring(0, 8) + "%' ";
            } else if (type.equals("26")) {
                sql = sql + " and ZRQ = '" + unit + "' ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            String sqls = "select * from HL.ADDRESS_DM where 1=1 and isdelete = 0 " + sql;

            sqls += "order by opt_time desc OFFSET" + " " + (page - 1) * limit + " " + "ROWS FETCH NEXT" + " " + limit + " " + "ROWS " + "ONLY";
            logger.warn(sqls);

            List<JSONObject> list = ora_hl.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfoUnit(list));
            } else {
                back.put("data", new ArrayList<>());

            }
            sqls = "select COUNT(1) AS COUNT from HL.ADDRESS_DM where 1=1 and isdelete = 0 " + sql;
            int count = ora_hl.query_count(sqls);
            back.put("count", count);
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();

        }

    }

    private List<JSONObject> RelaInfoUnit(List<JSONObject> list) {

        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String pcs = one.getString("PCS");
            one.put("PCS", RIUtil.RealDictNameList(RIUtil.StringToList(pcs.replace("|", ","))));
            String fj = one.getString("FJ");
            one.put("FJ", RIUtil.RealDictNameList(RIUtil.StringToList(fj.replace("|", ","))));
            String zrq = one.getString("ZRQ");
            one.put("ZRQ", RIUtil.RealDictNameList(RIUtil.StringToList(zrq.replace("|", ","))));
            one.put("OPT_USER", RIUtil1.users1.get(one.getString("OPT_USER")));
            back.add(one);
        }

        return back;
    }


    //******GET*******
    private JSONObject getXQBJ(JSONObject data) {
        OracleHelper ora = null;
        JSONObject back = ErrNo.set(0);
        try {
            ora = new OracleHelper("ora_hl");
            String sql = "";
            int limit = 20;
            int page = 1;
            String query = "";

            String unit = "";

            if (data.containsKey("query") && data.getString("query").length() > 0) {
                query = data.getString("query");
                sql = sql + "( NAME  like '%" + query + "%' or DZMC like '%" + query + "%') and ";
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " ZRQ='" + unit + "' and ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select * from XIAOQU_XY where 1=1 and " + sql + " isdelete = 0  " + "OFFSET " + (page - 1) * limit + " " + "ROWS FETCH NEXT" + " " + limit + " " + "ROWS ONLY";
            List<JSONObject> list = new ArrayList<>();
            list = ora.query(sqls);
            if (list.size() > 0) {
                back.put("data", list);
                sqls = "select count(FACE_PID) as count from XIAOQU_XY where 1=1 and " + sql + " 1=1 ";
                back.put("count", ora.query_count(sqls));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(507005);
        } finally {
            ora.close();

        }
        return back;
    }

    //*****relainfo************
    private Object RelaInfo(List<JSONObject> list, InfoModelHelper ora) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            one.put("create_user", RIUtil.users.get(one.getString("create_user")));
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private JSONObject updateXQBJ(JSONObject data, String remoteAddr) {
        OracleHelper ora = null;
        OracleHelper ora_gl = null;
        JSONObject back = ErrNo.set(0);
        try {
            ora = new OracleHelper("ora_hl");
            ora_gl = new OracleHelper("ora_bk_gl");
            String sql = "";
            String FACE_PID = "";
            String DZID = "";

            String opt_user = "";

            if (data.containsKey("FACE_PID") && data.getString("FACE_PID").length() > 0) {
                FACE_PID = data.getString("FACE_PID");

            } else {
                return ErrNo.set(507004);
            }


            if (data.containsKey("DZID") && data.getString("DZID").length() > 0) {
                DZID = data.getString("DZID");
                String s = "select DMDM,HJZRQ,DZ,FDDZ from CZQJ_YBDS.ADDRESS_INFO where DZID='" + DZID + "'";
                String dmdm = ora_gl.query_one(s, "DMDM");
                if (dmdm.length() > 0) {
                    String zrq = ora_gl.query_one(s, "HJZRQ");
                    String dz = ora_gl.query_one(s, "DZ");
                    String fddz = ora_gl.query_one(s, "FDDZ");
                    dz = dz.replace(fddz, "");


                    sql = "DZMC='" + dz + "',ZRQ='" + zrq + "',DMDM='" + dmdm + "',";
                }


            } else {
                return ErrNo.set(507004);
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
                sql = sql + "UPDATE_USER='" + opt_user + "' ,  ";
            } else {
                return ErrNo.set(507004);
            }
            String sqls = "update XIAOQU_XY set " + sql + " UPDATE_TIME='" + new SimpleDateFormat("yyyy-MM-dd  " +
                    "HH:mm:ss").format(new Date()) + "'  where FACE_PID='" + FACE_PID + "'";
            ora.update(sqls);
            UserLog userlog = new UserLog();
            // userlog.log(ora, opt_user, "更新小区边界", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(507003);
        } finally {
            ora.close();
            ora_gl.close();
        }
        return back;
    }


}