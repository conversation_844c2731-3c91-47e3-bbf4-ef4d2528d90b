package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.Lib.RIUtil1;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@RestController
public class WebNoticeController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());

    @RequestMapping(method = {RequestMethod.POST}, path = {"/web_notice"})
    @PassToken
    public JSONObject get_web_notice(TNOAHttpRequest request) throws Exception {
        //  logger.warn("web_notice--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_web_notice")) {
                return getWebNotice(data);
            } else if (opt.equals("create_web_notice")) {
                return createWebNotice(data, request.getRemoteAddr());
            } else if (opt.equals("update_web_notice")) {
                return updateWebNotice(data, request.getRemoteAddr());
            } else if (opt.equals("delete_web_notice")) {
                return deleteWebNotice(data, request.getRemoteAddr());
            } else {
                return ErrNo.set(504009);
            }
        } else {
            return ErrNo.set(504009);
        }
    }

    //******CREATE*******
    private JSONObject createWebNotice(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String title = "";
            String content = "";
            String isStop = "";
            String readed = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "1";
            String img = "";
            String unit = "";
            int isStrong = 0;
            if (data.containsKey("title") && data.getString("title").length() > 0) {
                title = data.getString("title");
            } else {
                return ErrNo.set(504002);
            }
            if (data.containsKey("content") && data.getString("content").length() > 0) {
                content = data.getString("content");
            } else {
                return ErrNo.set(504002);
            }
            if (data.containsKey("isStop") && data.getString("isStop").length() > 0) {
                isStop = data.getString("isStop");
            } else {

            }
            if (data.containsKey("isStrong") && data.getString("isStrong").length() > 0) {
                isStrong = data.getInteger("isStrong");
            }
            if (data.containsKey("img") && data.getString("img").length() > 0) {
                img = data.getString("img");
            }

            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                JSONObject uone = RIUtil1.users1.get(create_user);
                logger.warn(uone.toString());

                unit = uone.getString("unit").split(",")[0];
                JSONObject done = RIUtil.dicts.get(unit);
                int type = done.getInteger("type");
                if (type == 21 || type == 22 || type == 27) {
                    unit = "3204";
                } else if (type == 23 || type == 28 || type == 24) {
                    unit = unit.substring(0, 6);
                } else {
                    unit = unit.substring(0, 8);
                }
            } else {
                return ErrNo.set(504002);
            }
            String sqls = "insert web_notice (title,content,isStop,readed,create_user,create_time,isdelete,img," +
                    "isStrong,unit_name)" + "values" + "('" + title + "','" + content + "','" + isStop + "','" + readed + "','" + create_user + "','" + create_time + "','" + isdelete + "','" + img + "','" + isStrong + "','" + unit + "')";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建网页通知", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 504001, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private JSONObject getWebNotice(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String title = "";
            String content = "";
            String isStop = "";
            String readed = "";
            String create_user = "";
            String create_time_start = "";
            String create_time_end = "";
            String user_id = "";
            String source = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }

            if (data.containsKey("content") && data.getString("content").length() > 0) {
                content = data.getString("content");
                sql = sql + " (content like '%" + content + "%' or title like '%" + content + "%') and ";
            }
            if (data.containsKey("isStop") && data.getString("isStop").length() > 0) {
                isStop = data.getString("isStop");
                sql = sql + " isStop='" + isStop + "' and ";
            }
            if (data.containsKey("source") && data.getString("source").length() > 0) {
                source = data.getString("source");

            }
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
                if (source.equals("qqb")) {
                    String s = "select id_num,unit from user where user_qqb like '%" + user_id + "|%'";
                    System.out.println(s);
                    user_id = mysql.query_one(s, "id_num");
                    try {
                        String us = mysql.query_one(s, "unit").replace("，", ",");
                        us = us.split(",")[0];

                        data.put("unit", us);
                    } catch (Exception ex) {

                    }
                }
                sql = sql + " readed not like '%" + user_id + "%' and ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                String unit = data.getString("unit");
                JSONObject done = RIUtil.dicts.get(unit);
                int type = done.getInteger("type");
                if (type == 21 || type == 22 || type == 27) {
                    sql = sql + "  unit_name='3204' and ";
                } else if (type == 23 || type == 28 || type == 24) {
                    sql = sql + " (unit_name='3204' or  unit_name='" + unit.substring(0, 6) + "' ) and ";
                } else {
                    sql = sql + " (unit_name='3204' or  unit_name='" + unit.substring(0, 6) + "' or unit_name ='" + unit.substring(0, 8) + "' ) " + "and ";
                }
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' and ";
            }
            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
                create_time_start = data.getString("create_time_start");
                sql = sql + "create_time>='" + create_time_start + "' and ";
            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end");
                sql = sql + "create_time<='" + create_time_end + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                String opt_user = data.getString("opt_user");

            }
            String sqls =
                    "select * from web_notice where 1=1 and " + sql + " isdelete=1 order by create_time desc " +
                            "limit " + limit + " offset " + limit * (page - 1);
            // System.out.println(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select count(id) as count from web_notice where 1=1 and " + sql + " isdelete=1; ";
                back.put("count", mysql.query_count(sqls));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 504005, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            one.put("create_user", RIUtil1.users1.get(one.getString("create_user")));

            String img = one.getString("img");
            JSONArray imgs = new JSONArray();
            try {
                if (img.length() > 0) {

                    String[] imgss = img.split(",");
                    for (int a = 0; a < imgss.length; a++) {
                        JSONObject ione = new JSONObject();
                        String d = imgss[a];
                        String name = RIUtil.IdToName(d, mysql, "file_name", "upload");
                        ione.put("file_name", name.substring(14));
                        String path = RIUtil.IdToName(d, mysql, "file_path", "upload");
                        ione.put("file_path", path);
                        ione.put("id", imgss[a]);
                        imgs.add(ione);
                    }
                }
            } catch (Exception ex) {

            }
            one.put("img", imgs);

            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private JSONObject updateWebNotice(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String title = "";
            String content = "";
            String isStop = "";
            String readed = "";
            String opt_user = "";
            String img = "";
            String source = "";
            String isStrong = "0";
            String oldSToP = "";
            int isC = 0;
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(504004);
            }

            if (data.containsKey("title") && data.getString("title").length() > 0) {
                title = data.getString("title");
                sql = sql + " title='" + title + "' , ";
            }
            if (data.containsKey("img") && data.getString("img").length() > 0) {
                img = data.getString("img");
                sql = sql + " img='" + img + "' , ";
            }
            if (data.containsKey("content") && data.getString("content").length() > 0) {
                content = data.getString("content");
                sql = sql + " content='" + content + "' , ";
            }
            if (data.containsKey("isStop") && data.getString("isStop").length() > 0) {
                isStop = data.getString("isStop");
                String s = "select isStop from web_notice WHERE Id='" + id + "'";
                System.out.println(s);
                oldSToP = mysql.query_one(s, "isStop");
                System.out.println(oldSToP);
                sql = sql + " isStop='" + isStop + "' , ";
                if (oldSToP.equals("2") && isStop.equals("1")) {
                    isC = 1;
                    System.out.println(isC);
                }
            }
            if (data.containsKey("isStrong") && data.getString("isStrong").length() > 0) {
                isStrong = data.getString("isStrong");
                sql = sql + " isStrong='" + isStrong + "' , ";
            }
            if (data.containsKey("source") && data.getString("source").length() > 0) {
                source = data.getString("source");

            }
            if (data.containsKey("readed") && data.getString("readed").length() > 0) {
                readed = data.getString("readed");
                if (source.equals("qqb")) {
                    String s = "select id from user where user_qqb like '%" + readed + "|%'";
                    readed = mysql.query_one(s, "id");
                }

                String s = "select readed from web_notice where id='" + id + "' and isdelete=1";
                String red = mysql.query_one(s, "readed");
                HashMap<String, String> rd = RIUtil.StringToList(red);
                rd.put(readed, "");

                if (isC == 1) {
                    sql = sql + " readed='[]' , ";
                } else {
                    sql = sql + " readed='" + RIUtil.HashToList(rd) + "' , ";
                }
            } else {
                if (isC == 1) {
                    sql = sql + " readed='[]' , ";
                }
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
                if (source.equals("qqb")) {
                    String s = "select id from user where user_qqb like '%" + opt_user + "|%'";
                    opt_user = mysql.query_one(s, "id");
                }
            } else {
                return ErrNo.set(504004);
            }
            String sqls = "update web_notice set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新网页通知", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 504003, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private JSONObject deleteWebNotice(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(504008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(504008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update web_notice set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除网页通知", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 504007, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

}
