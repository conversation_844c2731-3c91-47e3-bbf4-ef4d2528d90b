package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.Lib.TNOAConf;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
public class ExamineAnalysisController {
    private static Logger logger = LoggerFactory.getLogger(ExamineAnalysisController.class);

    @RequestMapping(method = {RequestMethod.POST}, path = {"/examine_analysis"})
    public JSONObject get_examine(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        logger.warn("data---->" + data);
        String opt = "";

        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_rank")) {
                return getRank(data, request.getRemoteAddr());
            } else if (opt.equals("get_result")) {
                return getResult(data, request.getRemoteAddr());
            } else if (opt.equals("get_rank_score")) {
                return getRankScore(data, request.getRemoteAddr());
            } else if (opt.equals("get_short_dis")) {
                return getShortDis(data, request.getRemoteAddr());
            } else if (opt.equals("get_short_det")) {
                return getShortDet(data, request.getRemoteAddr());
            } else if (opt.equals("get_result_year")) {
                return getResultByYear(data, request.getRemoteAddr());
            } else if (opt.equals("get_jz_rank")) {
                return getJzRank(data);
            } else {
                return ErrNo.set(490001);
            }
        } else {
            return ErrNo.set(490001);
        }
    }

    /**
     * 获取评估指标
     *
     * @param data
     * @return
     */
    private JSONObject getShortDet(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            Map<String, String> params = extractAndValidateParams(data, "code", "start_time", "end_time", "rank_type");
            String code = params.get("code");
            String startTime = params.get("start_time");
            String endTime = params.get("end_time");
            String rankType = params.get("rank_type");
            int dateBetween = RIUtil.get2DateBetween(startTime, endTime);
            logger.warn("时间差==={}", dateBetween);
            String sql = String.format("select * from dict where id = '%s'", code);
            logger.warn(sql);
            List<JSONObject> list = mysql.query(sql);
            String time = "";
            String month = endTime.substring(5, 7);
            String sqls = "";
            if (dateBetween > 31 || rankType.equals("1")) {
                time = startTime.substring(0, 4);
                sqls = String.format("select * from kh_res_t where pg_object = '%s' and year = '%s' and month='%s'", code, time, month);
                List<JSONObject> arr = mysql.query(sqls);
                String khMonth = time + month;
                String s = String.format("select * from kh_history where time = '%s'", khMonth);
                logger.warn("历史指标--->{}", s);
                List<JSONObject> configHistory = mysql.query(s);
                getConfigOldInfoById(configHistory, arr);
                back.put("data", arr);
            } else {
                time = startTime.substring(0, 7).replace("-", "");
                String s = String.format("select * from kh_history where time = '%s'", time);
                List<JSONObject> p = mysql.query(s);
                if (p.size() == 0) {
                    return ErrNo.set(490017);
                }
                JSONObject plan = p.get(0).getJSONObject("plan");
                JSONArray fj = plan.getJSONArray("fj");
                JSONArray pcs = plan.getJSONArray("pcs");
                JSONArray zrq = plan.getJSONArray("zrq");
                if (list.size() > 0) {
                    JSONObject one = list.get(0);
                    String type = one.getString("type");
                    if (type.equals("23") || type.equals("24") || type.equals("28")) {
                        code = code.substring(0, 6) + "000000";
                        sqls = String.format("select * from kh_res where pg_object = '%s' and month = '%s' ", code, time);
                        logger.warn(sqls);
                        List<JSONObject> resList = mysql.query(sqls);
                        back.put("data_fj", doNext(fj, time, code, resList, mysql));
                        back.put("data_pcs", doNext(pcs, time, code, resList, mysql));
                        back.put("data_zrq", doNext(zrq, time, code, resList, mysql));
                    } else if (type.equals("25")) {
                        sqls = String.format("select * from kh_res where pg_object = '%s' and month = '%s' ", code, time);
                        logger.warn(sqls);
                        List<JSONObject> resList = mysql.query(sqls);
                        back.put("data_pcs", doNext(pcs, time, code, resList, mysql));
                        back.put("data_zrq", doNext(zrq, time, code, resList, mysql));
                    } else if (type.equals("26")) {
                        sqls = String.format("select * from kh_res where pg_object = '%s' and month = '%s' ", code, time);
                        logger.warn(sqls);
                        List<JSONObject> resList = mysql.query(sqls);
                        back.put("data_zrq", doNext(zrq, time, code, resList, mysql));
                    }
                }
            }
            String optUser = data.getString("opt_user");
            if (optUser != null && !optUser.isEmpty()) {
                UserLog userlog = new UserLog();
                userlog.log(mysql, optUser, "考核-getShortDet", userlog.TYPE_OPERATE, remoteAddr);
            }
        } catch (IllegalArgumentException ex) {
            logger.error("参数校验失败: {}", ex.getMessage());
            return ErrNo.set(490003);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static void getConfigOldInfoById(List<JSONObject> configHistory, List<JSONObject> data) {
        JSONObject plan = configHistory.get(0).getJSONObject("plan");

        JSONArray fj = plan.getJSONArray("fj");
        JSONArray pcs = plan.getJSONArray("pcs");
        fj.addAll(pcs);
        JSONArray zrq = plan.getJSONArray("zrq");
        fj.addAll(zrq);
        for (JSONObject jsonObject : data) {
            String configId = jsonObject.getString("config_id");
            jsonObject.put("config", getConfigInfo(fj, configId));
        }
    }

    private static JSONObject getConfigInfo(JSONArray data, String configId) {
        for (int i = 0; i < data.size(); i++) {
            JSONObject object = data.getJSONObject(i);
            JSONArray next = object.getJSONArray("next");
            for (int i1 = 0; i1 < next.size(); i1++) {
                JSONObject jsonObject = next.getJSONObject(i1);
                String id = jsonObject.getString("id");
                if (configId.equals(id)) {
                    return jsonObject;
                }
            }
        }
        return null;
    }


    /**
     * 获取短板
     *
     * @param data
     * @return
     */
    static JSONObject getShortDis(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            Map<String, String> params = extractAndValidateParams(data, "code", "start_time", "end_time", "rank_type");
            String code = params.get("code");
            String startTime = params.get("start_time");
            String endTime = params.get("end_time");
            String rankType = params.get("rank_type");

            List<JSONObject> resList = null;
            int dateBetween = RIUtil.get2DateBetween(startTime, endTime);
            String time = "";
            String type = RIUtil.dicts.get(code).getString("type");
            String fatherId = RIUtil.dicts.get(code).getString("father_id");

            if (dateBetween > 31 || rankType.equals("1")) {
                time = startTime.substring(0, 4);
                String month = endTime.substring(5, 7);
                if (type.equals("24") || type.equals("28")) {
                    fatherId = fatherId.substring(0, 6) + "000000";
                    String sqls = String.format(
                            "select a.*,b.kh_name,b.full_mark,b.point,b.resp_police from kh_res_t a left join kh_config b on a.config_id = b.id where a.pg_object = '%s' and a.rank_level = -1 and a.year= '%s' and month='%s'",
                            fatherId, time, month);
                    logger.warn(sqls);
                    resList = mysql.query(sqls);
                } else {
                    String sqls = String.format(
                            "select a.*,b.kh_name,b.full_mark,b.point,b.resp_police from kh_res_t a left join kh_config b on a.config_id = b.id where a.pg_object = '%s' and a.rank_level = -1 and a.year= '%s' and month='%s'",
                            code, time, month);
                    logger.warn(sqls);
                    resList = mysql.query(sqls);
                }
            } else {
                time = startTime.substring(0, 7).replace("-", "");
                if (type.equals("24") || type.equals("28")) {
                    fatherId = fatherId.substring(0, 6) + "000000";
                    String sqls = String.format(
                            "select a.*,b.kh_name,b.full_mark,b.point,b.resp_police from kh_res a left join kh_config b on a.config_id = b.id where a.pg_object = '%s' and a.month = '%s' and a.rank_level = -1",
                            fatherId, time);
                    logger.warn(sqls);
                    resList = mysql.query(sqls);
                } else {
                    String sqls = String.format(
                            "select a.*,b.kh_name,b.full_mark,b.point,b.resp_police from kh_res a left join kh_config b on a.config_id = b.id where a.pg_object = '%s' and a.month = '%s' and a.rank_level = -1",
                            code, time);
                    logger.warn(sqls);
                    resList = mysql.query(sqls);
                }
            }
            // 移除 config_id 为 1 的数据
            resList.removeIf(object -> "1".equals(object.getString("config_id")));
            back.put("data", resList);

            String optUser = data.getString("opt_user");
            if (optUser != null && !optUser.isEmpty()) {
                UserLog userlog = new UserLog();
                userlog.log(mysql, optUser, "考核-getShortDis", userlog.TYPE_OPERATE, remoteAddr);
            }
        } catch (IllegalArgumentException ex) {
            logger.error("参数校验失败: {}", ex.getMessage());
            return ErrNo.set(480051);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    private static JSONObject getShortDisYear(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            //组织机构代码

            String code = "";
            String start_time = "";
            String end_time = "";

            if (data.containsKey("code") && data.getString("code").length() > 0) {
                code = data.getString("code");
            } else {
                return ErrNo.set(480051);
            }

            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time");
            } else {
                return ErrNo.set(480051);
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time");
            } else {
                return ErrNo.set(480051);
            }

            String t = start_time.substring(0, 7);
            t = t.replace("-", "");
            String s = "select * from kh_history where time = '" + t + "'";
            logger.warn(s);
            List<JSONObject> p = mysql.query(s);

            ArrayList<JSONObject> conf_fj = new ArrayList<>();
            ArrayList<JSONObject> conf_pcs = new ArrayList<>();
            ArrayList<JSONObject> conf_zrq = new ArrayList<>();
            if (p.size() == 0) {
                return ErrNo.set(490017);
            } else {
                JSONObject plan = p.get(0).getJSONObject("plan");

                String fjConfig = plan.getString("fj_config");
                String[] fjs = fjConfig.split(",");
                for (int j = 0; j < fjs.length; j++) {
                    String s1 = fjs[j];
                    JSONObject object = RIUtil.kh_configs.get(s1);
                    conf_fj.add(object);
                }

                String pcsConfig = plan.getString("pcs_config");
                String[] pcss = pcsConfig.split(",");
                for (int j = 0; j < pcss.length; j++) {
                    String s1 = pcss[j];
                    JSONObject object = RIUtil.kh_configs.get(s1);
                    conf_pcs.add(object);
                }

                String zrqConfig = plan.getString("zrq_config");
                String[] zrqs = zrqConfig.split(",");
                for (int j = 0; j < zrqs.length; j++) {
                    String s1 = zrqs[j];
                    JSONObject object = RIUtil.kh_configs.get(s1);
                    conf_zrq.add(object);
                }
            }

            List<JSONObject> resList = null;
            List<JSONObject> configs = new ArrayList<>();
            List<JSONObject> arrayList = new ArrayList<>();

            int dateBetween = RIUtil.get2DateBetween(start_time, end_time);
            logger.warn("时间差===" + dateBetween);
            String time = "";
            if (dateBetween > 31) {
                time = start_time.substring(0, 4);
                String sqls = "select * from kh_res_t where pg_object = '" + code + "' and rank_level = -1 and year= "
                        + "'" + time + "'";
                logger.warn(sqls);
                resList = mysql.query(sqls);

            } else {
                time = start_time.substring(0, 7);
                time = time.replace("-", "");

                String sqls = "select * from kh_res where pg_object = '" + code + "' and month = '" + time + "'" +
                        "and rank_level = -1";
                logger.warn(sqls);
                resList = mysql.query(sqls);

            }

            String sql = "select * from dict where id = '" + code + "'";
            logger.warn(sql);
            List<JSONObject> list = mysql.query(sql);

            if (list.size() > 0) {
                JSONObject one = list.get(0);
                String type = one.getString("type");
                String father_id = one.getString("father_id");

                if (type.equals("23")) {

                    configs = conf_fj;

                } else if (type.equals("24") || type.equals("28")) {

                    String sqls = "select * from kh_res_t where pg_object = '" + father_id + "' and rank_level = -1";
                    logger.warn(sqls);
                    resList = mysql.query(sqls);

                    configs = conf_fj;

                } else if (type.equals("25")) {
                    configs = conf_pcs;
                } else if (type.equals("26")) {
                    configs = conf_zrq;
                }
            }

            if (configs.size() > 0) {
                for (int i = 0; i < configs.size(); i++) {
                    JSONObject object = configs.get(i);
                    String id = object.getString("id");
                    String kh_name = object.getString("kh_name");
                    if (id.length() > 0) {
                        JSONObject query2 = getScoreByConfigId(resList, id);
                        if (query2 != null) {
                            query2.put("kh_name", kh_name);
                            arrayList.add(query2);
                        }
                    }

                }
            }

            back.put("data", arrayList);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    /**
     * 获取得分排名
     *
     * @param data
     * @return
     */
    private static JSONObject getRankScore(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            Map<String, String> params = extractAndValidateParams(data, "code", "start_time", "end_time", "rank_type");
            String code = params.get("code");
            String startTime = params.get("start_time");
            String endTime = params.get("end_time");
            String rankType = params.get("rank_type");
            int dateBetween = RIUtil.get2DateBetween(startTime, endTime);
            String sql = String.format("select * from dict where id = '%s'", code);
            List<JSONObject> list = mysql.query(sql);
            String time = "";
            if (dateBetween > 31 || rankType.equals("1")) {
                time = startTime.substring(0, 4);
                if (list.size() > 0) {
                    JSONObject one = list.get(0);
                    String type = one.getString("type");
                    String fatherId = one.getString("father_id");
                    String month = endTime.substring(5, 7);
                    List<JSONObject> rank = new ArrayList<>();
                    if (type.equals("24")) {
                        String sqls = String.format(
                                "select * from kh_res_t  where pg_object = '%s'  and config_id = '1' and year = '%s' and month='%s' order by FIELD(pg_object,'320481000000','320413000000','320412000000','320411000000','320402000000','320404000000','320491000000'),pg_object asc",
                                fatherId, time, month);
                        rank = mysql.query(sqls);
                    } else if (type.equals("23") || type.equals("25") || type.equals("26")) {
                        String sqls = String.format(
                                "select * from kh_res_t  where pg_object = '%s' and config_id = '1' and month='%s' and year = '%s' order by FIELD(pg_object,'320481000000','320413000000','320412000000','320411000000','320402000000','320404000000','320491000000'),pg_object asc",
                                code, month, time);
                        rank = mysql.query(sqls);
                    } else if (type.equals("28")) {
                        fatherId = fatherId.substring(0, 6) + "000000";
                        String sqls = String.format(
                                "select * from kh_res_t  where pg_object = '%s'  and config_id = '1' and year = '%s' and month='%s' order by FIELD(pg_object,'320481000000','320413000000','320412000000','320411000000','320402000000','320404000000','320491000000'),pg_object asc",
                                fatherId, time, month);
                        rank = mysql.query(sqls);
                    }
                    for (JSONObject object : rank) {
                        String dictName = RIUtil.dicts.get(object.getString("pg_object")).getString("dict_name");
                        if ("23".equals(type)){
                            object.put("score",0.0);
                        }
                        object.put("pg_object_name", dictName);
                    }
                    back.put("data", rank);
                } else {
                    return ErrNo.set(480029);
                }
            } else {
                time = startTime.substring(0, 7).replace("-", "");
                if (list.size() > 0) {
                    JSONObject one = list.get(0);
                    String type = one.getString("type");
                    String fatherId = one.getString("father_id");
                    List<JSONObject> rank = new ArrayList<>();
                    if (type.equals("24")) {
                        String sqls = String.format(
                                "select * from kh_res  where pg_object = '%s'  and month = '%s' and config_id = '1' order by FIELD(pg_object,'320481000000','320413000000','320412000000','320411000000','320402000000','320404000000','320491000000'),pg_object asc",
                                fatherId, time);
                        rank = mysql.query(sqls);
                    } else if (type.equals("28")) {
                        fatherId = fatherId.substring(0, 6) + "000000";
                        String sqls = String.format(
                                "select * from kh_res  where pg_object = '%s' and month = '%s' and config_id = '1' order by FIELD(pg_object,'320481000000','320413000000','320412000000','320411000000','320402000000','320404000000','320491000000'),pg_object asc",
                                fatherId, time);
                        rank = mysql.query(sqls);
                    } else if (type.equals("23") || type.equals("25") || type.equals("26")) {
                        String sqls = String.format(
                                "select * from kh_res  where pg_object = '%s'  and month = '%s' and config_id = '1' order by FIELD(pg_object,'320481000000','320413000000','320412000000','320411000000','320402000000','320404000000','320491000000'),pg_object asc",
                                code, time);
                        rank = mysql.query(sqls);
                    }
                    for (JSONObject object : rank) {
                        String dictName = RIUtil.dicts.get(object.getString("pg_object")).getString("dict_name");
                        if ("23".equals(type)){
                            object.put("score",0.0);
                        }
                        object.put("pg_object_name", dictName);
                    }
                    back.put("data", rank);
                } else {
                    return ErrNo.set(480029);
                }
            }
        } catch (IllegalArgumentException ex) {
            logger.error("参数校验失败: {}", ex.getMessage());
            return ErrNo.set(480027);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    /**
     * 结果统计
     *
     * @param data
     * @return
     */
    private static JSONObject getResult(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            //组织机构代码
            String code = "";
            String isExp = "";
            String time = "";
            String isNext = "";
            if (data.containsKey("time") && data.getString("time").length() > 0) {
                time = data.getString("time");
            } else {
                return ErrNo.set(480030);
            }

            if (data.containsKey("code") && data.getString("code").length() > 0) {
                code = data.getString("code");
            } else {
                return ErrNo.set(480030);
            }

            if (data.containsKey("isNext") && data.getString("isNext").length() > 0) {
                isNext = data.getString("isNext");
            } else {
                return ErrNo.set(480030);
            }

            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getString("isExp");
            }


            String s = "select * from kh_history where time = '" + time + "'";
            logger.warn(s);
            List<JSONObject> p = mysql.query(s);

            ArrayList<JSONObject> conf_fj = new ArrayList<>();
            ArrayList<JSONObject> conf_pcs = new ArrayList<>();
            ArrayList<JSONObject> conf_zrq = new ArrayList<>();
            if (p.size() == 0) {
//                return ErrNo.set(490017);
                back.put("data", new ArrayList<>());
                return back;
            } else {
                JSONObject plan = p.get(0).getJSONObject("plan");

                JSONArray fj = plan.getJSONArray("fj");
                for (int i = 0; i < fj.size(); i++) {
                    JSONArray next = fj.getJSONObject(i).getJSONArray("next");
                    for (int i1 = 0; i1 < next.size(); i1++) {
                        JSONObject jsonObject = next.getJSONObject(i1);
                        jsonObject.put("pg_object", "fj");
                        conf_fj.add(jsonObject);
                    }
                }

                JSONArray pcs = plan.getJSONArray("pcs");
                for (int i = 0; i < pcs.size(); i++) {
                    JSONArray next = pcs.getJSONObject(i).getJSONArray("next");
                    for (int i1 = 0; i1 < next.size(); i1++) {
                        JSONObject jsonObject = next.getJSONObject(i1);
                        jsonObject.put("pg_object", "pcs");
                        conf_pcs.add(jsonObject);
                    }
                }

                JSONArray zrq = plan.getJSONArray("zrq");
                for (int i = 0; i < zrq.size(); i++) {
                    JSONArray next = zrq.getJSONObject(i).getJSONArray("next");
                    for (int i1 = 0; i1 < next.size(); i1++) {
                        JSONObject jsonObject = next.getJSONObject(i1);
                        jsonObject.put("pg_object", "zrq");
                        conf_zrq.add(jsonObject);
                    }
                }

//                String fjConfig = plan.getString("fj_config");
//                String[] fjs = fjConfig.split(",");
//                for (int j = 0; j < fjs.length; j++) {
//                    JSONObject object = RIUtil.kh_configs.get(fjs[j]);
//                    object.put("pg_object", "fj");
//                    conf_fj.add(object);
//
//                }
//
//                String pcsConfig = plan.getString("pcs_config");
//                String[] pcss = pcsConfig.split(",");
//                for (int j = 0; j < pcss.length; j++) {
//                    JSONObject object = RIUtil.kh_configs.get(pcss[j]);
//                    object.put("pg_object", "pcs");
//                    conf_pcs.add(object);
//                }
//
//                String zrqConfig = plan.getString("zrq_config");
//                String[] zrqs = zrqConfig.split(",");
//                for (int j = 0; j < zrqs.length; j++) {
//                    JSONObject object = RIUtil.kh_configs.get(zrqs[j]);
//                    object.put("pg_object", "zrq");
//                    conf_zrq.add(object);
//                }
            }
            //获取机构信息
            String sql = "select * from dict where id = '" + code + "'";
            List<JSONObject> list = mysql.query(sql);

            String sqls = "";
            List<JSONObject> units = new ArrayList<>();
            List<JSONObject> configs = new ArrayList<>();

            if (list.size() > 0) {
                JSONObject one = list.get(0);
                logger.warn(one.toString());
                String fatherId = one.getString("father_id");

                // 当前用户type
                String type = one.getString("type");

                JSONObject total = new JSONObject();
                total.put("id", "1");
                total.put("kh_name", "总分");
                total.put("resp_dept", "");
                configs.add(total);

                //区分分局、派出所、责任区
                //获取对应的评估指标

                if ("0".equals(isNext)) {

                    if (type.equals("21") || type.equals("22") || type.equals("23") || type.equals("24") || type.equals("27") || type.equals("28")) {

                        sqls = "select * from dict where type = 23 and isdelete =1 and is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);

                        configs.addAll(conf_fj);
                        configs.addAll(conf_pcs);
                        configs.addAll(conf_zrq);

                    } else if (type.equals("25")) {

                        sqls = "select * from dict where type = 25 and father_id = '" + fatherId + "' and isdelete " + "=1" + " and is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);
                        configs.addAll(conf_pcs);
                        configs.addAll(conf_zrq);

                    } else if (type.equals("26")) {

                        sqls = "select * from dict where type = 26 and father_id = '" + fatherId + "' and isdelete " + "=1" + " and is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);
                        configs.addAll(conf_zrq);


                    }
//                    else if (type.equals("28")) {
//
//                        fatherId = fatherId.substring(0, 6) + "000000";
//
//                        sqls = "select * from dict where type = 26 and father_id = '" + fatherId + "' and isdelete
//                        =1 and is_kh = '1'";
//                        logger.warn(sqls);
//                        units = mysql.query(sqls);
//                        configs.addAll(conf_zrq);
//
//                    }
                } else if ("1".equals(isNext)) {
                    if (type.equals("21") || type.equals("22") || type.equals("27")) {

                        sqls = "select * from dict where type = 23 and isdelete =1 and is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);

                        configs.addAll(conf_fj);
                        configs.addAll(conf_pcs);
                        configs.addAll(conf_zrq);

                    } else if (type.equals("23")) {

                        sqls = "select * from dict where type = 25 and father_id = '" + code + "' and isdelete =1 " + "and" + " is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);
                        configs.addAll(conf_pcs);
                        configs.addAll(conf_zrq);

                    } else if (type.equals("24")) {

                        sqls = "select * from dict where type = 25 and father_id = '" + fatherId + "' and isdelete " + "=1" + " and is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);
                        configs.addAll(conf_pcs);
                        configs.addAll(conf_zrq);

                    } else if (type.equals("25")) {

                        sqls = "select * from dict where type = 26 and father_id = '" + code + "' and isdelete =1 " + "and" + " is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);

                        configs.addAll(conf_zrq);

                    } else if (type.equals("26")) {

                        sqls = "select * from dict where type = 26 and father_id = '" + fatherId + "' and isdelete " + "=1" + " and is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);

                        configs.addAll(conf_zrq);

                    } else if (type.equals("28")) {

                        fatherId = fatherId.substring(0, 6) + "000000";

                        sqls = "select * from dict where type = 25 and father_id = '" + fatherId + "' and isdelete " + "=1" + " and is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);

                        configs.addAll(conf_zrq);

                    }
                }


                List<JSONObject> result = new ArrayList<>();

                sqls = "select * from kh_res where month = '" + time + "'";
                logger.warn(sqls);
                List<JSONObject> res = mysql.query(sqls);

                for (int i = 0; i < configs.size(); i++) {
                    JSONObject ob = new JSONObject();

                    JSONObject config = configs.get(i);
                    String id = config.getString("id");
                    String khName = config.getString("kh_name");
                    String pg_object = config.getString("pg_object");
                    String resp_dept = config.getString("resp_dept");
                    // logger.warn(resp_dept);
                    List<JSONObject> query = getResByConfigId(res, id);

                    ob.put("configId", id);
                    ob.put("name", khName);
                    ob.put("pg_object", pg_object);
                    try {
                        ob.put("resp_dept_name", RIUtil.dicts.get(resp_dept).getString("dict_name"));
                    } catch (Exception e) {
                        ob.put("resp_dept_name", "");
                    }

                    ArrayList<JSONObject> arr = new ArrayList<>();

                    for (int j = 0; j < units.size(); j++) {
                        JSONObject unit = units.get(j);
                        String unitId = unit.getString("id");
                        String unitName = unit.getString("dict_name");
                        JSONObject sc = getScoreByUnitId(query, unitId);

                        if (sc != null) {
                            sc.put("unitName", unitName);
                            if (unitId.endsWith("000000") && "1".equals(id)){
                                sc.put("score","-");
                            }
                            arr.add(sc);
                        }
                    }
                    ob.put("unit", arr);
                    result.add(ob);
                }

                String name = data.getString("name");
                String pgObject = data.getString("pg_object");
                String respDeptName = data.getString("resp_dept_name");
                if (StrUtil.isNotBlank(name) || StrUtil.isNotBlank(pgObject) || StrUtil.isNotBlank(respDeptName)) {
                    List<JSONObject> filterResult = new ArrayList<>();
                    for (JSONObject jsonObject : result) {
                        String khName = jsonObject.getString("name");
                        String pgObject1 = jsonObject.getString("pg_object");
                        String respDeptName1 = jsonObject.getString("resp_dept_name");
                        if (StrUtil.isNotBlank(name) && !khName.contains(name)) {
                            continue;
                        }
                        try {
                            if (StrUtil.isNotBlank(pgObject) && !pgObject1.equals(pgObject)) {
                                continue;

                            }
                        } catch (Exception ex) {
                            System.out.println(jsonObject);
                        }
                        if (StrUtil.isNotBlank(respDeptName) && !respDeptName1.contains(respDeptName)) {
                            continue;
                        }
                        if ("总分".equals(khName)) {
                            continue;
                        }
                        filterResult.add(jsonObject);
                    }
                    result = filterResult;
                }

                back.put("data", result);
                int file_id = -1;
                if (isExp.equals("1")) {
                    file_id = exportResult(back);
                }

                back.put("file_id", file_id);
            }

            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-getResult", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480031);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    private static JSONObject getResultByYear(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            //组织机构代码
            String code = "";
            String isExp = "";
            String time = "";
            String isNext = "";
            if (data.containsKey("time") && data.getString("time").length() > 0) {
                time = data.getString("time");
            } else {
                return ErrNo.set(480030);
            }

            if (data.containsKey("code") && data.getString("code").length() > 0) {
                code = data.getString("code");
            } else {
                return ErrNo.set(480030);
            }

            if (data.containsKey("isNext") && data.getString("isNext").length() > 0) {
                isNext = data.getString("isNext");
            } else {
                return ErrNo.set(480030);
            }

            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getString("isExp");
            }


            String s = "select * from kh_history where time like '" + time + "%'";
            logger.warn(s);
            List<JSONObject> p = mysql.query(s);

            ArrayList<JSONObject> conf_fj = new ArrayList<>();
            ArrayList<JSONObject> conf_pcs = new ArrayList<>();
            ArrayList<JSONObject> conf_zrq = new ArrayList<>();
            if (p.size() == 0) {
//                return ErrNo.set(490017);
                back.put("data", new ArrayList<>());
                return back;
            } else {
                Set<String> fjSet = new HashSet<>();
                Set<String> pcsSet = new HashSet<>();
                Set<String> zrqSet = new HashSet<>();

                for (JSONObject jsonObject : p) {
                    JSONObject plan = jsonObject.getJSONObject("plan");
                    String fjConfig = plan.getString("fj_config");
                    String[] fjs = fjConfig.split(",");
                    for (String fj : fjs) {
                        fjSet.add(fj);
                    }

                    String pcsConfig = plan.getString("pcs_config");
                    String[] pcss = pcsConfig.split(",");
                    for (String pcs : pcss) {
                        pcsSet.add(pcs);
                    }

                    String zrqConfig = plan.getString("zrq_config");
                    String[] zrqs = zrqConfig.split(",");
                    for (String zrq : zrqs) {
                        zrqSet.add(zrq);
                    }


                }

                for (String fj : fjSet) {
                    JSONObject object = RIUtil.kh_configs.get(fj);
                    object.put("pg_object", "fj");
                    conf_fj.add(object);
                }

                for (String pcs : pcsSet) {
                    JSONObject object = RIUtil.kh_configs.get(pcs);
                    object.put("pg_object", "pcs");
                    conf_pcs.add(object);
                }

                for (String zrq : zrqSet) {
                    JSONObject object = RIUtil.kh_configs.get(zrq);
                    object.put("pg_object", "zrq");
                    conf_zrq.add(object);
                }
            }
            //获取机构信息
            String sql = "select * from dict where id = '" + code + "'";
            List<JSONObject> list = mysql.query(sql);

            String sqls = "";
            List<JSONObject> units = new ArrayList<>();
            List<JSONObject> configs = new ArrayList<>();

            if (list.size() > 0) {
                JSONObject one = list.get(0);
                logger.warn(one.toString());
                String fatherId = one.getString("father_id");

                // 当前用户type
                String type = one.getString("type");

                JSONObject total = new JSONObject();
                total.put("id", "1");
                total.put("kh_name", "总分");
                total.put("resp_dept", "");
                configs.add(total);

                //区分分局、派出所、责任区
                //获取对应的评估指标

                if ("0".equals(isNext)) {

                    if (type.equals("21") || type.equals("22") || type.equals("23") || type.equals("24") || type.equals("27") || type.equals("28")) {

                        sqls = "select * from dict where type = 23 and isdelete =1 and is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);

                        configs.addAll(conf_fj);
                        configs.addAll(conf_pcs);
                        configs.addAll(conf_zrq);

                    } else if (type.equals("25")) {

                        sqls = "select * from dict where type = 25 and father_id = '" + fatherId + "' and " +
                                "isdelete " + "=1" + " and is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);
                        configs.addAll(conf_pcs);
                        configs.addAll(conf_zrq);

                    } else if (type.equals("26")) {

                        sqls = "select * from dict where type = 26 and father_id = '" + fatherId + "' and " +
                                "isdelete " + "=1" + " and is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);
                        configs.addAll(conf_zrq);


                    }

                } else if ("1".equals(isNext)) {
                    if (type.equals("21") || type.equals("22") || type.equals("27")) {

                        sqls = "select * from dict where type = 23 and isdelete =1 and is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);

                        configs.addAll(conf_fj);
                        configs.addAll(conf_pcs);
                        configs.addAll(conf_zrq);

                    } else if (type.equals("23")) {

                        sqls = "select * from dict where type = 25 and father_id = '" + code + "' and isdelete =1" +
                                " " + "and" + " is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);
                        configs.addAll(conf_pcs);
                        configs.addAll(conf_zrq);

                    } else if (type.equals("24")) {

                        sqls = "select * from dict where type = 25 and father_id = '" + fatherId + "' and " +
                                "isdelete " + "=1" + " and is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);
                        configs.addAll(conf_pcs);
                        configs.addAll(conf_zrq);

                    } else if (type.equals("25")) {

                        sqls = "select * from dict where type = 26 and father_id = '" + code + "' and isdelete =1" +
                                " " + "and" + " is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);

                        configs.addAll(conf_zrq);

                    } else if (type.equals("26")) {

                        sqls = "select * from dict where type = 26 and father_id = '" + fatherId + "' and " +
                                "isdelete " + "=1" + " and is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);

                        configs.addAll(conf_zrq);

                    } else if (type.equals("28")) {

                        fatherId = fatherId.substring(0, 6) + "000000";

                        sqls = "select * from dict where type = 25 and father_id = '" + fatherId + "' and " +
                                "isdelete " + "=1" + " and is_kh = '1'";
                        logger.warn(sqls);
                        units = mysql.query(sqls);

                        configs.addAll(conf_zrq);

                    }
                }


                ArrayList<JSONObject> result = new ArrayList<>();

                sqls = "select * from kh_res where month like '" + time + "%'";
                logger.warn(sqls);
                List<JSONObject> res = mysql.query(sqls);

                for (int i = 0; i < configs.size(); i++) {
                    JSONObject ob = new JSONObject();

                    JSONObject config = configs.get(i);
                    String id = config.getString("id");
                    String khName = config.getString("kh_name");
                    String pg_object = config.getString("pg_object");
                    String resp_dept = config.getString("resp_dept");
                    // logger.warn(resp_dept);
                    List<JSONObject> query = getResByConfigId(res, id);

                    ob.put("configId", id);
                    ob.put("name", khName);
                    ob.put("pg_object", pg_object);
                    try {
                        ob.put("resp_dept_name", RIUtil.dicts.get(resp_dept).getString("dict_name"));
                    } catch (Exception e) {
                        ob.put("resp_dept_name", "");
                    }

                    ArrayList<JSONObject> arr = new ArrayList<>();
//                    logger.warn("年排名测试");

                    for (int j = 0; j < units.size(); j++) {
                        JSONObject unit = units.get(j);
                        String unitId = unit.getString("id");
                        String unitName = unit.getString("dict_name");
                        JSONObject sc = getScoreByUnitIdByYearByAvg(query, unitId);
//                        logger.warn("年排名测试结果{}",sc);
                        if (sc != null) {
                            sc.put("unitName", unitName);
                            arr.add(sc);
                        }
                    }
                    ob.put("unit", arr);
                    result.add(ob);
                }


                back.put("data", result);
                int file_id = -1;
                if (isExp.equals("1")) {
                    file_id = exportResult(back);
                }

                back.put("file_id", file_id);
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480031);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    static JSONObject getRank(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            Map<String, String> params = extractAndValidateParams(data, "code", "start_time", "end_time", "rank_type");
            String  code = params.get("code");
            String startTime = params.get("start_time");
            String endTime = params.get("end_time");
            String rankType = params.get("rank_type");
            int dateBetween = RIUtil.get2DateBetween(startTime, endTime);
            String time = "";
            String month = "0";
            String pcsType = data.getString("pcs_type");
            if (StrUtil.isBlank(pcsType) && "320400000000".equals(code)) {
                back.put("errno", 9999);
                back.put("error","请选择分局");
                return back;
            }
            if (dateBetween > 31 || rankType.equals("1")) {
                time = startTime.substring(0, 4);
                month = endTime.substring(5, 7);
                String sql = String.format("select * from dict where id = '%s'", code);
                List<JSONObject> list = mysql.query(sql);


                if (list.size() > 0) {
                    JSONObject one = list.get(0);
                    String fatherId = one.getString("father_id");
                    String type = one.getString("type");
                    List<JSONObject> list2 = new ArrayList<>();
                    List<JSONObject> list2Next = new ArrayList<>();
                    if (StrUtil.isNotBlank(pcsType)) {
                        list2 = mysql.query("SELECT id FROM `kh_zf_pcs_type` where type2 = '" + pcsType + "'");
                    } else if (type.equals("21") || type.equals("22") || type.equals("23") || type.equals("27") || type.equals("28")) {
                        sql = "select * from dict where type = '23' and is_kh = '1'";
                        list2 = mysql.query(sql);
                        sql = String.format("select * from dict where type = '25' and  father_id =  '%s' and is_kh = '1'", code);
                        logger.warn(sql);
                        list2Next = mysql.query(sql);
                    } else if (type.equals("24")) {
                        sql = "select * from dict where type = '23' and is_kh = '1'";
                        list2 = mysql.query(sql);
                        sql = String.format("select * from dict where type = 25 and  father_id =  '%s' and is_kh = '1'", fatherId);
                        logger.warn(sql);
                        list2Next = mysql.query(sql);
                    } else if (type.equals("25")) {
                        sql = String.format("select * from dict where type = '25' and father_id = '%s' and is_kh = '1'", fatherId);
                        list2 = mysql.query(sql);
                        sql = String.format("select * from dict where  type = 26 and father_id =  '%s' and is_kh = '1'", code);
                        logger.warn(sql);
                        list2Next = mysql.query(sql);
                    } else if (type.equals("26")) {
                        sql = String.format("select * from dict where type = '26' and father_id = '%s' and is_kh = '1'", fatherId);
                        list2 = mysql.query(sql);
                    }
                    if (list2.size() > 0) {
                        String s = list2.stream().map(obj -> String.format("'%s'", obj.getString("id"))).collect(Collectors.joining(", ", "( ", ") "));
                        String sqls = String.format("select * from kh_res_t  where year = '%s' and month='%s' and pg_object in %s and  config_id = '1'  order by FIELD(pg_object,'320481000000','320413000000','320412000000','320411000000','320402000000','320404000000','320491000000'),pg_object asc", time, month, s);
                        logger.warn(sqls);
                        List<JSONObject> rank = mysql.query(sqls);
                        for (JSONObject object : rank) {
                            String dictName = RIUtil.dicts.get(object.getString("pg_object")).getString("remark");
                            object.put("pg_object_name", dictName);
                        }
                        List<JSONObject> rankNext = new ArrayList<>();
                        if (list2Next.size() > 0) {
                            String n = list2Next.stream().map(obj -> String.format("'%s'", obj.getString("id"))).collect(Collectors.joining(", ", "( ", ") "));
                            sqls = String.format("select * from kh_res_t where year = '%s' and month='%s' and pg_object in %s  and config_id = '1' order by FIELD(pg_object,'320481000000','320413000000','320412000000','320411000000','320402000000','320404000000','320491000000'),pg_object asc", time, month, n);
                            logger.warn(sqls);
                            rankNext = mysql.query(sqls);
                            for (JSONObject object : rankNext) {
                                String dictName = RIUtil.dicts.get(object.getString("pg_object")).getString("remark");
                                object.put("pg_object_name", dictName);
                            }
                        }
                        back.put("data", rank);
                        back.put("data_next", rankNext);
                    }
                } else {
                    return ErrNo.set(480029);
                }
            } else {
                time = startTime.substring(0, 7).replace("-", "");
                logger.warn("时间：{}", time);
                String sql = String.format("select * from dict where id = '%s'", code);
                logger.warn(sql);
                List<JSONObject> list = mysql.query(sql);
                if (list.size() > 0) {
                    JSONObject one = list.get(0);
                    String type = one.getString("type");
                    String fatherId = one.getString("father_id");
                    List<JSONObject> list2 = new ArrayList<>();
                    List<JSONObject> list2Next = new ArrayList<>();
                    if (StrUtil.isNotBlank(pcsType)) {
                        list2 = mysql.query("SELECT id FROM `kh_zf_pcs_type` where type2 = '" + pcsType + "'");
                    } else if (type.equals("21") || type.equals("22") || type.equals("23") || type.equals("27") || type.equals("28")) {
                        sql = "select * from dict where type = '23'";
                        list2 = mysql.query(sql);
                        sql = String.format("select * from dict where type = 25 and  father_id =  '%s' and is_kh = '1'", code);
                        list2Next = mysql.query(sql);
                    } else if (type.equals("24")) {
                        sql = "select * from dict where type = '23'";
                        list2 = mysql.query(sql);
                        sql = String.format("select * from dict where type = 25 and  father_id =  '%s' and is_kh = '1'", fatherId);
                        list2Next = mysql.query(sql);
                    } else if (type.equals("25")) {
                        sql = String.format("select * from dict where type = '25' and father_id = '%s' and is_kh = '1'", fatherId);
                        list2 = mysql.query(sql);
                        sql = String.format("select * from dict where  type = 26 and father_id =  '%s' and is_kh = '1'", code);
                        list2Next = mysql.query(sql);
                    } else if (type.equals("26")) {
                        sql = String.format("select * from dict where type = '26' and father_id = '%s' and is_kh = '1'", fatherId);
                        list2 = mysql.query(sql);
                    }
                    if (list2.size() > 0) {
                        String s = list2.stream().map(obj -> String.format("'%s'", obj.getString("id"))).collect(Collectors.joining(", ", "( ", ") "));
                        String sqls = String.format("select * from kh_res where pg_object in %s and month = '%s' and config_id = '1'  order by FIELD(pg_object,'320481000000','320413000000','320412000000','320411000000','320402000000','320404000000','320491000000'),pg_object asc", s, time);
                        logger.warn(sqls);
                        List<JSONObject> rank = mysql.query(sqls);
                        for (JSONObject object : rank) {
                            String dictName = RIUtil.dicts.get(object.getString("pg_object")).getString("remark");
                            object.put("pg_object_name", dictName);
                        }
                        List<JSONObject> rankNext = new ArrayList<>();
                        if (list2Next.size() > 0) {
                            String n = list2Next.stream().map(obj -> String.format("'%s'", obj.getString("id"))).collect(Collectors.joining(", ", "( ", ") "));
                            sqls = String.format("select * from kh_res where pg_object in %s  and month = '%s' and config_id = '1'  order by FIELD(pg_object,'320481000000','320413000000','320412000000','320411000000','320402000000','320404000000','320491000000'),pg_object asc", n, time);
                            logger.warn(sqls);
                            rankNext = mysql.query(sqls);
                            for (JSONObject object : rankNext) {
                                String dictName = RIUtil.dicts.get(object.getString("pg_object")).getString("remark");
                                object.put("pg_object_name", dictName);
                            }
                        }
                        back.put("data", rank);
                        back.put("data_next", rankNext);
                    }
                } else {
                    return ErrNo.set(480029);
                }
            }
        } catch (IllegalArgumentException ex) {
            logger.error("参数校验失败: {}", ex.getMessage());
            return ErrNo.set(480027);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    private JSONArray doNext(JSONArray data, String time, String org, List<JSONObject> det, InfoModelHelper mysql) {
        for (int i = 0; i < data.size(); i++) {
            JSONObject obj = data.getJSONObject(i);
            if (obj.containsKey("next") && obj.getJSONArray("next").size() > 0) {
                doNext(obj.getJSONArray("next"), time, org, det, mysql);
            } else {
                if (obj.containsKey("label") && obj.getBoolean("label")) {
                    String id = obj.getString("id");

                    JSONObject sc = getDetByConfigdAndOrgAndMonth(det, id, org, time);

                    if (sc != null) {
                        obj.put("score", sc.getString("score"));
                        obj.put("rank", sc.getString("rank"));
                        obj.put("rank_fj", sc.getString("rank_fj"));
                        obj.put("rank_pcs", sc.getString("rank_pcs"));
                        obj.put("rank_level", sc.getString("rank_level"));
                        obj.put("hb", sc.getString("hb"));
                    }
                }
            }
        }
        return data;
    }

    private static JSONObject getDetByConfigdAndOrgAndMonth(List<JSONObject> arrayList, String id, String org,
                                                            String time) {

        for (int i = 0; i < arrayList.size(); i++) {
            JSONObject object = arrayList.get(i);
            String config_id = object.getString("config_id");
            String org_id = object.getString("pg_object");
            String t = object.getString("month");
            if (config_id.equals(id) && org_id.equals(org) && t.equals(time)) {
                return object;
            }
        }
        return null;
    }

    private static JSONObject getScoreByConfigId(List<JSONObject> arrayList, String id) {

        for (int i = 0; i < arrayList.size(); i++) {
            JSONObject object = arrayList.get(i);
            String configId = object.getString("config_id");
            if (configId.equals(id)) {
                return object;
            }
        }

        return null;
    }

    private static List<JSONObject> getResByConfigId(List<JSONObject> arrayList, String id) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < arrayList.size(); i++) {
            JSONObject object = arrayList.get(i);
            String configId = object.getString("config_id");
            if (configId.equals(id)) {
                back.add(object);
            }
        }
        return back;
    }

    private static JSONObject getScoreByUnitId(List<JSONObject> arrayList, String id) {

        for (int i = 0; i < arrayList.size(); i++) {
            JSONObject object = arrayList.get(i);
            String pgObject = object.getString("pg_object");
            if (pgObject.equals(id)) {
                return object;
            }
        }

        return null;
    }

    // 2024.01.13
    private static JSONObject getScoreByUnitIdByYearByAvg(List<JSONObject> arrayList, String id) {

        ArrayList<JSONObject> list = new ArrayList<>();
        JSONObject back = new JSONObject();

        for (int i = 0; i < arrayList.size(); i++) {
            JSONObject object = arrayList.get(i);
            String pgObject = object.getString("pg_object");
            if (pgObject.equals(id)) {
                back = object;
                list.add(object);
            }
        }

        back.put("rank", 0);
        back.put("rank_fj", 0);
        back.put("rank_pcs", 0);
        back.put("rank_level", 0);
        back.put("hb", 0);
        back.put("month", 0);

        //计算 list 中 字段所有 score 的平均分
        double sum = 0;
        for (int i = 0; i < list.size(); i++) {
            JSONObject object = list.get(i);
            String score = object.getString("score");
            sum += Double.parseDouble(score);
        }

//        logger.warn("sum->" + sum);
//        logger.warn("list.size()->" + list.size());
        // list 可能为 0 需要判断处理
        if (list.isEmpty()) {
            back.put("score", sum);
        } else {
            double avg = sum / list.size();
            back.put("score", new BigDecimal(avg).setScale(3, BigDecimal.ROUND_HALF_UP));
        }

        back.put("sum", sum);
        back.put("size", list.size());


        return back;
    }

    private static int exportResult(JSONObject object) {
        String FileName = "考核结果统计_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        String filePath =
                new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

        String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;

        logger.warn(endPath);

        FileOutputStream fos = null;
        SXSSFWorkbook sxssfWorkbook = null;
        InfoModelHelper info = null;
        try {

            info = InfoModelPool.getModel();
            JSONArray data = object.getJSONArray("data");

            JSONArray unit = data.getJSONObject(0).getJSONArray("unit");

            // 获取SXSSFWorkbook实例
            sxssfWorkbook = new SXSSFWorkbook();
            Sheet sheet = sxssfWorkbook.createSheet("结果统计");

            // 冻结最左边的两列、冻结最上面的一行
            // 即：滚动横向滚动条时，左边的第一、二列固定不动;滚动纵向滚动条时，上面的第一行固定不动。
            sheet.createFreezePane(1, 1);

            // 创建第一行,作为header表头 0为第一行
            Row header = sheet.createRow(0);
            // 循环创建header单元格(根据实际情况灵活创建即可)
            header.createCell(0).setCellValue("方案");
            sheet.setColumnWidth(0, 5000);
            for (int i = 0; i < unit.size(); i++) {
                Cell cell = header.createCell(i + 1);
                JSONObject ob = unit.getJSONObject(i);
                String unitName = ob.getString("unitName");

                cell.setCellValue(unitName);
                sheet.setColumnWidth(i + 1, 3500);

            }
            // 遍历创建行,导出数据
            for (int rownum = 0; rownum < data.size(); rownum++) {
                Row row = sheet.createRow(rownum + 1);
                // 循环创建单元格
                JSONArray units = data.getJSONObject(rownum).getJSONArray("unit");
                for (int cellnum = 0; cellnum < units.size() + 1; cellnum++) {
                    Cell cell = row.createCell(cellnum);
                    if (cellnum == 0) {
                        String khName = data.getJSONObject(rownum).getString("name");
                        cell.setCellValue(khName);
                    } else {
                        JSONObject ob = units.getJSONObject(cellnum - 1);
                        String score = ob.getString("score");
                        cell.setCellValue(score);
                    }
                }
            }
            // 在后面设置sheet
//            setSheet(sheet);
            fos = new FileOutputStream(endPath);
            sxssfWorkbook.write(fos);

            String sql =
                    "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "','" + FileName + "',999)";
            logger.warn(sql);
            info.update(sql);
            sql = "select * from upload where file_name='" + FileName + "'";
            List<JSONObject> result = info.query(sql);
            JSONObject xJsonObject = result.get(0);
            int id = xJsonObject.getIntValue("id");

            logger.warn("id->" + id);
           /* BashExecutor bash = new BashExecutor();
            String cmd = "cp -r " + endPath + " " + TNOAConf.get("file", "bk_path");
            logger.warn(cmd);
            bash.exec(cmd, -1, true);*/
            String endPoint = "http://10.34.251.34:50101";
            String ak = "Q7D3OUVIRBEZEB4RWVSJ";
            String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
            String bucketName = "obs-qjjc-tyyh";
            ObsServer obsServ = new ObsServer();
            String obsFileName = "hl/" + filePath + FileName;
            System.out.println(obsFileName);
            boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
            logger.warn(obsFileName + "-->" + ret);
            return id;

        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        } finally {
            InfoModelPool.putModel(info);
            try {
                if (sxssfWorkbook != null) {
                    // dispose of temporary files backing this workbook on disk -> 处
                    //     理SXSSFWorkbook导出excel时，产生的临时文件
                    sxssfWorkbook.dispose();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 警种排名
     *
     * @param data
     * @return
     * @throws Exception
     */
    public static JSONObject getJzRank(JSONObject data) throws Exception {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String orgCode = "320400000000";
        try {
            mysql = InfoModelPool.getModel();
            String time = data.getString("time");
            if (StrUtil.isNotBlank(data.getString("code"))) {
                orgCode = data.getString("code");
            }
            String sql = "";

            JSONObject result = new JSONObject();
            if (StrUtil.isNotBlank(orgCode)) {
                JSONObject orgInfo = RIUtil.dicts.get(orgCode);
                String type = orgInfo.getString("type");
                if ("23".equals(type) || "24".equals(type)) {
                    sql += " and type = '25' and organization like '" + orgCode.substring(0, 6) + "%' ";
                } else if ("25".equals(type) || "26".equals(type)) {
                    sql += " and type = '26' and organization like '" + orgCode.substring(0, 8) + "%' ";
                } else if ("21".equals(type) || "22".equals(type) || "27".equals(type)) {
                    sql += " and type = '23' and organization like '" + orgCode.substring(0, 4) + "%' ";
                }
            }

            String baseSql = "select * from kh_jz_rank where month = '" + time + "' " + sql;

            logger.warn("警种排名baseSql->" + baseSql);
            List<JSONObject> query = mysql.query(baseSql);
            for (JSONObject jsonObject : query) {
                jsonObject.put("jz_name", RIUtil.dicts.get(jsonObject.getString("jz_id")).getString("dict_name"));
                String organization = jsonObject.getString("organization");
                JSONObject object = RIUtil.dicts.get(organization);
                if (object != null) {
                    jsonObject.put("org_name", object.getString("dict_name"));
                }
            }
            Set<String> jzId = query.stream().map(q -> q.getString("jz_id")).collect(Collectors.toSet());
            List<JSONObject> headList = new ArrayList<>();
            headList.add(new JSONObject().fluentPut("key", "name").fluentPut("value", "单位名称"));
            headList.add(new JSONObject().fluentPut("key", "code").fluentPut("value", "单位代码"));
            for (String s : jzId) {
                JSONObject head = new JSONObject();
                head.put("key", s);
                head.put("value", RIUtil.dicts.get(s).getString("dict_name"));
                headList.add(head);
            }

            Map<String, List<JSONObject>> collect = query.stream().collect(Collectors.groupingBy(u -> u.getString(
                    "organization")));

            List<JSONObject> body = new ArrayList<>();
            for (Map.Entry<String, List<JSONObject>> entry : collect.entrySet()) {
                JSONObject object = new JSONObject();
                object.put("name", RIUtil.dicts.get(entry.getKey()).getString("dict_name"));
                object.put("code", entry.getKey());
                object.put("type", RIUtil.dicts.get(entry.getKey()).getString("type"));
                List<JSONObject> list = entry.getValue();
                Map<String, List<JSONObject>> listMap = list.stream().collect(Collectors.groupingBy(u -> u.getString(
                        "jz_id")));
                for (Map.Entry<String, List<JSONObject>> stringListEntry : listMap.entrySet()) {
                    String key = stringListEntry.getKey();
                    JSONObject object1 = stringListEntry.getValue().get(0);
                    object.put(key, object1);
                }
                body.add(object);
            }
            result.put("head", headList);
            result.put("body", body);
            back.put("data", result);
            return back;
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    // 参数校验与提取工具方法
    private static Map<String, String> extractAndValidateParams(JSONObject data, String... keys) throws IllegalArgumentException {
        Map<String, String> params = new HashMap<>();
        for (String key : keys) {
            if (!data.containsKey(key) || data.getString(key).isEmpty()) {
                throw new IllegalArgumentException("缺少参数: " + key);
            }
            params.put(key, data.getString(key));
        }
        return params;
    }
}
