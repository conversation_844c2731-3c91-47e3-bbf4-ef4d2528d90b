package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.Utils.ReadFilesbak;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.Lib.RIUtil1;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
public class KnowController {
    private static Logger logger = LoggerFactory.getLogger(KnowController.class);

    @RequestMapping(method = {RequestMethod.POST}, path = {"/know"})
    public static JSONObject get_know(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        String ip = request.getRemoteAddr();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("know_get")) {
                return getKnow(data);
            } else if (opt.equals("know_create")) {
                logger.warn("know--->" + data.toString());
                return createKnow(data, ip);
            } else if (opt.equals("know_update")) {
                logger.warn("know--->" + data.toString());
                return updateKnow(data, ip);
            } else if (opt.equals("know_delete")) {
                return deleteKnow(data, ip);
            } else if (opt.equals("know_get_type2")) {
                return getType2Know(data);
            } else {
                return ErrNo.set(447009);
            }
        } else {
            return ErrNo.set(447009);
        }
    }

    private static JSONObject getType2Know(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String count = "";
            String type2 = "";
            if (data.containsKey("type_2") && data.getString("type_2").length() > 0) {
                type2 = data.getString("type_2");
            } else {
                return ErrNo.set(401001);
            }

            String sql =
                    "select count(decode(type_2,'" + RIUtil.enContent + "')) where decode(type_2,'" + RIUtil.enContent + "')='" + type2 + " and isdelete=1";
            logger.warn(sql);
            back.put("count", mysql.query_count(sql));
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(447001);
            //401004
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;

    }


    //******CREATE*******
    private static JSONObject createKnow(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String file_num = "";
            String file_name = "";
            String size = "";
            String type = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String content = "";
            String file_id = "";
            String type_2 = "";
            String isdelete = "1";
            String unit = "";
            String s = "select file_num from know order by file_num desc limit 1";
            String last_num = mysql.query_one(s, "file_num");
            String cover = "";
            int last = 0;
            if (last_num.equals("")) {
                last_num = "0";
            }
            last = Integer.parseInt(last_num) + 1;
            if (last == 999) {
                file_num = "001";
            }
            if (last < 10) {
                file_num = "00" + last;
            }
            if (last >= 10 && last < 100) {
                file_num = "0" + last;
            }
            if (last > 100) {
                file_num = String.valueOf(last);
            }
            if (data.containsKey("file_name") && data.getString("file_name").length() > 0) {
                file_name = data.getString("file_name");
            } else {
                return ErrNo.set(447002);
            }
            if (data.containsKey("size") && data.getString("size").length() > 0) {
                size = data.getString("size");
            }

            if (data.containsKey("type_2") && data.getString("type_2").length() > 0) {
                type_2 = data.getString("type_2");
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            }

            if (data.containsKey("cover") && data.getString("cover").length() > 0) {
                cover = data.getString("cover");
            }

            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
            } else {
                return ErrNo.set(447002);
            }

            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                if (unit.length() == 0) {
                    unit = RIUtil1.users1.get(create_user).getString("unit");
                }
            } else {
                return ErrNo.set(447002);
            }


            if (data.containsKey("file_id") && data.getString("file_id").length() > 0) {
                file_id = data.getString("file_id");
                content = ReadFilesbak.getContent(file_id);
            } else {
                return ErrNo.set(447002);
            }


            String sqls = "insert know (file_num,file_name,size,create_user,create_time,file_id,isdelete,content," +
                    "type_2,type,unit,cover) values('" + file_num + "',encode('" + file_name + "','" + RIUtil.enName + "')," + "'" + size + "','" + create_user + "','" + create_time + "','" + file_id + "','" + isdelete + "'," + "encode('" + content + "','" + RIUtil.enContent + "'),encode('" + type_2 + "','" + RIUtil.enContent + "'),'" + type + "','" + unit + "','" + cover + "')";
            logger.warn(sqls);
            mysql.update(sqls);


            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建工作宝典", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(470004);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private static JSONObject getKnow(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String type = "";
            String type_2 = "";
            String file_num = "";
            String file_name = "";
            String create_user = "";
            String create_time_start = "";
            String create_time_end = "";
            String unit = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' and ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type='" + type + "' and ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                //  sql = sql + " unit='" + unit + "' and ";
            }
            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
                create_time_start = data.getString("create_time_start").replace("|", " ");
                sql = sql + " create_time>='" + create_time_start + "' and ";
            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end").replace("|", " ");
                sql = sql + " create_time<='" + create_time_end + "' and ";
            }

            if (data.containsKey("file_num") && data.getString("file_num").length() > 0) {
                file_num = data.getString("file_num");
                sql = sql + " file_num like '%" + file_num + "%' and ";
            }
            if (data.containsKey("file_name") && data.getString("file_name").length() > 0) {
                file_name = data.getString("file_name");
                sql = sql + " (decode(file_name,'" + RIUtil.enName + "') like '%" + file_name + "%' or decode" +
                        "(content,'" + RIUtil.enContent + "') like '%" + file_name + "%') and ";
            }
            if (data.containsKey("type_2") && data.getString("type_2").length() > 0) {
                type_2 = data.getString("type_2");
                sql = sql + " (decode(type_2,'" + RIUtil.enContent + "'))='" + type_2 + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }


            String sqls =
                    "select id,decode(type_2,'" + RIUtil.enContent + "') as type_2,file_num,decode(file_name,'" + RIUtil.enName + "') as file_name,size,create_user,create_time,file_id ,decode(content,'" + RIUtil.enContent + "') as content ,cover from know where 1=1 and " + sql + " isdelete=1  limit " + limit + " offset " + limit * (page - 1);
            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);

            back.put("data", RelaInfo(list, mysql, file_name));
            sqls = "select count(id) as count from know where 1=1 and " + sql + " isdelete=1; ";
            logger.warn(sqls);
            back.put("count", mysql.query_count(sqls));

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(447005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private static Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql, String search) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String create_user = one.getString("create_user");
            JSONObject cu = RIUtil1.users1.get(create_user);
            one.put("create_user", cu);
            String file_id = one.getString("file_id");
            String file_name = RIUtil.IdToName(file_id, mysql, "file_name", "upload");
            one.put("org_name", file_name);
            String content = one.getString("content");
            int len = content.length();
            int s = content.indexOf(search);
            int start = s - 35;
            if (start < 0) {
                start = 0;
            }
            int end = s + 35;
            if (end > len) {
                end = len;
            }
            content = content.substring(start, end);
            one.put("content", content);
            if (one.containsKey("type_2")) {
                String type_2 = one.getString("type_2");
                String type2_name = RIUtil.RealDictNames(RIUtil.StringToList(type_2));
                one.put("type_2_name", type2_name);
            } else {
                one.put("type_2_name", "");
            }
            if (one.containsKey("unit")) {
                String unit = one.getString("unit");
                one.put("unit_name", RIUtil.RealDictNames(RIUtil.StringToList(unit)));
            } else {
                one.put("unit_name", "");
            }
//
//            logger.warn(content);
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private static JSONObject updateKnow(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String file_num = "";
            String file_name = "";
            String size = "";
            String create_user = "";
            String create_time = "";
            String type = "";
            String content = "";
            String file_id = "";
            String opt_user = "";
            String type_2 = "";
            String unit = "";
            String cover = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(447004);
            }

            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type='" + type + "' , ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " unit='" + unit + "' , ";
            }
            if (data.containsKey("file_name") && data.getString("file_name").length() > 0) {
                file_name = data.getString("file_name");
                sql = sql + " file_name=encode('" + file_name + "','" + RIUtil.enName + "') , ";
            }
            if (data.containsKey("size") && data.getString("size").length() > 0) {
                size = data.getString("size");
                sql = sql + " size='" + size + "' , ";
            }

            if (data.containsKey("cover") && data.getString("cover").length() > 0) {
                cover = data.getString("cover");
                sql = sql + " cover='" + cover + "' , ";
            }

            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' , ";
            }
            if (data.containsKey("type_2") && data.getString("type_2").length() > 0) {
                type_2 = data.getString("type_2");
                sql = sql + " type_2=encode('" + type_2 + "','" + RIUtil.enContent + "') , ";
            }


            if (data.containsKey("file_id") && data.getString("file_id").length() > 0) {
                file_id = data.getString("file_id");

                content = ReadFilesbak.getContent(file_id);
                sql = sql + " file_id='" + file_id + "' , content=encode('" + content + "','" + RIUtil.enContent +
                        "'), ";
            }

            String sqls = "update know set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新工作宝典", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(447003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private static JSONObject deleteKnow(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(447008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(447008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update know set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除工作宝典", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(447007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

}
