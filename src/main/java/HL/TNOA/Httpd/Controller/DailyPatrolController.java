package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.wechat.wechatMsgTemp;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
public class DailyPatrolController {
    private static Logger logger = LoggerFactory.getLogger(DailyPatrolController.class);

    ///patrol
    @RequestMapping(method = {RequestMethod.POST}, path = {"/patrol"})
    public static JSONObject getDailyPatrol(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        String clientIP = request.getRemoteAddr();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_daily_patrol")) {
                logger.warn("daily.patrol--->" + request.getRequestParams().toString());
                return getDailyPatrol(data);
            } else if (opt.equals("create_daily_patrol")) {
                logger.warn("daily.patrol--->" + request.getRequestParams().toString());
                return createDailyPatrol(data, clientIP);
            } else if (opt.equals("update_daily_patrol")) {
                logger.warn("daily.patrol--->" + request.getRequestParams().toString());
                return updateDailyPatrol(data, clientIP);
            } else if (opt.equals("delete_daily_patrol")) {
                logger.warn("daily.patrol--->" + request.getRequestParams().toString());
                return deleteDailyPatrol(data, clientIP);
            } else if (opt.equals("get_list_daily_patrol")) {
                return getListDailyPatrol(data);
            } else if (opt.equals("update_daily_patrol_status")) {
                logger.warn("daily.patrol--->" + request.getRequestParams().toString());
                return updateDailyPatrolStatus(data, clientIP);

            } else {
                return ErrNo.set(459009);
            }
        } else {
            return ErrNo.set(459009);
        }
    }

    static JSONObject updateDailyPatrolStatus(JSONObject data, String ip) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String id = "";
        String opt_user = "";
        String status = "";
        String check_time = "";
        String finish_time = "";
        String sql = "";
        String create_user = "";
        String police = "";
        String date = "";
        String remark = "";
        try {
            mysql = InfoModelPool.getModel();
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

                create_user = RIUtil.IdToName(id, mysql, "create_user", "daily_patrol");
                police = RIUtil.IdToName(id, mysql, "police", "daily_patrol");
                date = RIUtil.IdToName(id, mysql, "date", "daily_patrol");


            } else {
                return ErrNo.set(459004);
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
                if (opt_user.equals(create_user) || police.equals(opt_user)) {

                } else {
                    return ErrNo.set(459010);
                }
            } else {
                return ErrNo.set(459004);
            }
            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getString("status");
                String notice_id = "b_" + new SimpleDateFormat("yyMMddHHmmss").format(new Date());
                if (status.equals("1")) {
                    check_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                    sql = sql + "status='" + status + "' , check_time='" + check_time + "',";

                } else if (status.equals("2")) {
                    check_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                    sql = sql + "status='" + status + "' , check_time='" + check_time + "',";
                    String comment = date + " 每日巡查已提交，请审核";

                    wechatMsgTemp.createDingMsg(date, comment, create_user, 9, police, mysql, notice_id);
                } else if (status.equals("3")) {
                    finish_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                    sql = sql + "status='" + status + "' , finish_time='" + finish_time + "',";
                    String comment = date + " 每日巡查审核通过";
                    wechatMsgTemp.createDingMsg(date, comment, create_user, 9, police, mysql, notice_id);

                } else if (status.equals("4")) {
                    sql = sql + "status='" + 1 + "' , finish_time='',";
                    String comment = date + " 每日巡查审核未通过";
                    wechatMsgTemp.createDingMsg(date, comment, create_user, 9, police, mysql, notice_id);

                } else {

                    return ErrNo.set(459010);
                }


            } else {
                return ErrNo.set(459004);
            }

            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
            }

            String sqls = "update daily_patrol set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);
            sql = "INSERT INTO task_log (task_id, task_type, time, opt_user,cancel_type,user_id,remark) " + "VALUES" + " " + "(" + " " + "'" + id + "', '" + status + "', '" + new SimpleDateFormat("yyyy-MM-dd " + "HH:mm:ss").format(new Date()) + "'," + " '" + opt_user + "','88','" + opt_user + "','" + remark + "');";
            mysql.update(sql);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新每日巡检", userlog.TYPE_OPERATE, ip);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(459005);
        } finally {
            InfoModelPool.putModel(mysql);
        }


    }

    private static JSONObject getListDailyPatrol(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String start_date = "";
            String end_date = "";
            int limit = 20;
            int page = 1;
            String sql = "";
            String police = "";

            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date");
                sql = sql + "date>='" + start_date + "' and ";

            }
            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
                sql = sql + "date<='" + end_date + "' and ";

            }
            if (data.containsKey("police") && data.getString("police").length() > 0) {
                police = data.getString("police");
                sql = sql + " police like '%" + police + "%' and ";
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");

            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");

            }

            String sqls =
                    "select id,date,create_user,create_time,rectify_time from daily_patrol " + "where 1=1 and " + sql + " isdelete=1   group by date order by date desc " + "limit " + limit + " offset " + limit * (page - 1);

            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfoList(list, mysql));
                sqls = "select id from daily_patrol where 1=1 and " + sql + " isdelete=1 group by date; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(459005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private static List<JSONObject> RelaInfoList(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String police = one.getString("police");


            String create_user = one.getString("create_user");
            System.out.println(create_user);
            one.put("create_user", RIUtil.users.get(create_user));
            System.out.println(one.getString("create_user"));

            back.add(one);
        }
        return back;
    }

    //******CREATE*******
    private static JSONObject createDailyPatrol(JSONObject dds, String remoteAddr) {

        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String date = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String create_user = "";
            String isdelete = "1";
            if (dds.containsKey("date") && dds.getString("date").length() > 0) {
                date = dds.getString("date");
                String sql2 = "select create_user from daily_patrol where isdelete = 1 and date ='" + date + "' limit"
                        + " 1";
                logger.warn(sql2);
                create_user = mysql.query_one(sql2, "create_user");
                logger.warn(create_user);
                String sql = "delete from daily_patrol where date='" + date + "'";
                logger.warn(sql);
                mysql.update(sql);
            } else {
                return ErrNo.set(459002);

            }
            JSONArray ds = new JSONArray();
            if (dds.containsKey("data") && dds.getString("data").length() > 0) {
                ds = dds.getJSONArray("data");
            } else {
                return ErrNo.set(459002);
            }
            if (dds.containsKey("opt_user") && dds.getString("opt_user").length() > 0) {
                if (create_user.isEmpty() || create_user.equals("")) {
                    create_user = dds.getString("opt_user");
                }
            } else {
                return ErrNo.set(459002);

            }
            for (int i = 0; i < ds.size(); i++) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                JSONObject data = ds.getJSONObject(i);
                String pNum = "";
                String pType = "";
                String police = "";
                String problem = "";
                String isChange = "0";
                String pName = "";
                String checkStatus = "";
                String arriveTime = "";
                String outTime = "";
                String remark = "";
                String patrolPro = "";
                String rectify_time = sdf.format(new Date()) + " 23:59:59";

                String patrolType = "0";
                if (data.containsKey("pNum") && data.getString("pNum").length() > 0) {
                    pNum = data.getString("pNum");
                }
                if (data.containsKey("pType") && data.getString("pType").length() > 0) {
                    pType = data.getString("pType");
                }
                if (data.containsKey("police") && data.getString("police").length() > 0) {
                    police = data.getString("police");
                }
                if (data.containsKey("problem") && data.getString("problem").length() > 0) {
                    problem = data.getString("problem");
                }
                if (data.containsKey("isChange") && data.getString("isChange").length() > 0) {
                    isChange = data.getString("isChange");
                }
                if (data.containsKey("pName") && data.getString("pName").length() > 0) {
                    pName = data.getString("pName");
                }
                if (data.containsKey("checkStatus") && data.getString("checkStatus").length() > 0) {
                    checkStatus = data.getString("checkStatus");
                }
                if (data.containsKey("arriveTime") && data.getString("arriveTime").length() > 0) {
                    arriveTime = data.getString("arriveTime");
                }
                if (data.containsKey("outTime") && data.getString("outTime").length() > 0) {
                    outTime = data.getString("outTime");
                }
                if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                    remark = data.getString("remark");
                }
                if (data.containsKey("patrolPro") && data.getString("patrolPro").length() > 0) {
                    patrolPro = data.getString("patrolPro");
                }
                if (data.containsKey("rectify_time") && data.getString("rectify_time").length() > 0) {
                    rectify_time = data.getString("rectify_time");
                    if (rectify_time.length() != 19) {
                        return ErrNo.set(459012);
                    }
                }

                if (data.containsKey("patrolType") && data.getString("patrolType").length() > 0) {
                    patrolType = data.getString("patrolType");
                }
                String sqls = "insert daily_patrol (date,pNum,pType," + "police,problem,isChange," + "pName," +
                        "checkStatus," + "arriveTime," + "outTime,remark," + "patrolPro,create_time,create_user," +
                        "isdelete," + "patrolType,rectify_time)" + "values('" + date + "',encode('" + pNum + "','" + RIUtil.enNum + "'),'" + pType + "'," + "'" + police + "',encode('" + problem + "','" + RIUtil.enContent + "'),'" + isChange + "'," + "encode('" + pName + "','" + RIUtil.enTitle + "'),encode('" + checkStatus + "','" + RIUtil.enContent + "')," + "'" + arriveTime + "'," + "'" + outTime + "',encode('" + remark + "','" + RIUtil.enContent + "')," + "encode('" + patrolPro + "','" + RIUtil.enContent + "'),'" + create_time + "','" + create_user + "'," + "'" + isdelete + "','" + patrolType + "','" + rectify_time + "')";
                logger.warn(sqls);
                mysql.update(sqls);

                String cuser = RIUtil.IdToName(create_user, mysql, " name",
                        "user");
                String comment = date + " 每日巡查问题请查收";
                String notice_id = "b_" + new SimpleDateFormat("yyMMddHHmmss").format(new Date());
                wechatMsgTemp.createDingMsg(date, comment, create_user, 9, police, mysql, notice_id);

            }
            //发送给所领导
            String sqls =
                    "select id,name from user " + "where (position like '%01%' or position " +
                            "like '%02%' or position like '%03%' or " + "position like '%04%' or position like " +
                            "'%05%'" + " or " + "position like '%07%') and status=1;";
            List<JSONObject> list = mysql.query(sqls);
            String leaders = "";
            for (int i = 0; i < list.size(); i++) {
                leaders = leaders + list.get(i).getString("id") + ",";
            }
            logger.warn(leaders);
            String cuser = RIUtil.IdToName(create_user, mysql, " name", "user");
            String comment = date + " 每日巡查问题请查收";

            String notice_id = "b_" + new SimpleDateFormat("yyMMddHHmmss").format(new Date());
            wechatMsgTemp.createDingMsg(date, comment, create_user, 9, leaders, mysql, notice_id);
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建每日巡检", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(459001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    //******GET*******
    private static JSONObject getDailyPatrol(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";

            String pNum = "";
            String pType = "";
            String police = "";
            String problem = "";
            String isChange = "0";
            String pName = "";
            String patrolType = "";
            String start_date = "";
            String date = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }

            if (data.containsKey("date") && data.getString("date").length() > 0) {
                date = data.getString("date");
                sql = sql + " date='" + date + "' and ";
            }
            if (data.containsKey("pNum") && data.getString("pNum").length() > 0) {
                pNum = data.getString("pNum");
                sql = sql + " decode(pNum,'" + RIUtil.enNum + "') like '%" + pNum + "%' and ";
            }
            if (data.containsKey("pType") && data.getString("pType").length() > 0) {
                pType = data.getString("pType");
                sql = sql + " pType='" + pType + "' and ";
            }
            if (data.containsKey("police") && data.getString("police").length() > 0) {
                police = data.getString("police");
                sql = sql + " police like '%" + police + "%' and ";
            }

            if (data.containsKey("isChange") && data.getString("isChange").length() > 0) {
                isChange = data.getString("isChange");
                sql = sql + " isChange='" + isChange + "' and ";
            }
            if (data.containsKey("pName") && data.getString("pName").length() > 0) {
                pName = data.getString("pName");
                sql = sql + "  decode(pName,'" + RIUtil.enTitle + "') like '%" + pName + "%' and ";
            }
            if (data.containsKey("patrolType") && data.getString("patrolType").length() > 0) {
                patrolType = data.getString("patrolType");
                sql = sql + " patrolType='" + patrolType + "' and ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select *," + "decode(pNum,'" + RIUtil.enNum + "') as pNum," + "decode(problem,'" + RIUtil.enContent + "') as problem," + "decode(pName,'" + RIUtil.enTitle + "') as pName," + "decode(patrolPro,'" + RIUtil.enContent + "') as patrolPro ," + "decode(remark,'" + RIUtil.enContent + "') as remark, " + "decode(checkStatus,'" + RIUtil.enContent + "') as checkStatus,rectify_time " + "from daily_patrol where 1=1 and " + sql + " isdelete=1  " + "  order by date desc limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select id from daily_patrol where 1=1 and " + sql + " isdelete=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(459005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private static List<JSONObject> RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String police = one.getString("police");
            one.put("police", RIUtil.UseridToNames(RIUtil.StringToList(police)));
            String create_user = one.getString("create_user");
            logger.warn(create_user);
            one.put("create_user", RIUtil.users.get(create_user));
            //关联提交内容
            JSONArray subs = NoticeController.GetCommentText(one.getString("id"), mysql, 4, 3);
            JSONArray submits = new JSONArray();
            if (subs.size() > 0) {
                for (int s = 0; s < subs.size(); s++) {
                    JSONObject sone = subs.getJSONObject(s);
                    String comment = sone.getString("comment");
                    String comms[] = comment.split(",");
                    JSONArray comments = new JSONArray();
                    for (int c = 0; c < comms.length; c++) {
                        String sql = "select file_name from upload where id='" + comms[c] + "'";
                        String fileName = mysql.query_one(sql, "file_name");
                        JSONObject cone = new JSONObject();
                        if (fileName != null && fileName.length() > 0) {
                            cone.put("file_id", comms[c]);
                            sql = "select file_path from upload where id='" + comms[c] + "'";
                            String filePath = mysql.query_one(sql, "file_path");
                            cone.put("file_path", filePath);
                            cone.put("file_name", fileName);
                            comments.add(cone);
                        } else {

                            cone.put("file_id", comms[c]);
                            cone.put("file_name", "");
                            comments.add(cone);
                        }

                    }
                    if (comments.size() == 0) {
                        JSONObject cone = new JSONObject();
                        cone.put("file_id", comment);
                        cone.put("file_name", "");
                        comments.add(cone);
                    }
                    sone.put("comment", comments);
                    submits.add(sone);
                }
            }
            one.put("submits", submits);
            //finished_line
            String sql = "select task_type,user_id,opt_user,cancel_type,time,remark from task_log where " +
                    "cancel_type=88 and  task_id='" + one.getString("id") + "'";
            // logger.warn(sql);
            List<JSONObject> finishs = mysql.query(sql);
            JSONArray finishlist = new JSONArray();
            for (int c = 0; c < finishs.size(); c++) {
                JSONObject oneC = new JSONObject();
                JSONObject cone = finishs.get(c);
                String opt_user = cone.getString("opt_user");
                String user_id = cone.getString("user_id");
                String time = cone.getString("time");
                String task_type = cone.getString("task_type");
                String cancel_type = cone.getString("cancel_type");


                JSONObject cuser = RIUtil.users.get(opt_user);
                if (cuser == null) {
                    cuser = new JSONObject();
                }
                if (user_id.equals(opt_user)) {
                    oneC.put("opt_user", cuser);
                    oneC.put("user_id", cuser);
                } else {
                    oneC.put("opt_user", cuser);
                    cuser = RIUtil.users.get(user_id);
                    oneC.put("user_id", cuser);
                }
                oneC.put("time", time);
                oneC.put("task_type", task_type);
                oneC.put("cancel_type", cancel_type);
                if (!cone.containsKey("remark")) {
                    oneC.put("remark", "");
                } else {
                    oneC.put("remark", cone.getString("remark"));
                }
                finishlist.add(oneC);

            }
            one.put("finished_line", finishlist);
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private static JSONObject updateDailyPatrol(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String date = "";
            String pNum = "";
            String pType = "";
            String police = "";
            String problem = "";
            String isChange = "";
            String pName = "";
            String checkStatus = "";
            String arriveTime = "";
            String outTime = "";
            String remark = "";
            String patrolPro = "";
            String patrolType = "0";
            String opt_user = "";
            String rectify_time = sdf.format(new Date()) + " 23:59:59";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(459004);
            }
            if (data.containsKey("date") && data.getString("date").length() > 0) {
                date = data.getString("date");
                sql = sql + " date='" + date + "' , ";
            }
            if (data.containsKey("pNum") && data.getString("pNum").length() > 0) {
                pNum = data.getString("pNum");
                sql = sql + " pNum=encode('" + pNum + "'，'" + RIUtil.enNum + "') , ";
            }
            if (data.containsKey("pType") && data.getString("pType").length() > 0) {
                pType = data.getString("pType");
                sql = sql + " pType='" + pType + "' , ";
            }

            if (data.containsKey("problem") && data.getString("problem").length() > 0) {
                problem = data.getString("problem");
                sql = sql + " problem=encode('" + problem + "','" + RIUtil.enContent + "') , ";
            }
            if (data.containsKey("isChange") && data.getString("isChange").length() > 0) {
                isChange = data.getString("isChange");
                sql = sql + " isChange='" + isChange + "' , ";
            }
            if (data.containsKey("pName") && data.getString("pName").length() > 0) {
                pName = data.getString("pName");
                sql = sql + " pName=encode('" + pName + "','" + RIUtil.enTitle + "') , ";
            }
            if (data.containsKey("checkStatus") && data.getString("checkStatus").length() > 0) {
                checkStatus = data.getString("checkStatus");
                sql = sql + " checkStatus=encode(" + checkStatus + "','" + RIUtil.enContent + "') , ";
            }
            if (data.containsKey("arriveTime") && data.getString("arriveTime").length() > 0) {
                arriveTime = data.getString("arriveTime");
                sql = sql + " arriveTime='" + arriveTime + "' , ";
            }
            if (data.containsKey("outTime") && data.getString("outTime").length() > 0) {
                outTime = data.getString("outTime");
                sql = sql + " outTime='" + outTime + "' , ";
            }
            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
                sql = sql + " remark=encode(" + remark + "','" + RIUtil.enContent + "') , ";
            }
            if (data.containsKey("patrolPro") && data.getString("patrolPro").length() > 0) {
                patrolPro = data.getString("patrolPro");
                sql = sql + " patrolPro=encode('" + patrolPro + "','" + RIUtil.enContent + "') , ";
            }
            if (data.containsKey("patrolType") && data.getString("patrolType").length() > 0) {
                patrolType = data.getString("patrolType");
                sql = sql + " patrolType='" + patrolType + "' , ";
            }
            if (data.containsKey("rectify_time") && data.getString("rectify_time").length() > 0) {
                rectify_time = data.getString("rectify_time");
                sql = sql + " rectify_time='" + rectify_time + "' , ";
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(459004);
            }
            String sqls = "update daily_patrol set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);
            logger.warn(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新每日巡检", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(459003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private static JSONObject deleteDailyPatrol(JSONObject data, String remoteAddr) {
        String date = "";
        String opt_user = "";
        if (data.containsKey("date") && data.getString("date").length() > 0) {
            date = data.getString("date");
        } else {
            return ErrNo.set(459008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(459008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update daily_patrol set isdelete =2,delete_user='" + opt_user + "'," + "delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where date='" + date + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除每日巡检", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(459007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

}
