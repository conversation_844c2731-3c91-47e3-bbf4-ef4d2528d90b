package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
public class CommunityDirController {
    private static Logger logger = LoggerFactory.getLogger(CommunityDirController.class);

    @RequestMapping(method = {RequestMethod.POST}, path = {"/comm_dir"})

    public static JSONObject getDir(TNOAHttpRequest request) throws Exception {
        JSONObject data=request.getRequestParams();
        String opt = "";
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_comm_dir")) {
                return getCommDir(data);
            } else if (opt.equals("create_comm_dir")) {
                return createCommDir(data, request.getRemoteAddr());
            } else if (opt.equals("update_comm_dir")) {
                return updateCommDir(data, request.getRemoteAddr());
            } else if (opt.equals("delete_comm_dir")) {
                return deleteCommDir(data, request.getRemoteAddr());
            } else {
                return ErrNo.set(453009);
            }
        } else {
            return ErrNo.set(453009);
        }
    }

    //******CREATE*******
    private static JSONObject createCommDir(JSONObject req, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            JSONArray ll = req.getJSONArray("data");
            String create_user = "";
            if (ll.size() > 0) {

                for (int i = 0; i < ll.size(); i++) {
                    JSONObject data = ll.getJSONObject(i);
                    String date = "";
                    String key = "";
                    String value = "";

                    String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                    String isdelete = "1";
                    if (data.containsKey("date") && data.getString("date").length() > 0) {
                        date = data.getString("date");
                    } else {
                        return ErrNo.set(453002);
                    }
                    if (data.containsKey("key") && data.getString("key").length() > 0) {
                        key = data.getString("key");
                    } else {
                        return ErrNo.set(453002);
                    }
                    if (data.containsKey("value") && data.getString("value").length() > 0) {
                        value = data.getString("value");
                    } else {
                        return ErrNo.set(453002);
                    }
                    if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                        create_user = data.getString("opt_user");
                    } else {
                        return ErrNo.set(453002);
                    }

                    String sqls = "insert comm_dir (date,`key`,`value`,create_user,create_time,isdelete) values ('" + date + "','" + key + "','" + value + "','" + create_user + "','" + create_time + "','" + isdelete + "')";
                    mysql.update(sqls);
                }
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建社区工作字典", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(453001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private static JSONObject getCommDir(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 1;

            String id = "";
            String date = "";
            String key = "";
            String value = "";
            String create_user = "";
            String create_time = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("date") && data.getString("date").length() > 0) {
                date = data.getString("date");
                sql = sql + " date='" + date + "' and ";
            }
            if (data.containsKey("key") && data.getString("key").length() > 0) {
                key = data.getString("key");
                sql = sql + " `key`='" + key + "' and ";
            }
            if (data.containsKey("value") && data.getString("value").length() > 0) {
                value = data.getString("value");
                sql = sql + " `value`='" + value + "' and ";
            }

            String sqls = "select * from comm_dir where 1=1 and " + sql + " isdelete=1 order by date ";
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", list);

                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(453005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private static JSONObject updateCommDir(JSONObject req, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String opt_user = "";
            JSONArray ll = req.getJSONArray("data");
            if (ll.size() > 0) {

                for (int i = 0; i < ll.size(); i++) {
                    JSONObject data = ll.getJSONObject(i);
                    String sql = "";
                    String id = "";
                    String date = "";
                    String key = "";
                    String value = "";


                    if (data.containsKey("id") && data.getString("id").length() > 0) {
                        id = data.getString("id");

                    } else {
                        return ErrNo.set(453004);
                    }
                    if (data.containsKey("date") && data.getString("date").length() > 0) {
                        date = data.getString("date");
                        sql = sql + " date='" + date + "' , ";
                    }
                    if (data.containsKey("key") && data.getString("key").length() > 0) {
                        key = data.getString("key");
                        sql = sql + " `key`='" + key + "' , ";
                    }
                    if (data.containsKey("value") && data.getString("value").length() > 0) {
                        value = data.getString("value");
                        sql = sql + " `value`='" + value + "' , ";
                    }

                    if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                        opt_user = data.getString("opt_user");
                    }
                    String sqls = "update comm_dir set " + sql + " isdelete=1  where id='" + id + "'";
                    mysql.update(sqls);
                }
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新社区工作字典", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(453003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private static JSONObject deleteCommDir(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(453008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(453008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls = "update comm_dir set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除社区工作字典", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(453007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

}
