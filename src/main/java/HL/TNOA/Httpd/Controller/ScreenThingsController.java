package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.Export.ExportInterface;
import HL.TNOA.Lib.Export.ExportXlsxHelper;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.eclipse.jetty.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class ScreenThingsController {

    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");F

    @RequestMapping(method = {RequestMethod.POST}, path = {"/screen_thing"})
    @PassToken
    public JSONObject getScreenThing(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        String token = request.getHeader("token");
        try {

            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("get_public_opinion")) {
                    // num2  560841 200-070100
                    return getPublicOpinion(data);
                } else if (opt.equals("jq_type_analyze")) {
                    return jqTypeAnalyze(data);
                } else if (opt.equals("hotspot_list")) {
                    return HotSpotList(data);
                } else if (opt.equals("jq_sta_list_gk")) {
                    return JQStaList(data);
                } else if (opt.equals("get_mdjf_qgl")) {
                    return Mdfj_qgl(data);
                } else if (opt.equals("get_zdry_gj")) {
                    return GetZdryGj(data);
                } else if (opt.equals("get_zdry_gj_table")) {
                    return GetZdryGjTabel(data);
                } else if (opt.equals("get_jq_sta_barline")) {
                    return JQStaLineBar(data);
                } else if (opt.equals("get_jq_sta_top")) {
                    return JQStaTop(data);
                } else if (opt.equals("get_jq_mg")) {
                    return GetJQmg(data);
                } else if (opt.equals("get_mgjq_chat")) {
                    return GetMgjqChat(data);
                } else if (opt.equals("get_mgjq_table")) {
                    return GetMgjqTable(data);
                } else if (opt.equals("get_jq_sjry")) {
                    return JQSjry(data);

                } else if (opt.equals("get_jq_level")) {
                    return GetJqLevel(data);
                } else if (opt.equals("get_jq_sta2table")) {
                    return GetJqSta2Table(data);
                } else if (opt.equals("get_zdry_bgjl")) {
                    return GetZdryBgjl(data);
                } else if (opt.equals("get_zdry_bgjl_table")) {
                    return GetZdryBgjlTable(data);
                } else if (opt.equals("get_mdjf_table")) {
                    return GetMdjfTable(data);
                } else if (opt.equals("get_myjc_level")) {
                    return GetMyjcLevel(data);
                } else if (opt.equals("get_myjc_ywlx")) {
                    return GetMyjcYwlx(data);
                } else if (opt.equals("get_myjc_table")) {
                    return GetMyjcTable(data);
                } else if (opt.equals("get_qz_yq")) {
                    return GetQzYq(data, token);
                } else if (opt.equals("get_qz_yq_table")) {
                    return GetQzYqTable(data, token);
                } else if (opt.equals("get_jq_top_area")) {
                    return GetJqTopArea(data);
                } else if (opt.equals("get_jq_list")) {
                    return GetJQList(data);
                } else {
                    return ErrNo.set(465009);
                }
            } else {
                return ErrNo.set(465009);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private JSONObject GetJQList(JSONObject data) {

        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");


            String start_time = data.getString("start_time") + " 00:00:00";
            String end_time = data.getString("end_time") + " 23:59:59";
            String unit1 = data.getString("unit");


            String usql = "";
            int type = RIUtil.dicts.get(unit1).getIntValue("type");
            if (type == 21 || type == 22 || type == 27) {

            } else if (type == 23 || type == 24) {
                usql = " and FJ ='" + unit1.substring(0, 6) + "000000'";
            } else if (type == 25) {
                usql = " and PCS ='" + unit1.substring(0, 8) + "0000'";
            } else {
                usql = " and ZRQ ='" + unit1 + "'";
            }
            if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {

                String lb = data.getString("cjlb");
                if (lb.endsWith("000000")) {
                    usql += usql + " and  TYPE50='" + lb + "' ";
                } else if (lb.endsWith("0000")) {
                    usql += usql + " and  TYPE52='" + lb + "' ";
                } else if (lb.endsWith("00")) {
                    usql += usql + " and  TYPE54='" + lb + "' ";
                } else {
                    usql += usql + " and CJLB='" + lb + "'  ";
                }
            }

            String sfcs = "";
            String cjbq = "";

            if (data.containsKey("sfcs") && data.getString("sfcs").length() > 0) {
                sfcs = data.getString("sfcs");

                usql += usql + " and CJSFCS IN ('" + sfcs.replace(",", "','") + "' ) ";

            }

            if (data.containsKey("cjbq") && data.getString("cjbq").length() > 0) {
                cjbq = data.getString("cjbq");

                usql += usql + " and cjjqbq like '%" + cjbq + "%' ";

            }

            String sql =
                    "select jjbh,bjnr,cljg,cjlb,cjsfcs,cjjqbq,cjsj01,zrq from dsj_jq where cjsj01>='" + start_time +
                            " " +
                            "00:00:00 ' and cjsj01<='" + end_time + "23:59:59' " + usql + " order by cjsj01 desc" +
                            " offset 0 rows " +
                            "fetch next 20 rows only";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            JSONArray dets = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                JSONObject det = new JSONObject();
                det.put("title", one.getString("BJNR"));
                det.put("content", one.getString("CLJG"));
                String u = "";
                if (RIUtil.dicts.containsKey(one.getString("ZRQ")) && StringUtil.isNotBlank(RIUtil.dicts.get(one.getString("ZRQ")).getString("remark")))
                    u = RIUtil.dicts.get(one.getString("ZRQ")).getString("remark");
                det.put("unit", u);
                det.put("time", one.getString("CJSJ01"));

                String cjlb = RIUtil.dicts.get(one.getString("CJLB")).getString("dict_name");
                JSONArray labes = new JSONArray();
                JSONObject lab = new JSONObject();
                lab.put("id", 0);
                lab.put("name", cjlb);
                lab.put("color", "#ff8e56");
                labes.add(lab);

                if (sfcs.length() > 0) {
                    String cs = RIUtil.dicts.get("74-" + one.getString("CJSFCS")).getString("dict_name");
                    lab = new JSONObject();
                    lab.put("id", 0);
                    lab.put("name", cs);
                    lab.put("color", "#ad46f3");
                    labes.add(lab);
                }

                if (cjbq.length() > 0) {

                    String bqs[] = cjbq.split(",");
                    for (int a = 0; a < bqs.length; a++) {
                        String name = RIUtil.dicts.get("75-" + bqs[a]).getString("dict_name");
                        lab = new JSONObject();
                        lab.put("id", 0);
                        lab.put("name", name);
                        lab.put("color", "#4cbb6c");
                        labes.add(lab);

                    }
                }

                det.put("label", labes);

                JSONObject click = new JSONObject();
                click.put("type", "caseDetail");
                JSONObject opt = new JSONObject();
                opt.put("jjbh", one.getString("JJBH"));
                click.put("opt", opt);
                det.put("click", click);

                JSONObject dpclick = new JSONObject();
                dpclick.put("type", "OPEN_DING_DIA");
                det.put("ding", dpclick);

                dets.add(det);


            }

            back.put("data", dets);
            return back;


        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));

        } finally {
            ora_hl.close();

        }


    }

    private JSONObject GetMgjqTable(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");


            String start_time = data.getString("start_time") + " 00:00:00";
            String end_time = data.getString("end_time") + " 23:59:59";
            String unit1 = data.getString("unit");
            String hyxl = "";
            int page = data.getInteger("page");
            int limit = data.getInteger("limit");
            int isExp = data.getIntValue("isExp");
            try {
                hyxl = data.getString("hyxl");
            } catch (Exception ex) {
                hyxl = "";
            }
            String usql = "";
            int type = RIUtil.dicts.get(unit1).getIntValue("type");
            if (type == 21 || type == 22 || type == 27) {

            } else if (type == 23 || type == 24) {
                usql = " and FJ ='" + unit1.substring(0, 6) + "000000'";
            } else if (type == 25) {
                usql = " and PCS ='" + unit1.substring(0, 8) + "0000'";
            } else {
                usql = " and ZRQ ='" + unit1 + "'";
            }

            String xlsql = "";
            if (hyxl != null && hyxl.length() > 0) {
                String[] xls = hyxl.split(",");
                for (int i = 0; i < xls.length; i++) {
                    xlsql = xlsql + " hyxl like '%" + xls[i] + "%' or ";
                }

                xlsql = " and (" + xlsql.substring(0, xlsql.length() - 3) + ") ";

            }

            String sql =
                    "select * from V_MGJQ where cjsj01>='" + start_time + "' and cjsj01<='" + end_time + "' " + usql + xlsql + " order  by cjsj01 desc offset " + (page - 1) * limit + " rows fetch next " + limit + " rows only";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            String hds = "接警编号,处警类别,处警时间,处置状态,敏感标志,所属单位,操作";
            String keys = "JJBH,CJLB,CJSJ01,CJZT,MGBZ,XQDW,OPT";
            JSONArray heads = GetHeads(hds, keys);
            sql =
                    "select count(1) as count from V_MGJQ where cjsj01>='" + start_time + "' and cjsj01<='" + end_time + "'" + " " + usql + xlsql;
            int count = ora_hl.query_count(sql);
            JSONArray bodys = new JSONArray();


            if (list.size() > 0) {

                JSONArray dets = new JSONArray();
                JSONArray dds = new JSONArray();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);


                    try {
                        one.put("CJLB", RIUtil.dicts.get(one.getString("CJLB")).getString("dict_name"));
                    } catch (Exception ex) {


                    }

                    try {
                        one.put("XQDW", RIUtil.dicts.get(one.getString("XQDW")).getString("remark"));
                    } catch (Exception ex) {

                    }
                    one.put("OPT", "盯办");
                    dds.add(one);

                    JSONObject det = new JSONObject();
                    String[] key = keys.split(",");
                    for (int k = 0; k < key.length; k++) {
                        String c = key[k];
                        String v = "";

                        v = one.getString(c);


                        JSONObject val = new JSONObject();
                        val.put("value", v);


                        if (c.equals("OPT")) {
                            JSONObject click = new JSONObject();
                            click.put("type", "OPEN_DING_DIA");

                            val.put("ding", click);
                        }

                        if (c.equals("JJBH")) {
                            JSONObject click = new JSONObject();
                            click.put("type", "caseDetail");
                            JSONObject opt = new JSONObject();
                            opt.put("jjbh", one.getString("JJBH"));
                            click.put("opt", opt);

                            val.put("click", click);
                        }
                        det.put(c, val);
                    }
                    bodys.add(det);


                }
                JSONObject datas = new JSONObject();
                datas.put("head", heads);
                datas.put("body", bodys);
                datas.put("count", count);
                int fileId = -1;
                if (isExp == 1) {
                    //本页
                    fileId = ExportTables(dds, hds, keys, "MGJQ");

                } else if (isExp == 2) {
                    //全部
                    sql =
                            "select * from V_MGJQ where cjsj01>='" + start_time + "' and cjsj01<='" + end_time + "' " + usql + xlsql + " order  by cjsj01 desc";
                    logger.warn(sql);
                    list = ora_hl.query(sql);
                    dds = new JSONArray();
                    for (int i = 0; i < list.size(); i++) {
                        JSONObject one = list.get(i);

                        try {
                            one.put("CJLB", RIUtil.dicts.get("51-" + one.getString("CJLB")).getString("dict_name"));
                        } catch (Exception ex) {


                        }

                        try {
                            one.put("XQDW", RIUtil.dicts.get(one.getString("XQDW")).getString("remark"));
                        } catch (Exception ex) {

                        }
                        one.put("OPT", "");
                        dds.add(one);
                    }
                    fileId = ExportTables(dds, hds, keys, "MGJQ");

                } else {

                }
                datas.put("file_id", fileId);
                back.put("data", datas);


            } else {
                back.put("data", new JSONArray());

            }

            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private JSONObject GetMgjqChat(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");


            String start_time = data.getString("start_time") + " 00:00:00";
            String end_time = data.getString("end_time") + " 23:59:59";
            String unit1 = data.getString("unit");
            String hyxl = "";
            try {
                hyxl = data.getString("hyxl");
            } catch (Exception ex) {
                hyxl = "";
            }
            String usql = "";
            String groupB = "";
            int type = RIUtil.dicts.get(unit1).getIntValue("type");
            if (type == 21 || type == 22 || type == 27) {
                groupB = "fj";
            } else if (type == 23 || type == 24) {
                usql = " and FJ ='" + unit1.substring(0, 6) + "000000'";
                groupB = "pcs";
            } else if (type == 25) {
                usql = " and PCS ='" + unit1.substring(0, 8) + "0000'";
                groupB = "zrq";
            } else {
                usql = " and ZRQ ='" + unit1 + "'";
                groupB = "zrq";
            }

            String xlsql = "";
            if (hyxl != null && hyxl.length() > 0) {
                String[] xls = hyxl.split(",");
                for (int i = 0; i < xls.length; i++) {
                    xlsql = xlsql + " hyxl like '%" + xls[i] + "%' or ";
                }

                xlsql = " and (" + xlsql.substring(0, xlsql.length() - 3) + ") ";

            }

            String sql =
                    "select count(1) as count, " + groupB + " as code from V_MGJQ where cjsj01>='" + start_time + "' " +
                            "and cjsj01<='" + end_time + "' " + usql + xlsql + " group by " + groupB + " order by " + "count" + " " + "desc";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);


            if (list.size() > 0) {

                List<JSONObject> dets = new ArrayList<>();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String code = one.getString("CODE");
                    one.put("name", RIUtil.dicts.get(code).getString("dict_name"));
                    one.put("index", RIUtil.dicts.get(code).getString("index_no"));
                    one.put("count", one.getString("COUNT"));

                    JSONObject click = new JSONObject();
                    click.put("id", String.valueOf(UUID.randomUUID()));
                    click.put("label", "敏感警情列表");
                    click.put("permission", "table_page");
                    click.put("type", "dialog");

                    JSONObject cols = new JSONObject();
                    cols.put("start_time", "$start_time$");
                    cols.put("end_time", "$end_time$");
                    cols.put("hyxl", hyxl);
                    cols.put("code", code);
                    cols.put("unit", code);
                    //logger.warn(cols.toString());
                    click.put("remark", GetOpts("/screen_thing", "get_mgjq_table", cols));
                    one.put("click", click);

                    JSONObject dpclick = new JSONObject();
                    dpclick.put("id", String.valueOf(UUID.randomUUID()));
                    dpclick.put("label", RIUtil.dicts.get(code).getString("dict_name"));
                    dpclick.put("name", RIUtil.dicts.get(code).getString("dict_name"));
                    dpclick.put("url", "/screen_point");
                    dpclick.put("type", "next");

                    cols = new JSONObject();
                    cols.put("start_time", "$start_time$");
                    cols.put("end_time", "$end_time$");
                    cols.put("hyxl", hyxl);
                    cols.put("code", code);
                    cols.put("unit", code);
                    cols.put("name", RIUtil.dicts.get(code).getString("dict_name"));

                    //logger.warn(cols.toString());
                    dpclick.put("opt", GetOpts("/screen_thing", "get_mgjq_chat", cols));
                    one.put("dpclick", dpclick);
                    dets.add(one);

                }

                Collections.sort(dets, (JSONObject o1, JSONObject o2) -> {
                    //转成JSON对象中保存的值类型
                    String a = "";
                    String b = "";

                    try {
                        a = o1.getString("index");
                        b = o2.getString("index");
                    } catch (Exception ex) {

                    }

                    int result = a.compareTo(b);
                    // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                    if (result > 0) {  //降序排列，升序改成a>b
                        return 1;
                    } else if (a == b) {
                        return 0;
                    } else {
                        return -1;
                    }
                });
                back.put("data", dets);


            } else {
                back.put("data", new JSONArray());

            }

            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private JSONObject GetJQmg(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");


            String start_time = data.getString("start_time") + " 00:00:00";
            String end_time = data.getString("end_time") + " 23:59:59";
            String unit1 = data.getString("unit");
            String hyxl = "";
            try {
                hyxl = data.getString("hyxl");
            } catch (Exception ex) {
                hyxl = "";
            }
            String usql = "";
            int type = RIUtil.dicts.get(unit1).getIntValue("type");
            if (type == 21 || type == 22 || type == 27) {

            } else if (type == 23 || type == 24) {
                usql = " and FJ ='" + unit1.substring(0, 6) + "000000'";
            } else if (type == 25) {
                usql = " and PCS ='" + unit1.substring(0, 8) + "0000'";
            } else {
                usql = " and ZRQ ='" + unit1 + "'";
            }

            String xlsql = "";
            if (hyxl != null && hyxl.length() > 0) {
                String[] xls = hyxl.split(",");
                for (int i = 0; i < xls.length; i++) {
                    xlsql = xlsql + " hyxl like '%" + xls[i] + "%' or ";
                }

                xlsql = " and (" + xlsql.substring(0, xlsql.length() - 3) + ") ";

            }

            String sql =
                    "select * from V_MGJQ where cjsj01>='" + start_time + "' and cjsj01<='" + end_time + "' " + usql + xlsql + " order  by cjsj01 desc offset 0 rows fetch next 20 rows only";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);


            if (list.size() > 0) {

                JSONArray dets = new JSONArray();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    JSONObject det = new JSONObject();
                    String title = one.getString("BJNR");
                    String u = "";
                    if (RIUtil.dicts.containsKey(one.getString("PCS")) && StringUtil.isNotBlank(RIUtil.dicts.get(one.getString("PCS")).getString("dict_name")))
                        u = RIUtil.dicts.get(one.getString("PCS")).getString("dict_name");
                    String content = one.getString("CLJG");
                    String time = one.getString("JJSJ");


                    JSONArray labels = new JSONArray();
                    if (one.containsKey("CJLB") && one.getString("CJLB") != null && one.getString("CJLB").length() > 0) {
                        JSONObject lable = new JSONObject();
                        lable.put("id", RIUtil.dicts.get(one.getString("CJLB")).getString("id"));
                        lable.put("name", RIUtil.dicts.get(one.getString("CJLB")).getString("dict_name"));
                        lable.put("color", "#ff8e56");
                        labels.add(lable);

                    }
                    if (one.containsKey("BJLX") && one.getString("BJLX").length() > 0 && !one.getString("BJLX").equals("null")) {
                        JSONObject lable = new JSONObject();
                        lable.put("id", one.getString("BJLX"));
                        // logger.warn("=====>>>> BJLX: " + one.getString("BJLX"));
                        try {
                            lable.put("name", RIUtil.dicts.get(one.getString("BJLX")).getString("dict_name"));
                            lable.put("color", "#ff8e56");
                            labels.add(lable);
                        } catch (Exception ex) {
                            logger.warn(one.getString("BJLX"));
                        }
                    }

                    String keys = "刀,枪,杀,炸,尸,死,砍,塌,冲撞,碾压,爆炸,救命,学生,坠楼,遗书,政府,行政中心,小孩,浓烟,煤气,钢瓶,火势大,有人,高层,高压电线,变压器," +
                            "多人,人群," + "危化品,危险品,泄露,河里,沉,聚集,冲向,冲进,侧翻,翻车,小孩,持械,刀,红缨枪,十";

                    String[] kk = keys.split(",");
                    String hss = ",";
                    for (int k = 0; k < kk.length; k++) {
                        String key = kk[k];
                        if (title.contains(key) && !hss.contains("," + key + ",")) {
                            JSONObject lable = new JSONObject();
                            lable.put("id", k);
                            lable.put("name", key);
                            lable.put("color", "#ff1515");
                            labels.add(lable);
                            hss = hss + key + ",";
                        }
                    }

                    if (one.containsKey("MGBZ")) {
                        JSONObject lable = new JSONObject();
                        lable.put("id", 0);
                        lable.put("name", one.getString("MGBZ"));
                        lable.put("color", "#4179e1");
                        labels.add(lable);
                    }
                    if (one.containsKey("CJZT")) {
                        JSONObject lable = new JSONObject();
                        lable.put("id", 0);
                        lable.put("name", one.getString("CJZT"));
                        lable.put("color", "#4CBB6C");
                        labels.add(lable);
                    }
                    if (one.containsKey("HYXL") && one.getString("HYXL").length() > 0 && hyxl != null && hyxl.length() > 0) {

                        String xls[] = one.getString("HYXL").split("\\|");
                        for (int a = 0; a < xls.length; a++) {

                            JSONObject lable = new JSONObject();
                            lable.put("id", 0);
                            lable.put("name", RIUtil.dicts.get(xls[a]).getString("dict_name"));
                            lable.put("color", "#ad46f3");
                            labels.add(lable);
                        }

                    }


                    det.put("label", labels);

                    det.put("title", title);
                    det.put("unit", u);
                    det.put("content", content);
                    det.put("time", time);

                    JSONObject click = new JSONObject();
                    click.put("type", "caseDetail");
                    JSONObject opt = new JSONObject();
                    opt.put("jjbh", one.getString("JJBH"));
                    click.put("opt", opt);
                    det.put("click", click);

                    JSONObject dpclick = new JSONObject();
                    dpclick.put("type", "OPEN_DING_DIA");
                    det.put("ding", dpclick);


                    dets.add(det);


                }
                back.put("data", dets);


            } else {
                back.put("data", new JSONArray());

            }

            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }


    private JSONObject GetJqTopArea(JSONObject data) {

        JSONObject back = ErrNo.set(0);

        String name = "总数,上期,环比";
        String names[] = name.split(",");
        String xx = "bar,bar,line";
        String[] xxs = xx.split(",");
        String sign = ",,%";
        String[] signs = sign.split(",");
        String key = "jzqy,jzqy_hb,jzqy_hb_p";
        String[] keys = key.split(",");
        String ck = "1,1,0";
        String clicks[] = ck.split(",");
        String cjlb = data.getString("cjlb");
        String orderBy = "jzqy_hb_p";

        JSONObject times = Gettbhbcz(data);


        data.putAll(times);

        JSONObject rets = JQstaController.getYBTJ(data);
        if (rets.containsKey("errno") && rets.getInteger("errno") == 0) {

            JSONObject det = rets.getJSONObject("data");
            JSONArray bodys = det.getJSONArray("body");
            List<JSONObject> results = new ArrayList<>();
            for (int i = 0; i < bodys.size(); i++) {
                JSONObject one = bodys.getJSONObject(i);
                for (int a = 0; a < keys.length; a++) {
                    if (!one.containsKey(cjlb + "_" + keys[a])) {
                        one.put(cjlb + "_" + keys[a], 0);
                    }
                }

                results.add(one);
            }

            Collections.sort(results, (JSONObject o1, JSONObject o2) -> {

                String order = o1.getString(cjlb + "_" + orderBy);
                double a = 0;
                try {
                    a = Double.parseDouble(order);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                order = o2.getString(cjlb + "_" + orderBy);
                double b = 0;
                try {
                    b = Double.parseDouble(order);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a < b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });
            int count = 10;
            if (results.size() < 10) {
                count = results.size();
            }
            JSONArray dds = new JSONArray();
            for (int i = 0; i < count; i++) {
                dds.add(results.get(i));
            }

            JSONArray dets = new JSONArray();
            for (int i = 0; i < names.length; i++) {
                JSONObject d = new JSONObject();
                d.put("name", names[i]);
                d.put("id", i);
                d.put("sign", signs[i]);
                d.put("xx", xxs[i]);
                JSONArray dd = new JSONArray();
                for (int a = 0; a < dds.size(); a++) {
                    JSONObject aone = dds.getJSONObject(a);
                    String code = aone.getString("code");
                    String pcs = "";
                    if (aone.containsKey("pcs")) {
                        pcs = aone.getString("pcs").replace("\"", "");
                    }
                    String n = aone.getString("name");
                    if (n.contains("号")) {
                        n = n.split("\\号")[1];
                    }
                    n = n + pcs;
                    String c = aone.getString(cjlb + "_" + keys[i]);
                    String id = code;

                    JSONObject bone = new JSONObject();
                    bone.put("code", code);
                    bone.put("name", n);
                    bone.put("count", c);
                    bone.put("id", code);
                    if (clicks[i].equals("1")) {

                        JSONObject click = new JSONObject();

                        JSONObject cols = new JSONObject();
                        cols.put("start_time", "$start_time$");
                        cols.put("end_time", "$end_time$");
                        cols.putAll(times);
                        cols.put("key", cjlb + "_" + keys[i]);
                        cols.put("code", code);
                        cols.put("unit", "$unit$");
                        //logger.warn(cols.toString());
                        click.put("remark", GetOpts("/jqsta", "get_jq_list2table", cols));
                        click.put("permission", "table_page");
                        click.put("type", "dialog");
                        click.put("label", n);
                        click.put("id", String.valueOf(UUID.randomUUID()));
                        bone.put("click", click);
                    }
                    dd.add(bone);
                }


                d.put("det", dd);

                dets.add(d);
            }
            back.put("data", dets);


        } else {
            return rets;
        }


        return back;

    }

    private JSONObject GetQzYq(JSONObject data, String token) {

        JSONObject back = ErrNo.set(0);
        OracleHelper ora_myjc = null;
        try {

            String unit = data.getString("unit");
            String startTime = data.getString("start_time") + "00:00:00";
            String endTime = data.getString("end_time") + "23:59:59";
            int t = RIUtil.dicts.get(unit).getIntValue("type");

            String usql = "";
            if (t == 21 || t == 22 || t == 27) {


            } else if (t == 23 || t == 24 || t == 28) {

                usql = " and c.RESPONSIBILITY_AREA_CODE  like '" + unit.substring(0, 6) + "%' ";
            } else if (t == 25) {

                usql = " and c.RESPONSIBILITY_AREA_CODE  like '" + unit.substring(0, 8) + "%' ";
            } else {
                usql = " and c.RESPONSIBILITY_AREA_CODE  = '" + unit + "' ";
            }

            ora_myjc = new OracleHelper("ora_myjc");
            String sql = "SELECT b.INSTRUCTION_SERIALNO, b.INSTRUCTION_TITLE, b.INSTRUCTION_CONTENT, b" +
                    ".INSTRUCTION_PULISH_DATE, b.INSTRUCTION_URGENCY, b.INSTRUCTION_CATEGORY, b" +
                    ".INSTRUCTION_REMARK, b" + ".DEVICE_NAME, c.name, c.IDENTITYCARD_NUMBER, c.phone, c" +
                    ".RESPONSIBILITY_AREA " + "FROM " + "QZX_BY_XXZL" + ".V_PERSIONNEL_ASSOCIATION_DSJ a, " +
                    "QZX_BY_XXZL" + ".V_INSTRUCTION_INFO_DSJ b, " + "QZX_BY_XXZL" + ".V_PERSIONNEL_POOL_DSJ c " +
                    "WHERE a" + ".ASSOCIATION_SERIALNO = b.INSTRUCTION_SERIALNO  " + "AND a" + ".PERSONNEL_SERIALNO " + "=" + " c" + ".PERSONNEL_SERIALNO  and b.INSTRUCTION_PULISH_DATE " + ">=TO_DATE('" + startTime + "'," + "'yyyy-mm-dd " + "hh24:mi:ss')  and b.INSTRUCTION_PULISH_DATE " + "<=TO_DATE('" + endTime + "'," + "'yyyy-mm-dd hh24:mi:ss')" + " " + " " + usql + "  order by " + "INSTRUCTION_PULISH_DATE " + "desc   OFFSET 0 ROWS FETCH NEXT 20 ROWS ONLY";
            logger.warn(sql);

            List<JSONObject> list = ora_myjc.query(sql);
            JSONArray dets = new JSONArray();
            if (list.size() > 0) {

                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    JSONObject det = new JSONObject();
                    det.put("title", one.getString("INSTRUCTION_TITLE"));
                    det.put("time", one.getString("INSTRUCTION_PULISH_DATE"));
                    det.put("content", one.getString("INSTRUCTION_CONTENT"));
                    String id = one.getString("INSTRUCTION_SERIALNO");
                    sql = "select ATTACHMENT_ID from QZX_BY_XXZL.V_SYS_ATTACHMENT_ASSOCIATION_DSJ where " +
                            "ASSOCIATION_ID='" + id + "' order by ATTACHMENT_ID offset 0 rows fetch next 1 rows only ";
                    logger.warn(sql);
                    String attrId = ora_myjc.query_one(sql, "ATTACHMENT_ID");

                    String img = TNOAConf.get("Httpd", "qzyq_img").replace("$attrId$", attrId);
                    det.put("img", img);
                    String lbs =
                            one.getString("DEVICE_NAME") + "," + one.getString("INSTRUCTION_URGENCY") + "," + one.getString(
                                    "INSTRUCTION_CATEGORY");

                    String colors = "#4179e1,#219c1c,#ff5715";
                    JSONArray labels = GetLabels(lbs, colors);
                    det.put("label", labels);


                    JSONObject dpclick = new JSONObject();
                    dpclick.put("id", String.valueOf(UUID.randomUUID()));

                    JSONObject cols = new JSONObject();
                    cols.put("unit", unit);

                    cols.put("start_time", startTime);
                    cols.put("end_time", endTime);
                    JSONObject opt = GetOpts("/screen_thing", "get_qz_yq_table", cols);
                    dpclick.put("remark", opt);
                    dpclick.put("label", "明细");
                    dpclick.put("type", "dialog");
                    dpclick.put("id", String.valueOf(UUID.randomUUID()));
                    dpclick.put("permission", "table_page");

                    det.put("dpclick", dpclick);

                    JSONObject click = new JSONObject();


                    String url = TNOAConf.get("Httpd", "qzyq").replace("$insId$", id).replace("$token$", token);
                    logger.warn(url);
                    click.put("url", url);
                    click.put("type", "go_task");
                    det.put("click", click);
                    dets.add(det);

                }

                back.put("data", dets);
            } else {
                back.put("data", new JSONArray());
            }


            return back;
        } catch (Exception ex) {

            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_myjc.close();
        }
    }

    private JSONObject GetQzYqTable(JSONObject data, String token) {

        JSONObject back = ErrNo.set(0);
        OracleHelper ora_myjc = null;
        try {

            String unit = data.getString("unit");
            String startTime = data.getString("start_time");
            String endTime = data.getString("end_time");
            int t = RIUtil.dicts.get(unit).getIntValue("type");
            int page = data.getInteger("page");
            int limit = data.getInteger("limit");
            int isExp = data.getIntValue("isExp");

            String usql = "";
            if (t == 21 || t == 22 || t == 27) {


            } else if (t == 23 || t == 24 || t == 28) {

                usql = " and c.RESPONSIBILITY_AREA_CODE  like '" + unit.substring(0, 6) + "%' ";
            } else if (t == 25) {

                usql = " and c.RESPONSIBILITY_AREA_CODE  like '" + unit.substring(0, 8) + "%' ";
            } else {
                usql = " and c.RESPONSIBILITY_AREA_CODE  = '" + unit + "' ";
            }

            ora_myjc = new OracleHelper("ora_myjc");
            String sql = "SELECT b.INSTRUCTION_SERIALNO, b.INSTRUCTION_TITLE, b.INSTRUCTION_CONTENT, b" +
                    ".INSTRUCTION_PULISH_DATE, b.INSTRUCTION_URGENCY, b.INSTRUCTION_CATEGORY, b" +
                    ".INSTRUCTION_REMARK, b" + ".DEVICE_NAME, c.name, c.IDENTITYCARD_NUMBER, c.phone, c" +
                    ".RESPONSIBILITY_AREA_CODE " + "FROM " + "QZX_BY_XXZL.V_PERSIONNEL_ASSOCIATION_DSJ a, " +
                    "QZX_BY_XXZL" + ".V_INSTRUCTION_INFO_DSJ b, " + "QZX_BY_XXZL.V_PERSIONNEL_POOL_DSJ c " + "WHERE " + "a" + ".ASSOCIATION_SERIALNO = b" + ".INSTRUCTION_SERIALNO  " + "AND a" + ".PERSONNEL_SERIALNO " + "= c" + ".PERSONNEL_SERIALNO  and b" + ".INSTRUCTION_PULISH_DATE >=TO_DATE('" + startTime + "','yyyy-mm-dd " + "hh24:mi:ss')  and b" + ".INSTRUCTION_PULISH_DATE <=TO_DATE('" + endTime + "','yyyy-mm-dd hh24:mi:ss')" + " " + " " + usql + "  order by " + "INSTRUCTION_PULISH_DATE " + "desc   OFFSET " + (page - 1) * limit + " ROWS FETCH NEXT " + limit + " ROWS ONLY";
            logger.warn(sql);

            List<JSONObject> list = ora_myjc.query(sql);

            String hds = "标题,内容,发帖人,发帖人身份证,发布时间,来源,发帖人现住地责任区,操作";
            String keys =
                    "INSTRUCTION_TITLE,INSTRUCTION_CONTENT,NAME,IDENTITYCARD_NUMBER,INSTRUCTION_PULISH_DATE," +
                            "DEVICE_NAME,RESPONSIBILITY_AREA,OPT";
            JSONArray heads = GetHeads(hds, keys);
            sql =
                    "SELECT count(b.INSTRUCTION_SERIALNO) as count FROM QZX_BY_XXZL.V_PERSIONNEL_ASSOCIATION_DSJ a, " +
                            "QZX_BY_XXZL.V_INSTRUCTION_INFO_DSJ b, " + "QZX_BY_XXZL.V_PERSIONNEL_POOL_DSJ c " +
                            "WHERE a" +
                            ".ASSOCIATION_SERIALNO = b.INSTRUCTION_SERIALNO  " + "AND a" + ".PERSONNEL_SERIALNO = c" +
                            ".PERSONNEL_SERIALNO  and b.INSTRUCTION_PULISH_DATE >=TO_DATE('" + startTime + "','yyyy" + "-mm-dd " +
                            "hh24" + ":mi:ss')  and b.INSTRUCTION_PULISH_DATE <=TO_DATE('" + endTime + "','yyyy-mm-dd"
                            + " " + "hh24"
                            + ":mi" + ":ss')" + " " + " " + usql + "  ";
            int count = ora_myjc.query_count(sql);


            JSONArray dets = new JSONArray();
            JSONArray dds = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String id = one.getString("INSTRUCTION_SERIALNO");
                try {
                    one.put("RESPONSIBILITY_AREA",
                            RIUtil.dicts.get(one.getString("RESPONSIBILITY_AREA_CODE")).getString("remark"));
                } catch (Exception EX) {
                    one.put("RESPONSIBILITY_AREA", one.getString("RESPONSIBILITY_AREA_CODE"));
                }
                one.put("OPT", "详情");
                dds.add(one);
                JSONObject det = new JSONObject();

                String[] key = keys.split(",");
                for (int k = 0; k < key.length; k++) {
                    String c = key[k];
                    String v = "";

                    v = one.getString(c);


                    JSONObject val = new JSONObject();
                    val.put("value", v);

                    if (c.equals("IDENTITYCARD_NUMBER")) {
                        JSONObject click = new JSONObject();
                        click.put("type", "jump_person");

                        click.put("url", one.getString("IDENTITYCARD_NUMBER"));
                        val.put("click", click);
                    }
                    if (c.equals("OPT")) {

                        JSONObject click = new JSONObject();


                        String url = TNOAConf.get("Httpd", "qzyq").replace("$insId$", id).replace("$token$", token);
                        logger.warn(url);
                        click.put("url", url);
                        click.put("type", "go_task");
                        val.put("click", click);
                    }
                    det.put(c, val);
                }


                dets.add(det);

            }
            JSONObject datas = new JSONObject();
            datas.put("head", heads);
            datas.put("body", dets);
            datas.put("count", count);
            int fileId = -1;
            if (isExp == 1) {
                //本页
                fileId = ExportTables(dds, hds, keys, "QZYQ");

            } else if (isExp == 2) {
                //全部
                sql = "SELECT b.INSTRUCTION_SERIALNO, b.INSTRUCTION_TITLE, b.INSTRUCTION_CONTENT, b" +
                        ".INSTRUCTION_PULISH_DATE, b.INSTRUCTION_URGENCY, b.INSTRUCTION_CATEGORY, b" +
                        ".INSTRUCTION_REMARK, "
                        + "b.DEVICE_NAME, c.name, c.IDENTITYCARD_NUMBER, c.phone, c" + ".RESPONSIBILITY_AREA_CODE " + "FROM "
                        + "QZX_BY_XXZL.V_PERSIONNEL_ASSOCIATION_DSJ a, QZX_BY_XXZL" + ".V_INSTRUCTION_INFO_DSJ b, " +
                        "QZX_BY_XXZL.V_PERSIONNEL_POOL_DSJ c " + "WHERE a" + ".ASSOCIATION_SERIALNO = b" +
                        ".INSTRUCTION_SERIALNO  " + "AND a" + ".PERSONNEL_SERIALNO = c" + ".PERSONNEL_SERIALNO  and b" +
                        ".INSTRUCTION_PULISH_DATE >=TO_DATE('" + startTime + "','yyyy-mm-dd" + " " + "hh24:mi:ss')  " +
                        "and b" + ".INSTRUCTION_PULISH_DATE <=TO_DATE('" + endTime + "','yyyy-mm-dd " + "hh24:mi:ss')"
                        + " "
                        + " " + usql + "  order by " + "INSTRUCTION_PULISH_DATE " + "desc   ";
                logger.warn(sql);
                list = ora_myjc.query(sql);
                dds = new JSONArray();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    try {
                        one.put("RESPONSIBILITY_AREA",
                                RIUtil.dicts.get(one.getString("RESPONSIBILITY_AREA_CODE")).getString("remark"));
                    } catch (Exception EX) {
                        one.put("RESPONSIBILITY_AREA", one.getString("RESPONSIBILITY_AREA_CODE"));
                    }
                    one.put("OPT", "详情");
                    dds.add(one);
                }
                fileId = ExportTables(dds, hds, keys, "QZYQ");

            } else {

            }
            datas.put("file_id", fileId);
            back.put("data", datas);
            return back;
        } catch (Exception ex) {

            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_myjc.close();
        }
    }


    private JSONObject GetMyjcTable(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_myjc = null;
        try {

            String unit = data.getString("unit");
            String startTime = data.getString("start_time").replace("-", "") + "000000";
            String endTime = data.getString("end_time").replace("-", "") + "235959";
            String lx = data.getString("lx");
            int page = data.getInteger("page");
            int limit = data.getInteger("limit");
            int t = RIUtil.dicts.get(unit).getIntValue("type");

            String usql = "";
            if (t == 21 || t == 22 || t == 27) {


            } else if (t == 23 || t == 24 || t == 28) {

                usql = " and ssdw like '" + unit.substring(0, 6) + "%' ";
            } else {

                usql = " and ssdw = '" + unit.substring(0, 8) + "0000' ";
            }

            if (lx.length() > 0) {
                usql = usql + " and ywdl='" + lx + "' ";
            }

            ora_myjc = new OracleHelper("ora_myjc");
            String sql = "select * from qzx_xy_mydc.v_myjc_xx where " + "createtime>='" + startTime + "' and " +
                    "createtime<='" + endTime + "' " + usql + " order by  createtime desc offset " + (page - 1) * limit + " " + "rows fetch next " + limit + " " + "rows " + "only ";
            logger.warn(sql);

            String hds = "诉求人,诉求人证件,诉求内容,办理情况,诉求渠道,业务类型,受理时间,所属单位";
            String keys = "SRQXM,SRQZJHM,SQNR,BLQK,SQQD,YWXL,SLRQ,SSDWMC";
            JSONArray heads = GetHeads(hds, keys);
            int count = 0;
            JSONArray bodys = new JSONArray();
            List<JSONObject> list = ora_myjc.query(sql);
            if (list.size() > 0) {
                sql = "select count(1) as count from qzx_xy_mydc.v_myjc_xx where " + "createtime>='" + startTime +
                        "'" + " and " + "createtime<='" + endTime + "' " + usql;
                count = ora_myjc.query_count(sql);

                String key[] = keys.split(",");

                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    JSONObject det = new JSONObject();
                    for (int k = 0; k < key.length; k++) {
                        if (one.containsKey(key[k])) {
                            JSONObject val = new JSONObject();
                            if (key[k].equals("YWXL")) {
                                try {
                                    val.put("value", RIUtil.dicts.get("220-" + one.getString(key[k])).getString(
                                            "dict_name"));
                                } catch (Exception ex) {
                                    val.put("value", one.getString(key[k]));
                                }
                            } else {
                                val.put("value", one.getString(key[k]));
                            }
                            if (key[k].equals("SRQZJHM")) {
                                String sfz = one.getString(key[k]);
                                if (sfz.length() == 18) {
                                    JSONObject click = new JSONObject();
                                    click.put("type", "jump_person");
                                    click.put("url", sfz);
                                    val.put("click", click);
                                }
                            }
                            det.put(key[k], val);


                        }

                    }
                    bodys.add(det);
                }


            }

            JSONObject ds = new JSONObject();
            ds.put("head", heads);
            ds.put("count", count);
            ds.put("body", bodys);
            back.put("data", ds);

            return back;
        } catch (Exception ex) {

            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_myjc.close();
        }
    }

    private JSONObject GetMyjcYwlx(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_myjc = null;
        try {

            String unit = data.getString("unit");
            String startTime = data.getString("start_time").replace("-", "") + "000000";
            String endTime = data.getString("end_time").replace("-", "") + "235959";
            int t = RIUtil.dicts.get(unit).getIntValue("type");

            String usql = "";
            if (t == 21 || t == 22 || t == 27) {


            } else if (t == 23 || t == 24 || t == 28) {

                usql = " and ssdw like '" + unit.substring(0, 6) + "%' ";
            } else {

                usql = " and ssdw = '" + unit.substring(0, 8) + "0000' ";
            }

            ora_myjc = new OracleHelper("ora_myjc");
            String sql = "select count(1) as count,ywdl as code,dict_label as name from qzx_xy_mydc.v_myjc_xx a left "
                    + "join qzx_xy_mydc.v_dict_sqfl b on a.ywdl=b.dict_value" + " where " + "createtime>='" + startTime +
                    "'" + " and createtime<='" + endTime + "' " + usql + " group by ywdl," + "dict_label order by " + "count"
                    + " desc";
            logger.warn(sql);

            List<JSONObject> list = ora_myjc.query(sql);
            if (list.size() > 0) {

                back.put("data", RealInfoMYw(list, unit));
            } else {
                back.put("data", new JSONArray());
            }


            return back;
        } catch (Exception ex) {

            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_myjc.close();
        }
    }

    private JSONObject GetMyjcLevel(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_myjc = null;
        try {
            String unit = data.getString("unit");
            String startTime = data.getString("start_time").replace("-", "") + "000000";
            String endTime = data.getString("end_time").replace("-", "") + "235959";
            int t = RIUtil.dicts.get(unit).getIntValue("type");
            String groupSql = "";
            String usql = "";
            if (t == 21 || t == 22 || t == 27) {
                groupSql = " substr(ssdw,0,6) ";

            } else if (t == 23 || t == 24 || t == 28) {
                groupSql = " substr(ssdw,0,8) ";
                usql = " and ssdw like '" + unit.substring(0, 6) + "%' ";
            } else {
                groupSql = " substr(ssdw,0,8) ";
                usql = " and ssdw = '" + unit.substring(0, 8) + "0000' ";
            }

            ora_myjc = new OracleHelper("ora_myjc");
            String sql = "select count(1) as count," + groupSql + " as code from qzx_xy_mydc.v_myjc_xx where " +
                    "createtime>='" + startTime + "' and createtime<='" + endTime + "' " + usql + " group by " + groupSql;
            logger.warn(sql);

            List<JSONObject> list = ora_myjc.query(sql);
            if (list.size() > 0) {

                back.put("data", RealInfoM(list));
            } else {
                back.put("data", new JSONArray());
            }


            return back;
        } catch (Exception ex) {

            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_myjc.close();
        }
    }

    private Object RealInfoMYw(List<JSONObject> list, String unit) {
        List<JSONObject> back = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String code = one.getString("CODE");


            String name = one.getString("NAME");


            JSONObject det = new JSONObject();
            det.put("count", one.getString("COUNT"));
            det.put("name", name);

            det.put("lx", code);


            JSONObject click = new JSONObject();
            click.put("type", "dialog1");
            click.put("label", name + "民意监测类型分布");
            click.put("permission", "table_page");
            JSONObject col = new JSONObject();
            col.put("unit", unit);
            col.put("lx", code);
            col.put("end_time", "$end_time$");
            col.put("start_time", "$start_time$");
            JSONObject opt = GetOpts("/screen_thing", "get_myjc_table", col);
            click.put("remark", opt);
            click.put("id", String.valueOf(UUID.randomUUID()));
            det.put("click", click);
            back.add(det);

        }


        return back;
    }

    private Object RealInfoM(List<JSONObject> list) {
        List<JSONObject> back = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String code = one.getString("CODE");
            if (!code.contains("320496") && !code.contains("320498") && code.length() > 1) {
                if (code.length() == 6) {
                    code = code + "000000";
                } else if (code.length() == 8) {
                    code = code + "0000";
                }

                String name = RIUtil.dicts.get(code).getString("dict_name");
                int index = RIUtil.dicts.get(code).getIntValue("index_no");

                JSONObject det = new JSONObject();
                det.put("count", one.getString("COUNT"));
                det.put("name", name);
                det.put("index", index);
                det.put("unit", code);
                det.put("code", code);

                JSONObject dpclick = new JSONObject();
                dpclick.put("type", "next");
                dpclick.put("url", "/screen_thing");
                JSONObject col = new JSONObject();
                col.put("unit", code);
                col.put("name", name);
                col.put("start_time", "$start_time$");
                col.put("end_time", "$end_time$");
                JSONObject opt = GetOpts("/screen_thing", "get_myjc_level", col);
                dpclick.put("opt", opt);
                det.put("dpclick", dpclick);

                JSONObject click = new JSONObject();
                click.put("type", "dialog");
                click.put("label", name);
                int a = i % 5;
                if (a == 1) {
                    click.put("permission", "chat_pie");
                } else if (a == 2) {
                    click.put("permission", "chat_bar_top3");
                } else if (a == 3) {
                    click.put("permission", "chat_rose");

                } else if (a == 4) {
                    click.put("permission", "chat_pie_border");
                } else {
                    click.put("permission", "chat_bar");
                }


                col = new JSONObject();
                col.put("unit", code);
                col.put("end_time", "$end_time$");
                col.put("start_time", "$start_time$");
                opt = GetOpts("/screen_thing", "get_myjc_ywlx", col);
                click.put("remark", opt);
                click.put("id", String.valueOf(UUID.randomUUID()));
                det.put("click", click);

                back.add(det);
            }

            Collections.sort(back, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("index");
                    b = o2.getInteger("index");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });

        }
        Collections.sort(back, (JSONObject o1, JSONObject o2) -> {
            //转成JSON对象中保存的值类型
            String a = "";
            String b = "";

            try {
                a = o1.getString("index");
                b = o2.getString("index");
            } catch (Exception ex) {

            }

            int result = a.compareTo(b);
            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (result > 0) {  //降序排列，升序改成a>b
                return 1;
            } else if (a == b) {
                return 0;
            } else {
                return -1;
            }
        });


        return back;
    }

    private JSONObject GetMdjfTable(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;
        try {
            int page = data.getInteger("page");
            int limit = data.getInteger("limit");
            String unit = data.getString("unit");
            String type = RIUtil.dicts.get(unit).getString("type");
            String usql = "";
            if (type.equals("23") || type.equals("24") || type.equals("28")) {

                unit = unit.substring(0, 6) + "000000";
                usql = "and  fjdmlist like '%" + unit + "%'";
            } else if (type.equals("25") || type.equals("26")) {
                unit = unit.substring(0, 8) + "0000";
                usql = "and  pcsdmlist like '%" + unit + "%'";
            } else {

            }
            String lxm = data.getString("lxm");
            String lx = data.getString("lx");
            if (lx == null || lx.length() == 0) {
                usql = usql + " and " + lxm + " is null ";
            } else {
                usql = usql + " and " + lxm + " = '" + lx + "'";
            }

            ora_hl = new OracleHelper("ora_hl");
            String sql =
                    "select * from DSJ.OBJ_BASE_2071 where  1=1  " + usql + " order by JFDJSJ desc offset " + (page - 1) * limit + " rows " + "fetch next " + limit + " rows only";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            String hds = "录入时间,纠纷类型,发现来源,纠纷名称,风险内容,风险等级,录入人,销账情况";
            String keys = "JFDJSJ,JFLB,SJLY,BIZNAME,FXNRJPG,FXDJ,SBR,XZQK1";
            JSONArray heads = GetHeads(hds, keys);
            sql = "select count(1) as count from DSJ.OBJ_BASE_2071 where  1=1  " + usql;
            int count = ora_hl.query_count(sql);
            JSONArray bodys = new JSONArray();

            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                JSONObject det = new JSONObject();

                String key[] = keys.split(",");
                for (int k = 0; k < key.length; k++) {
                    String kk = key[k];
                    JSONObject val = new JSONObject();

                    val.put("value", one.getString(kk));


                    det.put(kk, val);
                }
                bodys.add(det);

            }
            JSONObject d = new JSONObject();
            d.put("head", heads);
            d.put("body", bodys);
            d.put("count", count);
            back.put("data", d);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private JSONArray GetHeads(String headN, String hdKeys) {
        JSONArray rets = new JSONArray();
        String[] hdns = headN.split(",");
        String[] hdks = hdKeys.split(",");

        for (int i = 0; i < hdks.length; i++) {
            JSONObject det = new JSONObject();
            String key = hdks[i];
            String value = hdns[i];
            det.put("key", key);
            det.put("value", value);
            rets.add(det);

        }
        return rets;
    }

    private JSONObject GetZdryBgjl(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            String unit = data.getString("unit");
            int t = RIUtil.dicts.get(unit).getInteger("type");
            if (t == 21 || t == 22 || t == 27) {
                unit = unit.substring(0, 4);
            } else if (t == 23 || t == 24) {
                unit = unit.substring(0, 6);

            } else if (t == 25) {
                unit = unit.substring(0, 8);
            } else {
                unit = unit;
            }
            String lxsql = " 1=1 ";
            if (data.containsKey("lx")) {
                String lx = data.getString("lx");
                lxsql = "  lx='" + lx + "' ";
            }

            String start_time = data.getString("start_time").replace("-", "") + "000000";
            String end_time = data.getString("end_time").replace("-", "") + "235959";

            String sql = "select a.*,b.zplj from v_zdry_bg_log a left join czqj_ybds.yw_syrk@qjjc_ybls b on a" +
                    ".gmsfhm=b" + ".gmsfhm where  " + lxsql + " and opt_unit like '" + unit + "%' and " +

                    "opt_time>='" + start_time + "' and opt_time<='" + end_time + "' order by opt_time desc " +
                    "offset 0 " + "rows fetch next 10 rows only";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            if (list.size() > 0) {
                JSONArray dets = new JSONArray();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String title = one.getString("XM") + " " + one.getString("GMSFHM");
                    String content =
                            one.getString("OLD4NAME") + " " + one.getString("OLD5NAME") + "->" + one.getString(
                                    "NEW1NAME") + " " + one.getString("NEW2NAME");
                    String time = one.getString("OPT_TIME");
                    String u = one.getString("OPT_USER_NAME") + "|" + one.getString("REMARK");
                    String img = one.getString("ZPLJ");
                    String lx = one.getString("LX");
                    JSONObject det = new JSONObject();
                    det.put("title", title);
                    det.put("content", content);
                    det.put("time", time);
                    det.put("img", img);
                    det.put("unit", u);

                    JSONArray labels = new JSONArray();
                    JSONObject label = new JSONObject();
                    if (lx.equals("2")) {
                        label.put("id", lx);
                        label.put("name", "管理级别变更");
                        label.put("color", "#ff1515");
                        labels.add(label);

                    } else {
                        label.put("id", lx);
                        label.put("name", "身份属性变更");
                        label.put("color", "#ff5715");
                        labels.add(label);
                    }
                    JSONObject click = new JSONObject();
                    click.put("type", "jump_zdry");
                    click.put("url", one.getString("GMSFHM"));
                    det.put("click", click);

                    JSONObject dpclick = new JSONObject();
                    dpclick.put("type", "dialog");
                    dpclick.put("label", "明细");
                    dpclick.put("permission", "table_page");
                    JSONObject col = new JSONObject();
                    col.put("unit", unit);
                    col.put("isExp", 0);
                    col.put("start_time", start_time);
                    col.put("end_time", end_time);
                    JSONObject opt = GetOpts("/screen_thing", "get_zdry_bgjl_table", col);
                    dpclick.put("remark", opt);
                    det.put("dpclick", dpclick);
                    det.put("label", labels);
                    dets.add(det);

                }

                back.put("data", dets);
            } else {
                back.put("data", new JSONArray());

            }
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            ora_hl.close();
        }
    }

    private JSONObject GetZdryBgjlTable(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            String unit = data.getString("unit");

            String lxsql = " 1=1 ";
            if (data.containsKey("lx")) {
                String lx = data.getString("lx");
                lxsql = "  lx='" + lx + "' ";
            }
            int page = data.getInteger("page");
            int limit = data.getInteger("limit");
            int isExp = data.getIntValue("isExp");

            String start_time = data.getString("start_time").replace("-", "") + "000000";
            String end_time = data.getString("end_time").replace("-", "") + "235959";

            String sql = "select a.*,b.zplj from v_zdry_bg_log a left join czqj_ybds.yw_syrk@qjjc_ybls b on a" +
                    ".gmsfhm=b" + ".gmsfhm where  " + lxsql + " and opt_unit like '" + unit + "%' and " +

                    "opt_time>='" + start_time + "' and opt_time<='" + end_time + "' order by opt_time desc " +
                    "offset " + (page - 1) * limit + " rows fetch next " + limit + " rows only";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            sql = "select count(1) as count from v_zdry_bg_log  where  " + lxsql + " and opt_unit like '" + unit +
                    "%' and " +

                    "opt_time>='" + start_time + "' and opt_time<='" + end_time + "'";
            logger.warn(sql);
            int count = ora_hl.query_count(sql);
            String hds = "姓名,身份证,变更类型,变更明细,管理单位,变更时间";
            String keys = "XM,GMSFHM,TYPE,CONTENT,UNIT,TIME";
            JSONArray heads = GetHeads(hds, keys);

            JSONArray dds = new JSONArray();
            if (list.size() > 0) {
                JSONArray dets = new JSONArray();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);

                    String content =
                            one.getString("OLD4NAME") + " " + one.getString("OLD5NAME") + "->" + one.getString(
                                    "NEW1NAME") + " " + one.getString("NEW2NAME");
                    String time = one.getString("OPT_TIME");
                    String u = one.getString("OPT_USER_NAME") + "|" + one.getString("REMARK");

                    String lx = one.getString("LX");

                    one.put("CONTENT", content);
                    one.put("TIME", time);
                    one.put("UNIT", u);
                    if (lx.equals("2")) {
                        one.put("TYPE", "管理级别变更");
                    } else {
                        one.put("TYPE", "身份属性变更");
                    }
                    dds.add(one);
                    JSONObject det = new JSONObject();
                    String[] key = keys.split(",");
                    for (int k = 0; k < key.length; k++) {
                        String c = key[k];
                        String v = "";

                        v = one.getString(c);


                        JSONObject val = new JSONObject();
                        val.put("value", v);
                        if (c.equals("GMSFHM")) {
                            JSONObject click = new JSONObject();

                            click.put("type", "jump_zdry");
                            click.put("url", one.getString("GMSFHM"));
                            val.put("click", click);
                        }
                        det.put(c, val);
                    }

                    dets.add(det);

                }

                JSONObject datas = new JSONObject();
                datas.put("head", heads);
                datas.put("body", dets);
                datas.put("count", count);
                int fileId = -1;
                if (isExp == 1) {
                    //本页
                    fileId = ExportTables(dds, hds, keys, "ZDRY_BGJL");

                } else if (isExp == 2) {
                    //全部
                    sql = "select a.*,b.zplj from v_zdry_bg_log a left join czqj_ybds.yw_syrk@qjjc_ybls b on a" +
                            ".gmsfhm=b" + ".gmsfhm where  " + lxsql + " and opt_unit like '" + unit + "%' and " +

                            "opt_time>='" + start_time + "' and opt_time<='" + end_time + "' order by opt_time desc ";
                    logger.warn(sql);
                    list = ora_hl.query(sql);
                    dds = new JSONArray();
                    for (int i = 0; i < list.size(); i++) {
                        JSONObject one = list.get(i);
                        String content =
                                one.getString("OLD4NAME") + " " + one.getString("OLD5NAME") + "->" + one.getString(
                                        "NEW1NAME"
                                ) + " " + one.getString("NEW2NAME");
                        String time = one.getString("OPT_TIME");
                        String u = one.getString("OPT_USER_NAME") + "|" + one.getString("REMARK");

                        String lx = one.getString("LX");

                        one.put("CONTENT", content);
                        one.put("TIME", time);
                        one.put("UNIT", u);
                        if (lx.equals("2")) {
                            one.put("name", "管理级别变更");
                        } else {
                            one.put("name", "身份属性变更");
                        }
                        dds.add(one);
                    }
                    fileId = ExportTables(dds, hds, keys, "ZDRY_BGJL");

                } else {

                }
                datas.put("file_id", fileId);
                back.put("data", datas);
            } else {
                back.put("data", new JSONArray());

            }
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            ora_hl.close();
        }
    }

    private JSONObject GetJqSta2Table(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        try {
            JSONObject rets = JQstaController.getYBTJ(data);
            if (rets.containsKey("errno") && rets.getInteger("errno") == 0) {

                JSONObject det = rets.getJSONObject("data");
                JSONArray heads = det.getJSONArray("head");
                JSONObject oneh = new JSONObject();
                oneh.put("value", "操作");
                oneh.put("key", "opt");
                heads.add(oneh);
                JSONArray dets = det.getJSONArray("body");
                JSONArray bodys = new JSONArray();
                for (int i = 0; i < dets.size(); i++) {

                    JSONObject one = dets.getJSONObject(i);
                    JSONObject d = new JSONObject();
                    String code = one.getString("code");
                    if (!code.contains("320400") && !code.equals("320498000000") && !code.equals("320496000000")) {
                        String index = "";
                        String name = "";
                        for (Map.Entry<String, Object> o : one.entrySet()) {
                            String k = o.getKey();
                            String v = o.getValue().toString();
                            if (k.equals("code")) {
                                code = v;
                            }
                            if (k.equals("name")) {
                                name = v;
                            }
                            if (k.equals("index_no")) {
                                index = v;
                            }

                            JSONObject val = new JSONObject();
                            if (k.equals("name")) {

                                v = RIUtil.dicts.get(one.getString("code")).getString("dict_name");
                                val.put("value", v);
                            } else {
                                val.put("value", v);
                            }
                            if (!k.contains("_p") && !k.equals("code") && !k.equals("name") && !k.equals("index_no")) {
                                JSONObject click = new JSONObject();

                                click.put("type", "dialog1");
                                JSONObject cols = new JSONObject();
                                cols.put("start_time", "$start_time$");
                                cols.put("end_time", "$end_time$");

                                cols.put("key", k);
                                cols.put("code", code);
                                cols.put("unit", code);
                                // logger.warn(cols.toString());
                                click.put("remark", GetOpts("/jqsta", "get_jq_list2table", cols));
                                click.put("permission", "table_page");

                                click.put("label", name);
                                click.put("id", String.valueOf(UUID.randomUUID()));
                                val.put("click", click);
                            }


                            d.put(k, val);
                        }

                        JSONObject val = new JSONObject();
                        val.put("value", "盯办");
                        JSONObject click = new JSONObject();
                        click.put("type", "OPEN_DING_DIA");

                        val.put("ding", click);
                        d.put("opt", val);

                        bodys.add(d);
                    }
                }

                JSONObject b = new JSONObject();
                b.put("head", heads);
                b.put("body", bodys);
                b.put("count", 0);
                back.put("data", b);
                return back;


            } else {
                return rets;
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }

    }

    private JSONObject GetJqLevel(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        try {

            String det = "总数,常量";
            String dets[] = det.split(",");
            String xx = "bar,bar";
            String xxs[] = xx.split(",");
            String key = data.getString("keys").replace("^", "|");
            String[] keys = key.split(",");
            String jump = "1,0";
            String[] jumps = jump.split(",");

            JSONObject times = Gettbhbcz(data);
            data.putAll(times);

            String unit = data.getString("unit");
            int t = RIUtil.dicts.get(unit).getInteger("type");
            int searchLevel = 1;
            if (t == 21 || t == 22 || t == 27) {
                searchLevel = 1;
            } else if (t == 23 || t == 24) {
                unit = unit.substring(0, 6) + "000000";
                searchLevel = 2;
            } else if (t == 25) {
                unit = unit.substring(0, 8) + "0000";
                searchLevel = 3;
            } else {
                unit = unit.substring(0, 8) + "0000";
                searchLevel = 3;
            }

            data.put("searchLevel", searchLevel);
            JSONObject rets = JQstaController.getYBTJ(data);
            //logger.warn(rets.toString());
            if (rets.containsKey("errno") && rets.getInteger("errno") == 0) {
                JSONObject da = rets.getJSONObject("data");
                JSONArray bodys = new JSONArray();
                JSONArray datas = da.getJSONArray("body");
                for (int a = 0; a < dets.length; a++) {

                    JSONObject d = new JSONObject();
                    d.put("name", dets[a]);
                    d.put("id", keys[a]);
                    d.put("xx", xxs[a]);
                    List<JSONObject> ds = new ArrayList<>();
                    for (int i = 0; i < datas.size(); i++) {
                        JSONObject one = datas.getJSONObject(i);
                        String code = one.getString("code");
                        if (!code.contains("320400") && !code.equals("320498000000") && !code.equals("320496000000") && !code.equals(unit)) {
                            JSONObject b = new JSONObject();
                            b.put("code", one.getString("code"));
                            b.put("name", RIUtil.dicts.get(one.getString("code")).getString("dict_name"));
                            b.put("index", one.getString("index_no"));
                            b.put("count", one.getString(keys[a]));
                            if (jumps[a].equals("1")) {
                                JSONObject dpclick = new JSONObject();
                                dpclick.put("type", "dialog");
                                JSONObject cols = data;
                                cols.remove("url");
                                cols.remove("opt");
                                cols.remove("X-Real-IP");

                                JSONObject opts = GetOpts("/screen_thing", "get_jq_sta2table", cols);
                                dpclick.put("remark", opts);
                                dpclick.put("label", "统计明细");
                                dpclick.put("permission", "table_page");
                                dpclick.put("id", String.valueOf(UUID.randomUUID()));

                                b.put("click", dpclick);
                            }


                            ds.add(b);
                        }

                    }
                    Collections.sort(ds, (JSONObject o1, JSONObject o2) -> {
                        //转成JSON对象中保存的值类型
                        int e = 0;
                        int b = 0;

                        try {
                            e = o1.getInteger("index");
                            b = o2.getInteger("index");
                        } catch (Exception ex) {

                        }

                        // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                        if (e > b) {  //降序排列，升序改成a>b
                            return 1;
                        } else if (e == b) {
                            return 0;
                        } else {
                            return -1;
                        }
                    });
                    d.put("det", ds);
                    bodys.add(d);
                }
                back.put("data", bodys);
                return back;


            } else {
                return rets;
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private JSONObject JQSjry(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        try {
            String start = data.getString("start_time");
            String end = data.getString("end_time");
            data.put("start_date", start);
            data.put("end_date", end);

            JSONObject rets = JQZTController.GetQCRY(data);
            if (rets.containsKey("errno") && rets.getInteger("errno") == 0) {

                JSONArray datas = rets.getJSONArray("data");
                int count = datas.size();
                if (count >= 10) {
                    count = 10;
                }

                JSONArray ddds = new JSONArray();
                for (int i = 0; i < count; i++) {
                    JSONObject one = datas.getJSONObject(i);
                    JSONObject det = new JSONObject();

                    det.put("img", one.getString("IMG"));
                    det.put("title", one.getString("XM") + " " + one.getString("GMSFHM"));
                    det.put("content", "涉警次数：" + one.getString("COUNT"));
                    det.put("time", one.getString("TIME"));

                    String rylxs = one.getString("rylx");
                    String[] lxs = rylxs.split(",");
                    JSONArray labels = new JSONArray();
                    for (int a = 0; a < lxs.length; a++) {
                        String l = lxs[a];
                        JSONObject lone = new JSONObject();
                        lone.put("id", l);
                        if (!RIUtil.dicts.get(l).getString("dict_name").equals("其他人员")) {
                            lone.put("name", RIUtil.dicts.get(l).getString("dict_name"));
                            //System.out.println(RIUtil.dicts.get(l));
                            lone.put("color", RIUtil.dicts.get(l).getString("color"));
                            labels.add(lone);
                        }
                    }
                    String color = "#4cbb6c,#ff1515,#219c1c,#ff8e15,#4aa6e6,#ff7e15,#6b9efc,#f6b400";
                    String[] colors = color.split(",");

                    int c = 0;
                    String bq = one.getString("BQ");
                    String bqs[] = bq.split("\\|");
                    for (int b = 0; b < bqs.length; b++) {
                        String bbqq = bqs[b];
                        JSONObject lone = new JSONObject();
                        lone.put("id", "0");
                        lone.put("name", bbqq);
                        //System.out.println(RIUtil.dicts.get(l));
                        lone.put("color", colors[c]);
                        c++;
                        if (c > 7) {
                            c = 0;
                        }
                        labels.add(lone);
                    }
                    det.put("label", labels);


                    JSONObject click = new JSONObject();
                    click.put("type", "dialog");
                    click.put("permission", "table_page");
                    click.put("label", one.getString("XM") + "涉警情况");
                    JSONObject cols = new JSONObject();
                    cols.put("jqs", one.getString("jqs"));

                    cols.put("start_time", start);
                    cols.put("end_time", end);
                    JSONObject opt = GetOpts("/jqsta", "get_jq_list3table", cols);
                    click.put("remark", opt);


                    det.put("click", click);
                    JSONObject dpclick = new JSONObject();
                    dpclick.put("type", "OPEN_DING_DIA");
                    det.put("ding", dpclick);
                    ddds.add(det);
                }
                back.put("data", ddds);
                return back;


            } else {
                return rets;
            }


        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        }
    }


    private JSONObject GetJQmg1(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        try {

            String start_time = data.getString("start_time") + " 00:00:00";
            String end_time = data.getString("end_time") + " 23:59:59";
            data.put("start_time", start_time);
            data.put("end_time", end_time);


            JSONObject rets = JQController.searchJQNoAddress(data);
            if (rets.containsKey("errno") && rets.getInteger("errno") == 0) {
                JSONArray datas = rets.getJSONArray("data");
                JSONArray dets = new JSONArray();
                for (int i = 0; i < datas.size(); i++) {
                    JSONObject one = datas.getJSONObject(i);
                    JSONObject det = new JSONObject();
                    String title = one.getString("BJNR");
                    String unit = one.getJSONObject("CHJDW_GAJGJGDM").getString("dict_name");
                    String content = one.getString("CLJG");
                    String time = one.getString("JJSJ");


                    JSONArray labels = new JSONArray();
                    if (one.containsKey("CJLB") && one.getString("CJLB") != null && one.getString("CJLB").length() > 0) {
                        JSONObject lable = new JSONObject();
                        lable.put("id", one.getJSONObject("CJLB").getString("id"));
                        lable.put("name", one.getJSONObject("CJLB").getString("dict_name"));
                        lable.put("color", "#ff8e56");
                        labels.add(lable);

                    }
                    if (one.containsKey("BJLX") && one.getString("BJLX").length() > 0 && !one.getString("BJLX").equals("null")) {
                        JSONObject lable = new JSONObject();
                        lable.put("id", one.getString("BJLX"));
                        // logger.warn("=====>>>> BJLX: " + one.getString("BJLX"));
                        try {
                            lable.put("name", RIUtil.dicts.get(one.getString("BJLX")).getString("dict_name"));
                            lable.put("color", "#ff8e56");
                            labels.add(lable);
                        } catch (Exception ex) {
                            logger.warn(one.getString("BJLX"));
                        }
                    }

                    String keys = "刀,枪,杀,炸,尸,死,砍,塌,冲撞,碾压,爆炸,救命,学生,坠楼,遗书,政府,行政中心,小孩,浓烟,煤气,钢瓶,火势大,有人,高层,高压电线,变压器," +
                            "多人,人群," + "危化品,危险品,泄露,河里,沉,聚集,冲向,冲进,侧翻,翻车,小孩,持械,刀,红缨枪,十";

                    String[] kk = keys.split(",");
                    for (int k = 0; k < kk.length; k++) {
                        String key = kk[k];
                        if (title.contains(key)) {
                            JSONObject lable = new JSONObject();
                            lable.put("id", k);
                            lable.put("name", key);
                            lable.put("color", "#ff1515");
                            labels.add(lable);
                        }
                    }


                    det.put("label", labels);

                    det.put("title", title);
                    det.put("unit", unit);
                    det.put("content", content);
                    det.put("time", time);

                    JSONObject click = new JSONObject();
                    click.put("type", "caseDetail");
                    JSONObject opt = new JSONObject();
                    opt.put("jjbh", one.getString("JJBH"));
                    click.put("opt", opt);
                    det.put("click", click);

                    JSONObject dpclick = new JSONObject();
                    dpclick.put("type", "OPEN_DING_DIA");
                    det.put("ding", dpclick);


                    dets.add(det);


                }
                back.put("data", dets);


            } else {
                return rets;
            }

            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private JSONObject JQStaTop(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        try {

            JSONObject times = Gettbhbcz(data);
            data.putAll(times);
            JSONObject rets = JQstaController.getYBTJ(data);
            logger.warn(rets.toString());
            String orderKey = data.getString("orderKey");
            String[] orderkeys = orderKey.split(",");
            String jumpKey = data.getString("jumpKey");
            int searchType = data.getInteger("searchType");

            if (rets.containsKey("errno") && rets.getInteger("errno") == 0) {
                JSONObject ds = rets.getJSONObject("data");
                JSONArray bodays = ds.getJSONArray("body");
                logger.warn(bodays.get(0).toString());

                List<JSONObject> bds = new ArrayList<>();
                for (int i = 0; i < bodays.size(); i++) {
                    JSONObject one = bodays.getJSONObject(i);
                    for (Map.Entry<String, Object> d : one.entrySet()) {
                        if (d.getKey().contains("51-") && !d.getKey().contains("JQS")) {
                            double val = 0;
                            try {
                                val = Double.parseDouble(d.getValue().toString());
                            } catch (Exception ex) {

                            }
                            one.put(d.getKey(), val);
                        }
                    }


                    bds.add(one);
                }


                if (orderKey.contains(",")) {
                    HashMap<String, JSONObject> ddds = new HashMap<>();
                    for (int k = 0; k < orderkeys.length; k++) {
                        String kk = orderkeys[k];
                        try {
                            Collections.sort(bds, (JSONObject o1, JSONObject o2) -> {
                                //转成JSON对象中保存的值类型
                                double a = 0;
                                double b = 0;

                                try {
                                    a = o1.getDoubleValue(kk);
                                    b = o2.getDoubleValue(kk);
                                } catch (Exception ex) {

                                }


                                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                                if (a < b) {  //降序排列，升序改成a>b
                                    return 1;
                                } else if (a == b) {
                                    return 0;
                                } else {
                                    return -1;
                                }
                            });

                        } catch (Exception ex) {
                            logger.warn(Lib.getTrace(ex));
                        }

                        for (int d = 0; d < bds.size(); d++) {
                            JSONObject done = bds.get(d);
                            //    logger.warn(done.toString());
                            int idx = 0;
                            if (ddds.containsKey(done.getString("code"))) {

                                JSONObject ddd = ddds.get(done.getString("code"));
                                idx = ddd.getIntValue("idx");
                            }
                            idx = idx + (d + 1);
                            done.put("idx", idx);
                            //  logger.warn(done.toString());
                            ddds.put(done.getString("code"), done);

                        }


                    }
                    bds = new ArrayList<>();
                    for (Map.Entry<String, JSONObject> d : ddds.entrySet()) {
                        bds.add(d.getValue());

                    }


                    Collections.sort(bds, (JSONObject o1, JSONObject o2) -> {
                        //转成JSON对象中保存的值类型
                        double a = 0;
                        double b = 0;

                        try {
                            a = o1.getDoubleValue("idx") / 2;
                            b = o2.getDoubleValue("idx") / 2;
                        } catch (Exception ex) {

                        }


                        // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                        if (a > b) {  //降序排列，升序改成a>b
                            return 1;
                        } else if (a == b) {
                            return 0;
                        } else {
                            return -1;
                        }
                    });
                } else {

                    Collections.sort(bds, (JSONObject o1, JSONObject o2) -> {
                        //转成JSON对象中保存的值类型
                        double a = 0;
                        double b = 0;

                        try {
                            a = o1.getDoubleValue(orderKey);
                            b = o2.getDoubleValue(orderKey);
                        } catch (Exception ex) {

                        }

                        // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                        if (a < b) {  //降序排列，升序改成a>b
                            return 1;
                        } else if (a == b) {
                            return 0;
                        } else {
                            return -1;
                        }
                    });
                }

                int size = bds.size();
                if (size > 10) {
                    size = 10;
                }
                JSONArray datas = new JSONArray();
                for (int i = 0; i < size; i++) {
                    JSONObject one = bds.get(i);
                    JSONObject det = new JSONObject();
                    String pcs = "";
                    if (one.containsKey("pcs")) {
                        pcs = one.getString("pcs");
                    }
                    String name = one.getString("name") + pcs;
                    if (name.contains("号")) {
                        try {
                            name = name.split("\\号")[1];
                        } catch (Exception ex) {

                        }
                    }

                    String count = "";

                    count = one.getString(jumpKey);

                    if (count == null || count.length() == 0) {
                        count = "0";
                    }
                    if (count.endsWith(".0")) {
                        count = count.replace(".0", "");
                    }
                    det.put("count", count);
                    try {
                        det.put("name",
                                name + "(总数：" + one.getString(orderkeys[1]) + " 环比：" + one.getString(orderkeys[0]) +
                                        ")");
                    } catch (Exception ex) {
                        det.put("name", name);
                    }
                    String jqs = "";

                    JSONObject click = new JSONObject();
                    JSONObject cols = new JSONObject();
                    cols.put("start_time", "$start_time$");
                    cols.put("end_time", "$end_time$");
                    cols.putAll(times);
                    cols.put("key", jumpKey);
                    cols.put("code", one.getString("code"));
                    if (searchType == 7) {

                        logger.warn(one.toString());
                        String jk = jumpKey.replace("zht", "JQS");
                        logger.warn(jk);
                        jqs = one.getString(jk);
                    }
                    cols.put("jqs", jqs);

                    //   logger.warn(cols.toString());
                    click.put("remark", GetOpts("/jqsta", "get_jq_list2table", cols));
                    click.put("permission", "table_page");
                    click.put("type", "dialog");
                    click.put("label", name);
                    click.put("id", String.valueOf(UUID.randomUUID()));

                    det.put("click", click);
                    datas.add(det);

                }

                back.put("data", datas);
                return back;


            } else {
                return rets;
            }


        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        }

    }

    private JSONObject GetZdryGj(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = data.getString("unit");
        String sfsx = data.getString("sfsx");


        String usql = "";
        String sfsql = "";

        int t = RIUtil.dicts.get(unit).getInteger("type");
        if (t == 21 || t == 22 || t == 27) {
            unit = unit.substring(0, 4) + "00000000";

        } else if (t == 23 || t == 24 || t == 28) {
            unit = unit.substring(0, 6) + "000000";
            usql = "and ssfj='" + unit + "'";
        } else if (t == 25) {
            unit = unit.substring(0, 8) + "0000";
            usql = "and sspcs='" + unit + "'";
        } else {
            unit = unit;
            usql = "and sszrq='" + unit + "'";
        }
        MysqlHelper my143 = null;
        try {
            my143 = new MysqlHelper("mysql_zxqc");
            if (sfsx.length() > 0) {


                String sql = "select id from dict  where (type=180 and id='" + sfsx + "') or (type=181 " + "and " +
                        "father_id" + "='" + sfsx + "')";
                logger.warn(sql);
                List<JSONObject> dicts = my143.query(sql);
                String xlsql = " ";
                for (int d = 0; d < dicts.size(); d++) {
                    JSONObject done = dicts.get(d);
                    xlsql = xlsql + " sfsx like '" + done.getString("id") + "' or ";

                }
                logger.warn(xlsql);
                if (xlsql.length() > 2) {
                    xlsql = xlsql.substring(0, xlsql.length() - 3);
                }
                sfsql = " and (" + xlsql + ") ";
            }

            String sql = "select * from zdry_gjxx where 1=1 " + usql + sfsql + " order by create_time desc limit 5";
            logger.warn(sql);
            JSONArray dets = new JSONArray();

            List<JSONObject> list = my143.query(sql);
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                JSONObject det = new JSONObject();
                det.put("id", one.getString("gmsfhm"));
                det.put("title", one.getString("xm") + " " + one.getString("gmsfhm"));
                det.put("content",
                        one.getString("sbmc") + " " + one.getString("zdmc1") + " " + one.getString("zdmc2") + " " + one.getString("cjdz") + " " + one.getString("room_num") + " " + one.getString("seat_num"));
                JSONArray labels = new JSONArray();
                String sf = one.getString("sfsx");
                String[] sfs = sf.split(",");
                for (int a = 0; a < sfs.length; a++) {
                    String sfName = "";
                    if (RIUtil.dicts.containsKey(sfs[a]) && StringUtil.isNotBlank(RIUtil.dicts.get(sfs[a]).getString(
                            "dict_name")))
                        sfName = RIUtil.dicts.get(sfs[a]).getString("dict_name");
                    JSONObject lone = new JSONObject();
                    lone.put("id", sf);
                    lone.put("name", sfName);
                    lone.put("color", "#D12C25");
                    labels.add(lone);
                }

                JSONObject lone = new JSONObject();
                lone.put("id", one.getString("gljb"));
                lone.put("name", RIUtil.dicts.get("182-" + one.getString("gljb")).getString("dict_name"));
                if (one.getString("gljb").contains("3")) {
                    lone.put("color", "#ff8e15");
                } else {
                    lone.put("color", "#ffd450");
                }
                labels.add(lone);
                lone = new JSONObject();
                lone.put("id", 1);
                lone.put("name", one.getString("lb"));
                lone.put("color", "#00A267");

                labels.add(lone);

                det.put("label", labels);
                det.put("time", one.getString("create_time"));
                JSONObject click = new JSONObject();
                click.put("type", "jump_zdry");
                click.put("url", one.getString("gmsfhm"));
                det.put("click", click);
                det.put("img", one.getString("xt"));

                JSONObject dpclick = new JSONObject();
                dpclick.put("type", "dialog");
                dpclick.put("label", "明细");
                dpclick.put("permission", "table_page");
                JSONObject col = new JSONObject();
                col.put("unit", "$unit$");
                col.put("isExp", 0);
                col.put("sfsx", sfsx);

                JSONObject opt = GetOpts("/screen_thing", "get_zdry_gj_table", col);
                dpclick.put("remark", opt);
                det.put("dpclick", dpclick);
                dets.add(det);
            }
            back.put("data", dets);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            my143.close();
        }
    }

    private JSONObject GetZdryGjTabel(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = data.getString("unit");
        String sfsx = data.getString("sfsx");
        int page = data.getInteger("page");
        int limit = data.getInteger("limit");
        int isExp = data.getIntValue("isExp");


        String usql = "";
        String sfsql = "";

        int t = RIUtil.dicts.get(unit).getInteger("type");
        if (t == 21 || t == 22 || t == 27) {
            unit = unit.substring(0, 4) + "0000000";

        } else if (t == 23 || t == 24 || t == 28) {
            unit = unit.substring(0, 6) + "00000";
            usql = "and ssfj='" + unit + "'";
        } else if (t == 25) {
            unit = unit.substring(0, 8) + "0000";
            usql = "and sspcs='" + unit + "'";
        } else {
            unit = unit;
            usql = "and sszrq='" + unit + "'";
        }
        MysqlHelper my143 = null;
        try {
            my143 = new MysqlHelper("mysql_zxqc");
            if (sfsx.length() > 0) {


                String sql = "select id from dict  where (type=180 and id='" + sfsx + "') or (type=181 " + "and " +
                        "father_id" + "='" + sfsx + "')";
                logger.warn(sql);
                List<JSONObject> dicts = my143.query(sql);
                String xlsql = " ";
                for (int d = 0; d < dicts.size(); d++) {
                    JSONObject done = dicts.get(d);
                    xlsql = xlsql + " sfsx like '" + done.getString("id") + "' or ";

                }
                logger.warn(xlsql);
                if (xlsql.length() > 2) {
                    xlsql = xlsql.substring(0, xlsql.length() - 3);
                }
                sfsql = " and (" + xlsql + ") ";
            }

            String sql =
                    "select * from zdry_gjxx where 1=1 " + usql + sfsql + " order by create_time desc limit " + ((page - 1) * limit) + "," + limit;
            logger.warn(sql);

            JSONArray dets = new JSONArray();
            String hds = "姓名,身份证,轨迹信息,身份属性,管理级别,最近一次轨迹时间,操作";
            String keys = "xm,gmsfhm,content,sfsx,gljb,time1,opt";
            JSONArray heads = GetHeads(hds, keys);

            List<JSONObject> list = my143.query(sql);
            sql = "select count(*) as count from zdry_gjxx where 1=1 " + usql + sfsql;
            int count = my143.query_count(sql);
            JSONArray dds = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                JSONObject det = new JSONObject();

                try {
                    one.put("gljb", RIUtil.dicts.get("182-" + one.getString("gljb")).getString("dict_name"));
                } catch (Exception ex) {
                    one.put("gljb", "");
                }
                String sf = one.getString("sfsx");
                String sfsxs = "";
                if (sf != null && sf.length() > 0) {
                    String[] sfs = sf.split(",");

                    for (int a = 0; a < sfs.length; a++) {
                        String sfName = RIUtil.dicts.get(sfs[a]).getString("dict_name");
                        sfsxs = sfsxs + sfName + ",";
                    }
                }
                one.put("sfsx", sfsxs);
                one.put("content",
                        one.getString("sbmc") + " " + one.getString("zdmc1") + " " + one.getString("zdmc2") + " " + one.getString("cjdz") + " " + one.getString("room_num") + " " + one.getString("seat_num"));
                one.put("opt", "盯办");
                dds.add(one);


                String[] key = keys.split(",");
                for (int k = 0; k < key.length; k++) {
                    String c = key[k];
                    String v = "";

                    v = one.getString(c);


                    JSONObject val = new JSONObject();
                    val.put("value", v);
                    if (c.equals("gmsfhm")) {
                        JSONObject click = new JSONObject();

                        click.put("type", "jump_zdry");
                        click.put("url", one.getString("gmsfhm"));
                        val.put("click", click);
                    }
                    if (c.equals("opt")) {

                        JSONObject click = new JSONObject();

                        click.put("type", "OPEN_DING_DIA");
                        val.put("ding", click);

                    }
                    det.put(c, val);
                }
                dets.add(det);


            }
            JSONObject datas = new JSONObject();
            datas.put("head", heads);
            datas.put("body", dets);
            datas.put("count", count);
            int fileId = -1;
            if (isExp == 1) {
                //本页
                fileId = ExportTables(dds, hds, keys, "ZDRYGJ");

            } else if (isExp == 2) {
                //全部
                sql = "select * from zdry_gjxx where 1=1 " + usql + sfsql;
                logger.warn(sql);
                list = my143.query(sql);
                dds = new JSONArray();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    try {
                        one.put("gljb", RIUtil.dicts.get("182-" + one.getString("gljb")).getString("dict_name"));
                    } catch (Exception ex) {
                        one.put("gljb", "");
                    }
                    String sf = one.getString("sfsx");
                    String sfsxs = "";
                    if (sf != null && sf.length() > 0) {
                        String[] sfs = sf.split(",");

                        for (int a = 0; a < sfs.length; a++) {
                            String sfName = RIUtil.dicts.get(sfs[a]).getString("dict_name");
                            sfsxs = sfsxs + sfName + ",";
                        }
                    }
                    one.put("sfsx", sfsxs);
                    one.put("content", one.getString("sbmc") + " " + one.getString("zdmc1") + " " + one.getString(
                            "zdmc2") + " " + one.getString("cjdz") + " " + one.getString("room_num") + " " + one.getString("seat_num"));
                    one.put("opt", "盯办");
                    dds.add(one);
                }
                fileId = ExportTables(dds, hds, keys, "ZDRYGJ");

            } else {

            }
            datas.put("file_id", fileId);
            back.put("data", datas);

            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            my143.close();
        }
    }

    private int ExportTables(JSONArray datas, String head, String keys, String name) {


        String FileName = name + "_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        String[] heads = head.split(",");
        String key[] = keys.split(",");
        for (int i = 0; i < key.length; i++) {
            header.add(key[i]);
            headername.put(key[i], heads[i]);
        }


        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) +
                            "/";

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
//            for (int i = 0; i < datas.size(); i++) {
//                JSONObject jsonObject = datas.getJSONObject(i);
//                JSONArray jsonArray = jsonObject.getJSONArray("real_all");
            exporthelper.write_data(datas);
//            }

            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql =
                        "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");
                try {
                    if (exporthelper != null) {
                        try {
                            exporthelper.close();
                        } catch (Exception ex) {

                        }

                    }
                } catch (Exception ex) {

                }

                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, TNOAConf.get("file",
                        "img_path") + filePath + FileName);
                logger.warn(obsFileName + "-->" + ret);

                logger.warn("-->" + id);
                return id;
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return 0;
        } finally {

            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }
        }
    }


    private JSONObject Mdfj_qgl(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = data.getString("unit");
        String type = RIUtil.dicts.get(unit).getString("type");

        String usql = "";
        if (type.equals("23") || type.equals("24") || type.equals("28")) {

            unit = unit.substring(0, 6) + "000000";
            usql = " WHERE fjdmlist like '%" + unit + "%'";
        } else if (type.equals("25") || type.equals("26")) {
            unit = unit.substring(0, 8) + "0000";
            usql = " WHERE pcsdmlist like '%" + unit + "%'";
        } else {

        }
        String lx = data.getString("lx");
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");

            String sql =
                    "select count(1) as count," + lx + " as lx from DSJ.OBJ_BASE_2071  " + usql + " group by " + lx;
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            JSONArray rets = new JSONArray();
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String LLXX = one.getString("LX");
                    if (LLXX == null || LLXX.length() == 0) {
                        one.put("LX", "其他");
                    }
                    one.put("id", one.getString("LX"));
                    one.put("name", one.getString("LX"));
                    one.put("count", one.getString("COUNT"));
                    JSONObject click = new JSONObject();
                    click.put("type", "dialog");
                    JSONObject cols = new JSONObject();
                    cols.put("unit", unit);
                    cols.put("lxm", lx);
                    cols.put("lx", LLXX);


                    JSONObject opts = GetOpts("/screen_thing", "get_mdjf_table", cols);
                    click.put("remark", opts);
                    click.put("label", LLXX);
                    click.put("permission", "table_page");
                    click.put("id", String.valueOf(UUID.randomUUID()));


                    one.put("click", click);

                    rets.add(one);
                }

                back.put("data", rets);
            } else {
                back.put("data", new JSONArray());
            }
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }


    }

    private JSONObject JQStaList(JSONObject data) {

        JSONObject back = ErrNo.set(0);
        String unit = data.getString("unit");
        String type = RIUtil.dicts.get(unit).getString("type");
        String perKey = data.getString("perKey");
        String jumpKey = data.getString("jumpKey");
        logger.warn(type);

        String searchLevel = "1";
        if (type.equals("21") || type.equals("22") || type.equals("27")) {
            searchLevel = "1";
            unit = "************";
        } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
            searchLevel = "2";
            unit = unit.substring(0, 6) + "000000";
        } else if (type.equals("25")) {
            searchLevel = "3";
            unit = unit.substring(0, 8) + "0000";
        } else {
            searchLevel = "3";
            unit = unit;
        }

        data.put("searchLevel", searchLevel);
        data.put("unit", unit);
        //   System.out.println(data);
        // System.out.println(unit);

        String style = "pie";
        try {
            style = data.getString("style");
        } catch (Exception ex) {

        }
        JSONObject times = Gettbhbcz(data);
        data.putAll(times);

        JSONObject ret = JQstaController.getYBTJ(data);
        // logger.warn(data.toString());
        JSONArray dets = new JSONArray();
        if (ret.getInteger("errno") == 0) {

            JSONObject datas = ret.getJSONObject("data");
            //   System.out.println(datas);


            JSONArray heads = datas.getJSONArray("head");
            HashMap<String, String> hds = new HashMap<>();
            for (int i = 0; i < heads.size(); i++) {
                JSONObject hone = heads.getJSONObject(i);
                String key = hone.getString("key");
                key = key.split("\\_")[0];
                String value = hone.getString("value");
                value = value.split("\\_")[0];
                if (key.contains("-")) {
                    hds.put(key, value);
                }
            }
            // logger.warn(hds.toString());
            JSONArray bodys = datas.getJSONArray("body");
            //  logger.warn(bodys.toString());
            for (int i = 0; i < bodys.size(); i++) {
                JSONObject bone = bodys.getJSONObject(i);
                String code = bone.getString("code");
                if (i == 0) {
                    //logger.warn(bone.toString());
                }
                // logger.warn(code + "-->" + unit);
                if (code.equals(unit)) {

                    for (Map.Entry<String, String> k : hds.entrySet()) {
                        String key = k.getKey() + jumpKey;
                        String name = k.getValue();

                        String perK = k.getKey() + perKey;


                        JSONObject done = new JSONObject();
                        done.put("key", key);
                        done.put("name", name);
                        done.put("count", bone.getDoubleValue(key));
                        if (perKey.length() > 0) {
                            try {
                                done.put("percent", bone.getDoubleValue(perK));
                            } catch (Exception ex) {
                                done.put("percent", 0);
                            }
                        }
                        if (i == 0) {
                            // logger.warn(done.toString());
                        }

                        JSONObject click = new JSONObject();

                        JSONObject cols = new JSONObject();
                        cols.put("start_time", "$start_time$");
                        cols.put("end_time", "$end_time$");

                        cols.put("key", key);
                        cols.put("code", unit);
                        cols.put("unit", unit);
                        // logger.warn(cols.toString());
                        click.put("remark", GetOpts("/jqsta", "get_jq_list2table", cols));
                        click.put("permission", "table_page");
                        click.put("type", "dialog");
                        click.put("label", name);
                        click.put("id", String.valueOf(UUID.randomUUID()));
                        done.put("click", click);

                        if (bone.getDoubleValue(perK) > 0) {
                            JSONObject ding = new JSONObject();
                            ding.put("type", "OPEN_DING_DIA");
                            done.put("ding", ding);
                        }
                        dets.add(done);


                    }


                    break;
                }
            }

            back.put("data", dets);

            return back;
        } else {
            return ret;
        }


    }

    private JSONObject JQStaLineBar(JSONObject data) {
        System.out.println(data);

        JSONObject back = ErrNo.set(0);
        String unit = data.getString("unit");
        String type = RIUtil.dicts.get(unit).getString("type");
        logger.warn(type);

        String searchLevel = "1";
        if (type.equals("21") || type.equals("22") || type.equals("27")) {
            searchLevel = "1";
            unit = "************";
        } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
            searchLevel = "2";
            unit = unit.substring(0, 6) + "000000";
        } else if (type.equals("25")) {
            searchLevel = "3";
            unit = unit.substring(0, 8) + "0000";
        } else {
            searchLevel = "3";
            unit = unit;
        }

        JSONObject times = Gettbhbcz(data);

        data.put("searchLevel", searchLevel);
        data.put("unit", unit);
        data.putAll(times);

        JSONObject ret = JQstaController.getYBTJ(data);
        //       logger.warn(ret.toString());
        JSONArray dets = new JSONArray();
        if (ret.getInteger("errno") == 0) {
            JSONObject datas = ret.getJSONObject("data");


            String names = data.getString("names");
            String keys = data.getString("keys");
            String did = data.getString("dids").replace("^", "|");
            String cjlb = data.getString("cjlbs");
            String[] dns = names.split(",");

            String[] key = keys.split(",");
            String sign = data.getString("sing");
            String sgs[] = sign.split(",");
            String jump = data.getString("jump");
            String djs[] = jump.split(",");
            String xxs = data.getString("xx");
            String xx[] = xxs.split(",");
            String dids[] = did.split(",");
            String cjlbs[] = cjlb.split(",");

            JSONObject dds = new JSONObject();
            for (int a = 0; a < dns.length; a++) {
                JSONObject det = new JSONObject();
                det.put("id", a);
                det.put("name", dns[a]);
                det.put("sign", sgs[a]);
                det.put("xx", xx[a]);
                JSONArray rets = new JSONArray();

                JSONArray bodys = datas.getJSONArray("body");
                for (int i = 0; i < bodys.size(); i++) {
                    JSONObject bone = bodys.getJSONObject(i);
                    String code = bone.getString("code");
                    // System.out.println(bone);
                    // logger.warn(code + "-->" + unit);
                    String lastK = key[a];
                    if (code.equals(unit)) {

                        for (int k = 0; k < dids.length; k++) {
                            String col = dids[k] + "_" + lastK;
                            // logger.warn(col);
                            String c = bone.getString(col);
                            if (c == null || c.equals("∞") || c.toLowerCase().equals("nan")) {
                                c = "0";
                            }
                            JSONObject r = new JSONObject();
                            r.put("code", col);
                            r.put("id", col);
                            r.put("name", cjlbs[k]);
                            r.put("count", c);

                            if (djs[a].equals("1")) {


                                JSONObject click = new JSONObject();

                                JSONObject cols = new JSONObject();
                                cols.put("start_time", "$start_time$");
                                cols.put("end_time", "$end_time$");
                                cols.putAll(times);
                                cols.put("key", col);
                                cols.put("code", unit);
                                cols.put("unit", unit);
                                //logger.warn(cols.toString());
                                click.put("remark", GetOpts("/jqsta", "get_jq_list2table", cols));
                                click.put("permission", "table_page");
                                click.put("type", "dialog");
                                click.put("label", cjlbs[k] + "_" + dns[a]);
                                click.put("id", String.valueOf(UUID.randomUUID()));

                                r.put("click", click);

                            }
                            rets.add(r);

                        }
                    }
                }
                det.put("det", rets);
                dets.add(det);
            }

            back.put("data", dets);
            return back;
        } else {
            return ret;
        }


    }

    private JSONObject Gettbhbcz(JSONObject data) {
        JSONObject back = new JSONObject();
        try {
            String start_time = data.getString("start_time");
            String end_time = data.getString("end_time");


            //同比
            String ts[] = start_time.split("-");
            int year = Integer.parseInt(ts[0]) - 1;


            String tb_start_time = year + "-" + ts[1] + "-" + ts[2];

            String ets[] = end_time.split("-");
            year = Integer.parseInt(ets[0]) - 1;
            String tb_end_time = year + "-" + ets[1] + "-" + ets[2];

            back.put("tb_start_time", tb_start_time);
            back.put("tb_end_time", tb_end_time);

            //环比
            //整年
            if (start_time.substring(5).equals("01-01") && end_time.substring(5).equals("12-31")) {
                back.put("hb_start_time", tb_start_time);
                back.put("hb_end_time", tb_end_time);
            }//整月
            else if (start_time.substring(8).equals("01") && RIUtil.getLMonthEnd(start_time).substring(0, 10).equals(end_time)) {
                String end = RIUtil.GetNextDate(start_time, -1);
                String t[] = end.split("-");
                String start = t[0] + "-" + t[1] + "-01";
                back.put("hb_start_time", start);
                back.put("hb_end_time", end);
            } else {

                int bt = RIUtil.get2DateBetween(start_time, end_time);
                bt = Math.abs(bt) * -1;

                back.put("hb_start_time", RIUtil.GetNextDate(start_time, bt));
                back.put("hb_end_time", RIUtil.GetNextDate(end_time, bt));
            }
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));

        }
        //  logger.warn(back.toString());
        return back;

    }

    private JSONObject GetOpts(String url, String opt, JSONObject cols) {

        JSONObject opts = new JSONObject();
        opts.put("url", url);
        opts.put("opt", opt);

        opts.put("opt_user", "$opt_user$");


        opts.putAll(cols);
        return opts;
    }

    private JSONArray GetLabels(String labs, String colors) {
        JSONArray lables = new JSONArray();
        String lab[] = labs.split(",");
        String cols[] = colors.split(",");
        for (int i = 0; i < lab.length; i++) {
            String one = lab[i];
            if (one != null || one.length() > 0) {
                String color = cols[i];

                JSONObject label = new JSONObject();
                label.put("id", i);
                label.put("name", one);
                label.put("color", color);
                lables.add(label);
            }
        }
        return lables;
    }

    private JSONObject HotSpotList(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        try {

            String opt_user = data.getString("opt_user");
            //  data.put("user_id", opt_user);


            JSONObject ret = HotspotController.get_list(data);
            //       logger.warn(ret.toString());
            JSONArray dets = new JSONArray();
            if (ret.getInteger("errno") == 0) {

                if (ret.getInteger("count") > 0) {
                    JSONArray datas = ret.getJSONArray("data");

                    for (int i = 0; i < datas.size(); i++) {
                        JSONObject one1 = datas.getJSONObject(i);

                        JSONObject det = new JSONObject();
                        String title = one1.getString("title");
                        String see = one1.getString("see");
                        String time = one1.getString("date");
                        String content = one1.getString("content");


                        det.put("title", title);
                        det.put("content", content);
                        det.put("time", time);
                        JSONArray labels = new JSONArray();
                        JSONObject done = new JSONObject();
                        done.put("id", "id");
                        done.put("name", one1.get("source"));
                        done.put("color", "#339ECB");
                        labels.add(done);
                        done = new JSONObject();
                        done.put("id", "id");
                        done.put("name", one1.get("type"));
                        done.put("color", "#00A267");
                        labels.add(done);
                        done = new JSONObject();
                        done.put("id", "id");
                        done.put("name", one1.get("keyword"));
                        done.put("color", "#D12C25");
                        labels.add(done);


                        det.put("label", labels);
                        String notice_id = one1.getString("id");

                        det.put("id", notice_id);

                        JSONObject click = new JSONObject();
                        click.put("type", "dialog_hot");
                        click.put("url", TNOAConf.get("HttpServ", "notice_url") + notice_id);
                        det.put("click", click);


                        dets.add(det);
                    }
                }
                back.put("data", dets);
                back.put("count", ret.getInteger("count"));
            } else {
                return ret;
            }

            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    //民意监测
    private JSONObject getPublicOpinion(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        JSONArray heads = new JSONArray();
        JSONObject head = new JSONObject();
        head.put("key", "num");
        head.put("value", "序号");
        heads.add(head);

        head = new JSONObject();
        head.put("key", "task_name");
        head.put("value", "任务名称");
        heads.add(head);

        head = new JSONObject();
        head.put("key", "count");
        head.put("value", "数量");
        heads.add(head);

        JSONObject click = new JSONObject();
        click.put("type", "jump");
        click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/myTask?Index");
        JSONArray bodys = new JSONArray();
        try {
            JSONObject ret = QQBTaskController.GetFollow(data);
            logger.warn(ret.toString());
            if (ret.getInteger("errno") == 0) {

                if (ret.getInteger("count") > 0) {
                    JSONArray datas = ret.getJSONArray("data");

                    for (int i = 0; i < datas.size(); i++) {
                        JSONObject det = new JSONObject();
                        JSONObject one = datas.getJSONObject(i);
                        // System.out.println(one);

                        int num = i + 1;

                        JSONObject dd = new JSONObject();
                        dd.put("value", num);
                        det.put("num", dd);

                        String task_name = one.getString("TASK_NAME");
                        String type = one.getString("TYPE");
                        if (type.equals("2")) {

                            task_name = "【分局】" + task_name;
                        } else if (type.equals("3")) {
                            task_name = "【派出所】" + task_name;
                        } else {
                            task_name = "【市局】" + task_name;
                        }

                        dd = new JSONObject();
                        dd.put("value", task_name);
                        dd.put("click", click);
                        det.put("task_name", dd);

                        String count = one.getString("count");
                        dd = new JSONObject();
                        dd.put("value", count);
                        det.put("count", dd);

                        bodys.add(det);

                    }
                }
                JSONObject d = new JSONObject();
                d.put("head", heads);
                d.put("body", bodys);
                back.put("data", d);
                back.put("count", ret.getInteger("count"));
                return back;

            } else {
                return ret;
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        }
    }


    //警情类别分析

    /**
     * type
     * 1. 重点刑事警情  2. 重点治安警情概况  3. 电信网络诈骗警情   4. 重大敏感警情   5. 矛盾纠纷警情概况   6. 火灾类警情概况   7. 侵财类警情概况
     *
     * @param data
     * @return
     */
    private JSONObject jqTypeAnalyze(JSONObject data) {

        JSONObject back = ErrNo.set(0);
        List<JSONObject> ret = new ArrayList<>();
        List<JSONObject> dets = new ArrayList<>();
        OracleHelper ora = null;
        String sql = "";
        String subSql = "";
        String searchType = "";
        String unit = "************";

        try {
            ora = new OracleHelper("ora_hl");

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            }

            String type = RIUtil.dicts.get(unit).getString("type");
            if (type.equals("23") || type.equals("24") || type.equals("28")) {
                subSql = subSql + " and fj='" + unit.substring(0, 6) + "000000' ";
            } else if (type.equals("25") || type.equals("26")) {
                subSql = subSql + " and pcs='" + unit.substring(0, 8) + "0000' ";
            }

            if (data.containsKey("searchType") && data.getString("searchType").length() > 0) {
                searchType = data.getString("searchType");
            }

            //敏感警情
            if (searchType.equals("4")) {

                JSONObject click = new JSONObject();
                click.put("type", "jump");

                sql = "select * from (select * from DSJ_JQ where CJSJ01 > '2024-01-01 00:00:00' " + subSql + " order "
                        + "by CJSJ01 desc) where ROWNUM  <= 30";
                ret = ora.query(sql);

                for (int i = 0; i < ret.size(); i++) {
                    JSONObject one = ret.get(i);
                    logger.warn(one.toString());
                    JSONObject det = new JSONObject();
                    String title = one.getString("title");
                    String createTime = one.getString("create_time");
                    String content = one.getString("smalltext");
                    String richText = one.getString("newstext");
                    String id = one.getString("id");

                    det.put("title", title);
                    det.put("content", content);
                    det.put("time", createTime);
                    det.put("richText", richText);
                    det.put("id", id);

                    det.put("click", click);

                    dets.add(det);
                }
                back.put("data", dets);
                back.put("count", ret.size());
            }

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora.close();
        }
    }

}
