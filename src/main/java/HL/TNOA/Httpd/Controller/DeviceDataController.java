package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.SM2.KeyUtils;
import HL.TNOA.Lib.SM2.Sm2Utils;
import HL.TNOA.Lib.TNOAConf;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.security.PrivateKey;
import java.util.Base64;

@RestController
public class DeviceDataController {
    private static Logger logger = LoggerFactory.getLogger(DeviceDataController.class);
    private static String ip = "";

    @RequestMapping(method = {RequestMethod.POST}, path = {"/data/sm2/sendData"})
    public static JSONObject get_datas_dev(TNOAHttpRequest request) throws Exception {
        String pKey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgmQYDr0ASmfS9NKo3ewv2+cL9knZ4hKNOyJ" +
                "/RmwoAAmOgCgYIKoEcz1UBgi2hRANCAAQGc+/G5xcZYQsJ63htZmprGQxx9Cgg9m37dGZazoGH7" +
                "+HY15f1jzNBlVxpj7L7dOr0rZKrjhX3ETqQNCkK0wKw";
        PrivateKey privateKey = KeyUtils.createPrivateKey(pKey);

        JSONObject dat = request.getRequestParams();
        logger.warn(dat.toString());
        String enData = dat.getString("data");

        byte[] decode = Base64.getDecoder().decode(enData);
        byte[] decrypt = Sm2Utils.decrypt(decode, privateKey);
        System.out.println("解密数据：" + new String(decrypt));

        JSONObject back = new JSONObject();
        back.put("success", true);
        back.put("code", "999999");
        back.put("message", "操作成功");

        back.put("time", System.currentTimeMillis());
        back.put("data", true);

        return back;

    }

    private static JSONObject getVersion(JSONObject data) {
        String version = "";
        JSONObject back = ErrNo.set(0);
        if (data.containsKey("version") && data.getString("version").length() > 0) {
            version = data.getString("version");
        } else {
            return ErrNo.set(500003);
        }
        String filePath = TNOAConf.get("file", "sou_path") + "app/";
        //logger.warn(filePath);
        File file = new File(filePath);
        String[] files = file.list();
        String appName = "zhpcs_";
        if (files.length > 0) {
            logger.warn(files[0]);
            String fileName = files[0].replace(appName, "").replace(".apk", "");
            if (version.equals(fileName)) {
                back.put("url", "");

            } else {
                back.put("url", files[0]);
            }
        }


        return back;

    }

}
