package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.Lib.TNOAConf;
import HL.TNOA.wechat.HttpConnection;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@RestController
public class ImportExamineController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/import_file"})
    public JSONObject importfile(TNOAHttpRequest request) throws Exception {

        JSONObject result = ErrNo.set(0);
        JSONObject data = request.getRequestParams();
        InfoModelHelper mysql = null;
        try {
            mysql = request.openInfoImpl();
            String file_id = "";
            String policeId = "";

            if (data.containsKey("file_id") && data.getString("file_id").length() > 0) {
                file_id = data.getString("file_id");
            } else {
                return ErrNo.set(490012);
            }

            boolean flag = false;
            boolean flag2 = false;
            boolean isAdmin = false;

            JSONObject userInfo = getUserInfo(request);
            JSONArray roleId = userInfo.getJSONArray("role_id");
            policeId = userInfo.getString("id_num");
            logger.warn("id_num--->" + policeId);
            for (Object o : roleId) {
                logger.warn("role_id---->" + o.toString());
                //管理员
                if ("hlcheckmanager".equals(o.toString()) || "ztchecksmanager".equals(o.toString())) {
                    flag = true;
                }
                //分管民警
                if ("hlcheckpolice".equals(o.toString())) {
                    flag2 = true;
                }
            }

            if (flag && flag2) {
                isAdmin = true;
            } else if (flag) {
                isAdmin = true;
            }

            ArrayList<String> confs = new ArrayList<>();

            String s = "select type,name,value from kh_control where type = 'kh_total'";
            List<JSONObject> control = mysql.query(s);

            String pid = "";
            String time = "";

            for (int i = 0; i < control.size(); i++) {
                JSONObject c = control.get(i);
                String name = c.getString("name");
                String value = c.getString("value");
                if ("total_plan".equals(name)) {
                    pid = value;
                }
                if ("total_month".equals(name)) {
                    time = value;
                }
            }

            s = "select * from kh_plan where id = '" + pid + "'";
            List<JSONObject> list = mysql.query(s);

            if (list.size() == 0) {
                return ErrNo.set(490012);
            } else {
                JSONObject plan = list.get(0);

                String fjConfig = plan.getString("fj_config");
                String[] fjs = fjConfig.split(",");
                for (int j = 0; j < fjs.length; j++) {
                    String s1 = fjs[j];
                    if (StrUtil.isBlank(s1)){
                        continue;
                    }
                    if (isAdmin) {
                        confs.add(s1);
                    } else {
                        String respPolice = RIUtil.kh_configs.get(s1).getString("resp_police");
                        if (respPolice.equals(policeId)) {
                            confs.add(s1);
                        }
                    }

                }

                String pcsConfig = plan.getString("pcs_config");
                String[] pcss = pcsConfig.split(",");
                for (int j = 0; j < pcss.length; j++) {
                    String s1 = pcss[j];
                    if (isAdmin) {
                        confs.add(s1);
                    } else {
                        String respPolice = RIUtil.kh_configs.get(s1).getString("resp_police");
                        if (respPolice.equals(policeId)) {
                            confs.add(s1);
                        }
                    }
                }

                String zrqConfig = plan.getString("zrq_config");
                String[] zrqs = zrqConfig.split(",");
                for (int j = 0; j < zrqs.length; j++) {
                    String s1 = zrqs[j];
                    if (isAdmin) {
                        confs.add(s1);
                    } else {
                        String respPolice = RIUtil.kh_configs.get(s1).getString("resp_police");
                        if (respPolice.equals(policeId)) {
                            confs.add(s1);
                        }
                    }
                }

            }

            if (confs.size() > 0) {

                long t1 = System.currentTimeMillis();
                result = ReadExcelList(file_id, confs, time);
                long t2 = System.currentTimeMillis();
                System.out.println("ReadFiles==========" + (t2 - t1));
                return result;

            } else {
                return ErrNo.set(490012);
            }
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }


    private JSONObject ReadExcelList(String file_id, ArrayList<String> confs, String time) {
        InfoModelHelper mysql = null;
//        FileInputStream fis = null;
        Workbook wb = null;
        try {
            mysql = InfoModelPool.getModel();

            String sql = "select * from upload where id='" + file_id + "'";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                JSONObject one = list.get(0);
//                String file_name = TNOAConf.get("file", "img_path") + one.getString("file_path") + one.getString(
//                        "file_name");
//                logger.warn(file_name);

                String filePath = one.getString("file_path");
                String fileName = one.getString("file_name");
                String filePName = "hl/" + filePath + fileName;
                String endPoint = "http://10.34.251.34:50101/obs-qjjc-tyyh";
                String url = endPoint + "/" + filePName;
                String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                logger.warn("考核数据导入文件地址-------> url:{}", url);
                byte[] bytes = HttpUtil.downloadBytes(url);
                try (InputStream fis = new ByteArrayInputStream(bytes)){
                    //判断文件是否存在
                        String[] split = fileName.split("\\.");  //.是特殊字符，需要转义！！！！！
                        int l = split.length - 1;

                        //根据文件后缀（xls/xlsx）进行判断
                        if ("xls".equalsIgnoreCase(split[l])) {
                            wb = new HSSFWorkbook(fis);
                        } else if ("xlsx".equalsIgnoreCase(split[l])) {
                            wb = new XSSFWorkbook(fis);
                        } else {
                            System.out.println("文件类型错误!");
                            return ErrNo.set(490020);
                        }

                        //开始解析
                        int sheetsCount = wb.getNumberOfSheets();
                        ArrayList<String> err = new ArrayList<>();
                        //读取sheet
                        for (int i = 0; i < sheetsCount; i++) {
                            Sheet sheet = wb.getSheetAt(i);     //读取sheet 0

                            int lastRowIndex = sheet.getLastRowNum();

                            if (lastRowIndex < 3) {
                                System.out.println("文件数据为空!");
                                break;
                            }

                            Row head2 = sheet.getRow(1);
                            ArrayList<JSONObject> configs = new ArrayList<>();

                            for (int j = 2; j < head2.getLastCellNum(); j++) {
                                Cell cell = head2.getCell(j, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK);
                                String v = cell.toString().trim();

                                JSONObject config = new JSONObject();
                                try {

                                    if (RIUtil.kh_configs.containsKey(v) && confs.contains(v)) {
                                        JSONObject obj = RIUtil.kh_configs.get(v);
                                        config.put("kh_name", obj.getString("kh_name"));
                                        config.put("id", obj.getString("id"));
                                        config.put("full_mark", obj.getString("full_mark"));
                                        config.put("point", obj.getString("point"));
                                        config.put("index", j);
                                        configs.add(config);
                                    }

                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }

                            String CONS = "";
                            String ORGS = "";
                            String v = "";
                            long t3 = System.currentTimeMillis();
                            for (int j = 3; j <= lastRowIndex; j++) {
                                Row row = sheet.getRow(j);

                                String org = row.getCell(1, Row.MissingCellPolicy.RETURN_NULL_AND_BLANK).toString().trim();
                                if (org.length() < 4) {
                                    continue;
                                }
                                ORGS = ORGS + "'" + org + "',";

                                try {
                                    for (JSONObject config : configs) {
                                        String configId = config.getString("id");
                                        int index = config.getInteger("index");
                                        String full_mark = config.getString("full_mark");
                                        String point = config.getString("point");

                                        try {
                                            String score = row.getCell(index,
                                                    Row.MissingCellPolicy.RETURN_NULL_AND_BLANK).toString().trim();
                                            String detail = row.getCell(index + 1,
                                                    Row.MissingCellPolicy.RETURN_NULL_AND_BLANK).toString().trim();
                                            Double.parseDouble(score);
                                            if ("".equals(score.trim())) {
                                                continue;
                                            }
                                            BigDecimal b1 = new BigDecimal(full_mark);
                                            BigDecimal b2 = new BigDecimal(score);
                                            BigDecimal b3 = new BigDecimal(0);
                                            if (b1.compareTo(b2) < 0) {
                                                score = full_mark;
                                            }
                                            if (b2.compareTo(b3) < 0) {
                                                score = "0";
                                            }

                                            CONS = CONS + "'" + configId + "',";
                                            String s = "('" + configId + "','" + org + "','" + score + "','" + detail +
                                                    "','" + time + "','" + create_time + "','1'),";
                                            v = v + s;
                                        } catch (Exception ex) {

                                            String score = "";
                                            String detail = "";
                                            CONS = CONS + "'" + configId + "',";
                                            if (point.equals("2")) {
                                                score = full_mark;
                                            } else {
                                                score = "0";
                                            }
                                            String s = "('" + configId + "','" + org + "','" + score + "','" + detail +
                                                    "','" + time + "','" + create_time + "','1'),";
                                            v = v + s;
                                            err.add("第" + (i + 1) + "张表第" + (j + 1) + "行" + "第" + (index + 1) + "列");

                                        }
                                    }
//
                                } catch (Exception ex) {
                                    logger.error(Lib.getTrace(ex));
                                }
                            }
                            long t4 = System.currentTimeMillis();
                            System.out.println("数据封装==============" + (t4 - t3));

                            if (CONS.length() > 0 && ORGS.length() > 0 && v.length() > 0) {
                                CONS = CONS.substring(0, CONS.length() - 1);
                                ORGS = ORGS.substring(0, ORGS.length() - 1);
                                v = v.substring(0, v.length() - 1);

                                String sqls =
                                        "update kh_det set isdelete =2,delete_time='"
                                                + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())
                                                + "' where config_id in (" + CONS + ") and org_id in (" + ORGS + ") and " +
                                                "time = '" + time + "' ";
                                mysql.update(sqls);

                                sqls = "insert kh_det(config_id,org_id,score,detail,time,create_time,isdelete) " +
                                        "values" + v;
                                mysql.update(sqls);
                            } else {
                                err.add("第" + (i + 1) + "张表数据缺失，导入失败");
                            }


                        }

                        if (err.size() > 0) {
                            return ErrNo.set(null, 490013, err.toString());
                        } else {
                            return ErrNo.set(0);
                        }

                }

            }
            return ErrNo.set(490020);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(490020);
        } finally {
            InfoModelPool.putModel(mysql);
            try {
                if (wb != null) {
                    wb.close();
                    IOUtils.closeQuietly(wb);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private JSONObject getUserInfo(TNOAHttpRequest request) {
        InfoModelHelper mysql = null;
        JSONObject uu = null;
        try {
            mysql = InfoModelPool.getModel();
            String token = request.getHeader("token");
            String unUrl = TNOAConf.get("HttpServ", "uni_url") + "/token";

            String back = HttpConnection.post_token(unUrl, token, new JSONObject(),
                    request.getRequestParams().getString("X-Real-IP"));
            JSONObject ret = JSONObject.parseObject(back);
            if (ret.containsKey("errno") && ret.getInteger("errno") == 0) {
                JSONObject dd = ret.getJSONObject("data");
                String police_id = dd.getString("police_id");

                JSONArray roleid = dd.getJSONArray("role_id");
                JSONObject org = dd.getJSONArray("organization").getJSONObject(0);

                String sql = "select * from user where police_id='" + police_id + "'";
                List<JSONObject> list = mysql.query(sql);
                if (list.size() != 1) {
                    logger.warn("200014");
                    return ErrNo.set(200014);
                } else {

                    int status = list.get(0).getInteger("status");
                    if (status == 3) {
                        logger.warn("200012");
                        return ErrNo.set(200012);
                    }
                }

                uu = list.get(0);

                uu.put("role_id", roleid);
                uu.put("organization", org);

                return uu;
            } else {
                logger.error(String.valueOf(ret));
                return ret;
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(490004);
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

}
