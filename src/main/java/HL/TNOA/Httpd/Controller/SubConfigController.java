package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class SubConfigController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/sub_config"})

    public JSONObject get_subConfig(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        InfoModelHelper mysql = null;
        try {
            mysql = request.openInfoImpl();
            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("get_subConfig")) {
                    return getSubConfig(data, mysql);
                } else if (opt.equals("create_subConfig")) {
                    logger.warn("sub_config--->" + request.getRequestParams().toString());
                    return createSubConfig(data, mysql);
                } else {
                    return ErrNo.set(502003);
                }
            } else {
                return ErrNo.set(502003);
            }
        }finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private JSONObject createSubConfig(JSONObject data, InfoModelHelper mysql) {
        String user_id = "";
        String sub_name = "";
        String config = "";
        JSONObject back = ErrNo.set(0);
        try {
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
            } else {
                return ErrNo.set(502001);
            }
            if (data.containsKey("sub_name") && data.getString("sub_name").length() > 0) {
                sub_name = data.getString("sub_name");

            } else {
                return ErrNo.set(502001);
            }
            if (data.containsKey("config") && data.getString("config").length() > 0) {
                config = data.getString("config");

            } else {
                return ErrNo.set(502001);
            }

            String sql =
                    "select id from sub_config where user_id='" + user_id + "' and sub_name='" + sub_name + "' " +
                            "and " + "isdelete=1";

            String id = mysql.query_one(sql, "id");


            if (id != null && id.length() > 0) {
                data.remove("opt_user");
                RIUtil.UpdateSql(data, "sub_config", id);
            } else {

                data.remove("opt_user");
                RIUtil.JsonInsert(data, "sub_config");
            }

            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(502002);
        }


    }

    private JSONObject getSubConfig(JSONObject data, InfoModelHelper mysql) {
        JSONObject back = ErrNo.set(0);
        String user_id = "";
        String sub_sql = " and 1=1 ";
        try {
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
            } else {
                return ErrNo.set(502001);
            }
            if (data.containsKey("sub_name") && data.getString("sub_name").length() > 0) {
                String sub_name = data.getString("sub_name");
                sub_sql = " and sub_name='" + sub_name + "' ";

            }

            String sql = "select * from sub_config where user_id='" + user_id + "' " + sub_sql + " and isdelete=1 ";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                back.put("data", list);
            } else {
                back.put("data", new JSONArray());
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(502002);
        }

    }


}
