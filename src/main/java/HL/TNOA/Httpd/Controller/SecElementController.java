package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
public class SecElementController {
    private static Logger logger = LoggerFactory.getLogger(SecElementController.class);
    private static String ip = "";

    @RequestMapping(method = {RequestMethod.POST}, path = {"/sec_element"})
    @PassToken
    public static JSONObject get_ele(TNOAHttpRequest reuest) throws Exception {

        String opt = "";
        ip = reuest.getRemoteAddr();
        JSONObject data = reuest.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_sec_element")) {
                return getSecElement(data);
            } else if (opt.equals("update_sec_element")) {
                logger.warn("sec_element-->" + data);
                return updateSecElement(data, ip);
            } else if (opt.equals("delete_sec_element")) {
                return deleteSecElement(data, ip);
            } else if (opt.equals("create_sec_element")) {
                return createSecElement(data, ip);
            } else if (opt.equals("get_sec_element_model")) {
                return GetSecElementModel(data);
            } else {
                return ErrNo.set(469009);
            }
        } else {
            return ErrNo.set(469009);
        }
    }

    private static JSONObject GetSecElementModel(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String unit = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");

        } else {
            return ErrNo.set(469006);
        }
        try {
            mysql = InfoModelPool.getModel();
            back.put("data", getSecElementContent(mysql, unit));
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(469005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject createSecElement(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String yyyyMM = "";
            String police_id = "";
            String community_id = "";
            String content = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String isdelete = "1";
            String unit = "";
            if (data.containsKey("yyyyMM") && data.getString("yyyyMM").length() > 0) {
                yyyyMM = data.getString("yyyyMM");
            } else {
                return ErrNo.set(469002);
            }
            if (data.containsKey("police_id") && data.getString("police_id").length() > 0) {
                police_id = data.getString("police_id");
            } else {
                return ErrNo.set(469002);
            }
            if (data.containsKey("community_id") && data.getString("community_id").length() > 0) {
                community_id = data.getString("community_id");
            } else {
                return ErrNo.set(469002);
            }
            if (data.containsKey("content") && data.getString("content").length() > 0) {
                content = data.getString("content");
            } else {
                return ErrNo.set(469002);
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
                if (unit.length() == 0) {
                    unit = RIUtil.users.get(create_user).getString("unit");
                }
            } else {
                return ErrNo.set(469002);
            }

            String sqls = "insert sec_element (yyyyMM,police_id,community_id,content,create_user,create_time," +
                    "isdelete,unit)values('" + yyyyMM + "','" + police_id + "','" + community_id + "','" + content +
                    "','" + create_user + "','" + create_time + "','" + isdelete + "','" + unit + "')";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建治安要素", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(469001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private static JSONObject getSecElement(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";

            String id = "";
            String yyyyMM = "";
            String opt_user = "";
            String community_id = "";
            String unit = "";


            if (data.containsKey("yyyyMM") && data.getString("yyyyMM").length() > 0) {
                yyyyMM = data.getString("yyyyMM");
                sql = sql + " yyyyMM='" + yyyyMM + "' and ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " unit='" + unit + "' and ";
            } else {
                return ErrNo.set(469006);
            }

            String sqls = "select * from sec_element where 1=1 and " + sql + " isdelete=1";
            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            logger.warn(list.toString());
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));

            } else {
                //插入一条
                JSONArray content = getSecElementContent(mysql, unit);
                sqls = "select police,community from community_check where month='" + yyyyMM.substring(2,
                        yyyyMM.length()) + "' and community!='all' and unit='" + unit + "'";
                List<JSONObject> comms = mysql.query(sqls);
                if (comms.size() > 0) {
                    for (int c = 0; c < comms.size(); c++) {
                        JSONObject one = comms.get(c);
                        String police = one.getString("police");
                        String commid = one.getString("community");
                        sqls = "INSERT INTO `sec_element`(`yyyyMM`, `police_id`, `community_id`, `content`, " +
                                "`create_user`, `create_time`,unit) " + "VALUES ( '" + yyyyMM + "', '" + police + "',"
                                + " '" + commid + "', '" + content + "', " + "'', '" + new SimpleDateFormat("yyyy-MM" + "-dd" + " HH:mm:ss").format(new Date()) + "','" + unit + "');";
                        mysql.update(sqls);
                        logger.warn(sqls);


                    }
                }
                sqls = "select * from sec_element where 1=1 and " + sql + " isdelete=1";
                logger.warn(sqls);
                list = mysql.query(sqls);

                back.put("data", RelaInfo(list, mysql));

            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(469005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONArray getSecElementContent(InfoModelHelper mysql, String unit) throws Exception {
        String sql = "select * from sec_element_model where unit='" + unit + "' order by `index`";
        List<JSONObject> list = mysql.query(sql);
        JSONArray all = new JSONArray();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String key = one.getString("key");
            String checkes = one.getString("checks");
            int index = one.getInteger("index");
            JSONObject ele = new JSONObject();
            ele.put("key", key);
            JSONArray cs = new JSONArray();
            String cks[] = checkes.split(",");
            for (int c = 0; c < cks.length; c++) {
                JSONObject cone = new JSONObject();
                String name = cks[c];
                if (name.equals("空")) {
                    name = "";
                }
                cone.put("name", name);
                cone.put("value", "");
                cs.add(cone);
            }
            ele.put("checks", cs);
            ele.put("index", index);

            all.add(ele);
        }
        //排序


        return all;
    }

    //*****relainfo************
    private static Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String police_id = one.getString("police_id");
            String polices[] = police_id.split(",");
            JSONArray pp = new JSONArray();
            for (int p = 0; p < polices.length; p++) {
                String pid = polices[p];
                JSONObject pone = RIUtil.users.get(pid);
                pp.add(pone);

            }
            one.put("polices", pp);
            String community_id = one.getString("community_id");
            one.put("community_name", RIUtil.RealDictNames(RIUtil.StringToList(community_id)));
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private static JSONObject updateSecElement(JSONObject dd, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String yyyyMM = "";
            String police_id = "";
            String community_id = "";
            String content = "";

            String opt_user = "";
            JSONArray ds = dd.getJSONArray("data");
            for (int i = 0; i < ds.size(); i++) {
                JSONObject data = ds.getJSONObject(i);
                if (data.containsKey("id") && data.getString("id").length() > 0) {
                    id = data.getString("id");
                    sql = sql + " id='" + id + "' , ";
                } else {
                    return ErrNo.set(469004);
                }
                if (data.containsKey("yyyyMM") && data.getString("yyyyMM").length() > 0) {
                    yyyyMM = data.getString("yyyyMM");
                    sql = sql + " yyyyMM='" + yyyyMM + "' , ";
                }
                if (data.containsKey("police_id") && data.getString("police_id").length() > 0) {
                    police_id = data.getString("police_id");
                    sql = sql + " police_id='" + police_id + "' , ";
                }
                if (data.containsKey("community_id") && data.getString("community_id").length() > 0) {
                    community_id = data.getString("community_id");
                    sql = sql + " community_id='" + community_id + "' , ";
                }
                if (data.containsKey("content") && data.getString("content").length() > 0) {
                    content = data.getString("content");
                    sql = sql + " content='" + content + "' , ";
                } else {
                    return ErrNo.set(469004);
                }

                if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                    opt_user = data.getString("opt_user");
                }
                String sqls = "update sec_element set " + sql + " isdelete=1  where id='" + id + "'";
                mysql.update(sqls);
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新治安要素", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(469003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private static JSONObject deleteSecElement(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(469008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(469008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update sec_element set isdelete =1,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id in('" + id + "')";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除治安要素", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(469007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }


}
