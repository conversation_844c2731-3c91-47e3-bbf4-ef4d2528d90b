package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
public class ClassController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/class"})

    public JSONObject get_class(TNOAHttpRequest request) throws Exception {
        logger.warn("class--->" + request.getRequestParams().toString());
        String opt = "";
        String ip = request.getRemoteAddr();
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if ("class_get".equals(opt)) {
                return getClasses(data);
            } else if ("class_create".equals(opt)) {
                logger.warn("class--->" + data.toString());
                return createClass(data, ip);
            } else if ("class_delete".equals(opt)) {
                return deleteClass(data, ip);
            } else if ("class_update".equals(opt)) {
                logger.warn("class--->" + data.toString());
                return updateClass(data, ip);
            } else {
                return ErrNo.set(435009);
            }
        } else {
            return ErrNo.set(435009);
        }

    }

    private JSONObject createClass(JSONObject data, String ip) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String class_name = "";
            String on_time = "";
            String off_time = "";
            String cycle = "0";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String class_type = "1";
            String isdelete = "1";
            String cycle_week = "";
            String isCheck = "0";
            String unit = "";
            if (data.containsKey("class_name") && data.getString("class_name").length() > 0) {
                class_name = data.getString("class_name");
            } else {
                return ErrNo.set(435002);
            }
            if (data.containsKey("on_time") && data.getString("on_time").length() > 0) {
                on_time = data.getString("on_time");
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit").split(",")[0];
            }
            if (data.containsKey("off_time") && data.getString("off_time").length() > 0) {
                off_time = data.getString("off_time");
            }
            if (data.containsKey("cycle") && data.getString("cycle").length() > 0) {
                cycle = data.getString("cycle");
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                if (unit.length() == 0) {
                    unit = RIUtil.users.get(create_user).getString("unit").split(",")[0];
                }

            } else {
                return ErrNo.set(435002);
            }

            if (data.containsKey("class_type") && data.getString("class_type").length() > 0) {
                class_type = data.getString("class_type");
            }

            if (data.containsKey("cycle_week") && data.getString("cycle_week").length() > 0) {
                cycle_week = data.getString("cycle_week");
            }

            if (("".equals(cycle) && cycle_week.length() == 0)) {
                logger.warn("无循环规则或循环规则重复");
                return ErrNo.set(435002);
            }

            if (data.containsKey("isCheck") && data.getString("isCheck").length() > 0) {
                isCheck = data.getString("isCheck");
            }
            String sqls = "insert class (class_name,on_time,off_time,cycle,create_user,create_time,class_type," +
                    "isdelete,cycle_week,isCheck,unit)values(encode('" + class_name + "','" + RIUtil.enName + "'),'" + on_time + "','" + off_time + "','" + cycle + "','" + create_user + "','" + create_time + "','" + class_type + "','" + isdelete + "','" + cycle_week + "','" + isCheck + "','" + unit + "')";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建排班", userlog.TYPE_OPERATE, ip);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(435001);
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return back;
    }

    private JSONObject getClasses(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {

            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 999;
            int page = 1;
            String id = "";
            String class_name = "";
            String on_time = "";
            String off_time = "";
            String cycle = "";
            String create_user = "";
            String create_time = "";
            String class_type = "";
            String unit = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("class_name") && data.getString("class_name").length() > 0) {
                class_name = data.getString("class_name");
                sql = sql + " class_name like'%" + class_name + "%' and ";
            }

            if (data.containsKey("cycle") && data.getString("cycle").length() > 0) {
                cycle = data.getString("cycle");
                sql = sql + " cycle='" + cycle + "' and ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit").split(",")[0];
                sql = sql + " unit='" + unit + "' and ";
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' and ";
            }

            if (data.containsKey("class_type") && data.getString("class_type").length() > 0) {
                class_type = data.getString("class_type");
                sql = sql + " class_type='" + class_type + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select decode(class_name,'1n2a3m4e') as class_name,id,on_time,off_time,cycle,cycle_week," +
                            "create_user,create_time,isCheck,unit" +
                            " from class where 1=1 and " + sql + " "
                            + "isdelete=1  limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select id from class where 1=1 and " + sql + " isdelete=1";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return back;
    }

    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            //create_user
            String create_user = one.getString("create_user");
            one.put("create_user", RIUtil.users.get(create_user));


            if (one.containsKey("unit")) {
                String unit = one.getString("unit").split(",")[0];
                try {
                    String unit_name = RIUtil.dicts.get(unit).getString("dict_name");
                    one.put("unit_name", unit_name);
                } catch (Exception ex) {
                    one.put("unit_name", "");
                }
            }
            back.add(one);
        }
        return back;
    }

    private JSONObject updateClass(JSONObject data, String ip) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String sql = "";
            String id = "";
            String class_name = "";
            String on_time = "";
            String off_time = "";
            String cycle = "0";
            String opt_user = "";
            String cycle_week = "";
            String isCheck = "0";
            String class_type = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(435004);
            }
            if (data.containsKey("class_name") && data.getString("class_name").length() > 0) {
                class_name = data.getString("class_name");
                sql = sql + " class_name=encode('" + class_name + "','" + RIUtil.enName + "') , ";
            }
            if (data.containsKey("on_time") && data.getString("on_time").length() > 0) {
                on_time = data.getString("on_time");
                sql = sql + " on_time='" + on_time + "' , ";
            }
            if (data.containsKey("off_time") && data.getString("off_time").length() > 0) {
                off_time = data.getString("off_time");
                sql = sql + " off_time='" + off_time + "' , ";
            }
            if (data.containsKey("cycle")) {
                cycle = data.getString("cycle");
            }
            if (cycle.length() == 0) {
                cycle = "0";
            }
            sql = sql + " cycle='" + cycle + "' , ";

            if (data.containsKey("cycle_week")) {
                cycle_week = data.getString("cycle_week");
                sql = sql + " cycle_week='" + cycle_week + "' , ";
            }
            if (data.containsKey("isCheck") && data.getString("isCheck").length() > 0) {
                isCheck = data.getString("isCheck");
                sql = sql + " isCheck='" + isCheck + "' , ";
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");

            }

            if (data.containsKey("class_type") && data.getString("class_type").length() > 0) {
                class_type = data.getString("class_type");
                sql = sql + " class_type='" + class_type + "' , ";
            }
            String sqls = "update class set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);

            sqls = "update class_table set on_time='" + on_time + "',off_time='" + off_time + "',class_type='" + class_type + "',isCheck='" + isCheck + "',class_name=encode('" + class_name + "','" + RIUtil.enName + "') where class_id='" + id + "' and isdelete=1";
            mysql.update(sqls);

            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新排次", userlog.TYPE_OPERATE, ip);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return back;
    }

    private JSONObject deleteClass(JSONObject data, String ip) throws Exception {
        String id = "";
        String opt_user = "";

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(435008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(435008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update class set isdelete=2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            sqls = "delete from class_group where class_id='" + id + "'";
            mysql.update(sqls);
            sqls = "delete from class_table where class_id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除排班", userlog.TYPE_OPERATE, ip);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return ErrNo.set(0);

    }

}
