package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class GroupController {
    private static Logger logger = LoggerFactory.getLogger(GroupController.class);
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/group"})
    public JSONObject groups(TNOAHttpRequest request) throws Exception {
        String ip = request.getRemoteAddr();
        logger.warn("group--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("group_get")) {
                return getGroup(data);
            } else if (opt.equals("group_create")) {
                logger.warn("group--->" + data.toString());
                return createGroup(data, ip);
            } else if (opt.equals("group_update")) {
                logger.warn("group--->" + data.toString());
                return updateGroup(data, ip);
            } else if (opt.equals("group_delete")) {
                return deleteGroup(data, ip);
            } else if (opt.equals("group_sort")) {
                logger.warn("group--->" + data.toString());
                return createSort(data);
            } else {
                return ErrNo.set(431003);
            }
        } else {
            return ErrNo.set(431003);
        }


    }

    private static JSONObject createSort(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String user_id = "";
            String type = "";
            String sort = "";
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
            } else {
                return ErrNo.set(443002);
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
            } else {
                return ErrNo.set(443002);
            }
            if (data.containsKey("index") && data.getString("index").length() > 0) {
                sort = data.getString("index");
            } else {
                return ErrNo.set(443002);
            }
            String sqls = "delete from sort_log where user_id='" + user_id + "' and type='" + type + "'";
            mysql.update(sqls);
            sqls = "insert sort_log (user_id,type,sort)values('" + user_id + "','" + type + "','" + sort + "')";
            mysql.update(sqls);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(443001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject deleteGroup(JSONObject data, String ip) throws Exception {
        String id = "";
        String opt_user = "";

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");

        } else {
            return ErrNo.set(431007);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");

        } else {
            return ErrNo.set(431007);
        }

        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update `group` set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);

            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除圈层", userlog.TYPE_OPERATE, ip);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(431008);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

    private static JSONObject updateGroup(JSONObject data, String ip) throws Exception {
        String id = "";
        String group_name = "";
        String members = "";
        String positions = "";
        String opt_user = "";
        String sql = "";
        String unit = "";

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");

        } else {
            return ErrNo.set(431006);
        }
        if (data.containsKey("group_name") && data.getString("group_name").length() > 0) {
            group_name = data.getString("group_name");
            sql = sql + " group_name=encode('" + group_name + "','" + RIUtil.enName + "') , ";
        }
        if (data.containsKey("members")) {
            members = data.getString("members");
            sql = sql + " members='" + members + "' , ";
        }
        if (data.containsKey("positions")) {
            positions = data.getString("positions");
            sql = sql + " positions='" + positions + "' , ";
        }
        if (data.containsKey("unit")) {
            unit = data.getString("unit");
            sql = sql + " unit='" + unit + "' , ";
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");

        } else {
            return ErrNo.set(431006);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls = "update `group` set " + sql + " isdelete=1 where id='" + id + "'";
            mysql.update(sqls);

            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新圈层", userlog.TYPE_OPERATE, ip);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(431005);
        } finally {
            InfoModelPool.putModel(mysql);
        }


        return ErrNo.set(0);

    }

    private static JSONObject getGroup(JSONObject data) throws Exception {
        String id = "";
        String group_name = "";
        String create_user = "";
        String user_id = "";
        String position_id = "";
        String name = "";
        String opt_user = "";
        int onoff = 1;
        String sql = "";

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
            sql = sql + " a.id='" + id + "' and ";
        }
        if (data.containsKey("group_name") && data.getString("group_name").length() > 0) {
            group_name = data.getString("group_name");

        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
            sql = sql + " b.id='" + create_user + "' and ";
        }
        if (data.containsKey("user_id") && data.getString("members").length() > 0) {

            user_id = data.getString("user_id");
            sql = sql + " a.members like'%" + user_id + "%' and ";
        }
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {

            String unit = data.getString("unit");
            sql = sql + " a.unit like'%" + unit + "%' and ";
        }
        if (data.containsKey("position_id") && data.getString("position_id").length() > 0) {
            position_id = data.getString("position_id");
            sql = sql + " a.positions like '%" + position_id + "%' and ";
        }
        if (data.containsKey("onoff") && data.getString("onoff").length() > 0) {
            onoff = data.getInteger("onoff");
            sql = sql + " b.onoff = '" + onoff + "' and ";
        }
        if (data.containsKey("name") && data.getString("name").length() > 0) {

            name = data.getString("name");

        } else {
            if (group_name.length() > 0) {
                sql = sql + " decode(group_name,'1n2a3m4e') like '%" + group_name + "%' and";
            }
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        }

        List<JSONObject> list = new ArrayList<>();
        JSONObject back = ErrNo.set(0);
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            if (name.length() > 0) {

                String sqls =
                        "select position,id from user where name like '%" + name +
                                "%' and status=1 and isdelete=1";
                List<JSONObject> ll = mysql.query(sqls);
                String ssqq = "";
                String mmqq = "";
                if (ll.size() > 0) {
                    HashMap<String, String> poss = new HashMap<>();
                    HashMap<String, String> ids = new HashMap<>();
                    // logger.warn(ll.toString());
                    for (int i = 0; i < ll.size(); i++) {
                        String pos = ll.get(i).getString("position");
                        poss.putAll(RIUtil.StringToList(pos));
                        String mid = ll.get(i).getString("id");
                        ids.putAll(RIUtil.StringToList(mid));

                    }
                    for (Map.Entry<String, String> one : poss.entrySet()) {
                        ssqq = ssqq + " a.positions like '%" + one.getKey() + "%' or";
                    }
                    for (Map.Entry<String, String> one : ids.entrySet()) {
                        mmqq = mmqq + " a.members like '%" + one.getKey() + "%' or";
                    }
                    ssqq = "(" + ssqq.substring(0, ssqq.length() - 3) + ") ";
                    mmqq = "(" + mmqq.substring(0, mmqq.length() - 3) + ")  ";
                    if (group_name.length() > 0) {
                        sql = sql + "(decode(a.group_name,'" + RIUtil.enName + "') like '%" + group_name + "%' or " + ssqq + " or " + mmqq + ") and";
                    } else {
                        sql = sql + "(" + ssqq + " or " + mmqq + ") and";
                    }
                }

            }

            String sqls =
                    "select decode(a.group_name,'" + RIUtil.enName + "') as group_name,a.members,a.positions,a.unit," +
                            "a.create_time,a.create_user, name from `group` a left " +
                            "join user b on b.id=a.create_user where 1=1 and " + sql + " 1=1 and a.isdelete=1 order by create_time desc";
            //logger.warn(sqls);
            list = mysql.query(sqls);
            //logger.warn(list.toString());
            if (list.size() > 0) {
                if (name.length() == 0) {
                    list = RealInfo(list, mysql);
                } else {
                    list = RealInfoOnlyName(list, mysql, name);
                }

                if (opt_user.length() > 0) {
                    list = SortGroup(list, mysql, opt_user);
                }
            }
            back.put("data", list);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(431004);
        } finally {
            InfoModelPool.putModel(mysql);
        }

        return back;
    }

    private static List<JSONObject> SortGroup(List<JSONObject> list, InfoModelHelper mysql, String opt_user) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        HashMap<String, JSONObject> listH = new HashMap<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String id = one.getString("id");
            listH.put(id, one);
        }


        HashMap<Integer, String> sortH = new HashMap<>();
        String sql = "select sort from sort_log where user_id='" + opt_user + "' and type=1";
        List<JSONObject> slist = mysql.query(sql);
        int mark = 1;
        if (slist.size() > 0) {
            JSONObject sorts = slist.get(0).getJSONObject("sort");
            for (Map.Entry<String, Object> s : sorts.entrySet()) {
                int index = Integer.parseInt(s.getKey());
                String did = String.valueOf(s.getValue());
                sortH.put(index, did);

            }
        }
        for (int i = 0; i < sortH.size(); i++) {
            try {
                String did = sortH.get(i);
                JSONObject one = listH.get(did);
                if (one != null) {
                    back.add(one);
                    listH.remove(did);
                }
            } catch (Exception ex) {

            }
        }
        if (listH.size() > 0) {
            for (Map.Entry<String, JSONObject> h : listH.entrySet()) {
                back.add(h.getValue());
            }
        }

        return back;
    }

    private static List<JSONObject> RealInfoOnlyName(List<JSONObject> list, InfoModelHelper mysql, String name) throws Exception {
        List<JSONObject> back = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            JSONArray mems = new JSONArray();
            JSONArray mems1 = new JSONArray();
            HashMap<String, String> m = new HashMap();
            JSONObject one = list.get(i);
            String members = one.getString("members");
            if (members.length() > 1) {
                m = RIUtil.StringToList(members);
                // logger.warn(m.toString());
                mems.addAll(RIUtil.UseridToNames(m));
                for (int a = 0; a < mems.size(); a++) {
                    JSONObject oo = mems.getJSONObject(a);
                    String on = oo.getString("name");
                    if (on.contains(name)) {
                        mems1.add(oo);
                    }

                }
            }
            String poss = one.getString("positions").replace("[", "").replace("]", "");

            JSONArray pos_users = new JSONArray();
            JSONArray pos_user1 = new JSONArray();
            if (poss.length() > 0) {
                HashMap ms = RIUtil.PositionsToUsers(RIUtil.StringToList(poss), mysql);
                pos_users = RIUtil.UseridToNames(ms);
                // logger.warn(pos_users.toString());
                for (int a = 0; a < pos_users.size(); a++) {
                    JSONObject oo = pos_users.getJSONObject(a);
                    String on = oo.getString("name");
                    //logger.warn(on + "-->" + name);
                    if (on.contains(name)) {
                        pos_user1.add(oo);
                    }

                }
            }
            one.put("pos_users", pos_user1);
            one.put("members", mems1);
            one.put("position_name", RIUtil.RealDictNames(RIUtil.StringToList(poss)));
            back.add(one);

        }

        return back;
    }

    private static List<JSONObject> RealInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            JSONArray mems = new JSONArray();
            HashMap<String, String> m = new HashMap();
            JSONObject one = list.get(i);
            String members = one.getString("members");
            if (members.length() > 1) {
                m = RIUtil.StringToList(members);
                // logger.warn(m.toString());
                mems.addAll(RIUtil.UseridToNames(m));
            }
            String poss = one.getString("positions");

            JSONArray pos_users = new JSONArray();
            if (poss.length() > 2) {
                HashMap ms = RIUtil.PositionsToUsers(RIUtil.StringToList(poss), mysql);
                pos_users = RIUtil.UseridToNames(ms);
            }
            one.put("pos_users", pos_users);
            one.put("members", mems);
            one.put("position_name", RIUtil.RealDictNames(RIUtil.StringToList(poss)));
            back.add(one);

        }

        return back;
    }


    private static JSONObject createGroup(JSONObject data, String ip) throws Exception {

        String group_name = "";
        String create_user = "";
        String members = "";
        String positions = "";
        String create_time = "";
        String isdelete = "1";
        String unit = "";

        String sql = "";

        if (data.containsKey("group_name") && data.getString("group_name").length() > 0) {
            group_name = data.getString("group_name");
        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
        }
        if (data.containsKey("members") && data.getString("members").length() > 0) {
            members = data.getString("members");
        }
        if (data.containsKey("positions") && data.getString("positions").length() > 0) {
            positions = data.getString("positions");
        }
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
        }
        create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());


        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "insert into `group` (group_name,create_user,members,positions,create_time,isdelete,unit) " +
                            "values" + " (encode('" + group_name + "','" + RIUtil.enName + "'),'" + create_user + "'," +
                            "'" + members + "'," + "'" + positions + "','" + create_time + "','" + isdelete + "','" + unit + "') ";
            logger.warn(sqls);
            mysql.update(sqls);

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(431001);
        } finally {
            InfoModelPool.putModel(mysql);
        }

        UserLog userlog = new UserLog();
        userlog.log(mysql, create_user, "创建圈层", userlog.TYPE_OPERATE, ip);
        return ErrNo.set(0);

    }


}
