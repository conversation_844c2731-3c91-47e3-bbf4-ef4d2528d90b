package HL.TNOA.Httpd.Controller;

import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CaseListController {
    private static Logger logger = LoggerFactory.getLogger(CaseListController.class);

    public static JSONObject createCaseList(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String case_id = "";
            String list_name = "";
            String end_time = "";
            String finish_time = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "1";
            String notice_cycle = "0,0";

            if (data.containsKey("case_id") && data.getString("case_id").length() > 0) {
                case_id = data.getString("case_id");
            } else {
                return ErrNo.set(458002);
            }
            if (data.containsKey("list_name") && data.getString("list_name").length() > 0) {
                list_name = data.getString("list_name");
            } else {
                return ErrNo.set(458002);
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time").replace("|", " ");
            }
            if (data.containsKey("finish_time") && data.getString("finish_time").length() > 0) {
                finish_time = data.getString("finish_time").replace("|", " ");
            }

            long cycle_seconds = 0;
            if (data.containsKey("notice_cycle") && data.getString("notice_cycle").length() > 0) {
                notice_cycle = data.getString("notice_cycle");
                String cyc[] = notice_cycle.split(",");
                int d = Integer.parseInt(cyc[0]);
                int m = Integer.parseInt(cyc[1]);
                cycle_seconds = d * 24 * 60 * 60 * 1000 + m * 60 * 60 * 1000;

            }
            String notice_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());


            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                return ErrNo.set(458002);
            }

            // String near_time=""
            String sqls = "insert case_list (case_id,list_name,end_time,finish_time," +
                    "create_user,create_time,isdelete,notice_time,notice_cycle,cycle_seconds)" +
                    "values('" + case_id + "',encode('" + list_name + "','" + RIUtil.enName + "'),'" + end_time + "'," +
                    "'" + finish_time + "'," +
                    "'" + create_user + "','" + create_time + "','" + isdelete + "','" + notice_time + "','" + notice_cycle + "','" + cycle_seconds + "')";
            mysql.update(sqls);

            CaseController.addLogs(new ArrayList<>(), mysql, case_id, "103", "", "创建清单 " + list_name, create_user);
            sqls = "update case_info set status=0 where id='" + case_id + "' ";
            mysql.update(sqls);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(458001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    public static JSONObject getCaseList(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String case_id = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("case_id") && data.getString("case_id").length() > 0) {
                case_id = data.getString("case_id");
                sql = sql + " case_id='" + case_id + "' and ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls = "select *,decode(list_name,'" + RIUtil.enName + "') as list_name from case_list where 1=1 " +
                    "and " + sql + " isdelete=1  limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select id from case_list where 1=1 and " + sql + " isdelete=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(458005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private static Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String create_user = one.getString("create_user");
            one.put("create_user", RIUtil.users.get(create_user));
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    public static JSONObject updateCaseList(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String case_id = "";
            String list_name = "";
            String end_time = "";
            String finish_time = "";
            String create_user = "";
            String create_time = "";
            String opt_user = "";
            String old_end_time = "";
            List<String> logs = new ArrayList<>();
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                String s = "select end_time from case_list where id='" + id + "'";
                old_end_time = mysql.query_one(s, "end_time");

            } else {
                return ErrNo.set(458004);
            }
            if (data.containsKey("case_id") && data.getString("case_id").length() > 0) {
                case_id = data.getString("case_id");
                sql = sql + " case_id='" + case_id + "' , ";
            }
            if (data.containsKey("list_name") && data.getString("list_name").length() > 0) {
                list_name = data.getString("list_name");
                sql = sql + " list_name=encode('" + list_name + "','" + RIUtil.enName + "') , ";
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time");
                if (!old_end_time.equals(end_time)) {
                    sql = sql + " end_time='" + end_time + "' , ";
                    String log = "修改自报时间：" + old_end_time + "->" + end_time;
                    logs.add(log);
                }
            }
            if (data.containsKey("finish_time")) {
                finish_time = data.getString("finish_time");
                sql = sql + " finish_time='" + finish_time + "' , ";
                if (finish_time.length() > 0) {
                    logs.add("添加完成时间");
                } else {
                    logs.add("取消完成");
                }
            }
            if (data.containsKey("create_time") && data.getString("create_time").length() > 0) {
                create_time = data.getString("create_time");
                sql = sql + " create_time='" + create_time + "' , ";
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            String sqls = "update case_list set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);

            CaseController.addLogs(logs, mysql, id, "103", "", "更新清单内容", opt_user);
            if (finish_time.length() > 0) {
                sqls = "select case_id from case_list where id='" + id + "'";
                case_id = mysql.query_one(sqls, "case_id");
                sqls = "select count(id) as count from case_object where current_index!='99' and case_id='" + case_id + "'";
                int count = mysql.query_count(sqls);
                sqls = "select count(id) from case_list where case_id='" + case_id + "' and isnull(finish_time) and " +
                        "isdelete=1";
                count = count + mysql.query_count(sqls);
                if (count == 0) {
                    sqls = "update case_info set  status=1 where id='" + case_id + "' ";
                    mysql.update(sqls);
                }

                sqls = "update case_list set cycle_seconds=0 where id='" + id + "'";
                mysql.update(sqls);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(458003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    public static JSONObject deleteCaseList(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(458008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(458008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update case_list set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            CaseController.addLogs(new ArrayList<>(), mysql, id, "103", "", "删除清单", opt_user);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(458007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

}
