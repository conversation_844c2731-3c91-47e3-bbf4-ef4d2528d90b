package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.Export.ExportInterface;
import HL.TNOA.Lib.Export.ExportXlsxHelper;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.awt.geom.Point2D;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static HL.TNOA.Httpd.Controller.StaticRhZfController.init;

@RestController
public class JQstaController {
    private static Logger logger = LoggerFactory.getLogger(JQstaController.class);
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/jqsta"})

    public JSONObject get_dict(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        InfoModelHelper mysql = null;
        try {

            mysql = request.openInfoImpl();
            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("get_ybtj")) {
                    return getYBTJ(data);
                } else if (opt.equals("get_jq_list")) {
                    return GetJQList(data);
                } else if (opt.equals("get_jq_list2table")) {
                    return GetJQList2Table(data);
                } else if (opt.equals("get_jq_list3table")) {
                    return GetJQList3Table(data);
                } else if (opt.equals("get_wlqc_list1")) {
                    return GetWLQCTree(data);
                } else if (opt.equals("get_wlqc_list")) {
                    return GetWLQCTree1(data);
                } else {
                    return ErrNo.set(505003);
                }
            } else {
                return ErrNo.set(505003);
            }
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private JSONObject GetJQList2Table(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        int isExp = 0;
        try {
            isExp = data.getIntValue("isExp");
        } catch (Exception ex) {

        }
        JSONObject ret = GetJQList(data);
        if (ret.getInteger("errno") == 0) {
            int count = ret.getInteger("count");
            if (count > 0) {
                String headN = "序号,警情编号,处警单位,处警类别,处警时间,发生地点,操作";
                String hdKeys = "num,JJBH,CHJDW_GAJGJGDM,CJLB,CJSJ01,CJDZ_DZMC,OPT";
                JSONArray heads = GetHeads(headN, hdKeys);

                JSONArray datas = ret.getJSONArray("data");
                JSONArray dets = new JSONArray();
                JSONArray dds = new JSONArray();
                for (int i = 0; i < datas.size(); i++) {
                    JSONObject one = datas.getJSONObject(i);
                    //logger.warn(one.toString());
                    String cjdw = one.getJSONObject("CHJDW_GAJGJGDM").getString("dict_name");
                    one.put("CHJDW_GAJGJGDM", cjdw);
                    String cjlb = "";
                    try {
                        cjlb = one.getJSONObject("CJLB").getString("dict_name");
                    } catch (Exception ex) {
                        // logger.warn(one.getJSONObject("CJLB").toString());
                    }
                    one.put("CJLB", cjlb);
                    one.put("num", (i + 1));
                    dds.add(one);
                    JSONObject det = new JSONObject();
                    JSONObject val = new JSONObject();
                    val.put("value", (i + 1));
                    det.put("num", val);

                    val = new JSONObject();
                    val.put("value", one.getString("JJBH"));
                    JSONObject click = new JSONObject();
                    click.put("type", "caseDetail");
                    JSONObject cols = new JSONObject();
                    cols.put("jjbh", one.getString("JJBH"));
                    click.put("opt", cols);
                    val.put("click", click);
                    det.put("JJBH", val);

                    val = new JSONObject();
                    val.put("value", cjdw);
                    det.put("CHJDW_GAJGJGDM", val);

                    val = new JSONObject();
                    val.put("value", cjlb);
                    det.put("CJLB", val);

                    val = new JSONObject();
                    val.put("value", one.getString("CJSJ01"));
                    det.put("CJSJ01", val);

                    val = new JSONObject();
                    val.put("value", one.getString("CJDZ_DZMC"));
                    det.put("CJDZ_DZMC", val);


                    val = new JSONObject();
                    val.put("value", "未盯办");
                    JSONObject ding = new JSONObject();
                    ding.put("type", "OPEN_DING_DIA");
                    val.put("click", ding);
                    det.put("OPT", val);


                    dets.add(det);
                }
                JSONObject ds = new JSONObject();
                ds.put("count", count);
                ds.put("head", heads);
                ds.put("body", dets);
                int fileId = -1;
                if (isExp == 1) {
                    //本页
                    fileId = ExportTables(dds, headN, hdKeys, "JQ");

                } else if (isExp == 2) {
                    //全部
                    data.put("page", 1);
                    data.put("limit", count);
                    ret = GetJQList(data);
                    if (ret.getInteger("errno") == 0) {
                        count = ret.getInteger("count");
                        if (count > 0) {

                            datas = ret.getJSONArray("data");

                            dds = new JSONArray();
                            for (int i = 0; i < datas.size(); i++) {
                                JSONObject one = datas.getJSONObject(i);
                                //logger.warn(one.toString());
                                String cjdw = one.getJSONObject("CHJDW_GAJGJGDM").getString("dict_name");
                                one.put("CHJDW_GAJGJGDM", cjdw);
                                String cjlb = "";
                                try {
                                    cjlb = one.getJSONObject("CJLB").getString("dict_name");
                                } catch (Exception ex) {
                                    // logger.warn(one.getJSONObject("CJLB").toString());
                                }
                                one.put("CJLB", cjlb);
                                one.put("num", (i + 1));
                                dds.add(one);
                            }
                        }
                    }

                    fileId = ExportTables(dds, headN, hdKeys, "JQ");

                } else {

                }
                ds.put("file_id", fileId);

                back.put("data", ds);

                return back;
            } else {
                return ret;
            }


        } else {
            return ret;
        }
    }

    private JSONObject GetJQList3Table(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        JSONObject ret = JQController.searchJQNoAddress(data);
        if (ret.getInteger("errno") == 0) {
            int count = ret.getInteger("count");
            if (count > 0) {
                String headN = "序号,警情编号,处警单位,处警类别,处警时间,发生地点";
                String hdKeys = "num,JJBH,CHJDW_GAJGJGDM,CJLB,CJSJ01,CJDZ_DZMC";
                JSONArray heads = GetHeads(headN, hdKeys);

                JSONArray datas = ret.getJSONArray("data");
                JSONArray dets = new JSONArray();
                for (int i = 0; i < datas.size(); i++) {
                    JSONObject one = datas.getJSONObject(i);
                    logger.warn(one.toString());
                    String cjdw = one.getJSONObject("CHJDW_GAJGJGDM").getString("dict_name");
                    one.put("CHJDW_GAJGJGDM", cjdw);
                    String cjlb = "";
                    try {
                        cjlb = one.getJSONObject("CJLB").getString("dict_name");
                    } catch (Exception ex) {
                        // logger.warn(one.getJSONObject("CJLB").toString());
                    }
                    one.put("CJLB", cjlb);
                    one.put("num", (i + 1));
                    JSONObject det = new JSONObject();
                    JSONObject val = new JSONObject();
                    val.put("value", (i + 1));
                    det.put("num", val);

                    val = new JSONObject();
                    val.put("value", one.getString("JJBH"));
                    JSONObject click = new JSONObject();
                    click.put("type", "caseDetail");
                    JSONObject cols = new JSONObject();
                    cols.put("jjbh", one.getString("JJBH"));
                    click.put("opt", cols);
                    val.put("click", click);
                    det.put("JJBH", val);

                    val = new JSONObject();
                    val.put("value", cjdw);
                    det.put("CHJDW_GAJGJGDM", val);

                    val = new JSONObject();
                    val.put("value", cjlb);
                    det.put("CJLB", val);

                    val = new JSONObject();
                    val.put("value", one.getString("CJSJ01"));
                    det.put("CJSJ01", val);

                    val = new JSONObject();
                    val.put("value", one.getString("CJDZ_DZMC"));
                    det.put("CJDZ_DZMC", val);


                    dets.add(det);
                }
                JSONObject ds = new JSONObject();
                ds.put("count", count);
                ds.put("head", heads);
                ds.put("body", dets);
                back.put("data", ds);

                return back;
            } else {
                return ret;
            }


        } else {
            return ret;
        }
    }


    private JSONArray GetHeads(String headN, String hdKeys) {
        JSONArray rets = new JSONArray();
        String[] hdns = headN.split(",");
        String[] hdks = hdKeys.split(",");

        for (int i = 0; i < hdks.length; i++) {
            JSONObject det = new JSONObject();
            String key = hdks[i];
            String value = hdns[i];
            det.put("key", key);
            det.put("value", value);
            rets.add(det);

        }
        return rets;
    }

    private JSONObject GetWLQCTree(JSONObject data) {

        JSONObject back = ErrNo.set(0);
        try {
            JSONArray type52 = RIUtil.GetDictByType(513);
            JSONArray res = new JSONArray();
            for (int i = 0; i < type52.size(); i++) {
                JSONObject one = type52.getJSONObject(i);
                String pmsion = one.getString("permission");
                String father_id = one.getString("father_id");
                String dict_name = one.getString("dict_name");
                if (pmsion.contains("QC")) {
                    if (father_id.contains("51-01") && !dict_name.contains("刑事")) {
                        dict_name = "刑事" + dict_name;
                    } else if (father_id.contains("51-02") && !dict_name.contains("治安")) {
                        dict_name = "治安" + dict_name;
                    }
                    one.put("dict_name", dict_name);

                    JSONArray type54 = RIUtil.GetDictByTypeFather(514, father_id);
                    one.put("child", type54);

                    logger.warn(one.toString());
                    res.add(one);
                }
            }


            logger.warn(res.toString());
            back.put("data", res);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }

    }

    private JSONObject GetWLQCTree1(JSONObject data) {

        JSONObject back = ErrNo.set(0);
        logger.warn(back.toString());
        String sql = "";
        InfoModelHelper mysql = null;
        String typeArr[] = {"盗窃", "诈骗", "抢劫", "抢夺", "敲诈勒索"};
        try {
            mysql = InfoModelPool.getModel();
            JSONArray res = new JSONArray();

            for (String type : typeArr) {
                JSONObject typeJson = new JSONObject();
                typeJson.put("dict_name", type);
                List<String> idList = new ArrayList<>();
                sql = "select * from dict where dict_name  like '%" + type + "%' and type = 513 and permission = " +
                        "'QC'" + " " + "and" + " " + "isdelete = 1";
                List<JSONObject> type52 = mysql.query(sql);
                for (JSONObject one : type52) {
                    String id = one.getString("id");
                    String father_id = one.getString("father_id");
                    String dict_name = one.getString("dict_name");

                    if (father_id.contains("51-01") && !dict_name.contains("刑事")) {
                        dict_name = "刑事" + dict_name;
                    } else if (father_id.contains("51-02") && !dict_name.contains("治安")) {
                        dict_name = "治安" + dict_name;
                    }
                    idList.add(id);
                    one.put("dict_name", dict_name);
                    JSONArray type54 = RIUtil.GetDictByTypeFather(514, id);
                    one.put("dets", type54);
                }
                typeJson.put("id", String.join(",", idList));
                typeJson.put("dets", type52);
                res.add(typeJson);
            }

            logger.warn(res.toString());
            back.put("data", res);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            if (mysql != null) {
                mysql.close();
                InfoModelPool.putModel(mysql);
            }
        }

    }

    private JSONObject GetJQList(JSONObject data) {
        try {
            JSONObject back = ErrNo.set(0);
            OracleHelper ora = null;

            String unit = "";
            String end_time = "";
            String start_time = "";

            String hb_start_time = "";
            String hb_end_time = "";
            String tb_start_time = "";
            String tb_end_time = "";
            String cjlb = "";
            String key = "";
            String jjbh = "";
            String code = "";
            String jqs = "";
            int is_mark = 0;
            int is_check = 0;
            String bjfs = "";
            int limit = 20;
            int page = 1;
            int isExp = -1;
            String cjnr = "";
            String zflx = "";
            String sql = "";
            String sub_sql = "";
            String file_id = "";
            String searchType = "1";


            if (data.containsKey("searchType") && data.getString("searchType").length() > 0) {
                searchType = data.getString("searchType");
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            if (data.containsKey("isExp")) {
                isExp = data.getInteger("isExp");
            }


            if (data.containsKey("zflx") && data.getString("zflx").length() > 0) {

                ora = new OracleHelper("ora_hl");

                zflx = data.getString("zflx");
                if (!zflx.equals("")) {
                    sub_sql += " and ( ";
                    if (zflx.contains("1")) {
                        sub_sql += " CZJZ_PDBS = 1 or ";
                    }
                    if (zflx.contains("2")) {
                        sub_sql += " SFQZ = 1 or ";
                    }
                    if (zflx.contains("3")) {
                        sub_sql += " SFHZF = 1 or ";
                    }
                    if (sub_sql.endsWith("or ")) {
                        sub_sql = sub_sql.substring(0, sub_sql.length() - 3);
                    }
                    sub_sql += " ) ";
                }

            }

            if (data.containsKey("code") && data.getString("code").length() > 0) {
                code = data.getString("code");
                String type = "";
                if (!zflx.equals("")) {
                    JSONObject done = RIUtil.dicts.get(code);

                    try {
                        type = done.getString("type");
                    } catch (Exception ex) {
                        type = "21";
                    }
                    logger.warn("type : " + type);

                    if (type.equals("25") || type.equals("26")) {
                        sub_sql += " and (CHJDW_GAJGJGDM like '%" + code.substring(0, 8) + "%') ";
                    } else if (type.equals("23")) {
                        sub_sql += " and (CHJDW_GAJGJGDM like '%" + code.substring(0, 6) + "%') ";
                    } else if (type.equals("22")) {
                        sub_sql += " and (CHJDW_GAJGJGDM like '%" + code.substring(0, 4) + "%') ";
                    } else if (type.equals("24")) {
                        String father_id = done.getString("father_id");
                        sub_sql = " and CHJDW_GAJGJGDM like '" + father_id.substring(0, 6) + "%' ";
                    } else if (type.equals("28")) {
                        code = code.substring(0, 6);
                        sub_sql = " and CHJDW_GAJGJGDM like '" + code + "%' ";
                    }
                }
            } else {
                return ErrNo.set(505001);
            }

            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time");
                sub_sql += " and CJSJ01 >= '" + start_time + " 00:00:00' ";
            } else {
                return ErrNo.set(505001);
            }

            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time");
                sub_sql += " and CJSJ01 <='" + end_time + " 23:59:59' ";
            } else {
                return ErrNo.set(505001);
            }

            if (data.containsKey("hb_start_time") && data.getString("hb_start_time").length() > 0) {
                hb_start_time = data.getString("hb_start_time");
                sub_sql += " and CJSJ01 >= '" + start_time + "' ";
            }

            if (data.containsKey("hb_end_time") && data.getString("hb_end_time").length() > 0) {
                hb_end_time = data.getString("hb_end_time");
                sub_sql += " and CJSJ01 <='" + end_time + "' ";
            }

            if (data.containsKey("tb_start_time") && data.getString("tb_start_time").length() > 0) {
                tb_start_time = data.getString("tb_start_time");
                sub_sql += " and CJSJ01 >= '" + start_time + "' ";
            }

            if (data.containsKey("tb_end_time") && data.getString("tb_end_time").length() > 0) {
                tb_end_time = data.getString("tb_end_time");
                sub_sql += " and CJSJ01 <='" + end_time + "' ";
            }
            if (data.containsKey("is_mark") && data.getString("is_mark").length() > 0) {
                is_mark = data.getInteger("is_mark");
                sub_sql += " and SFGLDZ_PDBZ=1  ";
            }
            if (data.containsKey("is_check") && data.getString("is_check").length() > 0) {
                is_check = data.getInteger("is_check");
                sub_sql += " and hczt = '1' ";
            }
            if (data.containsKey("bjfs") && data.getString("bjfs").length() > 0) {
                bjfs = data.getString("bjfs");
                bjfs = bjfs.replace("45-", "");
                sub_sql += " and bjfs = '" + bjfs + "' ";
            }

            if (data.containsKey("key") && data.getString("key").length() > 0) {
                key = data.getString("key");
            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");
            }

            if (data.containsKey("jqs") && data.getString("jqs").length() > 0) {
                jqs = data.getString("jqs");
            }
            if (data.containsKey("cjnr") && data.getString("cjnr").length() > 0) {
                cjnr = data.getString("cjnr");
                sub_sql += " and CLJG like '%" + cjnr + "%' ";
            }

            cjlb = key.split("\\_")[0];

            JSONObject det = new JSONObject();
            det.put("searchType", searchType);

            //如果是综合体且jqs是空 则直接返回空

            if (key.contains("zht") && jqs.length() == 0) {
                List<JSONObject> list = new ArrayList<>();
                back.put("data", list);
                back.put("count", 0);
                return back;
            }

            if (key.contains("_hb") && !key.contains("_p")) {
                start_time = hb_start_time + " 00:00:00";
                end_time = hb_end_time + " 23:59:59";

            } else if (key.contains("_tb") && !key.contains("_p")) {
                start_time = tb_start_time + " 00:00:00";
                end_time = tb_end_time + " 23:59:59";
            }

            if (key.contains("_hz")) {
                JSONObject done = RIUtil.dicts.get(cjlb);
                String type = "";
                if (done != null) {
                    type = done.getString("type");
                }
                String bq = "";
                if (type.equals("49")) {
                    JSONArray type53 = RIUtil.GetDictByType(53);
                    for (int t = 0; t < type53.size(); t++) {
                        JSONObject tone = type53.getJSONObject(t);
                        String father_id = tone.getString("father_id");
                        String id = tone.getString("id");
                        if (cjlb.equals(father_id)) {
                            bq = bq + id + ",";
                        }
                    }
                    if (bq.endsWith(",")) {
                        bq = bq.substring(0, bq.length() - 1);
                    }
                } else {
                    bq = cjlb;
                }


                det.put("bq", bq);
            } else if (key.contains("fsbw")) {
                String type = "";
                if (cjlb.contains("#")) {
                    String ids[] = cjlb.split("\\#");
                    type = ids[0];
                    cjlb = ids[1];

                } else {
                    type = cjlb;
                }
                det.put("type", type);


            } else if (key.contains("_hyxl")) {

                String type = "";
                if (cjlb.contains("#")) {
                    String ids[] = cjlb.split("\\#");
                    type = ids[0];
                    cjlb = ids[1];

                } else {
                    type = cjlb;
                }
                det.put("hyxl", type);

            }

            if (jjbh.length() > 0) {
                det.put("jjbh", jjbh);
            }
            det.put("start_time", start_time.replace(" 00:00:00", ""));
            det.put("end_time", end_time.replace(" 23:59:59", ""));
            if (key.contains("jzqy")) {
                det.put("xq", code);
            } else {
                //如果不是综合体再传code为unit
                if (jqs.equals("") && !key.contains("zht")) {
                    det.put("unit", code);
                }
            }
            if (is_check == 1) {
                det.put("hczt", "1");
            }
            if (is_mark == 1) {
                det.put("isMark", "1");
            }
            if (bjfs.length() > 0) {
                det.put("bjfs", bjfs);
            }
            if (jqs.length() > 0) {
                det.put("jqs", jqs);
            }
            det.put("cjnr", cjnr);
            det.put("cjlb", cjlb.replace("|", ","));

            if (isExp == 9) {
                det.put("isExp", 1);
                det.put("isDing", 9);
            } else {
                det.put("isExp", isExp);
            }
            det.put("isSat", 1);
            det.put("page", page);
            det.put("limit", limit);

            if (!zflx.equals("")) {
                //处理isMark


                sql = "select * from V_HZ_FW1 where 1=1 " + sub_sql;
                if (isExp != 1) {
                    sql += " OFFSET" + " " + (page - 1) * limit + " " + "ROWS FETCH NEXT" + " " + limit + " " + "ROWS"
 + " " + "ONLY";
                }

                logger.warn(sql);
                List<JSONObject> res = ora.query(sql);
                for (JSONObject js : res) {
                    String CJLB = js.getString("CJLB");
                    logger.warn(CJLB);
                    JSONObject done = RIUtil.dicts.get(CJLB);
                    logger.warn(done.toString());
                    js.put("CJLB", done);

                    String CHJDW_GAJGJGDM = js.getString("CHJDW_GAJGJGDM");
                    JSONObject done1 = RIUtil.dicts.get(CHJDW_GAJGJGDM);
                    js.put("CHJDW_GAJGJGDM", done1);

                }
                back.put("data", res);

                if (isExp == 1) {
                    JSONObject excel = new JSONObject();
                    excel.put("files", res);
                    logger.warn("开始导出");
                    file_id = String.valueOf(ExportTwo(excel.getJSONArray("files"), searchType));
                }

                sql = "select count(JJBH) as count from V_HZ_FW1 where 1=1 " + sub_sql;
                back.put("count", ora.query_count(sql));
                back.put("file_id", file_id);
            } else {
                back = JQController.searchJQNoAddress(det);
            }


            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    public static JSONObject getYBTJ(JSONObject data) {
        try {
            JSONObject back = ErrNo.set(0);
            String unit = "320400000000";
            String end_time = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String start_time = RIUtil.GetNextDate(end_time, -30);
            String hb_start_time = "";
            String hb_end_time = "";
            String tb_start_time = "";
            String tb_end_time = "";

            String cjlb = "";

            int searchLevel = 1;
            int searchType = 1;
            int searchWD = 0;
            int searchSub = 0;
            String fsbw = "";
            int isExp = 0;
            String uSql = "";
            String hyxl = "";
            String hzbq = "";
            int isMap = 0;
            String range = "";
            //0否1是
            int is_check = 0;
            int is_mark = 0;
            String bjfs = "";
            String region_name = "";
            String pysx = "";
            String cjnr = "";
            //租房类型   1  出租房  2群租房   3合租房
            String zflx = "";

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            }

            if (data.containsKey("isMap") && data.getString("isMap").length() > 0) {
                isMap = data.getInteger("isMap");
            }

            if (data.containsKey("range") && data.getString("range").length() > 0) {
                range = data.getString("range");
            }

            if (data.containsKey("searchLevel") && data.getString("searchLevel").length() > 0) {
                searchLevel = data.getInteger("searchLevel");
            }
            String type = RIUtil.dicts.get(unit).getString("type");
            if (type.equals("23") || type.equals("24") || type.equals("28")) {
                uSql = uSql + " and fj='" + unit.substring(0, 6) + "000000' ";
            } else if (type.equals("25") || type.equals("26")) {
                if (searchLevel == 3) {
                    uSql = uSql + " and zrq like '" + unit.substring(0, 8) + "%'  ";
                } else {
                    uSql = uSql + " and pcs='" + unit.substring(0, 8) + "0000' ";
                }
            }

            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time");
            }
            if (start_time.endsWith("00:00:00")) {
                start_time = start_time;
            } else {
                start_time = start_time + " 00:00:00";
            }


            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time");
            }
            end_time = end_time + " 23:59:59";

            if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {
                cjlb = data.getString("cjlb").trim();
            }

            if (data.containsKey("hb_start_time") && data.getString("hb_start_time").length() > 0) {
                hb_start_time = data.getString("hb_start_time");
            }

            if (data.containsKey("hb_end_time") && data.getString("hb_end_time").length() > 0) {
                hb_end_time = data.getString("hb_end_time");
            }

            if (data.containsKey("tb_start_time") && data.getString("tb_start_time").length() > 0) {
                tb_start_time = data.getString("tb_start_time");
            }

            if (data.containsKey("tb_end_time") && data.getString("tb_end_time").length() > 0) {
                tb_end_time = data.getString("tb_end_time");
            }

            if (data.containsKey("searchType") && data.getString("searchType").length() > 0) {
                searchType = data.getInteger("searchType");
            }
            if (data.containsKey("searchSub") && data.getString("searchSub").length() > 0) {
                searchSub = data.getInteger("searchSub");
            }
            if (data.containsKey("searchWD") && data.getString("searchWD").length() > 0) {
                searchWD = data.getInteger("searchWD");
            }
            if (data.containsKey("fsbw") && data.getString("fsbw").length() > 0) {
                fsbw = data.getString("fsbw");
            }
            if (data.containsKey("hyxl") && data.getString("hyxl").length() > 0) {
                hyxl = data.getString("hyxl");
            }
            if (data.containsKey("hzbq") && data.getString("hzbq").length() > 0) {
                hzbq = data.getString("hzbq");
            }

            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getInteger("isExp");
            }

            if (data.containsKey("is_check") && data.getString("is_check").length() > 0) {
                is_check = data.getInteger("is_check");
            }
            if (data.containsKey("is_mark") && data.getString("is_mark").length() > 0) {
                is_mark = data.getInteger("is_mark");
            }

            if (data.containsKey("bjfs") && data.getString("bjfs").length() > 0) {
                bjfs = data.getString("bjfs");
                bjfs = bjfs.replace("45-", "");
            }
            if (data.containsKey("region_name") && data.getString("region_name").length() > 0) {
                region_name = data.getString("region_name");
            }
            if (data.containsKey("pysx") && data.getString("pysx").length() > 0) {
                pysx = data.getString("pysx");
            }
            if (data.containsKey("cjnr") && data.getString("cjnr").length() > 0) {
                cjnr = data.getString("cjnr");
            }
            if (data.containsKey("zflx") && data.getString("zflx").length() > 0) {
                zflx = data.getString("zflx");
            }

            String lbs[] = cjlb.split(",");
            if (isMap == 0) {
                String group = "";
                String cby = "";
                JSONArray heads = new JSONArray();
                JSONObject hone = new JSONObject();

                if (searchLevel == 1) {
                    group = "FJ";
                    cby = "FJ ";
                } else if (searchLevel == 2) {
                    group = "PCS";
                    cby = "PCS ";
                } else {
                    group = "ZRQ";
                    cby = "ZRQ";
                }

                HashMap<String, JSONObject> results = new HashMap<>();

                if (searchType == 6) {
                    hone.put("key", "code");
                    hone.put("value", "小区编码");
                    heads.add(hone);
                    hone = new JSONObject();
                    hone.put("key", "name");
                    hone.put("value", "小区名称");
                    heads.add(hone);
                    hone = new JSONObject();
                    hone.put("key", "pcs");
                    hone.put("value", "派出所名称");
                    heads.add(hone);

                } else if (searchType == 8) {
                    hone.put("key", "code");
                    hone.put("value", "小区编码");
                    heads.add(hone);
                    hone = new JSONObject();
                    hone.put("key", "name");
                    hone.put("value", "街面道路名称");
                    heads.add(hone);
                    hone = new JSONObject();
                    hone.put("key", "pcs");
                    hone.put("value", "派出所名称");
                    heads.add(hone);
                } else {
                    hone.put("key", "code");
                    hone.put("value", "单位代码");

                    heads.add(hone);
                    hone = new JSONObject();
                    hone.put("key", "name");
                    hone.put("value", "名称");
                    heads.add(hone);
                    if (searchLevel == 2 && searchType != 7) {
                        hone = new JSONObject();
                        hone.put("key", "type2");
                        hone.put("value", "派出所类别");
                        heads.add(hone);
                    }
                }

                JSONObject rets = new JSONObject();
                if (searchType == 0 || searchType == 4) {
                    if (searchSub == 1)//刑事盗窃
                    {
                        cjlb = "51-01040300,51-01040310,51-01040310,51-01040395,51-01040320";
                        lbs = cjlb.split(",");
                        rets = GetZCTHB(start_time, end_time, group, uSql, searchWD, lbs, heads, cby, unit,
                                tb_start_time, tb_end_time, hb_start_time, hb_end_time, is_check, is_mark, bjfs, cjnr);
                    } else if (searchSub == 2)//治安盗窃
                    {
                        cjlb = "51-02040500,51-02040507,51-02040506,51-02040594,51-02040505,51-02040512";
                        lbs = cjlb.split(",");
                        rets = GetZCTHB(start_time, end_time, group, uSql, searchWD, lbs, heads, cby, unit,
                                tb_start_time, tb_end_time, hb_start_time, hb_end_time, is_check, is_mark, bjfs, cjnr);
                    } else if (searchSub == 3)//五类侵财
                    {
                        String names[] = {"五类侵财", "电信网络诈骗", "传统侵财"};
                        String idss[] =
                                {"51-02040600,51-02040700,51-02040500,51-02040300,51-01040400,51-01040500," + "51" +
                                        "-01040300," + "51-01040600", "51-01040500,51-02040700", "51-02040600," + "51" + "-02040500,"
                                 + "51" + "-02040300,51-01040400," + "51-01040300,51-01040600"};
                        rets = GetWLQC(start_time, end_time, group, uSql, searchWD, heads, cby, "_zc", names, idss,
 unit, tb_start_time, tb_end_time, hb_start_time, hb_end_time, is_check, is_mark, bjfs, cjnr,
                          searchLevel);


                    }
                    else if (searchSub == 4)//诈骗类
                    {
                        String names[] = {"诈骗", "电信网络诈骗", "贷款、代办信用卡", "刷单返利", "虚假购物、服务", "虚假网络婚恋、交友"};
                        String idss[] = {"51-01040400,51-01040500,51-02040600,51-02040700",//诈骗
                                "51-01040500,51-02040700", //电信
                                "51-01040501,51-02040701",//信用卡
                                "51-01040502,51-02040702", //刷单
                                "51-01040504,51-02040704",//虚假购物、服务
                                "51-01040509,51-02040709",//虚假网络婚恋、交友
                                "51-01040505,51-02040705",//虚假投资理财、博彩 51-01040505,51-02040705
                                "51-01040508,51-02040708"};//网络游戏产品、虚假交易

                        rets = GetWLQC(start_time, end_time, group, uSql, searchWD, heads, cby, "_zc", names, idss,
                         unit, tb_start_time, tb_end_time, hb_start_time, hb_end_time, is_check, is_mark, bjfs, cjnr,
                                searchLevel);
                    } else if (searchSub == 5)//五类侵财明细统计
                    {
                        String names[] = {"五类侵财", "盗窃", "诈骗", "抢劫", "抢夺", "敲诈勒索"};
                        String idss[] =
                         {"51-02040600,51-02040700,51-02040500,51-02040300,51-01040400,51-01040500," + "51" +
 "-01040300," + "51-01040600", "51-01040300,51-02040500", "51-01040400," + "51" + "-01040500,"
                          + "51" + "-02040600,51-02040700", "51-01040100", "51-01040200,51-02040200", "51" +
                                 "-01040600," + "51-02040300"};
                        rets = GetWLQC(start_time, end_time, group, uSql, searchWD, heads, cby, "_zc", names, idss,
                         unit, tb_start_time, tb_end_time, hb_start_time, hb_end_time, is_check, is_mark, bjfs, cjnr,
                          searchLevel);
                    } else {
                        //
                        rets = GetZCTHB(start_time, end_time, group, uSql, searchWD, lbs, heads, cby, unit,
                         tb_start_time, tb_end_time, hb_start_time, hb_end_time, is_check, is_mark, bjfs, cjnr);
                    }
                } else if (searchType == 2)//火灾
                {


                    if (hzbq.length() != 0) {
                        rets = GetHZ(start_time, end_time, group, uSql, heads, hzbq, cby, searchWD, unit,
                                tb_start_time, tb_end_time, hb_start_time, hb_end_time, is_check, is_mark, bjfs, cjnr);
                    } else if (!zflx.equals("")) {
                        rets = GetHZ2(start_time, end_time, group, uSql, heads, hzbq, cby, searchWD, unit,
tb_start_time, tb_end_time, hb_start_time, hb_end_time, is_check, is_mark, bjfs, zflx, cjnr);
                    }

                } else if (searchType == 3 || searchType == 5)//发生部位  发生单位
                {
                    if (searchType == 5 && hyxl.length() == 0) {
                        return ErrNo.set(505001);
                    } else {
                        rets = GetZYBW(start_time, end_time, group, uSql, heads, fsbw, hyxl, cby, searchWD,
                         searchType, lbs, unit, tb_start_time, tb_end_time, hb_start_time, hb_end_time, is_check,
                                is_mark, bjfs, cjnr);
                    }
                } else if (searchType == 6)//居住  02 小区 03 自然村
                {
                    if (hyxl.length() == 0) {
                        return ErrNo.set(505001);
                    } else {
                        uSql = uSql.replace("fj", "a.fj");
                        uSql = uSql.replace("pcs", "a.pcs");
                        uSql = uSql.replace("zrq", "a.zrq");
                        rets = GetJZQY(start_time, end_time, uSql, heads, fsbw, hyxl, searchWD, searchType, lbs,
                         tb_start_time, tb_end_time, hb_start_time, hb_end_time, is_check, is_mark, bjfs, region_name
                         , cjnr);
                    }
                } else if (searchType == 7) {
                    rets = GetJZQY1(start_time, end_time, uSql, heads, fsbw, hyxl, searchWD, searchType, lbs,
                     tb_start_time, tb_end_time, hb_start_time, hb_end_time, is_check, is_mark, bjfs, region_name,
 unit, cjnr);
                } else if (searchType == 8) {
                    uSql = uSql.replace("fj", "b.fj");
                    uSql = uSql.replace("pcs", "b.pcs");
                    uSql = uSql.replace("zrq", "b.zrq");
                    rets = GetJZQY(start_time, end_time, uSql, heads, fsbw, hyxl, searchWD, searchType, lbs,
 tb_start_time, tb_end_time, hb_start_time, hb_end_time, is_check, is_mark, bjfs, region_name,
                            cjnr);

                } else if (searchType == 9) //违法犯罪类警情
                {
                    String names[] = {"违法犯罪", "刑事", "行政（治安）"};
                    String idss[] = {"51-01000000,51-02000000", "51-01000000", "51-02000000"};
                    rets = GetWLQC(start_time, end_time, group, uSql, searchWD, heads, cby, "_zc", names, idss, unit,
                     tb_start_time, tb_end_time, hb_start_time, hb_end_time, is_check, is_mark, bjfs, cjnr,
                            searchLevel);
                }

                JSONObject datas = new JSONObject();
                datas.put("head", rets.getJSONArray("head"));
                datas.put("body", rets.getJSONArray("body"));
                back.put("data", datas);
                String fileId = "";
                if (isExp == 1) {
                    fileId = ExceptionFile(rets.getJSONArray("head"), rets.getJSONArray("body"));
                }
                back.put("file_id", fileId);
            } else {

                String sql = "";
                if (searchType <= 1 || searchType == 4 || searchType == 7 || searchType == 8 || searchType == 9) {

                    if (searchSub == 1)//刑事盗窃
                    {
                        cjlb = "51-01040300,51-01040310,51-01040310,51-01040395,51-01040320";


                    } else if (searchSub == 2)//治安盗窃
                    {
                        cjlb = "51-02040500,51-02040507,51-02040506,51-02040594,51-02040505,51-02040512";


                    } else if (searchSub == 3)//五类侵财
                    {

                        cjlb =
                                "51-02040600,51-02040700,51-02040500,51-02040300,51-01040400,51-01040500,51-01040300," + "51"
                                 + "-01040600,51-01040500,51-02040700,51-02040600,51-02040500,51-02040300," + "51-01040400," + "51" + "-01040300,51-01040600";


                    } else if (searchSub == 4)//诈骗类
                    {
                        cjlb =
                                "51-01040400,51-01040500,51-02040600,51-02040700,51-01040500,51-02040700,51-01040501," + "51"
                          + "-02040701,51-01040502,51-02040702,51-01040504,51-02040704,51-01040509," + "51-02040709," + "51" + "-01040505,51-02040705,51-01040508,51-02040708";

                    } else if (searchSub == 5) {
                        cjlb =
                                "51-02040600,51-02040700,51-02040500,51-02040300,51-01040400,51-01040500,51-01040300," + "51"
                          + "-01040600,51-01040300,51-02040500,51-01040400,51-01040500,51-02040600," + "51-02040700," + "51" + "-01040100,51-01040200,51-02040200,51-01040600," + "51-02040300";
                    } else if (searchType == 9) {
                        cjlb = "51-01000000,51-02000000";
                    }
                    lbs = cjlb.split(",");
                    String sq = "";
                    for (int i = 0; i < lbs.length; i++) {
                        String lb = lbs[i];
                        if (lb.endsWith("000000")) {
                            sq = sq + " type50='" + lb + "' or ";
                        } else if (lb.endsWith("0000")) {
                            sq = sq + " type52='" + lb + "' or ";
                        } else if (lb.endsWith("00")) {
                            sq = sq + " type54='" + lb + "' or ";
                        } else {
                            sq = sq + " CJLB='" + lb + "' or ";
                        }
                    }


                    String cjlbSql = " and (" + sq.substring(0, sq.length() - 3) + ") ";

                    sql =
                     "select JJBH,TYPE52,LAT,LNG,CJLB,CJSJ01 from HL.DSJ_JQ where cjsj01>='" + start_time + "'" + " " + "and " + "cjsj01<='" + end_time + "' " + uSql + cjlbSql;


                } else if (searchType == 2)//火灾
                {
                    String bqs[] = hzbq.split(",");
                    JSONArray dict53 = RIUtil.GetDictByType(53);
                    String ids = "";
                    for (int i = 0; i < bqs.length; i++) {
                        String bq = bqs[i];


                        for (int d = 0; d < dict53.size(); d++) {
                            JSONObject hone = dict53.getJSONObject(d);
                            String father_id = hone.getString("father_id");
                            String id = hone.getString("id");
                            if (father_id.equals(bq)) {
                                ids = ids + id + "','";
                            }


                        }
                    }
                    if (ids.endsWith(",'")) {
                        ids = ids.substring(0, ids.length() - 2);
                    }
                    if (hzbq.length() == 0) {

                        return ErrNo.set(505001);
                    } else {

                        sql = "select JJBH,TYPE52,LAT,LNG,CJLB,CJSJ01 from HL.DSJ_JQ where cjsj01>='" + start_time +
 "' " + "and" + " " + "cjsj01<='" + end_time + "' " + "" + uSql + " and BQ IN('" + ids + ")";
                    }
                } else if (searchType == 3)//发生部位
                {
                    String sq = "";
                    if (fsbw.contains("#")) {
                        String[] ids = fsbw.split("\\#");

                        fsbw = ids[0];
                        String lb = ids[1];
                        if (lb.endsWith("000000")) {
                            sq = sq + "  and type50='" + lb + "'  ";
                        } else if (lb.endsWith("0000")) {
                            sq = sq + "  and type52='" + lb + "'  ";
                        } else if (lb.endsWith("00")) {
                            sq = sq + "  and type54='" + lb + "'  ";
                        } else {
                            sq = sq + " and CJLB='" + lb + "'  ";
                        }

                    }
                    sql =
                    "select JJBH,TYPE52,LAT,LNG,CJLB,CJSJ01 from HL.DSJ_JQ where cjsj01>='" + start_time + "'" + " " + "and " + "cjsj01<='" + end_time + "' " + "" + uSql + " and TYPE='" + fsbw + "' " + sq;

                } else if (searchType == 5) {

                    String sq = "";
                    if (hyxl.contains("#")) {
                        String[] ids = fsbw.split("\\#");

                        hyxl = ids[0];
                        String lb = ids[1];
                        if (lb.endsWith("000000")) {
                            sq = sq + "  and type50='" + lb + "'  ";
                        } else if (lb.endsWith("0000")) {
                            sq = sq + "  and type52='" + lb + "'  ";
                        } else if (lb.endsWith("00")) {
                            sq = sq + "  and type54='" + lb + "'  ";
                        } else {
                            sq = sq + " and CJLB='" + lb + "'  ";
                        }

                    }
                    sql =
                    "select JJBH,TYPE52,LAT,LNG,CJLB,CJSJ01 from HL.DSJ_JQ where cjsj01>='" + start_time + "'" + " " + "and " + "cjsj01<='" + end_time + "' " + "" + uSql + " and TYPE='1' " + "and " + "hyxl " + "in" + "('" + hyxl.replace(",", "'," + "'") + "') " + sq;
                } else if (searchType == 6) {
                    String sq = "";
                    if (hyxl.contains("#")) {
                        String[] ids = fsbw.split("\\#");

                        hyxl = ids[0];
                        String lb = ids[1];
                        if (lb.endsWith("000000")) {
                            sq = sq + "  and type50='" + lb + "'  ";
                        } else if (lb.endsWith("0000")) {
                            sq = sq + "  and type52='" + lb + "'  ";
                        } else if (lb.endsWith("00")) {
                            sq = sq + "  and type54='" + lb + "'  ";
                        } else {
                            sq = sq + " and CJLB='" + lb + "'  ";
                        }

                    }
                    sql = "select JJBH,TYPE52,LAT,LNG,CJLB,CJSJ01 from HL.DSJ_JQ a left join address_dm where " +
                    "cjsj01>='" + start_time + "' and cjsj01<='" + end_time + "' " + "" + uSql + " " + "and " + "TYPE"
 + "='1' " + "and DMLX in('" + hyxl.replace(",", "','") + "') " + sq;
                }

                OracleHelper ora_hl = null;
                try {
                    ora_hl = new OracleHelper("ora_hl");
                    logger.warn(sql);
                    List<JSONObject> list = ora_hl.query(sql);
                    if (list.size() > 0) {
                        back.put("data", RelaInfo_null(list, range));
                        back.put("count", list.size());
                    } else {
                        back.put("data", new ArrayList<>());
                        back.put("count", 0);
                    }
                } catch (Exception ex) {
                    logger.error(Lib.getTrace(ex));
                } finally {
                    ora_hl.close();
                }
            }
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private static JSONObject GetJZQY1(String start, String end, String uSql, JSONArray heads, String fsbw,
     String hyxl, int searchWD, int searchType, String[] lbs, String tb_start_time, String tb_end_time,
String hb_start_time, String hb_end_time, int is_check, int is_mark, String bjfs, String region_name,
String unit, String cjnr) {
        String start_time = start;
        String end_time = end;
        JSONObject ret = new JSONObject();
        HashMap<String, JSONObject> results = new HashMap<>();


        for (int i = 0; i < lbs.length; i++) {
            String lb = lbs[i];
            String lbSql = uSql;
            if (lb.endsWith("000000")) {
                lbSql += lbSql + " and  TYPE50='" + lb + "' ";
            } else if (lb.endsWith("0000")) {
                lbSql += lbSql + " and  TYPE52='" + lb + "' ";
            } else if (lb.endsWith("00")) {
                lbSql += lbSql + " and  TYPE54='" + lb + "' ";
            } else {
                lbSql += lbSql + " and CJLB='" + lb + "'  ";
            }

            if (cjnr != null && cjnr.length() > 0) {
                lbSql += " and CLJG like '%" + cjnr + "%' ";
            }

            if (bjfs.length() > 0) {
                lbSql = lbSql + " and bjfs in (" + bjfs + ") ";
            }

            results = GetResults_zht(results, lbSql, start_time, end_time, lb, "", "_zht", is_check, is_mark,
                    region_name, bjfs, unit);
            JSONObject hone = new JSONObject();
            hone.put("key", lb + "_zht");
            hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "");
            heads.add(hone);
            // logger.warn(results);

            if (searchWD == 1)// 同环比
            {
                String start_time_t = tb_start_time + " 00:00:00";
                String end_time_t = tb_end_time + " 23:59:59";
                ;
                results = GetResults_zht(results, lbSql, start_time_t, end_time_t, lb, "_tb", "_zht", is_check,
                 is_mark, region_name, bjfs, unit);

                hone = new JSONObject();
                hone.put("key", lb + "_zht_tb");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同期");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_zht_tb_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同比(%)");
                heads.add(hone);
                String start_time_h = "";
                String end_time_h = "";
                try {
                    start_time_h = hb_start_time + " 00:00:00";
                    end_time_h = hb_end_time + " 23:59:59";
                } catch (Exception e) {
                    // TODO: handle exception
                }
                results = GetResults_zht(results, lbSql, start_time_h, end_time_h, lb, "_hb", "_zht", is_check,
                        is_mark, region_name, bjfs, unit);

                hone = new JSONObject();
                hone.put("key", lb + "_zht_hb");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_上期");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_zht_hb_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_环比(%)");
                heads.add(hone);
            } else if (searchWD == 2)//常量
            {
                String start_time_t = tb_start_time + " 00:00:00";
                String end_time_t = tb_end_time + " 23:59:59";

                String start_time_h = "";
                String end_time_h = "";
                try {

                    end_time_h = RIUtil.GetNextDate(start_time, -30);
                    end_time_h = RIUtil.getLMonthEnd(end_time_h);

                    start_time_h = RIUtil.GetNextDate(end_time_h, -40).substring(0, 7) + "-01 00:00:00";
                } catch (Exception e) {
                    // TODO: handle exception
                }
                lbSql = lbSql + " and ((cjsj01>='" + start_time_t + "' and cjsj01<='" + end_time_t + "') or " +
                        "(cjsj01>='" + start_time_h + "' and cjsj01<='" + end_time_h + "')) ";

                hone = new JSONObject();
                hone.put("key", lb + "_zht_cl_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_常量");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_zht_cl_xb_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_相比较(%)");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_zht_cl_zj_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_相差");
                heads.add(hone);

                results = GetResults_zht_cl(results, lbSql, lb, "_cl", "_zht", is_check, is_mark, region_name, bjfs,
                 start_time, end_time, unit);

            } else if (searchWD == 3) {
                String start_time_t = tb_start_time + " 00:00:00";
                String end_time_t = tb_end_time + " 23:59:59";
                ;
                results = GetResults_zht(results, lbSql, start_time_t, end_time_t, lb, "_tb", "_zht", is_check,
                 is_mark, region_name, bjfs, unit);

                hone = new JSONObject();
                hone.put("key", lb + "_zht_tb");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同期");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_zht_tb_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同比(%)");
                heads.add(hone);
            } else if (searchWD == 4) {
                String start_time_h = "";
                String end_time_h = "";
                try {
                    start_time_h = hb_start_time + " 00:00:00";
                    end_time_h = hb_end_time + " 23:59:59";
                } catch (Exception e) {
                    // TODO: handle exception
                }
                results = GetResults_zht(results, lbSql, start_time_h, end_time_h, lb, "_hb", "_zht", is_check,
                 is_mark, region_name, bjfs, unit);

                hone = new JSONObject();
                hone.put("key", lb + "_zht_hb");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_上期");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_zht_hb_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_环比(%)");
                heads.add(hone);
            }

        }
//        logger.warn(heads.toString());
//        logger.warn(results.toString());
        List<JSONObject> res = new ArrayList<>();
        String sql = "select id ,type2 from kh_zf_pcs_type";
        InfoModelHelper mysql = null;
        List<JSONObject> type2 = new ArrayList<>();
        try {
            mysql = InfoModelPool.getModel();
            type2 = mysql.query(sql);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(607005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        for (Map.Entry<String, JSONObject> dd : results.entrySet()) {
            String code = dd.getKey();
            try {
                JSONObject one = new JSONObject();
                String co[] = code.split(",");
                one.put("code", co[0]);
                one.put("name", co[1]);
                //派出所类别
                if (!type2.isEmpty()) {
                    List<JSONObject> id =
                     type2.stream().filter(jsonObject -> jsonObject.getString("id").equals(code)).collect(Collectors.toList());
                    if (!id.isEmpty()) {
                        one.put("type2", id.get(0).getString("type2").toUpperCase());
                    }
                }

                one.putAll(dd.getValue());
                if (!co[1].equals("")) {
                    res.add(one);
                }
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            }
        }


//        logger.warn(res.toString());
        ret.put("head", heads);
        ret.put("body", res);
        return ret;


    }

    private static JSONObject GetJZQY(String start, String end, String uSql, JSONArray heads, String fsbw,
     String hyxl, int searchWD, int searchType, String[] lbs, String tb_start_time, String tb_end_time,
      String hb_start_time, String hb_end_time, int is_check, int is_mark, String bjfs, String pysx, String cjnr) {
        String start_time = start;
        String end_time = end;
        JSONObject ret = new JSONObject();
        HashMap<String, JSONObject> results = new HashMap<>();


        for (int i = 0; i < lbs.length; i++) {
            String lb = lbs[i];
            String lbSql = uSql;
            if (lb.endsWith("000000")) {
                lbSql = lbSql + " and  TYPE50='" + lb + "' ";
            } else if (lb.endsWith("0000")) {
                lbSql = lbSql + " and  TYPE52='" + lb + "' ";
            } else if (lb.endsWith("00")) {
                lbSql = lbSql + " and  TYPE54='" + lb + "' ";
            } else {
                lbSql = lbSql + " and CJLB='" + lb + "'  ";
            }

            if (cjnr != null && cjnr.length() > 0) {
                lbSql += " and CLJG like '%" + cjnr + "%' ";
            }

            if (hyxl.contains(",")) {
                List<String> hyxlList = Arrays.asList(hyxl.split(","));
                lbSql = lbSql + " and DMLX in (" + String.join(",", hyxlList) + ") ";
            } else {
                lbSql = lbSql + " and DMLX='" + hyxl + "' ";
            }

            if (pysx.length() > 0) {
                lbSql = lbSql + " and (PYSX like '%" + pysx.toUpperCase() + "%' or DMMC like '%" + pysx + "%') ";
            }


            results = GetResults_jz(results, lbSql, start_time, end_time, lb, "", "_jzqy", bjfs, is_check, is_mark);
            JSONObject hone = new JSONObject();
            hone.put("key", lb + "_jzqy");
            if (!lb.equals("")) {
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "");
            } else {
                hone.put("value", "");
            }

            heads.add(hone);
            // logger.warn(results);

            if (searchWD == 1)// 同环比
            {
                String start_time_t = tb_start_time + " 00:00:00";
                String end_time_t = tb_end_time + " 23:59:59";
                ;
                results = GetResults_jz(results, lbSql, start_time_t, end_time_t, lb, "_tb", "_jzqy", bjfs, is_check,
 is_mark);

                hone = new JSONObject();
                hone.put("key", lb + "_jzqy_tb");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同期");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_jzqy_tb_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同比(%)");
                heads.add(hone);
                String start_time_h = "";
                String end_time_h = "";
                try {
                    start_time_h = hb_start_time + " 00:00:00";
                    end_time_h = hb_end_time + " 23:59:59";
                } catch (Exception e) {
                    // TODO: handle exception
                }
                results = GetResults_jz(results, lbSql, start_time_h, end_time_h, lb, "_hb", "_jzqy", bjfs, is_check,
 is_mark);

                hone = new JSONObject();
                hone.put("key", lb + "_jzqy_hb");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_上期");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_jzqy_hb_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_环比(%)");
                heads.add(hone);
            } else if (searchWD == 2)//常量
            {
                String start_time_t = tb_start_time + " 00:00:00";
                String end_time_t = tb_end_time + " 23:59:59";

                String start_time_h = "";
                String end_time_h = "";
                try {

                    end_time_h = RIUtil.GetNextDate(start_time, -30);
                    end_time_h = RIUtil.getLMonthEnd(end_time_h);

                    start_time_h = RIUtil.GetNextDate(end_time_h, -40).substring(0, 7) + "-01 00:00:00";
                } catch (Exception e) {
                    // TODO: handle exception
                }
                lbSql = lbSql + " and ((cjsj01>='" + start_time_t + "' and cjsj01<='" + end_time_t + "') or " +
                        "(cjsj01>='" + start_time_h + "' and cjsj01<='" + end_time_h + "')) ";

                hone = new JSONObject();
                hone.put("key", lb + "_jzqy_cl_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_常量");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_jzqy_cl_xb_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_相比较(%)");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_jzqy_cl_zj_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_相差");
                heads.add(hone);

                results = GetResults_jz_cl(results, lbSql, lb, "_cl", "_jzqy");

            } else if (searchWD == 3) {
                String start_time_t = tb_start_time + " 00:00:00";
                String end_time_t = tb_end_time + " 23:59:59";
                ;
                results = GetResults_jz(results, lbSql, start_time_t, end_time_t, lb, "_tb", "_jzqy", bjfs, is_check,
                 is_mark);

                hone = new JSONObject();
                hone.put("key", lb + "_jzqy_tb");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同期");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_jzqy_tb_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同比(%)");
                heads.add(hone);
            } else if (searchWD == 4) {
                String start_time_h = "";
                String end_time_h = "";
                try {
                    start_time_h = hb_start_time + " 00:00:00";
                    end_time_h = hb_end_time + " 23:59:59";
                } catch (Exception e) {
                    // TODO: handle exception
                }
                results = GetResults_jz(results, lbSql, start_time_h, end_time_h, lb, "_hb", "_jzqy", bjfs, is_check,
                        is_mark);

                hone = new JSONObject();
                hone.put("key", lb + "_jzqy_hb");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_上期");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_jzqy_hb_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_环比(%)");
                heads.add(hone);
            }

        }
        List<JSONObject> res = new ArrayList<>();
        String sql = "select id ,type2 from kh_zf_pcs_type";
        InfoModelHelper mysql = null;
        List<JSONObject> type2 = new ArrayList<>();
        try {
            mysql = InfoModelPool.getModel();
            type2 = mysql.query(sql);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(607005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        for (Map.Entry<String, JSONObject> dd : results.entrySet()) {
            String code = dd.getKey();
            try {
                JSONObject one = new JSONObject();
                String co[] = code.split(",");
                one.put("code", co[0]);
                one.put("name", co[1]);

                //派出所类别
                if (!type2.isEmpty()) {
                    List<JSONObject> id =
                    type2.stream().filter(jsonObject -> jsonObject.getString("id").equals(code)).collect(Collectors.toList());
                    if (!id.isEmpty()) {
                        one.put("type2", id.get(0).getString("type2").toUpperCase());
                    }
                }

                one.putAll(dd.getValue());
                res.add(one);
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            }
        }

        ret.put("head", heads);
        ret.put("body", res);
        return ret;


    }

    private static JSONObject GetWLQC(String start, String end, String group, String uSql, int searchWD,
JSONArray heads, String cby, String type, String[] names, String[] idss, String unit, String tb_start_time,
 String tb_end_time, String hb_start_time, String hb_end_time, int is_check, int is_mark, String bjfs,
 String cjnr, int searchLevel) {


        String start_time1 = start;
        String end_time1 = end;

        JSONObject ret = new JSONObject();
        HashMap<String, JSONObject> results = new HashMap<>();
        for (int i = 0; i < names.length; i++) {
            String bq = idss[i].replace(",", "|");
            String hname = names[i];
            String ids = idss[i];

            JSONObject hone = new JSONObject();
            hone.put("key", bq + type);

            hone.put("value", hname + "");
            heads.add(hone);
            if (searchWD == 1) {
                hone = new JSONObject();
                hone.put("key", bq + type + "_hb");
                hone.put("value", hname + "_上期");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + type + "_tb");
                hone.put("value", hname + "_同期");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + type + "_hb_p");
                hone.put("value", hname + "_环比（%）");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + type + "_tb_p");
                hone.put("value", hname + "_同比（%）");
                heads.add(hone);
            } else if (searchWD == 2) {
                hone = new JSONObject();
                hone.put("key", bq + type + "_cl_p");
                hone.put("value", hname + "_常量");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + type + "_cl_xb_p");
                hone.put("value", hname + "_相比较(%)");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + type + "_cl_zj_p");
                hone.put("value", hname + "_环增减");
                heads.add(hone);
            } else if (searchWD == 3) {
                hone = new JSONObject();
                hone.put("key", bq + type + "_tb");
                hone.put("value", hname + "_同期");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + type + "_tb_p");
                hone.put("value", hname + "_同比（%）");
                heads.add(hone);
            } else if (searchWD == 4) {
                hone = new JSONObject();
                hone.put("key", bq + type + "_hb");
                hone.put("value", hname + "_上期");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + type + "_hb_p");
                hone.put("value", hname + "_环比（%）");
                heads.add(hone);
            }

            String ds[] = ids.split(",");
            String sqlType = "";
            for (int a = 0; a < ds.length; a++) {
                String id = ds[a];
                if (id.endsWith("000000")) {
                    sqlType = sqlType + "TYPE50='" + id + "' or ";
                } else if (id.endsWith("0000")) {
                    sqlType = sqlType + "TYPE52='" + id + "' or ";
                } else if (id.endsWith("00")) {
                    sqlType = sqlType + "TYPE54='" + id + "' or ";
                } else {
                    sqlType = sqlType + "CJLB='" + id + "' or ";
                }

            }
            if (sqlType.endsWith("or ")) {
                sqlType = sqlType.substring(0, sqlType.length() - 3);
            }

            start_time1 = start;
            end_time1 = end;
            String uq = uSql + " AND  (" + sqlType + ")";
            if (cjnr != null && cjnr.length() > 0) {
                uq += " and CLJG like '%" + cjnr + "%' ";
            }
            //   logger.warn(uq);
            //  results=GetHzRess(cby,group,uSql,ids,results,"",bq);
            results = GetResults(results, group, uq, start_time1, end_time1, bq, "", cby, type + "", unit, is_check,
 is_mark, bjfs);
            if (searchWD == 1) {
                try {
                    start_time1 = hb_start_time + " 00:00:00";
                    end_time1 = hb_end_time + " 23:59:59";

                } catch (Exception ex) {
                }

                results = GetResults(results, group, uq, start_time1, end_time1, bq, "_hb", cby, type + "", unit,
                        is_check, is_mark, bjfs);

                start_time1 = tb_start_time + " 00:00:00";
                end_time1 = tb_end_time + " 23:59:59";

                //results=GetHzRess(cby,group,uSql,ids,results,"_tb",bq);
                results = GetResults(results, group, uq, start_time1, end_time1, bq, "_tb", cby, type + "", unit,
                        is_check, is_mark, bjfs);
            } else if (searchWD == 2) {
                String start_time_t = tb_start_time + " 00:00:00";
                String end_time_t = tb_end_time + " 23:59:59";

                String start_time_h = "";
                String end_time_h = "";
                try {

                    end_time_h = RIUtil.GetNextDate(start_time1, -30);
                    end_time_h = RIUtil.getLMonthEnd(end_time_h);

                    start_time_h = RIUtil.GetNextDate(end_time_h, -40).substring(0, 7) + "-01 00:00:00";
                } catch (Exception e) {
                    // TODO: handle exception
                }
                uq =
 uq + " and ((cjsj01>='" + start_time_t + "' and cjsj01<='" + end_time_t + "') or " + "(cjsj01" +
                 ">='" + start_time_h + "' and cjsj01<='" + end_time_h + "')) ";

                results = GetResults_cl(results, group, uq, bq, "_cl", cby, "_zc", unit, is_check, is_mark);
            } else if (searchWD == 3) {
                start_time1 = tb_start_time + " 00:00:00";
                end_time1 = tb_end_time + " 23:59:59";

                //results=GetHzRess(cby,group,uSql,ids,results,"_tb",bq);
                results = GetResults(results, group, uq, start_time1, end_time1, bq, "_tb", cby, type + "", unit,
                        is_check, is_mark, bjfs);
            } else if (searchWD == 4) {
                try {
                    start_time1 = hb_start_time + " 00:00:00";
                    end_time1 = hb_end_time + " 23:59:59";

                } catch (Exception ex) {
                }

                results = GetResults(results, group, uq, start_time1, end_time1, bq, "_hb", cby, type + "", unit,
 is_check, is_mark, bjfs);
            }
        }
        //  logger.warn(heads.toString());
        //  logger.warn(results.toString());
        String sql = "select id ,type2 from kh_zf_pcs_type";
        InfoModelHelper mysql = null;
        List<JSONObject> type2 = new ArrayList<>();
        try {
            mysql = InfoModelPool.getModel();
            type2 = mysql.query(sql);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(607005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        List<JSONObject> res = new ArrayList<>();
        for (Map.Entry<String, JSONObject> dd : results.entrySet()) {
            String code = dd.getKey();
            JSONObject one = new JSONObject();
            try {
                one.put("code", code);
                String name = RIUtil.dicts.get(code).getString("remark");
                //派出所类别
                if (!type2.isEmpty()) {
                    List<JSONObject> id =
 type2.stream().filter(jsonObject -> jsonObject.getString("id").equals(code)).collect(Collectors.toList());
                    if (!id.isEmpty()) {
                        one.put("type2", id.get(0).getString("type2").toUpperCase());
                    }
                }

                one.put("name", name);
                one.put("index_no", RIUtil.dicts.get(code).getInteger("index_no"));
                one.putAll(dd.getValue());
                if (!name.equals("")) {
                    res.add(one);
                }


            } catch (Exception ex) {

            }
        }

        Collections.sort(res, (JSONObject o1, JSONObject o2) -> {
            //转成JSON对象中保存的值类型
            String a = "";
            String b = "";

            try {
                a = o1.getString("code");
                b = o2.getString("code");
            } catch (Exception ex) {

            }

            int result = a.compareTo(b);
            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (result > 0) {  //降序排列，升序改成a>b
                return 1;
            } else if (a == b) {
                return 0;
            } else {
                return -1;
            }
        });
        //  logger.warn(res.toString());

        ret.put("head", heads);
        ret.put("body", res);
        return ret;


    }

    private static JSONObject GetZYBW(String start_time, String end_time, String group, String uSql, JSONArray heads,
                                      String fsbw, String hyxl, String cby, int searchWD, int searchType, String[] lbs, String unit,
String tb_start_time, String tb_end_time, String hb_start_time, String hb_end_time, int is_check, int is_mark,
       String bjfs, String cjnr) {
        OracleHelper ora_hl = null;

        HashMap<String, JSONObject> results = new HashMap<>();

        if (searchType == 5)//单位行业细类
        {
            String[] xls = hyxl.split(",");
            for (int i = 0; i < xls.length; i++) {
                String xl = xls[i];
                JSONObject hone = new JSONObject();
                hone.put("key", xl + "_hyxl");
                hone.put("value", RIUtil.dicts.get(xl).getString("dict_name") + "");
                heads.add(hone);

                String lbSql = uSql + " and type=1 and hyxl='" + xl + "' ";

                if (cjnr != null && cjnr.length() > 0) {
                    lbSql += " and CLJG like '%" + cjnr + "%' ";
                }

                results = GetResults(results, group, lbSql, start_time, end_time, xl, "", cby, "_hyxl", unit,
                is_check, is_mark, bjfs);

                if (searchWD == 1) {
                    String start_time_t = tb_start_time + " 00:00:00";
                    String end_time_t = tb_end_time + " 23:59:59";
                    results = GetResults(results, group, lbSql, start_time_t, end_time_t, xl, "_tb", cby, "_hyxl",
                     unit, is_check, is_mark, bjfs);

                    hone = new JSONObject();
                    hone.put("key", xl + "_hyxl_tb");
                    hone.put("value", RIUtil.dicts.get(xl).getString("dict_name") + "_同期");
                    heads.add(hone);

                    hone = new JSONObject();
                    hone.put("key", xl + "_hyxl_tb_p");
                    hone.put("value", RIUtil.dicts.get(xl).getString("dict_name") + "_同比(%)");
                    heads.add(hone);
                    String start_time_h = "";
                    String end_time_h = "";
                    try {
                        start_time_h = hb_start_time + " 00:00:00";
                        end_time_h = hb_end_time + " 23:59:59";
                    } catch (Exception e) {
                        // TODO: handle exception
                    }
                    results = GetResults(results, group, lbSql, start_time_h, end_time_h, xl, "_hb", cby, "_hyxl",
                    unit, is_check, is_mark, bjfs);

                    hone = new JSONObject();
                    hone.put("key", xl + "_hyxl_hb");
                    hone.put("value", RIUtil.dicts.get(xl).getString("dict_name") + "_上期");
                    heads.add(hone);

                    hone = new JSONObject();
                    hone.put("key", xl + "_hyxl_hb_p");
                    hone.put("value", RIUtil.dicts.get(xl).getString("dict_name") + "_环比(%)");
                    heads.add(hone);

                } else if (searchWD == 2) {
                    String start_time_t = tb_start_time + " 00:00:00";
                    String end_time_t = tb_end_time + " 23:59:59";

                    String start_time_h = "";
                    String end_time_h = "";
                    try {

                        end_time_h = RIUtil.GetNextDate(start_time, -30);
                        end_time_h = RIUtil.getLMonthEnd(end_time_h);

                        start_time_h = RIUtil.GetNextDate(end_time_h, -40).substring(0, 7) + "-01 00:00:00";
                    } catch (Exception e) {
                        // TODO: handle exception
                    }
                    lbSql = lbSql + " and ((cjsj01>='" + start_time_t + "' and cjsj01<='" + end_time_t + "') or " +
                    "(cjsj01>='" + start_time_h + "' and cjsj01<='" + end_time_h + "')) ";

                    hone = new JSONObject();
                    hone.put("key", xl + "_hyxl_cl_p");
                    hone.put("value", RIUtil.dicts.get(xl).getString("dict_name") + "_常量");
                    heads.add(hone);

                    hone = new JSONObject();
                    hone.put("key", xl + "_hyxl_cl_xb_p");
                    hone.put("value", RIUtil.dicts.get(xl).getString("dict_name") + "_相比较(%)");
                    heads.add(hone);

                    hone = new JSONObject();
                    hone.put("key", xl + "_hyxl_cl_zj_p");
                    hone.put("value", RIUtil.dicts.get(xl).getString("dict_name") + "_相差");
                    heads.add(hone);

                    results = GetResults_cl(results, group, lbSql, xl, "_cl", "_hyxl", cby, unit, is_check, is_mark);


                } else if (searchWD == 3) {
                    String start_time_t = tb_start_time + " 00:00:00";
                    String end_time_t = tb_end_time + " 23:59:59";
                    results = GetResults(results, group, lbSql, start_time_t, end_time_t, xl, "_tb", cby, "_hyxl",
                     unit, is_check, is_mark, bjfs);

                    hone = new JSONObject();
                    hone.put("key", xl + "_hyxl_tb");
                    hone.put("value", RIUtil.dicts.get(xl).getString("dict_name") + "_同期");
                    heads.add(hone);

                    hone = new JSONObject();
                    hone.put("key", xl + "_hyxl_tb_p");
                    hone.put("value", RIUtil.dicts.get(xl).getString("dict_name") + "_同比(%)");
                    heads.add(hone);
                } else if (searchWD == 4) {
                    String start_time_h = "";
                    String end_time_h = "";
                    try {
                        start_time_h = hb_start_time + " 00:00:00";
                        end_time_h = hb_end_time + " 23:59:59";
                    } catch (Exception e) {
                        // TODO: handle exception
                    }
                    results = GetResults(results, group, lbSql, start_time_h, end_time_h, xl, "_hb", cby, "_hyxl",
                     unit, is_check, is_mark, bjfs);

                    hone = new JSONObject();
                    hone.put("key", xl + "_hyxl_hb");
                    hone.put("value", RIUtil.dicts.get(xl).getString("dict_name") + "_上期");
                    heads.add(hone);

                    hone = new JSONObject();
                    hone.put("key", xl + "_hyxl_hb_p");
                    hone.put("value", RIUtil.dicts.get(xl).getString("dict_name") + "_环比(%)");
                    heads.add(hone);
                }


                if (lbs != null && lbs.length > 0) {
                    for (int l = 0; l < lbs.length; l++) {
                        String lb = lbs[l];
                        if (lb.length() > 2) {
                            hone = new JSONObject();
                            hone.put("key", xl + "#" + lb + "_hyxl");
                            hone.put("value",
                             RIUtil.dicts.get(xl).getString("dict_name") + "_" + RIUtil.dicts.get(lb).getString(
                                     "dict_name") + "");
                            heads.add(hone);


                            lbSql = uSql + " and type=1 and hyxl='" + xl + "' ";

                            if (lb.endsWith("000000")) {
                                lbSql = lbSql + " and type50='" + lb + "'";
                            } else if (lb.endsWith("0000")) {
                                lbSql = lbSql + " and type52='" + lb + "'";
                            } else if (lb.endsWith("00")) {
                                lbSql = lbSql + " and type54='" + lb + "'";
                            } else {
                                lbSql = lbSql + " and cjlb='" + lb + "'";
                            }


                            results = GetResults(results, group, lbSql, start_time, end_time, xl + "#" + lb, "", cby,
                                    "_hyxl", unit, is_check, is_mark, bjfs);

                            if (searchWD == 1) {
                                String start_time_t = tb_start_time + " 00:00:00";
                                String end_time_t = tb_end_time + " 23:59:59";
                                results = GetResults(results, group, lbSql, start_time_t, end_time_t, xl + "#" + lb,
 "_tb", cby, "_hyxl", unit, is_check, is_mark, bjfs);

                                hone = new JSONObject();
                                hone.put("key", xl + "#" + lb + "_hyxl_tb");
                                hone.put("value",
                                         RIUtil.dicts.get(xl).getString("dict_name") + "_" + RIUtil.dicts.get(lb).getString(
                                         "dict_name") + "_同期");
                                heads.add(hone);

                                hone = new JSONObject();
                                hone.put("key", xl + "#" + lb + "_hyxl_tb_p");
                                hone.put("value",
                                         RIUtil.dicts.get(xl).getString("dict_name") + "_" + RIUtil.dicts.get(lb).getString(
                                         "dict_name") + "_同比(%)");
                                heads.add(hone);
                                String start_time_h = "";
                                String end_time_h = "";
                                try {
                                    start_time_h = hb_start_time + " 00:00:00";
                                    end_time_h = hb_end_time + " 23:59:59";
                                } catch (Exception e) {
                                    // TODO: handle exception
                                }
                                results = GetResults(results, group, lbSql, start_time_h, end_time_h, xl + "#" + lb,
                                "_hb", cby, "_hyxl", unit, is_check, is_mark, bjfs);

                                hone = new JSONObject();
                                hone.put("key", xl + "#" + lb + "_hyxl_hb");
                                hone.put("value",
                                 RIUtil.dicts.get(xl).getString("dict_name") + "_" + RIUtil.dicts.get(lb).getString(
                                         "dict_name") + "_上期");
                                heads.add(hone);

                                hone = new JSONObject();
                                hone.put("key", xl + "#" + lb + "_hyxl_hb_p");
                                hone.put("value",
                                         RIUtil.dicts.get(xl).getString("dict_name") + "_" + RIUtil.dicts.get(lb).getString(
                                         "dict_name") + "_环比(%)");
                                heads.add(hone);

                            } else if (searchWD == 2) {
                                String start_time_t = tb_start_time + " 00:00:00";
                                String end_time_t = tb_end_time + " 23:59:59";

                                String start_time_h = "";
                                String end_time_h = "";
                                try {

                                    end_time_h = RIUtil.GetNextDate(start_time, -30);
                                    end_time_h = RIUtil.getLMonthEnd(end_time_h);

                                    start_time_h = RIUtil.GetNextDate(end_time_h, -40).substring(0, 7) + "-01 00:00:00";
                                } catch (Exception e) {
                                    // TODO: handle exception
                                }
                                lbSql = lbSql + " and ((cjsj01>='" + start_time_t + "' and cjsj01<='" + end_time_t +
 "') or " + "(cjsj01>='" + start_time_h + "' and cjsj01<='" + end_time_h + "')" + ") ";

                                hone = new JSONObject();
                                hone.put("key", xl + "#" + lb + "_hyxl_cl_p");
                                hone.put("value",
                                 RIUtil.dicts.get(xl).getString("dict_name") + "_" + RIUtil.dicts.get(lb).getString(
                                         "dict_name") + "_常量");
                                heads.add(hone);

                                hone = new JSONObject();
                                hone.put("key", xl + "#" + lb + "_hyxl_cl_xb_p");
                                hone.put("value",
                                 RIUtil.dicts.get(xl).getString("dict_name") + "_" + RIUtil.dicts.get(lb).getString(
                                         "dict_name") + "_相比较(%)");
                                heads.add(hone);

                                hone = new JSONObject();
                                hone.put("key", xl + "#" + lb + "_hyxl_cl_zj_p");
                                hone.put("value",
                                         RIUtil.dicts.get(xl).getString("dict_name") + "_" + RIUtil.dicts.get(lb).getString(
                                         "dict_name") + "_相差");
                                heads.add(hone);

                                results = GetResults_cl(results, group, lbSql, xl + "#" + lb, "_cl", cby, "_hyxl",
 unit, is_check, is_mark);

                            } else if (searchWD == 3) {
                                String start_time_t = tb_start_time + " 00:00:00";
                                String end_time_t = tb_end_time + " 23:59:59";
                                results = GetResults(results, group, lbSql, start_time_t, end_time_t, xl + "#" + lb,
                                "_tb", cby, "_hyxl", unit, is_check, is_mark, bjfs);

                                hone = new JSONObject();
                                hone.put("key", xl + "#" + lb + "_hyxl_tb");
                                hone.put("value",
                                 RIUtil.dicts.get(xl).getString("dict_name") + "_" + RIUtil.dicts.get(lb).getString(
                                         "dict_name") + "_同期");
                                heads.add(hone);

                                hone = new JSONObject();
                                hone.put("key", xl + "#" + lb + "_hyxl_tb_p");
                                hone.put("value",
                                 RIUtil.dicts.get(xl).getString("dict_name") + "_" + RIUtil.dicts.get(lb).getString(
                                         "dict_name") + "_同比(%)");
                                heads.add(hone);
                            } else if (searchWD == 4) {
                                String start_time_h = "";
                                String end_time_h = "";
                                try {
                                    start_time_h = hb_start_time + " 00:00:00";
                                    end_time_h = hb_end_time + " 23:59:59";
                                } catch (Exception e) {
                                    // TODO: handle exception
                                }
                                results = GetResults(results, group, lbSql, start_time_h, end_time_h, xl + "#" + lb,
"_hb", cby, "_hyxl", unit, is_check, is_mark, bjfs);

                                hone = new JSONObject();
                                hone.put("key", xl + "#" + lb + "_hyxl_hb");
                                hone.put("value",
RIUtil.dicts.get(xl).getString("dict_name") + "_" + RIUtil.dicts.get(lb).getString(
                                         "dict_name") + "_上期");
                                heads.add(hone);

                                hone = new JSONObject();
                                hone.put("key", xl + "#" + lb + "_hyxl_hb_p");
                                hone.put("value",
                                 RIUtil.dicts.get(xl).getString("dict_name") + "_" + RIUtil.dicts.get(lb).getString(
                                         "dict_name") + "_环比(%)");
                                heads.add(hone);
                            }
                        }

                    }
                }

            }
        } else {//发生部位

            String[] bws = fsbw.split(",");
            for (int i = 0; i < bws.length; i++) {
                String bw = bws[i];
                JSONObject hone = new JSONObject();
                hone.put("key", bw + "_fsbw");
                hone.put("value", GetFSBW(bw));
                heads.add(hone);
                String lbSql = uSql + " and type='" + bw + "' ";

                results = GetResults(results, group, lbSql, start_time, end_time, bw, "", cby, "_fsbw", unit,
                    is_check, is_mark, bjfs);

                if (searchWD == 1) {
                    String start_time_t = tb_start_time + " 00:00:00";
                    String end_time_t = tb_end_time + "23:59:59";
                    results = GetResults(results, group, lbSql, start_time_t, end_time_t, bw, "_tb", cby, "_fsbw",
                    unit, is_check, is_mark, bjfs);

                    hone = new JSONObject();
                    hone.put("key", bw + "_fsbw_tb");
                    hone.put("value", GetFSBW(bw) + "_同期");
                    heads.add(hone);

                    hone = new JSONObject();
                    hone.put("key", bw + "_fsbw_tb_p");
                    hone.put("value", GetFSBW(bw) + "_同比(%)");
                    heads.add(hone);
                    String start_time_h = "";
                    String end_time_h = "";
                    try {
                        start_time_h = hb_start_time + " 00:00:00";
                        end_time_h = hb_end_time + " 23:59:59";
                    } catch (Exception e) {
                        // TODO: handle exception
                    }
                    results = GetResults(results, group, lbSql, start_time_h, end_time_h, bw, "_hb", cby, "_fsbw",
                    unit, is_check, is_mark, bjfs);

                    hone = new JSONObject();
                    hone.put("key", bw + "_fsbw_hb");
                    hone.put("value", GetFSBW(bw) + "_上期");
                    heads.add(hone);

                    hone = new JSONObject();
                    hone.put("key", bw + "_fsbw_hb_p");
                    hone.put("value", GetFSBW(bw) + "_环比(%)");
                    heads.add(hone);

                } else if (searchWD == 2) {
                    String start_time_t = tb_start_time + " 00:00:00";
                    String end_time_t = tb_end_time + " 23:59:59";

                    String start_time_h = "";
                    String end_time_h = "";
                    try {

                        end_time_h = RIUtil.GetNextDate(start_time, -30);
                        end_time_h = RIUtil.getLMonthEnd(end_time_h);

                        start_time_h = RIUtil.GetNextDate(end_time_h, -40).substring(0, 7) + "-01 00:00:00";
                    } catch (Exception e) {
                        // TODO: handle exception
                    }
                    lbSql = lbSql + " and ((cjsj01>='" + start_time_t + "' and cjsj01<='" + end_time_t + "') or " +
                    "(cjsj01>='" + start_time_h + "' and cjsj01<='" + end_time_h + "')) ";

                    hone = new JSONObject();
                    hone.put("key", bw + "_fsbw_cl_p");
                    hone.put("value", GetFSBW(bw) + "_常量");
                    heads.add(hone);

                    hone = new JSONObject();
                    hone.put("key", bw + "_fsbw_cl_xb_p");
                    hone.put("value", GetFSBW(bw) + "_相比较(%)");
                    heads.add(hone);

                    hone = new JSONObject();
                    hone.put("key", bw + "_fsbw_cl_zj_p");
                    hone.put("value", GetFSBW(bw) + "_相差");
                    heads.add(hone);

                    results = GetResults_cl(results, group, lbSql, bw, "_cl", cby, "_fsbw", unit, is_check, is_mark);

                } else if (searchWD == 3) {
                    String start_time_t = tb_start_time + " 00:00:00";
                    String end_time_t = tb_end_time + "23:59:59";
                    results = GetResults(results, group, lbSql, start_time_t, end_time_t, bw, "_tb", cby, "_fsbw",
                    unit, is_check, is_mark, bjfs);

                    hone = new JSONObject();
                    hone.put("key", bw + "_fsbw_tb");
                    hone.put("value", GetFSBW(bw) + "_同期");
                    heads.add(hone);

                    hone = new JSONObject();
                    hone.put("key", bw + "_fsbw_tb_p");
                    hone.put("value", GetFSBW(bw) + "_同比(%)");
                    heads.add(hone);
                } else if (searchWD == 4) {
                    String start_time_h = "";
                    String end_time_h = "";
                    try {
                        start_time_h = hb_start_time + " 00:00:00";
                        end_time_h = hb_end_time + " 23:59:59";
                    } catch (Exception e) {
                        // TODO: handle exception
                    }
                    results = GetResults(results, group, lbSql, start_time_h, end_time_h, bw, "_hb", cby, "_fsbw",
                    unit, is_check, is_mark, bjfs);

                    hone = new JSONObject();
                    hone.put("key", bw + "_fsbw_hb");
                    hone.put("value", GetFSBW(bw) + "_上期");
                    heads.add(hone);

                    hone = new JSONObject();
                    hone.put("key", bw + "_fsbw_hb_p");
                    hone.put("value", GetFSBW(bw) + "_环比(%)");
                    heads.add(hone);
                }


                if (lbs != null && lbs.length > 0) {
                    for (int l = 0; l < lbs.length; l++) {
                        String lb = lbs[l];
                        if (lb.length() > 2) {
                            hone = new JSONObject();
                            hone.put("key", bw + "#" + lb + "_fsbw");
                            hone.put("value", GetFSBW(bw) + "_" + RIUtil.dicts.get(lb).getString("dict_name") + "");
                            heads.add(hone);


                            lbSql = uSql + " and type=" + bw + "  ";

                            if (lb.endsWith("000000")) {
                                lbSql = lbSql + " and type50='" + lb + "'";
                            } else if (lb.endsWith("0000")) {
                                lbSql = lbSql + " and type52='" + lb + "'";
                            } else if (lb.endsWith("00")) {
                                lbSql = lbSql + " and type54='" + lb + "'";
                            } else {
                                lbSql = lbSql + " and cjlb='" + lb + "'";
                            }


                            results = GetResults(results, group, lbSql, start_time, end_time, bw + "#" + lb, "", cby,
                             "_fsbw", unit, is_check, is_mark, bjfs);

                            if (searchWD == 1) {
                                String start_time_t = tb_start_time + " 00:00:00";
                                String end_time_t = tb_end_time + " 23:59:59";
                                results = GetResults(results, group, lbSql, start_time_t, end_time_t, bw + "#" + lb,
                                "_tb", cby, "_fsbw", unit, is_check, is_mark, bjfs);

                                hone = new JSONObject();
                                hone.put("key", bw + "#" + lb + "_fsbw_tb");
                                hone.put("value", GetFSBW(bw) + "_" + RIUtil.dicts.get(lb).getString("dict_name") +
                                "_同期");
                                heads.add(hone);

                                hone = new JSONObject();
                                hone.put("key", bw + "#" + lb + "_fsbw_tb_p");
                                hone.put("value", GetFSBW(bw) + "_" + RIUtil.dicts.get(lb).getString("dict_name") +
                                "_同比(%)");
                                heads.add(hone);
                                String start_time_h = "";
                                String end_time_h = "";
                                try {
                                    start_time_h = hb_start_time + " 00:00:00";
                                    end_time_h = hb_end_time + " 23:59:59";
                                } catch (Exception e) {
                                    // TODO: handle exception
                                }
                                results = GetResults(results, group, lbSql, start_time_h, end_time_h, bw + "#" + lb,
                                "_hb", cby, "_fsbw", unit, is_check, is_mark, bjfs);

                                hone = new JSONObject();
                                hone.put("key", bw + "#" + lb + "_fsbw_hb");
                                hone.put("value", GetFSBW(bw) + "_" + RIUtil.dicts.get(lb).getString("dict_name") +
                                "_上期");
                                heads.add(hone);

                                hone = new JSONObject();
                                hone.put("key", bw + "#" + lb + "_fsbw_hb_p");
                                hone.put("value", GetFSBW(bw) + "_" + RIUtil.dicts.get(lb).getString("dict_name") +
"_环比(%)");
                                heads.add(hone);

                            } else if (searchWD == 2) {
                                String start_time_t = tb_start_time + " 00:00:00";
                                String end_time_t = tb_end_time + " 23:59:59";

                                String start_time_h = "";
                                String end_time_h = "";
                                try {

                                    end_time_h = RIUtil.GetNextDate(start_time, -30);
                                    end_time_h = RIUtil.getLMonthEnd(end_time_h);

                                    start_time_h = RIUtil.GetNextDate(end_time_h, -40).substring(0, 7) + "-01 00:00:00";
                                } catch (Exception e) {
                                    // TODO: handle exception
                                }
                                lbSql = lbSql + " and ((cjsj01>='" + start_time_t + "' and cjsj01<='" + end_time_t +
"') or " + "(cjsj01>='" + start_time_h + "' and cjsj01<='" + end_time_h + "')" + ") ";

                                hone = new JSONObject();
                                hone.put("key", bw + "#" + lb + "_fsbw_cl_p");
                                hone.put("value", GetFSBW(bw) + "_" + RIUtil.dicts.get(lb).getString("dict_name") +
                                "_常量");
                                heads.add(hone);

                                hone = new JSONObject();
                                hone.put("key", bw + "#" + lb + "_fsbw_cl_xb_p");
                                hone.put("value", GetFSBW(bw) + "_" + RIUtil.dicts.get(lb).getString("dict_name") +
                                "_相比较(%)");
                                heads.add(hone);

                                hone = new JSONObject();
                                hone.put("key", bw + "#" + lb + "_fsbw_cl_zj_p");
                                hone.put("value", GetFSBW(bw) + "_" + RIUtil.dicts.get(lb).getString("dict_name") +
                                "_相差");
                                heads.add(hone);

                                results = GetResults_cl(results, group, lbSql, bw + "#" + lb, "_cl", cby, "_fsbw",
                                 unit, is_check, is_mark);

                            } else if (searchWD == 3) {
                                String start_time_t = tb_start_time + " 00:00:00";
                                String end_time_t = tb_end_time + " 23:59:59";
                                results = GetResults(results, group, lbSql, start_time_t, end_time_t, bw + "#" + lb,
                                "_tb", cby, "_fsbw", unit, is_check, is_mark, bjfs);

                                hone = new JSONObject();
                                hone.put("key", bw + "#" + lb + "_fsbw_tb");
                                hone.put("value", GetFSBW(bw) + "_" + RIUtil.dicts.get(lb).getString("dict_name") +
                                "_同期");
                                heads.add(hone);

                                hone = new JSONObject();
                                hone.put("key", bw + "#" + lb + "_fsbw_tb_p");
                                hone.put("value", GetFSBW(bw) + "_" + RIUtil.dicts.get(lb).getString("dict_name") +
                                "_同比(%)");
                                heads.add(hone);
                            } else if (searchWD == 4) {
                                String start_time_h = "";
                                String end_time_h = "";
                                try {
                                    start_time_h = hb_start_time + " 00:00:00";
                                    end_time_h = hb_end_time + " 23:59:59";
                                } catch (Exception e) {
                                    // TODO: handle exception
                                }
                                results = GetResults(results, group, lbSql, start_time_h, end_time_h, bw + "#" + lb,
                                "_hb", cby, "_fsbw", unit, is_check, is_mark, bjfs);

                                hone = new JSONObject();
                                hone.put("key", bw + "#" + lb + "_fsbw_hb");
                                hone.put("value", GetFSBW(bw) + "_" + RIUtil.dicts.get(lb).getString("dict_name") +
                                "_上期");
                                heads.add(hone);

                                hone = new JSONObject();
                                hone.put("key", bw + "#" + lb + "_fsbw_hb_p");
                                hone.put("value", GetFSBW(bw) + "_" + RIUtil.dicts.get(lb).getString("dict_name") +
        "_环比(%)");
                                heads.add(hone);
                            }
                        }

                    }
                }


            }
        }

        List<JSONObject> res = new ArrayList<>();
        String sql = "select id ,type2 from kh_zf_pcs_type";
        InfoModelHelper mysql = null;
        List<JSONObject> type2 = new ArrayList<>();
        try {
            mysql = InfoModelPool.getModel();
            type2 = mysql.query(sql);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(607005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        for (Map.Entry<String, JSONObject> dd : results.entrySet()) {
            String code = dd.getKey();
            JSONObject one = new JSONObject();
            try {
                one.put("code", code);
                String name = RIUtil.dicts.get(code).getString("remark");
                one.put("name", name);
                one.put("index_no", RIUtil.dicts.get(code).getInteger("index_no"));
                one.putAll(dd.getValue());
                //派出所类别
                if (!type2.isEmpty()) {
                    List<JSONObject> id =
                     type2.stream().filter(jsonObject -> jsonObject.getString("id").equals(code)).collect(Collectors.toList());
                    if (!id.isEmpty()) {
                        one.put("type2", id.get(0).getString("type2").toUpperCase());
                    }
                }
                if (!name.equals("")) {
                    res.add(one);
                }

            } catch (Exception ex) {

            }
        }

        Collections.sort(res, (JSONObject o1, JSONObject o2) -> {
            //转成JSON对象中保存的值类型
            String a = "";
            String b = "";

            try {
                a = o1.getString("code");
                b = o2.getString("code");
            } catch (Exception ex) {

            }

            int result = a.compareTo(b);
            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (result > 0) {  //降序排列，升序改成a>b
                return 1;
            } else if (a == b) {
                return 0;
            } else {
                return -1;
            }
        });
        JSONObject ret = new JSONObject();

        ret.put("head", heads);
        ret.put("body", res);
        return ret;


    }

    private static String GetFSBW(String bw) {
        String back = "";
        if (bw.equals("2")) {
            back = "房屋";
        } else if (bw.equals("1")) {
            back = "单位";
        } else if (bw.equals("3")) {
            back = "建筑工地";
        } else if (bw.equals("4")) {
            back = "桥梁（重要）";
        } else if (bw.equals("5")) {
            back = "船舶";
        } else if (bw.equals("6")) {
            back = "车库";
        } else if (bw.equals("7")) {
            back = "值班室";
        } else if (bw.equals("8")) {
            back = "厂房（仓库）";
        } else if (bw.equals("9")) {
            back = "集装箱板房";
        } else if (bw.equals("10")) {
            back = "涵洞";
        } else if (bw.equals("11")) {
            back = "码头";
        } else if (bw.equals("12")) {
            back = "共享单车停放点";
        } else if (bw.equals("13")) {
            back = "公交（地铁）站台";
        } else if (bw.equals("14")) {
            back = "道路";
        } else if (bw.equals("15")) {
            back = "河流";
        } else if (bw.equals("16")) {
            back = "广场";
        } else if (bw.equals("17")) {
            back = "湖泊";
        } else if (bw.equals("18")) {
            back = "地面停车场";
        } else if (bw.equals("19")) {
            back = "楼房";
        } else if (bw.equals("20")) {
            back = "门面房";
        } else if (bw.equals("21")) {
            back = "平房（简易房）";
        } else if (bw.equals("22")) {
            back = "工地工棚";
        } else if (bw.equals("23")) {
            back = "楼顶建筑";
        } else if (bw.equals("24")) {
            back = "固定住家船";
        } else if (bw.equals("25")) {
            back = "桥梁（非标）";
        } else if (bw.equals("26")) {
            back = "旅游景区";
        } else if (bw.equals("27")) {
            back = "高速服务区";
        } else {
            back = "其他";
        }
        return back;
    }

    private static List<JSONObject> RelaInfo_null(List<JSONObject> list, String range) {

        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            //   logger.warn(one.toString());
            String lat = one.getString("LAT");
            String lng = one.getString("LNG");
            String type = one.getString("TYPE52");
            String cjlb = one.getString("CJLB");

            String name = "其";
            try {
                name = RIUtil.dicts.get(type).getString("gadm");
            } catch (Exception ex) {

            }
            one.put("TYPE", name);
            try {
                one.put("CJLB", RIUtil.dicts.get(cjlb).getString("dict_name"));
            } catch (Exception ex) {
                one.put("CJLB", "");
            }


            if (lat != null && lat.length() > 2) {

                if (range.length() > 0) {
                    String[] ll = range.split("\\;");

                    double[] longa = new double[ll.length];
                    double[] lati = new double[ll.length];

                    for (int a = 0; a < ll.length; a++) {
                        String[] l = ll[a].split(",");
                        String oo = l[0];
                        String aa = l[1];
                        longa[a] = Double.parseDouble(oo);
                        lati[a] = Double.parseDouble(aa);
                    }

                    try {
                        if (isInPolygon(Double.parseDouble(lng), Double.parseDouble(lat), longa, lati)) {
                            back.add(one);
                        } else {
                        }
                    } catch (Exception ex) {

                    }

                } else {
                    back.add(one);
                }
            }

        }
        return back;
    }

    public static boolean isInPolygon(double pointLon, double pointLat, double[] lon, double[] lat) {
        // 将要判断的横纵坐标组成一个点
        Point2D.Double point = new Point2D.Double(pointLon, pointLat);
        // 将区域各顶点的横纵坐标放到一个点集合里面
        List<Point2D.Double> pointList = new ArrayList<Point2D.Double>();
        double polygonPoint_x = 0.0, polygonPoint_y = 0.0;
        for (int i = 0; i < lon.length; i++) {
            polygonPoint_x = lon[i];
            polygonPoint_y = lat[i];
            Point2D.Double polygonPoint = new Point2D.Double(polygonPoint_x, polygonPoint_y);
            pointList.add(polygonPoint);
        }
        return check(point, pointList);
    }

    /**
     * 一个点是否在多边形内
     *
     * @param point   要判断的点的横纵坐标
     * @param polygon 组成的顶点坐标集合
     * @return
     */
    private static boolean check(Point2D.Double point, List<Point2D.Double> polygon) {
        java.awt.geom.GeneralPath peneralPath = new java.awt.geom.GeneralPath();

        Point2D.Double first = polygon.get(0);
        // 通过移动到指定坐标（以双精度指定），将一个点添加到路径中
        peneralPath.moveTo(first.x, first.y);
        polygon.remove(0);
        for (Point2D.Double d : polygon) {
            // 通过绘制一条从当前坐标到新指定坐标（以双精度指定）的直线，将一个点添加到路径中。
            peneralPath.lineTo(d.x, d.y);
        }
        // 将几何多边形封闭
        peneralPath.lineTo(first.x, first.y);
        peneralPath.closePath();
        // 测试指定的 Point2D 是否在 Shape 的边界内。
        return peneralPath.contains(point);
    }

//    private JSONObject GetHZ1(String start, String end, String group, String uSql, JSONArray heads, String hzbq,
//                              String cby, int searchWD, String unit, String tb_start_time, String tb_end_time,
//                              String hb_start_time,
//                              String hb_end_time, int is_check, int is_mark, String bjfs, String zflx) {
//
//        List<String> list = Arrays.asList(zflx.split(","));
//
//
//        JSONObject ret = new JSONObject();
//        String start_time1 = start;
//        String end_time1 = end;
//        String bq = "";
//
//        HashMap<String, JSONObject> results = new HashMap<>();
//
//        for (String type : list) {
//
//
//
//            JSONObject hone = new JSONObject();
//            hone.put("key", "_hz");
//
//            hone.put("value", "合计");
//            heads.add(hone);
//            if (searchWD == 1) {
//                hone = new JSONObject();
//                hone.put("key", "_hz_hb");
//                hone.put("value", "合计_上期");
//                heads.add(hone);
//                hone = new JSONObject();
//                hone.put("key", "_hz_tb");
//                hone.put("value", "合计_同期");
//                heads.add(hone);
//                hone = new JSONObject();
//                hone.put("key", "_hz_hb_p");
//                hone.put("value", "合计_环比（%）");
//                heads.add(hone);
//                hone = new JSONObject();
//                hone.put("key", "_hz_tb_p");
//                hone.put("value", "合计_同比（%）");
//                heads.add(hone);
//            } else if (searchWD == 2) {
//                hone = new JSONObject();
//                hone.put("key", "_hz_cl_p");
//                hone.put("value", "合计_常量");
//                heads.add(hone);
//                hone = new JSONObject();
//                hone.put("key", "_hz_cl_xb_p");
//                hone.put("value", "合计_相比率（%）");
//                heads.add(hone);
//                hone = new JSONObject();
//                hone.put("key", "_hz_cl_zj_p");
//                hone.put("value", "合计_增减");
//                heads.add(hone);
//
//
//            } else if (searchWD == 3) {
//                hone = new JSONObject();
//                hone.put("key", "_hz_tb");
//                hone.put("value", "合计_同期");
//                heads.add(hone);
//                hone = new JSONObject();
//                hone.put("key", "_hz_tb_p");
//                hone.put("value", "合计_同比（%）");
//                heads.add(hone);
//            } else if (searchWD == 4) {
//                hone = new JSONObject();
//                hone.put("key", "_hz_hb");
//                hone.put("value", "合计_上期");
//                heads.add(hone);
//                hone = new JSONObject();
//                hone.put("key", "_hz_hb_p");
//                hone.put("value", "合计_环比（%）");
//                heads.add(hone);
//            }
//
//            String uq = "";
//            uq = uSql;
//
//            results = GetResults_hz(results, group, uq, start_time1, end_time1, "", cby, "_hz", unit, is_check,
//            is_mark, bjfs, zflx);
//            if (searchWD == 1) {
//                //环比
//                hone = new JSONObject();
//                hone.put("key", "_hz_hb");
//                hone.put("value", "上期");
//                heads.add(hone);
//                hone = new JSONObject();
//                hone.put("key", "_hz_hb_p");
//                hone.put("value", "环比（%）");
//                heads.add(hone);
//
//                try {
//                    start_time1 = hb_start_time + " 00:00:00";
//                    end_time1 = hb_end_time + " 23:59:59";
//
//                } catch (Exception ex) {
//                }
//
//                // results=GetHzRess(cby,group,uSql,id,results,"_hb",id);
//                results = GetResults_hz(results, group, uq, start_time1, end_time1, "_hb", cby, "_hz", unit,
//                is_check, is_mark, bjfs, zflx);
//                //同比
//
//                hone = new JSONObject();
//                hone.put("key", "_hz_tb");
//                hone.put("value", "同期");
//                heads.add(hone);
//                hone = new JSONObject();
//                hone.put("key", "_hz_tb_p");
//                hone.put("value", "同比（%）");
//                heads.add(hone);
//                start_time1 = tb_start_time + " 00:00:00";
//                end_time1 = tb_end_time + " 23:59:59";
//
//                // results=GetHzRess(cby,group,uSql,id,results,"_tb",id);
//                results = GetResults_hz(results, group, uq, start_time1, end_time1, "_tb", cby, "_hz", unit,
//                is_check, is_mark, bjfs, zflx);
//            } else if (searchWD == 2) {
//                String start_time_t = tb_start_time;
//                String end_time_t = tb_end_time;
//
//                String start_time_h = "";
//                String end_time_h = "";
//                try {
//
//                    end_time_h = RIUtil.GetNextDate(start_time1, -30);
//                    end_time_h = RIUtil.getLMonthEnd(end_time_h);
//
//                    start_time_h = RIUtil.GetNextDate(end_time_h, -40).substring(0, 7) + "-01 00:00:00";
//                } catch (Exception e) {
//                    // TODO: handle exception
//                }
//                uq = uq + " and ((cjsj01>='" + start_time_t + "' and cjsj01<='" + end_time_t + "') or " +
//                        "(cjsj01>='" + start_time_h + "' and cjsj01<='" + end_time_h + "')) ";
//
//                hone = new JSONObject();
//                hone.put("key", "_hz_cl_p");
//                hone.put("value", "常量");
//                heads.add(hone);
//
//                hone = new JSONObject();
//                hone.put("key", "_hz_cl_xb_p");
//                hone.put("value", "相比较(%)");
//                heads.add(hone);
//
//                hone = new JSONObject();
//                hone.put("key", "_hz_cl_zj_p");
//                hone.put("value", "相差");
//                heads.add(hone);
//
//                results = GetResults_cl_hz(results, group, uq, "_cl", cby, "_hz", unit, is_check, is_mark, zflx);
//
//            } else if (searchWD == 3) {
//                //同比
//
//                hone = new JSONObject();
//                hone.put("key", "_hz_tb");
//                hone.put("value", "同期");
//                heads.add(hone);
//                hone = new JSONObject();
//                hone.put("key", "_hz_tb_p");
//                hone.put("value", "同比（%）");
//                heads.add(hone);
//                start_time1 = tb_start_time + " 00:00:00";
//                end_time1 = tb_end_time + " 23:59:59";
//
//                // results=GetHzRess(cby,group,uSql,id,results,"_tb",id);
//                results = GetResults_hz(results, group, uq, start_time1, end_time1, "_tb", cby, "_hz", unit,
//                is_check, is_mark, bjfs, zflx);
//            } else if (searchWD == 4) {
//                //环比
//                hone = new JSONObject();
//                hone.put("key", "_hz_hb");
//                hone.put("value", "上期");
//                heads.add(hone);
//                hone = new JSONObject();
//                hone.put("key", "_hz_hb_p");
//                hone.put("value", "环比（%）");
//                heads.add(hone);
//
//                try {
//                    start_time1 = hb_start_time + " 00:00:00";
//                    end_time1 = hb_end_time + " 23:59:59";
//
//                } catch (Exception ex) {
//                }
//
//                // results=GetHzRess(cby,group,uSql,id,results,"_hb",id);
//                results = GetResults_hz(results, group, uq, start_time1, end_time1, "_hb", cby, "_hz", unit,
//                is_check, is_mark, bjfs, zflx);
//            }
//
//
//            start_time1 = start;
//            end_time1 = end;
//            uq = uSql;
//            //  results=GetHzRess(cby,group,uSql,ids,results,"",bq);
//            results = GetResults_hz(results, group, uq, start_time1, end_time1, "", cby, "_hz", unit, is_check,
//            is_mark, bjfs, zflx);
//            if (searchWD == 1) {
//                try {
//                    start_time1 = hb_start_time + " 00:00:00";
//                    end_time1 = hb_end_time + " 23:59:59";
//
//                } catch (Exception ex) {
//                }
//
//                results = GetResults_hz(results, group, uq, start_time1, end_time1, "_hb", cby, "_hz", unit,
//                is_check, is_mark, bjfs, zflx);
//
//                start_time1 = tb_start_time + " 00:00:00";
//                end_time1 = tb_end_time + " 23:59:59";
//
//                //results=GetHzRess(cby,group,uSql,ids,results,"_tb",bq);
//                results = GetResults_hz(results, group, uq, start_time1, end_time1, "_tb", cby, "_hz", unit,
//                is_check, is_mark, bjfs, zflx);
//            } else if (searchWD == 2) {
//                String start_time_t = tb_start_time;
//                String end_time_t = tb_end_time;
//
//                String start_time_h = "";
//                String end_time_h = "";
//                try {
//
//                    end_time_h = RIUtil.GetNextDate(start_time1, -30);
//                    end_time_h = RIUtil.getLMonthEnd(end_time_h);
//
//                    start_time_h = RIUtil.GetNextDate(end_time_h, -40).substring(0, 7) + "-01 00:00:00";
//                } catch (Exception e) {
//                    // TODO: handle exception
//                }
//                uq =
//                        uq + " and ((cjsj01>='" + start_time_t + "' and cjsj01<='" + end_time_t + "') or " + "
//                        (cjsj01" +
//                                ">='" + start_time_h + "' and cjsj01<='" + end_time_h + "')) ";
//
//
//                results = GetResults_cl_hz(results, group, uq, "_cl", cby, "_hz", unit, is_check, is_mark, zflx);
//
//            } else if (searchWD == 3) {
//                start_time1 = tb_start_time + " 00:00:00";
//                end_time1 = tb_end_time + " 23:59:59";
//
//                //results=GetHzRess(cby,group,uSql,ids,results,"_tb",bq);
//                results = GetResults_hz(results, group, uq, start_time1, end_time1, "_tb", cby, "_hz", unit,
//                is_check, is_mark, bjfs, zflx);
//            } else if (searchWD == 4) {
//                try {
//                    start_time1 = hb_start_time + " 00:00:00";
//                    end_time1 = hb_end_time + " 23:59:59";
//
//                } catch (Exception ex) {
//                }
//
//                results = GetResults_hz(results, group, uq, start_time1, end_time1, "_hb", cby, "_hz", unit,
//                is_check, is_mark, bjfs, zflx);
//            }
//
//        }
//        // logger.warn(heads.toString());
//        //logger.warn(results.toString());
//        List<JSONObject> res = new ArrayList<>();
//        for (Map.Entry<String, JSONObject> dd : results.entrySet()) {
//            String code = dd.getKey();
//            JSONObject one = new JSONObject();
//            try {
//                one.put("code", code);
//                if (group.equals("ZRQ")) {
//                    String name = RIUtil.dicts.get(code).getString("remark");
//                    if (!code.equals("320400000000")) {
//                        name = name.replace("常州市公安局", "");
//                    }
//                    one.put("name", name);
//                } else {
//                    one.put("name", RIUtil.dicts.get(code).getString("dict_name"));
//                }
//                one.put("index_no", RIUtil.dicts.get(code).getInteger("index_no"));
//                one.putAll(dd.getValue());
//                res.add(one);
//            } catch (Exception ex) {
//
//            }
//        }
//
//        Collections.sort(res, (JSONObject o1, JSONObject o2) -> {
//            //转成JSON对象中保存的值类型
//            String a = "";
//            String b = "";
//
//            try {
//                a = o1.getString("code");
//                b = o2.getString("code");
//            } catch (Exception ex) {
//
//            }
//
//            int result = a.compareTo(b);
//            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
//            if (result > 0) {  //降序排列，升序改成a>b
//                return 1;
//            } else if (a == b) {
//                return 0;
//            } else {
//                return -1;
//            }
//        });
//        ret.put("head", heads);
//        ret.put("body", res);
//        return ret;
//
//
//    }

    private static JSONObject GetHZ2(String start, String end, String group, String uSql, JSONArray heads,
     String hzbq, String cby, int searchWD, String unit, String tb_start_time, String tb_end_time,
      String hb_start_time, String hb_end_time, int is_check, int is_mark, String bjfs, String zflx, String cjnr) {
        DecimalFormat df = new DecimalFormat("#0.0");
        String start_time1 = start;
        String end_time1 = end;

        JSONObject ret = new JSONObject();
        HashMap<String, JSONObject> results = new HashMap<>();


        List<String> list = Arrays.asList(zflx.split(","));

        for (String bq : list) {
            String hname = "";
            if (bq.equals("1")) {
                hname = "出租房";
            } else if (bq.equals("2")) {
                hname = "群租房";
            } else if (bq.equals("3")) {
                hname = "合租房";
            }

            String ids = "";

            JSONObject hone = new JSONObject();
            hone.put("key", bq + "_hz");

            hone.put("value", hname + "_合计");
            heads.add(hone);


            String uq = "";

            if (bq.equals("1")) {
                uq += " and CZJZ_PDBS = 1 ";
            } else if (bq.equals("2")) {
                uq += " and SFQZ = 1 ";
            } else if (bq.equals("3")) {
                uq += " and SFHZF = 1 ";
            }

            if (cjnr != null && cjnr.length() > 0) {
                uq += " and CLJG like '%" + cjnr + "%' ";
            }

            uq += uSql;

            //  results=GetHzRess(cby,group,uSql,ids,results,"",bq);
            results = GetResults_hz(results, group, uq, start_time1, end_time1, bq, "", cby, "_hz", unit, is_check,
 is_mark, bjfs, bq);
            //同环比
            if (searchWD == 1) {

                //环比
                hone = new JSONObject();
                hone.put("key", bq + "_hz_hb");
                hone.put("value", hname + "_" + "_上期");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + "_hz_hb_p");
                hone.put("value", hname + "_" + "_环比（%）");
                heads.add(hone);

                try {
                    start_time1 = hb_start_time + " 00:00:00";
                    end_time1 = hb_end_time + " 23:59:59";

                } catch (Exception ex) {
                }

                results = GetResults_hz(results, group, uq, start_time1, end_time1, bq, "_hb", cby, "_hz", unit,
                 is_check, is_mark, bjfs, bq);

                hone = new JSONObject();
                hone.put("key", bq + "_hz_tb");
                hone.put("value", hname + "_" + "_同期");
                heads.add(hone);
                hone.put("key", bq + "_hz_tb_p");
                hone.put("value", hname + "_" + "_同比（%）");
                heads.add(hone);
                start_time1 = tb_start_time + " 00:00:00";
                end_time1 = tb_end_time + " 23:59:59";


                //results=GetHzRess(cby,group,uSql,ids,results,"_tb",bq);
                results = GetResults_hz(results, group, uq, start_time1, end_time1, bq, "_tb", cby, "_hz", unit,
 is_check, is_mark, bjfs, bq);
            } else if (searchWD == 2) {
                //常量
                String start_time_t = tb_start_time + " 00:00:00";
                String end_time_t = tb_end_time + " 23:59:59";

                String start_time_h = "";
                String end_time_h = "";
                try {

                    end_time_h = RIUtil.GetNextDate(start_time1, -30);
                    end_time_h = RIUtil.getLMonthEnd(end_time_h);

                    start_time_h = RIUtil.GetNextDate(end_time_h, -40).substring(0, 7) + "-01 00:00:00";
                } catch (Exception e) {
                    // TODO: handle exception
                }
                uq =
                 uq + " and ((cjsj01>='" + start_time_t + "' and cjsj01<='" + end_time_t + "') or " + "(cjsj01" +
                ">='" + start_time_h + "' and cjsj01<='" + end_time_h + "')) ";

                hone = new JSONObject();
                hone.put("key", bq + "_hz_cl_p");
                hone.put("value", hname + "_" + "_常量");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", bq + "_hz_cl_xb_p");
                hone.put("value", hname + "_" + "_相比较(%)");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", bq + "_hz_cl_zj_p");
                hone.put("value", hname + "_" + "_相差");
                heads.add(hone);

                String id = "";
                results = GetResults_cl_hz(results, group, uq, "_cl", cby, "_hz", unit, is_check, is_mark, bq);

            } else if (searchWD == 3) {
                //同比

                hone = new JSONObject();
                hone.put("key", bq + "_hz_tb");
                hone.put("value", hname + "_" + "_同期");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + "_hz_tb_p");
                hone.put("value", hname + "_" + "_同比（%）");
                heads.add(hone);

                start_time1 = tb_start_time + " 00:00:00";
                end_time1 = tb_end_time + " 23:59:59";

                //results=GetHzRess(cby,group,uSql,ids,results,"_tb",bq);
                String ty = "_tb";
                logger.warn(ty);
                results = GetResults_hz(results, group, uq, start_time1, end_time1, bq, ty, cby, "_hz", unit,
                        is_check, is_mark, bjfs, bq);
            } else if (searchWD == 4) {
                //环比

                hone = new JSONObject();
                hone.put("key", bq + "_hz_hb");
                hone.put("value", hname + "_" + "_上期");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + "_hz_hb_p");
                hone.put("value", hname + "_" + "_环比（%）");
                heads.add(hone);
                try {
                    start_time1 = hb_start_time + " 00:00:00";
                    end_time1 = hb_end_time + " 23:59:59";

                } catch (Exception ex) {
                }

                results = GetResults_hz(results, group, uq, start_time1, end_time1, bq, "_hb", cby, "_hz", unit,
is_check, is_mark, bjfs, bq);
            }

        }
        // logger.warn(heads.toString());
        //logger.warn(results.toString());
        List<JSONObject> res = new ArrayList<>();
        String sql = "select id ,type2 from kh_zf_pcs_type";
        InfoModelHelper mysql = null;
        List<JSONObject> type2 = new ArrayList<>();
        try {
            mysql = InfoModelPool.getModel();
            type2 = mysql.query(sql);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(607005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        for (Map.Entry<String, JSONObject> dd : results.entrySet()) {
            String code = dd.getKey();
            JSONObject one = new JSONObject();
            try {
                one.put("code", code);
                String name = RIUtil.dicts.get(code).getString("remark");
                one.put("name", name);
                one.put("index_no", RIUtil.dicts.get(code).getInteger("index_no"));
                one.putAll(dd.getValue());
                //派出所类别
                if (!type2.isEmpty()) {
                    List<JSONObject> id =
                            type2.stream().filter(jsonObject -> jsonObject.getString("id").equals(code)).collect(Collectors.toList());
                    if (!id.isEmpty()) {
                        one.put("type2", id.get(0).getString("type2").toUpperCase());
                    }
                }
                res.add(one);
            } catch (Exception ex) {

            }
        }

        Collections.sort(res, (JSONObject o1, JSONObject o2) -> {
            //转成JSON对象中保存的值类型
            String a = "";
            String b = "";

            try {
                a = o1.getString("code");
                b = o2.getString("code");
            } catch (Exception ex) {

            }

            int result = a.compareTo(b);
            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (result > 0) {  //降序排列，升序改成a>b
                return 1;
            } else if (a == b) {
                return 0;
            } else {
                return -1;
            }
        });

        ret.put("head", heads);
        ret.put("body", res);
        return ret;


    }

    private static JSONObject GetHZ(String start, String end, String group, String uSql, JSONArray heads, String hzbq
    , String cby, int searchWD, String unit, String tb_start_time, String tb_end_time, String hb_start_time,
     String hb_end_time, int is_check, int is_mark, String bjfs, String cjnr) {
        DecimalFormat df = new DecimalFormat("#0.0");
        String start_time1 = start;
        String end_time1 = end;

        JSONObject ret = new JSONObject();
        HashMap<String, JSONObject> results = new HashMap<>();
        String bqs[] = hzbq.split(",");
        JSONArray dict53 = RIUtil.GetDictByType(53);
        OracleHelper ora_hl = null;
        for (int i = 0; i < bqs.length; i++) {
            String bq = bqs[i];
            String hname = RIUtil.dicts.get(bq).getString("dict_name");
            String ids = "";

            JSONObject hone = new JSONObject();
            hone.put("key", bq + "_hz");

            hone.put("value", hname + "_合计");
            heads.add(hone);
            if (searchWD == 1) {
                hone = new JSONObject();
                hone.put("key", bq + "_hz_hb");
                hone.put("value", hname + "_合计_上期");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + "_hz_tb");
                hone.put("value", hname + "_合计_同期");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + "_hz_hb_p");
                hone.put("value", hname + "_合计_环比（%）");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + "_hz_tb_p");
                hone.put("value", hname + "_合计_同比（%）");
                heads.add(hone);
            } else if (searchWD == 2) {
                hone = new JSONObject();
                hone.put("key", bq + "_hz_cl_p");
                hone.put("value", hname + "_合计_常量");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + "_hz_cl_xb_p");
                hone.put("value", hname + "_合计_相比率（%）");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + "_hz_cl_zj_p");
                hone.put("value", hname + "_合计_增减");
                heads.add(hone);


            } else if (searchWD == 3) {
                hone = new JSONObject();
                hone.put("key", bq + "_hz_tb");
                hone.put("value", hname + "_合计_同期");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + "_hz_tb_p");
                hone.put("value", hname + "_合计_同比（%）");
                heads.add(hone);
            } else if (searchWD == 4) {
                hone = new JSONObject();
                hone.put("key", bq + "_hz_hb");
                hone.put("value", hname + "_合计_上期");
                heads.add(hone);
                hone = new JSONObject();
                hone.put("key", bq + "_hz_hb_p");
                hone.put("value", hname + "_合计_环比（%）");
                heads.add(hone);
            }

            for (int d = 0; d < dict53.size(); d++) {
                JSONObject done = dict53.getJSONObject(d);
                String father_id = done.getString("father_id");
                String id = done.getString("id");
                String name = done.getString("dict_name");

                if (father_id.equals(bq)) {
                    ids = ids + id + "','";
                    hone = new JSONObject();
                    hone.put("key", id + "_hz");
                    hone.put("value", hname + "_" + name);
                    heads.add(hone);

                    // results=GetHzRess(cby,group,uSql,id,results,"",id);

                    String uq = uSql + " and  BQ in ('" + id + "') ";

                    if (cjnr != null && cjnr.length() > 0) {
                        uq += " and CLJG like '%" + cjnr + "%' ";
                    }


                    results = GetResults(results, group, uq, start_time1, end_time1, id, "", cby, "_hz", unit,
                        is_check, is_mark, bjfs);
                    if (searchWD == 1) {
                        //环比
                        hone = new JSONObject();
                        hone.put("key", id + "_hz_hb");
                        hone.put("value", hname + "_" + name + "_上期");
                        heads.add(hone);
                        hone = new JSONObject();
                        hone.put("key", id + "_hz_hb_p");
                        hone.put("value", hname + "_" + name + "_环比（%）");
                        heads.add(hone);

                        try {
                            start_time1 = hb_start_time + " 00:00:00";
                            end_time1 = hb_end_time + " 23:59:59";

                        } catch (Exception ex) {
                        }

                        // results=GetHzRess(cby,group,uSql,id,results,"_hb",id);
                        results = GetResults(results, group, uq, start_time1, end_time1, id, "_hb", cby, "_hz", unit,
                         is_check, is_mark, bjfs);
                        //同比

                        hone = new JSONObject();
                        hone.put("key", id + "_hz_tb");
                        hone.put("value", hname + "_" + name + "_同期");
                        heads.add(hone);
                        hone = new JSONObject();
                        hone.put("key", id + "_hz_tb_p");
                        hone.put("value", hname + "_" + name + "_同比（%）");
                        heads.add(hone);
                        start_time1 = tb_start_time + " 00:00:00";
                        end_time1 = tb_end_time + " 23:59:59";

                        // results=GetHzRess(cby,group,uSql,id,results,"_tb",id);
                        results = GetResults(results, group, uq, start_time1, end_time1, id, "_tb", cby, "_hz", unit,
                        is_check, is_mark, bjfs);
                    } else if (searchWD == 2) {
                        String start_time_t = tb_start_time + " 00:00:00";
                        String end_time_t = tb_end_time + " 23:59:59";

                        String start_time_h = "";
                        String end_time_h = "";
                        try {

                            end_time_h = RIUtil.GetNextDate(start_time1, -30);
                            end_time_h = RIUtil.getLMonthEnd(end_time_h);

                            start_time_h = RIUtil.GetNextDate(end_time_h, -40).substring(0, 7) + "-01 00:00:00";
                        } catch (Exception e) {
                            // TODO: handle exception
                        }
                        uq = uq + " and ((cjsj01>='" + start_time_t + "' and cjsj01<='" + end_time_t + "') or " +
                        "(cjsj01>='" + start_time_h + "' and cjsj01<='" + end_time_h + "')) ";

                        hone = new JSONObject();
                        hone.put("key", id + "_hz_cl_p");
                        hone.put("value", hname + "_" + name + "_常量");
                        heads.add(hone);

                        hone = new JSONObject();
                        hone.put("key", id + "_hz_cl_xb_p");
                        hone.put("value", hname + "_" + name + "_相比较(%)");
                        heads.add(hone);

                        hone = new JSONObject();
                        hone.put("key", id + "_hz_cl_zj_p");
                        hone.put("value", hname + "_" + name + "_相差");
                        heads.add(hone);

                        results = GetResults_cl(results, group, uq, id, "_cl", cby, "_hz", unit, is_check, is_mark);

                    } else if (searchWD == 3) {
                        //同比

                        hone = new JSONObject();
                        hone.put("key", id + "_hz_tb");
                        hone.put("value", hname + "_" + name + "_同期");
                        heads.add(hone);
                        hone = new JSONObject();
                        hone.put("key", id + "_hz_tb_p");
                        hone.put("value", hname + "_" + name + "_同比（%）");
                        heads.add(hone);
                        start_time1 = tb_start_time + " 00:00:00";
                        end_time1 = tb_end_time + " 23:59:59";

                        // results=GetHzRess(cby,group,uSql,id,results,"_tb",id);
                        results = GetResults(results, group, uq, start_time1, end_time1, id, "_tb", cby, "_hz", unit,
                        is_check, is_mark, bjfs);
                    } else if (searchWD == 4) {
                        //环比
                        hone = new JSONObject();
                        hone.put("key", id + "_hz_hb");
                        hone.put("value", hname + "_" + name + "_上期");
                        heads.add(hone);
                        hone = new JSONObject();
                        hone.put("key", id + "_hz_hb_p");
                        hone.put("value", hname + "_" + name + "_环比（%）");
                        heads.add(hone);

                        try {
                            start_time1 = hb_start_time + " 00:00:00";
                            end_time1 = hb_end_time + " 23:59:59";

                        } catch (Exception ex) {
                        }

                        // results=GetHzRess(cby,group,uSql,id,results,"_hb",id);
                        results = GetResults(results, group, uq, start_time1, end_time1, id, "_hb", cby, "_hz", unit,
            is_check, is_mark, bjfs);
                    }

                }

            }
            if (ids.endsWith("','")) {
                ids = "'" + ids.substring(0, ids.length() - 2);
            }

            start_time1 = start;
            end_time1 = end;
            String uq = uSql + " AND  bq in(" + ids + ") ";
            if (cjnr != null && cjnr.length() > 0) {
                uq += " and CLJG like '%" + cjnr + "%' ";
            }
            //  results=GetHzRess(cby,group,uSql,ids,results,"",bq);
            results = GetResults(results, group, uq, start_time1, end_time1, bq, "", cby, "_hz", unit, is_check,
             is_mark, bjfs);
            if (searchWD == 1) {
                try {
                    start_time1 = hb_start_time + " 00:00:00";
                    end_time1 = hb_end_time + " 23:59:59";

                } catch (Exception ex) {
                }

                results = GetResults(results, group, uq, start_time1, end_time1, bq, "_hb", cby, "_hz", unit,
                is_check, is_mark, bjfs);

                start_time1 = tb_start_time + " 00:00:00";
                end_time1 = tb_end_time + " 23:59:59";

                //results=GetHzRess(cby,group,uSql,ids,results,"_tb",bq);
                results = GetResults(results, group, uq, start_time1, end_time1, bq, "_tb", cby, "_hz", unit,
is_check, is_mark, bjfs);
            } else if (searchWD == 2) {
                String start_time_t = tb_start_time + " 00:00:00";
                String end_time_t = tb_end_time + " 23:59:59";

                String start_time_h = "";
                String end_time_h = "";
                try {

                    end_time_h = RIUtil.GetNextDate(start_time1, -30);
                    end_time_h = RIUtil.getLMonthEnd(end_time_h);

                    start_time_h = RIUtil.GetNextDate(end_time_h, -40).substring(0, 7) + "-01 00:00:00";
                } catch (Exception e) {
                    // TODO: handle exception
                }
                uq =
                 uq + " and ((cjsj01>='" + start_time_t + "' and cjsj01<='" + end_time_t + "') or " + "(cjsj01" +
 ">='" + start_time_h + "' and cjsj01<='" + end_time_h + "')) ";


                results = GetResults_cl(results, group, uq, bq, "_cl", cby, "_hz", unit, is_check, is_mark);

            } else if (searchWD == 3) {
                start_time1 = tb_start_time + " 00:00:00";
                end_time1 = tb_end_time + " 23:59:59";

                //results=GetHzRess(cby,group,uSql,ids,results,"_tb",bq);
                results = GetResults(results, group, uq, start_time1, end_time1, bq, "_tb", cby, "_hz", unit,
                is_check, is_mark, bjfs);
            } else if (searchWD == 4) {
                try {
                    start_time1 = hb_start_time + " 00:00:00";
                    end_time1 = hb_end_time + " 23:59:59";

                } catch (Exception ex) {
                }

                results = GetResults(results, group, uq, start_time1, end_time1, bq, "_hb", cby, "_hz", unit,
                 is_check, is_mark, bjfs);
            }


        }
        // logger.warn(heads.toString());
        //logger.warn(results.toString());
        List<JSONObject> res = new ArrayList<>();
        String sql = "select id ,type2 from kh_zf_pcs_type";
        InfoModelHelper mysql = null;
        List<JSONObject> type2 = new ArrayList<>();
        try {
            mysql = InfoModelPool.getModel();
            type2 = mysql.query(sql);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(607005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        for (Map.Entry<String, JSONObject> dd : results.entrySet()) {
            String code = dd.getKey();
            JSONObject one = new JSONObject();
            try {
                one.put("code", code);
                String name = RIUtil.dicts.get(code).getString("remark");
                one.put("name", name);
                one.put("index_no", RIUtil.dicts.get(code).getInteger("index_no"));
                one.putAll(dd.getValue());
                //派出所类别
                if (!type2.isEmpty()) {
                    List<JSONObject> id =
                            type2.stream().filter(jsonObject -> jsonObject.getString("id").equals(code)).collect(Collectors.toList());
                    if (!id.isEmpty()) {
                        one.put("type2", id.get(0).getString("type2").toUpperCase());
                    }
                }
                if (!name.equals("")) {
                    res.add(one);
                }
            } catch (Exception ex) {

            }
        }

        Collections.sort(res, (JSONObject o1, JSONObject o2) -> {
            //转成JSON对象中保存的值类型
            String a = "";
            String b = "";

            try {
                a = o1.getString("code");
                b = o2.getString("code");
            } catch (Exception ex) {

            }

            int result = a.compareTo(b);
            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (result > 0) {  //降序排列，升序改成a>b
                return 1;
            } else if (a == b) {
                return 0;
            } else {
                return -1;
            }
        });
        ret.put("head", heads);
        ret.put("body", res);
        return ret;


    }

    private HashMap<String, JSONObject> GetHzRess1(String cby, String group, String uSql, String id, HashMap<String,
JSONObject> results, String wd, String bq) {
        DecimalFormat df = new DecimalFormat("#0.0");
        OracleHelper ora_hl = null;
        if (!id.contains("'")) {
            id = "'" + id + "'";
        }
        try {
            ora_hl = new OracleHelper("ora_hl");

            String sql =
            "select count(jjbh) as count ," + cby + " as " + group + "  from HL.DSJ_JQ where 1=1 " + uSql + " " +
             "and " + "bq " + "in(" + id + ") " + "group by " + cby;
            //  logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            if (group.equals("FJ")) {
                sql = "select count(jjbh) as count ,'320400000000' as FJ  from HL.DSJ_JQ where 1=1 " + uSql + " " +
                "and bq " + "in(" + id + ")";
                list.addAll(ora_hl.query(sql));
            }
            if (list.size() > 0) {
                for (int j = 0; j < list.size(); j++) {
                    JSONObject one = list.get(j);
                    String code = one.getString(group);
                    String count = one.getString("COUNT");
                    JSONObject det = new JSONObject();
                    if (results.containsKey(code)) {
                        det = results.get(code);
                    }
                    det.put(bq + "_hz" + "", count);
                    double c = Double.parseDouble(count);


                    if (wd.contains("tb")) {

                        double tb = 0;

                        try {
                            tb = det.getIntValue(bq + "_hz_tb");
                        } catch (Exception e) {
                            // TODO: handle exception
                        }

                        double bt = c - tb;
                        double tbp = bt / tb * 100;

                        det.put(bq + "_hz_tb_p", df.format(tbp));
                    }
                    if (wd.contains("hb")) {

                        double hb = 0;

                        try {
                            hb = det.getIntValue(bq + "_hz_hb");
                        } catch (Exception e) {
                            // TODO: handle exception
                        }

                        double bt = c - hb;
                        double hbp = bt / hb * 100;

                        det.put(bq + "_hz_hb_p", df.format(hbp));
                    }

                    results.put(code, det);

                }
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            ora_hl.close();

        }
        return results;
    }

    private static JSONObject GetZCTHB(String start_time, String end_time, String group, String uSql, int searchWD,
     String[] lbs, JSONArray heads, String cby, String unit, String tb_start_time, String tb_end_time,
                                       String hb_start_time, String hb_end_time, int is_check, int is_mark, String bjfs, String cjnr) {
        JSONObject ret = new JSONObject();

        HashMap<String, JSONObject> results = new HashMap<>();
        for (int i = 0; i < lbs.length; i++) {
            String uq = uSql;
            String lb = lbs[i];

            if (lb.endsWith("000000")) {
                uq = uq + " and type50='" + lb + "'";
            } else if (lb.endsWith("0000")) {
                uq = uq + " and type52='" + lb + "'";
            } else if (lb.endsWith("00")) {
                uq = uq + " and type54='" + lb + "'";
            } else {
                uq = uq + " and cjlb='" + lb + "'";
            }

            if (cjnr != null && cjnr.length() > 0) {
                uq += " and CLJG like '%" + cjnr + "%' ";
            }
            //  logger.warn(uq);

            results = GetResults(results, group, uq, start_time, end_time, lb, "", cby, "_zc", unit, is_check,
             is_mark, bjfs);


            JSONObject hone = new JSONObject();
            hone.put("key", lb + "_zc");
            try {
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "");
            } catch (Exception ex) {
                logger.error(lb);
                hone.put("value", "");
            }
            heads.add(hone);
            // logger.warn(results);

            if (searchWD == 1)// 同环比
            {
                //同比
                String start_time_t = tb_start_time + " 00:00:00";
                String end_time_t = tb_end_time + " 23:59:59";
                results = GetResults(results, group, uq, start_time_t, end_time_t, lb, "_tb", cby, "_zc", unit,
                 is_check, is_mark, bjfs);

                hone = new JSONObject();
                hone.put("key", lb + "_zc_tb");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同期");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_zc_tb_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同比(%)");
                heads.add(hone);

                //环比
                String start_time_h = "";
                String end_time_h = "";
                try {
                    start_time_h = hb_start_time + " 00:00:00";
                    end_time_h = hb_end_time + " 23:59:59";
                } catch (Exception e) {
                    // TODO: handle exception
                }
                results = GetResults(results, group, uq, start_time_h, end_time_h, lb, "_hb", cby, "_zc", unit,
is_check, is_mark, bjfs);

                hone = new JSONObject();
                hone.put("key", lb + "_zc_hb");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_上期");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_zc_hb_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_环比(%)");
                heads.add(hone);
            } else if (searchWD == 2)//常量
            {
                String start_time_t = tb_start_time + " 00:00:00";
                String end_time_t = tb_end_time + " 23:59:59";

                String start_time_h = "";
                String end_time_h = "";
                try {

                    end_time_h = RIUtil.GetNextDate(start_time, -30);
                    end_time_h = RIUtil.getLMonthEnd(end_time_h);

                    start_time_h = RIUtil.GetNextDate(end_time_h, -40).substring(0, 7) + "-01 00:00:00";
                } catch (Exception e) {
                    // TODO: handle exception
                }
                uq =
                 uq + " and ((cjsj01>='" + start_time_t + "' and cjsj01<='" + end_time_t + "') or (cjsj01>='" + start_time_h + "' and cjsj01<='" + end_time_h + "')) ";

                hone = new JSONObject();
                hone.put("key", lb + "_zc_cl_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_常量");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_zc_cl_xb_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_相比较(%)");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_zc_cl_zj_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_相差");
                heads.add(hone);

                results = GetResults_cl(results, group, uq, lb, "_cl", cby, "_zc", unit, is_check, is_mark);

            } else if (searchWD == 3) // 同比
            {
                String start_time_t = tb_start_time + " 00:00:00";
                String end_time_t = tb_end_time + " 23:59:59";
                results = GetResults(results, group, uq, start_time_t, end_time_t, lb, "_tb", cby, "_zc", unit,
                 is_check, is_mark, bjfs);

                hone = new JSONObject();
                hone.put("key", lb + "_zc_tb");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同期");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_zc_tb_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同比(%)");
                heads.add(hone);
            } else if (searchWD == 4) // 环比
            {
                String start_time_h = "";
                String end_time_h = "";
                try {
                    start_time_h = hb_start_time + " 00:00:00";
                    end_time_h = hb_end_time + " 23:59:59";
                } catch (Exception e) {
                    // TODO: handle exception
                }
                results = GetResults(results, group, uq, start_time_h, end_time_h, lb, "_hb", cby, "_zc", unit,
                 is_check, is_mark, bjfs);

                hone = new JSONObject();
                hone.put("key", lb + "_zc_hb");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_上期");
                heads.add(hone);

                hone = new JSONObject();
                hone.put("key", lb + "_zc_hb_p");
                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_环比(%)");
                heads.add(hone);
            }

        }

        //logger.warn(heads.toString());
        //logger.warn(results.toString());
        List<JSONObject> res = new ArrayList<>();
        String sql = "select id ,type2 from kh_zf_pcs_type";
        InfoModelHelper mysql = null;
        List<JSONObject> type2 = new ArrayList<>();
        try {
            mysql = InfoModelPool.getModel();
            type2 = mysql.query(sql);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(607005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        for (Map.Entry<String, JSONObject> dd : results.entrySet()) {
            String code = dd.getKey();
            JSONObject one = new JSONObject();
            try {
                one.put("code", code);
                String name = RIUtil.dicts.get(code).getString("remark");
                one.put("name", name);
                one.put("index_no", RIUtil.dicts.get(code).getInteger("index_no"));
                one.putAll(dd.getValue());
                //派出所类别
                if (!type2.isEmpty()) {
                    List<JSONObject> id =
                     type2.stream().filter(jsonObject -> jsonObject.getString("id").equals(code)).collect(Collectors.toList());
                    if (!id.isEmpty()) {
                        one.put("type2", id.get(0).getString("type2").toUpperCase());
                    }
                }
                if (!name.equals("")) {
                    res.add(one);
                }

            } catch (Exception ex) {

            }
        }

        Collections.sort(res, (JSONObject o1, JSONObject o2) -> {
            //转成JSON对象中保存的值类型
            String a = "";
            String b = "";

            try {
                a = o1.getString("code");
                b = o2.getString("code");
            } catch (Exception ex) {

            }

            int result = a.compareTo(b);
            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (result > 0) {  //降序排列，升序改成a>b
                return 1;
            } else if (a == b) {
                return 0;
            } else {
                return -1;
            }
        });

        ret.put("head", heads);
        ret.put("body", res);
        return ret;
    }

    //综合体同环比
//    private JSONObject GetZHTTHB(String start_time, String end_time, String group, String uSql, int searchWD,
//                                String[] lbs, JSONArray heads, String cby, String unit, String tb_start_time,
//                                String tb_end_time,
//                                String hb_start_time, String hb_end_time, int is_check, int is_mark) {
//        JSONObject ret = new JSONObject();
//
//        HashMap<String, JSONObject> results = new HashMap<>();
//        for (int i = 0; i < lbs.length; i++) {
//            String uq = uSql;
//            String lb = lbs[i];
//
//            if (lb.endsWith("0000")) {
//                uq = uq + " and type50='" + lb + "'";
//            } else if (lb.endsWith("00")) {
//                uq = uq + " and type52='" + lb + "'";
//            } else {
//                uq = uq + " and cjlb='" + lb + "'";
//            }
//
//            results = GetResults_zht(results, group, uq, start_time, end_time, lb, "", cby, "_zht", unit, is_check,
//            is_mark);
//
//
//            JSONObject hone = new JSONObject();
//            hone.put("key", lb + "_zht");
//            hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "");
//            heads.add(hone);
//            // logger.warn(results);
//
//            if (searchWD == 1)// 同环比
//            {
//                //同比
//                String start_time_t = tb_start_time + " 00:00:00";
//                String end_time_t = tb_end_time + " 23:59:59";
//                results = GetResults_jz(results, group, uq, start_time_t, end_time_t, lb, "_tb", cby, "_zc", unit,
//                is_check, is_mark);
//
//                hone = new JSONObject();
//                hone.put("key", lb + "_zht_tb");
//                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同期");
//                heads.add(hone);
//
//                hone = new JSONObject();
//                hone.put("key", lb + "_zht_tb_p");
//                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同比(%)");
//                heads.add(hone);
//
//                //环比
//                String start_time_h = "";
//                String end_time_h = "";
//                try {
//                    start_time_h = hb_start_time + " 00:00:00";
//                    end_time_h = hb_end_time + " 23:59:59";
//                } catch (Exception e) {
//                    // TODO: handle exception
//                }
//                results = GetResults_jz(results, group, uq, start_time_h, end_time_h, lb, "_hb", cby, "_zht", unit,
//                is_check, is_mark);
//
//                hone = new JSONObject();
//                hone.put("key", lb + "_zht_hb");
//                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_上期");
//                heads.add(hone);
//
//                hone = new JSONObject();
//                hone.put("key", lb + "_zht_hb_p");
//                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_环比(%)");
//                heads.add(hone);
//            } else if (searchWD == 2)//常量
//            {
//                String start_time_t = tb_start_time + " 00:00:00";
//                String end_time_t = tb_end_time + " 23:59:59";
//
//                String start_time_h = "";
//                String end_time_h = "";
//                try {
//
//                    end_time_h = RIUtil.GetNextDate(start_time, -30);
//                    end_time_h = RIUtil.getLMonthEnd(end_time_h);
//
//                    start_time_h = RIUtil.GetNextDate(end_time_h, -40).substring(0, 7) + "-01 00:00:00";
//                } catch (Exception e) {
//                    // TODO: handle exception
//                }
//                uq =
//                        uq + " and ((cjsj01>='" + start_time_t + "' and cjsj01<='" + end_time_t + "') or
//                        (cjsj01>='" + start_time_h + "' and cjsj01<='" + end_time_h + "')) ";
//
//                hone = new JSONObject();
//                hone.put("key", lb + "_zht_cl_p");
//                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_常量");
//                heads.add(hone);
//
//                hone = new JSONObject();
//                hone.put("key", lb + "_zht_cl_xb_p");
//                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_相比较(%)");
//                heads.add(hone);
//
//                hone = new JSONObject();
//                hone.put("key", lb + "_zht_cl_zj_p");
//                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_相差");
//                heads.add(hone);
//
//                results = GetResults_cl(results, group, uq, lb, "_cl", cby, "_zht", unit, is_check, is_mark);
//
//            } else if (searchWD == 3) // 同比
//            {
//                String start_time_t = tb_start_time + " 00:00:00";
//                String end_time_t = tb_end_time + " 23:59:59";
//                results = GetResults(results, group, uq, start_time_t, end_time_t, lb, "_tb", cby, "_zht", unit,
//                is_check, is_mark);
//
//                hone = new JSONObject();
//                hone.put("key", lb + "_zht_tb");
//                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同期");
//                heads.add(hone);
//
//                hone = new JSONObject();
//                hone.put("key", lb + "_zht_tb_p");
//                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_同比(%)");
//                heads.add(hone);
//            } else if (searchWD == 4) // 环比
//            {
//                String start_time_h = "";
//                String end_time_h = "";
//                try {
//                    start_time_h = hb_start_time + " 00:00:00";
//                    end_time_h = hb_end_time + " 23:59:59";
//                } catch (Exception e) {
//                    // TODO: handle exception
//                }
//                results = GetResults(results, group, uq, start_time_h, end_time_h, lb, "_hb", cby, "_zht", unit,
//                is_check, is_mark);
//
//                hone = new JSONObject();
//                hone.put("key", lb + "_zht_hb");
//                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_上期");
//                heads.add(hone);
//
//                hone = new JSONObject();
//                hone.put("key", lb + "_zht_hb_p");
//                hone.put("value", RIUtil.dicts.get(lb).getString("dict_name") + "_环比(%)");
//                heads.add(hone);
//            }
//
//        }
//
//        //logger.warn(heads.toString());
//        //logger.warn(results.toString());
//        List<JSONObject> res = new ArrayList<>();
//        for (Map.Entry<String, JSONObject> dd : results.entrySet()) {
//            String code = dd.getKey();
//            try {
//                JSONObject one = new JSONObject();
//                one.put("code", code);
//                one.put("name", RIUtil.dicts.get(code).getString("dict_name"));
//                one.put("index_no", RIUtil.dicts.get(code).getInteger("index_no"));
//                one.putAll(dd.getValue());
//                res.add(one);
//            } catch (Exception ex) {
//
//            }
//        }
//
//        Collections.sort(res, (JSONObject o1, JSONObject o2) -> {
//            //转成JSON对象中保存的值类型
//            int a = 0;
//            int b = 0;
//
//            try {
//                a = o1.getInteger("index_no");
//                b = o2.getInteger("index_no");
//            } catch (Exception ex) {
//
//            }
//
//            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
//            if (a > b) {  //降序排列，升序改成a>b
//                return 1;
//            } else if (a == b) {
//                return 0;
//            } else {
//                return -1;
//            }
//        });
//        logger.warn(res.toString());
//        ret.put("head", heads);
//        ret.put("body", res);
//        return ret;
//    }

    private static String ExceptionFile(JSONArray heads, JSONArray res) {
        String FileName = "警情统计_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        for (int i = 0; i < heads.size(); i++) {
            JSONObject hone = heads.getJSONObject(i);
            String key = hone.getString("key");
            String value = hone.getString("value");

            header.add(key);
            headername.put(key, value);
        }


        ExportInterface exporthelper = null;

        try {
            String filePath =
        new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) +
                     "/";

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
            exporthelper.write_data_num(res);

            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql = "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath +
                "','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");
                try {
                    if (exporthelper != null) {
                        exporthelper.close();
                        exporthelper = null;
                    }
                } catch (Exception ex) {

                }
                //返回拼接后的url,id
                String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;

                logger.warn(endPath + "-->" + String.valueOf(new File(endPath).length()));
                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
                logger.warn(obsFileName + "-->" + ret);
                logger.warn("id->" + id);
                return String.valueOf(id);
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return "0";
        } finally {


            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }

        }


    }

    private static HashMap<String, JSONObject> GetResults_zht(HashMap<String, JSONObject> results, String lbSql,
     String start_time, String end_time, String lb, String type, String zl, int is_check, int is_mark,
 String region_name, String bjfs, String unit) {

        DecimalFormat df = new DecimalFormat("#0.0");
        String bjSql = "";

        if (is_mark == 1) {
            lbSql += " and SFGLDZ_PDBZ = 1  ";
        }
        if (is_check == 1) {
            lbSql += " and HCZT = 1  ";
        }
        if (bjfs != null && bjfs.length() > 0) {
            lbSql += " and bjfs in (" + bjfs + ") ";
        }

        if (region_name != null && region_name.length() > 0) {
            bjSql = " and (DMMC like '%" + region_name + "%' or PYSX like '" + region_name + "') ";
        }


        String sql =
         "select LAT, LNG, TYPE52, CJLB, JJBH from HL.DSJ_JQ  where 1=1" + lbSql + " and cjsj01>='" + start_time +
         "'" + " and cjsj01<='" + end_time + "' ";
        logger.warn(sql);
        OracleHelper ora_hl = null;

        //只筛选符合组织机构的综合体
        String unitType = RIUtil.dicts.get(unit).getString("type");
        if (unitType.equals("23") || unitType.equals("24") || unitType.equals("28")) {
            bjSql += bjSql + " and fj='" + unit.substring(0, 6) + "000000' ";
        } else if (unitType.equals("25") || unitType.equals("26")) {
            bjSql += bjSql + " and pcs='" + unit.substring(0, 8) + "0000' ";
        }

        try {
            ora_hl = new OracleHelper("ora_hl");
            List<JSONObject> list = ora_hl.query(sql);

            sql = "select BJ, DMMC， DMDM from HL.ADDRESS_DM where ISDELETE = 0 and DMLX = '09' " + bjSql;
            logger.warn(sql);
            List<JSONObject> rangeList = ora_hl.query(sql);

            List<JSONObject> list1 = new ArrayList<>();
            for (JSONObject jsonObject : rangeList) {
                String range = jsonObject.getString("BJ");
                String DMMC = jsonObject.getString("DMMC");
                String DMDM = jsonObject.getString("DMDM");

                //处理经纬度点
                List<String> regionList = Arrays.asList(range.split(";"));
                String newRange = "";
                for (String s : regionList) {
                    List<String> pointList = Arrays.asList(s.split(","));
                    for (int i = 0; i < pointList.size(); i++) {
                        String point = pointList.get(i);
                        if (i % 2 == 0) {
                            newRange += point + ",";
                        } else {
                            newRange += point + ";";
                        }
                    }
                }

                range = newRange;

                List<JSONObject> list2 = RelaInfo_null(list, range);

                JSONObject body = new JSONObject();

                //把警情编号拼起来
                String JQS = "";
                for (JSONObject jsonObject1 : list2) {
                    JQS += jsonObject1.getString("JJBH") + ",";
                }

                //logger.warn(list.toString());
                if (JQS.length() > 0) {
                    JQS = JQS.substring(0, JQS.length() - 1);
                }
                String jqlb = lb + "_JQS" + type;
                body.put(jqlb, JQS);

                if (list2.size() > 0) {
                    body.put("COUNT", list2.size());
                } else {
                    body.put("COUNT", 0);
                }
                body.put("DMMC", DMMC);
                body.put("DMDM", DMDM);

                list1.add(body);
            }


            for (int a = 0; a < list1.size(); a++) {
                JSONObject one = list1.get(a);

                String code = one.getString("DMDM") + "," + one.getString("DMMC");
                JSONObject det = new JSONObject();

                if (results.containsKey(code)) {
                    det = results.get(code);

                }

                det.put(lb + "_JQS" + type, one.getString(lb + "_JQS" + type));
                det.put(lb + zl + type, one.getString("COUNT"));

                double zc = 0;
                try {
                    zc = det.getIntValue(lb + zl);
                } catch (Exception e) {
                    // TODO: handle exception
                }

                if (type.contains("tb")) {

                    double tb = 0;

                    try {
                        tb = det.getIntValue(lb + zl + "_tb");
                    } catch (Exception e) {
                        // TODO: handle exception
                    }

                    double bt = zc - tb;
                    double tbp = 0;

                    if (tb != 0) {
                        tbp = bt / tb * 100;
                    }
                    logger.warn("tbp->" + tbp);
                    logger.warn("bt->" + bt);
                    logger.warn("tb->" + tb);
                    det.put(lb + zl + "_tb_p", df.format(tbp));
                }
                if (type.contains("hb")) {

                    double hb = 0;

                    try {
                        hb = det.getIntValue(lb + zl + "_hb");
                    } catch (Exception e) {
                        // TODO: handle exception
                    }

                    double bt = zc - hb;
                    double hbp = 0;

                    if (hb != 0) {
                        hbp = bt / hb * 100;
                    }
                    det.put(lb + zl + "_hb_p", df.format(hbp));
                }

                results.put(code, det);

            }

        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
        } finally {
            ora_hl.close();
        }

        return results;
    }

    private static HashMap<String, JSONObject> GetResults(HashMap<String, JSONObject> results, String group,
     String lbSql, String start_time, String end_time, String lb, String type, String cyb, String zl, String unit,
 int is_check, int is_mark, String bjfs) {

        DecimalFormat df = new DecimalFormat("#0.0");

        if (is_mark == 1) {
            lbSql += " and SFGLDZ_PDBZ = 1  ";
        }
        if (is_check == 1) {
            lbSql += " and HCZT = 1  ";
        }
        if (bjfs != null && bjfs.length() > 0) {
            lbSql += " and bjfs in (" + bjfs + ") ";
        }
        // logger.warn(lbSql);

        String sql = "";
        sql = "select " + group + " from HL.DSJ_JQ where 1=1" + lbSql + " " + "and cjsj01>='" + start_time + "' " +
        "and" + " " + "cjsj01<='" + end_time + "' ";

        // logger.warn(sql);
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            List<JSONObject> list = ora_hl.query(sql);

            list = GetGroupByList(list, group, unit);


            for (int a = 0; a < list.size(); a++) {
                JSONObject one = list.get(a);

                String code = one.getString(group);
                JSONObject det = new JSONObject();

                if (results.containsKey(code)) {
                    det = results.get(code);

                }
                det.put(lb + zl + type, one.getString("COUNT"));

                double zc = 0;
                try {
                    zc = det.getIntValue(lb + zl);
                } catch (Exception e) {
                    // TODO: handle exception
                }

                if (type.contains("tb")) {

                    double tb = 0;

                    try {
                        tb = det.getIntValue(lb + zl + "_tb");
                    } catch (Exception e) {
                        // TODO: handle exception
                    }

                    double bt = zc - tb;
                    logger.warn(zc + "-" + tb + "=" + bt);
                    double tbp = 0;
                    if (bt == 0 && tb == 0) {
                        tbp = 0;
                    } else if (zc == 0 && tb > 0) {
                        tbp = tb * -100;
                    } else if (tb == 0 && zc > 0) {
                        tbp = zc * 100;
                    } else if (bt == 0) {

                        tbp = 0;
                        //logger.warn(String.valueOf(hbp));
                    } else {


                        tbp = bt / tb * 100;

                    }
                    det.put(lb + zl + "_tb_p", df.format(tbp));
                }
                if (type.contains("hb")) {

                    double hb = 0;

                    try {
                        hb = det.getIntValue(lb + zl + "_hb");
                    } catch (Exception e) {
                        // TODO: handle exception
                    }


                    double bt = zc - hb;

                    double hbp = 0;
                    if (bt == 0 && hb == 0) {
                        hbp = 0;
                    } else if (zc == 0 && hb > 0) {
                        hbp = hb * -100;
                    } else if (hb == 0 && zc > 0) {
                        hbp = zc * 100;
                    } else if (bt == 0) {

                        hbp = 0;
                        //logger.warn(String.valueOf(hbp));
                    } else {


                        hbp = bt / hb * 100;

                    }

                    det.put(lb + zl + "_hb_p", df.format(hbp));
                }

                results.put(code, det);

            }

        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
        } finally {
            ora_hl.close();
        }

        return results;
    }

    private static HashMap<String, JSONObject> GetResults_hz(HashMap<String, JSONObject> results, String group,
     String lbSql, String start_time, String end_time, String bq, String type, String cyb, String zl, String unit,
int is_check, int is_mark, String bjfs, String lb) {

        DecimalFormat df = new DecimalFormat("#0.0");

        if (is_mark == 1) {
            lbSql += " and SFGLDZ_PDBZ = 1  ";
        }
        if (is_check == 1) {
            lbSql += " and HCZT = 1  ";
        }
        if (bjfs != null && bjfs.length() > 0) {
            lbSql += " and bjfs in (" + bjfs + ") ";
        }

        String sql = "";

        sql =
         "select " + group + " from V_HZ_FW1 " + "where cjsj01 > '" + start_time + "' and cjsj01 < '" + end_time +
         "'" + " " + lbSql;

        logger.warn(sql);
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            List<JSONObject> list = ora_hl.query(sql);

            list = GetGroupByList(list, group, unit);


            for (int a = 0; a < list.size(); a++) {
                JSONObject one = list.get(a);

                String code = one.getString(group);
                JSONObject det = new JSONObject();

                if (results.containsKey(code)) {
                    det = results.get(code);
                }
                det.put(lb + zl + type, one.getString("COUNT"));

                double zc = 0;
                try {

                    zc = det.getIntValue(lb + zl);
                } catch (Exception e) {
                    // TODO: handle exception
                }


                if (type.contains("tb")) {

                    double tb = 0;

                    try {
                        tb = det.getIntValue(lb + zl + "_tb");
                    } catch (Exception e) {
                        // TODO: handle exception
                    }

                    double bt = zc - tb;
                    double tbp = bt / tb * 100;

                    logger.warn("zc -> " + zc + " tb -> " + tb + " bt -> " + bt + " tbp -> " + tbp + "");

                    det.put(lb + zl + "_tb_p", df.format(tbp));

                }
                if (type.contains("hb")) {

                    double hb = 0;

                    try {
                        hb = det.getIntValue(lb + zl + "_hb");
                    } catch (Exception e) {
                        // TODO: handle exception
                    }

                    double bt = zc - hb;
                    double hbp = bt / hb * 100;

                    det.put(lb + zl + "_hb_p", df.format(hbp));
                    logger.warn(lb + "计算环比 -> " + df.format(hbp));
                }
                results.put(code, det);

            }

        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
        } finally {
            ora_hl.close();
        }

        return results;
    }

    private static List<JSONObject> GetGroupByList(List<JSONObject> list, String group, String unit) {
        InfoModelHelper mysql = null;
        List<JSONObject> back = new ArrayList<>();
        String sql = "";
        String sub_sql = "";
        try {
            mysql = InfoModelPool.getModel();
            HashMap<String, Integer> ret = new HashMap<>();
            // String father_id = unit;

            JSONObject done = RIUtil.dicts.get(unit);
            String type = RIUtil.dicts.get(unit).getString("type");

            if (type.equals("25") || type.equals("26")) {
                sub_sql += " and (id like '%" + unit.substring(0, 8) + "%') ";
            } else if (type.equals("23")) {
                sub_sql += " and (id like '%" + unit.substring(0, 6) + "%') ";
            } else if (type.equals("22")) {
                sub_sql += " and (id like '%" + unit.substring(0, 4) + "%') ";
            } else if (type.equals("24")) {
                String father_id = done.getString("father_id");
                sub_sql = " and id like '" + father_id.substring(0, 6) + "%' ";
            } else if (type.equals("28")) {
                unit = unit.substring(0, 6);
                sub_sql = " and id like '" + unit + "%' ";
            }

            if (group.equals("PCS") || group.equals("ZRQ")) {

                if (group.equals("PCS")) {
                    sql = "select remark, id from dict where type = '25' and isdelete = 1  and is_kh = '1' " + sub_sql;
                } else {
                    sql = "select remark, id from dict where type = '26' and isdelete = 1 and is_kh = '1' " + sub_sql;
                }
                // logger.warn(sql);
                //所有单位
                List<JSONObject> groupList = mysql.query(sql);
                //有警情的单位
                List<String> codeList = list.stream().map(o -> o.getString(group)).collect(Collectors.toList());
                for (JSONObject js : list) {
                    String pcs = js.getString("PCS");

                }
                for (JSONObject record : groupList) {
                    String code = record.getString("id");
                    if (!codeList.contains(code)) {
                        JSONObject one = new JSONObject();
                        one.put(group, code);
                        one.put("COUNT", 0);
                        back.add(one);
                    }
                }
            }


            if (list.size() > 0) {

                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String code = one.getString(group);
                    int count = 0;
                    if (ret.containsKey(code)) {
                        count = ret.get(code);

                    }
                    count++;
                    if (code.equals("")) {
                        //logger.warn(code + "," + count);
                    }
                    ret.put(code, count);


                }


                for (Map.Entry<String, Integer> det : ret.entrySet()) {
                    String code = det.getKey();
                    int count = det.getValue();

                    JSONObject one = new JSONObject();
                    one.put(group, code);
                    one.put("COUNT", count);
                    back.add(one);
                }


                JSONObject one = new JSONObject();
                one.put(group, unit);
                one.put("COUNT", list.size());
                back.add(one);

                //去除支队等
                if (group.equals("PCS")) {
                    Iterator<JSONObject> it = back.iterator();
                    while (it.hasNext()) {
                        JSONObject js = it.next();

                        String code = js.getString(group);
                        JSONObject dictJs = RIUtil.dicts.get(code);
                        if (dictJs != null) {
                            String groupType = dictJs.getString("type");
                            if (!groupType.equals("25") && !groupType.equals("23")) {
                                //System.out.println(js);
                                it.remove();
                            }
                        }
                    }
                }


            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static HashMap<String, JSONObject> GetResults_cl_hz(HashMap<String, JSONObject> results, String group,
     String lbSql, String type, String cyb, String zl, String unit, int is_check, int is_mark, String lb) {

        DecimalFormat df = new DecimalFormat("#0.0");

        if (is_mark == 1) {
            lbSql += " and SFGLDZ_PDBZ = 1  ";
        }
        if (is_check == 1) {
            lbSql += " and HCZT = 1  ";
        }

        String sql = "";

        sql = "select " + group + " from V_HZ_FW1 where 1=1  " + lbSql;

        logger.warn(sql);
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            List<JSONObject> list = ora_hl.query(sql);
            list = GetGroupByList(list, group, unit);


            for (int a = 0; a < list.size(); a++) {
                JSONObject one = list.get(a);

                String code = one.getString(group);
                JSONObject det = new JSONObject();

                if (results.containsKey(code)) {
                    det = results.get(code);

                }


                double cmonth = 0;
                // double cl = 0;
                try {
                    cmonth = det.getIntValue(lb + zl);
                    logger.warn("det -> " + det);
                    logger.warn("cmonth -> " + cmonth);

                } catch (Exception e) {
                    // TODO: handle exception
                }


                int cll = 0;
                try {
                    cll = one.getInteger("COUNT") / 3;
                } catch (Exception e) {
                    // TODO: handle exception
                }

                double zj = cmonth - cll;
                double xb = zj / cll * 100;
                logger.warn("cmonth -> " + cmonth + " cll -> " + cll + " zj -> " + zj + " xb -> " + xb);
                det.put(lb + zl + type + "_p", cll);
                det.put(lb + zl + type + "_zj_p", zj);
                det.put(lb + zl + type + "_xb_p", df.format(xb));


                results.put(code, det);

            }

        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
        } finally {
            ora_hl.close();
        }

        return results;
    }

    private static HashMap<String, JSONObject> GetResults_cl(HashMap<String, JSONObject> results, String group,
String lbSql, String lb, String type, String cyb, String zl, String unit, int is_check, int is_mark) {

        DecimalFormat df = new DecimalFormat("#0.0");

        if (is_mark == 1) {
            lbSql += " and SFGLDZ_PDBZ = 1  ";
        }
        if (is_check == 1) {
            lbSql += " and HCZT = 1  ";
        }

        String sql = "select " + group + " from HL.DSJ_JQ where 1=1" + lbSql + " ";
        // logger.warn(sql);
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            List<JSONObject> list = ora_hl.query(sql);
            list = GetGroupByList(list, group, unit);


            for (int a = 0; a < list.size(); a++) {
                JSONObject one = list.get(a);

                String code = one.getString(group);
                JSONObject det = new JSONObject();

                if (results.containsKey(code)) {
                    det = results.get(code);

                }


                double cmonth = 0;
                // double cl = 0;
                try {
                    cmonth = det.getIntValue(lb + zl);

                } catch (Exception e) {
                    // TODO: handle exception
                }


                int cll = 0;
                try {
                    cll = one.getInteger("COUNT") / 3;
                } catch (Exception e) {
                    // TODO: handle exception
                }

                double zj = cmonth - cll;
                double xb = zj / cll * 100;
                if (cmonth == 0) {
                    xb = 0;
                }
                det.put(lb + zl + type + "_p", cll);
                det.put(lb + zl + type + "_zj_p", zj);
                det.put(lb + zl + type + "_xb_p", df.format(xb));


                results.put(code, det);

            }

        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
        } finally {
            ora_hl.close();
        }

        return results;
    }

    private static HashMap<String, JSONObject> GetResults_jz(HashMap<String, JSONObject> results, String lbSql,
 String start_time, String end_time, String lb, String type, String zl, String bjfs, int is_check, int is_mark) {

        DecimalFormat df = new DecimalFormat("#0.0");

        if (is_mark == 1) {
            lbSql += " and a.SFGLDZ_PDBZ = 1  ";
        }
        if (is_check == 1) {
            lbSql += " and a.HCZT = 1  ";
        }
        if (bjfs != null && bjfs.length() > 0) {
            lbSql += " and a.bjfs in (" + bjfs + ") ";
        }

        if (bjfs != null && bjfs.length() > 0) {
            lbSql += " and bjfs in (" + bjfs + ") ";
        }
        String sql =
        "select count(jjbh) as count,XQ,DMMC,b.PCS as PCS from HL.DSJ_JQ a left join address_dm b on " + "a" + ".xq" + "=b" + ".dmdm" + " " + " where 1=1" + lbSql + " and cjsj01>='" + start_time + "' and " + "cjsj01<='" + end_time + "' " + "group " + "by XQ,DMMC,b.PCS";
        logger.warn(sql);
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            List<JSONObject> list = ora_hl.query(sql);


            for (int a = 0; a < list.size(); a++) {
                JSONObject one = list.get(a);

                String code = one.getString("XQ") + "," + one.getString("DMMC");
                JSONObject det = new JSONObject();

                String pcsCode = one.getString("PCS");
                List<String> pcsCodeList = Arrays.asList(pcsCode.split("\\|"));
                List<String> pcsList = new ArrayList<>();


                for (String pcsCodes : pcsCodeList) {
                    JSONObject pcsJson = RIUtil.dicts.get(pcsCodes);
                    if (pcsJson != null && pcsJson.containsKey("dict_name")) {
                        String pcsName = pcsJson.getString("dict_name");
                        pcsList.add(pcsName);
                    }
                }

                det.put("pcs", pcsList);

                if (results.containsKey(code)) {
                    det = results.get(code);

                }
                det.put(lb + zl + type, one.getString("COUNT"));

                double zc = 0;
                try {
                    zc = det.getIntValue(lb + zl);
                } catch (Exception e) {
                    // TODO: handle exception
                }

                if (type.contains("tb")) {

                    double tb = 0;

                    try {
                        tb = det.getIntValue(lb + zl + "_tb");
                    } catch (Exception e) {
                        // TODO: handle exception
                    }

                    double bt = zc - tb;
                    double tbp = bt / tb * 100;

                    det.put(lb + zl + "_tb_p", df.format(tbp));
                }
                if (type.contains("hb")) {

                    double hb = 0;

                    try {
                        hb = det.getIntValue(lb + zl + "_hb");
                    } catch (Exception e) {
                        // TODO: handle exception
                    }

                    double bt = zc - hb;
                    double hbp = bt / hb * 100;

                    det.put(lb + zl + "_hb_p", df.format(hbp));
                }

                results.put(code, det);

            }

        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
        } finally {
            ora_hl.close();
        }

        return results;
    }

    private static HashMap<String, JSONObject> GetResults_zht_cl(HashMap<String, JSONObject> results, String lbSql,
     String lb, String type, String zl, int is_check, int is_mark, String region_name, String bjfs, String start_time
        , String end_time, String unit) {

        DecimalFormat df = new DecimalFormat("#0.0");
        String bjSql = "";

        if (is_mark == 1) {
            lbSql += " and SFGLDZ_PDBZ = 1  ";
        }
        if (is_check == 1) {
            lbSql += " and HCZT = 1  ";
        }
        if (bjfs != null && bjfs.length() > 0) {
            lbSql += " and bjfs in (" + bjfs + ") ";
        }

        if (region_name != null && region_name.length() > 0) {
            bjSql = " and DMMC like '%" + region_name + "%' ";
        }

        String sql =
         "select LAT, LNG, TYPE52, CJLB, JJBH from HL.DSJ_JQ  where 1=1" + lbSql + " and cjsj01>='" + start_time +
         "'" + " and cjsj01<='" + end_time + "' ";
        logger.warn(sql);
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            List<JSONObject> list = ora_hl.query(sql);

            sql = "select BJ, DMMC， DMDM from HL.ADDRESS_DM where ISDELETE = 0 and DMLX = '09' " + bjSql;
            List<JSONObject> rangeList = ora_hl.query(sql);

            List<JSONObject> list1 = new ArrayList<>();
            for (JSONObject jsonObject : rangeList) {
                String range = jsonObject.getString("BJ");
                String DMMC = jsonObject.getString("DMMC");
                String DMDM = jsonObject.getString("DMDM");
                logger.warn(range);

                //处理经纬度点
                List<String> regionList = Arrays.asList(range.split(";"));
                String newRange = "";
                for (String s : regionList) {
                    List<String> pointList = Arrays.asList(s.split(","));
                    for (int i = 0; i < pointList.size(); i++) {
                        String point = pointList.get(i);
                        if (i % 2 == 0) {
                            newRange += point + ",";
                        } else {
                            newRange += point + ";";
                        }
                    }
                }

                logger.warn(newRange);
                range = newRange;

                List<JSONObject> list2 = RelaInfo_null(list, range);
                JSONObject body = new JSONObject();

                //把警情编号拼起来
                String JQS = "";
                for (JSONObject jsonObject1 : list2) {
                    JQS += jsonObject1.getString("JJBH") + ",";
                }
                if (JQS.length() > 0) {
                    JQS = JQS.substring(0, JQS.length() - 1);
                }
                String jqlb = lb + "_JQS" + type;
                body.put(jqlb, JQS);

                logger.warn("list2 ->" + list2);

                if (list2.size() > 0) {
                    body.put("COUNT", list2.size());
                } else {
                    body.put("COUNT", 0);
                }
                body.put("DMMC", DMMC);
                body.put("DMDM", DMDM);

                list1.add(body);
            }


            for (int a = 0; a < list.size(); a++) {
                JSONObject one = list.get(a);

                String code = one.getString("XQ") + "," + one.getString("DMMC");
                JSONObject det = new JSONObject();
                if (results.containsKey(code)) {
                    det = results.get(code);

                }


                double cmonth = 0;
                // double cl = 0;
                try {
                    cmonth = det.getIntValue(lb + zl);

                } catch (Exception e) {
                    // TODO: handle exception
                }


                int cll = 0;
                try {
                    cll = one.getInteger("COUNT") / 3;
                } catch (Exception e) {
                    // TODO: handle exception
                }

                double zj = cmonth - cll;
                double xb = zj / cll * 100;
                det.put(lb + zl + type + "_p", cll);
                det.put(lb + zl + type + "_zj_p", zj);
                det.put(lb + zl + type + "_xb_p", df.format(xb));


                results.put(code, det);
            }

        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
        } finally {
            ora_hl.close();
        }

        return results;
    }

    private static HashMap<String, JSONObject> GetResults_jz_cl(HashMap<String, JSONObject> results, String lbSql,
        String lb, String type, String zl) {

        DecimalFormat df = new DecimalFormat("#0.0");
        String sql = "select count(jjbh) as count,XQ,DMMC from HL.DSJ_JQ a left join address_dm b on " + "a.xq=b" +
        ".dmdm" + " " + " where 1=1" + lbSql + "  group " + "by XQ,DMMC";
        logger.warn(sql);
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            List<JSONObject> list = ora_hl.query(sql);


            for (int a = 0; a < list.size(); a++) {
                JSONObject one = list.get(a);

                String code = one.getString("XQ") + "," + one.getString("DMMC");
                JSONObject det = new JSONObject();
                if (results.containsKey(code)) {
                    det = results.get(code);

                }


                double cmonth = 0;
                // double cl = 0;
                try {
                    cmonth = det.getIntValue(lb + zl);

                } catch (Exception e) {
                    // TODO: handle exception
                }


                int cll = 0;
                try {
                    cll = one.getInteger("COUNT") / 3;
                } catch (Exception e) {
                    // TODO: handle exception
                }

                double zj = cmonth - cll;
                double xb = zj / cll * 100;
                det.put(lb + zl + type + "_p", cll);
                det.put(lb + zl + type + "_zj_p", zj);
                det.put(lb + zl + type + "_xb_p", df.format(xb));


                results.put(code, det);
            }

        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
        } finally {
            ora_hl.close();
        }

        return results;
    }

    private static String GetTBTime2(String start_time) {
        // logger.warn(start_time);
        String year = start_time.split("-")[0];
        int y = Integer.parseInt(year);

        y = y - 1;

        String date = y + "-" + start_time.split("-")[1] + "-" + start_time.split("-")[2];

        // logger.warn(date);
        return date;
    }

    private JSONObject exportTemplate2(JSONObject data) {

        String sql = "";
        FileOutputStream fos = null;
        SXSSFWorkbook sxssfWorkbook = null;
        InfoModelHelper info = null;
        try {
            info = InfoModelPool.getModel();
            sxssfWorkbook = new SXSSFWorkbook();

            CellStyle cellStyle = initCellStyle(sxssfWorkbook);

            JSONArray unMarkList = data.getJSONArray("unMarkList");
            JSONArray relaError = data.getJSONArray("relaError");
            final int treeLength1 = getTreeLength(unMarkList);
            final int treeLength2 = getTreeLength(relaError);


            infoSheet(sxssfWorkbook, unMarkList, "七日内未标注", treeLength1, cellStyle);
            infoSheet1(sxssfWorkbook, relaError, "关联错误七日内未整改", treeLength2, cellStyle);

            String FileName = "七日内未标注及关联错误七日内未整改_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) +
            ".xlsx";
            String filePath =
             new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) +
                     "/";
            String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;
            init(endPath);

            fos = new FileOutputStream(endPath);
            sxssfWorkbook.write(fos);

            String sqls =
             "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "'," + "'" + FileName + "',999)";
            logger.warn(sqls);
            info.update(sqls);
            sql = "select * from upload where file_name='" + FileName + "'";
            List<JSONObject> result = info.query(sql);
            JSONObject xJsonObject = result.get(0);
            int id = xJsonObject.getIntValue("id");

            logger.warn("id->" + id);

            try {
                if (sxssfWorkbook != null) {
                    sxssfWorkbook.dispose();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            JSONObject back = new JSONObject();
            back.put("id", id);
            // String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;
            String endPoint = "http://10.34.251.34:50101";
            String ak = "Q7D3OUVIRBEZEB4RWVSJ";
            String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
            String bucketName = "obs-qjjc-tyyh";
            ObsServer obsServ = new ObsServer();
            String obsFileName = "hl/" + filePath + FileName;
            System.out.println(obsFileName);
            boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
            logger.warn(obsFileName + "-->" + ret);
            logger.warn("id->" + id);
            return back;

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(info);
            try {
                if (sxssfWorkbook != null) {
                    sxssfWorkbook.dispose();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private static CellStyle initCellStyle(SXSSFWorkbook sxssfWorkbook) {
        //************** 样式一 *******************//
        CellStyle cellStyle = sxssfWorkbook.createCellStyle();
        cellStyle.setWrapText(true);
        //对齐方式
        //设置水平对齐方式
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        //设置垂直对齐方式
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //设置边框
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        // 设置字体
        Font font = sxssfWorkbook.createFont();
        font.setFontName("Segoe UI");
        font.setFontHeightInPoints((short) 9);
        font.setBold(true);
        cellStyle.setFont(font);
        //************** 样式一 *******************//
        return cellStyle;
    }

    private static void infoSheet(SXSSFWorkbook sxssfWorkbook, JSONArray arr, String name, int treeLength,
     CellStyle cellStyle) {
        // 获取SXSSFWorkbook实例
        Sheet sheet = sxssfWorkbook.createSheet(name);

        Row head = sheet.createRow(0);

        Cell cell = head.createCell(treeLength);
        cell.setCellValue("警情编号");
        cell.setCellStyle(cellStyle);
        Cell cell2 = head.createCell(treeLength + 1);
        cell2.setCellValue("处警单位");
        cell2.setCellStyle(cellStyle);
        Cell cell3 = head.createCell(treeLength + 2);
        cell3.setCellValue("推送时间");
        cell3.setCellStyle(cellStyle);
        Cell cell4 = head.createCell(treeLength + 3);
        cell4.setCellValue("已超期时间");
        cell4.setCellStyle(cellStyle);


        int flag = 1;
        for (int i = 0; i < arr.size(); i++) {
            JSONObject obj = arr.getJSONObject(i);
            int r = fillTreeData(sheet, obj, flag, 0, cellStyle);
            flag += r;
        }
    }

    private static void infoSheet1(SXSSFWorkbook sxssfWorkbook, JSONArray arr, String name, int treeLength,
     CellStyle cellStyle) {
        // 获取SXSSFWorkbook实例
        Sheet sheet = sxssfWorkbook.createSheet(name);

        Row head = sheet.createRow(0);

        Cell cell = head.createCell(treeLength);
        cell.setCellValue("警情编号");
        cell.setCellStyle(cellStyle);
        Cell cell2 = head.createCell(treeLength + 1);
        cell2.setCellValue("处警单位");
        cell2.setCellStyle(cellStyle);
        Cell cell3 = head.createCell(treeLength + 2);
        cell3.setCellValue("推送时间（最新一次审核不通过时间）");
        cell3.setCellStyle(cellStyle);
        Cell cell4 = head.createCell(treeLength + 3);
        cell4.setCellValue("已超期时间");
        cell4.setCellStyle(cellStyle);
        Cell cell5 = head.createCell(treeLength + 4);
        cell5.setCellValue("整改次数");
        cell5.setCellStyle(cellStyle);

        int flag = 1;
        for (int i = 0; i < arr.size(); i++) {
            JSONObject obj = arr.getJSONObject(i);
            int r = fillTreeData1(sheet, obj, flag, 0, cellStyle);
            flag += r;
        }
    }

    private static int fillTreeData(Sheet sheet, JSONObject object, int rowStart, int colStart, CellStyle cellStyle) {
        String id = object.getString("id");

        // 获取开始行，如果不存在则创建一行
        Row row = sheet.getRow(rowStart) == null ? sheet.createRow(rowStart) : sheet.getRow(rowStart);

//        // 在开始列创建一个单元格
//        Cell cell = row.createCell(colStart);
//
//        // 设置单元格的值为节点名称
//        cell.setCellValue(object.getString("kh_name"));
//        cell.setCellStyle(cellStyle);
//        // 检查节点是否有子节点


        sheet.setColumnWidth(0, 5000);
        sheet.setColumnWidth(1, 12000);

        Cell cell2 = row.createCell(colStart);
        cell2.setCellValue(object.getString("pg_object"));
        cell2.setCellStyle(cellStyle);
        Cell cell3 = row.createCell(colStart + 1);
        cell3.setCellValue(object.getString("remark"));
        cell3.setCellStyle(cellStyle);
        Cell cell4 = row.createCell(colStart + 2);
        cell4.setCellValue(object.getDoubleValue("score"));
        cell4.setCellStyle(cellStyle);
        Cell cell5 = row.createCell(colStart + 3);
        cell5.setCellValue(object.getIntValue("rank"));
        cell5.setCellStyle(cellStyle);
        Cell cell6 = row.createCell(colStart + 4);
        cell6.setCellValue(object.getIntValue("rank_fj"));
        cell6.setCellStyle(cellStyle);


        Cell cell7 = row.createCell(colStart + 5);
        cell7.setCellValue(object.getIntValue("rank_pcs"));
        cell7.setCellStyle(cellStyle);
        Cell cell10 = row.createCell(colStart + 6);
        cell10.setCellValue(object.getIntValue("rank6"));
        cell10.setCellStyle(cellStyle);
        Cell cell8 = row.createCell(colStart + 7);
        cell8.setCellValue(object.getIntValue("rank_fj7"));
        cell8.setCellStyle(cellStyle);
        Cell cell9 = row.createCell(colStart + 8);
        cell9.setCellValue(object.getIntValue("rank_pcs8"));
        cell9.setCellStyle(cellStyle);


        // 如果没有子节点，那么这个节点只占用一行
        return 1;
    }

    private static int fillTreeData1(Sheet sheet, JSONObject object, int rowStart, int colStart, CellStyle cellStyle) {
        String id = object.getString("id");

        // 获取开始行，如果不存在则创建一行
        Row row = sheet.getRow(rowStart) == null ? sheet.createRow(rowStart) : sheet.getRow(rowStart);

//        // 在开始列创建一个单元格
//        Cell cell = row.createCell(colStart);
//
//        // 设置单元格的值为节点名称
//        cell.setCellValue(object.getString("kh_name"));
//        cell.setCellStyle(cellStyle);
//        // 检查节点是否有子节点


        sheet.setColumnWidth(0, 5000);
        sheet.setColumnWidth(1, 12000);

        Cell cell2 = row.createCell(colStart);
        cell2.setCellValue(object.getString("pg_object"));
        cell2.setCellStyle(cellStyle);
        Cell cell3 = row.createCell(colStart + 1);
        cell3.setCellValue(object.getString("remark"));
        cell3.setCellStyle(cellStyle);
        Cell cell4 = row.createCell(colStart + 2);
        cell4.setCellValue(object.getDoubleValue("score"));
        cell4.setCellStyle(cellStyle);
        Cell cell5 = row.createCell(colStart + 3);
        cell5.setCellValue(object.getIntValue("rank"));
        cell5.setCellStyle(cellStyle);
        Cell cell6 = row.createCell(colStart + 4);
        cell6.setCellValue(object.getIntValue("rank_fj"));
        cell6.setCellStyle(cellStyle);


        Cell cell7 = row.createCell(colStart + 5);
        cell7.setCellValue(object.getIntValue("rank_pcs"));
        cell7.setCellStyle(cellStyle);
        Cell cell10 = row.createCell(colStart + 6);
        cell10.setCellValue(object.getIntValue("rank6"));
        cell10.setCellStyle(cellStyle);
        Cell cell8 = row.createCell(colStart + 7);
        cell8.setCellValue(object.getIntValue("rank_fj7"));
        cell8.setCellStyle(cellStyle);
        Cell cell9 = row.createCell(colStart + 8);
        cell9.setCellValue(object.getIntValue("rank_pcs8"));
        cell9.setCellStyle(cellStyle);


        // 如果没有子节点，那么这个节点只占用一行
        return 1;
    }

    private static int getTreeLength(JSONArray tree) {
        int l = 0;
        int rl = 0;
        for (int i = 0; i < tree.size(); i++) {
            JSONObject o = tree.getJSONObject(i);
//            l = 1;
            rl = Math.max(rl, l);
            if (o.containsKey("next") && o.getJSONArray("next").size() > 0) {
                JSONArray next = o.getJSONArray("next");
                l = 2;
                rl = Math.max(rl, l);
                for (int j = 0; j < next.size(); j++) {
                    JSONObject two = next.getJSONObject(j);
                    if (two.containsKey("next") && two.getJSONArray("next").size() > 0) {
                        JSONArray next2 = two.getJSONArray("next");
                        l = 3;
                        rl = Math.max(rl, l);
                        for (int k = 0; k < next2.size(); k++) {
                            JSONObject three = next2.getJSONObject(k);
                            if (three.containsKey("next") && three.getJSONArray("next").size() > 0) {
                                rl = 4;
                            }
                        }
                    }
                }
            }
        }
        return rl;
    }

    private int ExportTwo(JSONArray datas, String searchType) {

        //处理数据，把处警单位和处警类别提出来
        for (int i = 0; i < datas.size(); i++) {
            JSONObject one = datas.getJSONObject(i);
            //logger.warn(one.toString());

            String CJLX = "";
            String CJDW = "";
            String SSXXQK = one.getString("SSXXQK");
            try {
                CJLX = one.getJSONObject("CJLB").getString("dict_name");
            } catch (Exception ex) {
            }
            try {
                CJDW = one.getJSONObject("CHJDW_GAJGJGDM").getString("dict_name");
            } catch (Exception ex) {

            }
            String HCZT_DM = one.getString("HCZT");
            String HCZT = "";
            switch (HCZT_DM) {
                case "1":
                    HCZT = "已核查";
                    break;
                case "2":
                    HCZT = "未核查";
                    break;
                case "3":
                    HCZT = "已核查未通过";
                    break;
                case "4":
                    HCZT = "已核查待整改";
                    break;
                default:
                    HCZT = "";
            }
            String SFGL_DM = one.getString("SFGLDZ_PDBZ");
            String SFGL = "";
            switch (SFGL_DM) {
                case "1":
                    SFGL = "已关联";
                    break;
                case "3":
                    SFGL = "七天未关联";
                    break;
                case "2":
                    SFGL = "无需关联";
                    break;
                default:
                    SFGL = "未关联";
            }

            one.put("CJDW", CJDW);
            one.put("CJLX", CJLX);
            one.put("HCZT", HCZT);
            one.put("SFGL", SFGL);
            one.put("SSXXQK", SSXXQK);
        }

        String FileName = "警情" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        header.add("JJBH");
        headername.put("JJBH", "接警编号");
        header.add("CJDW");
        headername.put("CJDW", "处警单位");
        header.add("CJSJ01");
        headername.put("CJSJ01", "处警时间");
        header.add("CJLX");
        headername.put("CJLX", "处警类型");
        header.add("CLJG");
        headername.put("CLJG", "处理结果");
        header.add("CJDZ_DZMC");
        headername.put("CJDZ_DZMC", "发生地点");
        header.add("HCZT");
        headername.put("HCZT", "核查状态");
        header.add("SFGL");
        headername.put("SFGL", "是否关联");
        if ("4".equals(searchType)) {
            header.add("SSXXQK");
            headername.put("SSXXQK", "损失情况");
        }


        ExportInterface exporthelper = null;
        String filePath = "";
        try {
            filePath =
            new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) +
                    "/";

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
//            for (int i = 0; i < datas.size(); i++) {
//                JSONObject jsonObject = datas.getJSONObject(i);
//                JSONArray jsonArray = jsonObject.getJSONArray("real_all");
            exporthelper.write_data(datas);
//            }

            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql = "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath +
"','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");

                logger.warn("id->" + id);
                if (exporthelper != null) {
                    exporthelper.close();
                    exporthelper = null;
                }
                Thread.sleep(1000);

                String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;
                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
                logger.warn(obsFileName + "-->" + ret);


                return id;
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return 0;
        } finally {

            //logger.warn("id->" + id);
            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }
        }
    }

    private int ExportTables(JSONArray datas, String head, String keys, String name) {


        String FileName = name + "_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        String[] heads = head.split(",");
        String key[] = keys.split(",");
        for (int i = 0; i < key.length; i++) {
            header.add(key[i]);
            headername.put(key[i], heads[i]);
        }


        ExportInterface exporthelper = null;

        try {
            String filePath =
            new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) +
"/";

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
//            for (int i = 0; i < datas.size(); i++) {
//                JSONObject jsonObject = datas.getJSONObject(i);
//                JSONArray jsonArray = jsonObject.getJSONArray("real_all");
            exporthelper.write_data(datas);
//            }

            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql = "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath +
                "','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");
                try {
                    if (exporthelper != null) {
                        exporthelper.close();
                        exporthelper = null;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                logger.warn("-->" + id);
                String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;
                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
                logger.warn(obsFileName + "-->" + ret);
                logger.warn("id->" + id);
                return id;
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return 0;
        } finally {

            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }
        }
    }


}
