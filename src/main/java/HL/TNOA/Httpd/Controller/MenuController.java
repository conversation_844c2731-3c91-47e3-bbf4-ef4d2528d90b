package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
public class MenuController {
    private static Logger logger = LoggerFactory.getLogger(MenuController.class);
    private static String ip = "";

    @RequestMapping(method = {RequestMethod.POST}, path = {"/menu"})
    public static JSONObject get_menu(TNOAHttpRequest request) throws Exception {

        String opt = "";
        ip = request.getRemoteAddr();
        JSONObject data = request.getRequestParams();

        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if ("menu_get".equals(opt)) {
                return getMenu(data);
            } else if ("menu_create".equals(opt)) {
                logger.warn("menu--->" + data.toString());
                return createMenu(data);
            } else if ("menu_update".equals(opt)) {
                logger.warn("menu--->" + data.toString());
                return updateMenu(data);
            } else if ("menu_delete".equals(opt)) {
                return deleteMenu(data);
            } else {
                return ErrNo.set(442009);
            }
        } else {
            return ErrNo.set(442009);
        }
    }

    //******CREATE*******
    private static JSONObject createMenu(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String dishes = "";
            String start_date = "";
            String end_date = "";
            String type = "";
            String cycle = "";
            String create_user = "";
            String unit = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "1";
            if (data.containsKey("dishes") && data.getString("dishes").length() > 0) {
                dishes = data.getString("dishes");
            } else {
                return ErrNo.set(442002);
            }
            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date");

            } else {
                return ErrNo.set(442002);
            }

            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
            } else {
                return ErrNo.set(442002);
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
            } else {
                return ErrNo.set(442002);
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(442002);
            }
            if (data.containsKey("cycle") && data.getString("cycle").length() > 0) {
                cycle = data.getString("cycle");
                if (cycle.contains("7")) {
                    cycle = cycle.replace("7", "0");
                }

            } else {
                cycle = "1,2,3,4,5,6,0";
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                return ErrNo.set(442002);
            }

            List<String> dates = GetDatas(start_date, end_date, cycle);
            for (int i = 0; i < dates.size(); i++) {
                String sql =
                        "delete from menu where type='" + type + "' and date='" + dates.get(i).substring(0, 10) + "' "
                                + "and isdelete=1";
                mysql.update(sql);
                String sqls =
                        "insert menu (dishes,date,type,create_user,create_time,isdelete,unit)values('" + dishes +
                                "',"
                                + "'" + dates.get(i).substring(0, 10) + "','" + type + "','" + create_user + "','" + create_time + "','" + isdelete + "','" + unit + "')";
                mysql.update(sqls);
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建菜单信息", userlog.TYPE_OPERATE, ip);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(442001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static List<String> GetDatas(String start_date, String end_date, String cycle) throws Exception {
        long start = RIUtil.dateToStamp(start_date);
        long end = RIUtil.dateToStamp(end_date);
        long date = start;
        List<String> datas = new ArrayList<>();

        while (date <= end) {
            int week = RIUtil.dateToWeek(RIUtil.stampToTime(date));
            if (cycle.contains(String.valueOf(week))) {
                datas.add(RIUtil.stampToTime(date));
                logger.warn(RIUtil.stampToTime(date));
            }
            date = date + 1000 * 60 * 60 * 24;
        }

        return datas;
    }

    //******GET*******
    public static JSONObject getMenu(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String dishes = "";
            String type = "";
            String start_date = "";
            String end_date = "";
            String unit = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("dishes") && data.getString("dishes").length() > 0) {
                dishes = data.getString("dishes");
                sql = sql + " dishes='" + dishes + "' and ";
            }

            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type='" + type + "' and ";
            }
            String menu = "";
            if (data.containsKey("menu") && data.getString("menu").length() > 0) {
                menu = data.getString("menu");
                sql = sql + " menu='" + menu + "' and ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " unit='" + unit + "' and ";
            }

            if (data.containsKey("start_date") && data.getString("start_date").length() > 0 && data.containsKey(
                    "end_date") && data.getString("end_date").length() > 0) {
                start_date = data.getString("start_date");
                end_date = data.getString("end_date");

            } else {
                start_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
                end_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            }
            sql = sql + "(date>='" + start_date + "' and date<='" + end_date + "') and ";

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select * from menu where 1=1 and " + sql + " isdelete=1  limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", list);
                sqls = "select count(id) as count from menu where 1=1 and " + sql + " isdelete=1; ";

                back.put("count", mysql.query_count(sqls));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(442005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    //******UPDATE*******
    private static JSONObject updateMenu(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String dishes = "";
            String date = "";
            String type = "";
            String cycle = "";
            String create_user = "";
            String create_time = "";
            String opt_user = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' , ";
            }
            if (data.containsKey("dishes") && data.getString("dishes").length() > 0) {
                dishes = data.getString("dishes");
                sql = sql + " dishes='" + dishes + "' , ";
            }
            if (data.containsKey("date") && data.getString("date").length() > 0) {
                date = data.getString("date");
                sql = sql + " date='" + date + "' , ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type='" + type + "' , ";
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            String sqls = "update menu set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新菜单信息", userlog.TYPE_OPERATE, ip);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(442003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private static JSONObject deleteMenu(JSONObject data) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(442008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(442008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update menu set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除菜单信息", userlog.TYPE_OPERATE, ip);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(442007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

}
