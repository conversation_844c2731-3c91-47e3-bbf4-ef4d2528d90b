package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.Lib.TNOAConf;
import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@RestController
public class DownFileController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());

    @RequestMapping(method = {RequestMethod.GET}, path = {"/download"})
    @PassToken
    public void previewfile(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // /download?file=0.jpg&url=http%3A%2F%2F192.168.10.103%3A1198%2FzipFIYmgGQZQ.jpg

        Map<String, String[]> map = request.getParameterMap();
        //logger.warn(map.toString());
        String id = map.get("id")[0];
        String big = "0";
        try {
            big = map.get("big")[0];
        } catch (Exception ex) {

        }
        String userid = "";
        try {
            userid = map.get("user_id")[0];
        } catch (Exception ex) {

        }
        String token = "";
        try {
            token = map.get("token")[0];
            String uid = JWT.decode(token).getAudience().get(0);
            if (uid == null) {

                return;
            }
        } catch (Exception ex) {
        }

        String fileName = "";
        try {
            fileName = map.get("name")[0];
        } catch (Exception ex) {

        }

        //logger.warn("big->" + big);
        InfoModelHelper info = null;

        try {
            info = InfoModelPool.getModel();
            String online = RIUtil.IdToName(userid, info, "online", "user");
            String userName = RIUtil.IdToName(userid, info, " name", "user");
            if (online.equals("1") || token.length() > 0) {

                String sql = "select * from upload where id='" + id + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = null;

                xJsonObject = result.get(0);


                String file_path = xJsonObject.getString("file_path");
                int nas_id = xJsonObject.getIntValue("nas_id");
                String file_name = xJsonObject.getString("file_name");

                String local_path = TNOAConf.get("file", "img_path");
                int type = xJsonObject.getIntValue("type");


                if (type != 999) {
                    file_name = StringToURL(file_name);
                    String filePName = file_path + file_name;
                    String endPoint = TNOAConf.get("HttpServ", "uni_url").replace("tyyh", "tymh");

                    String url = endPoint + "/images/" + filePName;
                    response.sendRedirect(url);
                } else {
                    file_name = StringToURL(file_name);
                    String filePName = "hl/" + file_path + file_name;
                    String endPoint = "http://10.34.251.34:50101/obs-qjjc-tyyh";
                   /* String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                    String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                    String bucketName = "obs-qjjc-tyyh";
                    ObsClient obsClient = new ObsClient(ak, sk, endPoint);

                    logger.warn("obs.fileName-->" + filePName);
                    ObsObject obsObj = obsClient.getObject(bucketName, filePName);
                    System.out.println("Obj Content:");
                    InputStream input = obsObj.getObjectContent();
                    byte[] b = new byte[1024];
                    //ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    OutputStream bos = new BufferedOutputStream(response.getOutputStream());
                    try {
                        int len;
                        while ((len = input.read(b)) != -1) bos.write(b, 0, len);
                        //System.out.println(new String(bos.toByteArray()));
                        bos.close();
                        input.close();
                    } catch (Exception e) {
                        logger.error(Lib.getTrace(e));
                    }
*/
                    String url = endPoint + "/" + filePName;
                    response.sendRedirect(url);
                }
            }
            // }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(info);

        }

    }

    private String GetContent(String fileName) {
        logger.warn(fileName);
        String n[] = fileName.split("\\.");
        String suffix = n[1];
        if (fileName.contains("ai")) {
            return "application/postscript";
        } else if (fileName.contains("eps")) {
            return "application/postscript";
        } else if (fileName.contains("exe")) {
            return "application/octet-stream";
        } else if (fileName.contains("doc")) {
            return "application/vnd.ms-word";
        } else if (fileName.contains("xls")) {
            return "application/vnd.ms-excel";
        } else if (fileName.contains("xlsx")) {
            return "application/vnd.ms-excel";
        } else if (fileName.contains("ppt")) {
            return "application/vnd.ms-powerpoint";
        } else if (fileName.contains("pps")) {
            return "application/vnd.ms-powerpoint";
        } else if (fileName.contains("pdf")) {
            return "application/pdf";
        } else if (fileName.contains("xml")) {
            return "application/xml";
        } else if (fileName.contains("odt")) {
            return "application/vnd.oasis.opendocument.text";
        } else if (fileName.contains("swf")) {
            return "application/x-shockwave-flash";
        } else if (fileName.contains("gz")) {
            return "application/x-gzip";
        } else if (fileName.contains("tgz")) {
            return "application/x-gzip";
        } else if (fileName.contains("bz")) {
            return "application/x-bzip2";
        } else if (fileName.contains("bz2")) {
            return "application/x-bzip2";
        } else if (fileName.contains("tbz")) {
            return "application/x-bzip2";
        } else if (fileName.contains("zip")) {
            return "application/zip";
        } else if (fileName.contains("rar")) {
            return "application/x-rar";
        } else if (fileName.contains("tar")) {
            return "application/x-tar";
        } else if (fileName.contains("7z")) {
            return "application/x-7z-compressed";
        } else if (fileName.contains("txt")) {
            return "text/plain";
        } else if (fileName.contains("php")) {
            return "text/x-php";
        } else if (fileName.contains("html")) {
            return "text/html";
        } else if (fileName.contains("htm")) {
            return "text/html";
        } else if (fileName.contains("js")) {
            return "text/javascript";
        } else if (fileName.contains("css")) {
            return "text/css";
        } else if (fileName.contains("rtf")) {
            return "text/rtf";
        } else if (fileName.contains("rtfd")) {
            return "text/rtfd";
        } else if (fileName.contains("py")) {
            return "text/x-python";
        } else if (fileName.contains("java")) {
            return "text/x-java-source";
        } else if (fileName.contains("rb")) {
            return "text/x-ruby";
        } else if (fileName.contains("sh")) {
            return "text/x-shellscript";
        } else if (fileName.contains("pl")) {
            return "text/x-perl";
        } else if (fileName.contains("sql")) {
            return "text/x-sql";
        } else if (fileName.contains("bmp")) {
            return "image/x-ms-bmp";
        } else if (fileName.contains("jpg")) {
            return "image/jpeg";
        } else if (fileName.contains("jpeg")) {
            return "image/jpeg";
        } else if (fileName.contains("gif")) {
            return "image/gif";
        } else if (fileName.contains("png")) {
            return "image/png";
        } else if (fileName.contains("tif")) {
            return "image/tiff";
        } else if (fileName.contains("tiff")) {
            return "image/tiff";
        } else if (fileName.contains("tga")) {
            return "image/x-targa";
        } else if (fileName.contains("psd")) {
            return "image/vnd.adobe.photoshop";
        } else if (fileName.contains("mp3")) {
            return "audio/mpeg";
        } else if (fileName.contains("mid")) {
            return "audio/midi";
        } else if (fileName.contains("ogg")) {
            return "audio/ogg";
        } else if (fileName.contains("mp4a")) {
            return "audio/mp4";
        } else if (fileName.contains("wav")) {
            return "audio/wav";
        } else if (fileName.contains("wma")) {
            return "audio/x-ms-wma";
        } else if (fileName.contains("avi")) {
            return "video/x-msvideo";
        } else if (fileName.contains("dv")) {
            return "video/x-dv";
        } else if (fileName.contains("mp4")) {
            return "video/mp4";
        } else if (fileName.contains("mpeg")) {
            return "video/mpeg";
        } else if (fileName.contains("mpg")) {
            return "video/mpeg";
        } else if (fileName.contains("mov")) {
            return "video/quicktime";
        } else {
            return "multipart/form-data";
        }


    }

    private static String StringToURL(String s) {

        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            if (c >= 0 && c <= 255) {
                sb.append(c);
            } else {
                byte[] b;
                try {
                    b = String.valueOf(c).getBytes("utf-8");
                } catch (Exception ex) {
                    System.out.println(ex);
                    b = new byte[0];
                }
                for (int j = 0; j < b.length; j++) {
                    int k = b[j];
                    if (k < 0) k += 256;
                    sb.append("%" + Integer.toHexString(k).toUpperCase());
                }
            }
        }
        return sb.toString();

    }

}
