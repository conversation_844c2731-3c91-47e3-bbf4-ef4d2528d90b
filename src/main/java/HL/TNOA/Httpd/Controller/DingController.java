package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.Export.ExportInterface;
import HL.TNOA.Lib.Export.ExportXlsxHelper;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.wechat.TestMsgLine;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import org.eclipse.jetty.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static HL.TNOA.Lib.RIUtil.RealDictNames;

@RestController
public class DingController {
    private static Logger logger = LoggerFactory.getLogger(DingController.class);
    // private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static String ip = "";
    public static int[] cycle_days_list = {0, 1, 7, 15, 30, 90, 180, 365};

    @RequestMapping(method = {RequestMethod.POST}, path = {"/ding"})
    //@PassToken
    public static Object get_ding(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        ip = request.getRemoteAddr();
        String opt = "";

        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("ding_get")) {
                return getDing(data);
            } else if (opt.equals("ding_get_list")) {
                //logger.warn("ding--->" + data.toString());
                return getDingList(data);
            } else if (opt.equals("ding_create")) {
                logger.warn("ding--->" + data.toString());
                return createDing(data, request);
            } else if (opt.equals("ding_up_reader")) {
                logger.warn("ding--->" + data.toString());
                return updateDingReader(data);
            } else if (opt.equals("ding_get_reading")) {
                return GetDingReading(data);
            } else if (opt.equals("ding_delete")) {
                return deleteNotice(data);
            } else if (opt.equals("ding_update")) {
                logger.warn("ding--->" + data.toString());
                return updateNotice(data, request);
            } else if (opt.equals("ding_ding_unread")) {
                logger.warn("ding--->" + data.toString());
                return dingUnread(data);
            } else if (opt.equals("get_favo_list")) {
                return getFavoList(data);
            } else if (opt.equals("get_notice_favo_list")) {
                return getNoticeFavoList(data);
            } else if (opt.equals("update_favo")) {
                return updateFavo(data);
            } else if (opt.equals("update_notice_favo")) {
                return updateNoticeFavo(data);
            } else if (opt.equals("get_dict_tree")) {
                return getDictTree(data);
            } else if (opt.equals("create_mul_ding")) {
                return CreateMulDing(data, request);
            } else if (opt.equals("ding_comment")) {
                logger.warn("notice--->" + data.toString());
                return comment(data);
            } else if (opt.equals("ding_delete_comment")) {
                return deleteComment(data);
            } else if (opt.equals("ding_up_comment")) {
                logger.warn("notice--->" + data.toString());
                return upComment(data);
            } else if (opt.equals("ding_auto_sta")) {
                return StaAutoDing(data);
            } else if (opt.equals("ding_auto_sta_det")) {
                return StaAutoDingDet(data);
            } else if (opt.equals("zb_qc")) {

                return Get_zbqc(data, request);
            } else if (opt.equals("zb_dl")) {
                return Getzbdl(data, request);
            } else if (opt.equals("ding_wffz")) {
                return GetDingWFfz(data, request);
            } else {
                return ErrNo.set(432003);
            }
        } else {
            return ErrNo.set(432003);
        }
    }

    private static Object GetDingWFfz(JSONObject data, TNOAHttpRequest request) {

        JSONObject back = ErrNo.set(0);
        try {
            String start_time = new SimpleDateFormat("yyyy-M").format(new Date()) + "-01";
            //String end_time = RIUtil.GetNextDate(today, -1);
            ;// 08-22
            logger.warn(start_time + "--");
            String token = request.getHeader("token");

            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time");
            }
            start_time = start_time.substring(0, 7) + "-01";
            start_time = start_time.replace("-", "");

            MysqlHelper mysql = null;

            try {
                mysql = new MysqlHelper("mysql");

                String sql = "select b.dict_name,c.notice_time,c.check_time,c.id from ding_auto a " +
                        "left join dict b on a.unit=b.id left " +
                        "join ding_notice c on a.id=c.auto_id where a.id like '%WFFZ%' AND yymm='" + start_time + "'";
                List<JSONObject> list = mysql.query(sql);
                JSONArray dets = new JSONArray();
                for (JSONObject one : list) {
                    String id = one.getString("id");
                    sql = "select opts from ding_form_logs where ding_id='" + id + "' order by time desc limit 1";
                    String opts = mysql.query_one(sql, "opts");
                    one.put("opts", opts);
                    System.out.println(one);
                    dets.add(one);
                }
                String heads = "派出所,下发时间,到期时间,完成情况";
                String keys = "dict_name,notice_time,check_time,opts";

                int fileId = ExportTables(dets, heads, keys, "ding_wffz");

                //JSONObject back = ErrNo.set(0);
                back.put("file_id", fileId);
                return back;

            } catch (Exception ex) {
                logger.warn(Lib.getTrace(ex));
                return ErrNo.set(null, 2, Lib.getTrace(ex));

            } finally {
                mysql.close();
            }


        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        }
    }

    private static Object Getzbdl(JSONObject data, TNOAHttpRequest request) {

        JSONObject back = ErrNo.set(0);
        try {
            String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String start_time = RIUtil.GetNextDate(today, -8);// 08-16
            String end_time = RIUtil.GetNextDate(today, -1);
            ;// 08-22
            logger.warn(start_time + "--" + end_time);
            String token = request.getHeader("token");

            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time");
            }

            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time");
            }


            String file_id = GetDLQK(start_time, end_time, token);


            back.put("file_id", file_id);
            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        }
    }

    private static String GetDLQK(String start_time, String end_time, String token) {

        HashMap<String, JSONObject> dds = new HashMap<>();
        try {

            OkHttpClient client = new OkHttpClient().newBuilder().readTimeout(1000 * 60, TimeUnit.MILLISECONDS).build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, "{\"ksrq\":\"" + start_time.replace("-", "") + "\"," +
                    "\"jsrq\":\"" + end_time.replace("-", "") + "\",\"mkid\":\"YY320400002305060000015\"}");
            Request request = new Request.Builder().url("http://50.56.90.173/tyjqglfw/event/gntj/queryMkfwqkList" +
                    "?faceKey=" + token).method("POST", body).addHeader("token", token).addHeader("Content-Type",
                    "application/json").build();
            Response response = client.newCall(request).execute();
            String resp = response.body().string();
            // String resp =
            // "{\"data\":{\"code\":\"10000\",\"results\":[{\"CODE\":\"320481000000\",\"ITEM1\":\"溧阳市公安局\",
            // \"DAYS\":\"6\",\"SL\":\"6081\",\"FWPL\":\"6.68241758241758241758241758241758241758E01\"},
            // {\"CODE\":\"320402000000\",\"ITEM1\":\"常州市公安局天宁分局\",\"DAYS\":\"6\",\"SL\":\"5636\",\"FWPL\":\"6
            // .19340659340659340659340659340659340659E01\"},{\"CODE\":\"320413000000\",\"ITEM1\":\"常州市公安局金坛分局\",
            // \"DAYS\":\"6\",\"SL\":\"4940\",\"FWPL\":\"5.42857142857142857142857142857142857143E01\"},
            // {\"CODE\":\"320404000000\",\"ITEM1\":\"常州市公安局钟楼分局\",\"DAYS\":\"6\",\"SL\":\"4701\",\"FWPL\":\"5
            // .16593406593406593406593406593406593407E01\"},{\"CODE\":\"320499000000\",\"ITEM1\":\"常州市公安局水上警察支队\",
            // \"DAYS\":\"6\",\"SL\":\"5\",\"FWPL\":\"5.49450549450549450549450549450549450549E-02\"},
            // {\"CODE\":\"320491000000\",\"ITEM1\":\"经济开发区分局\",\"DAYS\":\"6\",\"SL\":\"3770\",\"FWPL\":\"4
            // .14285714285714285714285714285714285714E01\"},{\"CODE\":\"320412000000\",\"ITEM1\":\"常州市公安局武进分局\",
            // \"DAYS\":\"6\",\"SL\":\"10439\",\"FWPL\":\"1.14714285714285714285714285714285714286E02\"},
            // {\"CODE\":\"320411000000\",\"ITEM1\":\"常州市公安局高新区分局\",\"DAYS\":\"6\",\"SL\":\"8124\",\"FWPL\":\"8
            // .92747252747252747252747252747252747253E01\"},{\"CODE\":\"320400000000\",\"ITEM1\":\"常州市公安局\",
            // \"DAYS\":\"6\",\"SL\":\"763\",\"FWPL\":\"8.38461538461538461538461538461538461538E00\"},
            // {\"CODE\":\"320498000000\",\"ITEM1\":\"常州市公安局交通治安分局\",\"DAYS\":\"6\",\"SL\":\"6\",\"FWPL\":\"6
            // .59340659340659340659340659340659340659E-02\"}]},\"message\":\"查询成功\",\"status\":200}";
            System.out.println(resp);
            JSONObject rrr = JSONObject.parseObject(resp);
            JSONObject det = rrr.getJSONObject("data");

            if (det.getInteger("code") == 10000) {

                JSONArray results = det.getJSONArray("results");

                for (int i = 0; i < results.size(); i++) {
                    JSONObject one = results.getJSONObject(i);
                    String code = one.getString("CODE");
                    String name = one.getString("ITEM1");
                    String count = one.getString("SL");
                    JSONObject d = new JSONObject();
                    d.put("code", code);
                    d.put("name", name);
                    d.put("count", count);
                    dds.put(code, d);

                }

            } else {
                System.out.println(det);
            }

        } catch (Exception e) {
            System.out.println(Lib.getTrace(e));
        }

        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");

            String sql = "select count(1) as count ,SUBSTR(ORGANIZATION_ID, 1, 6) as code from sys_log where  " +
                    "OPERATE_TIME>='" + start_time.replace("-", "") + "' and OPERATE_TIME<='" + end_time.replace("-",
                    "") +
                    "' group by SUBSTR(ORGANIZATION_ID, 1, 6)";

            System.out.println(sql);

            List<JSONObject> list = ora_hl.query(sql);
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String count = one.getString("COUNT");
                String code = one.getString("CODE") + "000000";

                if (dds.containsKey(code)) {
                    JSONObject det = dds.get(code);
                    det.put("opt_time", count);
                    dds.put(code, det);
                }

            }
            sql = "select count(1) as count ,SUBSTR(ORGANIZATION_ID, 1, 6) as code from sys_log where  " +
                    "OPERATE_TIME>='" + start_time.replace("-", "") + "' and OPERATE_TIME<='" + end_time.replace("-",
                    "") +
                    "' and OPERATE_NAME='主防大屏' group by SUBSTR(ORGANIZATION_ID, 1, 6)";

            System.out.println(sql);

            list = ora_hl.query(sql);
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String count = one.getString("COUNT");
                String code = one.getString("CODE") + "000000";

                if (dds.containsKey(code)) {
                    JSONObject det = dds.get(code);
                    det.put("zf", count);
                    dds.put(code, det);
                }

            }
        } catch (Exception e) {
            System.out.println(Lib.getTrace(e));
        } finally {

            ora_hl.close();
        }

        MysqlHelper my143 = null;
        try {
            my143 = new MysqlHelper("mysql_zxqc");
            String sql = "select count(zjbh) as count,SUBSTR(ssdwdm from 1 for 6) as code from login_log where  " +
                    "dlsj>='" + start_time.replace("-", "") + "' and dlsj<='" + end_time.replace("-", "") + "' group "
                    + "by "
                    + "SUBSTR(ssdwdm from 1 for 6)";

            System.out.println(sql);

            List<JSONObject> list = my143.query(sql);
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String count = one.getString("count");
                String code = one.getString("code") + "000000";

                if (dds.containsKey(code)) {
                    JSONObject det = dds.get(code);
                    det.put("login", count);
                    dds.put(code, det);
                }

            }

            sql =
                    "select count(SFZH) as count,SUBSTR(ssdwdm from 1 for 6) as code from login_log where dlsj>='" + start_time.replace("-", "") + "' " + "and dlsj<='" + end_time.replace("-", "") + "' group by SFZH,SUBSTR(ssdwdm from 1 for 6)";

            System.out.println(sql);

            list = my143.query(sql);
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String count = one.getString("count");
                String code = one.getString("code") + "000000";

                if (dds.containsKey(code)) {
                    JSONObject det = dds.get(code);
                    int rc = 0;
                    if (det.containsKey("rc")) {

                        rc = det.getIntValue("rc");

                    }
                    rc++;
                    det.put("rc", rc);
                    dds.put(code, det);
                }

            }

        } catch (Exception e) {
            System.out.println(Lib.getTrace(e));
        } finally {
            my143.close();
        }

        System.out.println(dds);
        int sjcount = 0;
        int sjopt = 0;

        int sjlogin = 0;
        int sjrc = 0;
        int sjzf = 0;
        JSONArray dets = new JSONArray();
        for (Map.Entry<String, JSONObject> det : dds.entrySet()) {
            String key = det.getKey();
            JSONObject d = det.getValue();
            if (key.equals("320400000000") || key.equals("320496000000") || key.equals("320499000000") || key.equals(
                    "320498000000")) {
                int count = d.getIntValue("count");
                int opt_time = d.getIntValue("opt_time");
                int login = d.getIntValue("login");
                int rc = d.getIntValue("rc");
                int zf = d.getIntValue("zf");
                sjcount = sjcount + count;
                sjopt = sjopt + opt_time;

                sjlogin = sjlogin + login;
                sjrc = sjrc + rc;
                sjzf = sjzf + zf;

            } else {
                System.out.println(d.getString("code") + " " + d.getString("name") + " " + d.getString("count") + " " + d.getString("opt_time") + " " + " " + d.getString("rc") + " " + d.getString("zf"));
                dets.add(d);

            }

        }

        System.out.println("320400000000 市局 " + sjcount + " " + sjopt + " " + sjrc + " " + sjzf);
        JSONObject d = new JSONObject();
        d.put("code", "320400000000");
        d.put("name", "市局");
        d.put("count", sjcount);
        d.put("opt_time", sjopt);
        d.put("rc", sjrc);
        d.put("zf", sjzf);
        dets.add(d);


        String fileId = String.valueOf(ExportTables(dets, "组织代码,单位名称,登陆次数,操作数,登陆人次,主防", "code,name,count,opt_time,rc,"
                + "zf", "zbdl"));

        return fileId;
    }

    private static JSONObject Get_zbqc(JSONObject data, TNOAHttpRequest request) {
        JSONObject back = ErrNo.set(0);
        try {
            String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String start_time = RIUtil.GetNextDate(today, -8);// 08-16
            String end_time = RIUtil.GetNextDate(today, -1);
            ;// 08-22
            logger.warn(start_time + "--" + end_time);
            String token = request.getHeader("token");
            String rets = GetQCid(start_time, end_time, token);

            String[] rs = rets.split("\\|");
            back.put("data", rs[0]);
            String fileId = GetSta(rs[1], token);
            back.put("file_id", fileId);
            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        }
    }

    private static String GetSta(String ids, String token) {

        String file_id = "";
        try {
            JSONObject det = new JSONObject();
            det.put("opt", "static_zxqc");
            det.put("isExp", 1);
            det.put("circle_id", ids);
            det.put("real_opt_user", "32040219900112281X");

            OkHttpClient client = new OkHttpClient().newBuilder().build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, det.toString());
            Request request =
                    new Request.Builder().url("http://50.56.93.142:10363/ss_static").method("POST", body).addHeader(
                            "token",
                            token).addHeader("Content-Type", "application/json").build();
            Response response = client.newCall(request).execute();
            String res = response.body().string();

//			 System.out.println(res);
            JSONObject ret = JSONObject.parseObject(res);
            file_id = ret.getString("file_id");
        } catch (Exception e) {
            System.out.println(Lib.getTrace(e));
        }
        return file_id;

    }

    private static String GetQCid(String start_time, String end_time, String token) {

        String ids = "";
        JSONObject ret = new JSONObject();
        HashMap<String, Integer> counts = new HashMap<>();
        String rets = "";
        try {
            OkHttpClient client = new OkHttpClient().newBuilder().build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType,
                    "{\"opt\":\"get_circle_list\",\"page\":1,\"limit\":999," + "\"query\":\"\",\"type\":1," +
                            "\"id_card" +
                            "\":\"\",\"start_time\":\"" + start_time + " 00:00:00\"," + "\"end_time\":\"" + end_time + " 23:59:59" + "\",\"opt_user\":\"320404198701012513\"," + "\"real_opt_user" + "\":\"320404198701012513" + "\"}");
            Request request = new Request.Builder().url("http://qjjcgzpt.czx.js/qjjc/hl/zxqq/circle").method("POST",
                    body).addHeader("token", token).addHeader("Content-Type", "application/json").build();
            Response response = client.newCall(request).execute();

            String res = response.body().string();

            // System.out.println(res);
            ret = JSONObject.parseObject(res);
            JSONArray dets = ret.getJSONArray("data");
            for (int i = 0; i < dets.size(); i++) {
                String id = dets.getJSONObject(i).getString("id");

                ids = ids + id + ",";

                JSONArray datas = dets.getJSONObject(i).getJSONArray("data");
                String unit = datas.getString(0).substring(0, 6);
                int c = 0;
                if (counts.containsKey(unit)) {
                    c = counts.get(unit);

                }
                c++;
                counts.put(unit, c);

            }

            ids = ids.substring(0, ids.length() - 1);
            System.out.println(counts);
            for (Map.Entry<String, Integer> d : counts.entrySet()) {
                String u = RIUtil.dicts.get(d.getKey() + "000000").getString("dict_name");
                int c = d.getValue();

                rets = rets + u + ":" + c + ";";
            }
            rets = rets + "|" + ids;
            System.out.println(rets);
        } catch (Exception e) {
            System.out.println(Lib.getTrace(e));
        }
        return rets;

    }

    private static JSONObject StaAutoDing(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String start_time = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        String end_time = start_time;
        if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
            start_time = data.getString("start_time");
        }

        if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
            end_time = data.getString("end_time");
        }


        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");
            String sql = "select a.unit,b.dict_name,b.remark,count(a.id) as count ,a.id,'逻辑1' as name from ding_auto "
                    + "a " + "left join dict b on a.unit=b.id where send_time>='" + start_time + " 00:00:00' and " +
                    "send_time<='" + end_time + " 23:59:59' and a.id like " +
                    "'KFXAJYJ1%'" + " group " + "by a.unit\n" + "\n" + "UNION\n" + "select a.unit,b.dict_name,b" +
                    ".remark,"
                    + "count(a.id) as count ,a.id,'逻辑2' as name  from ding_auto a " + "left join dict b on a.unit=b" + ".id where" + " send_time>='" + start_time + " 00:00:00' and send_time<='" + end_time + " 23:59:59' and a .id like 'KFXAJYJ2 %' group by a" +
                    ".unit\n" + "\n" + "UNION" + "\n" + "select a.unit,b.dict_name,b.remark,count(a.id) as count ,a" +
                    ".id,'逻辑3' as " + "name  from " + "ding_auto " + "a " + "left join dict b on a.unit=b.id where " +
                    "send_time>='" + start_time + " 00:00:00' and send_time<='" + end_time + " 23:59:59' and a.id " +
                    "like " +
                    "'KFXAJYJ3%' group by a .unit ";
            logger.warn(sql);
            List<JSONObject> list = mysql.query(sql);
            int fileId = -1;
            if (list.size() > 0) {
                JSONArray dets = new JSONArray();
                for (int i = 0; i < list.size(); i++) {
                    dets.add(list.get(i));
                }
                fileId = ExportTables(dets, "逻辑,明细,条数", "name,remark,count", "dingA");
            }

            back.put("file_id", fileId);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            mysql.close();
        }

    }

    private static JSONObject StaAutoDingDet(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String start = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        String end = start;
        if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
            start = data.getString("start_time");
        }

        if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
            end = data.getString("end_time");
        }


        MysqlHelper mysql = null;

        try {
            mysql = new MysqlHelper("mysql");


            System.out.println(start + "->" + end);
            String sql = "select id,auto_id,notice_time,check_time,form_dets from ding_notice where auto_id like " +
                    "'KFXAJYJ%' and create_time>='" + start + " 00:00:00' and create_time<='" + end + " 23:59:59' " + "order " +
                    "by " + "create_time desc";
            List<JSONObject> list = mysql.query(sql);
            JSONArray dets = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);


                String id = one.getString("id");
                String auto_id = one.getString("auto_id");
                JSONObject fdets = one.getJSONObject("form_dets");
                one.remove("id");
                one.remove("auto_id");
                one.remove("form_dets");


                if (auto_id.startsWith("KFXAJYJ1_")) {
                    one.put("title", "可防性案件精准走访（一）");
                    one.put("content", "处警类别为盗窃民宅，在处警完成后下发盯办任务。");
                }

                if (auto_id.startsWith("KFXAJYJ2_")) {
                    one.put("title", "可防性案件精准走访（二）");
                    one.put("content", "处警类别为盗窃商铺，在处警完成后下发盯办任务");
                }

                if (auto_id.startsWith("KFXAJYJ3_")) {
                    one.put("title", "可防性案件精准走访（三）");
                    one.put("content", "处警类别为盗窃单位，在处警完成后下发盯办任务。");
                }

                //上门日期

                String doorTime = fdets.getString("FormDate_1d7f30443468437e969d4d09ae0e1587");
                one.put("door_time", doorTime);


                sql = "select content,unit from ding_auto where id='" + auto_id + "'";
                List<JSONObject> dds = mysql.query(sql);
                if (dds.size() > 0) {
                    JSONObject dd = dds.get(0);

                    JSONObject con = dd.getJSONObject("content");
                    String unit = dd.getString("unit");

                    String uName = con.getString("name");
                    one.put("uName", uName);
                    String jjbh = con.getString("JJBH");
                    one.put("jjbh", jjbh);


                    //System.out.println(one);

                    //反馈民警 是否评价

                    sql = "select * from ding_form_logs where ding_id='" + id + "' order by id ";
                    List<JSONObject> logs = mysql.query(sql);
                    String status = "";
                    String finTime = "";
                    String finPer = "";
                    for (int a = 0; a < logs.size(); a++) {
                        JSONObject lone = logs.get(a);
                        String time = lone.getString("time");
                        status = lone.getString("opts");
                        if (status.contains("提交")) {
                            finTime = time;
                            try {
                                finPer = RIUtil1.users1.get(lone.getString("opt_user")).getString("name");
                            } catch (Exception e) {
                                // TODO: handle exception
                            }

                        }


                    }

                    one.put("fin_polie", finPer);
                    one.put("status", status);

                    dets.add(one);

                    System.out.println(one);


                }


            }

            String heads = "派出所,警情编号,任务名称,逻辑内容,下发时间,到期时间,反馈民警,上门时间,完成情况";
            String keys = "uName,jjbh,title,content,notice_time,check_time,fin_polie,door_time,status";

            int fileId = ExportTables(dets, heads, keys, "ding");

            //JSONObject back = ErrNo.set(0);
            back.put("file_id", fileId);
            return back;

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            mysql.close();
        }


    }

    private static int ExportTables(JSONArray datas, String head, String keys, String name) {


        String FileName = name + "_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        String[] heads = head.split(",");
        String key[] = keys.split(",");
        for (int i = 0; i < key.length; i++) {
            header.add(key[i]);
            headername.put(key[i], heads[i]);
        }


        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) +
                            "/";

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
//            for (int i = 0; i < datas.size(); i++) {
//                JSONObject jsonObject = datas.getJSONObject(i);
//                JSONArray jsonArray = jsonObject.getJSONArray("real_all");
            exporthelper.write_data(datas);
//            }

            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql = "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath +
                        "','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");
                try {
                    if (exporthelper != null) {
                        try {
                            exporthelper.close();
                        } catch (Exception ex) {

                        }

                    }
                } catch (Exception ex) {

                }

                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, TNOAConf.get("file",
                        "img_path") + filePath + FileName);
                logger.warn(obsFileName + "-->" + ret);

                logger.warn("-->" + id);
                return id;
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return 0;
        } finally {

            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }
        }
    }


    private static JSONObject CreateMulDing(JSONObject data, TNOAHttpRequest request) {

        JSONObject back = ErrNo.set(0);
        if (data.containsKey("users") && data.getString("users").length() > 0) {
            String users = data.getString("users");
            String[] us = users.split(",");
            for (int i = 0; i < us.length; i++) {
                String uone = us[i];
                data.put("users", uone);

                try {


                    JSONObject ret = createDing(data, request);
                    if (ret.containsKey("errno") && ret.getInteger("errno") != 0) {
                        back = ret;
                        break;
                    }
                } catch (Exception ex) {
                    back = ErrNo.set(null, 2, Lib.getTrace(ex));

                }

            }


        } else {
            return ErrNo.set(null, 2, "未选择发送人");
        }


        return back;
    }

    private static JSONObject getDictTree(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        try {
            JSONArray type156 = RIUtil.GetDictByType(156);
            List<JSONObject> fifty = new ArrayList<>();
            for (int i = 0; i < type156.size(); i++) {
                JSONObject fif = type156.getJSONObject(i);
                fifty.add(fif);
            }

            JSONArray type157 = RIUtil.GetDictByType(157);
            List<JSONObject> fifty2 = new ArrayList<>();
            for (int i = 0; i < type157.size(); i++) {
                JSONObject fif = type157.getJSONObject(i);
                fifty2.add(fif);
            }


            JSONArray type158 = RIUtil.GetDictByType(158);
            JSONArray rest = new JSONArray();
            JSONArray t156 = new JSONArray();
            for (int i = 0; i < fifty.size(); i++) {

                JSONObject one156 = fifty.get(i);
                String id = one156.getString("id");
                JSONArray t157 = new JSONArray();

                for (int a = 0; a < fifty2.size(); a++) {

                    JSONObject one157 = fifty2.get(a);
                    if (one157.getString("father_id").equals(id)) {
                        String id157 = one157.getString("id");
                        JSONArray t158 = new JSONArray();

                        for (int b = 0; b < type158.size(); b++) {

                            JSONObject one158 = type158.getJSONObject(b);
                            if (one158.getString("father_id").equals(id157)) {
                                t158.add(one158);
                            }
                        }
                        one157.put("dets", t158);
                        t157.add(one157);
                    }
                }

                one156.put("dets", t157);
                t156.add(one156);

            }
//            rest.add(t156);
            back.put("data", t156);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(505002);
        }
    }

    private static JSONObject getNoticeFavoList(JSONObject data) {
        String sql = "";
        String opt_user = "";
        String content = "";
        String order = " a.isTop desc,a.notice_time desc";
        String type = "";
        String isRead = "";//1未读 2已读
        String create_time_start = "";
        String create_time_end = "";
        String user_id = "";
        String create_user = "";
        int limit = 20;
        int page = 1;
        if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
            create_time_start = data.getString("create_time_start").replace("|", " ");
            sql = sql + " create_time>='" + create_time_start + "' and ";
        }
        if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
            create_time_end = data.getString("create_time_end").replace("|", " ");
            sql = sql + " create_time<='" + create_time_end + "' and ";
        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
            sql = sql + " create_user='" + create_user + "' and ";
        }
        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");
            sql = sql + " (a.reading like '%" + user_id + "%' or a.readed like '%" + user_id + "%') and ";
        }
        if (data.containsKey("isRead") && data.getString("isRead").length() > 0) {
            isRead = data.getString("isRead");
            if (isRead.equals("1")) {
                sql = sql + " a.reading like '%" + user_id + "%'  and ";
            } else {
                sql = sql + "a.readed like '%" + user_id + "%' and ";
            }
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        }
        if (data.containsKey("content") && data.getString("content").length() > 0) {
            content = data.getString("content");
            sql = sql + " (decode(a.title,'" + RIUtil.enTitle + "') like'%" + content + "%' or decode(a.content," +
                    "'" + RIUtil.enContent + "') like'%" + content + "%') and";
        }
        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
            sql = sql + " a.type = '" + type + "' and ";
        }
        if (data.containsKey("limit") && data.getString("limit").length() > 0) {
            limit = data.getInteger("limit");
        }
        if (data.containsKey("page") && data.getString("page").length() > 0) {
            page = data.getInteger("page");
        }
        MysqlHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = new MysqlHelper("mysql");
//            String sqls = "select a.id,a.content as content,a.title as title," + "a.create_user," + "a.isTop,a.isNew,"
//                    + "a.notice_time,a.readed,a.reading,a.label,a.type,a.subType,a.cycle,link ," + "old_author,a" +
//                    ".isSub,a.comms from " + "notice_favo b inner join notice a on b.ding_id = a.id where 1=1 and "
//                    + sql + " a.isdelete=1 and b.favo =1 and b.favo_user = '" + opt_user + "' order by " + order +
//                    " " + "limit " + limit + " " + "offset " + limit * (page - 1);
            String sqls =
                    "select a.id,decode(a.content,'" + RIUtil.enContent + "') as content,decode(a.title,'" + RIUtil.enTitle + "') as title," + "a.create_user,a.isTop,a.isNew,a.notice_time,a.readed,a.reading,a.label,a.type,a.subType,a.cycle,link ,old_author " + " from notice_favo b inner join notice a on b.notice_id = a.id where 1=1 and " + sql + " " + "a.isdelete=1 and b.favo =1 and b.favo_user = '" + opt_user + "' order by " + order + " " + "limit " + limit + " offset " + limit * (page - 1);

            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            //logger.warn(sqls);
            if (list.size() > 0) {
                for (JSONObject jsonObject : list) {
                    jsonObject.put("favo", 1);
                }
                back.put("data", RelaInfoList(list, mysql, opt_user));
                sqls = "select count(*) as count from notice_favo b inner join notice a on b.notice_id = a.id " +
                        "where " + "1=1 and " + sql + " a.isdelete=1 and b.favo =1 and b.favo_user = '" + opt_user +
                        "'";
                back.put("count", mysql.query_count(sqls));

            } else {
                back.put("data", list);
                back.put("count", 0);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 432004, Lib.getTrace(e));
        } finally {
            mysql.close();
        }
        return back;
    }

    private static JSONObject updateNoticeFavo(JSONObject data) {
        String sql = "";
        String opt_user = "";
        String notice_id = "";
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        }
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            notice_id = data.getString("id");
        }

        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");
            if (data.containsKey("favo") && Integer.parseInt(data.getString("favo")) == 1) {
                sql = "select * from notice_favo where favo_user = '" + opt_user + "' and favo =1 and notice_id =" +
                        " '" + notice_id + "'";
                List<JSONObject> list = mysql.query(sql);
                if (!list.isEmpty()) return ErrNo.set(null, 432004, "已收藏！");
                sql =
                        "insert into notice_favo(favo_user,favo,notice_id) values ('" + opt_user + "',1,'" + notice_id + "')";
            }
            if (data.containsKey("favo") && Integer.parseInt(data.getString("favo")) == 0) {
                sql = "delete from notice_favo where favo_user = '" + opt_user + "' and notice_id = '" + notice_id +
                        "'";
            }
            mysql.update(sql);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 432004, Lib.getTrace(e));
        } finally {
            mysql.close();
        }
        return ErrNo.set(0);

    }

    private static JSONObject updateFavo(JSONObject data) {
        String sql = "";
        String opt_user = "";
        String ding_id = "";
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        }
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            ding_id = data.getString("id");
        }

        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");
            if (data.containsKey("favo") && Integer.parseInt(data.getString("favo")) == 1) {
                sql =
                        "select * from ding_favo where favo_user = '" + opt_user + "' and favo =1 and ding_id = '" + ding_id + "'";
                List<JSONObject> list = mysql.query(sql);
                if (!list.isEmpty()) return ErrNo.set(null, 432004, "已收藏！");
                sql = "insert into ding_favo(favo_user,favo,ding_id) values ('" + opt_user + "',1,'" + ding_id + "')";
            }
            if (data.containsKey("favo") && Integer.parseInt(data.getString("favo")) == 0) {
                sql = "delete from ding_favo where favo_user = '" + opt_user + "' and ding_id = '" + ding_id + "'";
            }
            mysql.update(sql);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 432004, Lib.getTrace(e));
        } finally {
            mysql.close();
        }
        return ErrNo.set(0);

    }

    private static JSONObject getFavoList(JSONObject data) {
        String sql = "";
        String opt_user = "";
        String content = "";
        String isRead = "";//1未读 2已读
        String create_user = "";
        String create_time_start = "";
        String create_time_end = "";
        String user_id = "";
        String order = " a.isTop desc,a.notice_time desc";
        int limit = 20;
        int page = 1;
        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");
            sql = sql + " (a.reading like '%" + user_id + "%' or a.readed like '%" + user_id + "%') and ";
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        }
        if (data.containsKey("content") && data.getString("content").length() > 0) {
            content = data.getString("content");
            sql = sql + " (a.title like'%" + content + "%' or a.content like'%" + content + "%' and ding_num like" +
                    " " + "'%" + content + "%') " + "and";
        }
        if (data.containsKey("isRead") && data.getString("isRead").length() > 0) {
            isRead = data.getString("isRead");
            if (isRead.equals("1")) {
                sql = sql + " a.reading like '%" + user_id + "%'  and ";
            } else {
                sql = sql + "a.readed like '%" + user_id + "%' and ";
            }
        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
            sql = sql + " create_user='" + create_user + "' and ";
        }
        if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
            create_time_start = data.getString("create_time_start").replace("|", " ");
            sql = sql + " create_time>='" + create_time_start + "' and ";
        }
        if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
            create_time_end = data.getString("create_time_end").replace("|", " ");
            sql = sql + " create_time<='" + create_time_end + "' and ";
        }
        if (data.containsKey("limit") && data.getString("limit").length() > 0) {
            limit = data.getInteger("limit");
        }
        if (data.containsKey("page") && data.getString("page").length() > 0) {
            page = data.getInteger("page");
        }
        MysqlHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = new MysqlHelper("mysql");
            String sqls = "select a.id,a.content as content,a.title as title," + "a.create_user," + "a.isTop,a" +
                    ".isNew," + "a.notice_time,a.readed,a.reading,a.label,a.type,a.subType,a.cycle,link ," +
                    "old_author,a" + ".isSub,a.comms,a.ding_label,a.check_time from " + "ding_favo b inner join " +
                    "ding_notice a on " + "b" + ".ding_id = a" + ".id " + "where 1=1 and " + sql + " a.isdelete=1 " + "and b.favo =1 and b" + ".favo_user =" + " '" + opt_user + "' order " + "by " + order + " " + "limit " + limit + " " + "offset " + limit * (page - 1);

            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);

            if (list.size() > 0) {
                String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                for (JSONObject jsonObject : list) {
                    jsonObject.put("favo", 1);
                    jsonObject.put("ding_label", jsonObject.getJSONArray("ding_label"));
                    //判断是否逾期
                    jsonObject.put("isLate", "0");
                    sqls = "select time,opt_user,opts from ding_form_logs where ding_id='" + jsonObject.getString("id"
                    ) + "' and opts ='提交'";
                    List<JSONObject> query = mysql.query(sqls);
                    //有提交 判断提交时间是否超过check_time
                    if (query.size() > 0 && ((query.get(0).getString("time")).compareTo(jsonObject.getString(
                            "check_time")) > 0)) {
                        jsonObject.put("isLate", "2");//逾期完成
                    }
                    //无提交 判断当前时间是否超过check_time
                    else if (query.size() == 0 && jsonObject.getString("check_time").compareTo(date) < 0) {
                        jsonObject.put("isLate", "1");//逾期未完成
                    }
                }
                back.put("data", RelaInfoList(list, mysql, opt_user));
                sqls = "select count(*) as count from ding_favo b inner join ding_notice a on b.ding_id = a.id " +
                        "where " + "1=1 and " + sql + " a.isdelete=1 and b.favo =1 and b.favo_user = '" + opt_user +
                        "'";
                back.put("count", mysql.query_count(sqls));

            } else {
                back.put("data", list);
                back.put("count", 0);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 432004, Lib.getTrace(e));
        } finally {
            mysql.close();
        }
        return back;
    }


    private static String GetQQBUserId(String user_id) {
        MysqlHelper qqb_user = null;
        String exeid = "";
        try {
            qqb_user = new MysqlHelper("mysql_qqb_user");

            String sql = "select id from auth.SYS_AUTH_USER where idcard_no='" + user_id + "' and deleted=0 and " +
                    "(IS_TEMPORARY  is null or IS_TEMPORARY <>1) and type>=0 order by type limit 1";
            exeid = qqb_user.query_one(sql, "ID");

        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
        } finally {
            qqb_user.close();


        }
        return exeid;
    }


    private static JSONObject dingUnread(JSONObject data) {
        String notice_id = "";
        String accepters = "";
        String opt_user = "";
        int source = 0;
        if (data.containsKey("notice_id") && data.getString("notice_id").length() > 0) {
            notice_id = data.getString("notice_id");
        } else {
            // return ErrNo.set(432024);
        }
        if (data.containsKey("accepters") && data.getString("accepters").length() > 0) {
            accepters = data.getString("accepters");
        } else {
            return ErrNo.set(432024);
        }

        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(432024);
        }
        if (data.containsKey("source") && data.getString("source").length() > 0) {
            source = data.getInteger("source");
        } else {
            //return ErrNo.set(432024);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            JSONObject back = ErrNo.set(0);
            String comment = "您有一条通知请尽快签收";
            List<String> userList = new ArrayList<>();
            userList.add(accepters);


            TestMsgLine send = new TestMsgLine();
            send.sendMSG(comment, userList);


            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    public static JSONObject dingComment(JSONObject data) {
        String comment = "";
        String accepter = "";
        String real_id = "";
        String type = "";
        String opt_user = "";

        if (data.containsKey("comment") && data.getString("comment").length() > 0) {
            comment = data.getString("comment");
        } else {
            return ErrNo.set(432024);
        }
        if (data.containsKey("real_id") && data.getString("real_id").length() > 0) {
            real_id = data.getString("real_id");
        } else {
            return ErrNo.set(432024);
        }
        if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
            accepter = data.getString("accepter");
        } else {
            return ErrNo.set(432024);
        }

        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(432024);
        }
        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
        } else {
            return ErrNo.set(432024);
        }


        List<String> userList = new ArrayList<>();
        String[] accs = accepter.split(",");
        for (int i = 0; i < accs.length; i++) {
            userList.add(accs[i]);
            JSONObject det = data;
            det.remove("real_opt_user");
            det.put("accepter", accs[i]);
            System.out.println(det);
            RIUtil.JsonInsert(det, "ding_msg");
        }

        TestMsgLine send = new TestMsgLine();
        send.sendMSG(comment, userList);


        return ErrNo.set(0);


    }


    private static Object GetRelaTitle(List<JSONObject> list, InfoModelHelper mysql) throws Exception {

        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String rela_id = one.getString("rela_id");
            String title = RIUtil.IdToName(rela_id, mysql, "title", "ding_notice");
            one.put("title", title);
            back.add(one);
        }

        return back;
    }

    private static JSONObject updateNotice(JSONObject data, TNOAHttpRequest request) throws Exception {
        String id = "";
        String title = "";
        String content = "";
        String groups = "";
        String users = "";
        String comment_type = "1";
        String comment_select = "";
        int sendWechat = 0;
        int sendMsg = 0;
        String notice_time = "";
        String img = "";
        String isTop = "";
        String top_date = "";
        String type = "";
        String subType = "";
        String sql = "";
        String accessory = "";
        String link = "";
        String check_time = "";
        String unit = "";
        String source = "";
        int cycle = 0;
        int cycle_days = 0;
        long cycle_seconds = 0;
        String cycle_end = "";
        int isAll = 0;
        String label = "";
        String form_dets = "";
        JSONObject olds = new JSONObject();

        String isSub = "";
        String comms = "";


        JSONArray ding_label = new JSONArray();

        if (data.containsKey("content") && data.getString("content").length() > 0) {
            content = data.getString("content");
            sql = sql + "content='" + content + "',";
        }

        if (data.containsKey("title") && data.getString("title").length() > 0) {
            title = data.getString("title");
            sql = sql + "title='" + title + "',";
        }

        if (data.containsKey("ding_label") && !ObjectUtils.isEmpty("ding_label")) {
            ding_label = data.getJSONArray("ding_label");
            sql = sql + "ding_label='" + JSONArray.toJSONString(ding_label) + "',";
        }


        if (data.containsKey("is_all") && data.getString("is_all").length() > 0) {
            isAll = data.getInteger("is_all");
        }
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");


        } else {
            return ErrNo.set(432006);
        }


        if (data.containsKey("users") && data.getString("users").length() > 0) {
            users = data.getString("users");
            //  sql = sql + "users='" + users + "',";
        }

        if (data.containsKey("label") && data.getString("label").length() > 0) {
            label = data.getString("label");
            sql = sql + "label='" + label + "',";
        }
        if (data.containsKey("comment_type") && data.getString("comment_type").length() > 0) {
            comment_type = data.getString("comment_type");
            sql = sql + "comment_type='" + comment_type + "',";
        }
        if (data.containsKey("comment_select") && data.getString("comment_select").length() > 0) {
            comment_select = data.getString("comment_select");
            sql = sql + "comment_select='" + comment_select + "',";
        }
        if (data.containsKey("isTop") && data.getString("isTop").length() > 0) {
            isTop = data.getString("isTop");
            sql = sql + "isTop='" + isTop + "',";
        }

        if (data.containsKey("top_date") && data.getString("top_date").length() > 0) {
            top_date = data.getString("top_date");
            sql = sql + "top_date='" + top_date + "',";
        }

        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
            sql = sql + "type='" + type + "',";
        }


        if (data.containsKey("subType") && data.getString("subType").length() > 0) {
            subType = data.getString("subType");
            sql = sql + " subType='" + subType + "' , ";
        }

        if (data.containsKey("img") && data.getString("img").length() > 0) {
            img = data.getString("img");
            sql = sql + "img='" + img + "',";
        }
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            sql = sql + "unit='" + unit + "',";
        }
        if (data.containsKey("accessory")) {
            accessory = data.getString("accessory");
            sql = sql + "accessory='" + accessory + "',";
        }
        if (data.containsKey("check_time") && data.getString("check_time").length() > 0) {

            check_time = data.getString("check_time").replace("|", " ");
            sql = sql + "check_time='" + check_time + "' ,";
        }
        if (data.containsKey("form_dets") && data.getString("form_dets").length() > 0) {

            form_dets = data.getString("form_dets");
            sql = sql + "form_dets='" + form_dets + "' ,";
        }

        if (data.containsKey("cycle") && data.getString("cycle").length() > 0) {
            cycle = data.getInteger("cycle");
            if (cycle > 0) {
                String date_time[] = notice_time.split(" ");

                String date = date_time[0];

                if (check_time.length() == 0) {
                    check_time = date + " 23:59:59";
                }
                if (cycle == 8) {
                    if (data.containsKey("cycle_days") && data.getString("cycle_days").length() > 0) {
                        cycle_days = data.getInteger("cycle_days");
                    } else {
                        return ErrNo.set(null, 2, "startTime bigger than endTime");
                    }
                } else {
                    cycle_days = cycle_days_list[cycle];
                }


            } else {
                cycle_end = check_time;
            }

        }
        // cycle_seconds = RIUtil.dateToStamp(check_time) - RIUtil.dateToStamp(notice_time);
        //logger.warn(RIUtil.dateToStamp(end_time) + "-" + RIUtil.dateToStamp(start_time) + "=" + cycle_seconds);
       /* if (cycle_seconds < 0) {
            logger.error("startTime bigger than endTime");
            return ErrNo.set(null, 2, "startTime bigger than endTime");
        }*/
        /*sql = sql + " cycle='" + cycle + "' , ";
        sql = sql + " cycle_days='" + cycle_days + "' , ";
        sql = sql + " cycle_seconds='" + cycle_seconds + "' , ";
        //  sql = sql + " is_notice=0,";*/


        if (data.containsKey("cycle_end") && data.getString("cycle_end").length() > 0) {
            cycle_end = data.getString("cycle_end").replace("|", " ");
            sql = sql + " cycle_end='" + cycle_end + "' , ";
        }
        if (data.containsKey("link") && data.getString("link").length() > 0) {
            link = data.getString("link");
            sql = sql + " link='" + link + "' , ";
        }
        if (data.containsKey("isSub") && data.getString("isSub").length() > 0) {
            isSub = data.getString("isSub");
            sql = sql + "isSub='" + isSub + "',";
        }
        if (data.containsKey("comms") && data.getString("comms").length() > 0) {
            comms = data.getString("comms");
            sql = sql + "comms='" + comms + "',isSub=2,";
        }
        int allp = 1;
        InfoModelHelper mysql = null;

        try {
            mysql = InfoModelPool.getModel();
            String sqls = "select * from ding_notice where id='" + id + "'";
            List<JSONObject> list = mysql.query(sqls);
            olds = list.get(0);

            if (users.length() > 0) {
                String reading = olds.getString("reading");
                HashMap<String, String> ring = RIUtil.StringToList(reading);
                String us = olds.getString("users");

                String uss[] = users.split(",");
                String uname = "";
                for (int i = 0; i < uss.length; i++) {
                    String uo = uss[i];

                    ring.put(uo, "");
                    us = us + "," + uo;
                    uname = uname + RIUtil1.users1.get(uo).getString("name");
                }

                sql = sql + " reading='" + RIUtil.HashToList(ring) + "',users='" + us + "', ";

                dingLog(data.getString("opt_user"), "转发：" + uname, mysql, id);

                //在线文档赋权

                link = olds.getString("link");
                String createUser = olds.getString("create_user");
                String optUser = data.getString("opt_user");

                if (link != null && link.length() > 1) {
                    String token = request.getHeader("token");
                    if (!createUser.equals(optUser)) {
                        //token = GetToken(createUser);
                    }
                    String[] links = link.split(",");
                    for (int l = 0; l < links.length; l++) {
                        String s = "select rela_id from upload where id='" + links[l] + "'";
                        String rela_id = mysql.query_one(s, "rela_id");


                        String url = TNOAConf.get("HttpServ", "online_url") + "share";
                        logger.warn(url);
                        try {
                            JSONObject det = new JSONObject();
                            det.put("token", token);
                            det.put("fileId", rela_id);
                            det.put("sfzhList", RIUtil.HashToList(ring));
                            logger.warn(det.toString());

                            OkHttpClient client = new OkHttpClient().newBuilder().build();

                            MediaType mediaType = MediaType.parse("application/json");

                            RequestBody body = RequestBody.create(mediaType, det.toString());


                            Request ret = new Request.Builder().url(url).method("POST", body).addHeader("Content" +
                                    "-Type", "application/json").build();

                            Response response = client.newCall(ret).execute();

                            String back = response.body().string();
                            logger.warn(back + "-->");


                        } catch (Exception ex) {
                            logger.error(Lib.getTrace(ex));
                        }


                    }
                }

                //通知
                //System.out.println(olds);
                int isWechat = olds.getInteger("isWechat");
                if (isWechat == 1) {

                    TestMsgLine send = new TestMsgLine();
                    String url = TNOAConf.get("HttpServ", "notice_url") + id;
                    logger.warn(url);
                    send.sendMSG("您有一条盯办任务，请查收:" + title + "-->" + url, RIUtil.HashToList(ring));
                }


            }


            sqls = "update ding_notice set " + sql + " isdelete=1 where id='" + id + "'";
            logger.warn(sqls);
            mysql.update(sqls);


            if (isAll == 1) {
                sql = "select father_id from ding_notice where id='" + id + "'";
                list = mysql.query(sql);
                if (list.size() > 0) {
                    String father_id = list.get(0).getString("father_id");
                    if (father_id == null || father_id.length() == 0) {
                        father_id = id;
                    }

                    sql = "delete from ding_notice  where father_id='" + father_id + "' and id>'" + id + "' and " +
                            "is_notice=0";
                    logger.warn(sql);
                    mysql.update(sql);
                }
            }

            String formDets = olds.getString("form_dets");
            if (!form_dets.equals(formDets) && isSub.equals("") && comms.equals("")) {
                dingLog(data.getString("opt_user"), "工作", mysql, id);
            }
            if (isSub.equals("1")) {
                dingLog(data.getString("opt_user"), "提交", mysql, id);
            }
            if (comms.length() > 0) {
                dingLog(data.getString("opt_user"), "评价:" + comms, mysql, id);
            }


        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);

    }


    private static void dingLog(String opt_user, String opts, InfoModelHelper mysql, String id) {
        try {
            String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String sql =
                    "insert into ding_form_logs (time,opt_user,opts,ding_id) values ('" + time + "','" + opt_user +
                            "','" + opts + "','" + id + "')";
            mysql.update(sql);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        }
    }

    private static JSONObject upComment(JSONObject data) throws Exception {
        String id = "";
        String opt_user = "";
        String comment = "";

        JSONObject back = ErrNo.set(0);
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(432021);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(432021);
        }
        if (data.containsKey("comment") && data.getString("comment").length() > 0) {
            comment = data.getString("comment");
        } else {
            return ErrNo.set(432021);
        }
        InfoModelHelper mysql = null;
        HashMap<String, String> uu = new HashMap<>();
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";

            sql = "update ding_comment set comment='" + comment + "' where id='" + id + "' " + "and isdelete=1";

            mysql.update(sql);

            UserLog userLog = new UserLog();
            userLog.log(mysql, data.getString("opt_user"), "更新评论：" + id, userLog.TYPE_OPERATE, ip);
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(432022);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject deleteComment(JSONObject data) throws Exception {
        String id = "";
        String opt_user = "";
        int isManager = 0;
        JSONObject back = ErrNo.set(0);

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(432018);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(432018);
        }
        if (data.containsKey("isManager") && data.getString("isManager").length() > 0) {
            isManager = data.getInteger("isManager");
        }
        InfoModelHelper mysql = null;
        HashMap<String, String> uu = new HashMap<>();
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            if (isManager == 1) {
                sql = "update ding_comment set comment='该评论已被管理员删除',delete_time='" + new SimpleDateFormat("yyyy" +
                        "-MM-dd" + " " + "HH:mm:ss").format(new Date()) + "',delete_user='" + opt_user + "' where " + "id='" + id + "' " + "and" + " " + "source=9";
            } else {
                sql =
                        "update ding_comment set isdelete=2,delete_time='" + new SimpleDateFormat("yyyy-MM-dd " + "HH" +
                                ":mm:ss").format(new Date()) + "',delete_user='" + opt_user + "' where id='" + id +
                                "'" + " " + "and"
                                + " " + "source=9";
            }
            mysql.update(sql);

            UserLog userLog = new UserLog();
            userLog.log(mysql, data.getString("opt_user"), "删除评论：" + id, userLog.TYPE_OPERATE, ip);
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(432019);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    private static JSONObject deleteNotice(JSONObject data) throws Exception {
        String id = "";
        String opt_user = "";
        int isAll = 0;
        JSONObject back = ErrNo.set(0);
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(432016);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(432016);
        }

        if (data.containsKey("is_all") && data.getString("is_all").length() > 0) {
            isAll = data.getInteger("is_all");
        }
        InfoModelHelper mysql = null;
        HashMap<String, String> uu = new HashMap<>();
        try {
            mysql = InfoModelPool.getModel();
            if (isAll == 0) {
                String sql =
                        "update ding_notice set isdelete=2,delete_time='" + new SimpleDateFormat("yyyy-MM-dd " + "HH" + ":mm"
                                + ":ss").format(new Date()) + "',delete_user='" + opt_user + "' where " + "id='" + id + "'";
                mysql.update(sql);
            } else {
                String sql = "select father_id from ding_notice where id='" + id + "'";
                List<JSONObject> list = mysql.query(sql);
                if (list.size() > 0) {
                    String father_id = list.get(0).getString("father_id");
                    if (father_id == null || father_id.length() == 0 || father_id.equals("0")) {
                        father_id = id;
                    }

                    sql = "update ding_notice set isdelete=2,delete_time='" + new SimpleDateFormat("yyyy-MM-dd " +
                            "HH:mm:ss").format(new Date()) + "',delete_user='" + opt_user + "' where (" + "(father_id"
                            + "='" + father_id + "' or id ='" + id + "') )";
                    logger.warn(sql);
                    mysql.update(sql);
                    sql = "select check_time from ding_notice where father_id='" + father_id + "' and isdelete=1 " +
                            "order by " + "check_time desc limit 1";
                    list = mysql.query(sql);
                    if (list.size() > 0) {
                        String end_time = list.get(0).getString("check_time");
                        sql = "update ding_notice set cycle_end='" + end_time + "' where father_id='" + father_id +
                                "' " + "and" + " isdelete=1";
                        mysql.update(sql);
                    }
                }
            }

            String sql = "update upload set isMain=0 where rela_id='" + id + "'";
            mysql.update(sql);

            UserLog userLog = new UserLog();
            userLog.log(mysql, data.getString("opt_user"), "删除公告：" + id, userLog.TYPE_OPERATE, ip);
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject GetDingReading(JSONObject d) throws Exception {
        String id = "";
        JSONObject back = ErrNo.set(0);
        JSONObject data = d;
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(432014);
        }
        InfoModelHelper mysql = null;
        HashMap<String, String> uu = new HashMap<>();
        try {
            mysql = InfoModelPool.getModel();

            String sql = "select reading from ding_notice where id='" + id + "' and isdelete=1 and is_notice=1";

            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                JSONObject one = mysql.query(sql).get(0);
                JSONArray readingList = new JSONArray();
                String reading = one.getString("reading");
                // logger.warn(reading);
                if (reading.length() > 0) {
                    readingList = UseridToNames(RIUtil.StringToList(reading));

                }
                back.put("reading", readingList);
            } else {
                return ErrNo.set(432015);
            }
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject updateDingReader(JSONObject data) throws Exception {
        String id = "";
        String user_id = "";

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(432011);
        }
        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id").trim();
        } else {
            return ErrNo.set(432011);
        }
        JSONObject back = ErrNo.set(0);

        InfoModelHelper mysql = null;
        HashMap<String, String> uu = new HashMap<>();
        try {
            mysql = InfoModelPool.getModel();

            String sql = "select reading,readed from ding_notice where id='" + id + "' and isdelete=1 and " +
                    "is_notice=1";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {

                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    HashMap<String, String> reading = RIUtil.StringToList(one.getString("reading"));
                    HashMap<String, String> readed = RIUtil.StringToList(one.getString("readed"));
                    logger.warn("reading->" + reading.toString());
                    logger.warn("readed->" + readed.toString());
                    HashMap<String, String> readingn = new HashMap<>();
                    if (reading.size() > 0) {
                        for (Map.Entry<String, String> oner : reading.entrySet()) {

                            String uid = oner.getKey().trim();
                            if (uid.equals(user_id)) {
                                readingn.remove(uid);
                                readed.put(user_id, "");
                            } else {
                                readingn.put(uid, "");
                            }
                            //  logger.warn(reading.size() + "." + readingn.size());
                        }
                        //  logger.warn("reading->" + readingn.toString());
                        //logger.warn("readed->" + readed.toString());
                        sql =
                                "update ding_notice set reading='" + RIUtil.HashToList(readingn) + "',readed='" + RIUtil.HashToList(readed) + "' where id='" + id + "'";
                        mysql.update(sql);
                        dingLog(user_id, "签收", mysql, id);

                    }
                }
                JSONObject gg = new JSONObject();
                gg.put("id", id);
                JSONObject bb = GetDingReading(gg);
                JSONArray reading = bb.getJSONArray("reading");
                back.put("reading", reading);

            } else {
                return ErrNo.set(432013);
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;

    }

    private static JSONObject createDing(JSONObject data, TNOAHttpRequest request) throws Exception {
        String title = "";
        String content = "";
        String create_user = "";
        String notice_time = "";
        String is_notice = "0";
        String reading = "";

        String groups = "";
        String users = "";
        String comment_type = "1";
        String comment_select = "";
        String isTop = "0";
        String isNew = "1";
        String top_date = "1";
        String new_date = "1";
        String type = "";
        String subType = "";
        String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String isdelete = "1";
        String link = "";
        int sendWechat = 0;
        int sendMsg = 0;
        String img = "";
        String accessory = "";
        int cycle = 0;
        int cycle_days = 0;
        long cycle_seconds = 0;
        String cycle_end = "";
        String label = "";
        String check_time = "";
        String unit = "";
        String source = "1";
        String old_author = "";
        String form_dets = "";
        String auto_id = "";
        JSONArray ding_label = new JSONArray();

        if (data.containsKey("ding_label") && !ObjectUtils.isEmpty("ding_label")) {
            ding_label = data.getJSONArray("ding_label");
        }

        if (data.containsKey("title") && data.getString("title").length() > 0) {
            title = data.getString("title");
        }
        if (data.containsKey("content") && data.getString("content").length() > 0) {
            content = data.getString("content");
        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
        }
        if (data.containsKey("notice_time") && data.getString("notice_time").length() > 0) {
            notice_time = data.getString("notice_time").replace("|", " ");

        } else {
            notice_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        }
        long t = RIUtil.dateToStamp(notice_time);
        t = t + 1000 * 60 * 60 * 24;
        check_time = RIUtil.stampToTime(t);
        if (data.containsKey("groups") && data.getString("groups").length() > 0) {
            groups = data.getString("groups");
        }
        if (data.containsKey("users") && data.getString("users").length() > 0) {
            users = data.getString("users");
        }
        if (data.containsKey("comment_type") && data.getString("comment_type").length() > 0) {
            comment_type = data.getString("comment_type");
        }
        if (data.containsKey("comment_select") && data.getString("comment_select").length() > 0) {
            comment_select = data.getString("comment_select");
        }
        if (data.containsKey("isTop") && data.getString("isTop").length() > 0) {
            isTop = data.getString("isTop");
        }
        if (data.containsKey("isNew") && data.getString("isNew").length() > 0) {
            isNew = data.getString("isNew");
        }
        if (data.containsKey("top_date") && data.getString("top_date").length() > 0) {
            top_date = data.getString("top_date");
        }
        if (data.containsKey("new_date") && data.getString("new_date").length() > 0) {
            new_date = data.getString("new_date");
        }
        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
        }
        if (data.containsKey("subType") && data.getString("subType").length() > 0) {
            subType = data.getString("subType");
        }
        if (data.containsKey("isWechat") && data.getString("isWechat").length() > 0) {
            sendWechat = data.getInteger("isWechat");
        }
        if (data.containsKey("isMsg") && data.getString("isMsg").length() > 0) {
            sendMsg = data.getInteger("isMsg");
        }
        if (data.containsKey("img") && data.getString("img").length() > 0) {
            img = data.getString("img");
        }
        if (data.containsKey("accessory") && data.getString("accessory").length() > 0) {
            accessory = data.getString("accessory");
        }
        if (data.containsKey("link") && data.getString("link").length() > 0) {
            link = data.getString("link");
        }
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
        }

        if (data.containsKey("label") && data.getString("label").length() > 0) {
            label = data.getString("label");
        }

        if (data.containsKey("source") && data.getString("source").length() > 0) {
            source = data.getString("source");
        }
        if (data.containsKey("old_author") && data.getString("old_author").length() > 0) {
            old_author = data.getString("old_author");
        }
        String temp_id = "";
        if (data.containsKey("temp_id") && data.getString("temp_id").length() > 0) {
            temp_id = data.getString("temp_id");
        }
        if (data.containsKey("form_dets") && data.getString("form_dets").length() > 0) {
            form_dets = data.getString("form_dets");
        }
        if (data.containsKey("auto_id") && data.getString("auto_id").length() > 0) {
            auto_id = data.getString("auto_id").replace("|", " ");

        }
        if (data.containsKey("check_time") && data.getString("check_time").length() > 0) {
            check_time = data.getString("check_time").replace("|", " ");

        } else {
            long ct = RIUtil.dateToStamp(notice_time) + 24 * 60 * 60 * 1000;
            check_time = RIUtil.stampToTime(ct);
        }
        if (data.containsKey("cycle_end") && data.getString("cycle_end").length() > 0) {
            cycle_end = data.getString("cycle_end").replace("|", " ");
        } else {
            int year = Integer.parseInt(new SimpleDateFormat("yyyy").format(new Date())) + 2;
            cycle_end = year + "-12-31 23:59:59";
        }
        if (data.containsKey("cycle") && data.getString("cycle").length() > 0) {
            cycle = data.getInteger("cycle");
            if (cycle > 0) {
                String date_time[] = notice_time.split(" ");

                String date = date_time[0];

                if (check_time.length() == 0) {
                    check_time = date + " 23:59:59";
                }
                if (cycle == 8) {
                    if (data.containsKey("cycle_days") && data.getString("cycle_days").length() > 0) {
                        cycle_days = data.getInteger("cycle_days");
                    } else {
                        return ErrNo.set(433012);
                    }
                } else {
                    cycle_days = cycle_days_list[cycle];
                }


            } else {
                cycle_end = check_time;
            }

        }
        cycle_seconds = RIUtil.dateToStamp(check_time) - RIUtil.dateToStamp(notice_time);
        logger.warn(RIUtil.dateToStamp(check_time) + "-" + RIUtil.dateToStamp(notice_time) + "=" + cycle_seconds);
        if (cycle_seconds < 0) {
            logger.error("startTime bigger than endTime");
            return ErrNo.set(null, 2, "startTime bigger than endTime");
        }

        int isAll = 1;
        InfoModelHelper mysql = null;
        HashMap<String, String> uu = new HashMap<>();
        try {
            mysql = InfoModelPool.getModel();

            // JSONArray userList = UseridToNames(uu, mysql, 1);

            if (users.length() == 0 && groups.length() == 0) {
                String sqlT = " and 1=1 ";
                if (!type.equals("5")) {
                    sqlT = " and length(isFamily)=0 ";
                }
                String s =
                        "select id from user where isdelete=1 and status=1 and (unit='" + unit + "' or org='" + unit + "') " + "and id!='1' and position not like '%18%'" + sqlT;
                List<JSONObject> list = mysql.query(s);
                for (int i = 0; i < list.size(); i++) {
                    users = users + list.get(i).getString("id") + ",";
                }
                if (users.length() > 1) {
                    users = users.substring(0, users.length() - 1);
                }
            } else {
                isAll = 0;
            }

            String yyyy = new SimpleDateFormat("yyyy").format(new Date());
            String code = "";
            int tt = RIUtil.dicts.get(unit).getInteger("type");
            if (tt == 21 || tt == 22 || tt == 27 || tt == 25 || tt == 26 || tt == 23) {
                code = unit.substring(0, 8) + "0000";
            } else {
                code = unit.substring(0, 6) + "000000";
            }


            String s =
                    "select num from ding_num where `year`='" + yyyy + "' and `code`='" + code + "' and type='" + subType +
                            "' " + "order by num  desc  limit 1";
            logger.warn(s);
            String n = mysql.query_one(s, "num");
            int num = 0;
            if (n.length() > 0) {
                num = Integer.parseInt(n) + 1;
            } else {
                num = 1;

            }
            s = "insert into ding_num  (`code`,num,`year`, `type`) values('" + code + "','" + num + "','" + yyyy +
                    "','" + subType + "')";
            logger.warn(s);
            mysql.update(s);


            String sql =
                    "insert ding_notice (title,content,create_user,notice_time,is_notice,reading,readed,groups," +
                            "users," + "comment_type,comment_select,isTop,isNew,top_date,new_date,type," +
                            "create_time," +
                            "isdelete," + "isWechat,isMsg,img,isAll,accessory,link,check_time," + "cycle,cycle_days," +
                            "cycle_seconds," + "cycle_end,label,unit,source,subType,old_author," + "temp_id," +
                            "form_dets," + "ding_num,auto_id,ding_label)values('" + title + "','" + content + "'," +
                            "'" + create_user + "'," + "'" + notice_time + "','" + is_notice + "'," + "'',''," + "'" + groups + "','" + users + "','" + comment_type + "','" + comment_select + "'," + "'" + isTop + "','" + isNew + "'," + "'" + top_date + "','" + new_date + "','" + type + "','" + create_time + "','" + isdelete + "','" + sendWechat + "','" + sendMsg + "','" + img + "','" + isAll + "','" + accessory + "','" + link + "','" + check_time + "'," + "'" + cycle + "','" + cycle_days + "','" + cycle_seconds + "','" + cycle_end + "','" + label + "','" + unit + "','" + source + "','" + subType + "','" + old_author + "','" + temp_id + "','" + form_dets + "','" + num + "','" + auto_id + "','" + JSONArray.toJSONString(ding_label) + "') ";
            mysql.update(sql);
            sql = "select id from ding_notice where title='" + title + "' and " + "create_user='" + create_user + "' "
                    + "and create_time='" + create_time + "' and users='" + users + "'";
            List<JSONObject> list = mysql.query(sql);
            String id = list.get(0).getString("id");
            if (img.length() > 0) {
                JSONArray imgs = JSONArray.parseArray(img);
                for (int i = 0; i < imgs.size(); i++) {
                    JSONObject one = imgs.getJSONObject(i);
                    String img_id = one.getString("id");

                    int isMain = one.getInteger("isMain");

                    sql = "update upload set rela_id='" + id + "',isMain='" + isMain + "' where id='" + img_id + "'";
                    mysql.update(sql);
                }
            }
            if (cycle > 0) {
                MakeCycleDing(id, 10, mysql);
            }
            String token = request.getHeader("token");
            publishDing(token);

            dingLog(create_user, "发起", mysql, id);

            //通知
            //System.out.println(olds);

            if (sendWechat == 1) {
                TestMsgLine send = new TestMsgLine();
                String url = TNOAConf.get("HttpServ", "notice_url") + type + "__" + id;
                logger.warn(url);
                send.sendMSG("您有一条盯办任务，请查收:" + title + "-->" + url, Arrays.asList(users.split(",")));
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 432001, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

    private static void MakeCycleDing(String id, int count, InfoModelHelper mysql) throws Exception {
        String father_id = id;
        String sql = "select notice_time,check_time,cycle_end,cycle,cycle_days,cycle_seconds,source,unit,auto_id," +
                "ding_label,subType from ding_notice " + "where" + " " + "id='" + father_id + "'";
        List<JSONObject> list = mysql.query(sql);
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String notice_time = one.getString("notice_time");
            long start_time_long = RIUtil.dateToStamp(notice_time);
            String cycle_end = one.getString("cycle_end");
            String check_time = one.getString("check_time");
            int cycle_days = one.getInteger("cycle_days");
            int cycle = one.getInteger("cycle");
            int cycle_seconds = one.getInteger("cycle_seconds");
            String unit = one.getString("unit");
            String type = one.getString("subType");

            int mark = 0;
            while (start_time_long < RIUtil.dateToStamp(cycle_end)) {
                // logger.warn(start_time + "->" + end_time);
                if (cycle != 4 && cycle != 7) {
                    notice_time = RIUtil.GetNextDateTime(notice_time, cycle_days);


                } else if (cycle == 4) {//每月
                    String d[] = notice_time.split(" ");
                    String date = d[0];
                    String time = d[1];
                    String[] days = date.split("-");
                    int month = Integer.parseInt(days[1]);
                    int day = Integer.parseInt(days[2]);
                    int year = Integer.parseInt(days[0]);
                    if (day > 28) {
                        day = 28;
                    }
                    if (month < 12) {
                        month = month + 1;
                    } else {
                        month = 1;
                        year = year + 1;
                    }
                    String monthStr = "";
                    if (month < 10) {
                        monthStr = "0" + month;
                    } else {
                        monthStr = String.valueOf(month);
                    }
                    String dayStr = "";
                    if (day < 10) {
                        dayStr = "0" + day;
                    } else {
                        dayStr = String.valueOf(day);
                    }
                    notice_time = year + "-" + monthStr + "-" + dayStr + " " + time;


                } else if (cycle == 7)//每年
                {
                    String d[] = notice_time.split(" ");
                    String date = d[0];
                    String time = d[1];
                    String[] days = date.split("-");
                    int month = Integer.parseInt(days[1]);
                    int day = Integer.parseInt(days[2]);
                    int year = Integer.parseInt(days[0]);
                    notice_time = (year + 1) + "-" + days[1] + "-" + days[2] + " " + time;
                }
                start_time_long = RIUtil.dateToStamp(notice_time);
                if (start_time_long < RIUtil.dateToStamp(cycle_end)) {
                    long end_time_long = start_time_long + cycle_seconds;
                    check_time = RIUtil.stampToTime(end_time_long);

                    // logger.warn(start_time + "->" + end_time);


                    String code = "";
                    int tt = RIUtil.dicts.get(unit).getInteger("type");
                    if (tt == 21 || tt == 22 || tt == 27 || tt == 25 || tt == 26 || tt == 23) {
                        code = unit.substring(0, 8) + "0000";
                    } else {
                        code = unit.substring(0, 6) + "000000";
                    }


                    String s = "select num from ding_num where `year`='" + notice_time.substring(0, 4) + "' and " +
                            "`code`='" + code + "'" + " and " + "type='" + type + "' " + "order by num  desc  " +
                            "limit 1";
                    logger.warn(s);
                    String n = mysql.query_one(s, "num");
                    int num = 0;
                    if (n.length() > 0) {
                        num = Integer.parseInt(n) + 1;
                    } else {
                        num = 1;

                    }
                    s =
                            "insert into ding_num  (`code`,num,`year`, `type`) values('" + code + "','" + num + "','" + notice_time.substring(0, 4) + "','" + type + "')";
                    logger.warn(s);
                    mysql.update(s);

                    String sqls = "insert ding_notice (title,content,create_user,notice_time,is_notice,reading," +
                            "readed," + "groups,users,comment_type,comment_select," + "isTop,isNew,top_date," +
                            "new_date," + "type," + "create_time,isdelete,isWechat,isMsg,img,isAll,accessory,link," + "check_time," + "cycle," + "cycle_days,cycle_seconds,cycle_end,father_id,source,temp_id," + "ding_num," + "auto_id,ding_label,subType)" + "select " + "title,content," + "create_user,'" + notice_time + "' as notice_time,'0' as is_notice," + "'','',groups," + "users," + "comment_type,comment_select," + "isTop,isNew,top_date," + "new_date," + "type,'" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' as " + "create_time,isdelete,isWechat,isMsg,img,isAll,accessory,link,'" + check_time + "'" + " as check_time," + "cycle,cycle_days,cycle_seconds,cycle_end,'" + father_id + "'" + " as" + " father_id,source,temp_id,'" + num + "' as ding_num,auto_id,ding_label," + "subType from ding_notice where id='" + father_id + "'";
                    logger.warn(sqls);
                    mysql.update(sqls);
                    if (count != -1) {//全部
                        mark++;
                        if (mark > count) {
                            break;
                        }
                    }
                }
            }
        }

    }

    static JSONObject getDingList(JSONObject data) throws Exception {
        String id = "";

        String content = "";
        String create_user = "";
        String is_notice = "1";
        String isTop = "";
        String isNew = "";
        String isWechat = "";
        String type = "";
        String user_id = "";
        int limit = 20;
        int page = 1;
        String sql = "";
        String label = "";
        String unit = "";
        String create_unit = "";
        int tt = -1;

        String create_time_start = "";
        String create_time_end = "";
        String opt_user = "";
        String isRead = "";//1未读 2已读
        int isAll = 0;


        if (data.containsKey("isAll") && data.getString("isAll").length() > 0) {
            isAll = data.getInteger("isAll");
        }

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
            sql = sql + " a.id ='" + id + "' and ";
        }

        if (data.containsKey("content") && data.getString("content").length() > 0) {
            content = data.getString("content");
            sql = sql + " (a.title like'%" + content + "%' or a.content like'%" + content + "%' and ding_num like" +
                    " " + "'%" + content + "%') " + "and";
        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
            sql = sql + " a.create_user= '" + create_user + "' and ";
        }
        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");
            sql = sql + " (a.reading like '%" + user_id + "%' or a.readed like '%" + user_id + "%') and ";
        }

        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        }

        if (data.containsKey("isRead") && data.getString("isRead").length() > 0) {
            isRead = data.getString("isRead");
            if (isRead.equals("1")) {
                sql = sql + " a.reading like '%" + user_id + "%'  and ";
            } else {
                sql = sql + "a.readed like '%" + user_id + "%' and ";
            }
        }

        if (data.containsKey("is_notice") && data.getString("is_notice").length() > 0) {
            is_notice = data.getString("is_notice");
        }
        sql = sql + " a.is_notice='" + is_notice + "' and ";
        if (data.containsKey("isTop") && data.getString("isTop").length() > 0) {
            isTop = data.getString("isTop");
            sql = sql + " a.isTop='" + isTop + "' and ";
        }
        if (data.containsKey("isWechat") && data.getString("isWechat").length() > 0) {
            isWechat = data.getString("isWechat");
            sql = sql + " a.isWechat='" + isWechat + "' and ";
        }
        if (data.containsKey("isNew") && data.getString("isNew").length() > 0) {
            isNew = data.getString("isNew");
            sql = sql + " a.isNew='" + isNew + "' and ";
        }

        if (isAll == 1) {
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
//                int t = RIUtil.dicts.get(unit).getIntValue("type");
//                if (t == 22 || t == 27) {//支队
////                sql = sql + "a.unit = '320400000000' and ";
//                } else if (t == 23 || t == 24 || t == 28) {//所在分局和派出所和市局
//                    sql = sql + "(a.unit like concat('%','" + unit.substring(0, 6) + "','%') or b.type = 21 or b
//                    .type =22 or b.type =27) and ";
//                } else if (t == 25 || t == 26) {//所在派出所和所在分局和市局
//                    sql = sql + "(a.unit like concat('%','" + unit.substring(0, 8) + "','%') or (a.unit like concat
//                    ('%','" + unit.substring(0, 6) + "','%') and (b.type =23 or b.type = 24 or b.type = 28)) or b
//                    .type = 21 or b.type =22 or b.type =27)and ";
//                }
            }
        } else {
            if (data.containsKey("create_unit") && data.getString("create_unit").length() > 0) {
                create_unit = data.getString("create_unit");
                tt = RIUtil.dicts.get(create_unit).getIntValue("type");
                if (tt == 22 || tt == 27 || tt == 21) {//支队
                    create_unit = "320400000000";
                    sql = sql + "a.unit like concat('%','" + create_unit.substring(0, 6) + "','%') and ";
                } else if (tt == 23 || tt == 24 || tt == 28) {
                    create_unit = create_unit.substring(0, 6) + "000000";
                    sql = sql + "a.unit like concat('%','" + create_unit.substring(0, 6) + "','%') and ";
                } else if (tt == 25 || tt == 26) {
                    create_unit = create_unit.substring(0, 8) + "0000";
                    sql = sql + "a.unit like concat('%','" + create_unit.substring(0, 8) + "','%') and ";
                }

            } else {
                tt = 0;
            }
            if (StringUtil.isBlank(create_unit) && data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " a.unit like '%" + unit + "%' and ";
            }
        }


        if (data.containsKey("label") && data.getString("label").length() > 0) {
            label = data.getString("label");
            sql = sql + " a.label ='" + label + "' and ";
        }

        String order = " case when (a.reading like concat ('%','" + opt_user + "','%')) then 2 else 1 end desc, a" +
                ".isTop" + " desc,a.notice_time desc";
        if (data.containsKey("order") && data.getString("order").length() > 0) {
            order = data.getString("order").replace("|", " ");

        }

        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
            sql = sql + " a.type='" + type + "' and ";
        }
        String subType = "";
        if (data.containsKey("subType") && data.getString("subType").length() > 0) {
            subType = data.getString("subType");
            sql = sql + " a.subType='" + subType + "' and ";
        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
            sql = sql + " a.create_user='" + create_user + "' and ";
        }
        if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
            create_time_start = data.getString("create_time_start").replace("|", " ");
            sql = sql + " a.create_time>='" + create_time_start + "' and ";
        }
        if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
            create_time_end = data.getString("create_time_end").replace("|", " ");
            sql = sql + " a.create_time<='" + create_time_end + "' and ";
        }
        if (data.containsKey("limit") && data.getString("limit").length() > 0) {
            limit = data.getInteger("limit");

        }
        if (data.containsKey("page") && data.getString("page").length() > 0) {
            page = data.getInteger("page");

        }


        MysqlHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = new MysqlHelper("mysql");
            String sqls =
                    "select a.unit,a.id,a.content as content,a.title as title,a.create_user,a.isTop,a.isNew," + "a" +
                            ".notice_time,a.readed,a.reading,a.label,a.type,a.subType,a.cycle,a.link ," + "a" +
                            ".old_author,a" +
                            ".isSub,a" + ".comms,a.ding_label,a.auto_id,a.check_time from ding_notice a " + " where" + sql + " " +
                            "a.isdelete" + "=1  " + "order by " + order;

            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
//            logger.warn(list.toString());
            if (list.size() > 0) {
                String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                for (JSONObject jsonObject : list) {
                    jsonObject.put("ding_label", jsonObject.getJSONArray("ding_label"));
                    sqls =
                            "select * from ding_favo where favo_user = '" + opt_user + "' and ding_id = '" + jsonObject.getString("id") + "'";
                    List<JSONObject> query = mysql.query(sqls);
                    if (query.size() > 0) jsonObject.put("favo", 1);
                    else jsonObject.put("favo", 0);

                    //判断是否逾期
                    jsonObject.put("isLate", "0");
                    sqls = "select time,opt_user,opts from ding_form_logs where ding_id='" + jsonObject.getString("id"
                    ) + "' and opts ='提交'";
                    query = mysql.query(sqls);
                    //有提交 判断提交时间是否超过check_time
                    if (query.size() > 0 && ((query.get(0).getString("time")).compareTo(jsonObject.getString(
                            "check_time")) > 0)) {
                        jsonObject.put("isLate", "2");
                    }
                    //无提交 判断当前时间是否超过check_time
                    else if (query.size() == 0 && jsonObject.getString("check_time").compareTo(date) < 0) {
                        jsonObject.put("isLate", "1");
                    }
                }

                if (isAll != 1) {
                    if (create_unit.length() > 0) {
                        int finalTt = tt;
                        list = list.stream().filter(ding -> {
                            ding.put("create_unit", "");
                            int t = 1;
                            if (ding.containsKey("unit") && StringUtil.isNotBlank(ding.getString("unit"))) {
                                if (RIUtil.dicts.get(ding.getString("unit")).containsKey("type")) {
                                    t = RIUtil.dicts.get(ding.getString("unit")).getIntValue("type");
                                }
                            }

                            if ((t == 22 || t == 27 || t == 21) && (finalTt == 22 || finalTt == 27 || finalTt == 21)) {//支队
                                return true;
                            } else if ((t == 23 || t == 24 || t == 28) && (finalTt == 23 || finalTt == 24 || finalTt == 28)) {
                                return true;
                            } else if ((t == 25 || t == 26) && (finalTt == 25 || finalTt == 26)) {
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());
                    }
                } else {
                    int t = RIUtil.dicts.get(unit).getIntValue("type");
                    if (t == 22 || t == 27) {//支队

                    } else if (t == 23 || t == 24 || t == 28) {//所在分局和分局所有派出所
//                        sql = sql + "(a.unit like concat('%','" + unit.substring(0, 6) + "','%') or b.type = 21 or
//                        b.type =22 or b.type =27) and ";
                        String finalUnit = unit;
                        list = list.stream().filter(ding -> {
                            JSONObject jsonObject = RIUtil1.users1.get(ding.getString("create_user"));
//                            String[] split = jsonObject.getString("unit").split("，");
//                            for (String string : split) {
//                                if(StringUtil.isNotBlank(string)) {
//                                    int type1 = RIUtil.dicts.get(string).getIntValue("type");
//                                    if (type1 == 21 || type1 == 22 || type1 == 27)
//                                        return true;
//                                }
//                            }
                            if (jsonObject.getString("unit").contains(finalUnit.substring(0, 6))) {
                                return true;
                            }
                            return false;

                        }).collect(Collectors.toList());
                    } else if (t == 25 || t == 26) {//所在派出所
                        String finalUnit = unit;
                        list = list.stream().filter(ding -> {
                            JSONObject jsonObject = RIUtil1.users1.get(ding.getString("create_user"));
//                            String[] split =jsonObject.getString("unit").split("，");
//                            for (String string : split) {
//                                if(StringUtil.isNotBlank(string)) {
//                                    int type1 = RIUtil.dicts.get(string).getIntValue("type");
//                                    if (type1 == 21 || type1 == 22 || type1 == 27)
//                                        return true;
//                                    if ((type1 == 23 || type1 == 24 || type1 == 28) && jsonObject.getString("unit")
//                                    .contains(finalUnit.substring(0, 6)))
//                                        return true;
//                                }
//                            }
                            if (jsonObject.getString("unit").contains(finalUnit.substring(0, 8))) {
                                return true;
                            }
                            return false;

                        }).collect(Collectors.toList());
                    }
                }

                back.put("count", list.size());
                list = list.stream().skip((page - 1) * limit).limit(limit).collect(Collectors.toList());
                back.put("data", RelaInfoList(list, mysql, opt_user));

            } else {
                list = list.stream().skip((page - 1) * limit).limit(limit).collect(Collectors.toList());
                back.put("data", list);
                back.put("count", 0);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 432004, Lib.getTrace(e));
        } finally {
            mysql.close();
        }
        return back;


    }

    private static Object RelaInfoList(List<JSONObject> list, MysqlHelper mysql, String opt_user) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String id = one.getString("id");
            //create_user
            String create_user = one.getString("create_user");
//            String sql = "select id_num from user where id = '" + create_user + "' ";
//            String idNum = mysql.query_one("id_num", sql);
            one.put("create_user", RIUtil1.users1.get(create_user));

            String old_author = one.getString("old_author");

            try {
                one.put("old_author", RIUtil1.users1.get(old_author));
            } catch (Exception ex) {
                one.put("old_author", new JSONObject());
            }
            String readed = one.getString("readed");
            if (readed.length() > 0) {
                one.put("readed", UseridToNames(RIUtil.StringToList(readed)));
            }
            //未读
            String reading = one.getString("reading");
            if (reading.length() > 0) {
                JSONArray readingJ = UseridToNames(RIUtil.StringToList(reading));
                one.put("reading", readingJ);
            } else {
                one.put("reading", new JSONArray());
            }

           /* String content = one.getString("content");
            if (content.length() > 300) {
                content = content.substring(0, 299);
            }
            one.put("content", content);*/

            //标签
            if (one.containsKey("label")) {
                String label = one.getString("label");
                if (label.length() > 0) {

                    String labelOne = RealDictNames(RIUtil.StringToList(label));

                    one.put("label", labelOne);
                    one.put("label_id", label);
                } else {
                    one.put("label", "");
                    one.put("label_id", "");
                }
            } else {
                one.put("label", "");
                one.put("label_id", "");
            }

            if (one.containsKey("unit")) {
                String unit = one.getString("unit");
                one.put("unit_name", RealDictNames(RIUtil.StringToList(unit)));
            } else {
                one.put("unit_name", "");
            }


            if (one.containsKey("type")) {
                String type = one.getString("type");
                one.put("type", RIUtil.dicts.get(type));
            } else {
                one.put("type_name", new JSONObject());
            }

            if (one.containsKey("subType")) {
                String subType = one.getString("subType");
                one.put("subType", RIUtil.dicts.get(subType));
            } else {
                one.put("subType_name", new JSONObject());
            }


            //关联ding
            int count = 0;
            if (opt_user != null && opt_user.length() > 0) {
                String sql =
                        "select count(id) as count from ding_msg where readed=0 and real_id='" + id + "' and " +
                                "accepter" + "='" + opt_user + "'";
                count = mysql.query_count(sql);

            }
            one.put("ding", count);

            //reading count
//            String reading = one.getString("reading");
//            if (reading.length() > 0) {
//                one.put("reading", UseridToNames(RIUtil.StringToList(reading)).size());
//            }
            //comment

            String sql = "select count(id) as count from comment where notice_id='" + id + "' and isdelete=1 ";
            count = mysql.query_count(sql);

            one.put("comment", count);

            back.add(one);

        }
        return back;
    }

    public static JSONObject getDing(JSONObject data) throws Exception {
        String id = "";

        String content = "";
        String create_user = "";
        String is_notice = "1";
        String isTop = "";
        String isNew = "";
        String type = "1";

        String sql = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
            sql = sql + " a.id ='" + id + "' and ";
        }

        if (data.containsKey("content") && data.getString("content").length() > 0) {
            content = data.getString("content");
            sql = sql + " a.title like'%" + content + "%' " + "or a.content like'%" + content + "%') and";
        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
            sql = sql + " a.create_user like '%" + create_user + "%' and ";
        }

        if (data.containsKey("is_notice") && data.getString("is_notice").length() > 0) {
            is_notice = data.getString("is_notice");
        }
        sql = sql + " a.is_notice='" + is_notice + "' and ";
        if (data.containsKey("isTop") && data.getString("isTop").length() > 0) {
            isTop = data.getString("isTop");
            sql = sql + " a.isTop='" + isTop + "' and ";
        }
        if (data.containsKey("isNew") && data.getString("isNew").length() > 0) {
            isNew = data.getString("isNew");
            sql = sql + " a.isNew='" + isNew + "' and ";
        }

        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
            sql = sql + " a.type='" + type + "' and ";
        }

        String opt_user = "";
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");

        }

        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "select a.id,a.create_user,a.notice_time,a.is_notice,a.reading,a.readed,a.groups,a.users,a" +
                            ".comment_type,a.comment_select,a.isTop,a.isNew,a.top_date,a.new_date,a.type,a" +
                            ".isWechat," + "isMsg,"
                            + "a" + ".create_time,a.isBrief,a.img,a.isAll,a.mark,a.accessory," + "a.link,a" +
                            ".check_time," + "a" + ".cycle,a" + ".cycle_days," + "a.cycle_seconds,a" + ".cycle_end,a" + ".father_id,a" + ".label," + "a.unit,a" + ".source,a" + ".title," + "a" + ".content,a" + ".subType" + " ,a" + ".old_author,a" + ".temp_id,a" + ".form_dets,a.isSub,a.comms,a" + ".ding_num,a" + ".ding_label ,a" + ".auto_id from " + "ding_notice a " + "where 1=1 and " + sql + " a.isdelete=1  " + "order" + " " + "by " + "a.isTop " + "desc," + "a" + ".isNew" + " desc,a.notice_time desc";

            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            //logger.warn(sqls);
            if (list.size() > 0) {
                for (JSONObject jsonObject : list) {
                    jsonObject.put("ding_label", jsonObject.getJSONArray("ding_label"));
                }
                back.put("data", RelaInfo(list, mysql));

            } else {
                back.put("data", list);

            }

            sql = "update ding_msg set readed=1 where  real_id='" + id + "' and " + "accepter='" + opt_user + "'";
            mysql.update(sql);


        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 432004, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;


    }

    private static List<JSONObject> RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            //待阅读人
            String users = one.getString("users");
            if (users.length() > 3) {
                JSONArray userList = UseridToNames(RIUtil.StringToList(users));
                one.put("users", userList);
            }
            //待阅读圈层
            String groups = one.getString("groups");
            // logger.warn(groups);
            if (groups.length() > 0) {
                JSONArray groupList = RIUtil.GroupsToName(RIUtil.StringToList(groups), mysql);
                one.put("groups", groupList);
            }
            //已读
            String readed = one.getString("readed");
            if (readed.length() > 0) {
                JSONArray readedJ = UseridToNames(RIUtil.StringToList(readed));
                one.put("readed", readedJ);
            } else {
                one.put("readed", new JSONArray());
            }
            //未读
            String reading = one.getString("reading");
            if (reading.length() > 0) {
                logger.warn(reading);
                JSONArray readingJ = UseridToNames(RIUtil.StringToList(reading));
                one.put("reading", readingJ);
            } else {
                one.put("reading", new JSONArray());
            }


            if (one.containsKey("type")) {
                String type = one.getString("type");
                one.put("type", RIUtil.dicts.get(type));
            } else {
                one.put("type_name", new JSONObject());
            }

            if (one.containsKey("subType")) {
                String subType = one.getString("subType");
                one.put("subType", RIUtil.dicts.get(subType));
            } else {
                one.put("subType_name", new JSONObject());
            }


            //create_user  身份证号
            String create_user = one.getString("create_user");

//            String sql = "select id_num from user where id = '" + create_user + "' ";
//            String idNum = mysql.query_one("id_num", sql);
            one.put("create_user", RIUtil1.users1.get(create_user));


            String old_author = one.getString("old_author");

            try {
                one.put("old_author", RIUtil1.users1.get(old_author));
            } catch (Exception ex) {
                one.put("old_author", new JSONObject());
            }
            //accessory
            String accessory = one.getString("accessory");
            JSONArray access = new JSONArray();
            if (accessory.length() > 0) {

                HashMap<String, String> accesss = RIUtil.StringToList(accessory);
                for (Map.Entry<String, String> a : accesss.entrySet()) {
                    String d = a.getKey();

                    String sql = "select file_name,rela_id from upload where id='" + d + "'";
                    logger.warn(sql);
                    List<JSONObject> dets = mysql.query(sql);
                    JSONObject aone = new JSONObject();
                    if (dets.size() > 0) {
                        JSONObject det = dets.get(0);

                        aone.put("file_id", d);
                        String file_name = det.getString("file_name");
                        if (file_name.startsWith("17") && file_name.contains("_")) {
                            file_name = file_name.substring(14);
                        }
                        aone.put("file_name", file_name);
                        aone.put("rela_id", det.getString("rela_id"));
                    } else {


                        aone.put("file_id", d);
                        aone.put("file_name", "");
                        aone.put("rela_id", "");
                    }
                    access.add(aone);
                }
            }
            one.put("accessory", access);
            String link = one.getString("link");
            JSONArray links = new JSONArray();
            if (link.length() > 0) {

                HashMap<String, String> accesss = RIUtil.StringToList(link);
                for (Map.Entry<String, String> a : accesss.entrySet()) {
                    String d = a.getKey();
                    String sql = "select file_name,rela_id from upload where id='" + d + "'";
                    logger.warn(sql);
                    List<JSONObject> dets = mysql.query(sql);
                    JSONObject aone = new JSONObject();
                    if (dets.size() > 0) {
                        JSONObject det = dets.get(0);

                        aone.put("file_id", d);
                        String file_name = det.getString("file_name");
                        file_name = file_name.substring(14);
                        aone.put("file_name", file_name);
                        aone.put("rela_id", det.getString("rela_id"));
                    } else {


                        aone.put("file_id", d);
                        aone.put("file_name", "");
                        aone.put("rela_id", "");
                    }
                    links.add(aone);
                }
            }
            one.put("link", links);

            //img
            String img = one.getString("img");
            JSONArray imgs = new JSONArray();
            if (img.length() > 0) {

                JSONArray imgss = JSONArray.parseArray(img);
                for (int a = 0; a < imgss.size(); a++) {
                    JSONObject ione = imgss.getJSONObject(a);
                    String d = ione.getString("id");
                    String name = RIUtil.IdToName(d, mysql, "file_name", "upload");
                    ione.put("file_name", name);
                    String path = RIUtil.IdToName(d, mysql, "file_path", "upload");
                    ione.put("file_path", path);
                    imgs.add(ione);
                }
            }
            one.put("img", imgs);
            //签收时见
            String sql =
                    "select time,opt_user,opts from ding_form_logs where ding_id='" + one.getString("id") + "' " +
                            "and " +
                            "opts" + " !='签收'" + " order  by time,id desc";
            List<JSONObject> checks = mysql.query(sql);
            List<JSONObject> clogs = new ArrayList<>();
            if (checks.size() > 0) {
                for (int c = 0; c < checks.size(); c++) {
                    JSONObject cone = checks.get(c);
                    String user_id = cone.getString("opt_user");
//                    sql = "select id_num from user where id = '" + user_id + "' ";
//                    idNum = mysql.query_one("id_num", sql);
                    cone.put("user", RIUtil1.users1.get(user_id));
                    clogs.add(cone);
                }
                one.put("check_log", clogs);
            } else {
                one.put("check_log", new ArrayList<>());
            }

            //标签
            if (one.containsKey("label")) {
                String label = one.getString("label");
                if (label.length() > 0) {

                    String labelOne = RealDictNames(RIUtil.StringToList(label));

                    one.put("label", labelOne);
                    one.put("label_id", label);
                } else {
                    one.put("label", "");
                    one.put("label_id", "");
                }
            } else {
                one.put("label", "");
                one.put("label_id", "");
            }

            if (one.containsKey("unit")) {
                String unit = one.getString("unit");
                one.put("unit_name", RealDictNames(RIUtil.StringToList(unit)));
            } else {
                one.put("unit_name", "");
            }

            //模版
            String tempForm = "";
            if (one.containsKey("temp_id") && one.getString("temp_id").length() > 0) {
                String tempId = one.getString("temp_id");

                sql = "select form_config from ding_temp where id='" + tempId + "'";
                tempForm = mysql.query_one(sql, "form_config");

            }
            one.put("form_config", tempForm);
//评论
            one.put("comment_text", GetCommentText(one.getString("id"), mysql, 1, 1));
            one.put("comment_selects", "");

            back.add(one);

        }
        return back;

    }

    public static JSONArray UseridToNames(HashMap<String, String> members) throws Exception {
        //logger.warn(members.toString());
        JSONArray back = new JSONArray();
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();

            for (Map.Entry<String, String> one : members.entrySet()) {
                String idNum = one.getKey();

//            String sql = "select id_num from user where id = '" + id + "' ";
//            String idNum = mysql.query_one("id_num", sql);


                JSONObject u = RIUtil1.users1.get(idNum);
                // logger.warn(idNum + "->-" + u.toString());
                if (u != null) {
                    back.add(u);
                }

            }
        } catch (Exception ex) {

        } finally {
            InfoModelPool.putModel(mysql);
        }


        return back;

    }

    public static JSONArray GetCommentText(String id, InfoModelHelper mysql, int source, int commentType) throws Exception {
        JSONArray back = new JSONArray();
        String order = " ";
        if (source == 1) {
            order = " desc";
        }
        String sql =
                "select a.id,a.comment,b.name," + "a.create_time," + "b" + ".position,tele_long,b.tele_sort,b" +
                        ".tele_home,b.police_id," + "a.comment_user,a.create_time," + "b" + ".img,a" +
                        ".comment_type,b" + ".isMain,"
                        + "a" + ".isdelete " + "from ding_comment a left join user b on a" + ".comment_user=b.id_num "
                        + "where a" + ".notice_id='" + id + "' and (a.isdelete=1 or a" + ".isdelete=3) " + "and a" +
                        ".source=" + source + " " + "and " + "comment_type='" + commentType + "'" + " order by  " +
                        "a.create_time" + order;
        logger.warn(sql);
        List<JSONObject> list = mysql.query(sql);
        if (list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String pos = one.getString("position");
                one.put("position_names", RealDictNames(RIUtil.StringToList(pos)));

                String comment = one.getString("comment");
                String[] comms = comment.split("\\|,");

                JSONArray comments = new JSONArray();
                for (int c = 0; c < comms.length; c++) {
                    sql = "select file_name from upload where id='" + comms[c] + "'";
                    String fileName = mysql.query_one(sql, "file_name");
                    JSONObject cone = new JSONObject();
                    if (fileName != null && fileName.length() > 0) {
                        cone.put("file_id", comms[c]);
                        sql = "select file_path from upload where id='" + comms[c] + "'";
                        String filePath = mysql.query_one(sql, "file_path");
                        cone.put("file_path", filePath);
                        cone.put("file_name", fileName);
                        comments.add(cone);
                    } else {
                        cone.put("file_id", comms[c]);
                        cone.put("file_name", "");
                        comments.add(cone);
                    }

                }
                one.put("submits", comments);

                back.add(one);
            }
        }
        return back;
    }

    private static JSONObject comment(JSONObject data) throws Exception {

        String notice_id = "";
        String comment = "";
        String comment_user = "";
        String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String father_comment_id = "";
        String comment_type = "";
        String isdelete = "1";
        String source = "";


        if (data.containsKey("notice_id") && data.getString("notice_id").length() > 0) {
            notice_id = data.getString("notice_id");
        }
        if (data.containsKey("comment") && data.getString("comment").length() > 0) {
            comment = data.getString("comment");
        }
        if (data.containsKey("comment_user") && data.getString("comment_user").length() > 0) {
            comment_user = data.getString("comment_user");
        } else {
            if (data.containsKey("opt_user")) {
                comment_user = data.getString("opt_user");
            }
        }
        if (data.containsKey("create_time") && data.getString("create_time").length() > 0) {
            create_time = data.getString("create_time").replace("|", " ");
        }
        if (data.containsKey("father_comment_id") && data.getString("father_comment_id").length() > 0) {
            father_comment_id = data.getString("father_comment_id");
        }
        if (data.containsKey("comment_type") && data.getString("comment_type").length() > 0) {
            comment_type = data.getString("comment_type");
        }
        if (data.containsKey("source") && data.getString("source").length() > 0) {
            source = data.getString("source");
        }


        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            if (comment_type.equals("2")) {
                String s =
                        "select id from ding_comment where notice_id='" + notice_id + "' and comment_user='" + comment_user + "' " + "and isdelete =1 and source='" + source + "' and comment_type=2";
                List<JSONObject> l = mysql.query(s);
                if (l.size() > 0) {
                    return ErrNo.set(432020);
                }
            }

            String sql = "insert ding_comment (notice_id,comment,comment_user,create_time," + "father_comment_id," +
                    "comment_type,isdelete,source)" + "values('" + notice_id + "','" + comment + "','" + comment_user + "'," + "'" + create_time + "'," + "'" + father_comment_id + "','" + comment_type + "','" + isdelete + "','" + source + "') ";
            mysql.update(sql);
            logger.warn(sql);


        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 432009, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    public static void publishDing(String token) {
        logger.warn("--PUBLISH.DING--");

        InfoModelHelper mysql = null;

        try {
            String after5 = RIUtil.stampToTime(System.currentTimeMillis() + 1000 * 60 * 5);
            mysql = InfoModelPool.getModel();

            String sqls =
                    "select a.id,a.create_user,a.notice_time,a.is_notice,a.reading,a.readed,a.groups,a.users,a" +
                            ".comment_type,a.comment_select,a.isTop,a.isNew,a.top_date," + "a.new_date,a.type,a" +
                            ".isWechat,isMsg,"
                            + "a" + ".create_time,a.isBrief,a.img,a.isAll,a.mark," + "a.accessory," + "a.link,a" +
                            ".check_time," + "a" + ".cycle,a" + ".cycle_days," + "a.cycle_seconds,a" + ".cycle_end,a" + ".father_id,a.label,a" + ".unit,a.source,a" + ".title,a" + ".content,a" + ".subType from" + " ding_notice  a where " + "is_notice=0 and isdelete=1 and " + "notice_time" + "<='" + after5 + "'";
            logger.warn(sqls);
            List<JSONObject> list = mysql.query(sqls);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    String groups = one.getString("groups");
                    String users = one.getString("users");
                    int isWechat = one.getInteger("isWechat");
                    int isMsg = one.getInteger("isMsg");
                    String readed = one.getString("readed");
                    int isBrief = one.getInteger("isBrief");
                    String unit = one.getString("unit");
                    String title = one.getString("title");
                    String link = one.getString("link");
                    logger.warn(link);
                    //logger.warn(readed);
                    HashMap<String, String> ed = RIUtil.StringToList(readed);
                    HashMap<String, String> uu = new HashMap<String, String>();
                    if (groups.length() > 0) {
                        uu.putAll(RIUtil.GroupsToUsers(groups, mysql));

                    }
                    if (users.length() > 0) {
                        uu.putAll(RIUtil.StringToList(users));

                    }
                    if (uu.size() == 0) {
                        for (Map.Entry<String, JSONObject> uone : RIUtil.users.entrySet()) {
                            String uid = uone.getKey();
                            JSONObject us = uone.getValue();
                            if (us.getString("unit").equals(unit) && !uid.equals("1")) {
                                uu.put(uid, "");
                            }

                        }
                    }


                    if (isWechat == 2 || isWechat == 0 || isMsg == 2 || isMsg == 0) {
                        for (Map.Entry<String, String> d : ed.entrySet()) {
                            String edid = d.getKey();
                            uu.remove(edid);
                        }

                    } else if (isWechat == 1 || isMsg == 1) {
                        readed = "";
                        sqls = "delete from comment where notice_id='" + id + "' and source=9";
                        mysql.update(sqls);
                        isBrief = 0;
                    }
                    // logger.warn(uu.toString());

                    sqls =
                            "update ding_notice set reading='" + RIUtil.HashToList(uu) + "',is_notice=1,readed='" + readed + "'," + "isBrief='" + isBrief + "' where id='" + id + "'";
                    mysql.update(sqls);


                    logger.warn("ischat->" + isWechat);
                    if (isWechat == 1) {
//                        one.put("reading", RIUtil.HashToList(uu));
//                        logger.warn(one.toString());
//
//                        TestMsgLine send = new TestMsgLine();
//                        String url = TNOAConf.get("HttpServ", "ding_notice_url") + id;
//                        logger.warn(url);
//                        send.sendMSG("您有一条盯办任务，请查收:" + title + "-->" + url, RIUtil.HashToList(uu));
                    }

                    if (link.length() > 1) {
                        String[] links = link.split(",");
                        for (int l = 0; l < links.length; l++) {
                            String sql = "select rela_id from upload where id='" + links[l] + "'";
                            String rela_id = mysql.query_one(sql, "rela_id");


                            String url = TNOAConf.get("HttpServ", "online_url") + "share";
                            logger.warn(url);
                            try {
                                JSONObject det = new JSONObject();
                                det.put("token", token);
                                det.put("fileId", rela_id);
                                det.put("sfzhList", RIUtil.HashToList(uu));
                                logger.warn(det.toString());
                                OkHttpClient client = new OkHttpClient().newBuilder().build();

                                MediaType mediaType = MediaType.parse("application/json");

                                RequestBody body = RequestBody.create(mediaType, det.toString());


                                Request ret =
                                        new Request.Builder().url(url).method("POST", body).addHeader("Content" +
                                                        "-Type",
                                                "application/json").build();

                                Response response = client.newCall(ret).execute();

                                String back = response.body().string();
                                logger.warn(back + "-->");


                            } catch (Exception ex) {
                                logger.error(Lib.getTrace(ex));
                            }


                        }
                    }


                }
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

}
