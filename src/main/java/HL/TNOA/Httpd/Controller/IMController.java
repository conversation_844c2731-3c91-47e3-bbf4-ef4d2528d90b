package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class IMController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/im"})

    public JSONObject get_im(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();

        try {


            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("get_im_type_tree")) {
                    return GetIMTypeTree(data);
                } else {
                    return ErrNo.set(605009);
                }
            } else {
                return ErrNo.set(605009);
            }
        }catch (Exception ex)
        {
            return ErrNo.set(605009);
        }
    }

    private JSONObject GetIMTypeTree(JSONObject data) {
        JSONObject back=ErrNo.set(0);
        try {
            JSONArray ret = new JSONArray();
            JSONArray dict150 = RIUtil.GetDictByType(150);
            JSONArray dict151 = RIUtil.GetDictByType(151);
            for (int i = 0; i < dict150.size(); i++) {
                JSONObject one = dict150.getJSONObject(i);
                String id = one.getString("id");

                JSONArray d151 = new JSONArray();
                for (int a = 0; a < dict151.size(); a++) {
                    String fid = dict151.getJSONObject(a).getString("father_id");
                    if (id.equals(fid)) {
                        d151.add(dict151.get(a));
                    }
                }

                one.put("det", d151);

                ret.add(one);


            }

            back.put("data", ret);
        }catch ( Exception ex)
        {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(605005);
        }
        return back;

    }


}
