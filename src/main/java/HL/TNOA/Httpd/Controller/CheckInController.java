package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.Export.ExportInterface;
import HL.TNOA.Lib.Export.ExportXlsxHelper;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.Lib.TNOAConf;
import HL.TNOA.wechat.HttpConnection;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class CheckInController {
    private static Logger logger = LoggerFactory.getLogger(CheckInController.class);
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/check_in"})

    public JSONObject get_check_in(TNOAHttpRequest request) throws Exception {
        logger.warn("check_in--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_check_in")) {
                return getCheckIn(data);
            } else if (opt.equals("create_check_in")) {
                return createCheckIn(data, request.getRemoteAddr());
            } else if (opt.equals("update_check_in")) {
                return updateCheckIn(data, request.getRemoteAddr());
            } else if (opt.equals("get_check_in_calendar")) {
                return getCheckInCalendar(data);
            } else if (opt.equals("get_check_in_static")) {
                return getCheckInStatic(data);
            } else if (opt.equals("calendar")) {//获取今天是工作日还是节假日
                return getCalendar();
            } else if (opt.equals("export_check_static")) {//
                return ExportCheckStatic(data);
            } else {
                return ErrNo.set(473009);
            }
        } else {
            return ErrNo.set(473009);
        }
    }

    private JSONObject ExportCheckStatic(JSONObject data) {

        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String create_time_start = "";
            String create_time_end = "";
            String dept = "";


            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
                create_time_start = data.getString("create_time_start") + " 00:00:00";

            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end") + " 23:59:59";

            }
            if (data.containsKey("dept") && data.getString("dept").length() > 0) {
                dept = data.getString("dept");

            }

            JSONArray results = new JSONArray();
            HashMap<String, JSONObject> userlist = RIUtil.users;
            for (Map.Entry<String, JSONObject> uone : userlist.entrySet()) {
                String uid = uone.getKey();
                try {
                    JSONObject uobj = uone.getValue();


                    String unit = uobj.getJSONArray("unit").getJSONObject(0).getString("id");
                    //  System.out.println(uobj);
                    if (uobj.getInteger("org") == 1 && !uobj.getString("position").equals("caadfc30-74d6-436c-8dc3" + "-c4bc491129a8")) {
                        if (dept.contains(unit) || dept.length() == 0) {

                            sql = "select status,create_time from check_in where " + "create_user='" + uid + "' " +
                                    "and status>0 and create_time>='" + create_time_start + "' " + "and " +
                                    "create_time" + "<='" + create_time_end + "'" + " ;";
                            System.out.println(sql);

                            int bt = RIUtil.get2DateBetween(create_time_start, create_time_end) + 1;
                            List<JSONObject> ress = mysql.query(sql);
                            int noraml = 0;
                            int overtime = 0;
                            int lost = 0;
                            int exp = 0;
                            int total = 0;
                            if (ress.size() > 0) {
                                String last = "";

                                for (int i = 0; i < ress.size(); i++) {
                                    JSONObject ro = ress.get(i);
                                    String t = ro.getString("create_time").substring(0, 10);

                                    if (!t.equals(last)) {
                                        total++;
                                        if (ro.getInteger("status") == 5) {
                                            noraml++;
                                        }
                                        if (ro.getInteger("status") == 6) {
                                            overtime++;
                                        }
                                        if (ro.getInteger("status") == 7) {
                                            lost++;
                                        }
                                        if (ro.getInteger("status") == 9) {
                                            exp++;
                                        }
                                    }

                                    last = t;
                                }
                            }

                            JSONObject rone = new JSONObject();
                            rone.put("name", uobj.getString("name"));
                            rone.put("dept", uobj.getJSONArray("unit").getJSONObject(0).getString("name"));
                            rone.put("normal", noraml);
                            rone.put("overtime", overtime);
                            rone.put("lost", lost);
                            rone.put("exp", exp);
                            rone.put("no", bt - total);
                            System.out.println(rone);
                            results.add(rone);

                        }
                    }
                } catch (Exception ex) {

                }
            }

            String fileId = "0";
            fileId = ExportCheckStaticExcel(results);
            back.put("file_id", fileId);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject getCalendar() {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql =
                    "select mark from calendar where date='" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + "'";
            back.put("data", mysql.query_one(sql, "mark"));//1上班0节假日
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject getCheckInStatic(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String unit = "";

            String date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String dept = "";

            if (data.containsKey("date") && data.getString("date").length() > 0) {
                date = data.getString("date");
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit").split(",")[0];
            } else {
                return ErrNo.set(473006);
            }


            String sqls =
                    "select count(DISTINCT create_user) as count from check_in  where create_time like '%" + date +
                            "%' and unit='" + unit + "' ";
            int count = mysql.query_count(sqls);
            sqls = "select count(id) as count from user where  status=1 and isdelete=1 and unit  " + "like '%" + unit + "%' ";
            int userCount = mysql.query_count(sqls);

            List<JSONObject> checked = new ArrayList<>();
            JSONArray noCheck = new JSONArray();

            sqls = "select id from user where isdelete=1 and status=1 and unit  " + "like '%" + unit + "%' ";
            List<JSONObject> uone = mysql.query(sqls);
            for (int u = 0; u < uone.size(); u++) {
                String userid = uone.get(u).getString("id");

                sqls = "select status,create_time from check_in where create_user='" + userid + "' and " +
                        "create_time like '%" + date + "%' order by status desc limit 1";
                // logger.warn(sqls);
                String status = mysql.query_one(sqls, "status");
                String create_time = mysql.query_one(sqls, "create_time");
                JSONObject user = RIUtil.users.get(userid);
                if (status.length() == 0) {


                    user.put("status", "");

                    noCheck.add(user);
                } else {
                    user.put("status", status);
                    user.put("create_time", RIUtil.dateToStamp(create_time));
                    checked.add(user);
                }


            }
            JSONObject datas = new JSONObject();


            Collections.sort(checked, (JSONObject o1, JSONObject o2) -> {

                long a = o1.getLong("create_time");
                long b = o2.getLong("create_time");

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a < b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });
            datas.put("checked", checked);
            datas.put("nocheck", noCheck);
            back.put("data", datas);
            back.put("checked_count", count);
            back.put("user_count", userCount);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject getCheckInCalendar(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";


            String opt_user = "";
            String user_id = "";
            String month = new SimpleDateFormat("yyyy-MM").format(new Date());
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
            } else {
                return ErrNo.set(473006);
            }
            if (data.containsKey("month") && data.getString("month").length() > 0) {
                month = data.getString("month");
            }
            String start_time = month + "-01";
            String end_time = RIUtil.getLMonthEnd(start_time).substring(0, 10);
            logger.warn(start_time + "-" + end_time);
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");

            }
            String time = start_time;
            JSONArray datas = new JSONArray();
            while (!time.equals(end_time)) {
                String sqls =
                        "select id,address,create_time,status,mark,img,isCheck,check_mark from check_in where " +
                                "create_time " + "like '%" + time + "%' " + "and " + "create_user='" + user_id + "' " + "order by create_time desc";
                //logger.warn(sqls);
                JSONObject det = new JSONObject();
                List<JSONObject> list = mysql.query(sqls);
                if (list.size() > 0) {
                    sqls = "select check_mark from check_in  where create_time like '%" + time + "%'" + " " + "and " + "create_user='" + user_id + "' and isdelete=0 order by create_time desc limit 1";
                    String status = mysql.query_one(sqls, "check_mark");
                    int count = 0;
                    if (status == null || status.equals("")) {
                        count = 0;
                    } else {
                        count = Integer.parseInt(status);
                        if (count == 0) {
                            count = 1;
                        }
                    }
                    det.put("count", count);
                    det.put("details", list);
                } else {
                    det.put("count", 0);
                    det.put("details", new ArrayList<>());
                }
                det.put("date", time);
                datas.add(det);
                time = RIUtil.GetNextDate(time, 1);
            }
            String sqls = "select id,address,create_time,status,mark,img,isCheck,check_mark from check_in where " +
                    "create_time" + " like '%" + time + "%'" + " " + "and " + "create_user='" + user_id + "' and " +
                    "isdelete=0";
            // logger.warn(sqls);
            JSONObject det = new JSONObject();
            List<JSONObject> list = mysql.query(sqls);
            if (list.size() > 0) {
                sqls = "select status from check_in  where create_time like '%" + time + "%'" + " " + "and " +
                        "create_user='" + user_id + "' and isdelete=0 order by create_time desc limit 1";
                String status = mysql.query_one(sqls, "status");
                int count = 0;
                if (status == null || status.equals("")) {
                    count = 0;
                } else if (status.contains("上班") || status.contains("缺卡") || status.contains("异常") || status.contains("迟到")) {
                    count = 1;
                } else {
                    count = 2;
                }
                det.put("count", count);
                det.put("details", list);
            } else {
                det.put("count", 0);
                det.put("details", new ArrayList<>());
            }
            det.put("date", time);
            datas.add(det);

            back.put("data", datas);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******CREATE*******
    private JSONObject createCheckIn(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String lng = "";
            String lat = "";
            String address = "";
            String status = "-1";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String con = today + " 09:10:00";
            String coff = today + " 17:00:00";
            String cooff18 = today + " 18:00:00";
            long current = System.currentTimeMillis();
            long check_on = RIUtil.dateToStamp(con);
            long check_off = RIUtil.dateToStamp(coff);
            long check_off18 = RIUtil.dateToStamp(cooff18);
            String isdelete = "1";
            String mark = "";
            String img = "";

            if (data.containsKey("lng") && data.getString("lng").length() > 0) {
                lng = data.getString("lng");
            }
            if (data.containsKey("lat") && data.getString("lat").length() > 0) {
                lat = data.getString("lat");
            }
            address = "";
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                create_user = data.getString("user_id");
            } else {
                return ErrNo.set(473002);
            }
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(473002);
            }
            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                mark = data.getString("remark");
            } else {
                return ErrNo.set(473002);
            }
            if (data.containsKey("img") && data.getString("img").length() > 0) {
                img = data.getString("img");
            }

            if (data.containsKey("type") && data.getString("type").length() > 0) {
                img = data.getString("type");
            } else {
                return ErrNo.set(473002);
            }


            String sqls =
                    "insert attendance (user_id,time,dir,remark)values('" + create_user + "'," + "'" + create_time +
                            "'," +
                            "'" + img +
                            "','" + mark + "')";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建考勤", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static String ChangelaloToAddress(String latitude, String longitude) {
        String url =
                "https://apis.map.qq.com/ws/geocoder/v1/?location=" + latitude + "," + longitude + "&key=6MBBZ" +
                        "-PMOLU-NYHVF-2OZ4X-BDOG3-PFFXZ&poi_options=address_format=short";
        String patrol_name = "";
        String back = HttpConnection.http_get(url);
        JSONObject ret = JSONObject.parseObject(back);
        if (ret.getInteger("status") == 0 && ret.getString("message").contains("ok")) {
            JSONObject result = ret.getJSONObject("result");
            JSONObject formatted_addresses = result.getJSONObject("formatted_addresses");
            patrol_name = formatted_addresses.getString("recommend");

        }

        return patrol_name;


    }

    public static void main(String[] args) {
        JSONObject data =
                JSONObject.parseObject("{\"opt\":\"get_check_in\"," + "\"opt_user\":\"1ec1bad6-39f0-4429" + "-b7f6" + "-3e8389c5ab1e\",\"create_time_start\":\"2023-02-01\"," + "\"create_user\":\"dbf77efb-e3c2" + "-4496" + "-adde-41a8629c6f76\",\"create_time_end\":\"2023-02-28\"," + "\"isExp\":\"1\"}");
        System.out.println(getCheckIn(data));
    }

    //******GET*******
    private static JSONObject getCheckIn(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String csql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String lng = "";
            String lat = "";
            String address = "";
            String status = "";
            String create_user = "";
            String create_time_start = "";
            String create_time_end = "";
            int isExp = 0;
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("lng") && data.getString("lng").length() > 0) {
                lng = data.getString("lng");
                sql = sql + " lng='" + lng + "' and ";
            }
            if (data.containsKey("lat") && data.getString("lat").length() > 0) {
                lat = data.getString("lat");
                sql = sql + " lat='" + lat + "' and ";
            }
            if (data.containsKey("address") && data.getString("address").length() > 0) {
                address = data.getString("address");
                sql = sql + " address='" + address + "' and ";
            }
            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getString("status");
                sql = sql + " status='" + status + "' and ";
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' and ";
            }
            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
                create_time_start = data.getString("create_time_start");
                sql = sql + "create_time>='" + create_time_start + "' and ";
                csql = csql + "date>='" + create_time_start.substring(0, 10) + "' and ";
            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end");
                csql = csql + "date<='" + create_time_end.substring(0, 10) + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getInteger("isExp");
            }
            String file_id = "";
            String sqls =
                    "select * from check_in where 1=1 and " + sql + " isdelete=1 order by create_time limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select count(id) as count from check_in where 1=1 and " + sql + " isdelete=1; ";
                back.put("count", mysql.query_count(sqls));
                if (isExp == 1) {
                    list = new ArrayList<>();
                    sqls = "select date,mark from calendar where " + csql + "   1=1";
                    List<JSONObject> clist = mysql.query(sqls);

                    for (int c = 0; c < clist.size(); c++) {
                        JSONObject cone = clist.get(c);
                        int mark = cone.getInteger("mark");
                        String date = cone.getString("date");
                        if (mark == 1)//工作日
                        {
                            sqls = "select *,'工作日' as calendar from check_in where 1=1 and create_time like '%" + date + "%' and  isdelete=1 and status in('5','6','7','8','9') and create_user='" + create_user + "' " + "order by " + "create_time desc limit 1" + " ;";
                            System.out.println(sqls);
                            List<JSONObject> ll = mysql.query(sqls);
                            if (ll.size() == 0) {
                                JSONObject llost = new JSONObject();
                                llost.put("id", 0);
                                llost.put("create_user", create_user);
                                llost.put("address", "");
                                llost.put("create_time", date);
                                llost.put("status", 7);
                                llost.put("calendar", "工作日");
                                ll.add(llost);

                            }

                            list.addAll(ll);

                        } else {
                            sqls = "select *,'节假日' as calendar from check_in where 1=1 and create_time like '%" + date + "%' and  isdelete=1 and status in('5','6','7','8','9') and create_user='" + create_user + "' " + "order by " + "create_time desc limit 1" + " ;";
                            System.out.println(sqls);
                            List<JSONObject> ll = mysql.query(sqls);
                            if (ll.size() == 0) {
                                JSONObject llost = new JSONObject();
                                llost.put("id", 0);
                                llost.put("create_user", create_user);
                                llost.put("address", "");
                                llost.put("create_time", date);
                                llost.put("status", 333);
                                llost.put("calendar", "节假日");
                                ll.add(llost);
                            }
                            list.addAll(ll);
                            System.out.println(ll);
                        }


                    }


                    System.out.println(list);
                    JSONObject oo = new JSONObject();
                    oo.put("files", RelaInfo(list, mysql));


                    file_id = ExportCheckInFile(oo.getJSONArray("files"));
                    back.put("file_id", file_id);
                }
            } else {
                back.put("data", list);
                back.put("count", 0);
                back.put("file_id", file_id);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static String ExportCheckInFile(JSONArray datas) {


        String FileName = "CheckIn_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        header.add("id");
        headername.put("id", "序号");
        header.add("name");
        headername.put("name", "姓名");

        header.add("address");
        headername.put("address", "打卡地点");
        header.add("create_time");
        headername.put("create_time", "打卡时间");
        header.add("status_str");
        headername.put("status_str", "打卡状态");
        header.add("calendar");
        headername.put("calendar", "工作/节假日");
        header.add("week");
        headername.put("week", "星期");


        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
            exporthelper.write_data(datas);


            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql =
                        "insert into upload (nas_id,file_path,file_name) values" + " ('1','" + filePath + "','" + FileName + "')";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");
                //返回拼接后的url,id

                logger.warn("id->" + id);
                return String.valueOf(id);
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return "0";
        } finally {

            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }

        }


    }

    private String ExportCheckStaticExcel(JSONArray datas) {


        String FileName = "CheckIn_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        header.add("id");
        headername.put("id", "序号");
        header.add("name");
        headername.put("name", "姓名");

        header.add("dept");
        headername.put("dept", "部门");
        header.add("normal");
        headername.put("normal", "正常");
        header.add("overtime");
        headername.put("overtime", "加班");
        header.add("lost");
        headername.put("lost", "缺卡");
        header.add("exp");
        headername.put("exp", "异常处理");
        header.add("no");
        headername.put("no", "缺勤");


        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
            exporthelper.write_data(datas);


            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql =
                        "insert into upload (nas_id,file_path,file_name) values" + " ('1','" + filePath + "','" + FileName + "')";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");
                //返回拼接后的url,id

                logger.warn("id->" + id);
                return String.valueOf(id);
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return "0";
        } finally {

            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }

        }


    }

    //*****relainfo************
    private static Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String create_time = one.getString("create_time");
            if (create_time.length() > 0) {
                one.put("week", GetWeeklyNum(create_time));
            }
            one.put("create_user", RIUtil.users.get(one.getString("create_user")));
            one.put("name", one.getJSONObject("create_user").getString("name"));
            String status = one.getString("status");
            if (status.equals("0")) {
                one.put("status_str", "上班打开");
            } else if (status.equals("1")) {
                one.put("status_str", "下班打卡");
            } else if (status.equals("2")) {
                one.put("status_str", "加班");
            } else if (status.equals("5") || status.equals("6")) {
                one.put("status_str", "正常");
            } else if (status.equals("7") || status.equals("8")) {
                one.put("status_str", "缺卡");
            } else if (status.equals("333")) {
                one.put("status_str", "");
            } else {
                one.put("status_str", "无效打卡");
            }

            back.add(one);


        }
        return back;
    }

    public static String GetWeeklyNum(String str) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date date = format.parse(str);
            format = new SimpleDateFormat("EEEE");
            String week = format.format(date);

            if (week.equals("Monday")) {
                week = "星期一";
            } else if (week.equals("Tuesday")) {
                week = "星期二";
            } else if (week.equals("Wednesday")) {
                week = "星期三";
            } else if (week.equals("Thursday")) {
                week = "星期四";
            } else if (week.equals("Friday")) {
                week = "星期五";
            } else if (week.equals("Saturday")) {
                week = "星期六";
            } else if (week.equals("Sunday")) {
                week = "星期日";
            } else {

            }


            return week;
        } catch (Exception exception) {
            System.out.println(Lib.getTrace(exception));
        }
        return "";

    }

    //******UPDATE*******
    private JSONObject updateCheckIn(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String isCheck = "";
            String check_mark = "";

            String opt_user = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(473004);
            }

            if (data.containsKey("isCheck") && data.getString("isCheck").length() > 0) {
                isCheck = data.getString("isCheck");
                sql = sql + " isCheck='" + isCheck + "' , ";
                if (isCheck.equals("1")) {
                    sql = sql + " status='" + 9 + "' , ";
                }
            }
            if (data.containsKey("check_mark") && data.getString("check_mark").length() > 0) {
                check_mark = data.getString("check_mark");
                sql = sql + " check_mark='" + check_mark + "' , ";
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(473004);
            }
            String sqls = "update check_in set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新考勤", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(473003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private JSONObject deleteCheckIn(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(473008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(473008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update check_in set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除考勤", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(473007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }


}
