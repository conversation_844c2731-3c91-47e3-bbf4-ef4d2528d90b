package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
public class MemoController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());

    @RequestMapping(method = {RequestMethod.POST}, path = {"/memo"})
    @PassToken
    public JSONObject get_memo(TNOAHttpRequest request) throws Exception {
        logger.warn("memo--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_memo")) {
                return getMemo(data);
            } else if (opt.equals("create_memo")) {
                return createMemo(data, request.getRemoteAddr());
            } else if (opt.equals("update_memo")) {
                return updateMemo(data, request.getRemoteAddr());
            } else if (opt.equals("delete_memo")) {
                return deleteMemo(data, request.getRemoteAddr());
            } else if (opt.equals("create_JWBQ")) {
                return createJWBQ(data, request.getRemoteAddr());
            } else if (opt.equals("get_JWBQ")) {
                return getJWBQ(data);
            } else if (opt.equals("update_JWBQ")) {
                return updateJWBQ(data, request.getRemoteAddr());
            } else {
                return ErrNo.set(503009);
            }
        } else {
            return ErrNo.set(503009);
        }
    }

    //警务便签
    private JSONObject createJWBQ(JSONObject data, String remoteAddr) {
        JSONObject back = ErrNo.set(0);
        String title = "";
        String clock = "";
        String create_user = "";
        String isdelete = "1";
        String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        if (data.containsKey("title") && data.getString("title").length() > 0) {
            title = data.getString("title");
        } else {
            return ErrNo.set(503004);
        }
        if (data.containsKey("clock") && data.getString("clock").length() > 0) {
            clock = data.getString("clock");
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            create_user = data.getString("opt_user");
        } else {
            return ErrNo.set(503002);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();

            String sql =
                    "insert into memo (title,end_time,create_user,create_time,isdelete) values('" + title + "'," +
                            "'" + clock + "','" + create_user + "','" + create_time + "','" + isdelete + "')";
            mysql.update(sql);
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建安保信息", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(503001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //获取警务便签
    private JSONObject getJWBQ(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        InfoModelHelper mysql = null;
        String create_user = "";
        int limit = 5;
        int page = 1;
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            create_user = data.getString("opt_user");
        } else {
            return ErrNo.set(503002);
        }

        if (data.containsKey("limit") && data.getString("limit").length() > 0) {
            limit = data.getInteger("limit");
        }
        if (data.containsKey("page") && data.getString("limit").length() > 0) {
            page = data.getInteger("page");
        }
        String status = "";
        if (data.containsKey("status") && data.getString("status").length() > 0) {
            status = data.getString("status");
            status = "and status='" + status + "'";
        }
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select id,title,end_time as clock ,create_user ,create_time,status from memo where " +
                    "isdelete" + " =1 " + "and" + " create_user='" + create_user + "' and remark is null " + status + " order by " + "create_time " + "desc limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> query = mysql.query(sql);
            if (query.size() > 0) {
                back.put("data", query);
                back.put("count", mysql.query_count("select count(id) as count from memo where isdelete =1 and " +
                        "remark" + " is null " + status));
            } else {
                back.put("data", query);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(503001);
        } finally {
            InfoModelPool.putModel(mysql);
        }

        return back;
    }

    //修改警务便签
    private JSONObject updateJWBQ(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String id = "";
            String title = "";
            String clock = "";
            String sql = " ";
            String opt_user = "";
            int status = 0;
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(503004);
            }
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(503004);
            }

            if (data.containsKey("title") && data.getString("title").length() > 0) {
                title = data.getString("title");
                sql = sql + " title='" + title + "',";
            }
            if (data.containsKey("clock") && data.getString("clock").length() > 0) {
                clock = data.getString("clock");
                sql = sql + " end_time='" + clock + "',";
            }

            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getInteger("status");
                sql = sql + "  status='" + status + "' , ";
            }
            String sqls = "update memo set " + sql + "isdelete=1 where id = '" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新安保信息", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(503001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******CREATE*******
    private JSONObject createMemo(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String title = "";
            String start_time = "";
            String end_time = "";
            String remark = "";
            String level = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "1";
            if (data.containsKey("title") && data.getString("title").length() > 0) {
                title = data.getString("title");
            } else {
                title = "今日便签";
            }
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time");
            } else {
                start_time = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time");
            } else {
                end_time = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
            }
            if (data.containsKey("level") && data.getString("level").length() > 0) {
                level = data.getString("level");
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                return ErrNo.set(503002);
            }
            String sqls = "insert memo (title,start_time,end_time,remark,level,create_user,create_time," + "isdelete)"
                    + "values('" + title + "','" + start_time + "','" + end_time + "','" + remark + "','" + level +
                    "'," + "'" + create_user + "','" + create_time + "','" + isdelete + "')";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建安保信息", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(503001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private JSONObject getMemo(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String title = "";
            String start_time_start = "";
            String start_time_end = "";
            String remark = "";
            String level = "";
            String create_user = "";
            String create_time_start = "";
            String create_time_end = "";
            String status = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("title") && data.getString("title").length() > 0) {
                title = data.getString("title");
                sql = sql + " title like '%" + title + "%' and ";
            }
            if (data.containsKey("start_time_start") && data.getString("start_time_start").length() > 0) {
                start_time_start = data.getString("start_time_start");
                sql = sql + " start_time<='" + start_time_start + " 23:59:59' and ";
                sql = sql + " end_time>='" + start_time_start + " 00:00:00' and ";
            }
            if (data.containsKey("start_time_end") && data.getString("start_time_end").length() > 0) {
                start_time_end = data.getString("start_time_end");

            }
            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
                sql = sql + " remark='" + remark + "' and ";
            }
            if (data.containsKey("level") && data.getString("level").length() > 0) {
                level = data.getString("level");
                sql = sql + " level='" + level + "' and ";
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' and ";
            }
            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
                create_time_start = data.getString("create_time_start");
                sql = sql + "create_time>='" + create_time_start + "' and ";
            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end");
                sql = sql + "create_time<='" + create_time_end + "' and ";
            }
            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getString("status");
                sql = sql + "status='" + status + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select * from memo where 1=1 and " + sql + " isdelete=1  limit " + limit + " offset " + limit * (page - 1);
            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select count(id) as count from memo where 1=1 and " + sql + " isdelete=1; ";
                back.put("count", mysql.query_count(sqls));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(503005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            one.put("create_user", RIUtil.users.get(one.getString("create_user")));
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private JSONObject updateMemo(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String title = "";
            String start_time = "";
            String end_time = "";
            String remark = "";
            String level = "";
            String opt_user = "";
            int status = 0;
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(503004);
            }
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' , ";
            }
            if (data.containsKey("title") && data.getString("title").length() > 0) {
                title = data.getString("title");
                sql = sql + " title='" + title + "' , ";
            }
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time");
                sql = sql + " start_time='" + start_time + "' , ";
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time");
                sql = sql + " end_time='" + end_time + "' , ";
            }
            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
                sql = sql + " remark='" + remark + "' , ";
            }
            if (data.containsKey("level") && data.getString("level").length() > 0) {
                level = data.getString("level");
                sql = sql + " level='" + level + "' , ";
            }
            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getInteger("status");
                sql = sql + " status='" + status + "' , ";
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(503004);
            }
            String sqls = "update memo set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新安保信息", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(503003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private JSONObject deleteMemo(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(503008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(503008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update memo set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除安保信息", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(503007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }
}
