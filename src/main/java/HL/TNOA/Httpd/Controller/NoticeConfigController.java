package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
public class NoticeConfigController {
    private static Logger logger = LoggerFactory.getLogger(NoticeConfigController.class);
    private static String ip = "";

    @RequestMapping(method = {RequestMethod.POST}, path = {"/notice_config"})
    public static JSONObject get_config(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        String ip = request.getRemoteAddr();
        String opt = "";


        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_msg_config")) {
                return getNoticeConfig(data);
            } else if (opt.equals("update_msg_config")) {
                return updateNoticeConfig(data, ip);
            } else {
                return ErrNo.set(468009);
            }
        } else {
            return ErrNo.set(468009);
        }
    }

    //******CREATE*******
    private static JSONObject createNoticeConfig(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String notice_type = "";
            String user_id = "";
            String isOpen = "";
            if (data.containsKey("notice_type") && data.getString("notice_type").length() > 0) {
                notice_type = data.getString("notice_type");
            } else {
                return ErrNo.set(468002);
            }
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
            } else {
                return ErrNo.set(468002);
            }
            if (data.containsKey("isOpen") && data.getString("isOpen").length() > 0) {
                isOpen = data.getString("isOpen");
            } else {
                return ErrNo.set(468002);
            }
            String sqls =
                    "insert notice_config (notice_type,user_id,isOpen)" + "values('" + notice_type + "','" + user_id + "','" + isOpen + "')";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, user_id, "创建通知权限", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(468001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private static JSONObject getNoticeConfig(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String notice_type = "a";
            String user_id = "";

            if (data.containsKey("notice_type") && data.getString("notice_type").length() > 0) {
                notice_type = data.getString("notice_type");
                sql = sql + " notice_type like '%" + notice_type + "%' and ";
            }
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
                sql = sql + " user_id='" + user_id + "' and ";
            }

            String sqls = "select notice_type,user_id,isOpen from notice_config where 1=1 and " + sql + " 1=1";
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));

            } else {
                back.put("data", list);

            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(468005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private static Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private static JSONObject updateNoticeConfig(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String notice_type = "a";
            String user_id = "";
            String isOpen = "";

            if (data.containsKey("notice_type") && data.getString("notice_type").length() > 0) {
                notice_type = data.getString("notice_type");

            }
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");

            } else {
                return ErrNo.set(468004);
            }
            if (data.containsKey("isOpen") && data.getString("isOpen").length() > 0) {
                isOpen = data.getString("isOpen");
                sql = sql + " isOpen='" + isOpen + "' , ";
            } else {
                return ErrNo.set(468004);
            }
            String sqls = "update notice_config set " + sql + " isdelete=1  where user_id='" + user_id + "' and " +
                    "notice_type like '%" + notice_type + "%'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, user_id, "更新通知权限", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(468003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private static JSONObject deleteNoticeConfig(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(468008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(468008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update notice_config set isdelete =1,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除通知权限", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(468007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

}
