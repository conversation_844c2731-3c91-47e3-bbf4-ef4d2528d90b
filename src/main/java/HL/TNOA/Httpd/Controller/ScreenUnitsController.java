package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.Export.ExportInterface;
import HL.TNOA.Lib.Export.ExportXlsxHelper;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@RestController
public class ScreenUnitsController {

    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");F

    @RequestMapping(method = {RequestMethod.POST}, path = {"/screen_unit"})
    @PassToken
    public JSONObject getScreenUnit(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();

        try {
            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("get_calm_list")) {
                    // num2  560841 200-070100
                    return getCAnlmList(data);
                } else if (opt.equals("point_unit")) {
                    return getPointUnit(data);
                } else if (opt.equals("point_unit_level")) {
                    return getPointUnitLevel(data);
                } else if (opt.equals("get_xf_zg")) {
                    data.put("token", request.getHeader("token"));
                    // 消防隐患情况 整改率
                    return getXfZg(data);
                } else if (opt.equals("get_xf_jc_qk")) {
                    data.put("token", request.getHeader("token"));
                    // 消防单位检查情况
                    return getXfJcQk(data);
                } else if (opt.equals("get_xf_fx")) {
                    data.put("token", request.getHeader("token"));
                    // 消防隐患发现率
                    return getXfFx(data);
                } else if (opt.equals("get_xf_wzg")) {
                    data.put("token", request.getHeader("token"));
                    return getXfWzg(data);
                } else if (opt.equals("get_xf_yhs")) {
                    data.put("token", request.getHeader("token"));
                    return getXfYhs(data);
                } else if (opt.equals("get_yjc_xf_dw")) {
                    data.put("token", request.getHeader("token"));
                    return getYjcXfDw(data);
                } else if (opt.equals("get_xf_dw_gk")) {
                    data.put("token", request.getHeader("token"));
                    return getXfDwGk(data);
                } else if (opt.equals("get_xfdw_mx")) {//单位消防明细
                    return getXFXM(data, request.getHeader("token"));
                } else if (opt.equals("get_nbdw_gk")) {
                    return GetNBGK(data);
                } else if (opt.equals("get_nbdw_gk_table")) {
                    return GetNBGKTable(data);
                } else {
                    return ErrNo.set(465009);
                }
            } else {
                return ErrNo.set(465009);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private JSONObject GetNBGKTable(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = data.getString("unit");
        String dzs = data.getString("dzs");

        int page = data.getInteger("page");
        int limit = data.getInteger("limit");

        int isExp = data.getIntValue("isExp");
        int type = RIUtil.dicts.get(data.getString("unit")).getIntValue("type");
        String ssdw = "";
        String u = "";
        if (type == 21 || type == 22 || type == 27) {
            // 市局的人
            ssdw = "320400000000";
            u = "3204";
        } else if (type == 23 || type == 24 || type == 28) {
            //分局的人
            u = unit.substring(0, 6);
            ssdw = u + "000000";

        } else if (type == 25) {
            // 派出所的人
            u = unit.substring(0, 8);
            ssdw = u + "0000";

        } else {
            // 责任区
            u = unit.substring(0, 8);
            ssdw = unit;
        }

        JSONObject para = new JSONObject();
        JSONObject p = new JSONObject();
        p.put("zzjgdm", ssdw);

        para.put("params", p);

        JSONObject pg = new JSONObject();
        pg.put("curr", page);
        pg.put("limit", limit);
        para.put("page", pg);
        para.put("isQCount", "true");

        logger.warn(para.toString());
        try {
            OkHttpClient client =
                    new OkHttpClient().newBuilder().writeTimeout(60 * 5, TimeUnit.SECONDS).readTimeout(300,
                            TimeUnit.SECONDS).connectTimeout(300, TimeUnit.SECONDS).build();
            MediaType mediaType = MediaType.parse("application/json");

            RequestBody body = RequestBody.create(mediaType, para.toString());
            Request request = null;
            if (dzs.equals("Nbyhsl")) {
                request = new Request.Builder().url("http://50.56.93.205:8080/sjdcfw/event/nbdptj/queryNbYhslDw" +
                        "?=/sjdcfw/event/nbdptj/queryNbyhslDw").method("POST", body).addHeader("Content-Type",
                        "application/json").build();
            } else {
                request =
                        new Request.Builder().url("http://50.56.93.205:8080/sjdcfw/event/nbdptj/query" + dzs + "Dw").method("POST", body).addHeader("Content-Type", "application/json").build();
            }
            Response response = client.newCall(request).execute();
            String bk = response.body().string();

            JSONObject bjdon = JSONObject.parseObject(bk);
            if (bjdon.getString("tag").equals("succeed")) {

                JSONArray res = bjdon.getJSONArray("results");
                int count = bjdon.getIntValue("total");

                String hds = "单位名称,单位地址,所属单位";
                String keys = "dwmc,dwdzmc,sszrq";
                JSONArray heads = GetHeads(hds, keys);

                JSONArray dets = new JSONArray();
                JSONArray dds = new JSONArray();

                for (int i = 0; i < res.size(); i++) {
                    JSONObject one = res.getJSONObject(i);
                    JSONObject det = new JSONObject();

                    one.put("sszrq", RIUtil.dicts.get(one.getString("sszrq")).getString("remark"));

                    dds.add(one);


                    String key[] = keys.split(",");
                    for (int k = 0; k < key.length; k++) {
                        String kk = key[k];
                        JSONObject val = new JSONObject();


                        if (kk.equals("dwmc")) {
                            JSONObject click = new JSONObject();
                            click.put("type", "jump_unit");
                            click.put("url", one.getString("jgbh"));
                            val.put("click", click);
                            val.put("value", one.getString(kk));
                            det.put("dwmc", val);
                        } else {
                            val.put("value", one.getString(kk));
                        }

                        det.put(kk, val);

                    }
                    dets.add(det);

                }
                JSONObject datas = new JSONObject();
                datas.put("head", heads);
                datas.put("body", dets);
                datas.put("count", count);
                int fileId = -1;
                if (isExp == 1) {
                    //本页
                    fileId = ExportTables(dds, hds, keys, dzs);

                } else if (isExp == 2) {
                    p = new JSONObject();
                    para = new JSONObject();
                    p.put("zzjgdm", ssdw);

                    para.put("params", p);

                    pg = new JSONObject();
                    pg.put("curr", page);
                    pg.put("limit", count);
                    para.put("page", pg);
                    para.put("isQCount", "true");

                    logger.warn(para.toString());

                    try {

                        body = RequestBody.create(mediaType, para.toString());
                        request =
                                new Request.Builder().url("http://50.56.93.205:8080/sjdcfw/event/nbdptj/query" + dzs + "Dw").method("POST", body).addHeader("Content-Type", "application/json").build();
                        response = client.newCall(request).execute();
                        bk = response.body().string();

                        bjdon = JSONObject.parseObject(bk);
                        if (bjdon.getString("tag").equals("succeed")) {

                            res = bjdon.getJSONArray("results");


                            dds = new JSONArray();

                            for (int i = 0; i < res.size(); i++) {
                                JSONObject one = res.getJSONObject(i);

                                one.put("sszrq", RIUtil.dicts.get(one.getString("sszrq")).getString("remark"));

                                dds.add(one);
                            }
                        }
                    } catch (Exception ex) {
                        logger.error(Lib.getTrace(ex));

                    }
                    fileId = ExportTables(dds, hds, keys, "NBDWMX");

                } else {

                }
                datas.put("file_id", fileId);
                back.put("data", datas);
                return back;

            } else {
                logger.error(bk);
                return ErrNo.set(null, 2, bk);
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }


    }

    private JSONObject GetNBGK(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = data.getString("unit");

        String dzs = data.getString("dzs");
        int type = RIUtil.dicts.get(data.getString("unit")).getIntValue("type");
        String ssdw = "";
        String u = "";
        if (type == 21 || type == 22 || type == 27) {
            // 市局的人
            ssdw = "320400000000";
            u = "3204";
        } else if (type == 23 || type == 24 || type == 28) {
            //分局的人
            u = unit.substring(0, 6);
            ssdw = u + "000000";

        } else if (type == 25) {
            // 派出所的人
            u = unit.substring(0, 8);
            ssdw = u + "0000";

        } else {
            // 责任区
            u = unit.substring(0, 8);
            ssdw = unit;
        }

        JSONObject para = new JSONObject();
        JSONObject p = new JSONObject();
        p.put("zzjgdm", ssdw);

        para.put("params", p);
        logger.warn(para.toString());


        try {
            OkHttpClient client =
                    new OkHttpClient().newBuilder().writeTimeout(60 * 5, TimeUnit.SECONDS).readTimeout(300,
                            TimeUnit.SECONDS).connectTimeout(300, TimeUnit.SECONDS).build();
            MediaType mediaType = MediaType.parse("application/json");

            RequestBody body = RequestBody.create(mediaType, para.toString());
            Request request =
                    new Request.Builder().url("http://50.56.93.205:8080/sjdcfw/event/nbdptj/query" + dzs).method(
                            "POST", body).addHeader("Content-Type", "application/json").build();
            Response response = client.newCall(request).execute();
            String bk = response.body().string();
            System.out.println(bk);
            JSONObject bjdon = JSONObject.parseObject(bk);
            try {
                if (bjdon.getString("tag").equals("succeed")) {

                    JSONArray res = bjdon.getJSONArray("results");

                    List<JSONObject> dets = new ArrayList<>();

                    for (int i = 0; i < res.size(); i++) {
                        JSONObject one = res.getJSONObject(i);
                        JSONObject det = new JSONObject();
                        String zzjgdm = "";
                        if (one.containsKey("zzjgdm")) {
                            zzjgdm = one.getString("zzjgdm");
                        }
                        if (one.containsKey("ssfxj")) {
                            zzjgdm = one.getString("ssfxj");
                        }
                        if (one.containsKey("sspcs")) {
                            zzjgdm = one.getString("sspcs");
                        }
                        if (one.containsKey("sszrq")) {
                            zzjgdm = one.getString("sszrq");
                        }
                        int count = 0;
                        if (one.containsKey("yhqs")) {
                            count = one.getIntValue("yhqs");
                        }
                        if (one.containsKey("jqqs")) {
                            count = one.getIntValue("jqqs");
                        }
                        if (one.containsKey("abzd")) {
                            count = one.getIntValue("abzd");
                        }
                        if (zzjgdm.startsWith(u) && !zzjgdm.equals(ssdw) && count > 0) {
                            det.put("unit", zzjgdm);
                            try {
                                det.put("index", RIUtil.dicts.get(zzjgdm).getInteger("index_no"));
                                det.put("name", one.getString("zzjgmc"));
                                det.put("count", count);

                                JSONObject dpclick = new JSONObject();
                                dpclick.put("id", String.valueOf(UUID.randomUUID()));
                                dpclick.put("type", "next");
                                dpclick.put("url", "/screen_unit");
                                JSONObject col = new JSONObject();
                                col.put("unit", zzjgdm);
                                col.put("dzs", dzs);
                                col.put("name", one.getString("zzjgmc"));
                                JSONObject opts = GetOpts("/screen_unit", "get_nbdw_gk", col);
                                dpclick.put("opt", opts);
                                dpclick.put("name", one.getString("zzjgmc"));
                                det.put("dpclick", dpclick);

                                JSONObject click = new JSONObject();
                                click.put("id", String.valueOf(UUID.randomUUID()));
                                click.put("permission", "table_page");
                                click.put("label", one.getString("zzjgmc") + "_明细列表");
                                click.put("type", "dialog");
                                col = new JSONObject();
                                col.put("unit", zzjgdm);
                                col.put("name", one.getString("zzjgmc"));
                                col.put("dzs", dzs);
                                opts = GetOpts("/screen_unit", "get_nbdw_gk_table", col);

                                click.put("remark", opts);
                                click.put("name", one.getString("zzjgmc"));
                                det.put("click", click);

                                dets.add(det);
                            } catch (Exception ex) {

                            }

                        }
                    }

                    Collections.sort(dets, (JSONObject o1, JSONObject o2) -> {
                        //转成JSON对象中保存的值类型
                        String a = "";
                        String b = "";

                        try {
                            a = o1.getString("index");
                            b = o2.getString("index");
                        } catch (Exception ex) {

                        }

                        int result = a.compareTo(b);
                        // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                        if (result > 0) {  //降序排列，升序改成a>b
                            return 1;
                        } else if (a == b) {
                            return 0;
                        } else {
                            return -1;
                        }
                    });


                    back.put("data", dets);

                    return back;
                } else {
                    return ErrNo.set(null, 2, bk);
                }
            } catch (Exception ex) {
                logger.error(bjdon.toString());
                return ErrNo.set(null, 2, Lib.getTrace(ex));

            }


        } catch (Exception ex) {
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }

    }

    private JSONObject getXfDwGk(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        String ssdw;
        int type = RIUtil.dicts.get(data.getString("unit")).getIntValue("type");

        if (type == 21 || type == 22 || type == 27) {
            // 市局的人
            ssdw = "";
        } else if (type == 23 || type == 24 || type == 28) {
            //分局的人
            ssdw = data.getString("unit").substring(0, 6) + "000000";
        } else if (type == 25) {
            // 派出所的人
            ssdw = data.getString("unit").substring(0, 8) + "0000";
        } else {
            // 责任区
            ssdw = data.getString("unit");
        }

        ThreadPoolExecutor executor = ExecutorBuilder.create().setCorePoolSize(5).setMaxPoolSize(10).build();
        try {


            // 三级消防安全重点单位数量
            JSONObject sjxf = new JSONObject();
            sjxf.put("glbm", "04");
            sjxf.put("yyzt", "00");
            sjxf.put("xfdj", "4");
            sjxf.put("sfst", "1");
            sjxf.put("gxdwws", "4");
            sjxf.put("token", data.getString("token"));
            sjxf.put("zzjgdm", data.getString("unit"));
            sjxf.put("ssdw", ssdw);
            CompletableFuture<JSONObject> sjxfResult = CompletableFuture.supplyAsync(() -> postSydw(sjxf), executor);

            // 监管范围
            JSONObject jgfw = new JSONObject();
            jgfw.put("token", data.getString("token"));
            jgfw.put("glbm", "04");
            jgfw.put("yyzt", "00");
            jgfw.put("xfdj", "5");
            jgfw.put("xflb", "04");
            jgfw.put("sfst", "1");
            jgfw.put("gxdwws", "4");
            jgfw.put("zzjgdm", data.getString("unit"));
            jgfw.put("ssdw", ssdw);
            CompletableFuture<JSONObject> jgfwResult = CompletableFuture.supplyAsync(() -> postSydw(jgfw), executor);

            // 住宅服务业
            JSONObject zzfw = new JSONObject();
            zzfw.put("glbm", "04");
            zzfw.put("yyzt", "00");
            zzfw.put("hydl", "311000");
            zzfw.put("hyxl", "311004");
            zzfw.put("sfst", "1");
            zzfw.put("gxdwws", "4");
            zzfw.put("token", data.getString("token"));
            zzfw.put("zzjgdm", data.getString("unit"));
            zzfw.put("ssdw", ssdw);
            CompletableFuture<JSONObject> zzfwResult = CompletableFuture.supplyAsync(() -> postSydw(zzfw), executor);

            //村民居委会
            JSONObject jwh = new JSONObject();
            jwh.put("glbm", "04");
            jwh.put("yyzt", "00");
            jwh.put("hydl", "304000");
            jwh.put("hyxl", "311004,304009");
            jwh.put("sfst", "1");
            jwh.put("gxdwws", "4");
            jwh.put("token", data.getString("token"));
            jwh.put("zzjgdm", data.getString("unit"));
            jwh.put("ssdw", ssdw);
            CompletableFuture<JSONObject> jwhResult = CompletableFuture.supplyAsync(() -> postSydw(jwh), executor);

            // 小单位小场所
            JSONObject xdw = new JSONObject();
            xdw.put("glbm", "04");
            xdw.put("yyzt", "00");
            xdw.put("xfdj", "5");
            xdw.put("xflb", "05");
            xdw.put("sfst", "1");
            xdw.put("gxdwws", "4");
            xdw.put("token", data.getString("token"));
            xdw.put("zzjgdm", data.getString("unit"));
            xdw.put("ssdw", ssdw);
            CompletableFuture<JSONObject> xdwResult = CompletableFuture.supplyAsync(() -> postSydw(xdw), executor);
            CompletableFuture.allOf(sjxfResult, jgfwResult, zzfwResult, jwhResult, xdwResult).get(60, TimeUnit.SECONDS);

            List<JSONObject> dataList = new ArrayList<>();
            JSONObject obj = new JSONObject();

//            JSONObject click = new JSONObject();
//            click.put("type", "jump");
//            click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/iframe/index?target=unit");
            JSONObject click = new JSONObject();
            click.put("label", "三级消防重点单位");
            click.put("type", "dialog");
            JSONObject remark = new JSONObject();
            remark.put("opt", "get_xfdw_mx");
            remark.put("url", "/screen_unit");
            remark.put("unit", data.getString("unit"));
            remark.put("token", data.getString("token"));
            remark.put("point", "1");
            click.put("permission", "table_page");
            click.put("remark", remark);
            click.put("id", String.valueOf(UUID.randomUUID()));


            obj.put("name", "三级消防重点单位数量");
            obj.put("count", sjxfResult.get().getString("total"));
            obj.put("click", click);
            dataList.add(obj);

            obj = new JSONObject();
            obj.put("name", "监管范围单位数量");
            obj.put("count", jgfwResult.get().getString("total"));
            click = new JSONObject();
            click.put("label", "监管范围单位");
            click.put("type", "dialog");
            remark = new JSONObject();
            remark.put("opt", "get_xfdw_mx");
            remark.put("url", "/screen_unit");
            remark.put("unit", data.getString("unit"));
            remark.put("point", "2");
            click.put("permission", "table_page");
            click.put("remark", remark);
            click.put("id", String.valueOf(UUID.randomUUID()));
            obj.put("click", click);
            dataList.add(obj);

            obj = new JSONObject();
            obj.put("name", "居民住宅物业服务企业");
            obj.put("count", zzfwResult.get().getString("total"));
            click = new JSONObject();
            click.put("label", "居民住宅物业服务企业");
            click.put("type", "dialog");
            remark = new JSONObject();
            remark.put("opt", "get_xfdw_mx");
            remark.put("url", "/screen_unit");
            remark.put("unit", data.getString("unit"));
            remark.put("point", "3");
            click.put("permission", "table_page");
            click.put("remark", remark);
            click.put("id", String.valueOf(UUID.randomUUID()));
            obj.put("click", click);
            dataList.add(obj);

            obj = new JSONObject();
            obj.put("name", "村(居)民委员会");
            obj.put("count", jwhResult.get().getString("total"));
            click = new JSONObject();
            click.put("label", "村(居)民委员会");
            click.put("type", "dialog");
            remark = new JSONObject();
            remark.put("opt", "get_xfdw_mx");
            remark.put("url", "/screen_unit");
            remark.put("unit", data.getString("unit"));
            remark.put("point", "4");
            click.put("permission", "table_page");
            click.put("remark", remark);
            click.put("id", String.valueOf(UUID.randomUUID()));
            obj.put("click", click);
            dataList.add(obj);

            obj = new JSONObject();
            obj.put("name", "小单位、小场所");
            obj.put("count", xdwResult.get().getString("total"));
            click = new JSONObject();
            click.put("label", "小单位、小场所");
            click.put("type", "dialog");
            remark = new JSONObject();
            remark.put("opt", "get_xfdw_mx");
            remark.put("url", "/screen_unit");
            remark.put("unit", data.getString("unit"));
            remark.put("point", "5");
            click.put("permission", "table_page");
            click.put("remark", remark);
            click.put("id", String.valueOf(UUID.randomUUID()));
            obj.put("click", click);
            dataList.add(obj);
            back.put("data", dataList);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            executor.shutdown();
        }
        return back;

    }

    //消防单位细明
    private JSONObject getXFXM(JSONObject data, String token) {
        JSONObject back = ErrNo.set(0);
        JSONObject dates = new JSONObject();
        String ssdw;
        int type = RIUtil.dicts.get(data.getString("unit")).getIntValue("type");

        if (type == 21 || type == 22 || type == 27) {
            // 市局的人
            ssdw = "";
        } else if (type == 23 || type == 24 || type == 28) {
            //分局的人
            ssdw = data.getString("unit").substring(0, 6) + "000000";
        } else if (type == 25) {
            // 派出所的人
            ssdw = data.getString("unit").substring(0, 8) + "0000";
        } else {
            // 责任区
            ssdw = data.getString("unit");
        }
        List<JSONObject> headList = new ArrayList<>();
        JSONObject head = new JSONObject();
        head.put("key", "dwmc");
        head.put("value", "单位名称");
        headList.add(head);

        head = new JSONObject();
        head.put("key", "dwdzmc");
        head.put("value", "单位地址名称");
        headList.add(head);

        head = new JSONObject();
        head.put("key", "glbm_zw");
        head.put("value", "管理部门");
        headList.add(head);


        head = new JSONObject();
        head.put("key", "ssfxj_zw");
        head.put("value", "所属分县区");
        headList.add(head);

        head = new JSONObject();
        head.put("key", "sspcs_zw");
        head.put("value", "所属派出所");
        headList.add(head);

        head = new JSONObject();
        head.put("key", "sszrq_zw");
        head.put("value", "所属责任区");
        headList.add(head);

        dates.put("head", headList);

        ThreadPoolExecutor executor = ExecutorBuilder.create().setCorePoolSize(5).setMaxPoolSize(10).build();
        try {


            // 三级消防安全重点单位数量
            JSONObject sjxf = new JSONObject();
            sjxf.put("glbm", "04");
            sjxf.put("yyzt", "00");
            sjxf.put("xfdj", "4,5");
            sjxf.put("sfst", "1");
            sjxf.put("gxdwws", "4");
            sjxf.put("token", token);
            sjxf.put("zzjgdm", data.getString("unit"));
            sjxf.put("ssdw", ssdw);
            sjxf.put("page", data.getInteger("page"));
            sjxf.put("limit", data.getInteger("limit"));
            CompletableFuture<JSONObject> sjxfResult = CompletableFuture.supplyAsync(() -> postSydw(sjxf), executor);

            // 监管范围
            JSONObject jgfw = new JSONObject();
            jgfw.put("token", token);
            jgfw.put("glbm", "04");
            jgfw.put("yyzt", "00");
            jgfw.put("xfdj", "5");
            jgfw.put("xflb", "04");
            jgfw.put("sfst", "1");
            jgfw.put("gxdwws", "4");
            jgfw.put("zzjgdm", data.getString("unit"));
            jgfw.put("ssdw", ssdw);
            CompletableFuture<JSONObject> jgfwResult = CompletableFuture.supplyAsync(() -> postSydw(jgfw), executor);

            // 住宅服务业
            JSONObject zzfw = new JSONObject();
            zzfw.put("glbm", "04");
            zzfw.put("yyzt", "00");
            zzfw.put("hydl", "311000");
            zzfw.put("hyxl", "311004");
            zzfw.put("sfst", "1");
            zzfw.put("gxdwws", "4");
            zzfw.put("token", token);
            zzfw.put("zzjgdm", data.getString("unit"));
            zzfw.put("ssdw", ssdw);
            CompletableFuture<JSONObject> zzfwResult = CompletableFuture.supplyAsync(() -> postSydw(zzfw), executor);

            //村民居委会
            JSONObject jwh = new JSONObject();
            jwh.put("glbm", "04");
            jwh.put("yyzt", "00");
            jwh.put("hydl", "304000");
            jwh.put("hyxl", "311004,304009");
            jwh.put("sfst", "1");
            jwh.put("gxdwws", "4");
            jwh.put("token", token);
            jwh.put("zzjgdm", data.getString("unit"));
            jwh.put("ssdw", ssdw);
            CompletableFuture<JSONObject> jwhResult = CompletableFuture.supplyAsync(() -> postSydw(jwh), executor);

            // 小单位小场所
            JSONObject xdw = new JSONObject();
            xdw.put("glbm", "04");
            xdw.put("yyzt", "00");
            xdw.put("xfdj", "5");
            xdw.put("xflb", "05");
            xdw.put("sfst", "1");
            xdw.put("gxdwws", "4");
            xdw.put("token", token);
            xdw.put("zzjgdm", data.getString("unit"));
            xdw.put("ssdw", ssdw);
            CompletableFuture<JSONObject> xdwResult = CompletableFuture.supplyAsync(() -> postSydw(xdw), executor);

            CompletableFuture.allOf(sjxfResult, jgfwResult, zzfwResult, jwhResult, xdwResult).get(60, TimeUnit.SECONDS);

            String point = data.getString("point");
            JSONArray results = new JSONArray();
            if ("1".equals(point)) {//三级
                results = sjxfResult.get().getJSONArray("results");
                dates.put("count", sjxfResult.get().getInteger("total"));
            }
            if ("2".equals(point)) {//监管范围
                results = jgfwResult.get().getJSONArray("results");
                dates.put("count", jgfwResult.get().getInteger("total"));
            }
            if ("3".equals(point)) {//居民住宅
                results = zzfwResult.get().getJSONArray("results");
                dates.put("count", zzfwResult.get().getInteger("total"));
            }
            if ("4".equals(point)) {//居民委员会
                results = jwhResult.get().getJSONArray("results");
                dates.put("count", jwhResult.get().getInteger("total"));
            }
            if ("5".equals(point)) {//小单位
                results = xdwResult.get().getJSONArray("results");
                dates.put("count", xdwResult.get().getInteger("total"));
            }
            for (int i = 0; i < results.size(); i++) {
                JSONObject one = results.getJSONObject(i);
                JSONObject click = new JSONObject();
                JSONObject value = new JSONObject();
                click.put("type", "jump_unit");
                click.put("url", one.getString("jgbh"));
                String dwmc = one.getString("dwmc");
                value.put("value", dwmc);
                value.put("click", click);
                one.put("dwmc", value);

                String dwdzmc = one.getString("dwdzmc");
                value = new JSONObject();
                value.put("value", dwdzmc);
                one.put("dwdzmc", value);

                String glbm_zw = one.getString("glbm_zw");
                value = new JSONObject();
                value.put("value", glbm_zw);
                one.put("glbm_zw", value);

                String ssfxj_zw = one.getString("ssfxj_zw");
                value = new JSONObject();
                value.put("value", ssfxj_zw);
                one.put("ssfxj_zw", value);

                String sspcs_zw = one.getString("sspcs_zw");
                value = new JSONObject();
                value.put("value", sspcs_zw);
                one.put("sspcs_zw", value);

                String sszrq_zw = one.getString("sszrq_zw");
                value = new JSONObject();
                value.put("value", sszrq_zw);
                one.put("sszrq_zw", value);
            }

            dates.put("body", results);
            back.put("data", dates);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            executor.shutdown();
        }
        return back;
    }


    // 实有单位
    private JSONObject postSydw(JSONObject data) {
        String url = "http://50.56.91.133:30281/czdwglfw/event/sydwsycx/getSycxList?faceKey=" + data.getString("token");

        JSONObject body = new JSONObject();

        JSONObject page = new JSONObject();
        page.put("curr", data.getInteger("page") == null ? 1 : data.getInteger("page"));
        page.put("limit", data.getInteger("limit") == null ? 20 : data.getInteger("limit"));
        body.put("page", page);

        JSONObject params = new JSONObject();
        params.put("glbm", data.getString("glbm") == null ? "" : data.getString("glbm"));
        params.put("ssdw", data.getString("ssdw") == null ? "" : data.getString("ssdw"));
        params.put("ssflag", data.getString("ssflag") == null ? "单位名称" : data.getString("ssflag"));
        params.put("yyzt", data.getString("yyzt") == null ? "" : data.getString("yyzt"));
        // 消防等级
        params.put("xfdj", data.getString("xfdj") == null ? "" : data.getString("xfdj"));
        //消防类别
        params.put("xflb", data.getString("xflb") == null ? "" : data.getString("xflb"));
        // 是否实体
        params.put("sfst", data.getString("sfst") == null ? "" : data.getString("sfst"));

        params.put("gxdwws", data.getString("gxdwws") == null ? "" : data.getString("gxdwws"));

        params.put("zzjgdm", data.getString("zzjgdm") == null ? "" : data.getString("zzjgdm"));

        params.put("hydl", data.getString("hydl") == null ? "" : data.getString("hydl"));
        params.put("hyxl", data.getString("hyxl") == null ? "" : data.getString("hyxl"));

        body.put("params", params);
        body.put("isQCount", "true");
        HttpRequest request =
                HttpRequest.post(url).form("faceKey", data.getString("token")).body(body.toJSONString()).timeout(1000 * 60 * 5);
//        logger.warn(request.toString());
        HttpResponse response = request.execute();
//        logger.warn(response.toString());
        if (response.isOk()) {
            String res = response.body();
            JSONObject object = JSONObject.parseObject(res);
//            logger.warn(object.toJSONString());
            if (object.containsKey("tag") && "succeed".equals(object.getString("tag"))) {
                return object;
            }
        }
        return new JSONObject();
    }


    private JSONObject getYjcXfDw(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        JSONObject param = new JSONObject();
        if (data.containsKey("zzjgdm") && StrUtil.isNotBlank(data.getString("zzjgdm"))) {
            param.put("zzjgdm", data.getString("zzjgdm"));
        }
        if (data.containsKey("grade") && StrUtil.isNotBlank(data.getString("grade"))) {
            param.put("grade", data.getString("grade"));
        }
        if (data.containsKey("kssj") && StrUtil.isNotBlank(data.getString("kssj"))) {
            param.put("kssj", data.getString("kssj"));
        }
        if (data.containsKey("jssj") && StrUtil.isNotBlank(data.getString("jssj"))) {
            param.put("jssj", data.getString("jssj"));
        }
        if (data.containsKey("limit") && StrUtil.isNotBlank(data.getString("limit"))) {
            param.put("limit", data.getString("limit"));
        }
        if (data.containsKey("page") && StrUtil.isNotBlank(data.getString("page"))) {
            param.put("page", data.getString("page"));
        }
        param.put("token", data.getString("token"));
        JSONObject response = postYjcXfDw(param);
        if (response != null) {

            JSONObject resData = new JSONObject();
            resData.put("count", response.getInteger("total"));

            List<JSONObject> headList = new ArrayList<>();
            JSONObject head = new JSONObject();
            head.put("key", "dwmc");
            head.put("value", "单位名称");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "dwdzmc");
            head.put("value", "单位地址名称");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "xm");
            head.put("value", "法人姓名");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "lxdh");
            head.put("value", "联系电话");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "ssfxj_zw");
            head.put("value", "所属分县区");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "sspcs_zw");
            head.put("value", "所属派出所");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "sszrq_zw");
            head.put("value", "所属责任区");
            headList.add(head);

            resData.put("head", headList);

            List<JSONObject> body = new ArrayList<>();
            JSONObject info;
            JSONObject value;
//            logger.warn("response:{}", response);
            List<JSONObject> javaList = response.getJSONArray("results").toJavaList(JSONObject.class);
            for (JSONObject object : javaList) {
                info = new JSONObject();
                for (Map.Entry<String, Object> entry : object.entrySet()) {
                    value = new JSONObject();
                    value.put("value", entry.getValue());
                    if (entry.getKey().equals("dwmc")) {
                        JSONObject click = new JSONObject();
                        click.put("type", "jump_unit");
                        click.put("id", String.valueOf(UUID.randomUUID()));
                        click.put("url", object.getString("jgbh"));
                        value.put("click", click);
                    }
                    info.put(entry.getKey(), value);

                }
                body.add(info);
            }
            resData.put("body", body);
            back.put("data", resData);
        }
        return back;
    }

    private JSONObject getXfYhs(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        JSONObject param = new JSONObject();
        if (data.containsKey("zzjgdm") && StrUtil.isNotBlank(data.getString("zzjgdm"))) {
            param.put("zzjgdm", data.getString("zzjgdm"));
        }
        if (data.containsKey("grade") && StrUtil.isNotBlank(data.getString("grade"))) {
            param.put("grade", data.getString("grade"));
        }
        if (data.containsKey("kssj") && StrUtil.isNotBlank(data.getString("kssj"))) {
            param.put("kssj", data.getString("kssj"));
        }
        if (data.containsKey("jssj") && StrUtil.isNotBlank(data.getString("jssj"))) {
            param.put("jssj", data.getString("jssj"));
        }
        if (data.containsKey("limit") && StrUtil.isNotBlank(data.getString("limit"))) {
            param.put("limit", data.getString("limit"));
        }
        if (data.containsKey("page") && StrUtil.isNotBlank(data.getString("page"))) {
            param.put("page", data.getString("page"));
        }
        param.put("token", data.getString("token"));
        JSONObject response = postXfYhs(param);
        if (response != null) {

            JSONObject resData = new JSONObject();
            resData.put("count", response.getInteger("total"));

            List<JSONObject> headList = new ArrayList<>();
            JSONObject head = new JSONObject();
            head.put("key", "dwmc");
            head.put("value", "单位名称");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "xmmc");
            head.put("value", "隐患项名称");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "yhnr");
            head.put("value", "隐患内容");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "ssfxj_zw");
            head.put("value", "所属分县区");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "sspcs_zw");
            head.put("value", "所属派出所");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "sszrq_zw");
            head.put("value", "所属责任区");
            headList.add(head);

            resData.put("head", headList);

            List<JSONObject> body = new ArrayList<>();
            JSONObject info;
            JSONObject value;


//            logger.warn("response:{}", response);
            List<JSONObject> javaList = response.getJSONArray("results").toJavaList(JSONObject.class);
            for (JSONObject object : javaList) {
                info = new JSONObject();
                for (Map.Entry<String, Object> entry : object.entrySet()) {
                    value = new JSONObject();
                    value.put("value", entry.getValue());
                    if (entry.getKey().equals("dwmc")) {
                        JSONObject click = new JSONObject();
                        click.put("type", "jump_unit");
                        click.put("id", String.valueOf(UUID.randomUUID()));
                        click.put("url", object.getString("jgbh"));
                        value.put("click", click);
                    }
                    info.put(entry.getKey(), value);
                }
                value = new JSONObject();
                if (StrUtil.isNotBlank(object.getString("zxz")) && StrUtil.isNotBlank(object.getString("jcbxz")) &&
                        "文本".equals(object.getString("jcbxz"))) {
                    value.put("value", object.getString("zxz"));
                } else if (object.containsKey("zxz") && StrUtil.isNotBlank(object.getString("zxz")) && object.containsKey("jcbxz")) {
                    if ("无".equals(object.getString("zxz"))) {
                        value.put("value", "");
                    } else {
                        value.put("value", object.getString("jcbxz").split(",")[object.getInteger("zxz")]);
                    }
                }
                info.put("yhnr", value);
                body.add(info);
            }

            resData.put("body", body);
            back.put("data", resData);
        }
        return back;
    }

    // 已经检查消防单位
    private JSONObject postYjcXfDw(JSONObject data) {
        String url = "http://50.56.93.205:8080/sjdcfw/event/sjtj/queryJcXfDw?faceKey=" + data.getString("token");

        JSONObject body = new JSONObject();

        JSONObject page = new JSONObject();
        page.put("curr", data.getInteger("page") == null ? 1 : data.getInteger("page"));
        page.put("limit", data.getInteger("limit") == null ? 10 : data.getInteger("limit"));
        body.put("page", page);

        JSONObject params = new JSONObject();
        params.put("tjlb", data.getString("tjlb") == null ? "" : data.getString("tjlb"));
        params.put("hylb", data.getString("hylb") == null ? "" : data.getString("hylb"));
        params.put("zzjgdm", data.getString("zzjgdm") == null ? "" : data.getString("zzjgdm"));
        params.put("grade", data.getString("grade") == null ? "" : data.getString("grade"));
        params.put("kssj", data.getString("kssj") == null ? "" : data.getString("kssj"));
        params.put("jssj", data.getString("jssj") == null ? "" : data.getString("jssj"));
        body.put("params", params);
        body.put("isQCount", "true");
        HttpRequest request =
                HttpRequest.post(url).form("faceKey", data.getString("token")).body(body.toJSONString()).timeout(1000 * 60 * 5);
//        logger.warn(request.toString());
        HttpResponse response = request.execute();
//        logger.warn(response.toString());
        if (response.isOk()) {
            String res = response.body();
            JSONObject object = JSONObject.parseObject(res);
//            logger.warn(object.toJSONString());
            if (object.containsKey("tag") && "succeed".equals(object.getString("tag"))) {
                return object;
            }
        }
        return null;
    }


    // 消防发现隐患数
    private JSONObject postXfYhs(JSONObject data) {
        String url = "http://50.56.93.205:8080/sjdcfw/event/sjtj/queryYhslDw?faceKey=" + data.getString("token");

        JSONObject body = new JSONObject();

        JSONObject page = new JSONObject();
        page.put("curr", data.getInteger("page") == null ? 1 : data.getInteger("page"));
        page.put("limit", data.getInteger("limit") == null ? 10 : data.getInteger("limit"));
        body.put("page", page);

        JSONObject params = new JSONObject();
        params.put("tjlb", data.getString("tjlb") == null ? "" : data.getString("tjlb"));
        params.put("hylb", data.getString("hylb") == null ? "" : data.getString("hylb"));
        params.put("zzjgdm", data.getString("zzjgdm") == null ? "" : data.getString("zzjgdm"));
        params.put("grade", data.getString("grade") == null ? "" : data.getString("grade"));
        params.put("kssj", data.getString("kssj") == null ? "" : data.getString("kssj"));
        params.put("jssj", data.getString("jssj") == null ? "" : data.getString("jssj"));
        body.put("params", params);
        body.put("isQCount", "true");
        HttpRequest request =
                HttpRequest.post(url).form("faceKey", data.getString("token")).body(body.toJSONString()).timeout(1000 * 60 * 5);
//        logger.warn(request.toString());
        HttpResponse response = request.execute();
//        logger.warn(response.toString());
        if (response.isOk()) {
            String res = response.body();
            JSONObject object = JSONObject.parseObject(res);
//            logger.warn(object.toJSONString());
            if (object.containsKey("tag") && "succeed".equals(object.getString("tag"))) {
                return object;
            }
        }
        return null;
    }


    private JSONObject getXfWzg(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        JSONObject param = new JSONObject();
        if (data.containsKey("zzjgdm") && StrUtil.isNotBlank(data.getString("zzjgdm"))) {
            param.put("zzjgdm", data.getString("zzjgdm"));
        }
        if (data.containsKey("grade") && StrUtil.isNotBlank(data.getString("grade"))) {
            param.put("grade", data.getString("grade"));
        }
        if (data.containsKey("kssj") && StrUtil.isNotBlank(data.getString("kssj"))) {
            param.put("kssj", data.getString("kssj"));
        }
        if (data.containsKey("jssj") && StrUtil.isNotBlank(data.getString("jssj"))) {
            param.put("jssj", data.getString("jssj"));
        }
        if (data.containsKey("limit") && StrUtil.isNotBlank(data.getString("limit"))) {
            param.put("limit", data.getString("limit"));
        }
        if (data.containsKey("page") && StrUtil.isNotBlank(data.getString("page"))) {
            param.put("page", data.getString("page"));
        }
        param.put("token", data.getString("token"));
        JSONObject response = postXfWzg(param);
        if (response != null) {

            JSONObject resData = new JSONObject();
            resData.put("count", response.getInteger("total"));

            List<JSONObject> headList = new ArrayList<>();
            JSONObject head = new JSONObject();
            head.put("key", "dwmc");
            head.put("value", "单位名称");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "xmmc");
            head.put("value", "隐患项名称");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "yhnr");
            head.put("value", "隐患内容");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "ssfxj_zw");
            head.put("value", "所属分县区");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "sspcs_zw");
            head.put("value", "所属派出所");
            headList.add(head);

            head = new JSONObject();
            head.put("key", "sszrq_zw");
            head.put("value", "所属责任区");
            headList.add(head);


            resData.put("head", headList);

            List<JSONObject> body = new ArrayList<>();
            JSONObject info;
            JSONObject value;


            logger.warn("response:{}", response);
            List<JSONObject> javaList = response.getJSONArray("results").toJavaList(JSONObject.class);
            for (JSONObject object : javaList) {
                info = new JSONObject();
                for (Map.Entry<String, Object> entry : object.entrySet()) {
                    value = new JSONObject();
                    value.put("value", entry.getValue());
                    if (entry.getKey().equals("dwmc")) {
                        JSONObject click = new JSONObject();
                        click.put("type", "jump_unit");
                        click.put("id", String.valueOf(UUID.randomUUID()));
                        click.put("url", object.getString("jgbh"));
                        value.put("click", click);
                    }
                    info.put(entry.getKey(), value);


                }
                value = new JSONObject();
                if (object.containsKey("zxz") && StrUtil.isNotBlank(object.getString("zxz")) && object.containsKey(
                        "jcbxz")) {
                    if ("无".equals(object.getString("zxz"))) {
                        value.put("value", "");
                    } else {
                        int index = 0;
                        index = "0".equals(object.getString("zxz")) || "1".equals(object.getString("zxz")) ?
                                object.getInteger("zxz") : 0;
                        value.put("value", object.getString("jcbxz").split(",")[index]);
                    }

                }
                info.put("yhnr", value);
                body.add(info);
            }

            resData.put("body", body);
            back.put("data", resData);
        }
        return back;
    }


    private JSONObject postXfWzg(JSONObject data) {
        String url = "http://50.56.93.205:8080/sjdcfw/event/sjtj/queryFcbhgyhslDw?faceKey=" + data.getString("token");
        logger.warn("=====>>> url :  " + url);
        JSONObject body = new JSONObject();

        JSONObject page = new JSONObject();
        page.put("curr", data.getInteger("page") == null ? 1 : data.getInteger("page"));
        page.put("limit", data.getInteger("limit") == null ? 10 : data.getInteger("limit"));
        body.put("page", page);

        JSONObject params = new JSONObject();
        params.put("tjlb", data.getString("tjlb") == null ? "" : data.getString("tjlb"));
        params.put("hylb", data.getString("hylb") == null ? "" : data.getString("hylb"));
        params.put("zzjgdm", data.getString("zzjgdm") == null ? "" : data.getString("zzjgdm"));
        params.put("grade", data.getString("grade") == null ? "" : data.getString("grade"));
        params.put("kssj", data.getString("kssj") == null ? "" : data.getString("kssj"));
        params.put("jssj", data.getString("jssj") == null ? "" : data.getString("jssj"));
        body.put("params", params);
        body.put("isQCount", "true");
        HttpRequest request =
                HttpRequest.post(url).form("faceKey", data.getString("token")).body(body.toJSONString()).timeout(1000 * 30);
//        logger.warn("=========>>>"+request.toString());
        HttpResponse response = request.execute();
        logger.warn(response.toString());
        if (response.isOk()) {
            String res = response.body();
            JSONObject object = JSONObject.parseObject(res);
//            logger.warn(object.toJSONString());
            if (object.containsKey("tag") && "succeed".equals(object.getString("tag"))) {
                return object;
            }
        }
        return null;
    }


    // post 请求 保通消防统计
    private List<JSONObject> postXftj(JSONObject data) {
        String url = "http://50.56.93.205:8080/sjdcfw/event/sjtj/queryXftj?faceKey=" + data.getString("token");

        JSONObject body = new JSONObject();

        JSONObject page = new JSONObject();
        page.put("curr", 1);
        page.put("limit", 1000);
        body.put("page", page);

        JSONObject params = new JSONObject();
        params.put("tjlb", data.getString("tjlb") == null ? "" : data.getString("tjlb"));
        params.put("hylb", data.getString("hylb") == null ? "" : data.getString("hylb"));
        params.put("zzjgdm", data.getString("zzjgdm") == null ? "" : data.getString("zzjgdm"));
        params.put("grade", data.getString("grade") == null ? "" : data.getString("grade"));
        params.put("kssj", data.getString("kssj") == null ? "" : data.getString("kssj"));
        params.put("jssj", data.getString("jssj") == null ? "" : data.getString("jssj"));
        params.put("rwlx", data.getString("rwlx") == null ? "" : data.getString("rwlx"));
        params.put("ssrwb", data.getString("ssrwb") == null ? "" : data.getString("ssrwb"));
        body.put("params", params);


        body.put("isQCount", "true");
        HttpRequest request =
                HttpRequest.post(url).form("faceKey", data.getString("token")).body(body.toJSONString()).timeout(1000 * 30);
        HttpResponse response = request.execute();
//        logger.warn(response.toString());
        if (response.isOk()) {
            String res = response.body();
            JSONObject object = JSONObject.parseObject(res);
//            logger.warn(object.toJSONString());
            try {
                if (object.containsKey("tag") && "succeed".equals(object.getString("tag"))) {
                    JSONArray results = object.getJSONArray("results");
                    List<JSONObject> javaList = results.toJavaList(JSONObject.class);
                    return javaList;
                }
            } catch (Exception ex) {
                logger.error(res);
                logger.error(Lib.getTrace(ex));
                return Collections.emptyList();
            }
        }
        return Collections.emptyList();
    }


    private JSONObject getXfFx(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        // 消防隐患 发现率
        // 发现隐患数 / 检查单位数
        // xfjccount

        MysqlHelper mysql = null;

        MysqlHelper czoa = null;
        try {
            mysql = new MysqlHelper("mysql_zxqc");
            czoa = new MysqlHelper("mysql");
            String unit = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(null, 2, "缺少参数");
            }
            int type = RIUtil.dicts.get(unit).getInteger("type");
            JSONObject param = new JSONObject();

            if (data.containsKey("start_time") && StrUtil.isNotBlank(data.getString("start_time"))) {
                param.put("kssj", data.getString("start_time").replace("-", ""));
            }
            if (data.containsKey("end_time") && StrUtil.isNotBlank(data.getString("end_time"))) {
                param.put("jssj", data.getString("end_time").replace("-", ""));
            }

            if (type == 21 || type == 22 || type == 27) {
                // 市局的人
                param.put("zzjgdm", unit);
                param.put("grade", 2);
            } else if (type == 23 || type == 24 || type == 28) {
                //分局的人
                param.put("zzjgdm", unit);
                param.put("grade", 3);
            } else if (type == 25) {
                // 派出所的人
                param.put("zzjgdm", unit);
                param.put("grade", 4);
            } else {
                // 责任区
                param.put("zzjgdm", unit);
                param.put("grade", 4);
            }
            DecimalFormat df = new DecimalFormat("0.00");

            // 数量
            List<JSONObject> countList = new ArrayList<>();

            // 发现率
            List<JSONObject> fxlList = new ArrayList<>();
            //  隐患数
            List<JSONObject> yhList = new ArrayList<>();
            param.put("token", data.getString("token"));

            List<JSONObject> results = postXftj(param);
            for (JSONObject result : results) {
                if (result.containsKey("dwdm") && StrUtil.isNotBlank(result.getString("dwdm"))) {
                    String org = result.getString("dwdm");
                    Integer all = result.getInteger("xfjccount");
                    // 隐患数
                    Integer yhs = result.getInteger("yhsl");
                    JSONObject dpclickObj = new JSONObject();
                    dpclickObj.put("url", "/screen_unit");
                    JSONObject opts = new JSONObject();
                    opts.put("unit", org);
                    opts.put("name", RIUtil.dicts.get(org).getString("dict_name"));
                    opts.put("url", "/screen_unit");
                    opts.put("start_time", "$start_time$");
                    opts.put("end_time", "$end_time$");
                    opts.put("opt", "get_xf_fx");
                    dpclickObj.put("type", "next");
                    dpclickObj.put("opt", opts);
                    dpclickObj.put("id", String.valueOf(UUID.randomUUID()));


                    // 单机事件
                    JSONObject click = new JSONObject();
                    click.put("label", "发现隐患数");
                    click.put("type", "dialog");
                    JSONObject remark = new JSONObject();
                    remark.put("opt", "get_xf_yhs");
                    remark.put("zzjgdm", org);
                    remark.put("url", "/screen_unit");
                    remark.put("kssj", param.getString("kssj"));
                    remark.put("jssj", param.getString("jssj"));
                    remark.put("grade", param.getString("grade"));
                    click.put("permission", "table_page");
                    click.put("remark", remark);
                    click.put("id", String.valueOf(UUID.randomUUID()));

                    JSONObject count = new JSONObject();
                    count.put("code", org);
                    count.put("name", RIUtil.dicts.get(org).getString("dict_name"));
                    count.put("count", all);
                    count.put("id", org);

                    count.put("dpclick", dpclickObj);
                    count.put("click", click);
                    countList.add(count);


                    // 发现率
                    JSONObject fxlObj = new JSONObject();
                    fxlObj.put("code", org);
                    fxlObj.put("name", RIUtil.dicts.get(org).getString("dict_name"));
                    double correctionRate = (all != 0) ? ((double) yhs / all) * 100 : 0;
                    fxlObj.put("count", df.format(correctionRate));
                    fxlObj.put("id", org);
                    fxlObj.put("dpclick", dpclickObj);
                    fxlObj.put("click", click);
                    fxlList.add(fxlObj);

                    // 消防单位隐患数
                    JSONObject yhsObj = new JSONObject();
                    yhsObj.put("code", org);
                    yhsObj.put("name", RIUtil.dicts.get(org).getString("dict_name"));
                    yhsObj.put("count", yhs);
                    yhsObj.put("id", org);
                    yhsObj.put("dpclick", dpclickObj);
                    yhsObj.put("click", click);
                    yhList.add(yhsObj);
                }
            }

            List<JSONObject> list = new ArrayList<>();
            JSONObject zgl = new JSONObject();
            zgl.put("xx", "line");
            zgl.put("name", "发现率");
            zgl.put("id", 1);
            zgl.put("det", fxlList);
            zgl.put("sign", "%");
            list.add(zgl);

            JSONObject count = new JSONObject();
            count.put("xx", "bar");
            count.put("name", "检查总数");
            count.put("id", 2);
            count.put("det", countList);
            list.add(count);


            JSONObject wzg = new JSONObject();
            wzg.put("xx", "bar");
            wzg.put("name", "隐患数");
            wzg.put("id", 3);
            wzg.put("det", yhList);
            list.add(wzg);

            back.put("data", list);

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            if (mysql != null) {
                mysql.close();
            }
            if (czoa != null) {
                czoa.close();
            }
        }
        return back;
    }

    private JSONObject getXfJcQk(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        // 消防单位数
        // 205-03003000
        // 205-03014005 消防单位为整改数
        // 已整改数两个相减

        MysqlHelper mysql = null;

        MysqlHelper czoa = null;
        try {
            mysql = new MysqlHelper("mysql_zxqc");
            czoa = new MysqlHelper("mysql");
            String unit = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(null, 2, "缺少参数");
            }
            int type = RIUtil.dicts.get(unit).getInteger("type");
            JSONObject param = new JSONObject();

            if (data.containsKey("start_time") && StrUtil.isNotBlank(data.getString("start_time"))) {
                param.put("kssj", data.getString("start_time").replace("-", ""));
            }
            if (data.containsKey("end_time") && StrUtil.isNotBlank(data.getString("end_time"))) {
                param.put("jssj", data.getString("end_time").replace("-", ""));
            }

            if (type == 21 || type == 22 || type == 27) {
                // 市局的人
                param.put("zzjgdm", unit);
                param.put("grade", 2);
            } else if (type == 23 || type == 24 || type == 28) {
                //分局的人
                param.put("zzjgdm", unit);
                param.put("grade", 3);
            } else if (type == 25) {
                // 派出所的人

                param.put("zzjgdm", unit);
                param.put("grade", 4);
            } else {
                // 责任区
                param.put("zzjgdm", unit);
                param.put("grade", 4);
            }
            // 单位总数
            List<JSONObject> countList = new ArrayList<>();

            // 检查数
            List<JSONObject> jcList = new ArrayList<>();
            param.put("token", data.getString("token"));

            List<JSONObject> results = postXftj(param);
            for (JSONObject result : results) {
                if (result.containsKey("dwdm") && StrUtil.isNotBlank(result.getString("dwdm"))) {
                    String org = result.getString("dwdm");
                    // 单位总数
                    Integer all = result.getInteger("xfdwcount");
                    // 检查数
                    Integer jcs = result.getInteger("xfjccount");

                    JSONObject dpclickObj = new JSONObject();
                    dpclickObj.put("url", "/screen_unit");
                    JSONObject opts = new JSONObject();
                    opts.put("unit", org);
                    opts.put("name", RIUtil.dicts.get(org).getString("dict_name"));
                    opts.put("url", "/screen_unit");
                    opts.put("start_time", "$start_time$");
                    opts.put("end_time", "$end_time$");
                    opts.put("opt", "get_xf_jc_qk");
                    dpclickObj.put("type", "next");
                    dpclickObj.put("opt", opts);
                    dpclickObj.put("id", String.valueOf(UUID.randomUUID()));

                    JSONObject click = new JSONObject();
                    click.put("label", "已检查消防单位");
                    click.put("type", "dialog");
                    JSONObject remark = new JSONObject();
                    remark.put("opt", "get_yjc_xf_dw");
                    remark.put("zzjgdm", org);
                    remark.put("url", "/screen_unit");
                    remark.put("kssj", param.getString("kssj"));
                    remark.put("jssj", param.getString("jssj"));
                    remark.put("grade", param.getString("grade"));
                    click.put("permission", "table_page");
                    click.put("remark", remark);
                    click.put("id", String.valueOf(UUID.randomUUID()));

                    JSONObject count = new JSONObject();
                    count.put("code", org);
                    count.put("name", RIUtil.dicts.get(org).getString("dict_name"));
                    count.put("count", all);
                    count.put("id", org);
                    if (type != 26) {
                        count.put("dpclick", dpclickObj);
                    }
                    count.put("click", click);
                    countList.add(count);


                    JSONObject jcObj = new JSONObject();
                    jcObj.put("code", org);
                    jcObj.put("name", RIUtil.dicts.get(org).getString("dict_name"));
                    jcObj.put("count", jcs);
                    jcObj.put("id", org);
                    if (type != 26) {
                        jcObj.put("dpclick", dpclickObj);
                    }
                    jcObj.put("click", click);
                    jcList.add(jcObj);
                }
            }

            List<JSONObject> list = new ArrayList<>();
            JSONObject zgl = new JSONObject();
            zgl.put("name", "消防单位检查数");
            zgl.put("id", 1);
            zgl.put("det", jcList);
            list.add(zgl);

            JSONObject count = new JSONObject();
            count.put("name", "消防单位总数");
            count.put("id", 2);
            count.put("det", countList);
            list.add(count);
            back.put("data", list);

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            if (mysql != null) {
                mysql.close();
            }
            if (czoa != null) {
                czoa.close();
            }
        }
        return back;
    }

    private JSONObject getXfZg(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        // 消防隐患整改率
        // 已整改数 / 发现隐患总数
        // 205-03014003 消防单位隐患数   205-03014004 消防单位为整改数
        // 已整改数两个相减

        MysqlHelper mysql = null;

        MysqlHelper czoa = null;
        try {
            JSONObject param = new JSONObject();
            mysql = new MysqlHelper("mysql_zxqc");
            czoa = new MysqlHelper("mysql");
            String unit = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(null, 2, "缺少参数");
            }

            if (data.containsKey("start_time") && StrUtil.isNotBlank(data.getString("start_time"))) {
                param.put("kssj", data.getString("start_time").replace("-", ""));
            }
            if (data.containsKey("end_time") && StrUtil.isNotBlank(data.getString("end_time"))) {
                param.put("jssj", data.getString("end_time").replace("-", ""));
            }

            int type = RIUtil.dicts.get(unit).getInteger("type");


            if (type == 21 || type == 22 || type == 27) {
                // 市局的人
                param.put("zzjgdm", unit);
                param.put("grade", 2);
            } else if (type == 23 || type == 24 || type == 28) {
                //分局的人
                param.put("zzjgdm", unit);
                param.put("grade", 3);
            } else if (type == 25) {
                // 派出所的人

                param.put("zzjgdm", unit);
                param.put("grade", 4);
            } else {
                // 责任区
                param.put("zzjgdm", unit);
                param.put("grade", 4);
            }


            // 数量
            List<JSONObject> countList = new ArrayList<>();

            // 整改率
            List<JSONObject> zglList = new ArrayList<>();
            //  未整改
            List<JSONObject> yzgList = new ArrayList<>();

            DecimalFormat df = new DecimalFormat("0.00");

            param.put("toekn", data.getString("token"));
            List<JSONObject> results = postXftj(param);


            for (JSONObject result : results) {
                if (StrUtil.isNotBlank(result.getString("dwdm"))) {
                    String org = result.getString("dwdm");
                    Integer all = result.getInteger("yhsl");

                    JSONObject dpclickObj = new JSONObject();
                    dpclickObj.put("url", "/screen_unit");
                    JSONObject opts = new JSONObject();
                    opts.put("unit", org);
                    opts.put("name", RIUtil.dicts.get(org).getString("dict_name"));
                    opts.put("url", "/screen_unit");
                    opts.put("start_time", "$start_time$");
                    opts.put("end_time", "$end_time$");
                    opts.put("opt", "get_xf_zg");
                    dpclickObj.put("type", "next");
                    dpclickObj.put("opt", opts);
                    dpclickObj.put("id", String.valueOf(UUID.randomUUID()));

                    //穿到dinlog
                    JSONObject click = new JSONObject();
                    click.put("label", "消防未整改");
                    click.put("type", "dialog");
                    JSONObject remark = new JSONObject();
                    remark.put("opt", "get_xf_wzg");
                    remark.put("zzjgdm", org);
                    remark.put("url", "/screen_unit");
                    remark.put("kssj", param.getString("kssj"));
                    remark.put("jssj", param.getString("jssj"));
                    remark.put("grade", param.getString("grade"));
                    click.put("permission", "table_page");
                    click.put("remark", remark);
                    click.put("id", String.valueOf(UUID.randomUUID()));


                    // 已经整改
                    Integer yzg = result.getInteger("dczgyhsl") + result.getInteger("yzgyhsl");

                    JSONObject count = new JSONObject();
                    count.put("code", org);
                    count.put("name", RIUtil.dicts.get(org).getString("dict_name"));
                    count.put("count", all);
                    count.put("id", org);
                    if (type != 26) {
                        count.put("dpclick", dpclickObj);
                    }
                    count.put("click", click);
                    countList.add(count);

                    // 整改率 已整改 除以隐患总数
                    JSONObject zgl = new JSONObject();
                    zgl.put("code", org);
                    zgl.put("name", RIUtil.dicts.get(org).getString("dict_name"));
                    double correctionRate = (all != 0) ? ((double) yzg / all) * 100 : 100;
                    zgl.put("count", df.format(correctionRate));
                    zgl.put("id", org);
                    zgl.put("dpclick", dpclickObj);
                    zgl.put("click", click);
                    zglList.add(zgl);

                    // 为整改数
                    JSONObject yzgObj = new JSONObject();
                    yzgObj.put("code", org);
                    yzgObj.put("name", RIUtil.dicts.get(org).getString("dict_name"));
                    yzgObj.put("count", yzg);
                    yzgObj.put("id", org);
                    yzgObj.put("dpclick", dpclickObj);
                    yzgObj.put("click", click);
                    yzgList.add(yzgObj);
                }
            }


//            for (Map.Entry<String, List<JSONObject>> entry : code.entrySet()) {
//                String org = entry.getKey();
//                // 总数
//                Integer all = 0;
//                // 未整改
//                Integer wzg = 0;
//                for (JSONObject object : entry.getValue()) {
//                    if (object.getString("type").equals("205-03014003")) {
//                        all = object.getInteger("count");
//                    }
//                    if (object.getString("type").equals("205-03014004")) {
//                        wzg = object.getInteger("count");
//                    }
//                }
//                JSONObject dpclickObj = new JSONObject();
//                dpclickObj.put("url", "/screen_unit");
//                JSONObject opts = new JSONObject();
//                opts.put("unit", org);
//                opts.put("name", RIUtil.dicts.get(org).getString("dict_name"));
//                opts.put("url", "/screen_unit");
//                dpclickObj.put("opt", opts);
//
//                // 已经整改
//                Integer yzg = all - wzg;
//
//                JSONObject count = new JSONObject();
//                count.put("code", org);
//                count.put("name", RIUtil.dicts.get(org).getString("dict_name"));
//                count.put("count", all);
//                count.put("id", org);
//
//                count.put("dpclick", dpclickObj);
//                countList.add(count);
//
//                // 整改率 已整改 除以隐患总数
//                JSONObject zgl = new JSONObject();
//                zgl.put("code", org);
//                zgl.put("name", RIUtil.dicts.get(org).getString("dict_name"));
//                double correctionRate = (all != 0) ? ((double) yzg / all) * 100 : 0;
//                zgl.put("count", df.format(correctionRate));
//                zgl.put("test", "总数:" + all + " 已整改:" + yzg + " 整改率:" + correctionRate);
//                zgl.put("id", org);
//                zgl.put("dpclick", dpclickObj);
//                zglList.add(zgl);
//
//                // 为整改数
//                JSONObject wzgObj = new JSONObject();
//                wzgObj.put("code", org);
//                wzgObj.put("name", RIUtil.dicts.get(org).getString("dict_name"));
//                wzgObj.put("count", wzg);
//                wzgObj.put("id", org);
//                wzgObj.put("dpclick", dpclickObj);
//                wzgList.add(wzgObj);
//
//            }

            List<JSONObject> list = new ArrayList<>();
            JSONObject zgl = new JSONObject();
            zgl.put("xx", "line");
            zgl.put("name", "整改率");
            zgl.put("id", 1);
            zgl.put("det", zglList);
            zgl.put("sign", "%");
            list.add(zgl);

            JSONObject count = new JSONObject();
            count.put("xx", "bar");
            count.put("name", "隐患总数");
            count.put("id", 2);
            count.put("det", countList);
            list.add(count);


            JSONObject wzg = new JSONObject();
            wzg.put("xx", "bar");
            wzg.put("name", "已整改总数");
            wzg.put("id", 3);
            wzg.put("det", yzgList);
            list.add(wzg);

            back.put("data", list);


        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            if (mysql != null) {
                mysql.close();
            }
            if (czoa != null) {
                czoa.close();
            }
        }
        return back;
    }

    private JSONObject getPointUnitLevel(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String lx = data.getString("lx");

        String subStr = "";
        String unit = "";
        String type = "";
        String uSql = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            type = RIUtil.dicts.get(unit).getString("type");
            if (type.equals("23") || type.equals("24") || type.equals("28")) {
                subStr = "substr(\"SSZRQ\",0,8) ";
                uSql = " and \"SSZRQ\" like '" + unit.substring(0, 6) + "%' ";
            } else if (type.equals("25") || type.equals("26")) {
                subStr = "\"SSZRQ\" ";
                uSql = " and \"SSZRQ\" like '" + unit.substring(0, 8) + "%' ";
            } else {
                subStr = "substr(\"SSZRQ\",0,6) ";

            }
        } else {
            return ErrNo.set(501001);
        }
        GaussHelper gs = null;
        try {
            gs = new GaussHelper("gauss_hl");
            String sql =
                    "select count(1) as count ," + subStr + " as code from qjjc.qjjc_dw1 where " + "\"JCLX_XL\"" + " "
                            + "='" + lx + "' " + uSql + " group by " + subStr;
            logger.warn(sql);
            JSONArray dets = new JSONArray();
            List<JSONObject> list = gs.query(sql);
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String code = one.getString("code");
                if (code.length() == 6) {
                    code = code + "000000";
                } else if (code.length() == 8) {
                    code = code + "0000";
                } else {
                    code = code;
                }
                String name = RIUtil.dicts.get(code).getString("dict_name");
                JSONObject click = new JSONObject();
                click.put("type", "next");
                click.put("url", "/screen_unit");
                click.put("name", RIUtil.dicts.get(code).getString("dict_name"));
                click.put("id", String.valueOf(UUID.randomUUID()));
                JSONObject cols = new JSONObject();
                cols.put("lx", lx);
                cols.put("unit", code);

                click.put("opt", GetOpts("/screen_unit", "get_point_unit_level", cols));
                one.put("dpclick", click);
                one.put("name", name);

                JSONObject dpclick = new JSONObject();
                dpclick.put("type", "dialog1");
                dpclick.put("label", name + "_" + RIUtil.dicts.get("160-" + lx).getString("dict_name"));
                cols = new JSONObject();
                cols.put("point", lx);
                cols.put("lx", lx);
                cols.put("unit", unit);
                cols.put("name", name);
                JSONObject opts = GetOpts("/jqzt", "get_point_loc_table", cols);
                dpclick.put("remark", opts);
                dpclick.put("permission", "table_page");
                dpclick.put("id", String.valueOf(UUID.randomUUID()));
                one.put("click", dpclick);

                dets.add(one);
            }
            back.put("data", dets);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            gs.close();

        }
    }

    private JSONObject getPointUnit(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper mysql = null;
        GaussHelper gs_hl = null;
        String unit = "";
        String type = "";
        String gsUsql = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            type = RIUtil.dicts.get(unit).getString("type");
            if (type.equals("23") || type.equals("24") || type.equals("28")) {

                gsUsql = "\"SSZRQ\" like '" + unit.substring(0, 6) + "%'";
            } else if (type.equals("25")) {

                gsUsql = "\"SSZRQ\" like '" + unit.substring(0, 8) + "%'";
            } else if (type.equals("26")) {

                gsUsql = "\"SSZRQ\" = '" + unit + "'";
            } else {

                gsUsql = "1=1";
            }
        } else {
            return ErrNo.set(501001);
        }
        try {

            mysql = new MysqlHelper("mysql");

            gs_hl = new GaussHelper("gauss_hl");
            HashMap<String, String> gsXls = new HashMap<>();
            List<JSONObject> gsList = new ArrayList<>();
            String sql = "";
            if (!gsUsql.contains("1=1")) {

                sql = "select count(qjjc.qjjc_dw1.\"ID\") as \"COUNT\",\"JCLX_XL\" from qjjc.qjjc_dw1 where " + gsUsql + " group by " + " \"JCLX_XL\" ";

                gsList = gs_hl.query(sql);

                for (int i = 0; i < gsList.size(); i++) {
                    JSONObject one = gsList.get(i);
//                    gsXls.put(one.getString("JCLX_XL"), one.getString("COUNT"));
                    gsXls.put(one.getString("JCLX_XL"), one.getString("COUNT"));
                }

            } else {
                sql = "select content from temp where id=1";
                String con = mysql.query_one(sql, "content");
                JSONArray ll = JSONArray.parseArray(con);
                if (ll.size() > 0) {
                    for (int i = 0; i < ll.size(); i++) {
                        JSONObject one = ll.getJSONObject(i);
//                    gsXls.put(one.getString("JCLX_XL"), one.getString("COUNT"));
                        gsXls.put(one.getString("JCLX_XL"), one.getString("COUNT"));
                    }
                }
            }
            String father_id = data.getString("father_id");

            sql = "select id,dict_name from dict where isdelete=1 and father_id='" + father_id + "'";
            JSONArray roomChildList = RIUtil.GetDictByFather(father_id);
            logger.warn(roomChildList.toString());
            JSONArray dets = new JSONArray();
            int i = 0;
            for (int a = 0; a < roomChildList.size(); a++) {
                JSONObject one = roomChildList.getJSONObject(a);
                String name = one.getString("dict_name");
                String id = one.getString("id");
                String ids = id.replace("160-", "");
                String count = "0";
                if (gsXls.containsKey(ids)) {
                    count = gsXls.get(ids);
                }
                one.put("count", count);
                one.put("title", name);
                one.put("id", id);

                JSONObject click = new JSONObject();
                click.put("type", "dialog");
                int ran = i % 4;
                if (ran == 0) {
                    click.put("permission", "chat_bar");
                } else if (ran == 1) {
                    click.put("permission", "chat_pie");
                } else if (ran == 2) {
                    click.put("permission", "chat_bar_tran");
                } else {
                    click.put("permission", "chat_pie_3d");
                }
                JSONObject cols = new JSONObject();

                cols.put("lx", ids);
                cols.put("point", ids);
                cols.put("unit", unit);
                JSONObject opts = GetOpts("/screen_unit", "get_point_unit_level", cols);
                click.put("remark", opts);
                click.put("label", RIUtil.dicts.get(id).getString("dict_name"));

                one.put("click", click);


                JSONObject dpclick = new JSONObject();
                dpclick.put("type", "dialog");
                cols = new JSONObject();
                cols.put("point", ids);
                cols.put("unit", unit);

                opts = GetOpts("/jqzt", "get_point_loc_table", cols);
                dpclick.put("remark", opts);
                dpclick.put("permission", "table_page");
                dpclick.put("id", String.valueOf(UUID.randomUUID()));
                one.put("dpclick", dpclick);

                if (name != null && name.length() > 0) {
                    dets.add(one);
                }
                i++;
            }
            back.put("data", dets);

            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            mysql.close();
            gs_hl.close();
        }
    }

    private JSONObject GetOpts(String url, String opt, JSONObject cols) {

        JSONObject opts = new JSONObject();
        opts.put("url", url);
        opts.put("opt", opt);
        opts.put("opt_user", "$opt_user$");


        opts.putAll(cols);
        return opts;
    }

    private JSONArray GetHeads(String headN, String hdKeys) {
        JSONArray rets = new JSONArray();
        String[] hdns = headN.split(",");
        String[] hdks = hdKeys.split(",");

        for (int i = 0; i < hdks.length; i++) {
            JSONObject det = new JSONObject();
            String key = hdks[i];
            String value = hdns[i];
            det.put("key", key);
            det.put("value", value);
            rets.add(det);

        }
        return rets;
    }


    private int ExportTables(JSONArray datas, String head, String keys, String name) {


        String FileName = name + "_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        String[] heads = head.split(",");
        String key[] = keys.split(",");
        for (int i = 0; i < key.length; i++) {
            header.add(key[i]);
            headername.put(key[i], heads[i]);
        }


        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
//            for (int i = 0; i < datas.size(); i++) {
//                JSONObject jsonObject = datas.getJSONObject(i);
//                JSONArray jsonArray = jsonObject.getJSONArray("real_all");
            exporthelper.write_data(datas);
//            }

            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql = "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath +
                        "','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");
                try {
                    if (exporthelper != null) {
                        try {
                            exporthelper.close();
                            exporthelper = null;
                        } catch (Exception ex) {

                        }

                    }
                } catch (Exception ex) {

                }

                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, TNOAConf.get("file",
                        "img_path") + filePath + FileName);
                logger.warn(obsFileName + "-->" + ret);
                logger.warn("-->" + id);
                return id;
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return 0;
        } finally {

            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }
        }
    }


    private JSONObject getCAnlmList(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        MysqlHelper mysql = null;
        try {
//            mysql = new MysqlHelper("mysql");
//            String lx = data.getString("lx");
//            String sql = "select name,count from sta_calm where lx='" + lx + "' order by id";
//            List<JSONObject> list = mysql.query(sql);
//            if (list.size() > 0) {
//                back.put("data", list);
//            } else {
//                back.put("data", new ArrayList<>());
//            }

            mysql = new MysqlHelper("calm");
            String lx = data.getString("lx");
            List<JSONObject> list = new ArrayList<>();
            String sql = "";
            if ("1".equals(lx)) {
                sql = "select mc ,rs  from calm_dpsj_llzc order by rs desc";
                mysql.query(sql).forEach(jsonObject -> {
                    JSONObject object = new JSONObject();
                    object.put("name", jsonObject.getString("mc"));
                    object.put("count", jsonObject.getString("rs"));
                    JSONObject click = new JSONObject();
                    click.put("type", "jump");
                    click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/iframe/index?target=calm");
                    object.put("click", click);
                    list.add(object);
                });
            } else if ("2".equals(lx)) {
                sql = "select fsyf , fssl from calm_dpsj_jyywhb limit 12 ";
                mysql.query(sql).forEach(jsonObject -> {
                    JSONObject object = new JSONObject();
                    object.put("name", jsonObject.getString("fsyf"));
                    object.put("count", jsonObject.getString("fssl"));
                    JSONObject click = new JSONObject();
                    click.put("type", "jump");
                    click.put("url", "http://hygk.qjjcgzpt.czx.js/qj/goto?lx=hb");
                    object.put("click", click);
                    list.add(object);
                });
            } else if ("3".equals(lx)) {
                sql = "select hdyf , hds from calm_dpsj_hd limit 12";
                mysql.query(sql).forEach(jsonObject -> {
                    JSONObject object = new JSONObject();
                    object.put("name", jsonObject.getString("hdyf"));
                    object.put("count", jsonObject.getString("hds"));

                    JSONObject click = new JSONObject();
                    click.put("type", "jump");
                    click.put("url", "http://hygk.qjjcgzpt.czx.js/qj/goto?lx=zyz_hd");
                    object.put("click", click);
                    list.add(object);
                });
            }
            logger.warn(sql);
            back.put("data", list);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            mysql.close();
        }
    }


}
