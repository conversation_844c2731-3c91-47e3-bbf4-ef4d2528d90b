package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.Utils.MakeWaters;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.Lib.TNOAConf;
import HL.TNOA.wechat.wechatMsgTemp;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.*;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;

@RestController
public class PatrolController {
    private static Logger logger = LoggerFactory.getLogger(PatrolController.class);
    // private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static String ip = "";

    @RequestMapping(method = {RequestMethod.POST}, path = {"/patrol_log"})
    @PassToken
    public static JSONObject get_patrol(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        String opt = "";
        String clientIP = request.getRemoteAddr();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if ("get_patrol_log".equals(opt)) {
                return getPatrol_Log(data);
            } else if ("create_patrol_log".equals(opt)) {
                logger.warn("patrol_log--->" + data.toString());
                return createPatrol_Log(data, clientIP);
            } else if ("trend_patrol_log".equals(opt)) {
                return getPatrolTrend(data);
            } else if ("static_patrol_log".equals(opt)) {
                return getPatrolLogStatic(data);
            } else if ("rank_patrol_log".equals(opt)) {
                return getPatrolRank(data);
            } else if ("get_patrol_log_month".equals(opt)) {
                return getStaticPatrolAll(data);
            } else if ("get_patrol_log_day".equals(opt)) {
                return getStaticPatrolDay(data);
            } else if ("get_patrol_info".equals(opt)) {
                return getPatrol_info(data);
            } else if ("create_patrol_info".equals(opt)) {
                return createPatrolInfo(data);
            } else if ("update_patrol_info".equals(opt)) {
                logger.warn("patrol_info--->" + data.toString());
                return updatePatrolInfo(data, clientIP);
            } else if ("delete_patrol_info".equals(opt)) {
                logger.warn("patrol_info--->" + data.toString());
                return deletePatrolInfo(data, clientIP);
            } else if (opt.equals("get_patrol_task")) {
                logger.warn("patrol_task--->" + data.toString());
                return PatrolTaskController.getPatrolTask(data);
            } else if (opt.equals("create_patrol_task")) {
                logger.warn("patrol_task--->" + data.toString());
                return PatrolTaskController.createPatrolTask(data, clientIP);
            } /*else if (opt.equals("update_patrol_task")) {
                return updatePatrolTask(data, clientIP);
            } */ else if (opt.equals("delete_patrol_task")) {
                logger.warn("patrol_task--->" + data.toString());
                return PatrolTaskController.deletePatrolTask(data, clientIP);
            } else if (opt.equals("update_patrol_task_status")) {
                logger.warn("patrol_task--->" + data.toString());
                return PatrolTaskController.updateTaskStatus(data, clientIP);
            } else if ("ding_unread_patrol_task".equals(opt)) {
                logger.warn("patrol_info--->" + data.toString());
                return dingUnread(data);
            } else {
                return ErrNo.set(459009);
            }
        } else {
            return ErrNo.set(459009);
        }
    }

    private static JSONObject getStaticPatrolAll(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String user_id = "";
        String s1 = " 1=1 ";
        String s2 = " status=0 ";
        String s3 = " 1=1 ";
        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");
            s1 = "user_id='" + user_id + "'";
            s2 = "(accepter like '%" + user_id + "%' or checked like '%" + user_id + "%')";
            s3 = "(accepter like '%" + user_id + "%' or checked like '%" + user_id + "%' or finished like '%" + user_id + "%')";

        }
        String month = new SimpleDateFormat("yyyy-MM").format(new Date());
        if (data.containsKey("month") && data.getString("month").length() > 0) {
            month = data.getString("month");
        }

        String unit = "";
        String units = " and 1=1";

        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            units = " and unit='" + unit + "'";
        }
        String start_time = month + "-01";
        String end_time = RIUtil.getLMonthEnd(start_time).substring(0, 10);
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            JSONArray datas = new JSONArray();
            while (!start_time.equals(end_time)) {
                String start = start_time + " 00:00:00";
                String end = start_time + " 23:59:59";
                JSONObject one = new JSONObject();
                one.put("date", start_time);
                //打卡
                String sql =
                        "select count(id) as count from patrol_log where (length(rela_id)=0 or rela_id=0) and " + s1 + " and time>='" + start + "' and time<='" + end + "' " + units;
                int count = mysql.query_count(sql);
                one.put("check_count", count);
                sql = "select count(id) as count from patrol_log where " + s1 + " and time>='" + start + "' and " +
                        "time <='" + end + "' and `check`=3";
                count = mysql.query_count(sql);
                one.put("gun_count", count);
                //任务统计
                sql = "select count(id) as count from patrol_task " + "where " + s3 + " and  " + "start_time>='" + start + "' and start_time<='" + end + "' and isdelete=1 " + units;
                count = mysql.query_count(sql);
                if (count > 0) {
                    sql = "select count(id) as count from patrol_task " + "where " + s2 + " and  " + "start_time" +
                            ">='" + start + "' and start_time<='" + end + "' and isdelete=1 " + units;
                    count = mysql.query_count(sql);
                    one.put("task_un_count", count);
                } else {
                    one.put("task_un_count", -1);
                }
                datas.add(one);
                start_time = RIUtil.GetNextDate(start_time, 1);


            }
            String start = start_time + " 00:00:00";
            String end = start_time + " 23:59:59";
            JSONObject one = new JSONObject();
            one.put("date", start_time);
            //打卡
            String sql = "select count(id) as count from patrol_log where (length(rela_id)=0 or rela_id=0) and " +
                    "user_id='" + user_id + "' and time>='" + start + "' and time<='" + end + "'" + units;
            int count = mysql.query_count(sql);
            one.put("check_count", count);
            sql = "select count(id) as count from patrol_log where " + s1 + " and time>='" + start + "' and " + "time"
                    + "<='" + end + "' and `check`=3";
            count = mysql.query_count(sql);
            one.put("gun_count", count);
            //任务统计
            sql = "select count(id) as count from patrol_task where (accepter like '%" + user_id + "%' or " +
                    "checked like '%" + user_id + "%') and start_time>='" + start + "' and start_time<='" + end + "' "
                    + "and isdelete=1 " + units;
            count = mysql.query_count(sql);
            one.put("task_un_count", count);

            datas.add(one);


            back.put("data", datas);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(459005);
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    private static JSONObject getStaticPatrolDay(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String user_id = "";
        String s1 = " 1=1 ";
        String s2 = " 1=1 ";
        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");
            s1 = "user_id='" + user_id + "'";
            s2 = "(accepter like '%" + user_id + "%' or checked like '%" + user_id + "%' or finished like '%" + user_id + "%')";
        }
        String date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        if (data.containsKey("date") && data.getString("date").length() > 0) {
            date = data.getString("date");
        }

        String unit = "";
        String units = " and 1=1";

        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            units = " and unit='" + unit + "'";
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            JSONArray datas = new JSONArray();

            String start = date + " 00:00:00";
            String end = date + " 23:59:59";
            JSONObject one = new JSONObject();

            //打卡
            String sql =
                    "select * from patrol_log where (length(rela_id)=0 or rela_id=0) and " + s1 + " and time>='" + start + "' and time<='" + end + "'" + units;
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                one.put("check", RelaInfo(list, mysql));
            } else {
                one.put("check", new ArrayList<>());
            }
            //任务统计
            sql = "select * from patrol_task " + "where " + s2 + " and  " + "start_time>='" + start + "' and " +
                    "start_time<='" + end + "' and isdelete=1 " + units;
            list = mysql.query(sql);
            if (list.size() > 0) {
                one.put("task", PatrolController.RelaInfo(list, mysql));
            } else {
                one.put("task", new ArrayList<>());
            }
            datas.add(one);

            back.put("data", datas);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(459005);
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }


    private static JSONObject deletePatrolInfo(JSONObject data, String clientIP) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(442008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(442008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update patrol_info set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除云哨信息", userlog.TYPE_OPERATE, ip);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(442007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

    private static JSONObject createPatrolInfo(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";

            String patrol_name = "";

            String latitude = "";
            String longitude = "";
            String type = "";
            String opt_user = "";
            if (data.containsKey("patrol_name") && data.getString("patrol_name").length() > 0) {
                patrol_name = data.getString("patrol_name");

            } else {
                return ErrNo.set(460002);
            }
            String unit = "";

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");

            }
            if (data.containsKey("latitude") && data.getString("latitude").length() > 0) {
                latitude = data.getString("latitude");

            }
            if (data.containsKey("longitude") && data.getString("longitude").length() > 0) {
                longitude = data.getString("longitude");

            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");

            } else {
                return ErrNo.set(460002);
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(460002);
            }
            String id = String.valueOf(UUID.randomUUID());
            String sqls =
                    "insert patrol_info (id,patrol_name,latitude,longitude,type," + "create_user,create_time," +
                            "isdelete,unit) " + "values('" + id + "','" + patrol_name + "','" + latitude + "','" + longitude + "'," + "'" + type + "'," + "'" + opt_user + "','" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "','0','" + unit + "')";
            mysql.update(sqls);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(460001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject updatePatrolInfo(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String patrol_name = "";
            String patrol_img = "";
            String latitude = "";
            String longitude = "";
            String type = "";
            String active = "";
            String active_user = "";
            String active_time = "";
            String active_lat = "";
            String active_lon = "";

            String opt_user = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            }
            if (data.containsKey("patrol_name") && data.getString("patrol_name").length() > 0) {
                patrol_name = data.getString("patrol_name");
                sql = sql + " patrol_name='" + patrol_name + "' , ";
            }
            if (data.containsKey("patrol_img") && data.getString("patrol_img").length() > 0) {
                patrol_img = data.getString("patrol_img");
                sql = sql + " patrol_img='" + patrol_img + "' , ";
            }
            if (data.containsKey("latitude") && data.getString("latitude").length() > 0) {
                latitude = data.getString("latitude");
                sql = sql + " latitude='" + latitude + "' , ";
            }
            if (data.containsKey("longitude") && data.getString("longitude").length() > 0) {
                longitude = data.getString("longitude");
                sql = sql + " longitude='" + longitude + "' , ";
            }
            String unit = "";

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " unit='" + unit + "' and ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type='" + type + "' , ";
            }
            if (data.containsKey("active") && data.getString("active").length() > 0) {
                active = data.getString("active");
                sql = sql + " active='" + active + "' , ";
            }
            if (data.containsKey("active_user") && data.getString("active_user").length() > 0) {
                active_user = data.getString("active_user");
                sql = sql + " active_user='" + active_user + "' , ";
            }
            if (data.containsKey("active_time") && data.getString("active_time").length() > 0) {
                active_time = data.getString("active_time");
                sql = sql + " active_time='" + active_time + "' , ";
            }
            if (data.containsKey("active_lat") && data.getString("active_lat").length() > 0) {
                active_lat = data.getString("active_lat");
                sql = sql + " active_lat='" + active_lat + "' , ";
            }
            if (data.containsKey("active_lon") && data.getString("active_lon").length() > 0) {
                active_lon = data.getString("active_lon");
                sql = sql + " active_lon='" + active_lon + "' , ";
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            if (active.equals("2")) {
                wechatMsgTemp.createDingMsg(id, patrol_name + "激活成功", opt_user, 0, active_user, mysql,
                        "act2_" + id.substring(0, 19));
            } else if (active.equals("0")) {
                wechatMsgTemp.createDingMsg(id, patrol_name + "激活审核未通过", opt_user, 2, active_user, mysql,
                        "act0_" + id.substring(0, 19));
            }

            String sqls = "update patrol_info set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);


            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新巡查场所", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(459003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject getPatrolRank(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String month = new SimpleDateFormat("yyyy-MM").format(new Date());
        if (data.containsKey("month") && data.getString("month").length() > 0) {
            month = data.getString("month");
        }
        String unit = "";
        String sqls = " and 1=1";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            sqls = sqls + " unit='" + unit + "' and ";
        }
        try {
            mysql = InfoModelPool.getModel();
            String sql =
                    "SELECT DATE_FORMAT( time, '%Y-%m' ) AS date,count( id ) AS total,user_id FROM patrol_log " +
                            "WHERE time like '%" + month + "%' " + sqls + " GROUP BY DATE_FORMAT( time, '%Y-%m' )," + "user_id order by total desc";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                back.put("data", DescInfo(list, mysql));
            } else {
                back.put("data", list);
            }
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(459005);
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    private static Object DescInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        int rank = 0;
        int last = 0;
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            int total = one.getInteger("total");
            String user_id = one.getString("user_id");
            one.put("user_name", RIUtil.IdToName(user_id, mysql, " name", "user"));
            one.put("user", RIUtil.users.get(user_id));
            if (total != last) {
                rank++;
            }
            one.put("rank", rank);
            last = total;
            back.add(one);
        }
        return back;
    }

    private static JSONObject getPatrolLogStatic(JSONObject data) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        String today1 = sd.format(new Date()) + " 00:00:00";
        String today_e = sd.format(new Date()) + " 23:59:59";
        String month1 = sdf.format(BriefController.getmindate());
        String month_e = sdf.format(BriefController.getmaxdate());
        String week1 = BriefController.getWeekStart();
        String week_end = BriefController.getWeekEnd();

        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String unit = " and 1=1";

        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            String u = data.getString("unit");
            unit = " unit='" + u + "' and ";
        }
        try {
            mysql = InfoModelPool.getModel();
            JSONObject datas = new JSONObject();
            String sql = "select count(id) as count from patrol_log where isdelete=1 " + unit;
            int count = mysql.query_count(sql);
            datas.put("total", count);
            //month
            sql = "select count(id) as count from patrol_log " + "where isdelete=1  and time >='" + month1 + "' and " + "time<='" + month_e + "'" + unit;
            datas.put("month", mysql.query_count(sql));
            //week
            sql = "select count(id) as count  from patrol_log " + "where isdelete=1  and time >='" + week1 + "' and " + "time<='" + week_end + "'" + unit;
            datas.put("week", mysql.query_count(sql));
            //today
            sql = "select count(id) as count from patrol_log " + "where isdelete=1  and time >='" + today1 + "' and " + "time<='" + today_e + "'" + unit;
            datas.put("today", mysql.query_count(sql));

            back.put("data", datas);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(459005);
        } finally {
            InfoModelPool.putModel(mysql);
        }


    }

    private static JSONObject getPatrolTrend(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";

            String user_id = "";
            String patrol_id = "";

            String start_time = "";
            String end_time = "";

            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
                sql = sql + " user_id='" + user_id + "' and ";
            }
            String unit = "";

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " unit='" + unit + "' and ";
            }
            if (data.containsKey("patrol_id") && data.getString("patrol_id").length() > 0) {
                patrol_id = data.getString("patrol_id");
                sql = sql + " patrol_id='" + patrol_id + "' and ";
            }

            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time");
                sql = sql + " time>='" + start_time + " 00:00:00' and ";
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time");
                sql = sql + " time<='" + end_time + " 23:59:59' and ";
            }

            String sqls = "SELECT DATE_FORMAT( time, '%Y-%m-%d' ) AS date,count( id ) AS total FROM patrol_log WHERE "
                    + sql + " 1=1 GROUP BY DATE_FORMAT( time, '%Y-%m-%d' )";
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);

            back.put("data", list);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(459005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject getPatrol_info(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 99;
            int page = 1;
            String id = "";
            String active = "";
            String type = "";
            String patrol_name = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }

            if (data.containsKey("patrol_name") && data.getString("patrol_name").length() > 0) {
                patrol_name = data.getString("patrol_name");
                sql = sql + " patrol_name like '%" + patrol_name + "%' and ";
            }
            if (data.containsKey("active") && data.getString("active").length() > 0) {
                active = data.getString("active");
                sql = sql + " active='" + active + "' and ";
            }

            String unit = "";

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " unit='" + unit + "' and ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type='" + type + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select * from patrol_info where 1=1 and " + sql + " isdelete=1  " + "limit " + limit + " " +
                            "offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo_INfo(list, mysql));
                sqls = "select id from patrol_info where 1=1 and " + sql + " isdelete=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(459012);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static List<JSONObject> RelaInfo_INfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String create_user = one.getString("create_user");
            one.put("create_user", RIUtil.users.get(create_user));
            String active_user = one.getString("active_user");
            one.put("active_user", RIUtil.users.get(active_user));
            back.add(one);
        }
        return back;
    }

    private static JSONObject createPatrol_Log(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String user_id = "";
            String patrol_id = "";
            String patrol_name = "";
            String remark = "";
            String img = "";
            String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String longitude = "";
            String latitude = "";
            String transport = "0";
            String rela_id = "0";
            String check = "0";

            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
            } else {
                return ErrNo.set(459002);
            }
            if (data.containsKey("patrol_id") && data.getString("patrol_id").length() > 0) {
                patrol_id = data.getString("patrol_id");

                // patrol_name = RIUtil.IdToName(patrol_id, mysql, "patrol_name", "patrol_info");

            }
            if (data.containsKey("patrol_name") && data.getString("patrol_name").length() > 0) {
                patrol_name = data.getString("patrol_name");

                // patrol_name = RIUtil.IdToName(patrol_id, mysql, "patrol_name", "patrol_info");

            } else {

            }

            String unit = "";
            String unit_name = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");


            } else {
                logger.warn(user_id);
                logger.warn(RIUtil.users.get(user_id).toString());
                unit = RIUtil.users.get(user_id).getString("unit");

            }
            unit_name = RIUtil.dicts.get(unit).getString("dict_name").replace("派出所", "");
            if (data.containsKey("transport") && data.getString("transport").length() > 0) {
                transport = data.getString("transport");
            }
            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
            }
            if (data.containsKey("check") && data.getString("check").length() > 0) {
                check = data.getString("check");
            }
            if (data.containsKey("img") && data.getString("img").length() > 0) {
                img = data.getString("img");
            }
            if (data.containsKey("longitude") && data.getString("longitude").length() > 0) {
                longitude = data.getString("longitude");
            } else {
                if (patrol_name.length() == 0) {
                    return ErrNo.set(469002);
                }
            }
            if (data.containsKey("latitude") && data.getString("latitude").length() > 0) {
                latitude = data.getString("latitude");
            } else {
                if (patrol_name.length() == 0) {
                    return ErrNo.set(469002);
                }
            }

            if (data.containsKey("rela_id") && data.getString("rela_id").length() > 0) {
                rela_id = data.getString("rela_id");
            } else {
                if (patrol_name.length() > 0) {
                    String t = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                    JSONObject ds = new JSONObject();
                    ds.put("opt_user", user_id);
                    ds.put("accepter", user_id);

                    ds.put("start_time", t);
                    ds.put("end_time", t);
                    ds.put("near_time", t);
                    ds.put("isWechat", 0);
                    ds.put("address", patrol_name);

                    PatrolTaskController.createPatrolTask(ds, remoteAddr);
                    String s = "select id from patrol_task " + "where start_time='" + t + "' and end_time='" + t +
                            "'" + " and address='" + patrol_name + "' and accepter='" + user_id + "' and unit='" + unit + "'";
                    rela_id = mysql.query_one(s, "id");

                    if (rela_id.length() > 0) {
                        s = "update patrol_task set status=1,accepter='',finished='" + user_id + "' where id='" + rela_id + "'";
                        mysql.update(s);

                        String sql =
                                "INSERT INTO task_log (task_id, task_type, time, opt_user,cancel_type,user_id) " +
                                        "VALUES ( '" + rela_id + "', '1', '" + new SimpleDateFormat("yyyy-MM-dd " +
                                        "HH:mm:ss").format(new Date()) + "'," + " '" + user_id + "','99','" + user_id + "');";
                        mysql.update(sql);


                    } else {
                        rela_id = "0";
                    }
                }
            }
           /* if (patrol_name.length() == 0) {

                patrol_name = "";
                if (patrol_name.length() == 0) {
                    return ErrNo.set(459011);
                }

            }*/
            String sqls = "insert patrol_log (user_id,patrol_id,patrol_name,time,remark,img," + "longitude,latitude," +
                    "transport,rela_id,`check`,unit)" + "values('" + user_id + "','" + patrol_id + "','" + patrol_name + "','" + time + "','" + remark + "'," + "'" + img + "'," + "'" + longitude + "','" + latitude + "','" + transport + "','" + rela_id + "','" + check + "','" + unit + "')";
            mysql.update(sqls);

            //图片加水印
            if (img.length() > 0) {
                CreatWaterImg(img, mysql, new SimpleDateFormat("yyyy-MM-dd").format(new Date()),
                        new SimpleDateFormat("HH:mm:ss").format(new Date()), patrol_name, unit_name);
            }

            UserLog userlog = new UserLog();
            userlog.log(mysql, user_id, "创建联防日志", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(459001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    private static void CreatWaterImg(String img, InfoModelHelper mysql, String date, String time, String local,
                                      String unit_name) {
        try {
            String imgs[] = img.split(",");
            for (int i = 0; i < imgs.length; i++) {
                String id = imgs[i];
                String sql = "select file_path,file_name from upload where id='" + id + "'";
                List<JSONObject> list = mysql.query(sql);
                if (list.size() > 0) {
                    JSONObject one = list.get(0);
                    String file_path = TNOAConf.get("file", "img_path") + one.getString("file_path");
                    String file_name = one.getString("file_name");
                    String sourcePic = file_path + file_name;
                    File file = new File(sourcePic);

                    String size = GetSize(file);
                    if (!size.equals("0")) {
                        String[] ss = size.split(",");
                        int height = Integer.parseInt(ss[1]);
                        int ww = Integer.parseInt(ss[2]);
                        int wh = Integer.parseInt(ss[3]);

                        String water_file = MakeWaters.makeWaterimg(ww, wh, date, time, local, file_path, unit_name);
                        MakeWaters.markImageByIcon(file_path + water_file, sourcePic, sourcePic, 0, height - wh + 5);
                    }

                    file_path = TNOAConf.get("file", "img_path") + one.getString("file_path") + "big/";
                    file_name = one.getString("file_name");
                    sourcePic = file_path + file_name;
                    logger.warn(sourcePic);
                    file = new File(sourcePic);

                    size = GetSize(file);
                    if (!size.equals("0")) {
                        String[] ss = size.split(",");
                        int height = Integer.parseInt(ss[1]);
                        int ww = Integer.parseInt(ss[2]);
                        int wh = Integer.parseInt(ss[3]);

                        String water_file = MakeWaters.makeWaterimg(ww, wh, date, time, local, file_path, unit_name);
                        MakeWaters.markImageByIcon(file_path + water_file, sourcePic, sourcePic, 0, height - wh + 5);
                    }
                }

            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        }
    }

    private static String GetSize(File file) {
        try {
            Image src = ImageIO.read(file);
            int weidth = src.getWidth(null);
            int height = src.getHeight(null);
            System.out.println(weidth + "-" + height);
            int ww = 0;// 水印宽度
            int wh = 0;// 水印高度
            int bw = 0;
            int bh = 0;
            float p = 0;
            if (weidth > height) {
                ww = weidth / 2;
                wh = height / 4;

            } else {
                ww = height / 2;
                wh = weidth / 4;

            }
            System.out.println(ww + "-" + wh);
            bw = Math.abs(ww - 422);
            bh = Math.abs(wh - 272);
            System.out.println(bw + " " + bh);
            if (bw > bh) {
                p = (float) wh / (float) 272;
                System.out.println(wh + "/272=" + p);
            } else {
                p = (float) ww / (float) 422;
                System.out.println(ww + "/422=" + p);
            }
            System.out.println(p);
            ww = (int) (422 * p);
            wh = (int) (272 * p);
            System.out.println(ww + "-" + wh);
            return weidth + "," + height + "," + ww + "," + wh;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return "0";
        }
    }

    private static void checkTaskFinish(JSONObject tone, InfoModelHelper mysql, String user_id) throws Exception {
        String address = tone.getString("address");
        List<String> adds = RIUtil.HashToList(RIUtil.StringToList(address));
        String id = tone.getString("id");
        int mark = adds.size();
        for (int i = 0; i < adds.size(); i++) {
            String sql =
                    "select count(id) as count from patrol_log " + "where rela_id='" + id + "' and patrol_id='" + adds.get(i) + "' and isdelete=1 " + "and user_id='" + user_id + "'";
            int count = mysql.query_count(sql);
            if (count > 0) {
                mark--;
            }
        }

        String accepter = "";
        String checked = "";
        String finished = "";
        if (tone.containsKey("accepter")) {
            accepter = tone.getString("accepter");
        }
        if (tone.containsKey("checked")) {
            checked = tone.getString("checked");
        }
        if (tone.containsKey("finished")) {
            finished = tone.getString("finished");
        }

        HashMap<String, String> acc = RIUtil.StringToList(accepter);
        HashMap<String, String> check = RIUtil.StringToList(checked);
        HashMap<String, String> fin = RIUtil.StringToList(finished);
        int status = 0;
        if (acc.containsKey(user_id)) {
            acc.remove(user_id);
            check.put(user_id, "");

            String sql = "INSERT INTO task_log (task_id, task_type, time, opt_user,cancel_type,user_id) " + "VALUES " +
                    "(" + " '" + id + "', '1', '" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) +
                    "'," + " '" + user_id + "','99','" + user_id + "');";
            mysql.update(sql);
        }
        if (mark == 0) {
            if (check.containsKey(user_id)) {
                check.remove(user_id);
                fin.put(user_id, "");
            }
            if (acc.size() == 0 && check.size() == 0 && fin.size() > 0) {
                status = 1;
            }
        }
        String sqls =
                "update patrol_task " + "set `status`='" + status + "'," + "accepter='" + RIUtil.HashToList(acc) +
                        "'," + "checked='" + RIUtil.HashToList(check) + "', finished='" + RIUtil.HashToList(fin) +
                        "'" + " where id='" + id + "'";
        mysql.update(sqls);


    }

    //******GET*******
    private static JSONObject getPatrol_Log(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String user_id = "";
            String patrol_id = "";

            String start_time = "";
            String end_time = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
                sql = sql + " user_id='" + user_id + "' and ";
            }
            if (data.containsKey("patrol_id") && data.getString("patrol_id").length() > 0) {
                patrol_id = data.getString("patrol_id");
                sql = sql + " patrol_id='" + patrol_id + "' and ";
            }
            String unit = "";

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " unit='" + unit + "' and ";
            }

            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time").replace("|", " ");
                sql = sql + " time>='" + start_time + "' and ";
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time").replace("|", " ");
                sql = sql + " time<='" + end_time + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select * from patrol_log where 1=1 and " + sql + " isdelete=1 order by time desc limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select id from patrol_log where 1=1 and " + sql + " isdelete=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(459005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private static Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String user_id = one.getString("user_id");
            one.put("user", RIUtil.users.get(user_id));
            if (one.containsKey("unit")) {
                String unit = one.getString("unit");
                one.put("unit_name", RIUtil.RealDictNames(RIUtil.StringToList(unit)));
            } else {
                one.put("unit_name", "");
            }
            if (one.containsKey("accepter")) {
                String accepter = one.getString("accepter");
                one.put("accepter", RIUtil.UseridToNames(RIUtil.StringToList(accepter)));
            }
            if (one.containsKey("finished")) {
                String finished = one.getString("finished");
                one.put("finished", RIUtil.UseridToNames(RIUtil.StringToList(finished)));
            }
            back.add(one);
        }

        return back;
    }

    private static JSONObject dingUnread(JSONObject data) {
        String notice_id = "";
        String accepters = "";
        String opt_user = "";
        int source = 1;
        if (data.containsKey("notice_id") && data.getString("notice_id").length() > 0) {
            notice_id = data.getString("notice_id");
        } else {
            return ErrNo.set(432024);
        }
        if (data.containsKey("accepters") && data.getString("accepters").length() > 0) {
            accepters = data.getString("accepters");
        } else {
            return ErrNo.set(432024);
        }

        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(432024);
        }

        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            JSONObject back = ErrNo.set(0);
            String comment = "请尽快签收/完成";
            wechatMsgTemp.createDingMsg(notice_id, comment, "1", source, accepters, mysql, notice_id);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(432025);

        } finally {
            InfoModelPool.putModel(mysql);
        }

    }


}
