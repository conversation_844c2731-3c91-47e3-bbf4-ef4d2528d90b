package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.MysqlHelper;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

@RestController
public class ScreenPlacesController {

    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");F

    @RequestMapping(method = {RequestMethod.POST}, path = {"/screen_place"})
    @PassToken
    public JSONObject getScreenPlace(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        String token = request.getHeader("token");
        try {

            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("get_house_danger")) {

                    return getHouseDanger(data);
                } else if (opt.equals("get_house_danger_deal_lx")) {

                    return getHouseDangerDealLx(data, token);
                } else if (opt.equals("get_house_danger_find_lx")) {
                    return getHouseDangerFindLx(data, token);
                } else if (opt.equals("get_czf_danger_table")) {
                    return getCzfFindTable(data, token);
                } else if (opt.equals("get_czw_gk")) {
                    return getCzfGk(data, token);
                } else if (opt.equals("get_czwgk_table")) {
                    return GetCZWGKTable(data, token);
                } else if (opt.equals("get_risk")) {
                    return getRisk(data);
                } else {
                    return ErrNo.set(465009);
                }
            } else {
                return ErrNo.set(465009);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private JSONObject GetCZWGKTable(JSONObject data, String token) {

        JSONObject back = ErrNo.set(0);
        try {

            String hdns = "序号,地址名称,登记时间,操作时间,所属单位,房屋属性";
            String keys = "num,dzxxDzms,djrq,czsj,ssdw,fwsx";
            JSONArray heads = GetHeads(hdns, keys);
            JSONArray bodys = new JSONArray();
            int count = 0;

            String t = String.valueOf(System.currentTimeMillis());
            String end = data.getString("end_time");
            String unit = data.getString("unit");
            String orgType = data.getString("orgType");
            String ywlx = data.getString("ywlx");
            int page = data.getInteger("page");
            int limit = data.getInteger("limit");
            String url =
                    "http://50.56.94.47/czqjpt-api/fwxx/syfwgkFw?_t=" + t + "&end=" + end + "&orgType=" + orgType +
                            "&orgCode=" + unit + "&ywlx=" + ywlx + "&pageNo=" + page + "&pageSize=" + limit;
            logger.warn(url);
            JSONObject rets = GetOkHttpGet(url, token);
            if (rets.containsKey("code") && rets.getInteger("code") == 200) {
                JSONObject res = rets.getJSONObject("result");
                count = res.getInteger("total");
                JSONArray recods = res.getJSONArray("records");

                for (int i = 0; i < recods.size(); i++) {
                    JSONObject one = recods.getJSONObject(i);
                    JSONObject det = new JSONObject();

                    String num = String.valueOf(i + 1);
                    JSONObject val = new JSONObject();
                    val.put("value", num);
                    det.put("num", val);

                    String dzxxDzms = one.getString("dzxxDzms");
                    String dzid = one.getString("dzxxId");
                    val = new JSONObject();
                    val.put("value", dzxxDzms);
                    JSONObject click = new JSONObject();
                    click.put("type", "jump_house");
                    click.put("url", dzid);
                    val.put("click", click);
                    det.put("dzxxDzms", val);

                    String djrq = one.getString("djrq");
                    val = new JSONObject();
                    val.put("value", djrq);
                    det.put("djrq", val);

                    String czsj = one.getString("czsj");
                    val = new JSONObject();
                    val.put("value", czsj);
                    det.put("czsj", val);

                    String ssdw = one.getString("sszrqmc");
                    val = new JSONObject();
                    val.put("value", ssdw);
                    det.put("ssdw", val);

                    String jzcs = one.getString("jzcs_dictText");
                    val = new JSONObject();
                    val.put("value", jzcs);
                    det.put("fwsx", val);
                    bodys.add(det);

                }

                JSONObject datas = new JSONObject();
                datas.put("head", heads);
                datas.put("body", bodys);
                datas.put("count", count);

                back.put("data", datas);
                return back;

            } else {
                return ErrNo.set(null, 2, rets.toString());
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));


        }


    }
    //获取房屋类型个体表
//    private JSONObject getCzfGKS(JSONObject data ,String token){
//        JSONObject back = ErrNo.set(0);
//        try {
//            String end_time = data.getString("end_time");
//            long end = RIUtil.dateToStamp(end_time);
//            String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
//            long td = RIUtil.dateToStamp(today);
//            String yest = today;
//            if (end >= td) {
//                yest = RIUtil.GetNextDate(today, -1);
//            } else {
//                yest = end_time;
//            }
//            String point = data.getString("point");
//            String unit = data.getString("unit");
//            int type = RIUtil.dicts.get(unit).getInteger("type");
//            int orgType = 0;
//            if (type == 21 || type == 22 || type == 27) {
//                orgType = 2;
//                unit = unit.substring(0, 4) + "00000000";
//
//            } else if (type == 23 || type == 24 || type == 28) {
//                unit = unit.substring(0, 6) + "000000";
//                orgType = 3;
//            } else if (type == 25) {
//                orgType = 4;
//                unit = unit.substring(0, 8) + "0000";
//            } else {
//                unit = unit;
//                orgType = 4;
//            }
//            String t = String.valueOf(System.currentTimeMillis() / 1000);
//            String lx = "一般出租屋,群租房,合租房";
//            String lxs[] = lx.split(",");
//            String col = "ybczwtotal,qzftotal,hzftotal";
//            String cols[] = col.split(",");
//            String ywlx = "ybczwTotal,qzfTotal,hzfTotal";
//            String ywlxs[] = ywlx.split(",");
//            String url =
//                    "http://50.56.94.47/czqjpt-api/report/syfw?_t=" + t + "&end=" + yest + "&orgType=" +
//                    orgType + "&column=createTime&order=desc&pageNo=1&pageSize=99&parentOrgCode=" + unit;
//
//            logger.warn(url);
//            JSONObject rets = GetOkHttpGet(url, token);
//            JSONArray datas = new JSONArray();
//            if ("1".equals(point)){//一般出租屋
//                if (rets.containsKey("code") && rets.getInteger("code") == 200) {
//                    JSONArray r = rets.getJSONArray("result");
//
//                    for (int a = 0; a < r.size(); a++) {
//                        JSONObject dets = new JSONObject();
//                        JSONObject aone = r.getJSONObject(a);
//                        dets.put("name", aone.getString("jgmc"));
//                        dets.put("id",String.valueOf(UUID.randomUUID()));
//                        dets.put("count",aone.getString("ybcuwtotal"));
//                    }
//
//                }
//            }
//        } catch (Exception e) {
//            logger.error(Lib.getTrace(e));
//            return ErrNo.set(null, 2, Lib.getTrace(e));
//        }
//        return back;
//    }

    private JSONObject getCzfGk(JSONObject data, String token) {
        JSONObject back = ErrNo.set(0);
        try {

            String end_time = data.getString("end_time");
            long end = RIUtil.dateToStamp(end_time);
            String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            long td = RIUtil.dateToStamp(today);
            String yest = today;
            if (end >= td) {
                yest = RIUtil.GetNextDate(today, -1);
            } else {
                yest = end_time;
            }

            String unit = data.getString("unit");
            int type = RIUtil.dicts.get(unit).getInteger("type");
            int orgType = 0;
            if (type == 21 || type == 22 || type == 27) {
                orgType = 2;
                unit = unit.substring(0, 4) + "00000000";

            } else if (type == 23 || type == 24 || type == 28) {
                unit = unit.substring(0, 6) + "000000";
                orgType = 3;
            } else if (type == 25) {
                orgType = 4;
                unit = unit.substring(0, 8) + "0000";
            } else {
                unit = unit;
                orgType = 4;
            }
            String t = String.valueOf(System.currentTimeMillis() / 1000);

            String lx = "一般出租屋,群租房,合租房";
            String lxs[] = lx.split(",");
            String col = "ybczwtotal,qzftotal,hzftotal";
            String cols[] = col.split(",");
            String ywlx = "ybczwTotal,qzfTotal,hzfTotal";
            String ywlxs[] = ywlx.split(",");
            ;


            String url =
                    "http://50.56.94.47/czqjpt-api/report/syfw?_t=" + t + "&end=" + yest + "&orgType=" + orgType +
                            "&column=createTime&order=desc&pageNo=1&pageSize=99&parentOrgCode=" + unit;

            logger.warn(url);
            JSONObject rets = GetOkHttpGet(url, token);
            JSONArray datas = new JSONArray();
            if (rets.containsKey("code") && rets.getInteger("code") == 200) {
                JSONArray r = rets.getJSONArray("result");

                for (int i = 0; i < lxs.length; i++) {
                    JSONObject det = new JSONObject();
                    det.put("id", cols[i]);
                    det.put("name", lxs[i]);
                    JSONArray dets = new JSONArray();

                    for (int a = 0; a < r.size(); a++) {
                        JSONObject aone = r.getJSONObject(a);
                        String code = aone.getString("jgbm");
                        String name = aone.getString("jgmc");
                        if (!name.equals("合计")) {
                            String count = aone.getString(cols[i]);

                            JSONObject d = new JSONObject();
                            d.put("name", name);
                            d.put("code", code);
                            d.put("count", count);
                            JSONObject click = new JSONObject();
                            click.put("type", "dialog");
                            JSONObject cls = new JSONObject();

                            cls.put("unit", code);
                            cls.put("end_time", yest);
                            cls.put("orgType", orgType);
                            cls.put("ywlx", ywlxs[i]);
                            JSONObject opts = GetOpts("/screen_place", "get_czwgk_table", cls);
                            click.put("remark", opts);
                            click.put("permission", "table_page");
                            click.put("id", String.valueOf(UUID.randomUUID()));
                            click.put("label", name + "_" + lxs[i]);

                            d.put("click", click);

                            JSONObject dpclick = new JSONObject();
                            dpclick.put("type", "next");
                            dpclick.put("url", "/screen_place");

                            cls = new JSONObject();
                            cls.put("unit", code);
                            cls.put("code", code);
                            cls.put("name", name);
                            cls.put("end_time", end_time);
                            opts = GetOpts("/screen_place", "get_czw_gk", cls);
                            dpclick.put("opt", opts);
                            dpclick.put("id", String.valueOf(UUID.randomUUID()));
                            d.put("dpclick", dpclick);


                            dets.add(d);
                        }
                    }


                    det.put("det", dets);
                    datas.add(det);

                }
                back.put("data", datas);
                return back;


            } else {
                return ErrNo.set(null, 2, rets.toString());
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));


        }


    }


    private JSONObject getCzfFindTable(JSONObject data, String token) {
        JSONObject back = ErrNo.set(0);
        try {
            String start_time = "";
            String stsql = "";
            try {
                start_time = data.getString("start_time");
                if (start_time != null && start_time.length() > 0) {
                    stsql = "&begin=" + start_time;
                }
            } catch (Exception ex) {

            }
            String u = data.getString("u");
            String end_time = data.getString("end_time");
            String lx = data.getString("lx");
            String lxs = data.getString("lx");
            if (lx.endsWith("wzg")) {
                lx = lx.replace("wzg", "zs");
            } else {
                lx = lx.replace("hy", "").replace("yhyh", "yh");
            }

            int page = data.getInteger("page");
            int limit = data.getInteger("limit");
            String unit = data.getString("unit");
            int orgType = 0;
            int type = RIUtil.dicts.get(unit).getInteger("type");
            if (type == 21 || type == 22 || type == 27) {
                unit = unit.substring(0, 4) + "00000000";
                orgType = 1;
            } else if (type == 23 || type == 24 || type == 28) {
                unit = unit.substring(0, 6) + "000000";
                orgType = 2;
            } else if (type == 25) {
                unit = unit.substring(0, 8) + "0000";
                orgType = 3;
            } else {
                unit = unit;
                orgType = 3;
            }
            String t = String.valueOf(System.currentTimeMillis() / 1000);


            String url = "";

            url = "http://50.56.94.47/czqjpt-api/fwxx/page/report/" + u + "?_t=" + t + "&end=" + end_time + "&orgType" +
                    "=" + orgType + "&ywlx=" + lx + "&pageNo=" + page + "&pageSize=" + limit + stsql + "&orgCode=" + unit;
            logger.warn(url);
            JSONObject rets = GetOkHttpGet(url, token);
            String headN = "序号,地址名称,隐患大类,隐患小类,整改状态,所属单位";
            String keys = "num,dzxxDzmx,yhdl_dictText,yhlb_dictText,zzzt_dictText,ssdw";
            JSONArray heads = GetHeads(headN, keys);

            JSONArray bodys = new JSONArray();
            int count = 0;
            if (rets.containsKey("code") && rets.getInteger("code") == 200) {
                JSONObject r = rets.getJSONObject("result");
                count = r.getInteger("total");
                JSONArray recs = r.getJSONArray("records");
                for (int i = 0; i < recs.size(); i++) {
                    JSONObject one = recs.getJSONObject(i);
                    JSONObject det = new JSONObject();
                    String num = String.valueOf(i + 1);
                    JSONObject val = new JSONObject();
                    val.put("value", num);
                    det.put("num", val);

                    String dzxxDzmx = one.getString("dzxxDzms");
                    val = new JSONObject();
                    val.put("value", dzxxDzmx);
                    JSONObject click = new JSONObject();
                    click.put("type", "jump_house");
                    click.put("url", one.getString("dzxxId"));
                    val.put("click", click);
                    det.put("dzxxDzmx", val);

                    String yhdl_dictText = one.getString("yhdl_dictText");
                    val = new JSONObject();
                    val.put("value", yhdl_dictText);
                    det.put("yhdl_dictText", val);

                    String yhlb_dictText = one.getString("yhlb_dictText");
                    val = new JSONObject();
                    val.put("value", yhlb_dictText);
                    det.put("yhlb_dictText", val);

                    String zzzt_dictText = one.getString("zzzt_dictText");
                    val = new JSONObject();
                    val.put("value", zzzt_dictText);
                    det.put("zzzt_dictText", val);

                    String ssdw = one.getString("sszrqmc");
                    val = new JSONObject();
                    val.put("value", ssdw);
                    det.put("ssdw", val);

                    if (i == 0) {
                        System.out.println(one);
                        System.out.println(det);
                    }
                    if (lxs.endsWith("wzg")) {
                        if (zzzt_dictText.equals("未整改")) {
                            bodys.add(det);
                        }
                    } else {
                        bodys.add(det);
                    }

                }

                JSONObject datas = new JSONObject();
                datas.put("head", heads);
                datas.put("body", bodys);
                datas.put("count", count);
                back.put("data", datas);

                return back;
            } else {
                return ErrNo.set(null, 2, rets.toString());
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }

    }

    private JSONObject getHouseDangerFindLx(JSONObject data, String token) {

        JSONObject back = ErrNo.set(0);
        try {
            long t = System.currentTimeMillis() / 1000;
            String end_time = data.getString("end_time");
            String start_time = data.getString("start_time");
            String unit = data.getString("unit");
            String lx = data.getString("lx");

            int type = RIUtil.dicts.get(unit).getInteger("type");
            int orgType = 0;
            if (type == 21 || type == 22 || type == 27) {
                orgType = 2;
                unit = unit.substring(0, 4) + "00000000";

            } else if (type == 23 || type == 24 || type == 28) {
                unit = unit.substring(0, 6) + "000000";
                orgType = 3;
            } else if (type == 25) {
                orgType = 4;
                unit = unit;
            } else {
                unit = unit;
                orgType = 4;
            }

            MysqlHelper my143 = null;
            HashMap<String, Integer> ds = new HashMap<>();
            try {
                my143 = new MysqlHelper("mysql_zxqc");
                String sql =
                        "select code,sum(count) as count from sta_zf_mid where type='205-02002115' and time>='" + start_time + "' " + "and time<='" + end_time + "' group by code";
                List<JSONObject> list = my143.query(sql);
                for (int i = 0; i < list.size(); i++) {
                    JSONObject oen = list.get(i);
                    String code = oen.getString("code");
                    int count = oen.getInteger("count");
                    ds.put(code, count);
                }
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            } finally {
                my143.close();
            }


            String url = "http://50.56.94.47/czqjpt-api/report/ldrkczfw?_t=" + t + "&end" + "=" + end_time +
                    "&orgType=" + orgType + "&column=createTime&order=desc&pageNo=1" + "&pageSize=99";
            logger.warn(url);

            OkHttpClient client = new OkHttpClient().newBuilder().build();
            Request request =
                    new Request.Builder().url(url).method("GET", null).addHeader("X-Access-Token", token).build();
            Response response = client.newCall(request).execute();
            String res = response.body().string();
            JSONObject resj = JSONObject.parseObject(res);
            if (resj.containsKey("code") && resj.getInteger("code") == 200) {
                JSONArray rs = resj.getJSONArray("result");
                JSONArray ress = new JSONArray();
                String col = "czw" + lx + "xzs";
                logger.warn(col);

                for (int a = 0; a < rs.size(); a++) {
                    JSONObject aone = rs.getJSONObject(a);
                    JSONObject d = new JSONObject();
                    String code = aone.getString("jgbm");
                    String name = aone.getString("jgmc");
                    if (!name.equals("合计")) {

                        String count = aone.getString(col);
                        if (count == null || count.length() == 0) {
                            count = "0";
                        }
                        d.put("code", code);

                        d.put("name", name);


                        d.put("yhs", count);
                        d.put("jcs", ds.get(code));
                        double rhs = 0;
                        try {
                            rhs = (double) ds.get(code);
                        } catch (Exception ex) {

                        }
                        //  logger.warn(count + "/" + rhs);
                        double p = 0;
                        if (rhs != 0) {
                            p = Double.parseDouble(count) / rhs;
                        }
//                        if("0".equals(count)&&rhs==0){
//                            p=100;
//                        }

                        //  logger.warn(count + "/" + rhs + "=" + p);
                        d.put("fxl", String.format("%.5f", p));


                        d.put("id", code);
                        ress.add(d);

                    }

                }

                String cols = "检查数,隐患数";
                String[] cs = cols.split(",");
                String jumps = "czf$lx$yhxz,czf$lx$yhxz,czf$lx$yhxz".replace("$lx$", lx.toLowerCase());
                logger.warn(jumps);
                String[] jps = jumps.split(",");
                String xxs = "bar,bar,line";
                String signs = ",,%";
                String[] sign = signs.split(",");
                String[] xs = xxs.split(",");
                String keys = "jcs,yhs,fxl";
                String ks[] = keys.split(",");


                JSONArray datas = new JSONArray();
                for (int i = 0; i < cs.length; i++) {
                    JSONObject det = new JSONObject();
                    det.put("id", (i + 1));
                    det.put("name", cs[i]);
                    det.put("xx", xs[i]);
                    det.put("sign", sign[i]);


                    JSONArray dd = new JSONArray();
                    for (int a = 0; a < ress.size(); a++) {
                        JSONObject aone = ress.getJSONObject(a);
                        //    logger.warn(aone.toString());
                        JSONObject d = new JSONObject();
                        String code = aone.getString("code");
                        String name = aone.getString("name");
                        if (!name.equals("合计")) {
                            String count = aone.getString(ks[i]);
                            d.put("code", code);

                            d.put("name", name);

                            if (sign[i].equals("%")) {
                                if (count.startsWith("0.")) {
                                    double c = Double.parseDouble(count) * 100;
                                    count = String.format("%.2f", c);
                                }
                            }
                            d.put("count", count);
                            d.put("id", code);

                            JSONObject click = new JSONObject();
                            click.put("type", "dialog");
                            click.put("permission", "table_page");
                            JSONObject opts = new JSONObject();
                            JSONObject cls = new JSONObject();
                            cls.put("unit", code);
                            cls.put("code", code);
                            cls.put("page", "$page$");
                            cls.put("limit", "$limit$");
                            cls.put("name", name);
                            cls.put("lx", jps[i].toLowerCase());
                            cls.put("start_time", start_time);
                            cls.put("end_time", end_time);
                            cls.put("u", "pageReportCzfw");
                            opts = GetOpts("/screen_place", "get_czf_danger_table", cls);

                            click.put("remark", opts);
                            click.put("label", name + "_租赁房屋隐患");
                            d.put("click", click);

                            JSONObject dpclick = new JSONObject();
                            dpclick.put("type", "next");
                            dpclick.put("url", "/screen_place");


                            cls = new JSONObject();
                            cls.put("unit", code);
                            cls.put("code", code);

                            cls.put("name", name);
                            cls.put("lx", lx);
                            cls.put("start_time", start_time);
                            cls.put("end_time", end_time);
                            opts = GetOpts("/screen_place", "get_house_danger_find_lx", cls);


                            dpclick.put("opt", opts);
                            d.put("dpclick", dpclick);

                            dd.add(d);
                        }

                    }
                    det.put("det", dd);
                    datas.add(det);
                }

                back.put("data", datas);
                return back;
            } else {
                logger.error(resj.toString());
                return ErrNo.set(null, 2, resj.getString("message"));
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }

    }

    private JSONObject getHouseDangerDealLx(JSONObject data, String token) {

        JSONObject back = ErrNo.set(0);
        try {
            long t = System.currentTimeMillis() / 1000;
            String end_time = data.getString("end_time");
            end_time = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")); //统计表数据暂时写死 为昨天数据
            String start_time = data.getString("start_time");
            String unit = data.getString("unit");
            String lx = data.getString("lx");
            int type = RIUtil.dicts.get(unit).getInteger("type");
            int orgType = 0;
            if (type == 21 || type == 22 || type == 27) {
                orgType = 2;
                unit = unit.substring(0, 4) + "00000000";

            } else if (type == 23 || type == 24 || type == 28) {
                unit = unit.substring(0, 6) + "000000";
                orgType = 3;
            } else if (type == 25) {
                orgType = 4;
                unit = unit;
            } else {
                unit = unit;
                orgType = 4;
            }


            String url = "http://50.56.94.47/czqjpt-api/report/czwyhqk?_t=" + t + "&orgType=" + orgType + "&column" +
                    "=createTime&order=desc&pageNo=1" + "&pageSize=99&parentOrgCode=" + unit + "&end=" + end_time;
            logger.warn("========>>>>" + url);

            OkHttpClient client = new OkHttpClient().newBuilder().build();
            Request request =
                    new Request.Builder().url(url).method("GET", null).addHeader("X-Access-Token", token).build();
            Response response = client.newCall(request).execute();
            String res = response.body().string();
            JSONObject resj = JSONObject.parseObject(res);
            if (resj.containsKey("code") && resj.getInteger("code") == 200) {
                JSONArray rs = resj.getJSONArray("result");
                String cols = "隐患数,已整改,整改率";
                String[] cs = cols.split(",");
                String jumps = "$lx$zs,$lx$yzg,$lx$wzg".replace("$lx$", lx.toLowerCase());
                String[] jps = jumps.split(",");
                String xxs = "bar,bar,line";
                String signs = ",,%";
                String[] sign = signs.split(",");
                String[] xs = xxs.split(",");
                String keys = "czw$lx$hjs,czw$lx$yzgs,czw$lx$zgl".replace("$lx$", lx);
                String ks[] = keys.split(",");


                JSONArray datas = new JSONArray();
                for (int i = 0; i < cs.length; i++) {
                    JSONObject det = new JSONObject();
                    det.put("id", (i + 1));
                    det.put("name", cs[i]);
                    det.put("xx", xs[i]);
                    det.put("sign", sign[i]);

                    String col = ks[i];

                    JSONArray dd = new JSONArray();
                    for (int a = 0; a < rs.size(); a++) {
                        JSONObject aone = rs.getJSONObject(a);
                        JSONObject d = new JSONObject();
                        String code = aone.getString("jgbm");
                        String name = aone.getString("jgmc");
                        if (!name.equals("合计")) {
                            String count = aone.getString(col);
                            if (count == null || count.length() == 0) {
                                count = "0";
                            }
                            d.put("code", code);

                            d.put("name", name);

                            if (sign[i].equals("%")) {

                                //整改率 0% --> 100%
                                if (aone.getInteger(ks[0]) == 0) {
                                    count = "1.0";
                                }
                                if (count.startsWith("0.") || count.equals("1.0")) {
                                    double c = Double.parseDouble(count) * 100;
                                    count = String.format("%.2f", c);
                                }

                            }
                            d.put("count", count);
                            d.put("id", code);

                            JSONObject click = new JSONObject();
                            click.put("type", "dialog");
                            click.put("permission", "table_page");
                            JSONObject opts = new JSONObject();
                            JSONObject cls = new JSONObject();
                            cls.put("unit", code);
                            cls.put("code", code);
                            cls.put("page", "$page$");
                            cls.put("limit", "$limit$");
                            cls.put("name", name);
                            cls.put("lx", jps[i]);
                            //  cls.put("start_time", start_time);
                            cls.put("end_time", end_time);
                            cls.put("u", "czfyhqk");
                            opts = GetOpts("/screen_place", "get_czf_danger_table", cls);

                            click.put("remark", opts);
                            click.put("label", name + "_租赁房屋隐患");
                            d.put("click", click);

                            JSONObject dpclick = new JSONObject();
                            dpclick.put("type", "next");
                            dpclick.put("url", "/screen_place");


                            cls = new JSONObject();
                            cls.put("unit", code);
                            cls.put("code", code);

                            cls.put("name", name);
                            cls.put("lx", lx);
                            cls.put("start_time", start_time);
                            cls.put("end_time", end_time);
                            opts = GetOpts("/screen_place", "get_house_danger_deal_lx", cls);

                            dpclick.put("opt", opts);
                            d.put("dpclick", dpclick);

                            dd.add(d);
                        }

                    }
                    det.put("det", dd);
                    datas.add(det);
                }

                back.put("data", datas);
                return back;
            } else {
                logger.error(resj.toString());
                return ErrNo.set(null, 2, resj.getString("message"));
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }

    }

    private JSONObject getRisk(JSONObject data) {
        //返回格式 日 活动 地点 风险等级
        JSONObject back = ErrNo.set(0);
        String start_time = "";
        String end_time = "";
        String where = " 1=1 ";
        if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
            start_time = data.getString("start_time");
            where += " and VL.DATE >='" + start_time + "' ";
        }
        if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
            end_time = data.getString("end_time");
            where += " and VL.DATE <='" + end_time + "' ";
        }
        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql_94_250");
            mysql.update("set SESSION group_concat_max_len = 10240");
            String sql = "select VL.* ,group_concat(concat(VE.TITLE,'-',LOCALE) SEPARATOR '&') as active ," +
                    "group_concat(concat(VH.TITLE,'-',VH.DESCRIPTION) SEPARATOR '&') as holiday  from " + " " +
                    "V_QJJC_RISK_DUTY_LEVEL VL LEFT JOIN V_QJJC_RISK_MAJOR_EVENT VE on VL.DATE >= VE.KS_DATE AND VL" + ".DATE <=VE.JS_DATE " + " LEFT JOIN V_QJJC_RISK_MAJOR_HOLIDAY VH ON VH.DATE = VL.DATE  " + " WHERE " + where + " GROUP BY DATE";
            logger.warn("======>>> sql :  " + sql);
            List<JSONObject> query = mysql.query(sql);
            JSONArray datas = new JSONArray();
            for (JSONObject one : query) {
                JSONObject det = new JSONObject();
                det.put("date", one.getString("DATE"));
                det.put("level", one.getInteger("LEVEL"));

                if (one.getString("active").length() > 0 && !one.getString("active").equals("null") || one.getString(
                        "holiday").length() > 0 && !one.getString("holiday").equals("null")) {
                    JSONArray array = new JSONArray();
                    array = new JSONArray();
                    String holiday = one.getString("holiday");
                    if (one.getString("holiday").length() > 0 && one.containsKey("holiday")) {
                        String[] splitStr = holiday.split("&");
                        Set<String> seen = new HashSet<>(); // 去重
                        for (String str : splitStr) {
                            if (!seen.contains(str.substring(0, 12))) {
                                JSONObject dt = new JSONObject();
                                String[] split = str.split("-");
                                dt.put("name", split[0]);
                                dt.put("affair", split[1]);
                                array.add(dt);
                                seen.add(str.substring(0, 12));
                            }
                        }
                    }
                    det.put("holiday", array);
                    array = new JSONArray();
                    String active = one.getString("active");
                    if (one.getString("active").length() > 0 && one.containsKey("active")) {
                        String[] splitStr = active.split("&");
                        Set<String> seen = new HashSet<>(); // 去重
                        for (String str : splitStr) {
                            if (!seen.contains(str)) {
                                JSONObject dt = new JSONObject();
                                try {
                                    String[] split = str.split("-");
                                    dt.put("name", split[0]);
                                    dt.put("address", split[1]);
                                } catch (Exception ex) {
                                    logger.error(str);
                                    logger.error(Lib.getTrace(ex));
                                }
                                array.add(dt);
                                seen.add(str);
                            }
                        }
                    }
                    det.put("actice", array);
                    datas.add(det);
                } else {
                    det.put("active", "此日无活动");
                }
            }
            back.put("data", datas);

        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            mysql.close();
        }

        return back;

    }

    private JSONObject getHouseDanger(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper my_143 = null;
        try {
            String unit = data.getString("unit");
            String sql = "";
            int t = 22;
            String type = RIUtil.dicts.get(unit).getString("type");
            if (type.equals("21") || type.equals("22") || type.equals("27")) {
                t = 23;
                unit = unit.substring(0, 4) + "00000000";
            } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                t = 25;
                unit = unit.substring(0, 6) + "000000";
            } else if (type.equals("25") || type.equals("26")) {
                t = 26;
                unit = unit.substring(0, 8) + "0000";
            }
            JSONArray units = RIUtil.GetDictByTypeFather(t, unit);
            logger.warn(units.toString());
            HashMap<String, String> us = new HashMap<>();
            for (int i = 0; i < units.size(); i++) {
                JSONObject one = units.getJSONObject(i);
                String id = one.getString("id");
                us.put(id, "");
            }


            String lx = data.getString("lx");
            List<JSONObject> lxss = new ArrayList<>();

            try {
                my_143 = new MysqlHelper("mysql_zxqc");
                sql = "select * from sta_zf_mid where type in ('" + lx.replace(",", "','") + "')";
                logger.warn(sql);

                lxss = my_143.query(sql);

            } catch (Exception ex) {
                logger.warn(Lib.getTrace(ex));
                return ErrNo.set(null, 2, Lib.getTrace(ex));
            } finally {
                my_143.close();
            }


            String lxs[] = lx.split(",");
            String sqllx = "";
            JSONArray rets = new JSONArray();
            for (int i = 0; i < lxs.length; i++) {
                String l = lxs[i];
                String name = RIUtil.dicts.get(l).getString("dict_name");
                JSONObject det = new JSONObject();
                det.put("id", l);
                det.put("name", name);
                List<JSONObject> dets = new ArrayList<>();
                for (int a = 0; a < lxss.size(); a++) {
                    JSONObject one = lxss.get(a);
                    //  System.out.println(one);
                    String id = one.getString("code");
                    one.put("id", id);

                    String llxx = one.getString("type");

                    if (us.containsKey(id) && llxx.equals(l)) {
                        try {
                            String codeName = RIUtil.dicts.get(one.getString("code")).getString("dict_name");
                            one.put("name", codeName);
                            dets.add(one);
                        } catch (Exception ex) {
                            logger.warn(id);
                        }
                    }
                }
                Collections.sort(dets, (JSONObject o1, JSONObject o2) -> {

                    long a = o1.getLong("id");
                    long b = o2.getLong("id");

                    // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                    if (a < b) {  //降序排列，升序改成a>b
                        return 1;
                    } else if (a == b) {
                        return 0;
                    } else {
                        return -1;
                    }
                });

                det.put("det", dets);

                rets.add(det);
            }
            back.put("data", rets);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }


    }

    private JSONArray GetHeads(String headN, String hdKeys) {
        JSONArray rets = new JSONArray();
        String[] hdns = headN.split(",");
        String[] hdks = hdKeys.split(",");

        for (int i = 0; i < hdks.length; i++) {
            JSONObject det = new JSONObject();
            String key = hdks[i];
            String value = hdns[i];
            det.put("key", key);
            det.put("value", value);
            rets.add(det);

        }
        return rets;
    }

    private JSONObject GetOpts(String url, String opt, JSONObject cols) {

        JSONObject opts = new JSONObject();
        opts.put("url", url);
        opts.put("opt", opt);
        opts.put("opt_user", "$opt_user$");


        opts.putAll(cols);
        return opts;
    }

    private JSONObject GetOkHttpGet(String url, String token) {
        try {

            OkHttpClient client =
                    new OkHttpClient().newBuilder().connectTimeout(500, TimeUnit.SECONDS).readTimeout(500,
                            TimeUnit.SECONDS).writeTimeout(500, TimeUnit.SECONDS).build();
            Request request =
                    new Request.Builder().url(url).method("GET", null).addHeader("X-Access-Token", token).build();
            Response response = client.newCall(request).execute();
            String res = response.body().string();
            JSONObject resj = JSONObject.parseObject(res);
            return resj;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            JSONObject back = new JSONObject();
            back.put("code", 301);
            back.put("message", Lib.getTrace(ex));
            return back;
        }
    }

}
