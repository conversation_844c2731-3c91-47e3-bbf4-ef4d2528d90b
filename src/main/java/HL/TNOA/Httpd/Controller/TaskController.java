package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.Lib.TNOAConf;
import HL.TNOA.wechat.wechatMsgTemp;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class TaskController {
    private static Logger logger = LoggerFactory.getLogger(TaskController.class);
    //private SimpleDateFormat new SimpleDateFormat("yyyy-MM-dd HH:mm:ss") = ;
    public static int[] cycle_days_list = {0, 1, 7, 15, 30, 90, 180, 365};
    private static String ip = "";

    @RequestMapping(method = {RequestMethod.POST}, path = {"/task"})
    @PassToken
    public static JSONObject get_task(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        ip = request.getRemoteAddr();
        String opt = "";
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("task_create")) {
                logger.warn("task--->" + data.toString());
                return createTask(data);
            }
            if (opt.equals("task_create_list")) {
                logger.warn("task--->" + data.toString());
                return createTask_list(data);

            } else if (opt.equals("task_get")) {
                return getTask(data);

            } else if (opt.equals("task_get_list")) {
//                logger.warn("task-->" + data);
                return getTaskList(data);
            } else if (opt.equals("task_delete")) {
                return deleteTask(data);
            } else if (opt.equals("task_turning")) {
                logger.warn("task--->" + data.toString());
                return taskTuring(data);
            } else if (opt.equals("task_up_status")) {
                logger.warn("task--->" + data.toString());
                return upStatus(data);
            } else if (opt.equals("task_update")) {
                logger.warn("task--->" + data.toString());
                return updateTask(data);
            } else if (opt.equals("task_main")) {
                return mainTask(data);
            } else if (opt.equals("task_apply_get")) {
                return TaskApplyController.getTaskApply(data);
            } else if (opt.equals("task_apply_create")) {
                return TaskApplyController.createTaskApply(data, ip);
            } else if (opt.equals("task_apply_update")) {
                return TaskApplyController.updateTaskApply(data, ip);
            } else if (opt.equals("task_apply_delete")) {
                return TaskApplyController.deleteTaskApply(data, ip);
            } else {
                return ErrNo.set(433013);
            }

        } else {
            return ErrNo.set(433013);
        }


    }


    private static JSONObject mainTask(JSONObject data) {
        String user_id = "";
        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");

        } else {
            return ErrNo.set(433003);
        }
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "select id,decode(title,'" + RIUtil.enTitle + "') as title,decode(content,'" + RIUtil.enContent + "') " + "as" + " content," + "create_user,start_time,end_time,level,type,accepter,isPublish," + "father_id " + "from" + " task " + "where (accepter like '%" + user_id + "%' or checked like '%" + user_id + "%')" + "and " + "isdelete=1 and " + "isPublish =1 " + "and (type!='9999999' and type!='22222')" + "and " + "cycle!=1 order " + "by " + "end_time";

            List<JSONObject> list = new ArrayList<>();

            list = mysql.query(sqls);
            sqls = "select id,decode(title,'" + RIUtil.enTitle + "') as title,decode(content,'" + RIUtil.enContent +
                    "') as content," + "create_user,start_time,end_time,level,type,accepter,isPublish,father_id " +
                    "from " + "task where (type!='9999999' and type!='22222') and (accepter like '%" + user_id + "%' "
                    + "or checked" + " like " + "'%" + user_id + "%') " + "and isdelete=1 and isPublish =1 " + "and "
                    + "(cycle=1 and start_time>='" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + " " +
                    "00:00" + "：00' " + "and start_time<='" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + " " + "23:59:59') order by end_time";
            // logger.warn(sqls);
            List<JSONObject> list1 = new ArrayList<>();
            list1 = mysql.query(sqls);

            list.addAll(list1);

            back.put("unfinished", RelaInfoList(list, mysql, ""));
            String now = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String serven = RIUtil.GetNextDateTime(now, -7);
            sqls = "select id,decode(title,'" + RIUtil.enTitle + "') as title,decode(content,'" + RIUtil.enContent +
                    "') as content," + "create_user,start_time,end_time,level,type,accepter,isPublish,father_id " +
                    "from " + "task where (type!='9999999' and type!='22222') and finished like '%" + user_id + "%' " + "and isdelete=1 and end_time>='" + serven + "' and isPublish=1 " + "and cycle!=1 order by " + "end_time desc";

            list = new ArrayList<>();
            list = mysql.query(sqls);
            sqls = "select id,decode(title,'" + RIUtil.enTitle + "') as title,decode(content,'" + RIUtil.enContent +
                    "') as content," + "create_user,start_time,end_time,level,type,accepter,isPublish,father_id " +
                    "from " + "task where (type!='9999999' and type!='22222') and finished like '%" + user_id + "%' " + "and isdelete=1 and " + "end_time>='" + serven + "' and isPublish=1 " + "and (cycle=1 and start_time>='" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + " 00:00：00' " + "and start_time<='" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + " 23:59:59') order by end_time desc";

            list1 = new ArrayList<>();
            list1 = mysql.query(sqls);
            list.addAll(list1);

            back.put("finished", RelaInfoList(list, mysql, user_id));

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(433014);
        } finally {
            InfoModelPool.putModel(mysql);

        }
        // logger.warn(back.toString());
        return back;

    }

    private static JSONObject updateTask(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String title = "";
            String content = "";
            String level = "0";
            String type = "";
            String start_time = "";
            String end_time = "";
            int cycle = 0;
            int cycle_days = 0;
            long cycle_seconds = 0;
            String cycle_end = "";
            String users = "";
            String groups = "";
            String link = "";
            String comment_select = "";
            String comment_type = "";
            String isMsg = "";
            String isWechat = "";
            int isAll = 0;
            String accessory = "";
            String img = "";
            String opt_user = "";
            String near_time = "";
            int isFA = 0;
            String isSub = "0";
            String subType = "";
            String unit = "";
            String source = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' , ";
            }
            if (data.containsKey("title") && data.getString("title").length() > 0) {
                title = data.getString("title");
                sql = sql + " title=encode('" + title + "','" + RIUtil.enTitle + "') , ";
            }
            if (data.containsKey("source") && data.getString("source").length() > 0) {
                source = data.getString("source");
                sql = sql + "source='" + source + "',";
            }
            if (data.containsKey("content") && data.getString("content").length() > 0) {
                content = data.getString("content");
                sql = sql + " content=encode('" + content + "','" + RIUtil.enContent + "') , ";
            }
            if (data.containsKey("level") && data.getString("level").length() > 0) {
                level = data.getString("level");
            }
            sql = sql + " level='" + level + "' , ";
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
            }
            sql = sql + " type='" + type + "' , ";
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time").replace("|", " ");
                sql = sql + " start_time='" + start_time + "' , ";
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time").replace("|", " ");
                sql = sql + " end_time='" + end_time + "' , ";
            }
            if (data.containsKey("accessory")) {
                accessory = data.getString("accessory");
                sql = sql + " accessory='" + accessory + "' , ";
            }
            if (data.containsKey("img") && data.getString("img").length() > 0) {
                img = data.getString("img");
                sql = sql + " img='" + img + "' , ";
            }
            if (data.containsKey("link") && data.getString("link").length() > 0) {
                link = data.getString("link");
                sql = sql + " link='" + link + "' , ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " unit='" + unit + "' , ";
            }
            if (data.containsKey("cycle") && data.getString("cycle").length() > 0) {
                cycle = data.getInteger("cycle");
                if (cycle > 0) {
                    logger.warn(start_time);
                    String date_time[] = start_time.split(" ");

                    String date = date_time[0];

                    if (end_time.length() == 0) {
                        end_time = date + " 23:59:59";
                    }
                    logger.warn(end_time);
                    if (cycle == 8) {
                        if (data.containsKey("cycle_days") && data.getString("cycle_days").length() > 0) {
                            cycle_days = data.getInteger("cycle_days");
                        } else {
                            return ErrNo.set(433012);
                        }
                    } else {
                        cycle_days = cycle_days_list[cycle];
                    }


                } else {
                    cycle_end = end_time;
                }

            }
            cycle_seconds = RIUtil.dateToStamp(end_time) - RIUtil.dateToStamp(start_time);
            //logger.warn(RIUtil.dateToStamp(end_time) + "-" + RIUtil.dateToStamp(start_time) + "=" + cycle_seconds);
            long be = cycle_seconds - (cycle_seconds / 10);
            long nt = RIUtil.dateToStamp(start_time) + be;
            if (data.containsKey("near_time") && data.getString("near_time").length() > 0) {
                near_time = data.getString("near_time").replace("|", " ");
            } else {
                near_time = RIUtil.stampToTime(nt);
            }
            if (cycle_seconds < 0) {
                logger.error("startTime bigger than endTime");
                return ErrNo.set(433012);
            }
            sql = sql + " cycle='" + cycle + "' , ";
            sql = sql + " cycle_days='" + cycle_days + "' , ";
            sql = sql + " cycle_seconds='" + cycle_seconds + "' , ";
            sql = sql + " isPublish=0,";
            sql = sql + " near_time='" + near_time + "',";


            if (data.containsKey("cycle_end") && data.getString("cycle_end").length() > 0) {
                cycle_end = data.getString("cycle_end").replace("|", " ");
                sql = sql + " cycle_end='" + cycle_end + "' , ";
            }
            if (data.containsKey("users") && data.getString("users").length() > 0) {
                users = data.getString("users");
            }
            sql = sql + " users='" + users + "' , ";
            if (data.containsKey("groups") && data.getString("groups").length() > 0) {
                groups = data.getString("groups");
            }
            sql = sql + " groups='" + groups + "' , ";


            if (data.containsKey("comment_select") && data.getString("comment_select").length() > 0) {
                comment_select = data.getString("comment_select");
                sql = sql + " comment_select='" + comment_select + "' , ";
            }
            if (data.containsKey("comment_type") && data.getString("comment_type").length() > 0) {
                comment_type = data.getString("comment_type");
                sql = sql + " comment_type='" + comment_type + "' , ";
            }
            if (data.containsKey("isMsg") && data.getString("isMsg").length() > 0) {
                isMsg = data.getString("isMsg");
                sql = sql + " isMsg='" + isMsg + "' , ";
            }
            if (data.containsKey("isWechat") && data.getString("isWechat").length() > 0) {
                isWechat = data.getString("isWechat");
                sql = sql + " isWechat='" + isWechat + "' , ";
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            if (data.containsKey("is_all") && data.getString("is_all").length() > 0) {
                isAll = data.getInteger("is_all");
            }
            if (data.containsKey("isSub") && data.getString("isSub").length() > 0) {
                isSub = data.getString("isSub");
                sql = sql + " isSub='" + isSub + "' , ";
            }
            if (data.containsKey("subType") && data.getString("subType").length() > 0) {
                subType = data.getString("subType");
                sql = sql + " subType='" + subType + "' , ";
            }

            if (data.containsKey("isFA") && data.getString("isFA").length() > 0) {
                isFA = data.getInteger("isFA");
                sql = sql + " isFA='" + isFA + "' , ";
            }
            String sqls = "update task set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);
            logger.warn(sqls);
            if (isAll == 1) {
                sql = "select father_id from task where id='" + id + "'";
                List<JSONObject> list = mysql.query(sql);
                if (list.size() > 0) {
                    String father_id = list.get(0).getString("father_id");
                    if (father_id == null || father_id.length() == 0) {
                        father_id = id;
                    }

                    sql = "delete from task  where father_id='" + father_id + "' and id>'" + id + "' and isPublish=0";
                    logger.warn(sql);
                    mysql.update(sql);
                }
            }
            publishNotice_Task("");

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(433003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;

    }


    public static JSONObject upStatus(JSONObject data) throws Exception {
        String id = "";
        String user_id = "";
        int status = 0;
        String opt_user;

        JSONObject back = ErrNo.set(0);
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(433024);
        }
        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");
        } else {
            return ErrNo.set(433024);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(433024);
        }
        if (data.containsKey("status") && data.getString("status").length() > 0) {
            status = data.getInteger("status");
        } else {
            return ErrNo.set(433024);
        }
        int staFin = 0;
        InfoModelHelper mysql = null;
        String sqls = "";
        try {
            mysql = InfoModelPool.getModel();
            String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String last_type = "0";
            if (status == 3) {
                String sql =
                        "select id, task_type from task_log where task_id='" + id + "' and user_id='" + user_id + "' " +
                                "and task_type!=3 order by time desc limit 1";
                List<JSONObject> list = mysql.query(sql);
                if (list.size() > 0) {
                    String log_id = list.get(0).getString("id");
                    last_type = list.get(0).getString("task_type");
                    sql = "delete from task_log where id='" + log_id + "'";
                    mysql.update(sql);
                }

                sql = "update comment set isdelete=3 where notice_id='" + id + "' and comment_user='" + user_id + "' "
                        + "and isdelete=1";
                mysql.update(sql);
            }


            sqls =
                    "INSERT INTO task_log (task_id, task_type, time, opt_user,cancel_type,user_id) " + "VALUES ( '" + id +
                            "', '" + status + "', '" + time + "', '" + opt_user + "','" + last_type + "','" + user_id + "');";
            mysql.update(sqls);

            sqls = "select accepter,finished,checked,status,create_user,isFA,type from task where id='" + id + "'";
            List<JSONObject> list = mysql.query(sqls);
            if (list.size() > 0) {
                String accepter = list.get(0).getString("accepter");
                String finished = list.get(0).getString("finished");
                String checked = list.get(0).getString("checked");
                String tStatus = list.get(0).getString("status");
                String create_user = list.get(0).getString("create_user");
                String type = list.get(0).getString("type");
                logger.warn(type);
                int isFA = list.get(0).getInteger("isFA");
                //logger.warn(accepter + "-->" + finished);
                HashMap accs = new HashMap();
                accs = RIUtil.StringToList(accepter);
                HashMap fin = new HashMap();
                fin = RIUtil.StringToList(finished);
                HashMap check = new HashMap();
                check = RIUtil.StringToList(checked);
                String sql = "isdelete='" + 1 + "'";
                HashMap<String, String> nup = new HashMap<String, String>();

                if (status == 1) {
                    if (isFA == 1 && fin.size() > 0) {
                        fin.put(user_id, "");
                        nup = fin;
                        accs.remove(user_id);
                        staFin = 2;
                        if (accs.size() == 0 && check.size() == 0) {
                            String s =
                                    "update task set status=1,finish_time='" + new SimpleDateFormat("yyyy-MM-dd " +
                                            "HH:mm" + ":ss").format(new Date()) + "'";
                            mysql.update(s);
                            int source = 2;
                            if (type.equals("9999999")) {
                                source = 6;
                            }
                            wechatMsgTemp.createDingMsg(id, "所有人完成该事项", create_user, source, create_user, mysql, id);
                        }
                    } else {
                        accs.remove(user_id);
                        check.put(user_id, "");
                        staFin = 1;
                    }
                    sql = "accepter='" + RIUtil.HashToList(accs) + "',checked='" + RIUtil.HashToList(check) + "'," +
                            "finished='" + RIUtil.HashToList(fin) + "'";

                } else if (status == 2) {
                    staFin = 2;
                    //if (tStatus.equals("2")) {
                    if (check.containsKey(user_id)) {
                        if (isFA == 1) {
                            fin = check;
                            nup = fin;
                            check = new HashMap();
                        } else {
                            check.remove(user_id);
                            fin.put(user_id, "");

                        }
                        sql = "checked='" + RIUtil.HashToList(check) + "',finished='" + RIUtil.HashToList(fin) + "'";
                        if (accs.size() == 0 && check.size() == 0) {
                            sql =
                                    sql + ", status=1,finish_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "'";

                            int source = 2;
                            if (type.equals("9999999")) {
                                source = 6;
                            }
                            if (type.equals("22222")) {
                                source = 4;
                            }
                            wechatMsgTemp.createDingMsg(id, "所有人完成该事项", create_user, source, create_user, mysql, id);
                        }

                    }
                   /* } else {
                        return ErrNo.set(433028);
                    }*/
                } else if (status == 3) {
                    if (accs.size() == 0 && check.size() == 0) {
                        if (tStatus.equals("2")) {
                            sql = sql + ", status=2,finish_time=''";
                        } else {
                            sql = sql + ", status=0,finish_time=''";
                        }
                    }
                    if (last_type.equals("2")) {
                        staFin = 1;
                        if (fin.containsKey(user_id) && !check.containsKey(user_id)) {
                            if (isFA == 1) {
                                check = fin;
                                nup = fin;
                                fin = new HashMap();
                            } else {
                                fin.remove(user_id);
                                check.put(user_id, "");
                            }
                            int sta = 0;
                            if (accs.size() == 0 && check.size() == 0) {
                                sta = 1;
                            } else {
                                sta = Integer.parseInt(tStatus);
                            }
                            sql = "checked='" + RIUtil.HashToList(check) + "',finished='" + RIUtil.HashToList(fin) +
                                    "',status='" + sta + "'";

                        }

                    } else if (last_type.equals("1")) {
                        staFin = 0;
                        if (check.containsKey(user_id) && !accs.containsKey(user_id)) {
                            check.remove(user_id);
                            accs.put(user_id, "");
                            int sta = Integer.parseInt(tStatus);
                            if (accs.size() > 0) {
                                sta = Integer.parseInt(tStatus);
                            } else if (check.size() > 0) {
                                sta = Integer.parseInt(tStatus);
                            }
                            sql =
                                    "accepter='" + RIUtil.HashToList(accs) + "',checked='" + RIUtil.HashToList(check) + "'," + "status='" + sta + "'";
                        }
                    } else {
                        return ErrNo.set(433026);
                    }


                } else {
                    return ErrNo.set(433026);
                }

                if (sql.length() > 0) {
                    sqls = "update task set " + sql + " where id='" + id + "'";
                    // logger.warn(sqls);
                    mysql.update(sqls);
                }

                if (isFA == 0) {
                    sqls = "update task_accepter set status='" + staFin + "' where rela_id='" + id + "' and " +
                            "user_id='" + user_id + "'";
                    mysql.update(sqls);
                } else {
                    if (nup.size() > 0) {
                        for (Map.Entry<String, String> o : nup.entrySet()) {
                            sqls =
                                    "update task_accepter set status='" + staFin + "' where rela_id='" + id + "' and " +
                                            "user_id='" + o.getKey() + "'";
                            mysql.update(sqls);
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(433018);
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return back;

    }

    private static JSONObject taskTuring(JSONObject data) throws Exception {
        String id = "";
        String from_user = "";
        String to_user = "";

        JSONObject back = ErrNo.set(0);
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(433020);
        }
        if (data.containsKey("from_user") && data.getString("from_user").length() > 0) {
            from_user = data.getString("from_user");
        } else {
            return ErrNo.set(433020);
        }

        if (data.containsKey("to_user") && data.getString("to_user").length() > 0) {
            to_user = data.getString("to_user");
        } else {
            return ErrNo.set(433020);
        }


        InfoModelHelper mysql = null;
        HashMap<String, String> uu = new HashMap<>();
        try {
            mysql = InfoModelPool.getModel();

            //判断原接收人是否有指派人员
            String sqls = "select * from task where id='" + id + "' and isdelete=1";
            List<JSONObject> list = mysql.query(sqls);
            HashMap<String, String> accs = new HashMap<>();
            HashMap<String, String> fined = new HashMap<>();
            HashMap<String, String> userd = new HashMap<>();
            if (list.size() > 0) {
                JSONObject one = list.get(0);
                String accepter = one.getString("accepter");
                accs = RIUtil.StringToList(accepter);
                HashMap<String, String> tolist = RIUtil.StringToList(to_user);
                for (Map.Entry<String, String> a : tolist.entrySet()) {
                    String to_id = a.getKey();
                    if (accs.containsKey(to_id)) {
                        return ErrNo.set(433021);
                    }
                }
                String finished = one.getString("finished");
                if (finished.length() > 0) {
                    fined = RIUtil.StringToList(finished);
                }
                String users = one.getString("users");
                if (users.length() > 0) {
                    userd = RIUtil.StringToList(users);

                }

            } else {
                return ErrNo.set(433023);
            }

            //判断流转了多少层
            String turn_id = "";
            String father_turn_id = "";
            sqls = "select id,father_id from task_turning where task_id='" + id + "' and user_id='" + from_user + "'";
            list = mysql.query(sqls);
            if (list.size() > 0) {
                JSONObject one = list.get(0);
                turn_id = one.getString("id");
                try {
                    father_turn_id = one.getString("father_id");
                } catch (Exception ex) {
                    father_turn_id = turn_id;
                }
                if (father_turn_id == null || father_turn_id.length() == 0) {
                    father_turn_id = turn_id;
                }
            }
            sqls = "select id from task_turning where father_id='" + father_turn_id + "'";
            list = mysql.query(sqls);
            if (list.size() >= 5) {
                return ErrNo.set(433022);
            }

            sqls =
                    "insert task_turning (task_id,user_id,time," + "status,father_id)" + "values('" + id + "', '" + to_user + "', '" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "'," + " 1, '" + father_turn_id + "') ";
            mysql.update(sqls);

            //accs.remove(from_user);
            HashMap<String, String> accH = RIUtil.StringToList(to_user);
            accs.putAll(accH);
            List<String> accepters = RIUtil.HashToList(accs);
            fined.remove(to_user);
            List<String> finishedList = RIUtil.HashToList(fined);
            userd.put(to_user, "");
            List<String> userlist = RIUtil.HashToList(userd);
            sqls =
                    "update task set accepter='" + accepters + "',finished='" + finishedList + "',users='" + userlist + "' " + "where id='" + id + "'";
            mysql.update(sqls);
            String names = "";
            for (Map.Entry<String, String> one : accH.entrySet()) {

                names = names + RIUtil.IdToName(one.getKey(), mysql, " name", "user") + ",";
                String sql = "INSERT INTO `task_accepter` ( `rela_id`, `user_id`, `status`, `start_time`, `end_time`,"
                        + " `near_time`) " + "select `rela_id`, '" + one.getKey() + "' as user_id, `status`, " +
                        "`start_time`, " + "`end_time`, `near_time` from task_accepter where rela_id='" + id + "' " + "and" + " " + "user_id='" + from_user + "';";
                mysql.update(sql);
            }

            String com = "添加协作人:" + names;
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String sql =
                    "insert comment (notice_id,comment,comment_user,create_time,father_comment_id,comment_type," +
                            "isdelete,source)" + "values('" + id + "',encode('" + com + "','" + RIUtil.enContent +
                            "'),'" + from_user + "','" + create_time + "','" + father_turn_id + "','" + 1 + "','" + 1 + "','" + 2 + "') ";
            mysql.update(sql);
            sql = "select id from comment where notice_id='" + id + "' and create_time='" + create_time + "' and " +
                    "comment_user='" + from_user + "' ";
            String comment_id = mysql.query_one(sql, "id");

            wechatMsgTemp.createDingMsg(id, " 请求协作", from_user, 2, to_user, mysql, comment_id);
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(433018);
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return back;
    }

    private static JSONObject createTask(JSONObject data) throws Exception {
        String title = "";
        String content = "";
        int level = 0;
        int type = 0;
        String start_date = "";
        String start_time = "";
        String end_time = "";
        int cycle = 0;
        int cycle_days = 0;
        long cycle_seconds = 0;
        String cycle_end = "";
        String users = "";
        String groups = "";
        int status = 0;
        int isPublish = 0;
        String create_user = "";
        String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        int isdelete = 1;
        int isWechat = 0;
        int isMsg = 0;
        String comment_type = "1";
        String accessory = "";
        String img = "";
        String link = "";
        String near_time = "";
        int isFA = 0;
        String isSub = "0";
        String subType = "";
        String unit = "";
        String source = "";
        if (data.containsKey("title") && data.getString("title").length() > 0) {
            title = data.getString("title");
        } else {
            return ErrNo.set(433012);
        }
        if (data.containsKey("content") && data.getString("content").length() > 0) {
            content = data.getString("content");
        } else {
            return ErrNo.set(433012);
        }
        if (data.containsKey("level") && data.getString("level").length() > 0) {
            level = data.getInteger("level");
        }
        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getInteger("type");
        } else {
            return ErrNo.set(433012);
        }


        if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
            start_time = data.getString("start_time").replace("|", " ");
        } else {
            return ErrNo.set(433012);
        }
        if (data.containsKey("source") && data.getString("source").length() > 0) {
            source = data.getString("source");

        }
        if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
            end_time = data.getString("end_time").replace("|", " ");
        }
        if (data.containsKey("accessory") && data.getString("accessory").length() > 0) {
            accessory = data.getString("accessory");
        }
        if (data.containsKey("img") && data.getString("img").length() > 0) {
            img = data.getString("img");
        }
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
        }
        if (data.containsKey("link") && data.getString("link").length() > 0) {
            link = data.getString("link");
        }

        if (data.containsKey("cycle_end") && data.getString("cycle_end").length() > 0) {
            cycle_end = data.getString("cycle_end").replace("|", " ");
        } else {
            int year = Integer.parseInt(new SimpleDateFormat("yyyy").format(new Date())) + 2;
            cycle_end = year + "-12-31 23:59:59";
        }
        if (data.containsKey("cycle") && data.getString("cycle").length() > 0) {
            cycle = data.getInteger("cycle");
            if (cycle > 0) {
                String date_time[] = start_time.split(" ");

                String date = date_time[0];

                if (end_time.length() == 0) {
                    end_time = date + " 23:59:59";
                }
                if (cycle == 8) {
                    if (data.containsKey("cycle_days") && data.getString("cycle_days").length() > 0) {
                        cycle_days = data.getInteger("cycle_days");
                    } else {
                        return ErrNo.set(433012);
                    }
                } else {
                    cycle_days = cycle_days_list[cycle];
                }


            } else {
                cycle_end = end_time;
            }

        }
        cycle_seconds = RIUtil.dateToStamp(end_time) - RIUtil.dateToStamp(start_time);
        long be = cycle_seconds - (cycle_seconds / 10);
        long nt = RIUtil.dateToStamp(start_time) + be;
        if (data.containsKey("near_time") && data.getString("near_time").length() > 0) {
            near_time = data.getString("near_time").replace("|", " ");
            ;
        } else {
            near_time = RIUtil.stampToTime(nt);
        }
        //logger.warn(RIUtil.dateToStamp(end_time) + "-" + RIUtil.dateToStamp(start_time) + "=" + cycle_seconds);
        if (cycle_seconds < 0) {
            logger.error("startTime bigger than endTime");
            return ErrNo.set(433012);
        }


        if (data.containsKey("users") && data.getString("users").length() > 0) {
            users = data.getString("users");

        }
        if (data.containsKey("groups") && data.getString("groups").length() > 0) {
            groups = data.getString("groups");
        }

        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
        } else {
            return ErrNo.set(433012);
        }
        if (users.length() == 0 && groups.length() == 0) {
            users = create_user;
        }
        if (data.containsKey("isWechat") && data.getString("isWechat").length() > 0) {
            isWechat = data.getInteger("isWechat");
        }
        if (data.containsKey("isMsg") && data.getString("isMsg").length() > 0) {
            isMsg = data.getInteger("isMsg");
        }
        if (data.containsKey("isFA") && data.getString("isFA").length() > 0) {
            isFA = data.getInteger("isFA");
        }
        if (data.containsKey("isSub") && data.getString("isSub").length() > 0) {
            isSub = data.getString("isSub");
        }

        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            if (type == 22222) {
                String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
                String sql = "select count(id) as count from task where create_time like '%" + today + "%' and " +
                        "isdelete=1 and isPublish=1 and type=22222";
                int count = mysql.query_count(sql) + 1;
                today = today.replace("-", "");
                if (count < 10) {
                    title = today + "00" + count;

                } else if (count >= 10 && count < 100) {
                    title = today + "0" + count;
                } else {
                    title = today + count;
                }

            }
            String sqls = "insert task (title,content," + "level,type,start_time,end_time,cycle,cycle_days," +
                    "cycle_end,users,groups,status,isPublish,create_user," + "create_time,isdelete," +
                    "cycle_seconds," +
                    "comment_type,comment_select," + "isWechat,isMsg," + "accessory,img,link," + "near_time,isFA," +
                    "isSub," +
                    "unit,source)" + "values(encode('" + title + "','" + RIUtil.enTitle + "')," + "" + "encode('" + content + "','" + RIUtil.enContent + "')," + "'" + level + "','" + type + "','" + start_time + "'," + "" + "" + "'" + end_time + "','" + cycle + "','" + cycle_days + "'," + "'" + cycle_end + "','" + users + "','" + groups + "','" + status + "'," + "'" + isPublish + "','" + create_user + "'," + "'" + create_time + "','" + isdelete + "'," + "'" + cycle_seconds + "','" + comment_type + "'," + "'','" + isWechat + "'," + "'" + isMsg + "'," + "'" + accessory + "'," + "'" + img + "','" + link + "','" + near_time + "'," + "'" + isFA + "','" + isSub + "','" + unit + "',source) ";
            mysql.update(sqls);
            sqls = "select id from task where decode( title,'" + RIUtil.enTitle + "')='" + title + "' and create_time"
                    + "='" + create_time + "' and isdelete=1";
            List<JSONObject> list = mysql.query(sqls);
            String id = list.get(0).getString("id");

            UserLog userLog = new UserLog();
            userLog.log(mysql, data.getString("create_user"), "创建提醒事项：" + title, userLog.TYPE_OPERATE, ip);


            MakeCycleTask(id, 10);
            publishNotice_Task("");

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(433011);
        } finally {
            InfoModelPool.putModel(mysql);

        }

        return back;


    }

    private static JSONObject createTask_list(JSONObject d) throws Exception {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String create_user = "";
        try {
            mysql = InfoModelPool.getModel();

            JSONArray dlist = new JSONArray();
            int cycle = 0;
            String sqlsta = " start_time like '%" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + "%'";
            if (d.containsKey("cycle") && d.getString("cycle").length() > 0) {
                cycle = d.getInteger("cycle");
                if (cycle != 1) {
                    sqlsta = "start_time<='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "'";
                }

            }
            if (d.containsKey("data") && d.getString("data").length() > 0) {
                dlist = d.getJSONArray("data");
                String sql = "delete from task where cycle='" + cycle + "' and type=9999999 and isdelete=1 " + " and "
                        + "(isPublish=0 or " + sqlsta + ")";
                logger.warn(sql);
                mysql.update(sql);
                sql = "update task set cycle_end='" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + " 00:00"
                        + ":00' where type='9999999' and cycle='" + cycle + "'";

                mysql.update(sql);

            } else {
                return ErrNo.set(433012);
            }
            if (d.containsKey("opt_user") && d.getString("opt_user").length() > 0) {
                create_user = d.getString("opt_user");
            } else {
                return ErrNo.set(433012);
            }

            String unit = "";
            if (d.containsKey("unit") && d.getString("unit").length() > 0) {
                unit = d.getString("unit");
            }
            for (int i = 0; i < dlist.size(); i++) {
                JSONObject data = dlist.getJSONObject(i);

                String content = "";
                int level = 0;
                int type = 0;
                String start_date = "";
                String start_time = "";
                String end_time = "";

                int cycle_days = 0;
                long cycle_seconds = 0;
                String cycle_end = "";
                String users = "";
                String groups = "";
                int status = 0;
                int isPublish = 0;

                String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                int isdelete = 1;
                int isWechat = 0;
                int isMsg = 0;
                String comment_type = "1";
                String accessory = "";
                String img = "";
                String link = "";
                String near_time = "";
                int isFA = 0;
                String isSub = "0";
                String subType = "";

                if (data.containsKey("content") && data.getString("content").length() > 0) {
                    content = data.getString("content");
                } else {
                    return ErrNo.set(433012);
                }
                if (data.containsKey("level") && data.getString("level").length() > 0) {
                    level = data.getInteger("level");
                }
                if (data.containsKey("type") && data.getString("type").length() > 0) {
                    type = data.getInteger("type");
                } else {
                    return ErrNo.set(433012);
                }
                String id = "";
                if (data.containsKey("id") && data.getString("id").length() > 0) {
                    id = data.getString("id");

                    String sql = "select father_id from task where id='" + id + "'";
                    String father_id = mysql.query_one(sql, "father_id");
                    if (father_id != null && father_id.length() > 0) {
                        sql =
                                "update task " + "set cycle_end='" + new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + " 00:00:00' " + "where father_id='" + father_id + "'";
                        logger.warn(sql);
                        mysql.update(sql);
                        sql = "delete from task " + "where id>='" + id + "' and father_id='" + father_id + "' " +
                                "and type=9999999";
                        mysql.update(sql);
                    } else {
                        sql = "delete from task where id='" + id + "'";
                        mysql.update(sql);
                    }

                }

                if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                    start_time = data.getString("start_time").replace("|", " ");
                } else {
                    return ErrNo.set(433012);
                }

                if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                    end_time = data.getString("end_time").replace("|", " ");
                }
                if (data.containsKey("accessory") && data.getString("accessory").length() > 0) {
                    accessory = data.getString("accessory");
                }
                if (data.containsKey("img") && data.getString("img").length() > 0) {
                    img = data.getString("img");
                }
                if (data.containsKey("link") && data.getString("link").length() > 0) {
                    link = data.getString("link");
                }

                if (data.containsKey("cycle_end") && data.getString("cycle_end").length() > 0) {
                    cycle_end = data.getString("cycle_end").replace("|", " ");
                } else {
                    int year = Integer.parseInt(new SimpleDateFormat("yyyy").format(new Date())) + 2;
                    cycle_end = year + "-12-31 23:59:59";
                }

                if (cycle > 0) {
                    String date_time[] = start_time.split(" ");

                    String date = date_time[0];

                    if (end_time.length() == 0) {
                        end_time = date + " 23:59:59";
                    }
                    if (cycle == 8) {
                        if (data.containsKey("cycle_days") && data.getString("cycle_days").length() > 0) {
                            cycle_days = data.getInteger("cycle_days");
                        } else {
                            return ErrNo.set(433012);
                        }
                    } else {
                        cycle_days = cycle_days_list[cycle];
                    }


                } else {
                    cycle_end = end_time;
                }


                cycle_seconds = RIUtil.dateToStamp(end_time) - RIUtil.dateToStamp(start_time);
                long be = cycle_seconds - (cycle_seconds / 10);
                long nt = RIUtil.dateToStamp(start_time) + be;
                if (data.containsKey("near_time") && data.getString("near_time").length() > 0) {
                    near_time = data.getString("near_time").replace("|", " ");
                    ;
                } else {
                    near_time = RIUtil.stampToTime(nt);
                }
                //logger.warn(RIUtil.dateToStamp(end_time) + "-" + RIUtil.dateToStamp(start_time) + "=" +
                // cycle_seconds);
                if (cycle_seconds < 0) {
                    logger.error("startTime bigger than endTime");
                    return ErrNo.set(433012);
                }


                if (data.containsKey("users") && data.getString("users").length() > 0) {
                    users = data.getString("users");

                }
                if (data.containsKey("groups") && data.getString("groups").length() > 0) {
                    groups = data.getString("groups");
                }


                if (users.length() == 0 && groups.length() == 0) {
                    users = create_user;
                }
                if (data.containsKey("isWechat") && data.getString("isWechat").length() > 0) {
                    isWechat = data.getInteger("isWechat");
                } else {
                    if (type == 9999999) {
                        isWechat = 1;
                    }
                }
                if (data.containsKey("isMsg") && data.getString("isMsg").length() > 0) {
                    isMsg = data.getInteger("isMsg");
                }
                if (data.containsKey("isFA") && data.getString("isFA").length() > 0) {
                    isFA = data.getInteger("isFA");
                }
                if (data.containsKey("subType") && data.getString("subType").length() > 0) {
                    subType = data.getString("subType");
                }

                String sqls = "insert task (title,content,level,type,start_time,end_time,cycle,cycle_days," +
                        "cycle_end,users,groups,status,isPublish,create_user," + "create_time,isdelete," +
                        "cycle_seconds," + "comment_type,comment_select,isWechat,isMsg,accessory,img,link," +
                        "near_time,isFA,isSub," + "unit)" + "values(encode(' ','" + RIUtil.enTitle + "'),encode('" + content + "','" + RIUtil.enContent + "')," + "'" + level + "','" + type + "','" + start_time + "','" + end_time + "','" + cycle + "'," + "'" + cycle_days + "'," + "'" + cycle_end + "','" + users + "'," + "'" + groups + "','" + status + "','" + isPublish + "','" + create_user + "'," + "'" + create_time + "','" + isdelete + "'," + "'" + cycle_seconds + "','" + comment_type + "','','" + isWechat + "'," + "'" + isMsg + "'," + "'" + accessory + "','" + img + "','" + link + "','" + near_time + "','" + isSub + "','" + isFA + "','" + unit + "') ";
                mysql.update(sqls);
                sqls =
                        "select id from task where content=encode('" + content + "','" + RIUtil.enContent + "') and " +
                                "create_user='" + create_user + "' and create_time='" + create_time + "' and " +
                                "isdelete=1 order " + "by id " + "limit 1";
                List<JSONObject> list = mysql.query(sqls);
                id = list.get(0).getString("id");

                UserLog userLog = new UserLog();
                userLog.log(mysql, data.getString("create_user"), "创建社区提醒事项", userLog.TYPE_OPERATE, ip);


                MakeCycleTask(id, 10);
                publishNotice_Task("");


            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(433011);
        } finally {
            InfoModelPool.putModel(mysql);

        }

        return back;


    }

    private static JSONObject getTask(JSONObject data) throws Exception {

        String content = "";
        String level = "";
        String type = "";
        String start_time_start = "";
        String start_time_end = "";
        String end_time_start = "";
        String end_time_end = "";
        String cycle = "";
        String cycle_days = "";
        String cycle_end_start = "";
        String cycle_end_end = "";
        String user_id = "";
        String group_id = "";
        String status = "";
        String isPublish = "1";
        String id = "";
        String father_id = "";
        String create_user = "";

        String order = " start_time ";
        String sql = "";
        if (data.containsKey("content") && data.getString("content").length() > 0) {
            content = data.getString("content");
            sql =
                    sql + " (decode( content,'" + RIUtil.enContent + "') like'%" + content + "%' or decode( title,'" + RIUtil.enTitle + "') like '%" + content + "%') and ";
        }
        if (data.containsKey("level") && data.getString("level").length() > 0) {
            level = data.getString("level");
            sql = sql + " level='" + level + "' and ";
        }
        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
            sql = sql + " type='" + type + "' and ";
        }
        if (data.containsKey("start_time_start") && data.getString("start_time_start").length() > 0 && data.containsKey("start_time_end") && data.getString("start_time_end").length() > 0) {
            start_time_start = data.getString("start_time_start").replace("|", " ");
            start_time_end = data.getString("start_time_end").replace("|", " ");
            sql = sql + " (start_time>='" + start_time_start + "' and start_time<='" + start_time_end + "') and";
        }

        if (data.containsKey("end_time_start") && data.getString("end_time_start").length() > 0 && data.containsKey(
                "end_time_end") && data.getString("end_time_end").length() > 0) {
            end_time_start = data.getString("end_time_start").replace("|", " ");
            end_time_end = data.getString("end_time_end").replace("|", " ");
            sql = sql + " (end_time<='" + end_time_start + "' and end_time>='" + end_time_end + "') and";
        }

        if (data.containsKey("cycle") && data.getString("cycle").length() > 0) {
            cycle = data.getString("cycle");
            sql = sql + " cycle='" + cycle + "' and ";
        }
        if (data.containsKey("cycle_days") && data.getString("cycle_days").length() > 0) {
            cycle_days = data.getString("cycle_days");
            sql = sql + " cycle_days='" + cycle_days + "' and ";
        }
        if (data.containsKey("cycle_end_start") && data.getString("cycle_end_start").length() > 0 && data.containsKey("cycle_end_end") && data.getString("cycle_end_end").length() > 0) {
            cycle_end_start = data.getString("cycle_end_start").replace("|", " ");
            cycle_end_end = data.getString("cycle_end_end").replace("|", " ");
            sql = sql + " (cycle_end>='" + cycle_end_start + "' and cycle_end<='" + cycle_end_end + "') and";
        }

        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");

        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
            sql = sql + " create_user ='" + create_user + "' and ";
        }

        if (data.containsKey("status") && data.getString("status").length() > 0) {
            status = data.getString("status");
            if (user_id.length() > 0) {
                if (status.equals("task_0")) {
                    sql = sql + "accepter like '%" + user_id + "%' and";
                } else {
                    sql = sql + "finished like '%" + user_id + "%' and";
                }
            } else {

                sql = sql + " status='" + status + "' and ";
            }

        } else {
            sql = sql + "(accepter like '%" + user_id + "%' or finished like '%" + user_id + "%' )and";
        }
        if (data.containsKey("isPublish") && data.getString("isPublish").length() > 0) {
            isPublish = data.getString("isPublish");
        }
        sql = sql + " isPublish='" + isPublish + "' and ";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
            sql = sql + " id='" + id + "' and ";
        }
        if (data.containsKey("father_id") && data.getString("father_id").length() > 0) {
            father_id = data.getString("father_id");
            sql = sql + " father_id='" + father_id + "' and ";
        }


        if (data.containsKey("order") && data.getString("order").length() > 0) {
            order = data.getString("order").replace("|", " ");
        }
        String opt_user = "";
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        }
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "select *,decode(title,'" + RIUtil.enTitle + "') as title,decode(content,'" + RIUtil.enContent +
                            "') as "
                            + "content,unit from task where 1=1 and " + sql + " isdelete=1 order by " + order;
            //logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql, opt_user));

            } else {
                back.put("data", list);
                back.put("count", 0);
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(433014);
        } finally {
            InfoModelPool.putModel(mysql);

        }

        return back;

    }

    private static JSONObject getTaskList(JSONObject data) throws Exception {

        String content = "";
        String level = "";
        String type = "";
        String start_time_start = "";
        String start_time_end = "";
        String end_time_start = "";
        String end_time_end = "";
        String cycle = "";
        String cycle_days = "";
        String cycle_end_start = "";
        String cycle_end_end = "";
        String user_id = "";
        String group_id = "";
        String status = "";
        String isPublish = "1";
        String id = "";
        String father_id = "";
        String create_user = "";
        int limit = 20;
        int page = 1;
        String order = " start_time ";
        String sql = "";
        String unit = "";

        if (data.containsKey("content") && data.getString("content").length() > 0) {
            content = data.getString("content");
            sql =
                    sql + " (decode( content,'" + RIUtil.enContent + "') like'%" + content + "%' or decode( title,'" + RIUtil.enTitle + "') like '%" + content + "%') and ";
        }
        if (data.containsKey("level") && data.getString("level").length() > 0) {
            level = data.getString("level");
            sql = sql + " level='" + level + "' and ";
        }
        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
            sql = sql + " type='" + type + "' and ";
        } else {
            sql = sql + " type!=9999999 and type!=22222 and";
        }
        if (data.containsKey("start_time_start") && data.getString("start_time_start").length() > 0) {

            start_time_start = data.getString("start_time_start").replace("|", " ");

            sql = sql + " start_time>='" + start_time_start + "'  and ";
        }
        if (data.containsKey("start_time_end") && data.getString("start_time_end").length() > 0) {
            start_time_end = data.getString("start_time_end").replace("|", " ");
            sql = sql + "  start_time<='" + start_time_end + "' and ";
        }
        if (data.containsKey("end_time_start") && data.getString("end_time_start").length() > 0) {

            end_time_start = data.getString("end_time_start").replace("|", " ");

            sql = sql + " end_time>='" + end_time_start + "'  and";

        }
        if (data.containsKey("end_time_end") && data.getString("end_time_end").length() > 0) {
            end_time_end = data.getString("end_time_end").replace("|", " ");
            sql = sql + " end_time<='" + end_time_end + "' and";
        }

        if (data.containsKey("cycle") && data.getString("cycle").length() > 0) {
            cycle = data.getString("cycle");
            sql = sql + " cycle='" + cycle + "' and ";
        }
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            sql = sql + " unit='" + unit + "' and ";
        }
        if (data.containsKey("cycle_days") && data.getString("cycle_days").length() > 0) {
            cycle_days = data.getString("cycle_days");
            sql = sql + " cycle_days='" + cycle_days + "' and ";
        }
        if (data.containsKey("cycle_end_start") && data.getString("cycle_end_start").length() > 0) {

            cycle_end_start = data.getString("cycle_end_start").replace("|", " ");

            sql = sql + " cycle_end>='" + cycle_end_start + "' and ";
        }
        if (data.containsKey("cycle_end_end") && data.getString("cycle_end_end").length() > 0) {
            cycle_end_end = data.getString("cycle_end_end").replace("|", " ");
            sql = sql + " cycle_end<='" + cycle_end_end + "' and ";
        }

        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");

        }
        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
            sql = sql + " create_user ='" + create_user + "' and ";
        }

        if (data.containsKey("status") && data.getString("status").length() > 0) {
            status = data.getString("status");
            if (user_id.length() > 0) {
                if (status.equals("0")) {
                    sql = sql + "(accepter like '%" + user_id + "%' or checked like '%" + user_id + "%') and";
                } else {
                    sql = sql + " finished like '%" + user_id + "%' and";
                }
            } else {

                sql = sql + " status='" + status + "' and ";
            }

        } else {
            sql = sql + "(accepter like '%" + user_id + "%' or finished like '%" + user_id + "%' )and";
        }
        if (data.containsKey("isPublish") && data.getString("isPublish").length() > 0) {
            isPublish = data.getString("isPublish");
            sql = sql + " isPublish='" + isPublish + "' and ";
        }
        sql = sql + " isPublish='" + isPublish + "' and ";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
            sql = sql + " id='" + id + "' and ";
        }
        if (data.containsKey("father_id") && data.getString("father_id").length() > 0) {
            father_id = data.getString("father_id");
            sql = sql + " father_id='" + father_id + "' and ";
        }
        if (data.containsKey("limit") && data.getString("limit").length() > 0) {
            limit = data.getInteger("limit");

        }
        if (data.containsKey("page") && data.getString("page").length() > 0) {
            page = data.getInteger("page");

        }

        if (data.containsKey("order") && data.getString("order").length() > 0) {
            order = data.getString("order").replace("|", " ");
        }

        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "select id,decode( title,'" + RIUtil.enTitle + "') as title ," + "decode( content,'" + RIUtil.enContent + "') as content,create_user,start_time,end_time," + "level,type,accepter,isPublish,status,checked,finished,unit " + "from task where 1=1 and " + sql + " isdelete=1 order by " + order + "  limit " + limit + " offset " + limit * (page - 1);
            // logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfoList(list, mysql, ""));
                sqls = "select count(id) as count from task where 1=1 and " + sql + " isdelete=1";

                back.put("count", mysql.query_count(sqls));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(433014);
        } finally {
            InfoModelPool.putModel(mysql);

        }
        // logger.warn(back.toString());
        return back;

    }

    private static List<JSONObject> RelaInfo(List<JSONObject> list, InfoModelHelper mysql, String user) throws Exception {
        //logger.warn(list.toString());
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String id = one.getString("id");
            String sql = "select start_time,end_time,near_time from task_accepter where rela_id='" + id + "' and " +
                    "user_id='" + user + "' and isdelete=1";
            List<JSONObject> accs = mysql.query(sql);
            if (accs.size() > 0) {
                one.put("start_time", accs.get(0).getString("start_time"));
                one.put("end_time", accs.get(0).getString("end_time"));
                one.put("near_time", accs.get(0).getString("near_time"));
            }
            ///users
            String users = one.getString("users");
            if (users.length() > 3) {
                JSONArray userList = RIUtil.UseridToNames(RIUtil.StringToList(users));
                one.put("users", userList);
            }
            // logger.warn(one.getString("users"));
            //groups
            String groups = one.getString("groups");
            // logger.warn(groups);
            if (groups.length() > 0) {
                JSONArray groupList = RIUtil.GroupsToName(RIUtil.StringToList(groups), mysql);
                one.put("groups", groupList);
            }
            // logger.warn(one.getString("groups"));
            //comments
            if (one.getInteger("comment_type") == 1) {
                one.put("comment_text", NoticeController.GetCommentText(one.getString("id"), mysql, 2, 1));
                one.put("comment_selects", "");
            } else if (one.getInteger("comment_type") == 2) {
                one.put("comment_text", NoticeController.GetCommentText(one.getString("id"), mysql, 2, 2));
                one.put("comment_selects", NoticeController.GetCommentSelect(one.getString("id"), mysql, 2));
            }
            JSONArray subs = NoticeController.GetCommentText(one.getString("id"), mysql, 2, 3);
            JSONArray submits = new JSONArray();
            if (subs.size() > 0) {
                for (int s = 0; s < subs.size(); s++) {
                    JSONObject sone = subs.getJSONObject(s);
                    String comment = sone.getString("comment");
                    String comms[] = comment.split(",");
                    JSONArray comments = new JSONArray();
                    for (int c = 0; c < comms.length; c++) {
                        sql = "select file_name from upload where id='" + comms[c] + "'";
                        String fileName = mysql.query_one(sql, "file_name");
                        JSONObject cone = new JSONObject();
                        if (fileName != null && fileName.length() > 0) {
                            cone.put("file_id", comms[c]);
                            sql = "select file_path from upload where id='" + comms[c] + "'";
                            String filePath = mysql.query_one(sql, "file_path");
                            cone.put("file_path", filePath);
                            cone.put("file_name", fileName);
                            comments.add(cone);
                        } else {
                            cone.put("file_id", comms[c]);
                            cone.put("file_name", "");
                            comments.add(cone);
                        }

                    }

                    sone.put("comment", comments);
                    submits.add(sone);
                }
            }
            one.put("submits", submits);


            //create_user
            String create_user = one.getString("create_user");
            try {
                one.put("create_user", RIUtil.users.get(create_user));
            } catch (Exception ex) {

            }
            //finished
            String finished = one.getString("finished");
            JSONArray fin = new JSONArray();
            if (finished.length() > 0) {
                fin = RIUtil.UseridToNames(RIUtil.StringToList(finished));

            }
            one.put("finished", fin);
            //finished_line
            sql = "select task_type,user_id,opt_user,cancel_type,time from task_log where (task_type=2 or " +
                    "cancel_type=2) and  task_id='" + id + "'";
            List<JSONObject> finishs = mysql.query(sql);
            JSONArray finishlist = new JSONArray();
            for (int c = 0; c < finishs.size(); c++) {
                JSONObject oneC = new JSONObject();
                JSONObject cone = finishs.get(c);
                String opt_user = cone.getString("opt_user");
                String user_id = cone.getString("user_id");
                String time = cone.getString("time");
                String task_type = cone.getString("task_type");
                String cancel_type = cone.getString("cancel_type");
                JSONObject cuser = RIUtil.users.get(opt_user);
                if (user_id.equals(opt_user)) {
                    oneC.put("opt_user", cuser.toString());
                    oneC.put("user_id", cuser);
                } else {
                    oneC.put("opt_user", cuser.toString());
                    cuser = RIUtil.users.get(user_id);
                    oneC.put("user_id", cuser);
                }
                oneC.put("time", time);
                oneC.put("task_type", task_type);
                oneC.put("cancel_type", cancel_type);
                finishlist.add(oneC);

            }
            one.put("finished_line", finishlist);


            //accepter
            String accepter = one.getString("accepter");
            //logger.warn("accepter->" + accepter);
            JSONArray acc = new JSONArray();
            if (accepter.length() > 0) {
                acc = RIUtil.UseridToNames(RIUtil.StringToList(accepter));
            }
            one.put("accepter", acc);

            //checked_line
            sql = "select task_type,user_id,opt_user,cancel_type,time from task_log where (task_type=1 or " +
                    "cancel_type=1) and  task_id='" + id + "'";
            List<JSONObject> checks = mysql.query(sql);
            JSONArray checklist = new JSONArray();
            for (int c = 0; c < checks.size(); c++) {
                JSONObject oneC = new JSONObject();
                JSONObject cone = checks.get(c);
                String opt_user = cone.getString("opt_user");
                String user_id = cone.getString("user_id");
                String time = cone.getString("time");
                String task_type = cone.getString("task_type");
                String cancel_type = cone.getString("cancel_type");
                try {
                    JSONObject cuser = RIUtil.users.get(opt_user);

                    if (user_id.equals(opt_user)) {
                        oneC.put("opt_user", cuser.toString());
                        oneC.put("user_id", cuser);
                    } else {
                        oneC.put("opt_user", cuser.toString());
                        cuser = RIUtil.users.get(user_id);
                        oneC.put("user_id", cuser);
                    }
                } catch (Exception ex) {

                }
                oneC.put("time", time);
                oneC.put("task_type", task_type);
                oneC.put("cancel_type", cancel_type);
                //logger.warn(oneC.getString("opt_user"));
                //logger.warn(oneC.toString());
                checklist.add(oneC);

            }

            one.put("checked_line", checklist);

            //checked
            String checked = one.getString("checked");
            JSONArray ck = new JSONArray();
            if (checked.length() > 0) {
                ck = RIUtil.UseridToNames(RIUtil.StringToList(checked));

            }
            one.put("checked", ck);

            //level
            String level = one.getString("level");
            one.put("level_id", level);


            String color = "";
            if (level.length() > 0) {
                try {
                    color = RIUtil.dicts.get(level).getString("color");
                    level = RIUtil.dicts.get(level).getString("dict_name");
                } catch (Exception ex) {

                }

            }
            one.put("level", level);
            one.put("color", color);
            //type
            String type = one.getString("type");
            one.put("type_id", type);
            if (type.equals("9999999")) {
                type = "社区工作清单";
            } else if (type.length() > 0) {
                type = RIUtil.RealDictNames(RIUtil.StringToList(type));


            }

            one.put("type", type);
            //accessory
            String accessory = one.getString("accessory");
            JSONArray access = new JSONArray();
            if (accessory.length() > 0) {

                HashMap<String, String> accesss = RIUtil.StringToList(accessory);
                for (Map.Entry<String, String> a : accesss.entrySet()) {
                    String d = a.getKey();
                    String name = RIUtil.IdToName(d, mysql, "file_name", "upload");
                    JSONObject aone = new JSONObject();
                    aone.put("file_id", d);
                    aone.put("file_name", name);
                    access.add(aone);
                }
            }
            one.put("accessory", access);
            //img
            String img = one.getString("img");
            JSONArray imgs = new JSONArray();
            if (img.length() > 0) {

                HashMap<String, String> imgss = RIUtil.StringToList(img);
                for (Map.Entry<String, String> a : imgss.entrySet()) {
                    String d = a.getKey();
                    String name = RIUtil.IdToName(d, mysql, "file_name", "upload");
                    JSONObject aone = new JSONObject();
                    aone.put("file_id", d);
                    aone.put("file_name", name);
                    String path = RIUtil.IdToName(d, mysql, "file_path", "upload");
                    aone.put("file_path", path);
                    imgs.add(aone);
                }
            }
            one.put("img", imgs);
            if (one.containsKey("unit")) {
                String unit = one.getString("unit");
                one.put("unit_name", RIUtil.RealDictNames(RIUtil.StringToList(unit)));
            } else {
                one.put("unit_name", "");
            }
            back.add(one);
        }

        return back;
    }


    private static List<JSONObject> RelaInfoList(List<JSONObject> list, InfoModelHelper mysql, String userid) throws Exception {
        //logger.warn(list.toString());
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String id = one.getString("id");

            //create_user
            String create_user = one.getString("create_user");
            one.put("create_user", RIUtil.users.get(create_user));

            //accepter
            String accepter = one.getString("accepter");
            //logger.warn("accepter->" + accepter);
            JSONArray acc = new JSONArray();

            if (accepter.length() > 0) {
                acc = RIUtil.UseridToNames(RIUtil.StringToList(accepter));
            }
            one.put("accepter", acc);

            //level
            String level = one.getString("level");

            String color = "";
            try {
                if (level.length() > 0) {
                    color = RIUtil.dicts.get(level).getString("color");
                    level = RIUtil.dicts.get(level).getString("name");

                }
            } catch (Exception e) {

            }
            one.put("level", level);
            one.put("color", color);
            //type
            String type = one.getString("type");
            String checked = "";
            if (one.containsKey("checked")) {
                checked = one.getString("checked");
                one.put("checked", RIUtil.UseridToNames(RIUtil.StringToList(checked)));
            } else {
                one.put("checked", new ArrayList<>());
            }
            if (type.equals("9999999")) {
                try {

                    acc.addAll(RIUtil.UseridToNames(RIUtil.StringToList(checked)));
                    if (acc.size() > 0) {
                        one.put("unfinished", acc);
                    } else {
                        one.put("unfinished", new ArrayList<>());
                    }
                    String finished = one.getString("finished");
                    if (finished != null && finished.length() > 0) {
                        one.put("finished", RIUtil.UseridToNames(RIUtil.StringToList(finished)));
                    } else {
                        one.put("finished", new ArrayList<>());
                    }
                } catch (Exception ex) {
                    logger.warn("9999999");
                }
            }
            if (type.equals("9999999")) {
                type = "社区工作清单";
            } else if (type.equals("22222")) {
                type = "指令核查";
            } else if (type.length() > 0) {
                try {
                    type = RIUtil.dicts.get(type).getString("name");
                } catch (Exception ex) {

                }

            }

            one.put("type", type);

            if (userid.length() > 1) {
                String sql = "select time from task_turning where task_id='" + id + "' and user_id='" + userid + "'";
                List<JSONObject> tt = mysql.query(sql);
                if (tt.size() > 0) {
                    one.put("finish_time", tt.get(0).getString("time"));
                }
            }
            if (one.containsKey("unit")) {
                String unit = one.getString("unit");
                one.put("unit_name", RIUtil.RealDictNames(RIUtil.StringToList(unit)));
            } else {
                one.put("unit_name", "");
            }

            back.add(one);
        }


        return back;
    }

    private static JSONObject deleteTask(JSONObject data) throws Exception {
        String id = "";
        String opt_user = "";
        int isAll = 0;

        JSONObject back = ErrNo.set(0);
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(433017);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(433017);
        }
        if (data.containsKey("is_all") && data.getString("is_all").length() > 0) {
            isAll = data.getInteger("is_all");
        }
        InfoModelHelper mysql = null;
        HashMap<String, String> uu = new HashMap<>();
        try {
            mysql = InfoModelPool.getModel();

            if (isAll == 0) {
                String sql = "update task " + "set isdelete=2,delete_time='" + new SimpleDateFormat("yyyy-MM-dd " +
                        "HH:mm:ss").format(new Date()) + "',delete_user='" + opt_user + "'" + " where id='" + id + "'";
                mysql.update(sql);
                sql =
                        "update task_accepter " + "set isdelete=2,delete_time='" + new SimpleDateFormat("yyyy-MM-dd " + "HH" + ":mm:ss").format(new Date()) + "',delete_user='" + opt_user + "' " + "where rela_id= " + "'" + id + "'";
                mysql.update(sql);
            } else {
                String sql = "select father_id from task where id='" + id + "'";
                List<JSONObject> list = mysql.query(sql);
                if (list.size() > 0) {
                    String father_id = list.get(0).getString("father_id");
                    if (father_id == null || father_id.length() == 0) {
                        father_id = id;
                    }

                    sql =
                            "update task " + "set isdelete=2,delete_time='" + new SimpleDateFormat("yyyy-MM-dd " +
                                    "HH" +
                                    ":mm:ss").format(new Date()) + "'," + "delete_user='" + opt_user + "' where " +
                                    "(father_id='" + father_id + "' or id>='" + id + "') ";

                    mysql.update(sql);

                    sql = "update task_accepter " + "set isdelete=2,delete_time='" + new SimpleDateFormat("yyyy-MM-dd"
                            + " HH:mm:ss").format(new Date()) + "',delete_user='" + opt_user + "' " + "where rela_id "
                            + "in"
                            + " " + "(select id from task where (father_id='" + father_id + "' or id>='" + id + "'))";
                    mysql.update(sql);
                    logger.warn(sql);
                    mysql.update(sql);
                    sql = "select end_time from task where father_id='" + father_id + "' and isdelete=1 order by " +
                            "end_time desc limit 1";
                    list = mysql.query(sql);
                    if (list.size() > 0) {
                        String end_time = list.get(0).getString("end_time");
                        sql = "update task set cycle_end='" + end_time + "' where father_id='" + father_id + "' " +
                                "and" + " " + "isdelete=1";
                        mysql.update(sql);
                    }
                }
            }
            String sql =
                    "select accepter,decode(title,'" + RIUtil.enTitle + "') as title,type from task where id='" + id + "'";
            List<JSONObject> list = mysql.query(sql);
            String accepters = list.get(0).getString("accepter");
            String title = list.get(0).getString("title");
            String type = list.get(0).getString("type");
            int source = 2;
            if (type.equals("22222")) {
                title = "指令核查" + title;
                source = 4;
            }
            if (title.length() > 10) {
                title = title.substring(0, 10);
            }


            wechatMsgTemp.createDingMsg(id, title + " 已撤回", opt_user, source, accepters, mysql, id);
            UserLog userLog = new UserLog();
            userLog.log(mysql, data.getString("opt_user"), "删除提醒事项：" + id, userLog.TYPE_OPERATE, ip);
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(433018);
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return back;
    }


    public static void MakeCycleTask(String id, int count) {
        InfoModelHelper mysql = null;
        String sql = "";
        String title = "";
        String content = "";
        int level = 0;
        int type = 0;
        String start_time = "";
        String end_time = "";
        int cycle = 0;
        int cycle_days = 0;
        long cycle_seconds = 0;
        String father_id = "";
        String cycle_end = "";
        String users = "";
        String groups = "";
        String accepter = "";
        int status = 0;
        int isPublish = 0;
        String create_user = "";
        String create_time = "";
        int isdelete = 1;
        int isWeChat = 0;
        int isMsg = 0;
        int isSub = 0;
        int isFA = 0;
        String near_time = "";
        String checked = "";
        String source = "";
        try {

            mysql = InfoModelPool.getModel();
            sql = "select *,decode( title,'" + RIUtil.enTitle + "') as title,decode(content,'" + RIUtil.enContent +
                    "') as content from task where id='" + id + "' and isdelete=1 and cycle>0";

            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                JSONObject one = list.get(0);
                title = one.getString("title");
                content = one.getString("content");
                level = one.getInteger("level");
                type = one.getInteger("type");
                start_time = one.getString("start_time");
                long start_time_long = RIUtil.dateToStamp(start_time);
                end_time = one.getString("end_time");
                father_id = one.getString("father_id");
                if (father_id.length() == 0) {
                    father_id = id;
                }

                cycle = one.getInteger("cycle");
                cycle_days = one.getInteger("cycle_days");
                cycle_seconds = one.getLong("cycle_seconds");

                cycle_end = one.getString("cycle_end");
                near_time = one.getString("near_time");
                long nearend = RIUtil.dateToStamp(near_time) - RIUtil.dateToStamp(start_time);
                users = one.getString("users");
                groups = one.getString("groups");
                accepter = one.getString("accepter");
                checked = one.getString("checked");
                isWeChat = one.getInteger("isWechat");
                isMsg = one.getInteger("isMsg");
                status = 0;
                isPublish = 0;
                create_user = one.getString("create_user");

                isdelete = one.getInteger("isdelete");
                isFA = one.getInteger("isFA");
                isSub = one.getInteger("isSub");
                source = one.getString("source");


                int mark = 0;
                while (start_time_long < RIUtil.dateToStamp(cycle_end)) {
                    // logger.warn(start_time + "->" + end_time);
                    if (cycle != 4 && cycle != 7) {
                        start_time = RIUtil.GetNextDateTime(start_time, cycle_days);


                    } else if (cycle == 4) {//每月
                        String d[] = start_time.split(" ");
                        String date = d[0];
                        String time = d[1];
                        String[] days = date.split("-");
                        int month = Integer.parseInt(days[1]);
                        int day = Integer.parseInt(days[2]);
                        int year = Integer.parseInt(days[0]);
                        if (day > 28) {
                            day = 28;
                        }
                        if (month < 12) {
                            month = month + 1;
                        } else {
                            month = 1;
                            year = year + 1;
                        }
                        String monthStr = "";
                        if (month < 10) {
                            monthStr = "0" + month;
                        } else {
                            monthStr = String.valueOf(month);
                        }
                        String dayStr = "";
                        if (day < 10) {
                            dayStr = "0" + day;
                        } else {
                            dayStr = String.valueOf(day);
                        }
                        start_time = year + "-" + monthStr + "-" + dayStr + " " + time;


                    } else if (cycle == 7)//每年
                    {
                        String d[] = start_time.split(" ");
                        String date = d[0];
                        String time = d[1];
                        String[] days = date.split("-");
                        int month = Integer.parseInt(days[1]);
                        int day = Integer.parseInt(days[2]);
                        int year = Integer.parseInt(days[0]);
                        start_time = (year + 1) + "-" + days[1] + "-" + days[2] + " " + time;
                    }
                    start_time_long = RIUtil.dateToStamp(start_time);
                    if (start_time_long < RIUtil.dateToStamp(cycle_end)) {
                        long end_time_long = start_time_long + cycle_seconds;
                        end_time = RIUtil.stampToTime(end_time_long);
                        long near_time_long = start_time_long + nearend;
                        near_time = RIUtil.stampToTime(near_time_long);
                        create_time = RIUtil.stampToTime(System.currentTimeMillis());
                        logger.warn(start_time + "->" + end_time + "->" + near_time);
                        id = String.valueOf(UUID.randomUUID());
                        String sqls = "insert task (father_id,title,content,level,type,start_time,end_time," +
                                "cycle,cycle_days," + "cycle_end,users,groups,status,isPublish,create_user," +
                                "create_time," + "isdelete,cycle_seconds,accepter,isWechat," + "isMsg,near_time,isFA," +
                                "isSub,checked,source)" + "values('" + father_id + "',encode('" + title + "','" + RIUtil.enTitle + "')," + "encode('" + content + "','" + RIUtil.enContent + "')," + "'" + level + "','" + type + "','" + start_time + "','" + end_time + "','" + cycle + "'," + "'" + cycle_days + "'," + "'" + cycle_end + "','" + users + "','" + groups + "','" + status + "'," + "'" + isPublish + "','" + create_user + "'," + "'" + create_time + "','" + isdelete + "'," + "'" + cycle_seconds + "','" + accepter + "','" + isWeChat + "'," + "'" + isMsg + "'," + "'" + near_time + "','" + isFA + "','" + isSub + "','" + checked + "','" + source + "') ";

                        mysql.update(sqls);
                        if (count != -1) {//全部
                            mark++;
                            if (mark > count) {
                                break;
                            }
                        }
                    }
                }


            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));

        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    public static void publishNotice_Task(String token) {
        logger.warn("--PUBLISH.NOTICE--");

        InfoModelHelper mysql = null;

        try {
            String after5 = RIUtil.stampToTime(System.currentTimeMillis() + 1000 * 60 * 5);
            mysql = InfoModelPool.getModel();

            String sqls =
                    "select a.id,a.create_user,a.notice_time,a.is_notice,a.reading,a.readed,a.groups,a.users,a" +
                            ".comment_type,a.comment_select,a.isTop,a.isNew,a.top_date," + "a.new_date,a.type,a" +
                            ".isWechat,isMsg,a" +
                            ".create_time,a.isBrief,a.img,a.isAll,a.mark," + "a.accessory,a.link,a.check_time,a" +
                            ".cycle,a.cycle_days," +
                            "a.cycle_seconds,a" + ".cycle_end,a.father_id,a.label,a.unit,a.source,decode(a.title," +
                            "'" + RIUtil.enTitle + "') as title,decode(a.content,'" + RIUtil.enContent + "')as " +
                            "content,a.subType from " + "notice" + "  a where is_notice=0 and isdelete=1 and " +
                            "notice_time<='" + after5 + "'";
            logger.warn(sqls);
            List<JSONObject> list = mysql.query(sqls);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    String groups = one.getString("groups");
                    String users = one.getString("users");
                    int isWechat = one.getInteger("isWechat");
                    int isMsg = one.getInteger("isMsg");
                    String readed = one.getString("readed");
                    int isBrief = one.getInteger("isBrief");
                    String unit = one.getString("unit");
                    String title = one.getString("title");
                    String link = one.getString("link");
                    logger.warn(link);
                    //logger.warn(readed);
                    HashMap<String, String> ed = RIUtil.StringToList(readed);
                    HashMap<String, String> uu = new HashMap<String, String>();
                    if (groups.length() > 0) {
                        uu.putAll(RIUtil.GroupsToUsers(groups, mysql));

                    }
                    if (users.length() > 0) {
                        uu.putAll(RIUtil.StringToList(users));

                    }
                    if (uu.size() == 0) {
                        for (Map.Entry<String, JSONObject> uone : RIUtil.users.entrySet()) {
                            String uid = uone.getKey();
                            JSONObject us = uone.getValue();
                            if (us.getString("unit").equals(unit) && !uid.equals("1")) {
                                uu.put(uid, "");
                            }

                        }
                    }


                    if (isWechat == 2 || isWechat == 0 || isMsg == 2 || isMsg == 0) {
                        for (Map.Entry<String, String> d : ed.entrySet()) {
                            String edid = d.getKey();
                            uu.remove(edid);
                        }


                    } else if (isWechat == 1 || isMsg == 1) {
                        readed = "";
                        sqls = "delete from comment where notice_id='" + id + "' and source=1";
                        mysql.update(sqls);
                        isBrief = 0;
                    }
                    // logger.warn(uu.toString());

                    sqls =
                            "update notice set reading='" + RIUtil.HashToList(uu) + "',is_notice=1,readed='" + readed + "'," + "isBrief='" + isBrief + "' where id='" + id + "'";
                    mysql.update(sqls);


                    logger.warn("ischat->" + isWechat);
                    if (isWechat == 1) {
//                        one.put("reading", RIUtil.HashToList(uu));
//                        logger.warn(one.toString());
//
//                        TestMsgLine send = new TestMsgLine();
//                        String url = TNOAConf.get("HttpServ", "notice_url") + id;
//                        logger.warn(url);
//                        send.sendMSG("您有一条通知通报，请查收:" + title + "-->" + url, RIUtil.HashToList(uu));
                    }

                    if (link.length() > 1) {
                        String[] links = link.split(",");
                        for (int l = 0; l < links.length; l++) {
                            String sql = "select rela_id from upload where id='" + links[l] + "'";
                            String rela_id = mysql.query_one(sql, "rela_id");


                            String url = TNOAConf.get("HttpServ", "online_url") + "share";
                            logger.warn(url);
                            try {
                                JSONObject det = new JSONObject();
                                det.put("token", token);
                                det.put("fileId", rela_id);
                                det.put("sfzhList", RIUtil.HashToList(uu));
                                logger.warn(det.toString());
                                OkHttpClient client = new OkHttpClient().newBuilder().build();

                                MediaType mediaType = MediaType.parse("application/json");

                                RequestBody body = RequestBody.create(mediaType, det.toString());


                                Request ret = new Request.Builder().url(url).method("POST", body).addHeader("Content" +
                                        "-Type", "application/json").build();

                                Response response = client.newCall(ret).execute();

                                String back = response.body().string();
                                logger.warn(back + "-->");


                            } catch (Exception ex) {
                                logger.error(Lib.getTrace(ex));
                            }


                        }
                    }


                }
            }

            logger.warn("--PUBLISH.TASK--");
            sqls = "select *,decode( title,'" + RIUtil.enTitle + "') as title,decode(content,'" + RIUtil.enContent +
                    "') as content,type from task where isPublish=0 and isdelete=1 and start_time<='" + after5 + "'";
            list = mysql.query(sqls);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("id");
                    String groups = one.getString("groups");
                    String type = one.getString("type");
                    logger.warn(groups);
                    String users = one.getString("users");
                    String create_user = one.getString("create_user");
                    int isWechat = one.getInteger("isWechat");
                    int isMsg = one.getInteger("isMsg");
                    String finished = one.getString("finished");
                    String checked = one.getString("checked");
                    String unit = one.getString("unit");
                    int isBrief = one.getInteger("isBrief");
                    HashMap<String, String> ck = RIUtil.StringToList(checked);
                    HashMap<String, String> ed = RIUtil.StringToList(finished);
                    HashMap<String, String> accepter = new HashMap<>();
                    if (users.length() > 0) {
                        accepter.putAll(RIUtil.StringToList(users));
                    }
                    //  logger.warn(accepter.toString());
                    if (groups.length() > 0) {
                        accepter.putAll(RIUtil.GroupsToUsers(groups, mysql));
                    }
                    if (users.length() == 0 && groups.length() == 0) {
                        accepter.putAll(RIUtil.StringToList(create_user));
                    }

                    if (accepter.size() == 0) {
                        for (Map.Entry<String, JSONObject> uone : RIUtil.users.entrySet()) {
                            String uid = uone.getKey();
                            JSONObject us = uone.getValue();
                            if (us.getString("unit").equals(unit)) {
                                accepter.put(uid, "");
                            }

                        }
                    }

                    if (isWechat == 2 || isWechat == 0 || isMsg == 2 || isMsg == 0) {
                        for (Map.Entry<String, String> d : ed.entrySet()) {
                            String edid = d.getKey();
                            accepter.remove(edid);
                        }
                        for (Map.Entry<String, String> d : ck.entrySet()) {
                            String edid = d.getKey();
                            accepter.remove(edid);
                        }


                    } else if (isWechat == 1 || isMsg == 1) {
                        finished = "";
                        checked = "";
                        sqls = "delete from comment where notice_id='" + id + "' and source=2";
                        mysql.update(sqls);
                        sqls = "delete from task_log where task_id='" + id + "'";
                        mysql.update(sqls);
                        isBrief = 0;

                    }


                    List<String> accepters = RIUtil.HashToList(accepter);
                    if (type.equals("9999999")) {
                        checked = accepters.toString();
                        accepters = new ArrayList<String>();
                    }

                    sqls =
                            "update task set accepter='" + accepters + "',finished='" + finished + "',checked='" + checked + "',isBrief='" + isBrief + "' where id='" + id + "'";
                    mysql.update(sqls);
                    if (!type.equals("9999999")) {
                        one.put("accepter", accepters);
                    } else {
                        one.put("accepter", checked);
                    }
                    for (int a = 0; a < accepters.size(); a++) {
                        sqls =
                                "insert task_turning (task_id,user_id,time,status)values('" + id + "','" + accepters.get(a) + "','" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "',1)";
                        mysql.update(sqls);
                    }

                    if (isWechat == 1 || isWechat == 2) {
                        wechatMsgTemp.createMessage_task(one, mysql, isWechat, type);
                    }
                    if (isMsg == 1 || isMsg == 2) {
                        wechatMsgTemp.sendMsg(one, mysql, isMsg, 2);
                    }
                    //添加人员分表
                    sqls = "delete from task_accepter where rela_id='" + id + "'";
                    mysql.update(sqls);

                    for (Map.Entry<String, String> o : accepter.entrySet()) {

                        sqls = "INSERT INTO `task_accepter` ( `rela_id`, `user_id`, `status`, `start_time`, " +
                                "`end_time`, `near_time`) " + "VALUES ( '" + id + "', '" + o.getKey() + "',  '0'," +
                                " '" + one.getString("start_time") + "', '" + one.getString("end_time") + "', '" + one.getString("near_time") + "');";
                        mysql.update(sqls);
                    }


                }
                sqls = "update task set isPublish=1 where isPublish=0 and isdelete=1 and start_time<='" + after5 + "'";
                mysql.update(sqls);
                logger.warn(sqls);
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }

    }


}
