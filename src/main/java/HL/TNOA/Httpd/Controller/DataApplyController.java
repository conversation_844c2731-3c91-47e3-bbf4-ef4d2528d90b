package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@RestController
public class DataApplyController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/data_apply"})
    @PassToken
    public JSONObject get_data_apply(TNOAHttpRequest request) throws Exception {
        logger.warn("data_apply--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_data_apply")) {
                return getDataApply(data);
            } else if (opt.equals("create_data_apply")) {
                return createDataApply(data, request.getRemoteAddr());
            } else if (opt.equals("update_data_apply")) {
                return updateDataApply(data, request.getRemoteAddr());
            } else if (opt.equals("delete_data_apply")) {
                return deleteDataApply(data, request.getRemoteAddr());
            } else {
                return ErrNo.set(null, 2, "OPT错误");
            }
        } else {
            return ErrNo.set(null, 2, "OPT错误");
        }
    }

    //******CREATE*******
    private JSONObject createDataApply(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String apply_name = "";
            String ips = "";
            String url = "";
            String opt = "";

            String unit = "";
            String use_count = "";
            String day_count = "100";
            String limits = "20";
            String token = "";
            String apply_file = "";
            String isStart = "1";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "1";
            if (data.containsKey("apply_name") && data.getString("apply_name").length() > 0) {
                apply_name = data.getString("apply_name");
            } else {
                return ErrNo.set(null, 2, "缺少参数：apply_name");
            }
            if (data.containsKey("ips") && data.getString("ips").length() > 0) {
                ips = data.getString("ips");
            } else {
                return ErrNo.set(null, 2, "缺少参数：ips");
            }
            if (data.containsKey("url") && data.getString("url").length() > 0) {
                url = data.getString("url");
            } else {
                return ErrNo.set(null, 2, "缺少参数：url");
            }
            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
            } else {
                return ErrNo.set(null, 2, "缺少参数：opt");
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");

            } else {
                return ErrNo.set(null, 2, "缺少参数：unit");
            }

            if (data.containsKey("day_count") && data.getString("day_count").length() > 0) {
                day_count = data.getString("day_count");
            }
            if (data.containsKey("limits") && data.getString("limits").length() > 0) {
                limits = data.getString("limits");
            }

            if (data.containsKey("apply_file") && data.getString("apply_file").length() > 0) {
                apply_file = data.getString("apply_file");
            }

            token = String.valueOf(UUID.randomUUID());
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                return ErrNo.set(null, 2, "缺少参数：opt_user");
            }
            String sqls = "insert data_apply (apply_name,ips,url,opt,unit,use_count,day_count,limits," +
                    "token,apply_file,isStart,create_time,create_user,isdelete) values ('" + apply_name + "','" + ips + "','" + url + "','" + opt + "','" + unit + "','" + use_count + "','" + day_count + "','" + limits + "','" + token + "','" + apply_file + "','" + isStart + "','" + create_time + "','" + create_user + "','" + isdelete + "')";
            mysql.update(sqls);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private JSONObject getDataApply(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String query = "";

            String unit = "";

            String isStart = "";
            String create_time_start = "";
            String create_time_end = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("query") && data.getString("query").length() > 0) {
                query = data.getString("query");
                sql =
                        sql + "( apply_name like '%" + query + "%' or ips like '%" + query + "%' or url like '%" + query + "%' " +
                                "or opt like '%" + query + "%') and ";
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " unit='" + unit + "' and ";
            }

            if (data.containsKey("isStart") && data.getString("isStart").length() > 0) {
                isStart = data.getString("isStart");
                sql = sql + " isStart='" + isStart + "' and ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
                create_time_start = data.getString("create_time_start");
                sql = sql + "create_time>='" + create_time_start + "' and ";
            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end");
                sql = sql + "create_time<='" + create_time_end + "' and ";
            }
            String sqls = "select * from data_apply where 1=1 and " + sql + " isdelete=1  limit " + limit + " offset "
                    + limit * (page - 1);
            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select count(id) as count from data_apply where 1=1 and " + sql + " isdelete=1; ";
                back.put("count", mysql.query_count(sqls));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            one.put("create_user", RIUtil.users1.get(one.getString("create_user")));
            one.put("apply_unit", RIUtil.dicts.get(one.getString("unit")));
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private JSONObject updateDataApply(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String apply_name = "";
            String ips = "";
            String url = "";
            String opt = "";
            String apply_unit = "";
            String unit = "";
            String use_count = "";
            String day_count = "";
            String limits = "";
            String token = "";
            String apply_file = "";
            String isStart = "";
            String opt_user = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(null, 2, "缺少参数：id");
            }

            if (data.containsKey("apply_name") && data.getString("apply_name").length() > 0) {
                apply_name = data.getString("apply_name");
                sql = sql + " apply_name='" + apply_name + "' , ";
            }
            if (data.containsKey("ips") && data.getString("ips").length() > 0) {
                ips = data.getString("ips");
                sql = sql + " ips='" + ips + "' , ";
            }
            if (data.containsKey("url") && data.getString("url").length() > 0) {
                url = data.getString("url");
                sql = sql + " url='" + url + "' , ";
            }
            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                sql = sql + " opt='" + opt + "' , ";
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");

                sql = sql + " unit='" + unit + "' , ";
            }
            if (data.containsKey("use_count") && data.getString("use_count").length() > 0) {
                use_count = data.getString("use_count");
                sql = sql + " use_count='" + use_count + "' , ";
            }
            if (data.containsKey("day_count") && data.getString("day_count").length() > 0) {
                day_count = data.getString("day_count");
                sql = sql + " day_count='" + day_count + "' , ";
            }
            if (data.containsKey("limits") && data.getString("limits").length() > 0) {
                limits = data.getString("limits");
                sql = sql + " limits='" + limits + "' , ";
            }
            if (data.containsKey("token") && data.getString("token").length() > 0) {
                token = data.getString("token");
                sql = sql + " token='" + token + "' , ";
            }
            if (data.containsKey("apply_file") && data.getString("apply_file").length() > 0) {
                apply_file = data.getString("apply_file");
                sql = sql + " apply_file='" + apply_file + "' , ";
            }
            if (data.containsKey("isStart") && data.getString("isStart").length() > 0) {
                isStart = data.getString("isStart");
                sql = sql + " isStart='" + isStart + "' , ";
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(null, 2, "缺少参数：opt_user");
            }
            String sqls = "update data_apply set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private JSONObject deleteDataApply(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(null, 2, "缺少参数：id");
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(null, 2, "缺少参数：opt_user");
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update data_apply set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

}
