package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.wechat.ConnectWeChat;
import com.alibaba.fastjson.JSONObject;
import com.sun.org.apache.xerces.internal.impl.dv.util.Base64;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.spec.AlgorithmParameterSpec;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
public class PhoneNumController {
    private static Logger logger = LoggerFactory.getLogger(PhoneNumController.class);

    @RequestMapping(method = {RequestMethod.POST}, path = {"/phone"})
    @PassToken
    public JSONObject get_phone(TNOAHttpRequest request) throws Exception {
        logger.warn("phone--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject obj = ErrNo.set(0);

        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
        } else {
            return ErrNo.set(446001);
        }
        if (opt.equals("up_info")) {
            return UpInfo(data);
        } else if (opt.equals("get_info")) {
            return GetInfo(data);
        } else {
            return ErrNo.set(446001);
        }


    }

    public JSONObject collide(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        logger.warn("collide: " + data.toJSONString());
        String opt = "";
        if (data.containsKey("opt") && Strings.isNotEmpty(data.getString("opt"))) {
            opt = data.getString("opt");
            if ("collide".equals(opt)) {
                String sql = "select * from information ";
                String where = "";
                if (data.containsKey("") && Strings.isNotEmpty(data.getString(""))) {
                    String start_time = data.getString("start_time");
                    String end_time = data.getString("end_time");
                }
            }
        }
        return ErrNo.set(0);
    }

    private JSONObject GetInfo(JSONObject data) {
        String tele = "";
        String start_time = "";
        String end_time = "";
        int page = 1;
        int limit = 20;
        String s = "";

        if (data.containsKey("tele") && data.getString("tele").length() > 0) {
            tele = data.getString("tele");
            s = s + "tele like '%" + tele + "%' and ";
        }
        if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
            start_time = data.getString("start_time");
            end_time = data.getString("end_time");
            s = s + " (time>='" + start_time + "' and time<='" + end_time + "') and ";
        }
        if (data.containsKey("limit") && data.getString("limit").length() > 0) {
            limit = data.getInteger("limit");
        }
        if (data.containsKey("page") && data.getString("page").length() > 0) {
            page = data.getInteger("page");
        }

        InfoModelHelper mysql = null;
        try {
            JSONObject back = ErrNo.set(0);
            mysql = InfoModelPool.getModel();

            String sql = "select * from information where 1=1 and " + s + "  1=1 order by time desc limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sql);

            back.put("data", list);
            sql = "select count(id) as count from information where 1=1 and " + s + "  1=1 ";
            back.put("count", mysql.query_count(sql));
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(446003);
        }

    }

    private JSONObject UpInfo(JSONObject data) {
        String tele = "";
        String openid = "";
        JSONObject obj = ErrNo.set(0);
        if (data.containsKey("js_code") && data.containsKey("iv") && data.containsKey("encrypted")) {
            String jscode = data.getString("js_code");
            String s[] = ConnectWeChat.GetSession(jscode).split(",");
            String session = s[0];
            openid = s[1];
            logger.warn("key->" + session);

            String encryptedDataEncode = data.getString("encrypted").replace("|", "/");
            logger.warn("en-->" + encryptedDataEncode);
            String ivEncode = data.getString("iv").replace("|", "/");
            logger.warn("iv-->" + ivEncode);

            String back = getPhoneNumber(encryptedDataEncode, session, ivEncode);

            JSONObject b = JSONObject.parseObject(back);
            tele = b.getString("phoneNumber");
            logger.warn(back);
        } else {
            return ErrNo.set(446002);
        }

        String latitude = "";
        String longitude = "";
        if (data.containsKey("latitude") && data.getString("latitude").length() > 0 && data.containsKey("longitude") && data.getString("longitude").length() > 0) {
            latitude = data.getString("latitude");
            longitude = data.getString("longitude");
        }
        InfoModelHelper mysql = null;

        try {
            mysql = InfoModelPool.getModel();
            String sql = "insert information(tele,lat,lon,time,open_id)values('" + tele + "','" + latitude + "','" + longitude + "','" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "','" + openid + "')";
            mysql.update(sql);
            logger.warn(sql);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(446003);
        }
        return obj;
    }

    private static String getPhoneNumber(String encryptedData, String sessionKey, String ivA) {
        try {

            byte[] encData = Base64.decode(encryptedData);
            byte[] iv = Base64.decode(ivA);
            byte[] key = Base64.decode(sessionKey);

            AlgorithmParameterSpec ivSpec = new IvParameterSpec(iv);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            //System.out.println(cipher.doFinal(encData));
            return new String(cipher.doFinal(encData), "UTF-8");


        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return null;
        }

    }

   /* public static void main(String[] args) {
        String en = "IUR7o/d/ZHuqYF9ULWBfudFgmibcYH0oC2ZP4htC/77ty6LzVkdtVF9QrDFTTknD4cRYOw/GdRAGhU8lQNLZ+cRS4sgkPP2ig3DHsEvdO1SmEJzf2ZddiMHxq2jrYchNPnT+2lxrX3Qehr2ozmWsA2rLBLdB2NnA11alXwd4ydQZtV5LAA+MwkJlD5dpBHq4ubcJD/aq+XPfO3XYNoKL8w==";
        String key = "79S/18FL5v29gu6p6A2dsw==";
        String iv = "KYodQHSARPQ0grRJFRzz7A==";
        String back = getPhoneNumber(en, key, iv);
    }*/
}

