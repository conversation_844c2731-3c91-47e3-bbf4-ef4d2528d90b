package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.InfoModel.OracleModelHelper;
import HL.TNOA.Lib.InfoModel.OracleModelPool;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static HL.TNOA.Httpd.Controller.StaticRhZfController.init;

@RestController
public class DisputeController {


    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/dispute"})
    @PassToken
    public JSONObject get_dispute(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        InfoModelHelper mysql = null;
        try {


            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("get_tjy")) {
                    return getTjy(data);
                } else if (opt.equals("create_tjy")) {
                    return createTjy(data);
                } else if (opt.equals("delete_tjy")) {
                    return deleteTjy(data);
                } else if (opt.equals("update_tjy")) {
                    return updateTjy(data);
                } else if (opt.equals("get_dispute")) {
                    return getDispute(data);
                } else if (opt.equals("create_dispute")) {
                    return createDispute(data);
                } else if (opt.equals("update_dispute")) {
                    return updateDispute(data);
                } else if (opt.equals("stat_dispute")) {
                    return statDispute(data);
                } else if (opt.equals("delete_dispute")) {
                    return deleteDispute(data);
                } else {
                    return ErrNo.set(601009);
                }
            } else {
                return ErrNo.set(601009);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(601009);
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    private JSONObject deleteDispute(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora = null;

        String ids = "";

        try {
            ora = new OracleHelper("ora_hl");
            if (data.containsKey("ids") && data.getString("ids").length() > 0) {
                ids = data.getString("ids");
                String[] idArr = ids.split(",");
                for (int i = 0; i < idArr.length; i++) {
                    String id = idArr[i];
                    String sqls = "update MDJF_GT set IS_DELETE = 2 where JJBH = '" + id + "'";
                    ora.update(sqls);
                }
            } else {
                return ErrNo.set(602002);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(602001);
        } finally {
            if (ora != null) {
                ora.close();
            }
        }
        return back;
    }

    private JSONObject statDispute(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora = null;
        InfoModelHelper mysql = null;

        String sqls = "";
        String subSql = "";
        String timeSql = "";
        String page_sql = "";

        List<JSONObject> resList = new ArrayList<>();
        JSONObject exportJson = new JSONObject();
        List<JSONObject> pcsExportJson = new ArrayList<>();
        List<JSONObject> fjExportJson = new ArrayList<>();
        //23 分局   25 派出所
        String type = "23";
        String fj = "";
        String name = "";
        String startTime = "";
        String endTime = "";
        String unit = "";
        int count = -1;
        int limit = 20;
        int page = 1;
        int isExp = -1;

        try {
            ora = new OracleHelper("ora_hl");
            mysql = InfoModelPool.getModel();
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
//                subSql += " and id like '" + fj.substring(0, 6) + "%' ";
            }
            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");
                subSql += " and dict_name like '%" + name + "%' ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }
            if (data.containsKey("startTime") && data.getString("startTime").length() > 0) {
                startTime = data.getString("startTime");
                timeSql += " and a.CJSJ >= '" + startTime + "' ";
            }
            if (data.containsKey("endTime") && data.getString("endTime").length() > 0) {
                endTime = data.getString("endTime");
                timeSql += " and a.CJSJ <= '" + endTime + "' ";
            }
            if (data.containsKey("isExp")) {
                isExp = data.getIntValue("isExp");
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject done = RIUtil.dicts.get(unit);
                String father_id = done.getString("father_id");
                try {
                    type = done.getString("type");
                } catch (Exception ex) {
                    type = "21";
                }


                if (type.equals("25") || type.equals("26")) {
                    subSql += " and (id like '%" + unit.substring(0, 8) + "%') ";
                } else if (type.equals("23")) {
                    subSql += " and (id like '%" + unit.substring(0, 6) + "%') ";
                } else if (type.equals("22")) {
                    subSql += " and (id like '%" + unit.substring(0, 4) + "%') ";
                } else if (type.equals("24")) {
                    subSql += " and id like '" + father_id.substring(0, 6) + "%' ";
                } else if (type.equals("28")) {
                    unit = unit.substring(0, 6);
                    subSql += " and id like '" + unit + "%' ";
                }
            }


            page_sql = " limit " + limit + " offset " + limit * (page - 1);

            if (!type.equals("")) {
                if (type.equals("22") || type.equals("21") || isExp == 1) {
                    //分局
                    count = mysql.query_count("select count(id) as count from dict where type = '23' and isdelete = " + "'1' " + subSql);
                    //如果导出则不分页
                    if (isExp == 1) {
                        page_sql = "";
                    }
                    sqls = "select * from dict where type = '23' and isdelete = '1' " + subSql + page_sql;
                    List<JSONObject> fjArr = mysql.query(sqls);
                    for (int i = 0; i < fjArr.size(); i++) {
                        JSONObject fjJson = fjArr.get(i);
                        String JGMC = fjJson.getString("dict_name");
                        String JGDM = fjJson.getString("id");
                        String JG_sql = " and b.CHJDW_GAJGJGDM LIKE '" + JGDM.substring(0, 6) + "%' ";
                        //x调解纠纷数x  已登记数
                        sqls = "select count(a.JJBH) as count from MDJF_GT a join DSJ_JQ b on a.JJBH = b.JJBH " +
                                "where (TJJG='1' OR TJJG='2' ) and (b.CJJG = '46-09' or b.cjjg='46-18') and a" +
                                ".IS_DELETE = 1 " + JG_sql + timeSql;
                        int jfCount = ora.query_count(sqls);
                        //未登记数
                        sqls = "select count(a.JJBH) as count from MDJF_GT a join DSJ_JQ b on a.JJBH = b.JJBH " +
                                "where TJJG is null and (b.CJJG = '46-09' or b.cjjg='46-18') and a.IS_DELETE = 1 " + JG_sql + timeSql;
                        int wdjs = ora.query_count(sqls);
                        //先期处置调解警情数
                        sqls = "select count(a.JJBH) as count from MDJF_GT a join DSJ_JQ b on a.JJBH = b.JJBH " +
                                "where TJJG = '1' and CZZT = '3' and (b.CJJG = '46-09' or b.cjjg='46-18') and a" +
                                ".IS_DELETE = 1 " + JG_sql + timeSql;
                        int xqczCount = ora.query_count(sqls);
                        //专职调解员数
                        sqls = "select count(SFZHM) as count from MDJF_TJY " + " where ISDELETE = 1 and RYLX = " +
                                "'131-01' " + " and JGBH like '" + JGDM.substring(0, 6) + "%' ";
                        int tjyCount = ora.query_count(sqls);
                        //总纠纷数
                        sqls = "select count(a.JJBH) as count from MDJF_GT a join DSJ_JQ b on a.JJBH = b.JJBH " +
                                "where 1=1 and (b.CJJG = '46-09' or b.cjjg='46-18')and  a.IS_DELETE = 1 " + JG_sql + timeSql;
                        int allJfCount = ora.query_count(sqls);

                        //调解成功数
                        sqls = "select count(a.JJBH) as count from MDJF_GT a join DSJ_JQ b on a.JJBH = b.JJBH " +
                                "where TJJG = '1' and (b.CJJG = '46-09' or b.cjjg='46-18') and a.IS_DELETE = 1 " + JG_sql + timeSql;
                        int tjcgCount = ora.query_count(sqls);

                        JSONObject js = new JSONObject();
                        js.put("JGMC", JGMC);
                        js.put("JGDM", JGDM);
                        js.put("jfCount", jfCount);
                        js.put("xqczCount", xqczCount);
                        js.put("tjyCount", tjyCount);
                        js.put("allJfCount", allJfCount);
                        js.put("tjcgCount", tjcgCount);
                        js.put("wdjs", wdjs);


                        //计算调解率
                        if (allJfCount != 0) {
                            double tjl = (double) jfCount / allJfCount * 100;
                            String tjlPercent = String.format("%.000f%%", tjl);
                            js.put("tjlPercent", tjlPercent);
                        } else {
                            js.put("tjlPercent", "0%");
                        }

                        //计算调解成功率
                        if (jfCount != 0) {
                            double tjcgl = (double) tjcgCount / jfCount * 100;
                            String tjcglPercent = String.format("%.000f%%", tjcgl);
                            js.put("tjcglPercent", tjcglPercent);
                        } else {
                            js.put("tjcglPercent", "0%");
                        }

                        resList.add(js);
                        if (isExp == 1) {
                            fjExportJson.add(js);
                        }
                    }
                }
                if (type.equals("25") || type.equals("26") || type.equals("28") || type.equals("23") || isExp == 1) {
                    //派出所
                    count = mysql.query_count("select count(id) as count from dict where type = '25' and isdelete = " + "'1' " + subSql);
                    //如果导出则不分页
                    if (isExp == 1) {
                        page_sql = "";
                    }
                    sqls = "select * from dict where type = '25' " + subSql + page_sql;
                    List<JSONObject> pcsArr = mysql.query(sqls);
                    for (int i = 0; i < pcsArr.size(); i++) {
                        JSONObject pcsJson = pcsArr.get(i);
                        String JGMC = pcsJson.getString("dict_name");
                        String JGDM = pcsJson.getString("id");
                        String JG_sql = " and b.pcs = '" + JGDM.substring(0, 8) + "0000'";
                        //调解纠纷数
                        sqls = "select count(a.JJBH) as count from MDJF_GT a join DSJ_JQ b on a.JJBH = b.JJBH " +
                                "where (TJJG = '1' or TJJG = '2') and (b.CJJG = '46-09' or b.cjjg='46-18') and a" +
                                ".IS_DELETE = 1  " + JG_sql + timeSql;
                        int jfCount = ora.query_count(sqls);
                        //未登记数
                        sqls = "select count(a.JJBH) as count from MDJF_GT a join DSJ_JQ b on a.JJBH = b.JJBH " +
                                "where TJJG is null and (b.CJJG = '46-09'or b.cjjg='46-18') and a.IS_DELETE = 1 " + JG_sql + timeSql;
                        int wdjs = ora.query_count(sqls);
                        //先期处置调解警情数
                        sqls = "select count(a.JJBH) as count from MDJF_GT a join DSJ_JQ b on a.JJBH = b.JJBH " +
                                "where TJJG = '1' and CZZT = '3' and (b.CJJG = '46-09' or b.cjjg='46-18') and a" +
                                ".IS_DELETE = 1 " + JG_sql + timeSql;
                        int xqczCount = ora.query_count(sqls);
                        //专职调解员数
                        sqls = "select count(SFZHM) as count from MDJF_TJY " + "where ISDELETE = 1 and RYLX = " +
                                "'131-01' " + " and JGBH like '" + JGDM.substring(0, 8) + "%' ";
                        int tjyCount = ora.query_count(sqls);

                        //总纠纷数
                        sqls = "select count(a.JJBH) as count from MDJF_GT a join DSJ_JQ b on a.JJBH = b.JJBH " +
                                "where 1=1 and (b.CJJG = '46-09' or b.cjjg='46-18') and a.IS_DELETE = 1 " + JG_sql + timeSql;
                        int allJfCount = ora.query_count(sqls);

                        //调解成功数
                        sqls = "select count(a.JJBH) as count from MDJF_GT a join DSJ_JQ b on a.JJBH = b.JJBH " +
                                "where TJJG = '1' and (b.CJJG = '46-09' or b.cjjg='46-18') and a.IS_DELETE = 1 " + JG_sql + timeSql;
                        int tjcgCount = ora.query_count(sqls);

                        JSONObject js = new JSONObject();
                        js.put("JGMC", JGMC);
                        js.put("JGDM", JGDM);
                        js.put("wdjs", wdjs);
                        js.put("jfCount", jfCount);
                        js.put("xqczCount", xqczCount);
                        js.put("tjyCount", tjyCount);
                        js.put("allJfCount", allJfCount);
                        js.put("tjcgCount", tjcgCount);


                        //计算调解率
                        if (allJfCount != 0) {
                            double tjl = (double) jfCount / allJfCount * 100;
                            String tjlPercent = String.format("%.0f%%", tjl);
                            js.put("tjlPercent", tjlPercent);
                        } else {
                            js.put("tjlPercent", "0%");
                        }

                        //计算调解成功率
                        if (jfCount != 0) {
                            double tjcgl = (double) tjcgCount / jfCount * 100;
                            String tjcglPercent = String.format("%.0f%%", tjcgl);
                            js.put("tjcglPercent", tjcglPercent);
                        } else {
                            js.put("tjcglPercent", "0%");
                        }
                        resList.add(js);
                        if (isExp == 1) {
                            pcsExportJson.add(js);
                        }
                    }
                }
            }

            if (isExp != 1) {
                back.put("data", resList);
                back.put("count", count);
            } else {
                exportJson.put("fj", fjExportJson);
                exportJson.put("pcs", pcsExportJson);
                int file_id = exportTemplate2(exportJson).getIntValue("id");
                back.put("file_id", file_id);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(603001);
        } finally {
            ora.close();
            mysql.close();
        }

        return back;
    }

    private JSONObject updateDispute(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora = null;

        String sqls = "";
        String sub_sql = "";

        String JJBH = ""; //接警编号
        String TJJG = ""; //调解结果
        String SFYJ = ""; //是否移交
        String JFLB = ""; //纠纷类别
        String TJND = ""; //调解难度
        String SFDCXY = ""; //是否达成协议
        String SFZZAJ = ""; //是否制作案卷
        String JGBH = ""; //机构编号
        String TJY = ""; //调解员
        String TJQK = ""; //调解情况
        String CJDW = ""; //处警单位
        String CZZT = ""; //处置状态
        String opt_user = "";
        String IS_DELETE = ""; //是否删除  0否1是


        try {
            ora = new OracleHelper("ora_hl");

            if (data.containsKey("JJBH") && data.getString("JJBH").length() > 0) {
                JJBH = data.getString("JJBH");
                sqls = " select count(*) as count from MDJF_GT where JJBH = '" + JJBH + "' ";
                int count = ora.query_count(sqls);
                if (count > 1) {
                    return ErrNo.set(602002);
                }
            } else {
                return ErrNo.set(602002);
            }
            if (data.containsKey("TJJG") && data.getString("TJJG").length() > 0) {
                TJJG = data.getString("TJJG");
                sub_sql += ", TJJG = '" + TJJG + "' ";
            }
            if (data.containsKey("SFYJ") && data.getString("SFYJ").length() > 0) {
                SFYJ = data.getString("SFYJ");
                sub_sql += ", SFYJ = '" + SFYJ + "' ";

                if (SFYJ.equals("1")) {
                    sub_sql += ", CZZT = '2' ";
                }
            }
            if (data.containsKey("JFLB") && data.getString("JFLB").length() > 0) {
                JFLB = data.getString("JFLB");
                sub_sql += ", JFLB = '" + JFLB + "' ";
            }
            if (data.containsKey("TJND") && data.getString("TJND").length() > 0) {
                TJND = data.getString("TJND");
                sub_sql += ", TJND = '" + TJND + "' ";
            }
            if (data.containsKey("SFDCXY") && data.getString("SFDCXY").length() > 0) {
                SFDCXY = data.getString("SFDCXY");
                sub_sql += ", SFDCXY = '" + SFDCXY + "' ";
            }
            if (data.containsKey("SFZZAJ") && data.getString("SFZZAJ").length() > 0) {
                SFZZAJ = data.getString("SFZZAJ");
                sub_sql += ", SFZZAJ = '" + SFZZAJ + "' ";
            }
            if (data.containsKey("JGBH") && data.getString("JGBH").length() > 0) {
                JGBH = data.getString("JGBH");
                sub_sql += ", JGBH = '" + JGBH + "' ";
            }
            if (data.containsKey("TJY") && data.getString("TJY").length() > 0) {
                TJY = data.getString("TJY");
                sub_sql += ", TJY = '" + TJY + "' ";
            }
            if (data.containsKey("CJDW") && data.getString("CJDW").length() > 0) {
                CJDW = data.getString("CJDW");
                sub_sql += ", CJDW = '" + CJDW + "' ";
            }
            if (data.containsKey("TJQK") && data.getString("TJQK").length() > 0) {
                TJQK = data.getString("TJQK");
                sub_sql += ", TJQK = '" + TJQK + "' ";
            }
            if (data.containsKey("CZZT") && data.getString("CZZT").length() > 0) {
                CZZT = data.getString("CZZT");
                if (!sub_sql.contains("CZZT")) {
                    sub_sql += ", CZZT = '" + CZZT + "' ";
                }
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                sub_sql += ", UPDATE_USER = '" + data.getString("opt_user") + "' ";
            }
            if (data.containsKey("is_delete") && data.getString("is_delete").length() > 0) {
                IS_DELETE = data.getString("is_delete");
                sub_sql += ", IS_DELETE = '" + IS_DELETE + "' ";
            }


            if (sub_sql.length() > 0) {
                String createTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                sqls = " update MDJF_GT " + "set CREATE_TIME = '" + createTime + "' " + sub_sql + " where JJBH = '" + JJBH + "' ";
                logger.warn(sqls);
                ora.update(sqls);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(602001);
        } finally {
            if (ora != null) {
                ora.close();
            }
        }
        return back;
    }

    private JSONObject createDispute(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora = null;

        String sqls = "";

        String JJBH = ""; //接警编号
        String TJJG = ""; //调解结果
        String SFYJ = ""; //是否移交
        String JFLB = ""; //纠纷类别
        String TJND = ""; //调解难度
        String SFDCXY = ""; //是否达成协议
        String SFZZAJ = ""; //是否制作案卷
        String JGBH = ""; //机构编号
        String TJY = ""; //调解员
        String TJQK = ""; //调解情况


        try {
            ora = new OracleHelper("ora_hl");

            if (data.containsKey("JJBH") && data.getString("JJBH").length() > 0) {
                JJBH = data.getString("JJBH");
                sqls = " select count(*) as count from MDJF_GT where JJBH = '" + JJBH + "' ";
                int count = ora.query_count(sqls);
                if (count > 1) {
                    return ErrNo.set(602002);
                }
            } else {
                return ErrNo.set(602002);
            }
//            if (data.containsKey("TJJG") && data.getString("TJJG").length() > 0) {
//                TJJG = data.getString("TJJG");
//            }
//            if (data.containsKey("SFYJ") && data.getString("SFYJ").length() > 0) {
//                SFYJ = data.getString("SFYJ");
//            }
//            if (data.containsKey("JFLB") && data.getString("JFLB").length() > 0) {
//                JFLB = data.getString("JFLB");
//            }
//            if (data.containsKey("TJND") && data.getString("TJND").length() > 0) {
//                TJND = data.getString("TJND");
//            }
//            if (data.containsKey("SFDCXY") && data.getString("SFDCXY").length() > 0) {
//                SFDCXY = data.getString("SFDCXY");
//            }
//            if (data.containsKey("SFZZAJ") && data.getString("SFZZAJ").length() > 0) {
//                SFZZAJ = data.getString("SFZZAJ");
//            }
//            if (data.containsKey("JGBH") && data.getString("JGBH").length() > 0) {
//                JGBH = data.getString("JGBH");
//            }
//            if (data.containsKey("JJBH") && data.getString("JJBH").length() > 0) {
//                JJBH = data.getString("JJBH");
//            }
//            if (data.containsKey("TJY") && data.getString("TJY").length() > 0) {
//                TJY = data.getString("TJY");
//            }
//            if (data.containsKey("TJQK") && data.getString("TJQK").length() > 0) {
//                TJQK = data.getString("TJQK");
//            }
            data.remove("opt_user");
            data.remove("opt");
            data.remove("real_opt_user");
            data.remove("optType");
            if (data.containsKey("JJBH") && data.getString("JJBH").length() > 0) {
                String BH = data.getString("JJBH");
                sqls = " select CLJG, JJDW_GAJGJGDM from DSJ_JQ where JJBH = '" + BH + "' ";
                List<JSONObject> jsonObjects = ora.query(sqls);
                if (jsonObjects.size() > 0) {
                    JSONObject relaJs = jsonObjects.get(0);
                    String CJNR = relaJs.getString("CLJG");
                    String CJDW = relaJs.getString("JJDW_GAJGJGDM");
                    data.put("CJNR", CJNR);
                    data.put("CJDW", CJDW);

                    if (CJNR.contains("调解员")) {
                        data.put("CZZT", "3");
                    } else {
                        data.put("CZZT", "2");
                    }
                }
            }
            data.put("CREATE_TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            RIUtil.JsonInsert_ora(data, "MDJF_GT", ora);

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(602001);
        } finally {
            if (ora != null) {
                ora.close();
            }
        }
        return back;
    }

    private JSONObject getDispute(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleModelHelper ora = null;

        String sql = "";
        String subSql = "";
        String markSql = "";
        int count = -1;
        int page = 1;
        int limit = 20;

        String unit = "";
        String startTime = "";
        String endTime = "";
        String bjfs = "";
        String jjbh = "";
        String cjlb = "";
        String sfdj = "";
        String czzt = "";
        String tjjg = "";
        String sfdcxy = "";
        String sfzzaj = "";
        int isMark = 99;

        try {
            ora = OracleModelPool.getModel();

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject done = RIUtil.dicts.get(unit);

                String type = "";
                try {
                    type = done.getString("type");
                } catch (Exception ex) {
                    type = "21";
                }
                String father_id = done.getString("father_id");
                if (father_id.length() < 5) {
                    father_id = done.getString("id");
                }
//                String id = done.getString("id");


                if (type.equals("23")) {
                    subSql = subSql + " and (b.CHJDW_GAJGJGDM like '" + unit.substring(0, 6) + "%') ";
                } else if (type.equals("25") || type.equals("26")) {
                    subSql = subSql + " and (b.CHJDW_GAJGJGDM like '" + unit.substring(0, 8) + "%') ";
                } else if (type.equals("24")) {
                    subSql = subSql + " and b.CHJDW_GAJGJGDM like '" + father_id.substring(0, 6) + "%' ";
                } else if (type.equals("28")) {
                    unit = unit.substring(0, 6);
                    subSql = subSql + " and b.CHJDW_GAJGJGDM like '" + unit + "%' ";
                }
            }

            if (data.containsKey("isMark") && data.getString("isMark").length() > 0) {
                isMark = data.getInteger("isMark");
            }

            String cjlbSql = "";
            if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {
                cjlb = data.getString("cjlb");
                if (cjlb.startsWith("50-")) {
                    String[] lbs = cjlb.split(",");
                    String sq = "";
                    for (int i = 0; i < lbs.length; i++) {
                        String lb = lbs[i];
                        if (lb.endsWith("0000")) {
                            sq = sq + " type50='" + lb + "' or ";
                        } else if (lb.endsWith("00")) {
                            sq = sq + " type52='" + lb + "' or ";
                        } else {
                            sq = sq + " CJLB='" + lb + "' or ";
                        }
                    }

                    cjlbSql = " and (" + sq.substring(0, sq.length() - 3) + ") ";

                } else {

                    isMark = -1;
                    if (cjlb.equals("MG")) {
                        cjlbSql = cjlbSql + " and MG=1 ";
                    } else if (cjlb.equals("WFFZ")) {
                        cjlbSql = cjlbSql + " and CJLB LIKE '50-01%'";
                    } else if (cjlb.equals("ZA")) {
                        cjlbSql = cjlbSql + " and CJLB LIKE '50-02%'";
                    } else if (cjlb.equals("RSDQ")) {
                        cjlbSql = cjlbSql + " and (CJLB= '50-020208' OR CJLB='50-011701')";
                    } else if (cjlb.equals("DQDDC")) {
                        cjlbSql = cjlbSql + " and (CJLB= '50-020209' OR CJLB='50-011709')";
                    } else if (cjlb.equals("HZ")) {
                        cjlbSql = cjlbSql + " and CJLB LIKE '50-04%'";
                    } else if (cjlb.equals("DZ")) {
                        cjlbSql = cjlbSql + "  and (CJLB= '50-011805' OR CJLB='50-011806' OR CJLB='50-011807')";
                    } else if (cjlb.equals("SC")) {
                        cjlbSql = cjlbSql + "  and (CJLB LIKE  '50-0121%' OR CJLB='50-0217%')";
                    } else if (cjlb.equals("SD")) {
                        cjlbSql = cjlbSql + " and CJLB LIKE '50-0215%'";
                    } else if (cjlb.equals("CD")) {
                        cjlbSql = cjlbSql + "  and (CJLB LIKE  '50-0121%' OR CJLB like '50-0217%' or CJLB LIKE " +
                                "'50-0215%')";
                    } else if (cjlb.equals("YX")) {

                    } else if (cjlb.equals("QZ")) {
                        cjlbSql = cjlbSql + " and CJLB LIKE '50-05%'";
                    } else if (cjlb.equals("JB")) {
                        cjlbSql = cjlbSql + " and CJLB LIKE '50-06%'";
                    } else if (cjlb.equals("JF")) {
                        cjlbSql = cjlbSql + " and CJLB LIKE '50-08%'";
                    }
                }

            }

            if (data.containsKey("czzt") && data.getString("czzt").length() > 0) {
                czzt = data.getString("czzt");
                subSql += " and a.czzt = '" + czzt + "' ";
            }
            if (data.containsKey("startTime") && data.getString("startTime").length() > 0) {
                startTime = data.getString("startTime");
                subSql += " and a.CJSJ >= '" + startTime + "' ";
            }
            if (data.containsKey("endTime") && data.getString("endTime").length() > 0) {
                endTime = data.getString("endTime");
                subSql += " and a.CJSJ <= '" + endTime + "' ";
            }
            if (data.containsKey("bjfs") && data.getString("bjfs").length() > 0) {
                bjfs = data.getString("bjfs");
                subSql += " and b.BJFS = '" + bjfs + "' ";
            }
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");
                subSql += " and b.JJBH like '%" + jjbh + "%' ";
            }
            if (data.containsKey("tjjg") && data.getString("tjjg").length() > 0) {
                tjjg = data.getString("tjjg");
                subSql += " and a.TJJG = '" + tjjg + "' ";
            }
            if (data.containsKey("sfdcxy") && data.getString("sfdcxy").length() > 0) {
                sfdcxy = data.getString("sfdcxy");
                subSql += " and a.SFDCXY = '" + sfdcxy + "' ";
            }
            if (data.containsKey("sfzzaj") && data.getString("sfzzaj").length() > 0) {
                sfzzaj = data.getString("sfzzaj");
                subSql += " and a.SFZZAJ = '" + sfzzaj + "' ";
            }


            if (data.containsKey("sfdj") && data.getString("sfdj").length() > 0) {
                sfdj = data.getString("sfdj");
                if (sfdj.equals("1")) {
                    subSql += " and (a.TJJG = '1' or a.TJJG = '2') ";
                } else if (sfdj.equals("2")) {
                    subSql += " and a.TJJG is null  ";
                }
            }

            if (isMark >= 0) {
                if (isMark == 0) {
                    markSql = " and (  SFGLDZ_PDBZ is null or SFGLDZ_PDBZ=0 )  ";
                } else if (isMark == 1) {
                    markSql = " and SFGLDZ_PDBZ=1  ";
                } else if (isMark == 3) {
                    markSql =
                            " and (  SFGLDZ_PDBZ is null or SFGLDZ_PDBZ=0 )  AND  CJSJ01 < to_char(SYSDATE - 7, " +
                                    "'YYYY-MM-DD HH24:MM:SS')  ";
                } else if (isMark == 2) {
                    markSql = " and SFGLDZ_PDBZ=2  ";
                } else if (isMark == 4) {
                    markSql = " and SFGLDZ_PDBZ=4  ";
                } else if (isMark == 5) {
                    markSql = " and SFGLDZ_PDBZ=1 and (SUBSTR(CHJDW_GAJGJGDM,1,8)!=SUBSTR(ZRQ,1,8) and (SUBSTR" +
                            "(CHJDW_GAJGJGDM,6,2)!=16 OR SUBSTR(CHJDW_GAJGJGDM,6,2)!=28)) ";
                }
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            subSql += " and a.IS_DELETE = '1' ";

            sql = "select * from MDJF_GT a join DSJ_JQ b on a.JJBH = b.JJBH " + "where 1=1 and (b.cjjg='46-09' or " + "b.cjjg='46-18')" + subSql + cjlbSql + markSql + " order by a.CJSJ desc " + "OFFSET " + (page - 1) * limit + " ROWS FETCH NEXT " + limit + "ROWS ONLY ";
            logger.warn(sql);
            List<JSONObject> list = ora.query(sql);
            for (JSONObject one : list) {
                if (one.containsKey("JFLB") && one.getString("JFLB").length() > 0) {
                    String JFLB = one.getString("JFLB");
                    JSONObject JFLBJs = RIUtil.dicts.get(JFLB);
                    if (JFLBJs != null) {
                        one.put("JFLBMC", JFLBJs.getString("dict_name"));
                    }
                }
                if (one.containsKey("CJLB") && one.getString("CJLB").length() > 0) {
                    String CJLB = one.getString("CJLB");
                    JSONObject CJLBJs = RIUtil.dicts.get(CJLB);
                    if (CJLBJs != null) {
                        one.put("CJLBMC", CJLBJs.getString("dict_name"));
                    }
                }
                if (one.containsKey("TJND") && one.getString("TJND").length() > 0) {
                    String TJND = one.getString("TJND");
                    JSONObject TJNDJs = RIUtil.dicts.get(TJND);
                    if (TJNDJs != null) {
                        one.put("TJNDMC", TJNDJs.getString("dict_name"));
                    }
                }
                if (one.containsKey("JJDW_GAJGJGDM") && one.getString("JJDW_GAJGJGDM").length() > 0) {
                    String JJDW_GAJGJGDM = one.getString("JJDW_GAJGJGDM");
                    JSONObject JJDW_GAJGJGDMJs = RIUtil.dicts.get(JJDW_GAJGJGDM);
                    if (JJDW_GAJGJGDMJs != null) {
                        one.put("JJJGMC", JJDW_GAJGJGDMJs.getString("dict_name"));
                    }
                }

                if (one.containsKey("CJDW") && one.getString("CJDW").length() > 0) {
                    String CJDW = one.getString("CJDW");
                    JSONObject CJDWJs = RIUtil.dicts.get(CJDW);
                    if (CJDWJs != null) {
                        one.put("TJYCJDWMC", CJDWJs.getString("dict_name"));
                    }
                }
                if (one.containsKey("BJFS") && one.getString("BJFS").length() > 0) {
                    String BJFS = one.getString("BJFS");
                    JSONObject BJFSJs = RIUtil.dicts.get("45-" + BJFS);
                    if (BJFSJs != null) {
                        one.put("BJFSMC", BJFSJs.getString("dict_name"));
                    }
                }

                //处理涉警人员
//                if (one.containsKey("SJRY") && one.getString("SJRY").length() > 0) {
//                    JSONArray sjryList = JSONArray.parseArray(one.getString("SJRY"));
//                    for (int i = 0; i < sjryList.size(); i++) {
//                        JSONObject sjry = sjryList.getJSONObject(i);
//
//                    }
//                }

                //调解员
                if (one.containsKey("TJY") && one.getString("TJY").length() > 0) {
                    String tjy = one.getString("TJY");
                    sql = "select XM,JGBH from MDJF_TJY where SFZHM = '" + tjy + "' and ISDELETE = 1 " + "OFFSET 0 " + "ROWS FETCH NEXT 1 ROWS ONLY";
                    List<JSONObject> tjyList = ora.query(sql);
                    JSONObject tjyJson = new JSONObject();
                    if (tjyList.size() > 0) {
                        tjyJson = tjyList.get(0);
                        JSONObject tjyJs = RIUtil.dicts.get(tjyJson.getString("JGBH"));
                        if (tjyJs != null) {
                            one.put("TJYJGMC", tjyJs.getString("dict_name"));
                        } else {
                            one.put("TJYJGMC", "");
                        }
                        one.put("TJYJGDM", tjyJson.getString("JGBH"));
                        one.put("TJYXM", tjyJson.getString("XM"));
                    }
                }

            }


            sql = "select count(*) as count from  MDJF_GT a join DSJ_JQ b on a.JJBH = b.JJBH " + "where (b.CJJG = " + "'46-09' or b.cjjg='46-18') " + subSql + cjlbSql + markSql + " order by CREATE_TIME desc";
            logger.warn(sql);
            count = ora.query_count(sql);

            back.put("data", list);
            back.put("count", count);

        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(2);
        } finally {
            OracleModelPool.putModel(ora);
        }

        return back;
    }

    private JSONObject updateTjy(JSONObject data) {

        JSONObject back = ErrNo.set(0);
        OracleModelHelper ora = null;

        String subSql = "";
        String sqls = "";

        String unit = "";
        String name = "";
        String idCard = "";
        String tele = "";
        String personType = "";
        String address = "";
        String opt_user = "";
        String updateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

        try {

            ora = OracleModelPool.getModel();


            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                subSql += ", JGBH = '" + unit + "' ";
            }
            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");
                subSql += ", XM = '" + name + "' ";
            }
            if (data.containsKey("idCard") && data.getString("idCard").length() > 0) {
                idCard = data.getString("idCard");
                subSql += ", SFZHM = '" + idCard + "' ";
            } else {
                return ErrNo.set(601003);
            }
            if (data.containsKey("tele") && data.getString("tele").length() > 0) {
                tele = data.getString("tele");
                subSql += ", LXDH = '" + tele + "' ";
            }
            if (data.containsKey("personType") && data.getString("personType").length() > 0) {
                personType = data.getString("personType");
                subSql += ", RYLX = '" + personType + "' ";
            }
            if (data.containsKey("address") && data.getString("address").length() > 0) {
                address = data.getString("address");
                subSql += ", XZZ = '" + address + "' ";
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }


            if (subSql.startsWith(",")) {
                subSql = subSql.substring(1, subSql.length() - 1);
            }
            subSql += ", XGR = '" + opt_user + "' ";
            subSql += ", XGSJ = '" + updateTime + "' ";

            sqls = " update MDJF_TJY set " + subSql + " where SFZHM = '" + idCard + "'";
            logger.warn(sqls);
            ora.update(sqls);

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(601005);
        } finally {
            //ora.close();
            OracleModelPool.putModel(ora);
        }
        return back;
    }

    private JSONObject deleteTjy(JSONObject data) {

        OracleModelHelper ora = null;
        JSONObject back = ErrNo.set(0);
        String sqls = "";
        String opt_user = "";
        String idCard = "";
        String deleteTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());


        try {
            ora = OracleModelPool.getModel();
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }

            if (data.containsKey("idCard") && data.getString("idCard").length() > 0) {
                idCard = data.getString("idCard");
                sqls = "select ISDELETE from MDJF_TJY where ISDELETE = '1' and SFZHM = '" + idCard + "' ";
                List<JSONObject> list = ora.query(sqls);
                if (list.size() == 1) {
                    sqls = " update MDJF_TJY set ISDELETE = '2', SCR = '" + opt_user + "', SCSJ ='" + deleteTime +
                            "'" + " " + "where ISDELETE = '1' and SFZHM = '" + idCard + "'";
                    logger.warn(sqls);
                    ora.update(sqls);
                } else {
                    return ErrNo.set(601007);
                }
            } else {
                return ErrNo.set(601006);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(601008);
        } finally {
            if (ora != null) {
                OracleModelPool.putModel(ora);
            }
        }
        return back;
    }

    private JSONObject createTjy(JSONObject data) {

        OracleModelHelper ora = null;
        JSONObject back = ErrNo.set(0);

        String unit = "";
        String name = "";
        String idCard = "";
        String tele = "";
        String personType = "";
        String address = "";
        String opt_user = "";
        String createTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

        String sqls = "";

        try {
            ora = OracleModelPool.getModel();

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(601002);
            }
            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");
            } else {
                return ErrNo.set(601002);
            }
            if (data.containsKey("idCard") && data.getString("idCard").length() > 0) {
                idCard = data.getString("idCard");
                sqls = " select count(SFZHM) as count from MDJF_TJY where ISDELETE ='1' and SFZHM = '" + idCard + "' ";
                if (ora.query_count(sqls) > 0) {
                    return ErrNo.set(601000);
                }
            } else {
                return ErrNo.set(601002);
            }
            if (data.containsKey("tele") && data.getString("tele").length() > 0) {
                tele = data.getString("tele");
            } else {
                return ErrNo.set(601002);
            }
            if (data.containsKey("personType") && data.getString("personType").length() > 0) {
                personType = data.getString("personType");
            } else {
                return ErrNo.set(601002);
            }
            if (data.containsKey("address") && data.getString("address").length() > 0) {
                address = data.getString("address");
            } else {
                return ErrNo.set(601002);
            }

            sqls = " insert into MDJF_TJY(JGBH, XM, SFZHM, LXDH, RYLX, XZZ, CJSJ, CJR) " + "values('" + unit + "', '" + name + "', '" + idCard + "', '" + tele + "', '" + personType + "', '" + address + "', '" + createTime + "','" + opt_user + "') ";
            logger.warn(sqls);
            ora.update(sqls);

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(601001);
        } finally {
            //ora.close();
            OracleModelPool.putModel(ora);
        }
        return back;
    }

    private JSONObject getTjy(JSONObject data) {

        OracleModelHelper ora = null;
        JSONObject back = ErrNo.set(0);
        int page = 1;
        int limit = 20;
        int count = -1;

        String unit = "";
        String name = "";
        String idCard = "";
        String personType = "";
        String address = "";
        String tele = "";

        String sqls = "";
        String subSql = "";
        List<JSONObject> res = new ArrayList<>();

        try {
            ora = OracleModelPool.getModel();

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject done = RIUtil.dicts.get(unit);

                String type = "";
                try {
                    type = done.getString("type");
                } catch (Exception ex) {
                    type = "21";
                }
                String father_id = done.getString("father_id");
                if (father_id.length() < 5) {
                    father_id = done.getString("id");
                }
//                String id = done.getString("id");


                if (type.equals("23")) {
                    subSql = subSql + " and (JGBH like '" + unit.substring(0, 6) + "%') ";
                } else if (type.equals("25") || type.equals("26")) {
                    subSql = subSql + " and (JGBH='" + unit.substring(0, 8) + "0000') ";
                } else if (type.equals("24")) {
                    subSql = subSql + " and JGBH like '" + father_id.substring(0, 6) + "%' ";
                } else if (type.equals("28")) {
                    unit = unit.substring(0, 6);
                    subSql = subSql + " and JGBH like '" + unit + "%' ";
                }
            }


            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");
                subSql += " and XM like '%" + name + "%' ";
            }
            if (data.containsKey("idCard") && data.getString("idCard").length() > 0) {
                idCard = data.getString("idCard");
                subSql += " and SFZHM like '%" + idCard + "%' ";
            }
            if (data.containsKey("personType") && data.getString("personType").length() > 0) {
                personType = data.getString("personType");
                subSql += " and RYLX = '" + personType + "' ";
            }
            if (data.containsKey("address") && data.getString("address").length() > 0) {
                address = data.getString("address");
                subSql += " and XZZ like '%" + address + "%' ";
            }
            if (data.containsKey("tele") && data.getString("tele").length() > 0) {
                tele = data.getString("tele");
                subSql += " and LXDH like '%" + tele + "%' ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }


            sqls = "select * from MDJF_TJY where ISDELETE = '1' " + subSql + " order by CJSJ desc OFFSET " + (page - 1) * limit + " ROWS FETCH NEXT " + limit + "ROWS ONLY ";
            logger.warn(sqls);
            res = ora.query(sqls);
            for (JSONObject one : res) {
                if (one.containsKey("RYLX") && one.getString("RYLX").length() > 0) {
                    String RYLX = one.getString("RYLX");
                    JSONObject RYLXMC = RIUtil.dicts.get(RYLX);
                    if (RYLXMC != null) {
                        one.put("RYLXMC", RYLXMC.getString("dict_name"));
                    } else {
                        one.put("RYLXMC", "");
                    }
                }
                if (one.containsKey("JGBH") && one.getString("JGBH").length() > 0) {
                    String JGBH = one.getString("JGBH");
                    JSONObject JGMC = RIUtil.dicts.get(JGBH);
                    if (JGMC != null) {
                        one.put("JGMC", JGMC.getString("dict_name"));
                    } else {
                        one.put("JGMC", "");
                    }
                }
            }

            sqls = "select count(SFZHM) as count from MDJF_TJY where ISDELETE = '1' " + subSql;
            count = ora.query_count(sqls);

            back.put("data", res);
            back.put("count", count);

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(601003);
        } finally {
            OracleModelPool.putModel(ora);
        }
        return back;
    }

    private JSONObject exportTemplate2(JSONObject data) {

        String sql = "";
        FileOutputStream fos = null;
        SXSSFWorkbook sxssfWorkbook = null;
        InfoModelHelper info = null;
        try {
            info = InfoModelPool.getModel();
            sxssfWorkbook = new SXSSFWorkbook();

            CellStyle cellStyle = initCellStyle(sxssfWorkbook);

            JSONArray fj = data.getJSONArray("fj");
            JSONArray pcs = data.getJSONArray("pcs");
            final int treeLength1 = getTreeLength(fj);
            final int treeLength2 = getTreeLength(pcs);

            infoSheet(sxssfWorkbook, fj, "分局", treeLength1, cellStyle);
            infoSheet(sxssfWorkbook, pcs, "派出所", treeLength2, cellStyle);

            String FileName = "矛盾公调统计_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";
            String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;
            init(endPath);

            fos = new FileOutputStream(endPath);
            sxssfWorkbook.write(fos);

            String sqls =
                    "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "','" + FileName + "',999)";
            logger.warn(sqls);
            info.update(sqls);
            sql = "select * from upload where file_name='" + FileName + "'";
            List<JSONObject> result = info.query(sql);
            JSONObject xJsonObject = result.get(0);
            int id = xJsonObject.getIntValue("id");

            logger.warn("id->" + id);
            JSONObject back = new JSONObject();
            back.put("id", id);
            /*BashExecutor bash = new BashExecutor();
            String cmd = "cp -r " + endPath + " " + TNOAConf.get("file", "bk_path");
            logger.warn(cmd);
            bash.exec(cmd, -1, true);*/

            String endPoint = "http://10.34.251.34:50101";
            String ak = "Q7D3OUVIRBEZEB4RWVSJ";
            String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
            String bucketName = "obs-qjjc-tyyh";
            ObsServer obsServ = new ObsServer();
            String obsFileName = "hl/" + filePath + FileName;
            System.out.println(obsFileName);
            boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
            logger.warn(obsFileName + "-->" + ret);


            return back;

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(480012);
        } finally {
            InfoModelPool.putModel(info);
            try {
                if (sxssfWorkbook != null) {
                    sxssfWorkbook.dispose();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private static void infoSheet(SXSSFWorkbook sxssfWorkbook, JSONArray arr, String name, int treeLength,
                                  CellStyle cellStyle) {
        // 获取SXSSFWorkbook实例
        Sheet sheet = sxssfWorkbook.createSheet(name);

        Row head = sheet.createRow(0);

        Cell cell = head.createCell(treeLength);
        cell.setCellValue("处警单位");
        cell.setCellStyle(cellStyle);
        Cell cell2 = head.createCell(treeLength + 1);
        cell2.setCellValue("总调解纠纷数");
        cell2.setCellStyle(cellStyle);
        Cell cell3 = head.createCell(treeLength + 2);
        cell3.setCellValue("已调解纠纷数");
        cell3.setCellStyle(cellStyle);
        Cell cell4 = head.createCell(treeLength + 3);
        cell4.setCellValue("调解成功数");
        cell4.setCellStyle(cellStyle);
        Cell cell5 = head.createCell(treeLength + 4);
        cell5.setCellValue("调解率");
        cell5.setCellStyle(cellStyle);
        Cell cell6 = head.createCell(treeLength + 5);
        cell6.setCellValue("调解成功率");
        cell6.setCellStyle(cellStyle);
        Cell cell7 = head.createCell(treeLength + 6);
        cell7.setCellValue("先期处置调解警情数");
        cell7.setCellStyle(cellStyle);
        Cell cell8 = head.createCell(treeLength + 7);
        cell8.setCellValue("专职人民调解员数");
        cell8.setCellStyle(cellStyle);


        int flag = 1;
        for (int i = 0; i < arr.size(); i++) {
            JSONObject obj = arr.getJSONObject(i);
            int r = fillTreeData(sheet, obj, flag, 0, cellStyle);
            flag += r;
        }
    }

    private static int fillTreeData(Sheet sheet, JSONObject object, int rowStart, int colStart, CellStyle cellStyle) {
        String id = object.getString("id");

        // 获取开始行，如果不存在则创建一行
        Row row = sheet.getRow(rowStart) == null ? sheet.createRow(rowStart) : sheet.getRow(rowStart);

//        // 在开始列创建一个单元格
//        Cell cell = row.createCell(colStart);
//
//        // 设置单元格的值为节点名称
//        cell.setCellValue(object.getString("kh_name"));
//        cell.setCellStyle(cellStyle);
//        // 检查节点是否有子节点


        sheet.setColumnWidth(0, 5000);

        Cell cell2 = row.createCell(colStart);
        cell2.setCellValue(object.getString("JGMC"));
        cell2.setCellStyle(cellStyle);
        Cell cell3 = row.createCell(colStart + 1);
        cell3.setCellValue(object.getIntValue("allJfCount"));
        cell3.setCellStyle(cellStyle);
        Cell cell4 = row.createCell(colStart + 2);
        cell4.setCellValue(object.getIntValue("jfCount"));
        cell4.setCellStyle(cellStyle);
        Cell cell5 = row.createCell(colStart + 3);
        cell5.setCellValue(object.getIntValue("tjcgCount"));
        cell5.setCellStyle(cellStyle);
        Cell cell6 = row.createCell(colStart + 4);
        cell6.setCellValue(object.getString("tjlPercent"));
        cell6.setCellStyle(cellStyle);
        Cell cell7 = row.createCell(colStart + 5);
        cell7.setCellValue(object.getString("tjcglPercent"));
        cell7.setCellStyle(cellStyle);
        Cell cell8 = row.createCell(colStart + 6);
        cell8.setCellValue(object.getIntValue("xqczCount"));
        cell8.setCellStyle(cellStyle);
        Cell cell9 = row.createCell(colStart + 7);
        cell9.setCellValue(object.getIntValue("tjyCount"));
        cell9.setCellStyle(cellStyle);

        // 如果没有子节点，那么这个节点只占用一行
        return 1;
    }

    private static int getTreeLength(JSONArray tree) {
        int l = 0;
        int rl = 0;
        for (int i = 0; i < tree.size(); i++) {
            JSONObject o = tree.getJSONObject(i);
//            l = 1;
            rl = Math.max(rl, l);
            if (o.containsKey("next") && o.getJSONArray("next").size() > 0) {
                JSONArray next = o.getJSONArray("next");
                l = 2;
                rl = Math.max(rl, l);
                for (int j = 0; j < next.size(); j++) {
                    JSONObject two = next.getJSONObject(j);
                    if (two.containsKey("next") && two.getJSONArray("next").size() > 0) {
                        JSONArray next2 = two.getJSONArray("next");
                        l = 3;
                        rl = Math.max(rl, l);
                        for (int k = 0; k < next2.size(); k++) {
                            JSONObject three = next2.getJSONObject(k);
                            if (three.containsKey("next") && three.getJSONArray("next").size() > 0) {
                                rl = 4;
                            }
                        }
                    }
                }
            }
        }
        return rl;
    }

    private static CellStyle initCellStyle(SXSSFWorkbook sxssfWorkbook) {
        //************** 样式一 *******************//
        CellStyle cellStyle = sxssfWorkbook.createCellStyle();
        cellStyle.setWrapText(true);
        //对齐方式
        //设置水平对齐方式
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        //设置垂直对齐方式
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //设置边框
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        // 设置字体
        Font font = sxssfWorkbook.createFont();
        font.setFontName("Segoe UI");
        font.setFontHeightInPoints((short) 9);
        font.setBold(true);
        cellStyle.setFont(font);
        //************** 样式一 *******************//
        return cellStyle;
    }
}
