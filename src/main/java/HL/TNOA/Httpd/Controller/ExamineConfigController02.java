package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.User;
import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import static HL.TNOA.Httpd.Controller.StaticRhZfController.init;

@RestController
public class ExamineConfigController02 {
    private Logger logger = LoggerFactory.getLogger(ExamineConfigController02.class);
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/examine_config02"})

    public JSONObject examine_config(TNOAHttpRequest request) throws Exception {
        String opt = "";
        JSONObject data = request.getRequestParams();
        User user = request.getUser();
        logger.warn("examine_config ----> User--->" + user);
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("create_event")) {
                return createEvent(data, request.getRemoteAddr());
            } else if (opt.equals("delete_event")) {
                return deleteEvent(data, request.getRemoteAddr());
            } else if (opt.equals("update_event")) {
                return updateEvent(data, request.getRemoteAddr());
            } else if (opt.equals("get_event")) {
                return getEvent(data, request.getRemoteAddr());
            } else if (opt.equals("create_plan")) {
                return createPlan(data, request.getRemoteAddr());
            } else if (opt.equals("update_plan")) {
                return updatePlan(data, request.getRemoteAddr());
            } else if (opt.equals("get_plan")) {
                return getPlan(data, request.getRemoteAddr());
            } else if (opt.equals("delete_plan")) {
                return deletePlan(data, request.getRemoteAddr());
            } else if (opt.equals("copy_plan")) {
                return copyPlan(data, request.getRemoteAddr());
            } else if (opt.equals("export_plan")) {
                return exportTemplate2(data, request.getRemoteAddr());
            } else if (opt.equals("get_history")) {
                return getHistory(data, request.getRemoteAddr());
            } else if (opt.equals("get_unify_control")) {
                return getUnifyControl(data, request.getRemoteAddr());
            } else if (opt.equals("update_unify_control")) {
                return updateUnifyControl(data, request.getRemoteAddr());
            } else if (opt.equals("update_cache_file")) {
                return updateCacheFile(data, request.getRemoteAddr());
            } else if (opt.equals("get_cache_file")) {
                return getCacheFile(data, request.getRemoteAddr());
            } else if (opt.equals("get_organ_info")) {
                return getOrganInfo(data, request.getRemoteAddr());
            } else {
                return ErrNo.set(480001);
            }
        } else {
            return ErrNo.set(480001);
        }
    }

    private JSONObject exportTemplate2(JSONObject data, String remoteAddr) {

        String planId = "";
        if (data.containsKey("planId") && data.getString("planId").length() > 0) {
            planId = data.getString("planId");
        } else {
            return ErrNo.set(400083);
        }

        String sql = "";
        FileOutputStream fos = null;
        SXSSFWorkbook sxssfWorkbook = null;
        InfoModelHelper info = null;
        try {
            info = InfoModelPool.getModel();
            sxssfWorkbook = new SXSSFWorkbook();

            CellStyle cellStyle = initCellStyle(sxssfWorkbook);

            String s = "select * from kh_plan where id = '" + planId + "'";
            List<JSONObject> query = info.query(s);
            JSONObject object = query.get(0);
            JSONArray fj = object.getJSONArray("fj");
            JSONArray pcs = object.getJSONArray("pcs");
            JSONArray zrq = object.getJSONArray("zrq");
            final int treeLength = getTreeLength(fj);
            final int treeLength2 = getTreeLength(pcs);
            final int treeLength3 = getTreeLength(zrq);

            infoSheet(sxssfWorkbook, fj, "分局", treeLength, cellStyle);
            infoSheet(sxssfWorkbook, pcs, "派出所", treeLength2, cellStyle);
            infoSheet(sxssfWorkbook, zrq, "责任区", treeLength3, cellStyle);

            String FileName = "质态评估方案_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";
            String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;
            init(endPath);

            fos = new FileOutputStream(endPath);
            sxssfWorkbook.write(fos);

            String sqls = "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "'," +
                    "'" + FileName + "',999)";
            logger.warn(sqls);
            info.update(sqls);
            sql = "select * from upload where file_name='" + FileName + "'";
            List<JSONObject> result = info.query(sql);
            JSONObject xJsonObject = result.get(0);
            int id = xJsonObject.getIntValue("id");

            logger.warn("id->" + id);
            JSONObject back = new JSONObject();
            back.put("id", id);

            String endPoint = "http://10.34.251.34:50101";
            String ak = "Q7D3OUVIRBEZEB4RWVSJ";
            String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
            String bucketName = "obs-qjjc-tyyh";
            ObsServer obsServ = new ObsServer();
            String obsFileName = "hl/" + filePath + FileName;
            System.out.println(obsFileName);
            boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
            logger.warn(obsFileName + "-->" + ret);

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(info, opt_user, "考核-exportTemplate2", userlog.TYPE_OPERATE, remoteAddr);

            /*BashExecutor bash = new BashExecutor();
            String cmd = "cp -r " + endPath + " " + TNOAConf.get("file", "bk_path");
            logger.warn(cmd);
            bash.exec(cmd, -1, true);*/
            return back;

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(info);
            try {
                if (sxssfWorkbook != null) {
                    sxssfWorkbook.dispose();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private static CellStyle initCellStyle(SXSSFWorkbook sxssfWorkbook) {
        //************** 样式一 *******************//
        CellStyle cellStyle = sxssfWorkbook.createCellStyle();
        cellStyle.setWrapText(true);
        //对齐方式
        //设置水平对齐方式
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        //设置垂直对齐方式
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //设置边框
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        // 设置字体
        Font font = sxssfWorkbook.createFont();
        font.setFontName("Segoe UI");
        font.setFontHeightInPoints((short) 9);
        font.setBold(true);
        cellStyle.setFont(font);
        //************** 样式一 *******************//
        return cellStyle;
    }

    private static int getTreeLength(JSONArray tree) {
        int l = 0;
        int rl = 0;
        for (int i = 0; i < tree.size(); i++) {
            JSONObject o = tree.getJSONObject(i);
            l = 1;
            rl = Math.max(rl, l);
            if (o.containsKey("next") && o.getJSONArray("next").size() > 0) {
                JSONArray next = o.getJSONArray("next");
                l = 2;
                rl = Math.max(rl, l);
                for (int j = 0; j < next.size(); j++) {
                    JSONObject two = next.getJSONObject(j);
                    if (two.containsKey("next") && two.getJSONArray("next").size() > 0) {
                        JSONArray next2 = two.getJSONArray("next");
                        l = 3;
                        rl = Math.max(rl, l);
                        for (int k = 0; k < next2.size(); k++) {
                            JSONObject three = next2.getJSONObject(k);
                            if (three.containsKey("next") && three.getJSONArray("next").size() > 0) {
                                rl = 4;
                            }
                        }
                    }
                }
            }
        }
        return rl;
    }

    private static void infoSheet(SXSSFWorkbook sxssfWorkbook, JSONArray arr, String name, int treeLength,
                                  CellStyle cellStyle) {
        // 获取SXSSFWorkbook实例
        Sheet sheet = sxssfWorkbook.createSheet(name);

        int c = 0;
        if (treeLength == 2) {
            c = treeLength + 5;
            sheet.setColumnWidth(c, 40 * 256);
        } else if (treeLength == 3) {
            c = treeLength + 6;
            sheet.setColumnWidth(c, 40 * 256);
        } else if (treeLength == 4) {
            c = treeLength + 7;
            sheet.setColumnWidth(c, 40 * 256);
        }
        Row head = sheet.createRow(0);
        for (int i = 1; i <= treeLength; i++) {
            Cell cell = head.createCell(i - 1);
            cell.setCellStyle(cellStyle);
            cell.setCellValue("层级" + i);
        }
        Cell cell = head.createCell(treeLength);
        cell.setCellValue("主管警钟");
        cell.setCellStyle(cellStyle);
        Cell cell2 = head.createCell(treeLength + 1);
        cell2.setCellValue("分管民警");
        cell2.setCellStyle(cellStyle);
        Cell cell3 = head.createCell(treeLength + 2);
        cell3.setCellValue("权重");
        cell3.setCellStyle(cellStyle);
        Cell cell4 = head.createCell(treeLength + 3);
        cell4.setCellValue("加权");
        cell4.setCellStyle(cellStyle);
        Cell cell5 = head.createCell(treeLength + 4);
        cell5.setCellValue("统计方式");
        cell5.setCellStyle(cellStyle);
        Cell cell6 = head.createCell(treeLength + 5);
        cell6.setCellValue("评估规则");
        cell6.setCellStyle(cellStyle);

        int flag = 1;
        for (int i = 0; i < arr.size(); i++) {
            JSONObject obj = arr.getJSONObject(i);
            int r = fillTreeData(sheet, obj, flag, 0, cellStyle);
            flag += r;
        }
    }


    private static int fillTreeData(Sheet sheet, JSONObject object, int rowStart, int colStart, CellStyle cellStyle) {
        String id = object.getString("id");

        // 获取开始行，如果不存在则创建一行
        Row row = sheet.getRow(rowStart) == null ? sheet.createRow(rowStart) : sheet.getRow(rowStart);

        // 在开始列创建一个单元格
        Cell cell = row.createCell(colStart);

        // 设置单元格的值为节点名称
        cell.setCellValue(object.getString("kh_name"));
        cell.setCellStyle(cellStyle);
        // 检查节点是否有子节点
        if (!object.containsKey("next")) {
            //
            JSONObject info = getObjedtById(id);
            Cell cell2 = row.createCell(colStart + 1);
            cell2.setCellValue(info.getString("resp_dept_name"));
            cell2.setCellStyle(cellStyle);
            Cell cell3 = row.createCell(colStart + 2);
            cell3.setCellValue(info.getString("resp_police_name"));
            cell3.setCellStyle(cellStyle);
            Cell cell4 = row.createCell(colStart + 3);
            cell4.setCellValue(info.getString("full_mark"));
            cell4.setCellStyle(cellStyle);
            Cell cell5 = row.createCell(colStart + 4);
            String point = info.getString("point");
            if (point.equals("1")) {
                point = "加分";
            } else if (point.equals("2")) {
                point = "减分";
            } else {
                point = "其他";
            }
            cell5.setCellValue(point);
            cell5.setCellStyle(cellStyle);
            Cell cell6 = row.createCell(colStart + 5);
            String static_type = info.getString("static_type");
            if (static_type.equals("1")) {
                static_type = "自动";
            } else if (static_type.equals("2")) {
                static_type = "手动";
            }
            cell6.setCellValue(static_type);
            cell6.setCellStyle(cellStyle);
            Cell cell7 = row.createCell(colStart + 6);
            cell7.setCellValue(info.getString("pg_rules"));
            cell7.setCellStyle(cellStyle);

            // 如果没有子节点，那么这个节点只占用一行
            return 1;
        } else if (object.getJSONArray("next").size() == 0) {
            return 1;
        } else {
            // 如果有子节点，则需要计算所有子节点占用的行数
            int totalRows = 0;
            int childrenColStart = colStart + 1;

            JSONArray next = object.getJSONArray("next");
            // 对每个子节点进行递归调用

            for (int i = 0; i < next.size(); i++) {
                JSONObject o = next.getJSONObject(i);
                int childRows = fillTreeData(sheet, o, rowStart + totalRows, childrenColStart, cellStyle);
                // 将子节点占用的行数累加到总行数中
                totalRows += childRows;
            }

            // 如果子节点占用的行数大于1，则需要合并单元格
            if (totalRows > 1) {
                // 合并单元格
                sheet.addMergedRegion(new CellRangeAddress(rowStart, rowStart + totalRows - 1, colStart, colStart));
            }
            // 返回占用的总行数
            return totalRows;
        }
    }

    private static JSONObject getObjedtById(String id) {
        InfoModelHelper mysql = null;
        JSONObject back = null;
        try {
            mysql = InfoModelPool.getModel();
            back = RIUtil.kh_configs.get(id);

            try {
                String resp_dept_name = RIUtil.dicts.get(back.getString("resp_dept")).getString("dict_name");
                back.put("resp_dept_name", resp_dept_name);
            } catch (Exception e) {
                back.put("resp_dept_name", back.getString("resp_dept"));
            }
            try {
                String sql = "select id,police_id,id_num,name from user where id_num = '" + back.getString(
                        "resp_police") + "'";
                List<JSONObject> users = mysql.query(sql);
                JSONObject user = users.get(0);
                String name = user.getString("name");
                back.put("resp_police_name", name);
            } catch (Exception ex) {
                back.put("resp_police_name", back.getString("resp_police"));
            }

        } catch (Exception e) {

        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    /**
     * 获取机构信息
     *
     * @param data
     * @return
     */
    private JSONObject getOrganInfo(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String sql = "";

            sql = "select id,dict_name,father_id from dict where type = '23' and isdelete = '1' and is_kh = '1'";
            List<JSONObject> fj = mysql.query(sql);

            sql = "select id,dict_name,father_id from dict where type = '25' and isdelete = '1' and is_kh = '1'";
            List<JSONObject> pcs = mysql.query(sql);

            sql = "select id,dict_name,father_id from dict where type = '26' and isdelete = '1' and is_kh = '1'";
            List<JSONObject> zrq = mysql.query(sql);

            if (fj.size() > 0) {
                for (int i = 0; i < fj.size(); i++) {
                    JSONObject one = fj.get(i);
                    String id = one.getString("id");

                    ArrayList<JSONObject> arr2 = getOrgByFather(pcs, id);

                    if (arr2.size() > 0) {
                        for (int j = 0; j < arr2.size(); j++) {
                            JSONObject two = arr2.get(j);
                            String id2 = two.getString("id");

                            ArrayList<JSONObject> arr3 = getOrgByFather(zrq, id2);
                            two.put("next", arr3);
                            two.put("user", new JSONObject());
                        }
                    }

                    one.put("next", arr2);
                    one.put("user", new JSONObject());
                }
            }

            back.put("data", fj);

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-getOrganInfo", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
        return back;
    }

    private ArrayList<JSONObject> getOrgByFather(List<JSONObject> list, String id) {
        ArrayList<JSONObject> back = new ArrayList<>();
        try {
            for (int i = 0; i < list.size(); i++) {
                JSONObject obj = list.get(i);
                String fatherId = obj.getString("father_id");

                if (fatherId.equals(id)) {
                    back.add(obj);
                }

            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return back;
    }

    /**
     * 获取统一配置
     *
     * @param data
     * @return
     */
    private JSONObject getUnifyControl(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String sql = "";

            sql = "SELECT type from kh_control GROUP BY type;";
            List<JSONObject> types = mysql.query(sql);

            JSONObject object = new JSONObject();
            if (types.size() > 0) {
                for (int i = 0; i < types.size(); i++) {
                    JSONObject one = types.get(i);
                    String type = one.getString("type");
                    List<JSONObject> list = null;

                    sql = "select name,value from kh_control where type = '" + type + "'";
                    logger.warn(sql);
                    list = mysql.query(sql);

                    JSONObject ob = new JSONObject();
                    for (int j = 0; j < list.size(); j++) {
                        JSONObject two = list.get(j);
                        String name = two.getString("name");
                        String value = two.getString("value");
                        ob.put(name, value);
                    }

                    object.put(type, ob);

                }
            }

            back.put("data", object);

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-getUnifyControl", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
        return back;
    }

    /**
     * 更新统一配置
     *
     * @param data
     * @return
     */
    private JSONObject updateUnifyControl(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String sql = "";
            JSONObject datas = null;
            if (data.containsKey("datas") && !data.getJSONObject("datas").isEmpty()) {
                datas = data.getJSONObject("datas");
            } else {
                return ErrNo.set(480041);
            }
            String time = "";
            if (datas.containsKey("total_month") && datas.getString("total_month").length() > 0) {
                time = datas.getString("total_month");
            } else {
                return ErrNo.set(480041);
            }
            String plan = "";
            if (datas.containsKey("total_plan") && datas.getString("total_plan").length() > 0) {
                plan = datas.getString("total_plan");
            } else {
                return ErrNo.set(480041);
            }

            for (Map.Entry<String, Object> entries : datas.entrySet()) {
                String name = entries.getKey();
                Object value = entries.getValue();

                sql = "update kh_control set value = '" + value + "' where name = '" + name + "'";
                logger.warn(sql);
                mysql.update(sql);
            }

            if (datas.containsKey("appeal_enable") && datas.getString("appeal_enable").length() > 0) {
                //判断考核是否结束
                if (datas.getString("appeal_enable").equals("0")) {
                    sql = "update kh_control set value = '0' where name = 'appeal_jjl_enable'";
                    logger.warn(sql);
                    mysql.update(sql);
                }
                infoAppealEnd(mysql);
            }

            if (datas.containsKey("appeal_jjl_enable") && datas.getString("appeal_jjl_enable").length() > 0) {
                //判断考核是否结束
                infoAppealEnd(mysql);
            }

            //获取最新配置信息
            JSONObject configInfo = getConfigInfo(mysql);
            data.put("configs", configInfo);

            //判断是否存在历史
            infoHistory(time, plan, mysql);

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-updateUnifyControl", userlog.TYPE_OPERATE, remoteAddr);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    private JSONObject getConfigInfo(InfoModelHelper mysql) throws Exception {

        String sql = "SELECT type from kh_control GROUP BY type;";
        List<JSONObject> types = mysql.query(sql);

        JSONObject info = new JSONObject();
        if (types.size() > 0) {
            for (int i = 0; i < types.size(); i++) {
                JSONObject one = types.get(i);
                String type = one.getString("type");
                List<JSONObject> list = null;

                sql = "select name,value from kh_control where type = '" + type + "'";
                logger.warn(sql);
                list = mysql.query(sql);

                JSONObject ob = new JSONObject();
                for (int j = 0; j < list.size(); j++) {
                    JSONObject two = list.get(j);
                    String name = two.getString("name");
                    String value = two.getString("value");
                    ob.put(name, value);
                }

                info.put(type, ob);

            }
        }

        return info;
    }

    private void infoAppealEnd(InfoModelHelper mysql) throws Exception {

        String sql = "select * from kh_control where type = 'kh_appeal'";
        List<JSONObject> query = mysql.query(sql);

        String appeal = "";
        String appeal_jjl = "";

        for (int i = 0; i < query.size(); i++) {
            JSONObject object = query.get(i);
            String name = object.getString("name");
            if (name.equals("appeal_enable")) {
                appeal = object.getString("value");
            }

            if (name.equals("appeal_jjl_enable")) {
                appeal_jjl = object.getString("value");
            }
        }

        if (appeal.equals("1") || appeal_jjl.equals("1")) {
            sql = "update kh_control set value = '0' where name = 'appeal_is_end'";
            logger.warn(sql);
            mysql.update(sql);
        } else {
            sql = "update kh_control set value = '1' where name = 'appeal_is_end'";
            logger.warn(sql);
            mysql.update(sql);
        }
    }

    private void infoHistory(String time, String plan, InfoModelHelper mysql) throws Exception {

        String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String s = "select id from kh_history where time = '" + time + "'";
        logger.warn(s);
        List<JSONObject> his = mysql.query(s);

        if (his.size() == 0) {
            s = "select id,name,fj,pcs,zrq,fj_config,pcs_config,zrq_config from kh_plan where id = '" + plan + "'";
            logger.warn(s);
            List<JSONObject> query = mysql.query(s);
            JSONObject object = query.get(0);
            object.getString("fj");
            JSONArray fj = object.getJSONArray("fj");
            JSONArray pcs = object.getJSONArray("pcs");
            JSONArray zrq = object.getJSONArray("zrq");
            object.put("fj", planInfo(fj, mysql));
            object.put("pcs", planInfo(pcs, mysql));
            object.put("zrq", planInfo(zrq, mysql));
            String fjs = filterConfigs(fj);
            String pcss = filterConfigs(pcs);
            String zrqs = filterConfigs(zrq);

            object.put("fj_config", fjs);
            object.put("pcs_config", pcss);
            object.put("zrq_config", zrqs);
            s = "insert kh_history(time,plan,create_time,isdelete) values('" + time + "','" + object + "','" + create_time + "','1')";
            logger.warn(s);
            mysql.update(s);
        } else {
            JSONObject history = his.get(0);
            String id = history.getString("id");
            s = "select id,name,fj,pcs,zrq,fj_config,pcs_config,zrq_config from kh_plan where id = '" + plan + "'";
            logger.warn(s);
            List<JSONObject> query = mysql.query(s);
            JSONObject object = query.get(0);
            object.getString("fj");
            JSONArray fj = object.getJSONArray("fj");
            JSONArray pcs = object.getJSONArray("pcs");
            JSONArray zrq = object.getJSONArray("zrq");
            object.put("fj", planInfo(fj, mysql));
            object.put("pcs", planInfo(pcs, mysql));
            object.put("zrq", planInfo(zrq, mysql));
            String fjs = filterConfigs(fj);
            String pcss = filterConfigs(pcs);
            String zrqs = filterConfigs(zrq);

            object.put("fj_config", fjs);
            object.put("pcs_config", pcss);
            object.put("zrq_config", zrqs);

            s = "update kh_history set plan = '" + object + "' where id = '" + id + "'";
//                logger.warn(s);
            mysql.update(s);
        }
    }

    private JSONObject getCacheFile(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String sql = "select * from upload where type = '14' order by time desc";
            List<JSONObject> list = mysql.query(sql);

            if (list.size() > 0) {
                JSONObject obj = list.get(0);
                String id = obj.getString("id");
                String time = obj.getString("time");
                back.put("file_id", id);
                back.put("time", time);
            } else {
                back.put("file_id", "");
                back.put("time", "");
            }

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-getCacheFile", userlog.TYPE_OPERATE, remoteAddr);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480012);
        }
        return back;
    }

    private JSONObject updateCacheFile(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String id = "";
            String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(480013);
            }

            String sqls = "update upload set type = '14',time = '" + time + "' where id = '" + id + "'";
            mysql.update(sqls);

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-updateCacheFile", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480014);
        }
        return back;
    }

    private JSONObject getEvent(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String sql = "";
            String id = "";
            String kh_name = "";
            String resp_dept = "";
            String resp_police = "";
            String point = "";
            String static_type = "";
            int limit = 20;
            int page = 1;

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id ='" + id + "' and ";
            }
            if (data.containsKey("kh_name") && data.getString("kh_name").length() > 0) {
                kh_name = data.getString("kh_name");
                sql = sql + " kh_name  like '%" + kh_name + "%' and ";
            }
            if (data.containsKey("resp_dept") && data.getString("resp_dept").length() > 0) {
                resp_dept = data.getString("resp_dept");
//                sql = sql + " resp_dept_name ='" + resp_dept + "' and ";
                sql = sql + " resp_dept_name like'%" + resp_dept + "%' and ";
            }
            if (data.containsKey("resp_police") && data.getString("resp_police").length() > 0) {
                resp_police = data.getString("resp_police");
//                sql = sql + " resp_police_name ='" + resp_police + "' and ";
                sql = sql + " resp_police_name like'%" + resp_police + "%' and ";
            }
            if (data.containsKey("point") && data.getString("point").length() > 0) {
                point = data.getString("point");
                sql = sql + " point ='" + point + "' and ";
            }
            if (data.containsKey("static_type") && data.getString("static_type").length() > 0) {
                static_type = data.getString("static_type");
                sql = sql + " static_type ='" + static_type + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }
            String baseSql = "select * from (select * , (select dict_name from dict where id = resp_dept) as " +
                    "resp_dept_name," + "(select name from user where id_num = resp_police) as resp_police_name from " +
                    "kh_config) kh " + "where" + sql + " isdelete=1 order by create_time desc limit " + limit + " " +
                    "offset " + limit * (page - 1);

            String sqls =
                    "select * from kh_config where " + sql + " isdelete=1 order by create_time desc limit " + limit + " offset " + limit * (page - 1);
            logger.warn(baseSql);
            List<JSONObject> list = mysql.query(baseSql);

            if (list.size() > 0) {
//                back.put("data", RelaInfo(list,mysql));
                back.put("data", RelaInfo(list, mysql));
                sqls = "select * from (select * , (select dict_name from dict where id = resp_dept) as " +
                        "resp_dept_name," + " (select name from user where id_num = resp_police) as resp_police_name " +
                        "from kh_config) kh" + " where " + sql + " isdelete=1";
//                sqls = "select id from kh_config where " + sql + " isdelete=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());

            } else {
                back.put("data", list);
                back.put("count", 0);
            }

            // 添加日志
//            String opt_user = "";
//            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
//                opt_user = data.getString("opt_user");
//            }
//            UserLog userlog = new UserLog();
//            userlog.log(mysql, opt_user, "考核-getEvent", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480004);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String respDept = one.getString("resp_dept");
            String respPolice = one.getString("resp_police");

            try {
                String sql = "select id,police_id,id_num,name from user where id_num = '" + respPolice + "'";
                List<JSONObject> users = mysql.query(sql);
                JSONObject user = users.get(0);
                String name = user.getString("name");
                one.put("resp_police_name", name);
            } catch (Exception ex) {
                one.put("resp_police_name", respPolice);
            }

            try {
                one.put("resp_dept_name", RIUtil.dicts.get(respDept).getString("dict_name"));
            } catch (Exception ex) {
                one.put("resp_dept_name", respDept);
            }
            back.add(one);

        }
        return back;
    }

    private JSONObject createEvent(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String kh_name = "";
            String pg_rules = "";
            String resp_dept = "";
            String resp_police = "";
            String full_mark = "0.00";
            String point = "2";
            String static_type = "2";
            String static_rules = "";
            String static_period = "";
            String real_table = "";
            String real_field = "";
            String remark = "";
            String create_user = "";

            // 新增字段计分节点
            String score_node = "";

            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                return ErrNo.set(480005);
            }
            if (data.containsKey("kh_name") && data.getString("kh_name").length() > 0) {
                kh_name = data.getString("kh_name");

            } else {
                return ErrNo.set(480005);
            }

            if (data.containsKey("pg_rules") && data.getString("pg_rules").length() > 0) {
                pg_rules = data.getString("pg_rules");
            }
            if (data.containsKey("resp_dept") && data.getString("resp_dept").length() > 0) {
                resp_dept = data.getString("resp_dept");
            }
            if (data.containsKey("resp_police") && data.getString("resp_police").length() > 0) {
                resp_police = data.getString("resp_police");
            }

            if (data.containsKey("full_mark") && data.getString("full_mark").length() > 0) {
                full_mark = data.getString("full_mark");
            }
            if (data.containsKey("point") && data.getString("point").length() > 0) {
                point = data.getString("point");
            }
            if (data.containsKey("static_type") && data.getString("static_type").length() > 0) {
                static_type = data.getString("static_type");
            }
            if (data.containsKey("static_rules") && data.getString("static_rules").length() > 0) {
                static_rules = data.getString("static_rules");
            }
            if (data.containsKey("static_period") && data.getString("static_period").length() > 0) {
                static_period = data.getString("static_period");
            }
            if (data.containsKey("real_table") && data.getString("real_table").length() > 0) {
                real_table = data.getString("real_table");
            }
            if (data.containsKey("real_field") && data.getString("real_field").length() > 0) {
                real_field = data.getString("real_field");
            }
            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
            }

            // 新增字段计分节点
            if (data.containsKey("score_node") && data.getString("score_node").length() > 0) {
                score_node = data.getString("score_node");
            }

            String sql = "";

            String id = UUID.randomUUID().toString().replace("-", "");
            sql = "insert kh_config(id,kh_name,pg_rules,resp_dept,resp_police," + "full_mark,point,static_type," +
                    "static_rules,static_period,real_table,real_field,remark,create_user," + "create_time,isdelete," +
                    "score_node) values" + "('" + id + "','" + kh_name + "','" + pg_rules + "','" + resp_dept + "','" + resp_police + "','" + full_mark + "','" + point + "','" + static_type + "','" + static_rules + "','" + static_period + "','" + real_table + "','" + real_field + "','" + remark + "','" + create_user + "','" + create_time + "',1,'" + score_node + "')";
            logger.warn(sql);
            mysql.update(sql);

            sql = "select * from kh_config where id='" + id + "'";
            List<JSONObject> list = mysql.query(sql);

            RIUtil.kh_configs.put(id, list.get(0));


            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-createEvent", userlog.TYPE_OPERATE, remoteAddr);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480006);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject deleteEvent(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String id = "";
            String opt_user = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(480007);
            }

            // 1.8 原来未赋值
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }

            String sqls =
                    "update kh_config set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id = '" + id + "'";
            logger.warn(sqls);
            mysql.update(sqls);

            // 添加日志
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-deleteEvent", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480008);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject deletePlan(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String id = "";
            String opt_user = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(480035);
            }

            // 1.8 原来未给opt_user 赋值
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }

            String sqls =
                    "update kh_plan set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id = '" + id + "'";
            logger.warn(sqls);
            mysql.update(sqls);


            // 添加日志

            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-deletePlan", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480036);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject copyPlan(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String id = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(480038);
            }
            String sqls = "select * from kh_plan where id = '" + id + "'";
            List<JSONObject> list = mysql.query(sqls);
            if (list.size() > 0) {
                JSONObject obj = list.get(0);

                JSONArray fj = upConfigInfo(obj.getJSONArray("fj"));
                JSONArray pcs = upConfigInfo(obj.getJSONArray("pcs"));
                JSONArray zrq = upConfigInfo(obj.getJSONArray("zrq"));

                obj.put("fj", fj);
                obj.put("pcs", pcs);
                obj.put("zrq", zrq);

                back.put("data", list);
            } else {
                return ErrNo.set(480038);
            }

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-copyPlan", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480039);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONArray upConfigInfo(JSONArray data) {
        for (int i = 0; i < data.size(); i++) {
            JSONObject obj = data.getJSONObject(i);
            if (obj.containsKey("next") && obj.getJSONArray("next").size() > 0) {
                upConfigInfo(obj.getJSONArray("next"));
            } else {
                if (obj.containsKey("label") && obj.getBoolean("label")) {
                    String id = obj.getString("id");
                    String kh_name = "";
                    String pg_rules = "";
                    String resp_dept = "";
                    String resp_police = "";
                    String full_mark = "";
                    String point = "";
                    String static_type = "";
                    try {
                        kh_name = RIUtil.kh_configs.get(id).getString("kh_name");
                        pg_rules = RIUtil.kh_configs.get(id).getString("pg_rules");
                        resp_dept = RIUtil.kh_configs.get(id).getString("resp_dept");
                        resp_police = RIUtil.kh_configs.get(id).getString("resp_police");
                        full_mark = RIUtil.kh_configs.get(id).getString("full_mark");
                        point = RIUtil.kh_configs.get(id).getString("point");
                        static_type = RIUtil.kh_configs.get(id).getString("static_type");
                        obj.put("kh_name", kh_name);
                        obj.put("pg_rules", pg_rules);
                        obj.put("resp_dept", resp_dept);
                        obj.put("resp_police", resp_police);
                        obj.put("full_mark", full_mark);
                        obj.put("point", point);
                        obj.put("static_type", static_type);

                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }

                }
            }
        }
        return data;
    }

    private JSONObject updateEvent(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String sql = "";
            String sqls = "";
            String id = "";
            String kh_name = "";
            String pg_rules = "";
            String pg_object = "";
            String resp_dept = "";
            String resp_police = "";
            String full_mark = "0.00";
            String point = "0";
            String static_type = "0";
            String static_rules = "";
            String static_period = "";
            String real_table = "";
            String real_field = "";
            String remark = "";

            // 新增计分节点
            String score_node = "";

            String update_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(480009);
            }

            if (data.containsKey("kh_name") && data.getString("kh_name").length() > 0) {
                kh_name = data.getString("kh_name");
                sql = sql + "kh_name = '" + kh_name + "',";
            }
            if (data.containsKey("pg_rules") && data.getString("pg_rules").length() > 0) {
                pg_rules = data.getString("pg_rules");
                sql = sql + "pg_rules = '" + pg_rules + "',";
            }
            if (data.containsKey("pg_object") && data.getString("pg_object").length() > 0) {
                pg_object = data.getString("pg_object");
                sql = sql + "pg_object = '" + pg_object + "',";
            }
            if (data.containsKey("resp_dept") && data.getString("resp_dept").length() > 0) {
                resp_dept = data.getString("resp_dept");
                sql = sql + "resp_dept = '" + resp_dept + "',";
            }
            if (data.containsKey("resp_police") && data.getString("resp_police").length() > 0) {
                resp_police = data.getString("resp_police");
                sql = sql + "resp_police = '" + resp_police + "',";
            }
            if (data.containsKey("point") && data.getString("point").length() > 0) {
                point = data.getString("point");
                sql = sql + "point = '" + point + "',";
            }
            if (data.containsKey("full_mark") && data.getString("full_mark").length() > 0) {
                full_mark = data.getString("full_mark");
                sql = sql + "full_mark = '" + full_mark + "',";
            }
            if (data.containsKey("static_type") && data.getString("static_type").length() > 0) {
                static_type = data.getString("static_type");
                sql = sql + "static_type = '" + static_type + "',";
            }
            if (data.containsKey("static_rules") && data.getString("static_rules").length() > 0) {
                static_rules = data.getString("static_rules");
                sql = sql + "static_rules = '" + static_rules + "',";
            }
            if (data.containsKey("static_period") && data.getString("static_period").length() > 0) {
                static_period = data.getString("static_period");
                sql = sql + "static_period = '" + static_period + "',";
            }
            if (data.containsKey("real_table") && data.getString("real_table").length() > 0) {
                real_table = data.getString("real_table");
                sql = sql + "real_table = '" + real_table + "',";
            }
            if (data.containsKey("real_field") && data.getString("real_field").length() > 0) {
                real_field = data.getString("real_field");
                sql = sql + "real_field = '" + real_field + "',";
            }
            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
                sql = sql + "remark = '" + remark + "',";
            }

            // 新增计分节点
            if (data.containsKey("score_node") && data.getString("score_node").length() > 0) {
                score_node = data.getString("score_node");
                sql = sql + "score_node = '" + score_node + "',";
            }


            sqls = "update kh_config set " + sql + " update_time = '" + update_time + "' where id = '" + id + "'";
            logger.warn(sqls);
            mysql.update(sqls);

            sql = "select * from kh_config where id='" + id + "'";
            List<JSONObject> list = mysql.query(sql);
            RIUtil.kh_configs.put(id, list.get(0));

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-updateEvent", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480010);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject updatePlan(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String sql = "";
            String sqls = "";
            String id = "";
            String fj = "";
            String pcs = "";
            String zrq = "";
            String name = "";
            String update_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(480033);
            }

            if (data.containsKey("fj") && data.getString("fj").length() > 0) {
                fj = data.getString("fj");
                String fjs = filterConfigs(JSONArray.parseArray(fj));
                sql = sql + "fj = '" + fj + "',";
                sql = sql + "fj_config = '" + fjs + "',";
            }
            if (data.containsKey("pcs") && data.getString("pcs").length() > 0) {
                pcs = data.getString("pcs");
                String pcss = filterConfigs(JSONArray.parseArray(pcs));
                sql = sql + "pcs = '" + pcs + "',";
                sql = sql + "pcs_config = '" + pcss + "',";
            }
            if (data.containsKey("zrq") && data.getString("zrq").length() > 0) {
                zrq = data.getString("zrq");
                String zrqs = filterConfigs(JSONArray.parseArray(zrq));
                sql = sql + "zrq = '" + zrq + "',";
                sql = sql + "zrq_config = '" + zrqs + "',";
            }
            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");
                sql = sql + "name = '" + name + "',";
            }


            sqls = "update kh_plan set " + sql + " update_time = '" + update_time + "' where id = '" + id + "'";
            mysql.update(sqls);

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-updatePlan", userlog.TYPE_OPERATE, remoteAddr);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480034);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject createPlan(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String opt_user = "";
            String name = "";
            String fj = "";
            String pcs = "";
            String zrq = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");

            } else {
                return ErrNo.set(480031);
            }
            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");

            } else {
                return ErrNo.set(480031);
            }
            if (data.containsKey("fj") && data.getString("fj").length() > 0) {
                fj = data.getString("fj");

            } else {
                return ErrNo.set(480031);
            }
            if (data.containsKey("pcs") && data.getString("pcs").length() > 0) {
                pcs = data.getString("pcs");
            } else {
                return ErrNo.set(480031);
            }
            if (data.containsKey("zrq") && data.getString("zrq").length() > 0) {
                zrq = data.getString("zrq");
            } else {
                return ErrNo.set(480031);
            }

            String sql = "";

            String fjs = filterConfigs(JSONArray.parseArray(fj));
            String pcss = filterConfigs(JSONArray.parseArray(pcs));
            String zrqs = filterConfigs(JSONArray.parseArray(zrq));

            sql = "insert kh_plan(name,fj,pcs,zrq,create_user,create_time,fj_config,pcs_config,zrq_config,isdelete) " + "values('" + name + "','" + fj + "','" + pcs + "','" + zrq + "','" + opt_user + "','" + create_time + "','" + fjs + "','" + pcss + "','" + zrqs + "','1')";
            logger.warn(sql);
            mysql.update(sql);

            // 添加日志

            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-createPlan", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480032);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    private JSONObject getPlan(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String id = "";
            String time = "";
            String name = "";

            String sql = "";


            int limit = 20;
            int page = 1;

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id ='" + id + "' and ";
            }

            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");
                sql = sql + " name like '%" + name + "%' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            String sqls = "select * from kh_plan where " + sql + " isdelete = 1 order by time desc limit " + limit +
                    " offset " + limit * (page - 1);
            logger.warn(sqls);
            List<JSONObject> list = mysql.query(sqls);

            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject object = list.get(i);

                    JSONArray fj = object.getJSONArray("fj");
                    JSONArray array = planInfo(fj, mysql);
                    object.put("fj", array);
                    JSONArray pcs = object.getJSONArray("pcs");
                    JSONArray array2 = planInfo(pcs, mysql);
                    object.put("pcs", array2);
                    JSONArray zrq = object.getJSONArray("zrq");
                    JSONArray array3 = planInfo(zrq, mysql);
                    object.put("zrq", array3);

                    String createUser = object.getString("create_user");
                    try {
                        String n = RIUtil1.users1.get(createUser).getString("name");
                        object.put("create_user", n);
                    } catch (Exception ex) {
                        System.out.println("人员信息错误！");
                    }
                }

                back.put("data", list);
                sqls = "select * from kh_plan where " + sql + " isdelete = 1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());

            } else {
                back.put("data", list);
                back.put("count", 0);
            }

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-getPlan", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480049);
        }
        return back;
    }

    private JSONObject getHistory(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String id = "";
            String time = "";
            String sql = "";


            int limit = 20;
            int page = 1;

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id ='" + id + "' and ";
            }

            if (data.containsKey("time") && data.getString("time").length() > 0) {
                time = data.getString("time");
                sql = sql + " time ='" + time + "' and ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }

            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            String sqls =
                    "select * from kh_history where " + sql + " isdelete = 1 order by time desc limit " + limit + " " +
                            "offset " + limit * (page - 1);
            logger.warn(sqls);
            List<JSONObject> list = mysql.query(sqls);

            if (list.size() > 0) {


                for (int i = 0; i < list.size(); i++) {
                    JSONObject obj = list.get(i);
                    JSONObject plan = obj.getJSONObject("plan");
                    JSONArray fj = plan.getJSONArray("fj");
                    plan.put("fj", fj);
                    JSONArray pcs = plan.getJSONArray("pcs");
                    plan.put("pcs", pcs);
                    JSONArray zrq = plan.getJSONArray("zrq");
                    plan.put("zrq", zrq);
                    obj.put("plan", plan);
                }
                back.put("data", list);
                sqls = "select * from kh_history where " + sql + " isdelete = 1 ";
                list = mysql.query(sqls);
                back.put("count", list.size());

            } else {
                back.put("data", list);
                back.put("count", 0);
            }

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480047);
        }
        return back;
    }

    private String filterConfigs(JSONArray data) {
        InfoModelHelper mysql = null;
        String back = "";
        try {
            mysql = InfoModelPool.getModel();
            for (int i = 0; i < data.size(); i++) {
                JSONObject obj = data.getJSONObject(i);

                if (obj.containsKey("id") && obj.getString("id").length() > 0) {

                    if (obj.containsKey("label") && obj.getBoolean("label")) {
                        String id = obj.getString("id");
                        back = back + id + ",";
                    }

                }
                if (obj.containsKey("next") && obj.getJSONArray("next").size() > 0) {
                    JSONArray next = obj.getJSONArray("next");
                    String s = filterConfigs(next);
                    back = back + s + ",";
                }
            }
            if (back.trim().length() == 0) {
                return "";
            } else {
                back = back.substring(0, back.length() - 1);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONArray planInfo(JSONArray data, InfoModelHelper mysql) {
        for (int i = 0; i < data.size(); i++) {
            JSONObject obj = data.getJSONObject(i);
            if (obj.containsKey("next") && obj.getJSONArray("next").size() > 0) {
                planInfo(obj.getJSONArray("next"), mysql);
            } else {
                if (obj.containsKey("label") && obj.getBoolean("label")) {
                    String id = obj.getString("id");
                    JSONObject conf = RIUtil.kh_configs.get(id);
                    try {
                        logger.warn(conf.toJSONString());
                        try {
                            obj.put("kh_name", conf.getString("kh_name"));
                        } catch (Exception e) {
                            logger.error(e.getMessage());
                        }
                        try {
                            obj.put("pg_rules", conf.getString("pg_rules"));
                        } catch (Exception e) {
                            logger.error(e.getMessage());
                        }
                        try {
                            obj.put("resp_dept", conf.getString("resp_dept"));
                        } catch (Exception e) {
                            logger.error(e.getMessage());
                        }

                    } catch (Exception e) {
                        logger.error("方案Id:{}未从缓存查询到", id);
                        logger.error(e.getMessage());
                    }

                    try {
                        String resp_dept_name = RIUtil.dicts.get(conf.getString("resp_dept")).getString("dict_name");
                        obj.put("resp_dept_name", resp_dept_name);
                    } catch (Exception e) {
                        obj.put("resp_dept_name", conf.getString("resp_dept"));
                    }
                    try {
                        String sql = "select id,police_id,id_num,name from user where id_num = '" + conf.getString(
                                "resp_police") + "'";
                        List<JSONObject> users = mysql.query(sql);
                        JSONObject user = users.get(0);
                        String name = user.getString("name");
                        obj.put("resp_police_name", name);
                    } catch (Exception ex) {
                        obj.put("resp_police_name", conf.getString("resp_police"));
                    }

                    try {
                        obj.put("resp_police", conf.getString("resp_police"));
                        obj.put("full_mark", conf.getString("full_mark"));
                        obj.put("point", conf.getString("point"));
                        obj.put("static_type", conf.getString("static_type"));
                    } catch (Exception e) {
                        logger.error(e.getMessage());
                    }


                }
            }
        }
        return data;
    }


}
