package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
public class StarController {
    private static Logger logger = LoggerFactory.getLogger(StarController.class);
    // private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static String ip = "";
    private static String[] quarter = {"01", "01", "01", "01", "02", "02", "02", "02", "03", "03", "03", "03", "04",
            "04", "04", "04"};

    @RequestMapping(method = {RequestMethod.POST}, path = {"/star"})
    public static JSONObject get_star(TNOAHttpRequest request) throws Exception {

        String opt = "";

        JSONObject data = request.getRequestParams();
        String ip = request.getRemoteAddr();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_star")) {
                return getStar(data);
            } else if (opt.equals("create_star")) {
                logger.warn("star--->" + data.toString());
                return createStar(data, ip);
            } else if (opt.equals("update_star")) {
                logger.warn("star--->" + data.toString());
                return updateStar(data, ip);
            } else if (opt.equals("delete_star")) {
                return deleteStar(data, ip);
            } else {
                return ErrNo.set(455009);
            }
        } else {
            return ErrNo.set(455009);
        }
    }

    //******CREATE*******
    private static JSONObject createStar(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String user_id = "";
            String type = "";
            String mark = "";
            String disc = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "1";
            String img = "";
            String unit = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            }
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
            } else {
                return ErrNo.set(455002);
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");

            } else {
                return ErrNo.set(455002);
            }

            if (data.containsKey("mark") && data.getString("mark").length() > 0) {
                mark = data.getString("mark");

            } else {
                return ErrNo.set(455002);
            }
            if (data.containsKey("disc") && data.getString("disc").length() > 0) {
                disc = data.getString("disc");
            } else {
                return ErrNo.set(455002);
            }
            if (data.containsKey("img") && data.getString("img").length() > 0) {
                img = data.getString("img");
            } else {
                return ErrNo.set(455002);
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
                if (unit.length() == 0) {
                    unit = RIUtil.users.get(create_user).getString("unit");
                }
            } else {
                return ErrNo.set(455002);
            }

            String sqls =
                    "insert star (user_id,type,mark,disc,create_user,create_time,isdelete,img,unit)values('" + user_id + "','" + type + "','" + mark + "',encode('" + disc + "','" + RIUtil.enContent + "'),'" + create_user + "','" + create_time + "','" + isdelete + "','" + img + "','" + unit + "')";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建警营之星", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(455001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private static JSONObject getStar(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String user_id = "";
            String type = "";
            String mark = "";
            String isMain = "";
            String unit = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " a.id='" + id + "' and ";
            }
            if (data.containsKey("userName") && data.getString("userName").length() > 0) {
                user_id = data.getString("userName");
                sql = sql + " b.name like '%" + user_id + "%' and ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " a.type='" + type + "' and ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " a.unit='" + unit + "' and ";
            }
            if (data.containsKey("mark") && data.getString("mark").length() > 0) {
                mark = data.getString("mark");
                sql = sql + " a.mark in ('" + mark.replace(",", "','") + "') and ";
            }
            if (data.containsKey("isMain") && data.getString("isMain").length() > 0) {
                isMain = data.getString("isMain");
                sql = sql + " b.isMain='" + isMain + "' and ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select a.*,decode(a.disc,'" + RIUtil.enContent + "') as disc  from star a left join user b on a" +
                            ".user_id=b.id where 1=1 and " + sql + " a.isdelete=1  limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select a.id from star a left join user b on a.user_id=b.id where 1=1 and " + sql + " a" +
                        ".isdelete=1";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(455005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private static Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String user_id = one.getString("user_id");
            one.put("user", RIUtil.users.get(user_id));
            if (one.containsKey("unit")) {
                String unit = one.getString("unit");
                one.put("unit_name", RIUtil.RealDictNames(RIUtil.StringToList(unit)));
            } else {
                one.put("unit", "");
                one.put("unit_name", "");
            }
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private static JSONObject updateStar(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String user_id = "";
            String type = "";
            String mark = "";
            String disc = "";
            String img = "";
            String opt_user = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(455004);
            }
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
                sql = sql + " user_id='" + user_id + "' , ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type='" + type + "' , ";
            }
            if (data.containsKey("mark") && data.getString("mark").length() > 0) {
                mark = data.getString("mark");
                sql = sql + " mark='" + mark + "' , ";
            }
            if (data.containsKey("img") && data.getString("img").length() > 0) {
                img = data.getString("img");
                sql = sql + " img='" + img + "' , ";
            }
            if (data.containsKey("disc") && data.getString("disc").length() > 0) {
                disc = data.getString("disc");
                sql = sql + " disc=encode('" + disc + "','" + RIUtil.enContent + "') , ";
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            String sqls = "update star set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);
            logger.warn(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新警营之星", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(455003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private static JSONObject deleteStar(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(455008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(455008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update star set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除警营之星", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(455007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

}

