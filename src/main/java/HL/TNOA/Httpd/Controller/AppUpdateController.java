package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.TNOAConf;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;

@RestController
public class AppUpdateController {
    private static Logger logger = LoggerFactory.getLogger(AppUpdateController.class);
    private static String ip = "";

    @RequestMapping(method = {RequestMethod.POST}, path = {"/update"})
    public static JSONObject get_version(TNOAHttpRequest request) throws Exception {

        String opt = "";
        ip = request.getRemoteAddr();
        JSONObject data = request.getRequestParams();

        if (data.containsKey("version") && data.getString("version").length() > 0) {

            return getVersion(data);

        } else {
            return ErrNo.set(50001);
        }
    }

    private static JSONObject getVersion(JSONObject data) {
        String version = "";
        JSONObject back = ErrNo.set(0);
        if (data.containsKey("version") && data.getString("version").length() > 0) {
            version = data.getString("version");
        } else {
            return ErrNo.set(500003);
        }
        String filePath = TNOAConf.get("file", "sou_path") + "app/";
        //logger.warn(filePath);
        File file = new File(filePath);
        String[] files = file.list();
        String appName = "zhpcs_";
        if (files.length > 0) {
            logger.warn(files[0]);
            String fileName = files[0].replace(appName, "").replace(".apk", "");
            if (version.equals(fileName)) {
                back.put("url", "");

            } else {
                back.put("url", files[0]);
            }
        }


        return back;

    }

}
