package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@RestController
public class NotificationController {

    private static Logger logger = LoggerFactory.getLogger(NotificationController.class);

    @RequestMapping(method = {RequestMethod.POST}, path = {"/noticefication"})
    public static JSONObject get_Noticefication(TNOAHttpRequest requset) throws Exception {
        String remoteAddr = requset.getRemoteAddr();
        JSONObject data = requset.getRequestParams();
        String opt = "";
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("notification_create")) {
                return NftCreate(data, remoteAddr);
            } else if (opt.equals("notification_delete")) {
                return NftDelete(data, remoteAddr);
            } else if (opt.equals("notification_update")) {
                return NftUpdate(data, remoteAddr);
            } else if (opt.equals("notification_get")) {
                return NtfGet(data);
            } else if (opt.equals("notification_get_list")) {
                return NtfGetList(data);
            } else {
                return ErrNo.set(470001);
            }
        } else {
            return ErrNo.set(470001);
        }
    }

    private static JSONObject NtfGetList(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String start_date = "";
            String end_date = "";
            int limit = 20;
            int page = 1;
            String sql = "";
            String Pid = "";

            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date");
                sql = sql + "date>='" + start_date + "' and ";

            }
            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
                sql = sql + "date<='" + end_date + "' and ";

            }
            if (data.containsKey("Pid") && data.getString("Pid").length() > 0) {
                Pid = data.getString("Pid");
                sql = sql + " Pid like '%" + Pid + "%' and ";
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");

            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");

            }
            String sqls = "select id,date,create_user,create_time from police_notification " +
                    "where 1=1 and " + sql + " isdelete=1   group by date order by date desc " + "limit " + limit +
                    " offset " + limit * (page - 1);
            // logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfoList(list, mysql));
                sqls = "select id from police_notification where 1=1 and " + sql + " isdelete=1 group by date; ";
                //logger.warn(sqls);
                list = mysql.query(sqls);
                back.put("count", list.size());
                // logger.warn(back.toString());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(470008);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        logger.warn(back.toString());
        return back;
    }

    private static List<JSONObject> RelaInfoList(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String create_user = one.getString("create_user");
            one.put("create_user", RIUtil.IdToName(create_user, mysql, " name"
                    , "user"));
            back.add(one);
        }
        return back;
    }

    private static List<JSONObject> RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String create_user = one.getString("create_user");
            String Pid = one.getString("Pid");

            JSONObject cu = RIUtil.users.get(create_user);
            one.put("create_user", cu);
//
            HashMap<String, String> pids = RIUtil.StringToList(Pid);
            one.put("Pid", RIUtil.UseridToNames(pids));
            back.add(one);
        }
        return back;
    }

    private static JSONObject NtfGet(JSONObject data) {
        logger.warn(String.valueOf(System.currentTimeMillis()));
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String Pid = "";
            String situation = "";
            String crimal = "";
            String public_security = "";
            String fight_crimal = "";
            String prostitution_gambling_clues = "";
            String other_clues = "";
            String stability_situation = "";
            String date = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("date") && data.getString("date").length() > 0) {
                date = data.getString("date");
                sql = sql + " date>='" + date + "' and";
            }
            if (data.containsKey("Pid") && data.getString("Pid").length() > 0) {
                Pid = data.getString("Pid");
                sql = sql + " Pid='" + Pid + "' and ";
            }
            if (data.containsKey("situation") && data.getString("situation").length() > 0) {
                situation = data.getString("situation");
                sql = sql + " decode(situation,'" + RIUtil.enContent + "') like '%" + situation + "%' and ";
            }
            if (data.containsKey("crimal") && data.getString("crimal").length() > 0) {
                crimal = data.getString("crimal");
                sql = sql + " decode(crimal,'" + RIUtil.enContent + "') like '%" + crimal + "%' and ";
            }
            if (data.containsKey("public_security") && data.getString("public_security").length() > 0) {
                public_security = data.getString("public_security");
                sql = sql + " decode(public_security,'" + RIUtil.enContent + "') like '%" + public_security + "%' and ";
            }
            if (data.containsKey("fight_crimal") && data.getString("fight_crimal").length() > 0) {
                fight_crimal = data.getString("fight_crimal");
                sql = sql + " decode(fight_crimal,'" + RIUtil.enContent + "') like '%" + fight_crimal + "%' and ";
            }
            if (data.containsKey("prostitution_gambling_clues") && data.getString("prostitution_gambling_clues").length() > 0) {
                prostitution_gambling_clues = data.getString("prostitution_gambling_clues");
                sql = sql + " decode(prostitution_gambling_clues,'" + RIUtil.enContent + "') like '%" + prostitution_gambling_clues + "%' and ";
            }
            if (data.containsKey("other_clues") && data.getString("other_clues").length() > 0) {
                other_clues = data.getString("other_clues");
                sql = sql + " decode(other_clues,'" + RIUtil.enContent + "') like '%" + other_clues + "%' and ";
            }
            if (data.containsKey("stability_situation") && data.getString("stability_situation").length() > 0) {
                stability_situation = data.getString("stability_situation");
                sql = sql + " decode(stability_situation,'" + RIUtil.enContent + "') like '%" + stability_situation + "%' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls = "select id,Pid,decode(situation,'" + RIUtil.enContent + "') as situation," +
                    "decode(crimal,'" + RIUtil.enContent + "') as crimal,decode(public_security,'" + RIUtil.enContent + "') as public_security," +
                    "decode(fight_crimal,'" + RIUtil.enContent + "') as fight_crimal,decode" +
                    "(prostitution_gambling_clues,'" + RIUtil.enContent + "') as prostitution_gambling_clues," +
                    "decode(other_clues,'" + RIUtil.enContent + "') as other_clues,decode(stability_situation,'" + RIUtil.enContent + "')as stability_situation,date,create_user,create_time " +
                    "from police_notification where 1=1 and " + sql + " isdelete=1 limit " + limit + " offset " + limit * (page - 1);
            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
//                logger.warn(back.toString());
                sqls = "select count(id) as count from police_notification where 1=1 and " + sql + " isdelete=1; ";
                back.put("count", mysql.query_count(sqls));
//                logger.warn(back.toString());
            } else {
                back.put("data", new ArrayList<>());
                back.put("count", 0);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(470007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
//        logger.warn(back.toString());
        logger.warn(String.valueOf(System.currentTimeMillis()));
        return back;
    }

    private static JSONObject NftUpdate(JSONObject data, String remoteAddr) {
        logger.warn(String.valueOf(System.currentTimeMillis()));
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String id = "";
            String Pid = "";
            String situation = "";
            String crimal = "";
            String public_security = "";
            String fight_crimal = "";
            String prostitution_gambling_clues = "";
            String other_clues = "";
            String stability_situation = "";
            String opt_user = "";
            String sql = "";
            String date = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(470005);
            }
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                logger.warn(id);
            } else {
                return ErrNo.set(470005);
            }
            if (data.containsKey("Pid") && data.getString("Pid").length() > 0) {
                Pid = data.getString("Pid");
                sql = sql + " Pid='" + Pid + "',";
            }
            if (data.containsKey("date") && data.getString("date").length() > 0) {
                date = data.getString("date");
                sql = sql + " date='" + date + "',";
            }
            if (data.containsKey("situation") && data.getString("situation").length() > 0) {
                situation = data.getString("situation");
                sql = sql + " situation=encode('" + situation + "','" + RIUtil.enContent + "'),";
            }
            if (data.containsKey("crimal") && data.getString("crimal").length() > 0) {
                crimal = data.getString("crimal");
                sql = sql + " crimal=encode('" + crimal + "','" + RIUtil.enContent + "'),";
            }
            if (data.containsKey("public_security") && data.getString("public_security").length() > 0) {
                public_security = data.getString("public_security");
                sql = sql + " public_security=encode('" + public_security + "','" + RIUtil.enContent + "'),";
            }
            if (data.containsKey("fight_crimal") && data.getString("fight_crimal").length() > 0) {
                fight_crimal = data.getString("fight_crimal");
                sql = sql + " fight_crimal=encode('" + fight_crimal + "','" + RIUtil.enContent + "'),";
            }
            if (data.containsKey("prostitution_gambling_clues") && data.getString("prostitution_gambling_clues").length() > 0) {
                prostitution_gambling_clues = data.getString("prostitution_gambling_clues");
                sql = sql + " prostitution_gambling_clues=encode('" + prostitution_gambling_clues + "','" + RIUtil.enContent + "'),";
            }
            if (data.containsKey("other_clues") && data.getString("other_clues").length() > 0) {
                other_clues = data.getString("other_clues");
                sql = sql + " other_clues=encode('" + other_clues + "','" + RIUtil.enContent + "'),";
            }
            if (data.containsKey("stability_situation") && data.getString("stability_situation").length() > 0) {
                stability_situation = data.getString("stability_situation");
                sql = sql + " stability_situation=encode('" + stability_situation + "','" + RIUtil.enContent + "'),";
            }
            String sqls = "update police_notification set " + sql + "create_time='" + create_time + "',isdelete=1  " +
                    "where id=" + id;
            logger.warn(sqls);
            mysql.update(sqls);

            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新警情通报", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(470006);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        logger.warn(String.valueOf(System.currentTimeMillis()));
        return back;
    }

    private static JSONObject NftDelete(JSONObject data, String remoteAddr) {
        String id = "";
        InfoModelHelper mysql = null;
        String opt_user = "";
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0)
            opt_user = data.getString("opt_user");
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(470003);
        }
        try {
            mysql = InfoModelPool.getModel();
            String sqls = "update police_notification set isdelete =2,delete_user='" + opt_user + "'," + "delete_time" +
                    "='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            logger.warn(sqls);
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除警情通报", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(470004);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

    private static JSONObject NftCreate(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String Pid = "";
            String situation = "";
            String crimal = "";
            String public_security = "";
            String fight_crimal = "";
            String isdelete = "1";
            String prostitution_gambling_clues = "";
            String other_clues = "";
            String stability_situation = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String date = "";
            String unit = "";
            if (data.containsKey("date") && data.getString("date").length() > 0) {
                date = data.getString("date");
                String sql2 =
                        "select create_user from police_notification where where isdelete = 1 and date ='" + date +
                                "' limit 1";
                logger.warn(sql2);
                create_user = mysql.query_one(sql2, "create_user");
                logger.warn(create_user);
                String sql = "delete from police_notification where date='" + date + "'";
                logger.warn(sql);
                mysql.update(sql);
            } else {
                return ErrNo.set(470009);
            }
            if (data.containsKey("Pid") && data.getString("Pid").length() > 0) {
                Pid = data.getString("Pid");
            } else {
                return ErrNo.set(470009);
            }

            if (data.containsKey("situation") && data.getString("situation").length() > 0) {
                situation = data.getString("situation");
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            }
            if (data.containsKey("crimal") && data.getString("crimal").length() > 0) {
                crimal = data.getString("crimal");
            }
            if (data.containsKey("public_security") && data.getString("public_security").length() > 0) {
                public_security = data.getString("public_security");
            }
            if (data.containsKey("fight_crimal") && data.getString("fight_crimal").length() > 0) {
                fight_crimal = data.getString("fight_crimal");
            }
            if (data.containsKey("prostitution_gambling_clues") && data.getString("prostitution_gambling_clues").length() > 0) {
                prostitution_gambling_clues = data.getString("prostitution_gambling_clues");
            }
            if (data.containsKey("other_clues") && data.getString("other_clues").length() > 0) {
                other_clues = data.getString("other_clues");
            }
            if (data.containsKey("stability_situation") && data.getString("stability_situation").length() > 0) {
                stability_situation = data.getString("stability_situation");
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                return ErrNo.set(447002);
            }

            String sql = "Insert police_notification (Pid,situation,crimal,public_security,fight_crimal," +
                    "prostitution_gambling_clues," +
                    "other_clues,stability_situation,create_time,create_user,isdelete,date,unit) " +
                    "values('" + Pid + "',encode('" + situation + "','" + RIUtil.enContent + "'),encode('" + crimal + "','" + RIUtil.enContent + "')," +
                    "encode('" + public_security + "','" + RIUtil.enContent + "'),encode('" + fight_crimal + "','" + RIUtil.enContent + "')," +
                    "encode('" + prostitution_gambling_clues + "','" + RIUtil.enContent + "'),encode('" + other_clues + "','" + RIUtil.enContent + "')," +
                    "encode('" + stability_situation + "','" + RIUtil.enContent + "'),'" + create_time + "','" + create_user + "','" + isdelete + "','" + date + "','" + unit + "')";
            logger.warn(sql);
            mysql.update(sql);
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建警情通报", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(470002);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }
}
