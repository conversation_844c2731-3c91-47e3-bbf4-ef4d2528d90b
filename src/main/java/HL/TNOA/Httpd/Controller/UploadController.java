package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.TNOAConf;
import com.alibaba.fastjson.JSONObject;
import net.coobird.thumbnailator.Thumbnails;
import okhttp3.*;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class UploadController extends HttpServlet {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());

    @RequestMapping(method = {RequestMethod.POST}, path = {"/upload"})
    @PassToken
    public JSONObject upload(HttpServletRequest request) throws Exception {

        request.setCharacterEncoding("UTF-8");
        // 1、创建一个DiskFileItemFactory工厂
        DiskFileItemFactory factory = new DiskFileItemFactory();
        //  2、创建一个文件上传解析器
        ServletFileUpload upload = new ServletFileUpload(factory);
        // 解决上传文件名的中文乱码
        upload.setHeaderEncoding("UTF-8");
        factory.setSizeThreshold(999999);
        // 设置内存的临界值为
        upload.setSizeMax(2000000000);
        //  设置上传的文件总的大小不能超过1G
        upload.setFileSizeMax(2000000000);
        File linshi = new File("/run/upload_tmp");
        //  当超过500K的时候，存到一个临时文件夹中
        if (!linshi.exists() && !linshi.isDirectory()) {
            linshi.mkdirs();
        }
        factory.setRepository(linshi);
        Calendar cal = Calendar.getInstance();


        String token = request.getHeader("token");
        Map<String, String[]> map = request.getParameterMap();
        String online = "";
        try {
            online = map.get("online")[0];
        } catch (Exception ex) {

        }

        String filePath =
                new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

        File oaupload1 = new File(TNOAConf.get("file", "img_path") + filePath);
        // logger.warn(SciConf.get("file", "upload_pic") + File.separator + filePath);
        //  这路径后面若需要能改成关联nas的路径
        if (!oaupload1.isDirectory()) {
            oaupload1.mkdirs();
        }

        File oaupload2 = new File(TNOAConf.get("file", "sou_path") + filePath);
        // logger.warn(SciConf.get("file", "upload_pic") + File.separator + filePath);
        //  这路径后面若需要能改成关联nas的路径
        if (!oaupload2.isDirectory()) {
            oaupload2.mkdirs();
        }
        File oaupload3 = new File(TNOAConf.get("file", "img_path") + filePath + "big/");
        // logger.warn(SciConf.get("file", "upload_pic") + File.separator + filePath);
        //  这路径后面若需要能改成关联nas的路径
        if (!oaupload3.isDirectory()) {
            oaupload3.mkdirs();
        }

        //  double size1 = getDirSize(oaupload1);
        //   拼接后的url
        String url = "";
        //  接收的初始文件名
        String fileName = "";


        //  nas_id
        List<Integer> ids = new ArrayList<>();

        int nas_id = 1;
        try {
            // 1. 得到 FileItem 的集合 items
            List<FileItem> items = upload.parseRequest(request);

            // 2. 遍历 items:
            for (FileItem item : items) {
                //    若是一个一般的表单域返回错误信息
                if (item.isFormField()) {
                    return ErrNo.set(210000);
                } else {
                    //       接收的初始文件名
                    fileName = System.currentTimeMillis() + "_" + item.getName();
                    logger.warn(fileName);
                    String suffix = "." + fileName.split("\\.")[1];
                    InputStream in = item.getInputStream();
                    byte[] buffer = new byte[1024];
                    int len = 0;
                    String filepath = "";


                    String filepath_small = "";
                    // 文件最终上传的位置
                    if (fileName.endsWith("jpg") || fileName.endsWith("png")) {

                        filepath = TNOAConf.get("file", "img_path") + filePath + "big/" + fileName;
                        filepath_small = TNOAConf.get("file", "img_path") + filePath + fileName;
                    } else {
                        filepath = TNOAConf.get("file", "img_path") + filePath + fileName;
                    }
                    // 拼接路径
//                    url = "http:192.168.1.111:1199/" + fileName;
                    OutputStream out = new FileOutputStream(filepath);
                    while ((len = in.read(buffer)) != -1) {
                        out.write(buffer, 0, len);
                    }
                    out.close();
                    in.close();
                    //缩略图
                    if (filepath_small.length() > 5) {
                        Thumbnails.of(filepath).scale(1f).outputQuality(0.3f).toFile(filepath_small);
                    }
                    String rela_id = "";

                    if (!online.equals("1")) {


                        //复制到
                      /*  BashExecutor bash = new BashExecutor();
                        long fileSize = new File(filepath).length();
                        logger.warn(filepath + "-size->" + fileSize);
                        if (fileSize > 100000000) {
                            logger.warn(String.valueOf(5 * 1000 * (int) (fileSize / 100000000)));
                            Thread.sleep(5 * 1000 * (int) (fileSize / 100000000));
                        }
                        String cmd = "cp -r " + filepath + " " + TNOAConf.get("file", "bk_path");
                        logger.warn(cmd);
                        bash.exec(cmd, -1, true);*/
                    } else {
                        String uu = TNOAConf.get("HttpServ", "online_url") + "upload";
                        try {
                            OkHttpClient client = new OkHttpClient().newBuilder().build();
                            MediaType mediaType = MediaType.parse("text/plain");

                            RequestBody body =
                                    new MultipartBody.Builder().setType(MultipartBody.FORM).addFormDataPart("origin",
                                            "dwOfNPqxXAQgse5B").addFormDataPart("token", token).addFormDataPart("file",
                                            filepath,
                                            RequestBody.create(MediaType.parse("application" + "/octet-stream"),
                                                    new File(filepath))).build();


                            Request ret = new Request.Builder().url(uu).method("POST", body).build();

                            Response response = client.newCall(ret).execute();

                            String back = response.body().string();
                            System.out.println(back + "-->");
                            JSONObject bone = JSONObject.parseObject(back).getJSONObject("data");
                            rela_id = bone.getString("fileId");


                        } catch (Exception ex) {
                            System.out.println(Lib.getTrace(ex));
                        }


                    }
//obs
                    String endPoint = "http://10.34.251.34:50101";
                    String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                    String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                    String bucketName = "obs-qjjc-tyyh";
                    ObsServer obsServ = new ObsServer();
                    String obsFileName = "hl/" + filePath + fileName;
                    System.out.println(obsFileName);
                    boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, filepath);
                    logger.warn(obsFileName + "-->" + ret);
//
//                    obsServ.showList(ak, sk, endPoint, bucketName);


                    InfoModelHelper info = InfoModelPool.getModel();
                    try {
                        String sql =
                                "insert into upload (nas_id,file_path,file_name,rela_id,type) values" + " ('1','" + filePath + "'," + "'" + fileName + "','" + rela_id + "','999')";

                        System.out.println(sql);
                        info.update(sql);
                        sql = "select * from upload where file_name='" + fileName + "' AND file_path='" + filePath +
                                "'";
                        List<JSONObject> result = info.query(sql);

                        JSONObject xJsonObject = result.get(0);
                        int id = xJsonObject.getIntValue("id");

                        //返回拼接后的url,id
                        ids.add(id);
                        logger.warn("id->" + id);
                    } finally {
                        InfoModelPool.putModel(info);
                    }
                }
            }
        } catch (FileUploadException e) {
            logger.error(Lib.getTrace(e));
            JSONObject ee = new JSONObject();
            ee.put("errno", e);
            return ErrNo.set(ee, 0);
        }

        JSONObject ret = new JSONObject();
        ret.put("id", ids);
        logger.warn("back->" + ret.toJSONString());
        return ErrNo.set(ret, 0);
    }


    //  上传文件
    @RequestMapping(method = {RequestMethod.POST}, path = {"/upload_info"})
    @PassToken
    public JSONObject uploadInfo(HttpServletRequest request, TNOAHttpRequest req) throws Exception {
        JSONObject back = ErrNo.set(0);
        JSONObject data = req.getRequestParams();
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                String id = data.getString("id");

                String sql = "select * from upload where id in (" + id + ")";
                List<JSONObject> list = mysql.query(sql);

                if (list.size() > 0) {
                    back.put("data", list);
                } else {
                    back.put("data", new ArrayList<>());
                }

                return back;
            } else {
                return ErrNo.set(210000);
            }
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(210000);
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    //	   ***************************计算文件夹大小**********************
    public double getDirSize(File file) {
        //判断文件是否存在
        if (file.exists()) {
            //如果是目录则递归计算其内容的总大小
            if (file.isDirectory()) {
                File[] children = file.listFiles();
                double size = 0;
                for (File f : children) {
                    size += getDirSize(f);
                }
                return size;
            } else {//如果是文件则直接返回其大小,以“兆”为单位
                double size = (double) file.length() / 1024 / 1024;
                return size;
            }
        } else {
            System.out.println("文件或者文件夹不存在，请检查路径是否正确！");
            return 0.0;
        }
    }

    //	    ***********************随机数******************************
    public StringBuffer RandomNum() {
        int maxNum = 62;
        int i;
        int count = 0;
        int randomlength;
        char[] str = {'z', 'x', 'c', 'v', 'b', 'n', 'm', 'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'q', 'w', 'e',
                'r', 't', 'y', 'u', 'i', 'o', 'p', 'Z', 'X', 'C', 'V', 'B', 'N', 'M', 'A', 'S', 'D', 'F', 'G', 'H',
                'J', 'K', 'L', 'Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '0', '1', '2', '3', '4', '5', '6',
                '7', '8', '9'};
        StringBuffer uuid = new StringBuffer();
        Random r = new Random();
        randomlength = r.nextInt(8) + 7;
        while (count < randomlength) {
            i = Math.abs(r.nextInt(maxNum));//最大61
            if (i >= 0 && i < str.length) {
                uuid.append(str[i]);
                count++;
            }
        }
        return uuid;
    }

//	********************************类结尾***************************


    private String savePic(String imgName, String imgData) throws Exception {

        SimpleDateFormat sdfM = new SimpleDateFormat("MM");
        SimpleDateFormat sdfY = new SimpleDateFormat("yyyy");

        InfoModelHelper infoModel = InfoModelPool.getModel();
        String filePath =
                TNOAConf.get("file", "img_path") + sdfY.format(new Date()) + "/" + sdfM.format(new Date()) + "/";


        int id = 0;
        String sql = "";

        if (imgName.endsWith("jpg") || imgName.endsWith("png")) {

            if (generateImage(imgData, imgName, filePath)) {

                sql = "insert into upload (nas_id,file_path,file_name) values" + " ('1','" + sdfY.format(new Date()) + "/" + sdfM.format(new Date()) + "/','" + imgName + "')";

                infoModel.update(sql);
                logger.warn(sql);

                sql = "select * from upload where file_name='" + imgName + "'";

                List<JSONObject> result = infoModel.query(sql);
                JSONObject xJsonObject = result.get(0);
                id = xJsonObject.getIntValue("id");
            } else {
                return "0";
            }
        } else if (imgName.endsWith(".mp4")) {
            if (generteMp4(imgData, imgName, filePath)) {

                sql = "insert into upload (nas_id,file_path,file_name) values" + " ('1','" + sdfY.format(new Date()) + "/" + sdfM.format(new Date()) + "/','" + imgName.replace(".mp4", ".m3u8") + "')";

                infoModel.update(sql);
                logger.warn(sql);

                sql = "select * from upload where file_name='" + imgName.replace(".mp4", ".m3u8") + "'";

                List<JSONObject> result = infoModel.query(sql);
                JSONObject xJsonObject = result.get(0);
                id = xJsonObject.getIntValue("id");
                new File(filePath + imgName).delete();
            } else {
                return "0";
            }
        } else {
            if (generateFile(imgData, imgName, filePath)) {

                sql = "insert into upload (nas_id,file_path,file_name) values" + " ('1','" + sdfY.format(new Date()) + "/" + sdfM.format(new Date()) + "/','" + imgName + "')";

                infoModel.update(sql);
                logger.warn(sql);

                sql = "select * from upload where file_name='" + imgName + "'";

                List<JSONObject> result = infoModel.query(sql);
                JSONObject xJsonObject = result.get(0);
                id = xJsonObject.getIntValue("id");
            } else {
                return "0";
            }
        }


        return String.valueOf(id);
    }

    private boolean generteMp4(String imgStr, String fileName, String file_path) {
        logger.warn(file_path + fileName);
        OutputStream out = null;
        try {
            if (imgStr == null) {
                logger.warn("imgstr null");
                return false;
            }
            File f = new File(file_path);
            if (!f.exists()) {
                logger.warn("==flord==:" + file_path);
                f.mkdirs();
                f.setWritable(true, false);
            }
            if (!new File(file_path + fileName).exists()) {

                new File(file_path + fileName);
            }

            // 解密
            byte[] b = Base64.getMimeDecoder().decode(imgStr);
            logger.warn(String.valueOf(b.length));
            // 处理数据
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;
                }
            }
            out = new FileOutputStream(file_path + fileName);
            out.write(b);
            out.flush();

           /* try {
                // 起作用的代码其实就下面这一行, 参数是linux中要执行的代码
                String newfileName = fileName.replace("mp4", "m3u8");
                Runtime.getRuntime().exec("./ffmpeg -i " + file_path + fileName + " -c:v libx264 -profile:v baseline
                -level 3.0 -b 600k -start_number 0 -hls_time 10 -hls_list_size 0 -f hls " + file_path + newfileName)
                .waitFor();
                logger.warn("./ffmpeg -i " + file_path + fileName + " -c:v libx264 -profile:v baseline -level 3.0 -b
                600k -start_number 0 -hls_time 10 -hls_list_size 0 -f hls " + file_path + newfileName);
            } catch (Exception e) {
                logger.error(Lib.getTrace(e));
                return false;
            }*/


            return true;
        } catch (IOException e) {
            logger.error(Lib.getTrace(e));
            return false;
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    logger.error(Lib.getTrace(e));
                }
            }
        }

    }

    private boolean generateFile(String imgStr, String fileName, String file_path) {
        logger.warn(file_path + fileName);
        OutputStream out = null;
        try {
            if (imgStr == null) {
                logger.warn("imgstr null");
                return false;
            }
            File f = new File(file_path);
            if (!f.exists()) {
                logger.warn("==flord==:" + file_path);
                f.mkdirs();
                f.setWritable(true, false);
            }
            if (!new File(file_path + fileName).exists()) {

                new File(file_path + fileName);
            }

            // 解密
            byte[] b = Base64.getMimeDecoder().decode(imgStr);
            logger.warn(String.valueOf(b.length));
            // 处理数据
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;
                }
            }
            out = new FileOutputStream(file_path + fileName);
            out.write(b);
            out.flush();
            return true;
        } catch (IOException e) {
            logger.error(Lib.getTrace(e));
            return false;
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    logger.error(Lib.getTrace(e));
                }
            }
        }

    }

    public boolean generateImage(String imgStr, String fileName1, String file_path) {
        SimpleDateFormat sdfM = new SimpleDateFormat("MM");
        SimpleDateFormat sdfY = new SimpleDateFormat("yyyy");
        String big_filePath = file_path + "big/";

        logger.warn(big_filePath + fileName1);
        OutputStream out = null;
        try {
            if (imgStr == null) {
                logger.warn("imgstr null");
                return false;
            }
            File f = new File(big_filePath);
            if (!f.exists()) {
                logger.warn("==flord==:" + big_filePath);
                f.mkdirs();
                f.setWritable(true, false);
            }
            if (!new File(big_filePath + fileName1).exists()) {

                new File(big_filePath + fileName1);
            }

            // 解密
            byte[] b = Base64.getMimeDecoder().decode(imgStr);
            // 处理数据
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;
                }
            }
            out = new FileOutputStream(big_filePath + fileName1);
            out.write(b);
            out.flush();
            String filePath =
                    TNOAConf.get("file", "img_path") + sdfY.format(new Date()) + "/" + sdfM.format(new Date()) + "/";

            //图片尺寸不变，压缩图片文件大小outputQuality实现,参数1为最高质量
            Thumbnails.of(big_filePath + fileName1).scale(1f).outputQuality(0.3f).toFile(file_path + fileName1);

            return true;
        } catch (IOException e) {
            logger.error(Lib.getTrace(e));
            return false;
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    logger.error(Lib.getTrace(e));
                }
            }
        }

    }
}
