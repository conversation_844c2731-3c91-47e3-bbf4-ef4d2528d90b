package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
public class ScreenDataOtherController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/screen_other"})
    @PassToken
    public JSONObject get_screenOther(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        String token = request.getHeader("token");
        try {

            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("get_task_gk")) {
                    // num2  560841 200-070100
                    return getTaskGK(data);
                } else if (opt.equals("get_task_level")) {
                    //num2 560842   200-070200~200-070204(待办 7天到期 逾期 7天下发 责任未落实)
                    return getTaskLevel(data);
                } else if (opt.equals("get_task_follow")) {
                    //chat_bar  层级任务 200-071300 0714 0715 0716 0702
                    return getTaskFollow(data);
                } else if (opt.equals("get_notice_list")) {
                    //notice_thing 200-0704 0705 0706 通知通告 工作通报 工作盯办
                    return getNoticeList(data);
                } else if (opt.equals("get_exam_rank")) {
                    //table
                    return GetExamRank(data);
                } else if (opt.equals("get_exam_short")) {
                    //table
                    return GetExamShort(data);
                } else if (opt.equals("get_zhidui_shouye")) {
                    //table
                    return getZhiduiShouYe(data);
                } else if (opt.equals("get_exam_rank_jz")) {
                    return GeExamRankJZ(data);
                } else if (opt.equals("get_top_ten_pcs")) {
                    // 积分前十派出所
                    return getTopTenPcs(data);
                } else if (opt.equals("get_buttom_ten_pcs")) {
                    // 积分后十名派出所
                    return getButtomTenPcs(data);
                } else if (opt.equals("get_top_twenty_zrq")) {
                    // 积分前二十 责任区
                    return getTopTwentyZrq(data);
                } else if (opt.equals("get_buttom_fifty_zrq")) {
                    // 积分后五十责任区
                    return getButtomFiftyZrq(data);
                } else if (opt.equals("get_score_rank_year")) {
                    return getScoreRankYear(data);
                } else if (opt.equals("get_short_table")) {
                    // 短板分析滚动列表
                    return getShortTable(data);
                } else if (opt.equals("get_zxqc_zgkb")) {
                    return GetZxqcZgkb(data);
                } else if (opt.equals("get_xsq")) {
                    return GetXsq(data, token);
                } else if (opt.equals("get_zf_score")) {
                    return getZfScore();
                } else if (opt.equals("get_jc_score")) {
                    return getJcScore();
                } else {
                    return ErrNo.set(465009);
                }
            } else {
                return ErrNo.set(465009);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private JSONObject GetXsq(JSONObject data, String token) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;

        try {
            String unit = data.getString("unit");
            String orgCode = "";
            int type = RIUtil.dicts.get(unit).getInteger("type");
            if (type == 21 || type == 22 || type == 27) {

                unit = unit.substring(0, 4) + "";
                orgCode = unit + "00000000";
            } else if (type == 23 || type == 24 || type == 28) {
                unit = unit.substring(0, 6) + "";
                orgCode = unit + "000000";
            } else if (type == 25) {

                unit = unit.substring(0, 8) + "";
                orgCode = unit + "0000";
            } else {

                unit = unit;
                orgCode = unit;

            }
            String start_time = data.getString("start_time");
            String end_time = data.getString("end_time");

            ora_hl = new OracleHelper("ora_bk_gl");

            String sql =
                    "select * from czqj_ybds.yw_xsq where czdwdm like '" + unit + "%' and kqsj>=TO_DATE('" + start_time + " 00:00:00','yyyy-MM-dd HH24:mi:ss') and kqsj<=TO_DATE('" + end_time + " 23:59:59','yyyy-MM-dd HH24:mi:ss') order " + "by czsj desc" + " offset 0 rows fetch next 10 rows only";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            JSONArray dets = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String img = "http://qjjcgzpt.czx.js/qjjc/hl/tymh/images/polices/" + one.getString("CZR") + ".jpg";

                String title = one.getString("CZRXM") + "|" + one.getString("CZDWMC").replace("责任区", "").replace(
                        "社区责任区", "");
                String content = "开始 " + one.getString("KQSJ");
                JSONArray lables = new JSONArray();
                JSONObject label = new JSONObject();
                label.put("id", 0);
                if (one.getString("JSSJ") == null || one.getString("JSSJ").length() == 0) {


                    label.put("name", "进行中");
                    label.put("color", "#ff8e15");
                } else {
                    String js = one.getString("JSSJ");
                    String ks = one.getString("KQSJ");
                    // logger.warn(one.toString());
                    long bt = RIUtil.dateToStamp(js) - RIUtil.dateToStamp(ks);
                    long h = bt / (60 * 60 * 1000) % 24;
                    long m = bt / (60 * 1000) % 60;


                    content = "时长：" + h + "小时" + m + "分钟";
                    label.put("name", "已结束");
                    label.put("color", "#219c1c");
                }
                lables.add(label);

                JSONObject det = new JSONObject();
                det.put("img", img);
                det.put("content", content);
                det.put("title", title);
                det.put("label", lables);

                JSONObject click = new JSONObject();
                click.put("type", "jump");
                String url =
                        "http://50.56.94.47/oauth2-app/login?redirect=/openPage/xsqGjMap&xsqId=" + one.getString("ID") + "&orgCode=" + one.getString("CZDWDM") + "&oauth2LoginToken=" + token;

                click.put("url", url);
                det.put("click", click);
                dets.add(det);


            }
            back.put("data", dets);
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();

        }
    }

    private JSONObject GetZxqcZgkb(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        try {
            JSONObject ret = ZXQCController.GetXDList(data);
            if (ret.containsKey("errno") && ret.getInteger("errno") == 0) {

                JSONArray datas = ret.getJSONArray("data");
                String ids = "";
                for (int i = 0; i < datas.size(); i++) {
                    JSONObject one = datas.getJSONObject(i);
                    String id = one.getString("id");
                    ids = ids + id + ",";
                }
                if (ids.endsWith(",")) {
                    ids = ids.substring(0, ids.length() - 1);
                }
                JSONArray bodys = new JSONArray();
                if (ids.length() > 2) {
                    MysqlHelper zxqc = null;
                    try {
                        zxqc = new MysqlHelper("mysql_zxqc");
                        String sql =
                                "select * from static_zxqc_det where  CIRCLE_ID IN('" + ids.replace(",", "','") + "')"
                                        + " " + "order" + " by " + "CZSJ desc limit 10";
                        // logger.warn(sql);
                        List<JSONObject> list = zxqc.query(sql);


                        for (int i = 0; i < list.size(); i++) {
                            JSONObject one = list.get(i);
                            String img = one.getString("IMG");
                            if (img.length() == 5) {
                                img = "0" + img;
                            }
                            img = "http://qjjcgzpt.czx.js/qjjc/hl/tymh/images/polices/" + img + ".jpg";
                            String time = one.getString("CZSJ");
                            String title = one.getString("MC");
                            String content =
                                    one.getString("NAME") + "|" + RIUtil.dicts.get(one.getString("ORG")).getString(
                                            "dict_name");
                            JSONArray labels = new JSONArray();
                            JSONObject lone = new JSONObject();
                            lone.put("id", 0);
                            lone.put("name", one.getString("TYPE"));
                            lone.put("color", "#FF1515");
                            labels.add(lone);
                            lone = new JSONObject();
                            lone.put("id", 1);
                            lone.put("name", one.getString("SUB") + one.getString("OPT"));
                            lone.put("color", "#4179e1");
                            labels.add(lone);
                            JSONObject det = new JSONObject();
                            det.put("img", img);
                            det.put("time", time);
                            det.put("title", title);
                            det.put("content", content);
                            det.put("label", labels);

                            bodys.add(det);

                        }
                    } catch (Exception ex) {
                        logger.error(Lib.getTrace(ex));

                    } finally {
                        zxqc.close();
                    }
                    back.put("data", bodys);
                    return back;


                } else {
                    return ret;
                }

            } else {
                return ret;
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        }


    }

    private JSONObject getShortTable(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");

            String unit = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(null, 2, "缺少参数");
            }

            String time = data.getString("time");

            int type = RIUtil.dicts.get(unit).getInteger("type");

            String sql = "";

//            if (type == 21 || type == 22 || type == 27) {
//                // 市局的人
//                sql += " type = '23'";
//            } else if (type == 23 || type == 24 || type == 28) {
//                //分局的人
//                sql += " pg_object = '" + unit.substring(0, 6) + "000000'";
//            } else if (type == 25) {
//                // 派出所的人
//                sql += " pg_object = '" + unit.substring(0, 8) + "000000'";
//            } else {
//                // 责任区
//                sql += " pg_object = '" + unit + "'";
//            }

            if (type == 21 || type == 22 || type == 27) {
                // 市局的人
                sql += " type = '23'";
            } else if (type == 23 || type == 24 || type == 28) {
                //分局的人
                sql += " type = '23'";
            } else if (type == 25) {
                // 派出所的人
                sql += " pg_object = '" + unit.substring(0, 6) + "000000'";
            } else {
                // 责任区
                sql += " pg_object = '" + unit.substring(0, 8) + "000000'";
            }

            String querySql =
                    " select * from kh_res where rank_level = '-1' and config_id != '1' and  `month`  = '" + time +
                            "' and " + sql;
            logger.warn(querySql);
            List<JSONObject> query = mysql.query(querySql);


            if (!query.isEmpty()) {
                Map<String, List<JSONObject>> pgObject =
                        query.stream().collect(Collectors.groupingBy(jsonObject -> jsonObject.getString("pg_object")));


                String s = "select id,plan from kh_history where time = '" + time + "'";
                List<JSONObject> p = mysql.query(s);
                JSONObject plan = p.get(0).getJSONObject("plan");
                Map<String, JSONObject> oldConfig = getOldConfig(plan);

                List<JSONObject> headList = new ArrayList<>();
                JSONObject head = new JSONObject();
                head.put("key", "org");
                head.put("value", "单位");
                headList.add(head);

                head = new JSONObject();
                head.put("key", "config");
                head.put("value", "评估项");
                headList.add(head);

                head = new JSONObject();
                head.put("key", "full_mark");
                head.put("value", "权重");

                head = new JSONObject();
                head.put("key", "score");
                head.put("value", "分数");
                headList.add(head);

                head = new JSONObject();
                head.put("key", "rank");
                head.put("value", "全市排名");
                headList.add(head);
                JSONObject bodyData;
                List<JSONObject> list = new ArrayList<>();
                JSONObject valueData;

                JSONObject click = new JSONObject();
                click.put("type", "jump");
                click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/check/Index");
                for (Map.Entry<String, List<JSONObject>> entry : pgObject.entrySet()) {
                    List<JSONObject> value = entry.getValue();
                    for (int i = 0; i < value.size(); i++) {
                        bodyData = new JSONObject();
                        if (i == value.size() - 1) {
                            bodyData.put("remark", true);
                        } else {
                            bodyData.put("remark", false);
                        }
                        JSONObject object = value.get(i);
                        valueData = new JSONObject();
                        valueData.put("value", RIUtil.dicts.get(object.getString("pg_object")).getString("dict_name"));
                        valueData.put("click", click);
                        bodyData.put("org", valueData);


                        valueData = new JSONObject();
                        valueData.put("value", oldConfig.get(object.getString("config_id")).getString("kh_name"));
                        bodyData.put("config", valueData);

                        valueData = new JSONObject();
                        valueData.put("value", oldConfig.get(object.getString("config_id")).getString("full_mark"));
                        bodyData.put("full_mark", valueData);

                        valueData = new JSONObject();
                        valueData.put("value", object.getString("score"));
                        bodyData.put("score", valueData);

                        valueData = new JSONObject();
                        valueData.put("value", object.getString("rank"));
                        bodyData.put("rank", valueData);

                        list.add(bodyData);
                    }
                }

                JSONObject res = new JSONObject();
                res.put("body", list);
                res.put("head", headList);
                back.put("data", res);
                return back;
            }


        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {

            if (mysql != null) {
                mysql.close();
            }
        }
        return back;

    }

    // 获取老的评估项目名称
    private Map<String, JSONObject> getOldConfig(JSONObject plan) {
        Map<String, JSONObject> map = new HashMap<>();
        JSONArray fj = plan.getJSONArray("fj");
        for (int i = 0; i < fj.size(); i++) {
            JSONObject jsonObject = fj.getJSONObject(i);
            JSONArray next = jsonObject.getJSONArray("next");
            for (int i1 = 0; i1 < next.size(); i1++) {
                JSONObject object = next.getJSONObject(i1);
                map.put(object.getString("id"), object);
            }
        }
        JSONArray pcs = plan.getJSONArray("pcs");
        for (int i = 0; i < pcs.size(); i++) {
            JSONObject jsonObject = pcs.getJSONObject(i);
            JSONArray next = jsonObject.getJSONArray("next");
            for (int i1 = 0; i1 < next.size(); i1++) {
                JSONObject object = next.getJSONObject(i1);
                map.put(object.getString("id"), object);
            }
        }

        JSONArray zrq = plan.getJSONArray("zrq");
        for (int i = 0; i < zrq.size(); i++) {
            JSONObject jsonObject = zrq.getJSONObject(i);
            JSONArray next = jsonObject.getJSONArray("next");
            for (int i1 = 0; i1 < next.size(); i1++) {
                JSONObject object = next.getJSONObject(i1);
                map.put(object.getString("id"), object);
            }
        }
        return map;
    }

    // 获取积分年排名
    private JSONObject getScoreRankYear(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");
            String unit = "";
            if (data.containsKey("unit") && StrUtil.isNotBlank(data.getString("unit"))) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(465009);
            }
            String year = "";
            if (data.containsKey("time") && StrUtil.isNotBlank(data.getString("time"))) {
                DateTime time = DateUtil.parse(data.getString("time"));
                year = String.valueOf(DateUtil.year(time));
            }
            Integer type = RIUtil.dicts.get(unit).getInteger("type");
            if (type == 21 || type == 22 || type == 27) {
                String sql =
                        " select avg(score) as score , `year` ,`month` from kh_res_t where config_id = '1' and " +
                                "type = '23' and `year` = '" + year + "' group by `month` , `year`";
                logger.warn(sql);
                List<JSONObject> query = mysql.query(sql);
                if (!query.isEmpty()) {
                    JSONArray array = new JSONArray();
                    query.forEach(jsonObject -> {
                        JSONObject object = new JSONObject();
                        object.put("count", jsonObject.getString("score"));
                        object.put("name", jsonObject.getString("year") + "-" + jsonObject.getString("month"));
                        array.add(object);
                    });
                    back.put("data", array);
                }
            } else {
                String sql = " select score , `year` ,`month` , rank, rank_fj, rank_pcs from kh_res_t where " +
                        "config_id" + " = '1' and type = '" + type + "' and pg_object = '" + unit + "' and `year` = " + "'" + year + "'";
                logger.warn(sql);
                List<JSONObject> query = mysql.query(sql);
                if (!query.isEmpty()) {
                    JSONArray array = new JSONArray();
                    query.forEach(jsonObject -> {
                        JSONObject object = new JSONObject();
                        object.put("count", jsonObject.getString("score"));
                        object.put("name", jsonObject.getString("year") + "-" + jsonObject.getString("month"));
                        String remark = "";
                        if (type == 23) {
                            remark = "市局排名:" + jsonObject.getString("rank");
                        }
                        if (type == 25) {
                            remark = "市局排名:" + jsonObject.getString("rank") + "  分局排名:" + jsonObject.getString(
                                    "rank_fj");
                        }
                        if (type == 26) {
                            remark = "市局排名:" + jsonObject.getString("rank") + "  分局排名:" + jsonObject.getString(
                                    "rank_fj") + "  派出所排名:" + jsonObject.getString("rank_pcs");
                        }
                        object.put("remark", remark);
                        array.add(object);
                    });
                    back.put("data", array);
                }
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            if (mysql != null) {
                mysql.close();
            }
        }
        return back;
    }

    private JSONObject GeExamRankJZ(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String jz = data.getString("jz");
        String lx = data.getString("rank_type");//1month2year
//        String time = data.getString("time").replace("-", "").substring(0, 6);
        String khMonth = data.getString("kh_month");
        data.put("time", khMonth);

        try {
            JSONObject ret = ExamineAnalysisController.getJzRank(data);
            if (ret.getInteger("errno") == 0) {
                JSONObject datas = ret.getJSONObject("data");

                JSONArray heads = datas.getJSONArray("head");
                String key = "320400140000";
                for (int i = 0; i < heads.size(); i++) {
                    JSONObject one = heads.getJSONObject(i);
                    String val = one.getString("value");
                    key = one.getString("key");
                    if (key.equals(jz)) {
                        break;
                    }
                }

                JSONArray dets = new JSONArray();
                JSONArray bodys = datas.getJSONArray("body");

                // 单击跳转事件
                JSONObject oneClick = new JSONObject();
                oneClick.put("type", "jump");
                oneClick.put("url", "http://qjjcgzpt.czx.js/web/index.html#/check/Static");

                for (int i = 0; i < bodys.size(); i++) {
                    JSONObject one = bodys.getJSONObject(i);
                    //  System.out.println(one);

                    JSONObject jzpm = one.getJSONObject(key);
                    String count = "0";

                    // 排名
                    String remark = "";
                    if (lx.equals("1")) {
                        count = jzpm.getString("month_score");
                        remark = "排名:" + jzpm.getString("month_rank");
                    } else {
                        count = jzpm.getString("year_score");
                        remark = "排名:" + jzpm.getString("year_rank");
                    }

                    JSONObject det = new JSONObject();

                    String t = one.getString("type");

                    JSONObject dpclick = new JSONObject();
                    if (!t.equals("26")) {
                        dpclick.put("url", "/screen_other");
                        JSONObject cols = new JSONObject();
                        cols.put("code", one.getString("code"));
                        det.put("name", RIUtil.dicts.get(one.getString("code")).getString("dict_name"));
                        cols.put("name", RIUtil.dicts.get(one.getString("code")).getString("dict_name"));
                        cols.put("rank_type", lx);
                        cols.put("time", data.getString("time"));


                        JSONObject opts = GetOpts("/screen_other", "get_exam_rank_jz", cols);
                        logger.warn(opts.toString());
                        dpclick.put("opt", opts);
                        dpclick.put("type", "next");
                        dpclick.put("name", RIUtil.dicts.get(one.getString("code")).getString("dict_name"));
                        det.put("dpclick", dpclick);
                        det.put("id", one.getString("code"));
                    }
                    // 单机实践
                    det.put("click", oneClick);

                    det.put("id", one.getString("code"));
                    det.put("name", one.getString("name"));
                    det.put("count", count);
                    det.put("remark", remark);
                    dets.add(det);
                }

                List<JSONObject> collect = dets.toJavaList(JSONObject.class)
                        // 从高到低排序
                        .stream().sorted((a, b) -> Double.compare(b.getDouble("count"), a.getDouble("count"))).collect(Collectors.toList());

                back.put("data", collect);
                // 测试排序
//                back.put("test",collect);
                return back;
            } else {
                return ret;
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }

    }

    private JSONObject GetExamShort(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        JSONArray heads = new JSONArray();
        JSONObject head = new JSONObject();
        head.put("key", "kh_name");
        head.put("value", "名称");
        heads.add(head);

        head = new JSONObject();
        head.put("key", "score");
        head.put("value", "分数");
        heads.add(head);

        head = new JSONObject();
        head.put("key", "rank");
        head.put("value", "全市排名");
        heads.add(head);


        try {
            String unit = data.getString("unit");
            String t = RIUtil.dicts.get(unit).getString("type");

            if (t.equals("21") || t.equals("22") || t.equals("27")) {
                data.put("code", "320481000000");
            }
            String startTime = data.getString("start_time").substring(0, 7);
            String cMonth = new SimpleDateFormat("yyyy-MM").format(new Date());
            if (startTime.equals(cMonth)) {
                String kh_year = data.getString("kh_month").substring(0, 4);
                String khM = data.getString("kh_month").substring(4, 6);
                startTime = kh_year + "-" + khM + "-01";
                String end = RIUtil.getLMonthEnd(startTime).substring(0, 10);

                data.put("start_time", startTime);
                data.put("end_time", end);
            }


            JSONArray dets = new JSONArray();
            JSONObject ret = ExamineAnalysisController.getShortDis(data, "");
            if (ret.getInteger("errno") == 0) {
                JSONArray datas = ret.getJSONArray("data");
                if (datas.size() == 0) {
                    datas = ret.getJSONArray("data");
                }
                for (int i = 0; i < datas.size(); i++) {
                    JSONObject one = datas.getJSONObject(i);
                    //    logger.warn(one.toString());
                    JSONObject det = new JSONObject();

                    String kh_name = one.getString("kh_name");
                    String score = one.getString("score");
                    String rank = one.getString("rank");


                    JSONObject val = new JSONObject();
                    val.put("value", kh_name);
                    JSONObject click = new JSONObject();
                    click.put("type", "jump");
                    click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/check/Index");
                    val.put("click", click);
                    det.put("kh_name", val);

                    val = new JSONObject();
                    val.put("value", score);
                    det.put("score", val);

                    val = new JSONObject();
                    val.put("value", rank);
                    det.put("rank", val);
                    dets.add(det);


                }
                JSONObject dd = new JSONObject();
                dd.put("head", heads);
                dd.put("body", dets);

                back.put("data", dd);

                return back;
            } else {
                return ret;
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private JSONObject GetExamRank(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        try {

            String style = data.getString("style");
            String startTime = data.getString("start_time").substring(0, 7);
            logger.warn(startTime);
            String cMonth = new SimpleDateFormat("yyyy-MM").format(new Date());
            if (startTime.equals(cMonth)) {
                String kh_year = data.getString("kh_month").substring(0, 4);
                String khM = data.getString("kh_month").substring(4, 6);
                startTime = kh_year + "-" + khM + "-01";
                String end = RIUtil.getLMonthEnd(startTime).substring(0, 10);

                data.put("start_time", startTime);
                data.put("end_time", end);
            }

            logger.warn(data.toString());
            // 单机跳转
            JSONObject oneClick = new JSONObject();
            oneClick.put("type", "jump");
            oneClick.put("url", "http://qjjcgzpt.czx.js/web/index.html#/check/Static");

            JSONArray dets = new JSONArray();
            JSONObject ret = ExamineAnalysisController.getRank(data, "");
            //logger.warn("get_exam_rank--->" + ret.toString());
            if (ret.getInteger("errno") == 0) {

                if (style.equals("chat_bar")) {

                    JSONArray datas = ret.getJSONArray("data_next");
                    if (datas.size() == 0) {
                        datas = ret.getJSONArray("data");
                    }
                    for (int i = 0; i < datas.size(); i++) {
                        JSONObject one = datas.getJSONObject(i);
                        JSONObject det = new JSONObject();
                        String name = one.getString("pg_object_name");
                        String count = one.getString("score");
                        String unit = one.getString("pg_object");
                        String t = one.getString("type");

                        JSONObject dpclick = new JSONObject();

                        if (!t.equals("26")) {
                            dpclick.put("url", "/screen_other");
                            JSONObject cols = new JSONObject();
                            cols.put("code", unit);
                            cols.put("name", name);
                            cols.put("rank_type", data.getString("rank_type"));
                            cols.put("start_time", data.getString("start_time"));
                            cols.put("end_time", data.getString("end_time"));
                            cols.put("style", data.getString("style"));
                            JSONObject opts = GetOpts("/screen_other", "get_exam_rank", cols);
                            // logger.warn(opts.toString());
                            dpclick.put("opt", opts);
                            dpclick.put("type", "next");
                            dpclick.put("name", RIUtil.dicts.get(unit).getString("dict_name"));
                            det.put("dpclick", dpclick);
                            det.put("click", oneClick);
                            det.put("id", unit);
                        }
                        det.put("name", RIUtil.dicts.get(unit).getString("dict_name"));
                        det.put("count", count);

                        dets.add(det);

                    }


                    back.put("data", dets);
//                    back.put("data", parseDataWithAddButton(dets));

                } else if (style.equals("table")) {
//                    logger.warn("table");

                    JSONArray heads = new JSONArray();
                    JSONObject head = new JSONObject();
                    head.put("key", "num");
                    head.put("value", "序号");
                    heads.add(head);

                    head = new JSONObject();
                    head.put("key", "code");
                    head.put("value", "单位名称");
                    heads.add(head);

                    head = new JSONObject();
                    head.put("key", "score");
                    head.put("value", "分数");
                    heads.add(head);
                    head = new JSONObject();
                    head.put("key", "rank");
                    head.put("value", "全市排名");
                    heads.add(head);

                    head = new JSONObject();
                    head.put("key", "rank_fj");
                    head.put("value", "分局排名");
                    heads.add(head);

                    head = new JSONObject();
                    head.put("key", "rank_pcs");
                    head.put("value", "派出所排名");
                    heads.add(head);


                    JSONArray datas = ret.getJSONArray("data_next");
                    if (datas.size() == 0) {
                        datas = ret.getJSONArray("data");
                    }
//                    logger.warn("data{}",datas);
                    for (int i = 0; i < datas.size(); i++) {
                        JSONObject one = datas.getJSONObject(i);
                        JSONObject det = new JSONObject();
                        String name = one.getString("pg_object_name");
                        String count = one.getString("score");
                        String unit = one.getString("pg_object");
                        String t = one.getString("type");
                        String num = String.valueOf(i + 1);


                        JSONObject val = new JSONObject();
                        val.put("value", num);
                        det.put("num", val);


                        val = new JSONObject();
                        val.put("value", name);
                        JSONObject dpclick = new JSONObject();
                        if (!t.equals("26")) {
                            dpclick.put("url", "/screen_other");
                            JSONObject cols = new JSONObject();
                            cols.put("code", unit);
                            cols.put("start_time", data.getString("start_time"));
                            cols.put("end_time", data.getString("end_time"));
                            cols.put("style", data.getString("style"));
                            cols.put("rank_type", data.getString("rank_type"));
                            JSONObject opts = GetOpts("/screen_other", "get_exam_rank", cols);
                            // logger.warn(opts.toString());
                            dpclick.put("opt", opts);
                            dpclick.put("type", "next");
                            dpclick.put("name", RIUtil.dicts.get(unit).getString("dict_name"));
                            val.put("dpclick", dpclick);
                            val.put("click", oneClick);
                        }
                        det.put("code", val);

                        val = new JSONObject();
                        val.put("value", count);
                        det.put("score", val);

                        val = new JSONObject();
                        val.put("value", one.getString("rank"));
                        det.put("rank", val);

                        val = new JSONObject();
                        val.put("value", one.getString("rank_fj"));
                        det.put("rank_fj", val);

                        val = new JSONObject();
                        val.put("value", one.getString("rank_pcs"));
                        det.put("rank_pcs", val);

                        dets.add(det);

                    }

                    JSONObject das = new JSONObject();
                    das.put("body", dets);
                    das.put("head", heads);
                    back.put("data", das);

                }
                return back;
            } else {
                logger.warn("参数不对");
                return ret;
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }

    }


    //积分排名柱状图添加 右上角 跳转button
    private JSONObject parseDataWithAddButton(JSONArray data) {
        JSONObject result = new JSONObject();
        result.put("data", data);

        JSONArray button = new JSONArray();
        // 短板
        JSONObject db = new JSONObject();
        db.put("label", "短板");
        JSONObject dbClick = new JSONObject();
        dbClick.put("type", "jump");
        dbClick.put("url", "http://qjjcgzpt.czx.js/web/index.html#/check/Index");
        db.put("click", dbClick);
        button.add(db);

        // 统计
        JSONObject tj = new JSONObject();
        tj.put("label", "统计");
        JSONObject tjClick = new JSONObject();
        tjClick.put("type", "jump");
        tjClick.put("url", "http://qjjcgzpt.czx.js/web/index.html#/check/Static");
        tj.put("click", tjClick);
        button.add(tj);
        result.put("button", button);
        return result;
    }


    private JSONObject getTopTenPcs(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");
            if (data.containsKey("kh_month") && StrUtil.isNotBlank(data.getString("kh_month"))) {
                String sql =
                        " select * from kh_res where  config_id = '1' and type = '25' and rank <= 10 and month " +
                                "=" + " '" + data.getString("kh_month") + "' order by rank asc";
                logger.warn(sql);
                List<JSONObject> query = mysql.query(sql);
                if (!query.isEmpty()) {
                    JSONArray res = new JSONArray();
                    for (JSONObject jsonObject : query) {
                        JSONObject det = new JSONObject();
                        det.put("id", jsonObject.getString("pg_object"));
                        det.put("count", jsonObject.getString("score"));
                        det.put("name", RIUtil.dicts.get(jsonObject.getString("pg_object")).getString("dict_name"));
                        det.put("rank", jsonObject.getString("rank"));
                        res.add(det);
                    }
                    back.put("data", res);
                    return back;
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            if (mysql != null) {
                mysql.close();
            }
        }
        return back;
    }

    private JSONObject getTopTwentyZrq(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");
            if (data.containsKey("kh_month") && StrUtil.isNotBlank(data.getString("kh_month"))) {
                String sql =
                        " select * from kh_res where  config_id = '1' and type = '26' and rank <= 20 and month " +
                                "=" + " '" + data.getString("kh_month") + "' order by rank asc";
                logger.warn(sql);
                List<JSONObject> query = mysql.query(sql);
                if (!query.isEmpty()) {
                    JSONArray res = new JSONArray();
                    for (JSONObject jsonObject : query) {
                        JSONObject det = new JSONObject();
                        det.put("id", jsonObject.getString("pg_object"));
                        det.put("count", jsonObject.getString("score"));
                        det.put("name", RIUtil.dicts.get(jsonObject.getString("pg_object")).getString("remark"));
                        det.put("rank", jsonObject.getString("rank"));
                        res.add(det);
                    }
                    back.put("data", res);
                    return back;
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            if (mysql != null) {
                mysql.close();
            }
        }
        return back;
    }


    // 获取倒数后十派出所
    private JSONObject getButtomTenPcs(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");
            if (data.containsKey("kh_month") && StrUtil.isNotBlank(data.getString("kh_month"))) {

                String sql =
                        " select max(rank) as rank from kh_res where config_id = '1' and type = '25' and month " +
                                "=" + " '" + data.getString("kh_month") + "'";
                logger.warn(sql);
                List<JSONObject> query = mysql.query(sql);
                if (!query.isEmpty()) {
                    int rank = query.get(0).getInteger("rank") - 10;
                    sql = " select * from kh_res where config_id = '1' and type = '25' and rank >= '" + rank + "' " + "and" + " month = '" + data.getString("kh_month") + "' order by rank asc";
                    logger.warn(sql);
                    List<JSONObject> list = mysql.query(sql);
                    if (!list.isEmpty()) {
                        JSONArray res = new JSONArray();
                        for (JSONObject jsonObject : list) {
                            JSONObject det = new JSONObject();
                            det.put("id", jsonObject.getString("pg_object"));
                            det.put("count", jsonObject.getString("score"));
                            det.put("name", RIUtil.dicts.get(jsonObject.getString("pg_object")).getString("dict_name"));
                            det.put("rank", jsonObject.getString("rank"));
                            res.add(det);
                        }
                        back.put("data", res);
                    }
                }
            }

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            if (mysql != null) {
                mysql.close();
            }
        }
        return back;
    }

    private JSONObject getButtomFiftyZrq(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");
            if (data.containsKey("kh_month") && StrUtil.isNotBlank(data.getString("kh_month"))) {

                String sql =
                        " select max(rank) as rank from kh_res where config_id = '1' and type = '26' and month " +
                                "=" + " '" + data.getString("kh_month") + "'";
                logger.warn(sql);
                List<JSONObject> query = mysql.query(sql);
                if (!query.isEmpty()) {
                    logger.warn(query.get(0).toString());
                    int rank = query.get(0).getIntValue("rank") - 50;
                    sql = " select * from kh_res where config_id = '1' and type = '26' and rank >= '" + rank + "' " + "and" + " month = '" + data.getString("kh_month") + "' order by rank asc";
                    logger.warn(sql);
                    List<JSONObject> list = mysql.query(sql);
                    if (!list.isEmpty()) {
                        JSONArray res = new JSONArray();
                        for (JSONObject jsonObject : list) {
                            JSONObject det = new JSONObject();
                            det.put("id", jsonObject.getString("pg_object"));
                            det.put("count", jsonObject.getString("score"));
                            det.put("name", RIUtil.dicts.get(jsonObject.getString("pg_object")).getString("remark"));
                            det.put("rank", jsonObject.getString("rank"));
                            res.add(det);
                        }
                        back.put("data", res);
                    }
                }
            }

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            if (mysql != null) {
                mysql.close();
            }
        }
        return back;
    }


    private JSONObject getNoticeList(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        try {

            String opt_user = data.getString("opt_user");
            //  data.put("user_id", opt_user);


            JSONObject ret = NoticeController.getNoticeList(data);
            //       logger.warn(ret.toString());
            JSONArray dets = new JSONArray();
            if (ret.getInteger("errno") == 0) {

                if (ret.getInteger("count") > 0) {
                    JSONArray datas = ret.getJSONArray("data");

                    for (int i = 0; i < datas.size(); i++) {
                        JSONObject one = datas.getJSONObject(i);
                        //logger.warn(one.toString());
                        JSONObject det = new JSONObject();
                        String title = one.getString("title");
                        String notice_time = one.getString("notice_time");
                        String oldAuthor = one.getString("old_author");
                        if (oldAuthor != null && oldAuthor.length() > 0) {
                            title = "[转]" + title;
                        }
                        String comment = one.getString("comment");
                        String reading = one.getString("reading");
                        JSONArray readed = one.getJSONArray("readed");
                        String rded = one.getString("readed");

                        int readeds = 0;
                        if (!ObjectUtil.isEmpty(readed))
                            readeds = readed.size();
                        String content = "☑已读 " + readeds + " ✉未读 " + reading + "✎评论数 " + comment;
                        det.put("title", title);
                        det.put("content", content);
                        det.put("time", notice_time);
                        JSONArray labels = new JSONArray();

                        try {
                            JSONObject aone = one.getJSONObject("subType");


                            String id = aone.getString("id");
                            String name = aone.getString("dict_name");
                            String color = aone.getString("color");
                            JSONObject done = new JSONObject();
                            done.put("id", id);
                            done.put("name", name);
                            done.put("color", color);
                            labels.add(done);

                        } catch (Exception ex) {
                        }
                        if (rded.contains(opt_user)) {
                            JSONObject done = new JSONObject();
                            done.put("id", "id");
                            done.put("name", "已读");
                            done.put("color", "#00ff00");
                            labels.add(done);
                        } else {
                            JSONObject done = new JSONObject();
                            done.put("id", "id");
                            done.put("name", "未读");
                            done.put("color", "#ff0000");
                            labels.add(done);
                        }

                        det.put("label", labels);
                        String notice_id = one.getString("id");
                        JSONObject click = new JSONObject();
                        click.put("type", "jump");
                        click.put("url", TNOAConf.get("HttpServ", "notice_url") + notice_id);
                        det.put("click", click);
                        det.put("id", notice_id);

                        dets.add(det);
                    }
                }
                back.put("data", dets);
                back.put("count", ret.getInteger("count"));
            } else {
                return ret;
            }

            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private JSONObject getTaskFollow(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        JSONArray heads = new JSONArray();
        JSONObject head = new JSONObject();
        head.put("key", "num");
        head.put("value", "序号");
        heads.add(head);

        head = new JSONObject();
        head.put("key", "task_name");
        head.put("value", "任务名称");
        heads.add(head);

        head = new JSONObject();
        head.put("key", "count");
        head.put("value", "数量");
        heads.add(head);

        JSONObject click = new JSONObject();
        click.put("type", "jump");
        click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/myTask/Index");
        JSONArray bodys = new JSONArray();
        try {
            JSONObject ret = QQBTaskController.GetFollow(data);
            logger.warn(ret.toString());
            if (ret.getInteger("errno") == 0) {

                if (ret.getInteger("count") > 0) {
                    JSONArray datas = ret.getJSONArray("data");

                    for (int i = 0; i < datas.size(); i++) {
                        JSONObject det = new JSONObject();
                        JSONObject one = datas.getJSONObject(i);
                        //  System.out.println(one);

                        int num = i + 1;

                        JSONObject dd = new JSONObject();
                        dd.put("value", num);
                        det.put("num", dd);

                        String task_name = one.getString("TASK_NAME");
                        String type = one.getString("TYPE");
                        if (type.equals("2")) {

                            task_name = "【分局】" + task_name;
                        } else if (type.equals("3")) {
                            task_name = "【派出所】" + task_name;
                        } else {
                            task_name = "【市局】" + task_name;
                        }

                        dd = new JSONObject();
                        dd.put("value", task_name);
                        dd.put("click", click);
                        det.put("task_name", dd);

                        String count = one.getString("count");
                        dd = new JSONObject();
                        dd.put("value", count);
                        det.put("count", dd);

                        bodys.add(det);

                    }
                }
                JSONObject d = new JSONObject();
                d.put("head", heads);
                d.put("body", bodys);
                back.put("data", d);
                back.put("count", ret.getInteger("count"));
                return back;

            } else {
                return ret;
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        }
    }

    private JSONObject getTaskLevel(JSONObject data) {

        JSONObject back = ErrNo.set(0);

        try {

            String lx = data.getString("lx");
            data.remove("type");
            data.put("type", lx);


            JSONObject ret = QQBTaskController.GetMyLevel(data);
            if (ret.getInteger("errno") == 0) {

                JSONObject d = ret.getJSONObject("data");
                JSONArray datas = d.getJSONArray("data");
                JSONArray rets = new JSONArray();
                JSONObject click = new JSONObject();
                click.put("type", "jump");
                click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/myTask/Index");


                for (int i = 0; i < datas.size(); i++) {
                    JSONObject dpclick = new JSONObject();
                    dpclick.put("type", "next");

                    JSONObject one = datas.getJSONObject(i);
                    //    logger.warn(one.toString());
                    JSONObject det = new JSONObject();
                    det.put("name", one.getString("dict_name"));
                    det.put("count", one.getString("count"));
                    det.put("id", one.getString("id"));
                    det.put("click", click);
                    String cj = one.getString("cj");
                    if (!cj.equals("26")) {
                        dpclick.put("url", "/screen_other");
                        dpclick.put("name", RIUtil.dicts.get(det.getString("id")).getString("dict_name"));
                        JSONObject cols = new JSONObject();
                        cols.put("unit", det.getString("id"));
                        cols.put("name", RIUtil.dicts.get(det.getString("id")).getString("dict_name"));
                        cols.put("lx", lx);
                        JSONObject opts = GetOpts("/screen_other", "get_task_level", cols);
                        logger.warn(opts.toString());
                        dpclick.put("opt", opts);
                        det.put("dpclick", dpclick);
                    }
                    //  logger.warn(det.toString());
                    rets.add(det);

                }


                back.put("data", rets);
                return back;

            } else {
                return ret;
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }

    }

    private JSONObject GetOpts(String url, String opt, JSONObject cols) {

        JSONObject opts = new JSONObject();
        opts.put("url", url);
        opts.put("opt", opt);
        opts.put("opt_user", "$opt_user$");

        opts.putAll(cols);
        return opts;
    }

    private JSONObject getTaskGK(JSONObject data) {

        JSONObject back = ErrNo.set(0);
        String qqb_id = "";
        String opt_user = "";
        String unit = "";
        try {
            opt_user = data.getString("opt_user");
            unit = data.getString("unit");
            qqb_id = data.getString("qqb_id");

            JSONObject ret = QQBTaskController.GetMyTaskGKList(data);
            if (ret.getInteger("errno") == 0) {

                JSONObject datas = ret.getJSONObject("data");
                JSONArray rets = new JSONArray();
                JSONObject click = new JSONObject();
                click.put("type", "jump");
                click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/myTask/Index");
                for (Map.Entry<String, Object> o : datas.entrySet()) {
                    JSONObject det = new JSONObject();
                    String name = o.getKey();
                    String v = String.valueOf(o.getValue());

                    if (name.equals("unFin")) {
                        det.put("title", "待办任务");
                        det.put("icon", "loadingImg");

                    } else if (name.equals("near7")) {
                        det.put("title", "7天到期");
                        det.put("icon", "sevenImg");
                    } else if (name.equals("delay")) {
                        det.put("title", "逾期未执行");
                        det.put("icon", "overdueImg");
                    } else if (name.equals("7down")) {
                        det.put("title", "近7天下发");
                        det.put("icon", "dayImg");
                    } else if (name.equals("fin")) {
                        det.put("title", "已完成任务");
                        det.put("icon", "successImg");
                    }
                    if (det.containsKey("title")) {

                        det.put("count", v);
                        det.put("click", click);
                        logger.warn(det.toString());
                        rets.add(det);
                    }

                }
                back.put("data", rets);

                return back;
            } else {
                return ret;
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }

    }

    private String GetQQBUserId(String unit, String user_id, String job_name) {
        MysqlHelper qqb_user = null;
        String exeid = "";
        try {
            qqb_user = new MysqlHelper("mysql_qqb_user");

            JSONObject done = RIUtil.dicts.get(unit);
            int type = done.getInteger("type");
            int t = 0;
            if (type == 21 || type == 22 || type == 27) {
                t = 1;
            } else if (type == 23 || type == 24 || type == 28) {
                t = 2;
            } else if (type == 25 && (job_name.contains("所长") || job_name.contains("教导员"))) {
                t = 3;
            } else {
                t = 13;
            }

            String sql = "select id from auth.SYS_AUTH_USER where idcard_no='" + user_id + "' and deleted=0 and " +
                    "(IS_TEMPORARY  is null or IS_TEMPORARY <>1)" +

                    " and " + "type=" + t;
            logger.warn(sql);
            exeid = qqb_user.query_one(sql, "ID");

        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
        } finally {
            qqb_user.close();


        }
        return exeid;
    }

    private JSONObject getZhiduiShouYe(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        List<JSONObject> ret = new ArrayList<>();
        List<JSONObject> dets = new ArrayList<>();
        MysqlHelper mysql = null;
        String sql = "";
        String classid = "";
        try {
            mysql = new MysqlHelper("mysql");
            String opt_user = data.getString("opt_user");
            //  data.put("user_id", opt_user);
            String url = "http://www.czx.js/sites/rkzd/";
            if (data.containsKey("classid") && data.getString("classid").length() > 0) {
                classid = data.getString("classid");
                if (classid.equals("14")) {
                    url = url + "lingdaopishi/";
                } else if (classid.equals("25")) {
                    url = url + "jingyanjiaoliu/";
                }
            } else {
                return ErrNo.set(null, -1, "缺少参数");
            }


            JSONObject click = new JSONObject();
            click.put("type", "jump");
            click.put("url", url);


            sql = "select * from main_news where classid = '" + classid + "' order by create_time desc limit 5";
            ret = mysql.query(sql);
            if (ret.size() > 0) {
                for (int i = 0; i < ret.size(); i++) {
                    JSONObject one = ret.get(i);
//                    logger.warn(one.toString());
                    JSONObject det = new JSONObject();
                    String title = one.getString("title");
                    String createTime = one.getString("create_time");
                    String content = one.getString("smalltext");
                    String richText = one.getString("newstext");
                    String id = one.getString("id");

                    det.put("title", title);
                    det.put("content", content);
                    det.put("time", createTime);
                    det.put("richText", richText);
                    det.put("id", id);

                    det.put("click", click);

                    dets.add(det);
                }
                back.put("data", dets);
                back.put("count", ret.size());
            }
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            mysql.close();
        }
    }


    private JSONObject getJcScore() {
        JSONObject back = ErrNo.set(0);

        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");
            String sql = "select org_name as name , jc_score as score from kh_zf_custom_import order by jc_score desc";

            List<JSONObject> list = mysql.query(sql);

            List<JSONObject> objectList = new ArrayList<>();

            JSONObject oneClick = new JSONObject();
            oneClick.put("type", "jump");
            oneClick.put("url", "http://qjjcgzpt.czx.js/web/index.html#/check/Static");

            for (JSONObject jsonObject : list) {
                JSONObject json = new JSONObject();
                json.put("count", jsonObject.getString("jc_score"));
                json.put("name", jsonObject.getString("org_name"));
                json.put("click", oneClick);
                objectList.add(json);
            }

            back.put("data", objectList);

        } catch (Exception e) {
            back.put("error", e.getMessage());
            logger.error(Lib.getTrace(e));
        } finally {
            mysql.close();
        }
        return back;
    }

    private JSONObject getZfScore() {
        JSONObject back = ErrNo.set(0);

        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");
            String sql = "select org_name as name , zf_score as score from kh_zf_custom_import order by zf_score desc";

            List<JSONObject> list = mysql.query(sql);
            List<JSONObject> objectList = new ArrayList<>();

            JSONObject oneClick = new JSONObject();
            oneClick.put("type", "jump");
            oneClick.put("url", "http://qjjcgzpt.czx.js/web/index.html#/check/Static");

            for (JSONObject jsonObject : list) {
                JSONObject json = new JSONObject();
                json.put("count", jsonObject.getString("zf_score"));
                json.put("name", jsonObject.getString("org_name"));
                json.put("click", oneClick);
                objectList.add(json);
            }
            back.put("data", objectList);
        } catch (Exception e) {
            back.put("error", e.getMessage());
            logger.error(Lib.getTrace(e));
        } finally {
            mysql.close();
        }
        return back;
    }


    private JSONObject getTypeAPcsRank(JSONObject data) {
        JSONObject res = new JSONObject();

        MysqlHelper mysql = null;
        try {

            String sql = "select * from dict di left join kh_det kh on di.id = kh.id  ";

            List<JSONObject> query = mysql.query(sql);
            for (JSONObject jsonObject : query) {
                String dictName = jsonObject.getString("dict_name");
                String id = jsonObject.getString("id");
                String name = jsonObject.getString("name");
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            mysql.close();
        }


        return res;
    }
}
