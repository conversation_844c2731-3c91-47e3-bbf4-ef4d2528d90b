package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.Export.ExportInterface;
import HL.TNOA.Lib.Export.ExportXlsxHelper;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.eclipse.jetty.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static HL.TNOA.Lib.RIUtil.RealDictNames;


@RestController
public class XxzxController {
    private static Logger logger = LoggerFactory.getLogger(XxzxController.class);

    @RequestMapping(method = {RequestMethod.POST}, path = {"/xxzx_count"})
    public JSONObject xxzx_count(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        InfoModelHelper mysql = null;
        try {

            mysql = request.openInfoImpl();
            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("get_count")) {
                    return get_count(data);
                } else if (opt.equals("get_list")) {
                    return get_list(data);
                } else {
                    return ErrNo.set(505003);
                }
            } else {
                return ErrNo.set(505003);
            }
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private JSONObject get_count(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String count_type = data.getString("count_type");

        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String unit = "";
            String type = "";//统计类型
            String reading = "";//0未读1已读
            String is_sub = "";//完成情况
            String comms = "";//评价情况
            String sub_type = "";//业务类型
            String ding_label = "";//盯办标签
            String check_time = "";//反馈期限（1 3 7 15）
            String temp_id = "";//个性表单
            int searchLevel = 1;
            String where_sql1 = "";//盯办
            String where_sql2 = "";//通知通报
            String where_sql3 = "";//信息速递
            int tt = -1;
            int isExp = 0;
            String start_time = "";//创建时间
            String end_time = "";//创建时间
            String up_start_time = "";//上报时间
            String up_end_time = "";//上报时间
            String flow_start_time = "";//录用时间
            String flow_end_time = "";//录用时间
            int offer_type = 0;//信息or经验交流
            int flow = -99;//流转编号
            String forward_unit = "";//警种筛选
            String accept_unit = "";//接收单位筛选

            if (data.containsKey("accept_unit") && data.getString("accept_unit").length() > 0) {
                accept_unit = data.getString("accept_unit");
            }

            if (data.containsKey("forward_unit") && data.getString("forward_unit").length() > 0) {
                forward_unit = data.getString("forward_unit");
                if (!forward_unit.equals("320400140000"))
                    where_sql3 += " and a.forward_unit = '" + forward_unit + "' ";
                else
                    where_sql3 += " and (isnull(a.forward_unit) or a.forward_unit = '') ";
            }
            if (data.containsKey("flow") && data.getString("flow").length() > 0) {
                flow = data.getInteger("flow");
                where_sql3 += " and a.flow = '" + flow + "' ";
            }
            if (data.containsKey("offer_type") && data.getString("offer_type").length() > 0) {
                offer_type = data.getInteger("offer_type");
                where_sql3 += " and a.offer_type = '" + offer_type + "' ";
            }
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time");
                where_sql1 += " and notice_time >= '" + start_time + "'";
                where_sql2 += " and notice_time >= '" + start_time + "'";
//                where_sql3 += " and a.create_time >= '" + start_time + "'";
                where_sql3 += "and exists (select 1 from express_his b where a.id = b.real_id and b.flow in (1,2,5) and b.opt_time >= '"+ start_time +"' " +
                        "and b.opt_time = (select min(c.opt_time) from express_his c where a.id = c.real_id and c.flow in (1,2,5) ))";
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time");
                where_sql1 += " and notice_time <= '" + end_time + "'";
                where_sql2 += " and notice_time <= '" + end_time + "'";
//                where_sql3 += " and a.create_time <= '" + end_time + "'";
                where_sql3 += "and exists (select 1 from express_his b where a.id = b.real_id and b.flow in (1,2,5) and b.opt_time <= '"+ end_time +"' " +
                        "and b.opt_time = (select min(c.opt_time) from express_his c where a.id = c.real_id and c.flow in (1,2,5)))";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
            }
            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getInteger("isExp");
            }
            if (data.containsKey("searchLevel") && data.getString("searchLevel").length() > 0) {
                searchLevel = data.getInteger("searchLevel");
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                if (StringUtil.isNotBlank(accept_unit)){
                    unit = accept_unit;
                }
                else {
                    unit = data.getString("unit");
                }
                tt = RIUtil.dicts.get(unit).getIntValue("type");
                if (tt == 22 || tt == 27 || tt == 21) {//支队
                    unit = "320400000000";

                } else if (tt == 23 || tt == 24 || tt == 28) {
                    unit = unit.substring(0, 6) + "000000";

                } else if (tt == 25 || tt == 26) {
                    unit = unit.substring(0, 8) + "0000";
                }
            }
            else {
                unit = accept_unit;
                tt = RIUtil.dicts.get(unit).getIntValue("type");
                if (tt == 22 || tt == 27 || tt == 21) {//支队
                    unit = "320400000000";

                } else if (tt == 23 || tt == 24 || tt == 28) {
                    unit = unit.substring(0, 6) + "000000";

                } else if (tt == 25 || tt == 26) {
                    unit = unit.substring(0, 8) + "0000";
                }
            }

            if (data.containsKey("reading") && data.getString("reading").length() > 0) {
                reading = data.getString("reading");
            }
            if (data.containsKey("is_sub") && data.getString("is_sub").length() > 0) {
                is_sub = data.getString("is_sub");
            }
            if (data.containsKey("comms") && data.getString("comms").length() > 0) {
                comms = data.getString("comms");
                where_sql1 += " and comms = '" + comms + "' and isSub = '2'";
            }
            if (data.containsKey("sub_type") && data.getString("sub_type").length() > 0) {
                sub_type = data.getString("sub_type");
                where_sql1 += " and subType = '" + sub_type + "'";
                where_sql2 += " and subType = '" + sub_type + "'";
            }
            if (data.containsKey("ding_label") && data.getString("ding_label").length() > 0) {
                ding_label = data.getString("ding_label");
                where_sql1 += " and ding_label = '" + ding_label + "'";
            }
            if (data.containsKey("temp_id") && data.getString("temp_id").length() > 0) {
                temp_id = data.getString("temp_id");
                if ("1".equals(temp_id))
                    where_sql1 += " and temp_id != '' and !isnull(temp_id)";
                if ("0".equals(temp_id))
                    where_sql1 += " and (temp_id = '' or isnull(temp_id))";
            }
            if (data.containsKey("check_time") && data.getString("check_time").length() > 0) {
                check_time = data.getString("check_time");
                Calendar c = Calendar.getInstance();
                String start = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(c.getTime());
                c.add(Calendar.DAY_OF_MONTH, Integer.parseInt(check_time));
                String end = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(c.getTime());
                where_sql1 += " and check_time >='" + start + "' and check_time <= '" + end + "'";
                where_sql2 += " and check_time >='" + start + "' and check_time <= '" + end + "'";
            }
            if (data.containsKey("flow_start_time") && data.getString("flow_start_time").length() >0){
                flow_start_time = data.getString("flow_start_time");
                where_sql3 += "and exists (select 1 from express_his b where a.id = b.real_id and b.flow = 9 and b.opt_time >= '"+ flow_start_time +"' " +
                        "and b.opt_time = (select max(c.opt_time) from express_his c where a.id = c.real_id and c.flow = 9 ))";
            }
            if (data.containsKey("flow_end_time") && data.getString("flow_end_time").length() >0){
                flow_end_time = data.getString("flow_end_time");
                where_sql3 += "and exists (select 1 from express_his b where a.id = b.real_id and b.flow = 9 and b.opt_time <= '"+ flow_end_time +"' " +
                        "and b.opt_time = (select max(c.opt_time) from express_his c where a.id = c.real_id and c.flow = 9 ))";
            }

            if (data.containsKey("up_start_time") && data.getString("up_start_time").length() >0){
                up_start_time = data.getString("up_start_time");
                where_sql3 += "and exists (select 1 from express_his b where a.id = b.real_id and b.flow = 2 and b.opt_time >= '"+ up_start_time +"' " +
                        "and b.opt_time = (select max(c.opt_time) from express_his c where a.id = c.real_id and c.flow = 2 ))";
            }
            if (data.containsKey("up_end_time") && data.getString("up_end_time").length() >0){
                up_end_time = data.getString("up_end_time");
                where_sql3 += "and exists (select 1 from express_his b where a.id = b.real_id and b.flow = 2 and b.opt_time <= '"+ up_end_time +"' " +
                        "and b.opt_time = (select max(c.opt_time) from express_his c where a.id = c.real_id and c.flow = 2 ))";
            }

            sql = "select * from dict where type = 150 and (dict_name = '盯办任务' or dict_name = '通知通告' or dict_name = " +
                    "'工作通报')";
            List<JSONObject> dict = mysql.query(sql);
            JSONObject jsonObject = new JSONObject();
            if (dict.size() > 0) {
                for (JSONObject object : dict) {
                    jsonObject.put(object.getString("dict_name"), object.getString("id"));
                }
            }

            JSONObject jsonObject2 = new JSONObject();
            if (searchLevel == 2) {//派出所
                tt = 25;
                if (StringUtil.isNotBlank(unit)) {
                    int type1 = RIUtil.dicts.get(unit).getIntValue("type");
                    if (type1 == 23) {//分局
                        sql = "select * from dict where isdelete = 1 and (type = 25) and id like concat('%','" + unit.substring(0, 6) + "','%')";
                    } else if (type1 == 25) {//派出所
                        sql = "select * from dict where isdelete = 1 and (type = 25) and id like concat('%','" + unit.substring(0, 8) + "','%')";
                    } else if (type1 == 21) {//市局
                        sql = "select * from dict where isdelete = 1 and (type = 25) ";
                    }
                } else {
                    sql = "select * from dict where isdelete = 1 and (type = 25) ";
                }
            } else if (searchLevel == 1) {//分局
                tt = 23;
                if (StringUtil.isNotBlank(unit) && !"320400000000".equals(unit)) {
                    sql = "select * from dict where isdelete = 1 and (((type = 23) and id like concat('%','" + unit.substring(0, 6) + "','%')) )";
                } else {
                    sql = "select * from dict where isdelete = 1 and (type = 23 or type =21)";
                }
            }
//            else {//市局
//                tt = 21;
//                sql = "select * from dict where isdelete = 1 and (type = 21)";
//            }
            List<JSONObject> unitList = mysql.query(sql);
            if (unitList.size() > 0) {
                JSONArray fjJsonArray = new JSONArray();
                JSONArray pcsJsonArray = new JSONArray();
                JSONArray sjJsonArray = new JSONArray();
                JSONObject jsonObject1 = new JSONObject();
                for (JSONObject object : unitList) {
                    String where_unit_sql = "";
                    if ("23".equals(object.getString("type")) || "21".equals(object.getString("type"))) {
                        where_unit_sql = " and a.unit like concat('%','" + object.getString("id").substring(0, 6) + "'," +
                                "'%') ";
                    } else if ("25".equals(object.getString("type"))) {
                        where_unit_sql = " and a.unit like concat('%','" + object.getString("id").substring(0, 8) + "'," +
                                "'%') ";
                    }
                    jsonObject1 = new JSONObject();
                    jsonObject1.put("index_no", object.getIntValue("index_no"));
                    jsonObject1.put("name", object.getString("dict_name"));
                    jsonObject1.put("code", object.getString("id"));

                    sql = "select * from ding_notice a where isdelete = 1 " + where_sql1 ;
                    List<JSONObject> query1 = mysql.query(sql);
                    if (query1.size() > 0) {
                        //接收单位筛选
                        if (accept_unit.length() > 0){
                            InfoModelHelper finalMysql1 = mysql;
                            String finalAccept_unit = accept_unit;
                            query1 = query1.stream().filter(ding_notice -> {
                                if (StringUtil.isNotBlank(ding_notice.getString("users"))){
                                    for (String idCard : ding_notice.getString("users").split(",")) {
                                        String s = "select * from user where status=1 and isdelete=1 and id_num = '"+ idCard +"'";
                                        try {
                                            List<JSONObject> list = finalMysql1.query(s);
                                            if (!list.isEmpty() && list.get(0).getString("unit").startsWith(finalAccept_unit.substring(0,6))){
                                                return true;
                                            }
                                        } catch (Exception e) {
                                            throw new RuntimeException(e);
                                        }
                                    }
                                }
                                return false;
                            }).collect(Collectors.toList());
                            //发送单位筛选
                            if(data.containsKey("unit") && data.getString("unit").length() > 0){
                                int finalTt = tt;
                                query1 = query1.stream().filter(ding_notice -> {
                                    ding_notice.put("create_unit", "");
                                    int t = 1;
                                    if (ding_notice.containsKey("unit") && StringUtil.isNotBlank(ding_notice.getString("unit"))) {
                                        if (RIUtil.dicts.get(ding_notice.getString("unit")).containsKey("type")) {
                                            t = RIUtil.dicts.get(ding_notice.getString("unit")).getIntValue("type");
                                        }
                                    }

                                    if ((t == 22 || t == 27 || t == 21 || t == 23 || t == 24 || t == 28) && (finalTt == 22 || finalTt == 27 || finalTt == 21 || finalTt == 23 || finalTt == 24 || finalTt == 28)) {//支队
                                        return true;
                                    } else if ((t == 25 || t == 26) && (finalTt == 25 || finalTt == 26)) {
                                        return true;
                                    }
                                    return false;
                                }).collect(Collectors.toList());

                                int t = RIUtil.dicts.get(data.getString("unit")).getIntValue("type");
                                query1 = query1.stream().filter(ding_notice -> {
                                    if (t == 23  || t == 21 ) {
                                        return ding_notice.containsKey("unit") && ding_notice.getString("unit").startsWith(data.getString("unit").substring(0, 6));
                                    }
                                    else if (t == 25) {
                                        return ding_notice.containsKey("unit") && ding_notice.getString("unit").startsWith(data.getString("unit").substring(0, 8));
                                    }
                                    return false;
                                }).collect(Collectors.toList());
                            }
                        }
                        //发送单位筛选
                        else if(data.containsKey("unit") && data.getString("unit").length() > 0){
                            int finalTt = tt;
                            query1 = query1.stream().filter(ding_notice -> {
                                ding_notice.put("create_unit", "");
                                int t = 1;
                                if (ding_notice.containsKey("unit") && StringUtil.isNotBlank(ding_notice.getString("unit"))) {
                                    if (RIUtil.dicts.get(ding_notice.getString("unit")).containsKey("type")) {
                                        t = RIUtil.dicts.get(ding_notice.getString("unit")).getIntValue("type");
                                    }
                                }

                                if ((t == 22 || t == 27 || t == 21 || t == 23 || t == 24 || t == 28) && (finalTt == 22 || finalTt == 27 || finalTt == 21 || finalTt == 23 || finalTt == 24 || finalTt == 28)) {//支队
                                    return true;
                                } else if ((t == 25 || t == 26) && (finalTt == 25 || finalTt == 26)) {
                                    return true;
                                }
                                return false;
                            }).collect(Collectors.toList());

                            query1 = query1.stream().filter(ding_notice -> {
                                if ("23".equals(object.getString("type")) || "21".equals(object.getString("type"))) {
                                    return ding_notice.containsKey("unit") && ding_notice.getString("unit").startsWith(object.getString("id").substring(0, 6));
                                }
                                else if ("25".equals(object.getString("type"))) {
                                    return ding_notice.containsKey("unit") && ding_notice.getString("unit").startsWith(object.getString("id").substring(0, 8));
                                }
                                return false;
                            }).collect(Collectors.toList());
                        }

                        String finalReading = reading;
                        String finalIs_sub = is_sub;
                        String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                        if (finalReading.length() > 0) {
                            query1 = query1.stream().filter(q1 -> {
                                //未读
                                if ("0".equals(finalReading)) {
                                    if (q1.getString("reading").length() > 0 && !"[]".equals(q1.getString("reading")))
                                        return true;
                                }
                                //已读
                                else if ("1".equals(finalReading)) {
                                    if ("[]".equals(q1.getString("reading")) || q1.getString("reading").length() == 0)
                                        return true;
                                }
                                return false;
                            }).collect(Collectors.toList());
                        }
                        if (finalIs_sub.length() > 0) {
                            InfoModelHelper finalMysql = mysql;

                            query1 = query1.stream().filter(q1 -> {
                                //未完成：0或null或    已完成：1或2    已评价：2
                                //未完成
                                if ("0".equals(finalIs_sub)) {
                                    if (StringUtil.isBlank(q1.getString("isSub")) || (q1.getString("isSub").length() > 0 && "0".equals(q1.getString("isSub"))))
                                        return true;
                                }
                                //逾期未完成
                                if ("1".equals(finalIs_sub)) {
                                    if (StringUtil.isBlank(q1.getString("isSub")) || (q1.getString("isSub").length() > 0 && "0".equals(q1.getString("isSub")))) {
                                        String s =
                                                "select time,opt_user,opts from ding_form_logs where ding_id='" + q1.getString("id") + "' and opts ='提交'";
                                        try {
                                            List<JSONObject> query = finalMysql.query(s);
                                            //有提交 判断提交时间是否超过check_time
                                            if (query.size() > 0 && ((query.get(0).getString("time")).compareTo(q1.getString("check_time")) > 0)) {
                                                return true;
                                            }
                                            //无提交 判断当前时间是否超过check_time
                                            else if (query.size() == 0 && q1.getString("check_time").compareTo(date) < 0) {
                                                return true;
                                            }
                                        } catch (Exception e) {
                                            throw new RuntimeException(e);
                                        }
                                    }
                                }
                                //逾期已完成
                                if ("2".equals(finalIs_sub)) {
                                    if (q1.getString("isSub").length() > 0 && ("1".equals(q1.getString("isSub")) ||
                                            "2".equals(q1.getString("isSub")))) {
                                        String s =
                                                "select time,opt_user,opts from ding_form_logs where ding_id='" + q1.getString("id") + "' and opts ='提交'";
                                        try {
                                            List<JSONObject> query = finalMysql.query(s);
                                            //有提交 判断提交时间是否超过check_time
                                            if (query.size() > 0 && ((query.get(0).getString("time")).compareTo(q1.getString("check_time")) > 0)) {
                                                return true;
                                            }
                                            //无提交 判断当前时间是否超过check_time
                                            else if (query.size() == 0 && q1.getString("check_time").compareTo(date) < 0) {
                                                return true;
                                            }
                                        } catch (Exception e) {
                                            throw new RuntimeException(e);
                                        }
                                    }
                                }
                                //已完成
                                if ("3".equals(finalIs_sub)) {
                                    if (q1.getString("isSub").length() > 0 && ("1".equals(q1.getString("isSub")) ||
                                            "2".equals(q1.getString("isSub"))))
                                        return true;
                                }
                                //已完成未评价
                                if ("4".equals(finalIs_sub)) {
                                    if (q1.getString("isSub").length() > 0 && "1".equals(q1.getString("isSub")))
                                        return true;
                                }
                                return false;
                            }).collect(Collectors.toList());

                        }
                    }
                    jsonObject1.put("ding", query1.size());

                    sql = "select * from notice a where is_notice = '1' and type = '" + jsonObject.getString("通知通告") +
                            "' and isdelete = 1 " + where_sql2 + where_unit_sql;
                    List<JSONObject> query2 = mysql.query(sql);
                    if (query2.size() > 0) {
                        int finalTt = tt;
                        query2 = query2.stream().filter(ding_notice -> {
                            ding_notice.put("create_unit", "");
                            int t = 1;
                            if (ding_notice.containsKey("unit") && StringUtil.isNotBlank(ding_notice.getString("unit"))) {
                                if (RIUtil.dicts.get(ding_notice.getString("unit")).containsKey("type")) {
                                    t = RIUtil.dicts.get(ding_notice.getString("unit")).getIntValue("type");
                                }
                            }

                            if ((t == 22 || t == 27 || t == 21 || t == 23 || t == 24 || t == 28 || t == 25 || t == 26) && (finalTt == 22 || finalTt == 27 || finalTt == 21 || finalTt == 23 || finalTt == 24 || finalTt == 28)) {//支队
                                return true;
                            } else if ((t == 25 || t == 26) && (finalTt == 25 || finalTt == 26)) {
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());

                        String finalReading = reading;
                        if (finalReading.length() > 0) {
                            query2 = query2.stream().filter(q2 -> {
                                //未读
                                if ("0".equals(finalReading)) {
                                    if (q2.getString("reading").length() > 0 && !"[]".equals(q2.getString("reading")))
                                        return true;
                                }
                                //已读
                                else if ("1".equals(finalReading)) {
                                    if ("[]".equals(q2.getString("reading")) || q2.getString("reading").length() == 0)
                                        return true;
                                }
                                return false;
                            }).collect(Collectors.toList());
                        }

                    }
                    jsonObject1.put("notice_tztg", query2.size());

                    sql = "select * from notice a where is_notice = '1' and type = '" + jsonObject.getString("工作通报") +
                            "' and isdelete = 1 " + where_sql2 + where_unit_sql;
                    List<JSONObject> query3 = mysql.query(sql);
                    if (query3.size() > 0) {
                        int finalTt = tt;
                        query3 = query3.stream().filter(ding_notice -> {
                            ding_notice.put("create_unit", "");
                            int t = 1;
                            if (ding_notice.containsKey("unit") && StringUtil.isNotBlank(ding_notice.getString("unit"))) {
                                if (RIUtil.dicts.get(ding_notice.getString("unit")).containsKey("type")) {
                                    t = RIUtil.dicts.get(ding_notice.getString("unit")).getIntValue("type");
                                }
                            }

                            if ((t == 22 || t == 27 || t == 21 || t == 23 || t == 24 || t == 28 || t == 25 || t == 26) && (finalTt == 22 || finalTt == 27 || finalTt == 21 || finalTt == 23 || finalTt == 24 || finalTt == 28)) {//支队
                                return true;
                            } else if ((t == 25 || t == 26) && (finalTt == 25 || finalTt == 26)) {
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());

                        String finalReading = reading;
                        if (finalReading.length() > 0) {
                            query3 = query3.stream().filter(q2 -> {
                                //未读
                                if ("0".equals(finalReading)) {
                                    if (q2.getString("reading").length() > 0 && !"[]".equals(q2.getString("reading")))
                                        return true;
                                }
                                //已读
                                else if ("1".equals(finalReading)) {
                                    if ("[]".equals(q2.getString("reading")) || q2.getString("reading").length() == 0)
                                        return true;
                                }
                                return false;
                            }).collect(Collectors.toList());
                        }

                    }
                    jsonObject1.put("notice_gztb", query3.size());

                    sql = "select * from express_info a where a.isdelete = 0 " + where_sql3 + where_unit_sql;
                    List<JSONObject> query4 = mysql.query(sql);
                    jsonObject1.put("offer_score_xx", 0);
                    jsonObject1.put("offer_score_dy", 0);
                    if (query4.size() > 0) {
                        int finalTt = tt;
                        query4 = query4.stream().filter(ding_notice -> {
                            ding_notice.put("create_unit", "");
                            int t = 1;
                            if (ding_notice.containsKey("unit") && StringUtil.isNotBlank(ding_notice.getString("unit"))) {
                                if (RIUtil.dicts.get(ding_notice.getString("unit")).containsKey("type")) {
                                    t = RIUtil.dicts.get(ding_notice.getString("unit")).getIntValue("type");
                                }
                            }

                            if ((t == 22 || t == 27 || t == 21 || t == 23 || t == 24 || t == 28 || t == 25 || t == 26) && (finalTt == 22 || finalTt == 27 || finalTt == 21 || finalTt == 23 || finalTt == 24 || finalTt == 28)) {//支队
                                return true;
                            } else if ((t == 25 || t == 26) && (finalTt == 25 || finalTt == 26)) {
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());

                        try {
                            int count_xx = 0;
                            int count_dy = 0;
                            for (JSONObject jsonObject3 : query4) {
                                if (jsonObject3.containsKey("flow") && !ObjectUtil.isEmpty(jsonObject3.get("flow")) && "9".equals(jsonObject3.getString("flow")) && jsonObject3.containsKey("offer_type") && !ObjectUtil.isEmpty(jsonObject3.get("offer_type")) && "1".equals(jsonObject3.getString("offer_type"))) {
                                    count_xx++;
                                } else if (jsonObject3.containsKey("flow") && !ObjectUtil.isEmpty(jsonObject3.get(
                                        "flow")) && "9".equals(jsonObject3.getString("flow")) && jsonObject3.containsKey("offer_type") && !ObjectUtil.isEmpty(jsonObject3.get("offer_type")) && "2".equals(jsonObject3.getString("offer_type"))) {
                                    count_dy++;
                                }
                            }
                            int offer_score_xx = Integer.parseInt(TNOAConf.get("offer_score", jsonObject1.getString(
                                    "code") + "_xx"));
                            double v = (double) count_xx / (double) offer_score_xx * 0.5;
                            jsonObject1.put("offer_score_xx", v);
                            if (v > 0.5) {
                                jsonObject1.put("offer_score_xx", 0.5);
                            }
                            int offer_score_dy = Integer.parseInt(TNOAConf.get("offer_score", jsonObject1.getString(
                                    "code") + "_dy"));
                            v = (double) count_dy / (double) offer_score_dy * 0.5;
                            jsonObject1.put("offer_score_dy", v);
                            if (v > 0.5) {
                                jsonObject1.put("offer_score_dy", 0.5);
                            }
                        } catch (NumberFormatException e) {
                            jsonObject1.put("offer_score_xx", 0);
                            jsonObject1.put("offer_score_dy", 0);
                        }

                    }
                    jsonObject1.put("express", query4.size());

                    fjJsonArray.add(jsonObject1);
                    pcsJsonArray.add(jsonObject1);
                    sjJsonArray.add(jsonObject1);

                }


                fjJsonArray.sort(Comparator.comparing(obj -> JSONObject.parseObject(obj.toString()).getString(
                        "index_no")));
                List<Object> list = fjJsonArray.stream().filter(obj -> {
                    JSONObject json = JSONObject.parseObject(obj.toString());
                    if (!json.getString("code").startsWith("320496") && !json.getString("code").startsWith("320498")) {
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());
                fjJsonArray = JSONArray.parseArray(JSON.toJSONString(list));
                jsonObject2.put("fj", fjJsonArray);
                pcsJsonArray.sort(Comparator.comparing(obj -> JSONObject.parseObject(obj.toString()).getString("code")));
                jsonObject2.put("pcs", pcsJsonArray);
                sjJsonArray.sort(Comparator.comparing(obj -> JSONObject.parseObject(obj.toString()).getString("code")));
                jsonObject2.put("sj", sjJsonArray);
            }

            //分局
            if (searchLevel == 1) {
                back.put("data", jsonObject2.getJSONArray("fj"));
            }
            //派出所
            else if (searchLevel == 2) {
                back.put("data", jsonObject2.getJSONArray("pcs"));
            }
            //市局
            else {
                back.put("data", jsonObject2.getJSONArray("sj"));
            }
            String file_id = "";
            if (isExp == 1) {
                JSONObject excel = new JSONObject();
                excel.put("files", back.getJSONArray("data"));
                logger.warn("开始导出");
                file_id = String.valueOf(ExceptionFile1(excel.getJSONArray("files"), type, offer_type));
                back.put("file_id", file_id);

            }

        } catch (Exception ex) {
            return ErrNo.set(607001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static String ExceptionFile1(JSONArray datas, String type, int offer_type) {
        String name = "";
        if (!"0".equals(type)) {
            name = RIUtil.dicts.get(type).getString("dict_name");
        }
        String FileName = "信息统计分析_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        for (int i = 0; i < datas.size(); i++) {
            JSONObject one = datas.getJSONObject(i);
            if ("通知通告".equals(name)) {
                one.put("count", one.getInteger("notice_tztg"));
            } else if ("工作通报".equals(name)) {
                one.put("count", one.getInteger("notice_gztb"));
            } else if ("盯办任务".equals(name)) {
                one.put("count", one.getInteger("ding"));
            } else if ("信息速递".equals(name)) {
                one.put("count", one.getInteger("express"));
                if (offer_type == 1) {
                    one.put("offer_score", one.getDoubleValue("offer_score_xx"));
                } else if (offer_type == 2) {
                    one.put("offer_score", one.getDoubleValue("offer_score_dy"));
                } else {
                    one.put("offer_score", one.getDoubleValue("offer_score_dy") + one.getDoubleValue("offer_score_xx"));
                }
            } else if (StringUtil.isBlank(name)) {
                one.put("count", one.getInteger("ding") + one.getInteger("notice_gztb") + one.getInteger("notice_tztg"
                ));
            }
        }
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        header.add("name");
        headername.put("name", "单位名称");
        header.add("count");
        headername.put("count", "数量");
        if ("信息速递".equals(name)) {
            header.add("offer_score");
            headername.put("offer_score", "积分");
        }


        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) +
                            "/";

            logger.warn(header.toString());
            logger.warn(String.valueOf(headername));
            logger.warn(String.valueOf(datas));

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
            exporthelper.write_data_num(datas);

            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql =
                        "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");
                try {
                    if (exporthelper != null) {
                        try {
                            exporthelper.close();
                        } catch (Exception ex) {

                        }

                    }
                } catch (Exception ex) {

                }

                //返回拼接后的url,id
                String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;

                logger.warn(endPath + "-->" + String.valueOf(new File(endPath).length()));
                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName,
                        TNOAConf.get("file", "img_path") + filePath + FileName);
                logger.warn(obsFileName + "-->" + ret);
                logger.warn("id->" + id);
                return String.valueOf(id);
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return "0";
        } finally {


            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }

        }


    }

    private static String ExceptionFile2(JSONArray datas, String type) {
        String name = "";
        if (!"0".equals(type)) {
            name = RIUtil.dicts.get(type).getString("dict_name");
        }
        String FileName = "信息统计分析_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        for (int i = 0; i < datas.size(); i++) {
            JSONObject one = datas.getJSONObject(i);
            one.put("org", one.getJSONObject("create_user").getJSONObject("unit_name").getString("dict_name"));
            one.put("name", one.getJSONObject("create_user").getString("name"));

            if ("通知通告".equals(name)) {
                one.put("count", one.getInteger("notice_tztg"));
                one.put("time", one.getString("notice_time"));
            } else if ("工作通报".equals(name)) {
                one.put("count", one.getInteger("notice_gztb"));
                one.put("time", one.getString("notice_time"));
            } else if ("盯办任务".equals(name)) {
                one.put("time", one.getString("notice_time"));
                one.put("count", one.getInteger("ding"));
                one.put("sub_type", one.getJSONObject("subType").getString("dict_name"));
                one.put("dingLabel", "");
                if (StringUtil.isNotBlank(one.getString("ding_label")) && !one.getJSONArray("ding_label").isEmpty()) {
                    Object object = one.getJSONArray("ding_label").get(one.getJSONArray("ding_label").size() - 1);
                    JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(object));
                    one.put("dingLabel", jsonObject.getString("dict_name"));
                }

                if (one.containsKey("isSub") && ("1".equals(one.getString("isSub")) || "2".equals(one.getString(
                        "isSub")))) {
                    one.put("is_sub", "已完成");
                } else {
                    one.put("is_sub", "未完成");
                }
                if ("1".equals(one.getString("isLate"))) {
                    one.put("is_sub", "已逾期");
                } else if ("2".equals(one.getString("isLate"))) {
                    one.put("is_sub", "逾期完成");
                }

                if (one.containsKey("temp_id") && StringUtil.isNotBlank(one.getString("temp_id"))) {
                    one.put("is_temp", "使用");
                } else {
                    one.put("is_temp", "未使用");
                }
                if (one.containsKey("isSub") && "2".equals(one.getString("isSub")) && one.containsKey("comms") && StringUtil.isNotBlank(one.getString("comms"))) {
                    one.put("comms", one.getString("comms"));
                } else {
                    one.put("comms", "");
                }

            } else if ("信息速递".equals(name)) {
                one.put("count", one.getInteger("express"));
                one.put("time", one.getString("create_time"));
                one.put("offerType", "");
                if ("1".equals(one.getString("offer_type")))
                    one.put("offerType", "信息");
                else if ("2".equals(one.getString("offer_type")))
                    one.put("offerType", "经验交流");
                one.put("Flow", "");
                if ("0".equals(one.getString("flow")))
                    one.put("Flow", "草稿");
                else if ("1".equals(one.getString("flow")))
                    one.put("Flow", "派出所上报");
                else if ("2".equals(one.getString("flow")))
                    one.put("Flow", "分局上报");
                else if ("3".equals(one.getString("flow")))
                    one.put("Flow", "分局退回");
                else if ("4".equals(one.getString("flow")))
                    one.put("Flow", "支队退回");
                else if ("5".equals(one.getString("flow")))
                    one.put("Flow", "业务大队上报");
                else if ("6".equals(one.getString("flow")))
                    one.put("Flow", "支队退回");
                else if ("9".equals(one.getString("flow")))
                    one.put("Flow", "录用");
            } else if (StringUtil.isBlank(name)) {
                one.put("count", one.getInteger("ding") + one.getInteger("notice_gztb") + one.getInteger("notice_tztg"
                ));
            }
        }
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        header.add("title");
        headername.put("title", "标题");
        if ("盯办任务".equals(name)) {
            header.add("sub_type");
            headername.put("sub_type", "类型");
            header.add("dingLabel");
            headername.put("dingLabel", "标签");
            header.add("is_temp");
            headername.put("is_temp", "是否使用表单");
            header.add("is_sub");
            headername.put("is_sub", "完成情况");
            header.add("comms");
            headername.put("comms", "评价");
        }
        if ("信息速递".equals(name)) {
            header.add("offerType");
            headername.put("offerType", "类型");
            header.add("Flow");
            headername.put("Flow", "状态");
            header.add("offer_score");
            headername.put("offer_score", "分值");
        }
        header.add("time");
        headername.put("time", "发送时间");
        header.add("org");
        headername.put("org", "发送单位");
        header.add("name");
        headername.put("name", "发送人");


        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) +
                            "/";

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
            exporthelper.write_data_num(datas);

            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql =
                        "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");

                //返回拼接后的url,id
                String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;

                logger.warn(endPath + "-->" + String.valueOf(new File(endPath).length()));
                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName,
                        TNOAConf.get("file", "img_path") + filePath + FileName);
                logger.warn(obsFileName + "-->" + ret);
                logger.warn("id->" + id);
                return String.valueOf(id);
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return "0";
        } finally {
            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }

        }


    }

    private JSONObject get_list(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";//发送单位
        String type = "";//统计类型
        String reading = "";//0未读1已读
        String is_sub = "";//完成情况
        String comms = "";//评价情况
        String sub_type = "";//业务类型
        String ding_label = "";//盯办标签
        String check_time = "";//反馈期限（1 3 7 15）
        String temp_id = "";//个性表单
        String opt_user = "";
        int limit = 20;
        int page = 1;
        int tt = -1;
        int isExp = -1;
        String start_time = "";//创建时间
        String end_time = "";//创建时间
        String up_start_time = "";//上报时间
        String up_end_time = "";//上报时间
        String flow_start_time = "";//录用时间
        String flow_end_time = "";//录用时间
        int offer_type = 0;//信息or经验交流
        int flow = -99;//流转编号
        String forward_unit = "";//警种筛选
        String accept_unit = "";//接收单位筛选

        if (data.containsKey("accept_unit") && data.getString("accept_unit").length() > 0) {
            accept_unit = data.getString("accept_unit");
        }


        InfoModelHelper mysql = null;
//        MysqlHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String where_sql1 = "";//盯办
            String where_sql2 = "";//通知通报
            String where_sql3 = "";//信息速递
            if (data.containsKey("forward_unit") && data.getString("forward_unit").length() > 0) {
                forward_unit = data.getString("forward_unit");
                if (!forward_unit.equals("320400140000"))
                    where_sql3 += " and a.forward_unit = '" + forward_unit + "' ";
                else
                    where_sql3 += " and (isnull(a.forward_unit) or a.forward_unit = '') ";
            }
            if (data.containsKey("flow") && data.getString("flow").length() > 0) {
                flow = data.getInteger("flow");
                where_sql3 += " and a.flow = '" + flow + "' ";
            }
            if (data.containsKey("offer_type") && data.getString("offer_type").length() > 0) {
                offer_type = data.getInteger("offer_type");
                where_sql3 += " and a.offer_type = '" + offer_type + "' ";
            }

            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time");
                where_sql1 += " and notice_time >= '" + start_time + "'";
                where_sql2 += " and notice_time >= '" + start_time + "'";
//                where_sql3 += " and create_time >= '" + start_time + "'";
                where_sql3 += "and exists (select 1 from express_his b where a.id = b.real_id and b.flow in (1,2,5) and b.opt_time >= '"+ start_time +"' " +
                        "and b.opt_time = (select min(c.opt_time) from express_his c where a.id = c.real_id and c.flow in (1,2,5) ))";
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time");
                where_sql1 += " and notice_time <= '" + end_time + "'";
                where_sql2 += " and notice_time <= '" + end_time + "'";
//                where_sql3 += " and create_time <= '" + end_time + "'";
                where_sql3 += "and exists (select 1 from express_his b where a.id = b.real_id and b.flow in (1,2,5) and b.opt_time <= '"+ end_time +"' " +
                        "and b.opt_time = (select min(c.opt_time) from express_his c where a.id = c.real_id and c.flow in (1,2,5)))";
            }

            if (data.containsKey("isExp")) {
                isExp = data.getInteger("isExp");
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                if (StringUtil.isNotBlank(accept_unit)){
                    unit = accept_unit;
                }
                else {
                    unit = data.getString("unit");
                }
                tt = RIUtil.dicts.get(unit).getIntValue("type");
                if (tt == 22 || tt == 27 || tt == 21) {//支队
                    unit = "320400000000";
//                    where_sql1 += " and unit like concat('%','" + unit.substring(0, 6) + "','%') ";
                    where_sql2 += " and unit like concat('%','" + unit.substring(0, 6) + "','%') ";
                    where_sql3 += " and a.unit like concat('%','" + unit.substring(0, 6) + "','%') ";
                } else if (tt == 23 || tt == 24 || tt == 28) {
                    unit = unit.substring(0, 6) + "000000";
//                    where_sql1 += " and unit like concat('%','" + unit.substring(0, 6) + "','%') ";
                    where_sql2 += " and unit like concat('%','" + unit.substring(0, 6) + "','%') ";
                    where_sql3 += " and a.unit like concat('%','" + unit.substring(0, 6) + "','%') ";
                } else if (tt == 25 || tt == 26) {
                    unit = unit.substring(0, 8) + "0000";
//                    where_sql1 += " and unit like concat('%','" + unit.substring(0, 8) + "','%') ";
                    where_sql2 += " and unit like concat('%','" + unit.substring(0, 8) + "','%') ";
                    where_sql3 += " and a.unit like concat('%','" + unit.substring(0, 8) + "','%') ";

                }
            }
            else {
                unit = accept_unit;
                tt = RIUtil.dicts.get(unit).getIntValue("type");
                if (tt == 22 || tt == 27 || tt == 21) {//支队
                    unit = "320400000000";
//                    where_sql1 += " and unit like concat('%','" + unit.substring(0, 6) + "','%') ";
                    where_sql2 += " and unit like concat('%','" + unit.substring(0, 6) + "','%') ";
                    where_sql3 += " and a.unit like concat('%','" + unit.substring(0, 6) + "','%') ";
                } else if (tt == 23 || tt == 24 || tt == 28) {
                    unit = unit.substring(0, 6) + "000000";
//                    where_sql1 += " and unit like concat('%','" + unit.substring(0, 6) + "','%') ";
                    where_sql2 += " and unit like concat('%','" + unit.substring(0, 6) + "','%') ";
                    where_sql3 += " and a.unit like concat('%','" + unit.substring(0, 6) + "','%') ";
                } else if (tt == 25 || tt == 26) {
                    unit = unit.substring(0, 8) + "0000";
//                    where_sql1 += " and unit like concat('%','" + unit.substring(0, 8) + "','%') ";
                    where_sql2 += " and unit like concat('%','" + unit.substring(0, 8) + "','%') ";
                    where_sql3 += " and a.unit like concat('%','" + unit.substring(0, 8) + "','%') ";

                }
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                where_sql1 += " and type = '" + type + "'";
                where_sql2 += " and type = '" + type + "'";
            }
            if (data.containsKey("reading") && data.getString("reading").length() > 0) {
                reading = data.getString("reading");
            }
            if (data.containsKey("is_sub") && data.getString("is_sub").length() > 0) {
                is_sub = data.getString("is_sub");
            }
            if (data.containsKey("comms") && data.getString("comms").length() > 0) {
                comms = data.getString("comms");
                where_sql1 += " and comms = '" + comms + "' and isSub = '2'";
            }
            if (data.containsKey("sub_type") && data.getString("sub_type").length() > 0) {
                sub_type = data.getString("sub_type");
                where_sql1 += " and subType = '" + sub_type + "'";
                where_sql2 += " and subType = '" + sub_type + "'";
            }
            if (data.containsKey("ding_label") && data.getString("ding_label").length() > 0) {
                ding_label = data.getString("ding_label");
                where_sql1 += " and ding_label = '" + ding_label + "'";
            }
            if (data.containsKey("temp_id") && data.getString("temp_id").length() > 0) {
                temp_id = data.getString("temp_id");
                if ("1".equals(temp_id))
                    where_sql1 += " and temp_id != '' and !isnull(temp_id)";
                if ("0".equals(temp_id))
                    where_sql1 += " and (temp_id = '' or isnull(temp_id))";
            }
            if (data.containsKey("check_time") && data.getString("check_time").length() > 0) {
                check_time = data.getString("check_time");
                Calendar c = Calendar.getInstance();
                String start = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(c.getTime());
                c.add(Calendar.DAY_OF_MONTH, Integer.parseInt(check_time));
                String end = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(c.getTime());
                where_sql1 += " and check_time >='" + start + "' and check_time <= '" + end + "'";
                where_sql2 += " and check_time >='" + start + "' and check_time <= '" + end + "'";
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            if (data.containsKey("flow_start_time") && data.getString("flow_start_time").length() >0){
                flow_start_time = data.getString("flow_start_time");
                where_sql3 += "and exists (select 1 from express_his b where a.id = b.real_id and b.flow = 9 and b.opt_time >= '"+ flow_start_time +"' " +
                        "and b.opt_time = (select max(c.opt_time) from express_his c where a.id = c.real_id and c.flow = 9 ))";
            }
            if (data.containsKey("flow_end_time") && data.getString("flow_end_time").length() >0){
                flow_end_time = data.getString("flow_end_time");
                where_sql3 += "and exists (select 1 from express_his b where a.id = b.real_id and b.flow = 9 and b.opt_time <= '"+ flow_end_time +"' " +
                        "and b.opt_time = (select max(c.opt_time) from express_his c where a.id = c.real_id and c.flow = 9 ))";
            }

            if (data.containsKey("up_start_time") && data.getString("up_start_time").length() >0){
                up_start_time = data.getString("up_start_time");
                where_sql3 += "and exists (select 1 from express_his b where a.id = b.real_id and b.flow = 2 and b.opt_time >= '"+ up_start_time +"' " +
                        "and b.opt_time = (select max(c.opt_time) from express_his c where a.id = c.real_id and c.flow = 2 ))";
            }
            if (data.containsKey("up_end_time") && data.getString("up_end_time").length() >0){
                up_end_time = data.getString("up_end_time");
                where_sql3 += "and exists (select 1 from express_his b where a.id = b.real_id and b.flow = 2 and b.opt_time <= '"+ up_end_time +"' " +
                        "and b.opt_time = (select max(c.opt_time) from express_his c where a.id = c.real_id and c.flow = 2 ))";
            }

            //盯办列表
            String sql = "select * from ding_notice where isdelete = 1" + where_sql1 + " order by isTop desc," +
                    "notice_time desc";
            logger.warn(sql);
            List<JSONObject> query1 = mysql.query(sql);

            if (query1.size() > 0) {
                //接收单位筛选
                if (accept_unit.length() > 0){
                    InfoModelHelper finalMysql1 = mysql;
                    String finalAccept_unit = accept_unit;
                    query1 = query1.stream().filter(ding_notice -> {
                        if (StringUtil.isNotBlank(ding_notice.getString("users"))){
                            for (String idCard : ding_notice.getString("users").split(",")) {
                                String s = "select * from user where status=1 and isdelete=1 and id_num = '"+ idCard +"'";
                                try {
                                    List<JSONObject> list = finalMysql1.query(s);
                                    if (!list.isEmpty() && list.get(0).getString("unit").startsWith(finalAccept_unit.substring(0,6))){
                                        return true;
                                    }
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                            }
                        }
                        return false;
                    }).collect(Collectors.toList());
                    //发送单位筛选
                    if(data.containsKey("unit") && data.getString("unit").length() > 0){

                        int finalTt = RIUtil.dicts.get(data.getString("unit")).getIntValue("type");
                        query1 = query1.stream().filter(ding_notice -> {
                            ding_notice.put("create_unit", "");
                            int t = 1;
                            if (ding_notice.containsKey("unit") && StringUtil.isNotBlank(ding_notice.getString("unit"))) {
                                if (RIUtil.dicts.get(ding_notice.getString("unit")).containsKey("type")) {
                                    t = RIUtil.dicts.get(ding_notice.getString("unit")).getIntValue("type");
                                }
                            }

                            if ((t == 22 || t == 27 || t == 21) && (finalTt == 22 || finalTt == 27 || finalTt == 21)) {//支队
                                return true;
                            } else if ((t == 23 || t == 24 || t == 28 || t == 25 || t == 26) && (finalTt == 23 || finalTt == 24 || finalTt == 28)) {
                                return true;
                            } else if ((t == 25 || t == 26) && (finalTt == 25 || finalTt == 26)) {
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());

                        int finalTt1 = RIUtil.dicts.get(data.getString("unit")).getIntValue("type");
                        query1 = query1.stream().filter(ding_notice -> {
                            if (finalTt1 == 22 || finalTt1 == 27 || finalTt1 == 21) {
                                return ding_notice.containsKey("unit") && ding_notice.getString("unit").startsWith("320400");
                            }
                            else if (finalTt1 == 23 || finalTt1 == 24 || finalTt1 == 28){
                                return ding_notice.containsKey("unit") && ding_notice.getString("unit").startsWith(data.getString("unit").substring(0,6));
                            }
                            else if (finalTt1 == 25 || finalTt1 == 26) {
                                return ding_notice.containsKey("unit") && ding_notice.getString("unit").startsWith(data.getString("unit").substring(0, 8));
                            }
                            return false;
                        }).collect(Collectors.toList());
                    }
                }
                //发送单位筛选
                else if(data.containsKey("unit") && data.getString("unit").length() > 0){

                    int finalTt = tt;
                    query1 = query1.stream().filter(ding_notice -> {
                        ding_notice.put("create_unit", "");
                        int t = 1;
                        if (ding_notice.containsKey("unit") && StringUtil.isNotBlank(ding_notice.getString("unit"))) {
                            if (RIUtil.dicts.get(ding_notice.getString("unit")).containsKey("type")) {
                                t = RIUtil.dicts.get(ding_notice.getString("unit")).getIntValue("type");
                            }
                        }
                        if ((t == 22 || t == 27 || t == 21) && (finalTt == 22 || finalTt == 27 || finalTt == 21)) {//支队
                            return true;
                        } else if ((t == 23 || t == 24 || t == 28 || t == 25 || t == 26) && (finalTt == 23 || finalTt == 24 || finalTt == 28)) {
                            return true;
                        } else if ((t == 25 || t == 26) && (finalTt == 25 || finalTt == 26)) {
                            return true;
                        }
                        return false;
                    }).collect(Collectors.toList());

                    int finalTt1 = tt;
                    query1 = query1.stream().filter(ding_notice -> {
                        if (finalTt1 == 22 || finalTt1 == 27 || finalTt1 == 21) {
                            return ding_notice.containsKey("unit") && ding_notice.getString("unit").startsWith("320400");
                        }
                        else if (finalTt1 == 23 || finalTt1 == 24 || finalTt1 == 28){
                            return ding_notice.containsKey("unit") && ding_notice.getString("unit").startsWith(data.getString("unit").substring(0,6));
                        }
                        else if (finalTt1 == 25 || finalTt1 == 26) {
                            return ding_notice.containsKey("unit") && ding_notice.getString("unit").startsWith(data.getString("unit").substring(0, 8));
                        }
                        return false;
                    }).collect(Collectors.toList());
                }

                String finalReading = reading;
                String finalIs_sub = is_sub;
                String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                if (finalReading.length() > 0) {
                    query1 = query1.stream().filter(q1 -> {
                        //未读
                        if ("0".equals(finalReading)) {
                            if (q1.getString("reading").length() > 0 && !"[]".equals(q1.getString("reading")))
                                return true;
                        }
                        //已读
                        else if ("1".equals(finalReading)) {
                            if ("[]".equals(q1.getString("reading")) || q1.getString("reading").length() == 0)
                                return true;
                        }
                        return false;
                    }).collect(Collectors.toList());
                }
                if (finalIs_sub.length() > 0) {
                    InfoModelHelper finalMysql = mysql;
                    query1 = query1.stream().filter(q1 -> {
                        //未完成
                        if ("0".equals(finalIs_sub)) {
                            if (StringUtil.isBlank(q1.getString("isSub")) || (q1.getString("isSub").length() > 0 &&
                                    "0".equals(q1.getString("isSub"))))
                                return true;
                        }
                        //逾期未完成
                        if ("1".equals(finalIs_sub)) {
                            if (StringUtil.isBlank(q1.getString("isSub")) || (q1.getString("isSub").length() > 0 &&
                                    "0".equals(q1.getString("isSub")))) {
                                String s =
                                        "select time,opt_user,opts from ding_form_logs where ding_id='" + q1.getString("id") + "' and opts ='提交'";
                                try {
                                    List<JSONObject> query = finalMysql.query(s);
                                    //有提交 判断提交时间是否超过check_time
                                    if (query.size() > 0 && ((query.get(0).getString("time")).compareTo(q1.getString(
                                            "check_time")) > 0)) {
                                        return true;
                                    }
                                    //无提交 判断当前时间是否超过check_time
                                    else if (query.size() == 0 && q1.getString("check_time").compareTo(date) < 0) {
                                        return true;
                                    }
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                            }
                        }
                        //逾期已完成
                        if ("2".equals(finalIs_sub)) {
                            if (q1.getString("isSub").length() > 0 && ("1".equals(q1.getString("isSub")) || "2".equals(q1.getString("isSub")))) {
                                String s =
                                        "select time,opt_user,opts from ding_form_logs where ding_id='" + q1.getString("id") + "' and opts ='提交'";
                                try {
                                    List<JSONObject> query = finalMysql.query(s);
                                    //有提交 判断提交时间是否超过check_time
                                    if (query.size() > 0 && ((query.get(0).getString("time")).compareTo(q1.getString(
                                            "check_time")) > 0)) {
                                        return true;
                                    }
                                    //无提交 判断当前时间是否超过check_time
                                    else if (query.size() == 0 && q1.getString("check_time").compareTo(date) < 0) {
                                        return true;
                                    }
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                            }
                        }
                        //已完成
                        if ("3".equals(finalIs_sub)) {
                            if (q1.getString("isSub").length() > 0 && ("1".equals(q1.getString("isSub")) || "2".equals(q1.getString("isSub"))))
                                return true;
                        }
                        //已完成未评价
                        if ("4".equals(finalIs_sub)) {
                            if (q1.getString("isSub").length() > 0 && "1".equals(q1.getString("isSub")))
                                return true;
                        }
                        return false;

                    }).collect(Collectors.toList());


                }
                for (JSONObject jsonObject : query1) {
                    jsonObject.put("favo", 1);
                    jsonObject.put("ding_label", jsonObject.getJSONArray("ding_label"));
                    //判断是否逾期
                    jsonObject.put("isLate", "0");
                    String sqls =
                            "select time,opt_user,opts from ding_form_logs where ding_id='" + jsonObject.getString(
                                    "id") + "' and opts ='提交'";
                    List<JSONObject> query = mysql.query(sqls);
                    //有提交 判断提交时间是否超过check_time
                    if (query.size() > 0 && ((query.get(0).getString("time")).compareTo(jsonObject.getString(
                            "check_time")) > 0)) {
                        jsonObject.put("isLate", "2");//逾期完成
                    }
                    //无提交 判断当前时间是否超过check_time
                    else if (query.size() == 0 && jsonObject.getString("check_time").compareTo(date) < 0) {
                        jsonObject.put("isLate", "1");//逾期未完成
                    }
                }

            }
            query1 = RelaInfoList(query1, mysql, opt_user);
            logger.warn(String.valueOf(query1.size()));

            //通知通报列表
            sql = "select id,decode(content,'" + RIUtil.enContent + "') as content,decode(title,'" + RIUtil.enTitle + "') as title," +
                    "create_user,isTop,isNew,notice_time,readed,reading,label,type,subType,cycle,link ,old_author," +
                    "unit " +
                    "from notice where isdelete = 1 and is_notice = '1' " + where_sql2 + " order by isTop desc," +
                    "notice_time desc";
            logger.warn(sql);
            List<JSONObject> query2 = mysql.query(sql);

            if (query2.size() > 0) {
                int finalTt = tt;
                query2 = query2.stream().filter(ding_notice -> {
                    ding_notice.put("create_unit", "");
                    int t = 1;
                    if (ding_notice.containsKey("unit") && StringUtil.isNotBlank(ding_notice.getString("unit"))) {
                        if (RIUtil.dicts.get(ding_notice.getString("unit")).containsKey("type")) {
                            t = RIUtil.dicts.get(ding_notice.getString("unit")).getIntValue("type");
                        }
                    }

                    if ((t == 22 || t == 27 || t == 21) && (finalTt == 22 || finalTt == 27 || finalTt == 21)) {//支队
                        return true;
                    } else if ((t == 23 || t == 24 || t == 28 || t == 25 || t == 26) && (finalTt == 23 || finalTt == 24 || finalTt == 28)) {
                        return true;
                    } else if ((t == 25 || t == 26) && (finalTt == 25 || finalTt == 26)) {
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());
                String finalReading = reading;
                if (finalReading.length() > 0) {
                    query2 = query2.stream().filter(q2 -> {
                        //未读
                        if ("0".equals(finalReading)) {
                            if (q2.getString("reading").length() > 0 && !"[]".equals(q2.getString("reading")))
                                return true;
                        }
                        //已读
                        else if ("1".equals(finalReading)) {
                            if ("[]".equals(q2.getString("reading")) || q2.getString("reading").length() == 0)
                                return true;
                        }
                        return false;
                    }).collect(Collectors.toList());
                }
            }
            query2 = RelaInfoList(query2, mysql, opt_user);
            logger.warn(String.valueOf(query2.size()));

            //信息速递列表
            sql = "select * from express_info a where a.isdelete = 0" + where_sql3 + " order by a.update_time desc," +
                    "a.create_time desc";
            logger.warn(sql);
            List<JSONObject> query3 = mysql.query(sql);
            if (query3.size() > 0) {
                int finalTt = tt;
                query3 = query3.stream().filter(ding_notice -> {
                    ding_notice.put("create_unit", "");
                    int t = 1;
                    if (ding_notice.containsKey("unit") && StringUtil.isNotBlank(ding_notice.getString("unit"))) {
                        if (RIUtil.dicts.get(ding_notice.getString("unit")).containsKey("type")) {
                            t = RIUtil.dicts.get(ding_notice.getString("unit")).getIntValue("type");
                        }
                    }

                    if ((t == 22 || t == 27 || t == 21) && (finalTt == 22 || finalTt == 27 || finalTt == 21)) {//支队
                        return true;
                    } else if ((t == 23 || t == 24 || t == 28 || t == 25 || t == 26) && (finalTt == 23 || finalTt == 24 || finalTt == 28)) {
                        return true;
                    } else if ((t == 25 || t == 26) && (finalTt == 25 || finalTt == 26)) {
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());
                for (JSONObject jsonObject : query3) {
                    JSONArray jsonArray = new JSONArray();
                    if (jsonObject.containsKey("online") && !"0".equals(jsonObject.getString("online"))) {
                        String[] split = jsonObject.getString("online").split(",");
                        for (String string : split) {
                            if (ObjectUtil.isEmpty(RIUtil.dicts.get(string))) {
                                jsonArray.add(new JSONObject());
                            } else {
                                jsonArray.add(RIUtil.dicts.get(string));
                            }
                        }
                    }
                    jsonObject.put("online_dict", jsonArray);

                    //录用分数
                    sql = "select * from express_his where real_id = '" + jsonObject.getString("id") + "' and flow = " +
                            "9 order by opt_time limit 1";
                    List<JSONObject> query = mysql.query(sql);
                    if (!query.isEmpty()) {
                        jsonObject.put("offer_score", query.get(0).getDoubleValue("offer_score"));
                    } else {
                        jsonObject.put("offer_score", (double) 0);
                    }
                    //首次上报时间
                    sql = "select * from express_his where real_id = '" + jsonObject.getString("id") + "' and flow in (1,2,5) order by opt_time limit 1";
                    List<JSONObject> q = mysql.query(sql);
                    if (!q.isEmpty()) {
                        jsonObject.put("first_up_time", q.get(0).getString("opt_time"));
                    } else {
                        jsonObject.put("first_up_time", "");
                    }
                    //分局最后一次上报时间
                    sql = "select max(opt_time) as up_time from express_his where real_id = '" + jsonObject.getString("id") + "' and flow = 2";
                    List<JSONObject> q2 = mysql.query(sql);
                    if (!q2.isEmpty()) {
                        jsonObject.put("up_time", q2.get(0).getString("up_time"));
                    } else {
                        jsonObject.put("up_time", "");
                    }
                    //最后一次录用时间
                    sql = "select max(opt_time) as flow_time from express_his where real_id = '" + jsonObject.getString("id") + "' and flow = 9";
                    List<JSONObject> q3 = mysql.query(sql);
                    if (!q3.isEmpty()) {
                        jsonObject.put("flow_time", q3.get(0).getString("flow_time"));
                    } else {
                        jsonObject.put("flow_time", "");
                    }
                }
            }
            query3 = RelaInfo(query3, mysql, opt_user);

            JSONObject all = new JSONObject();
            JSONObject t = new JSONObject();
            String string = RealDictNames(RIUtil.StringToList(type));

            if ("盯办任务".equals(string)) {
                String file_id = "";
                if (isExp == 1) {
                    JSONObject excel = new JSONObject();
                    excel.put("files", query1);
                    logger.warn("开始导出");
                    file_id = String.valueOf(ExceptionFile2(excel.getJSONArray("files"), type));
                    back.put("file_id", file_id);
                }
            } else if ("通知通告".equals(string)) {
                String file_id = "";
                if (isExp == 1) {
                    JSONObject excel = new JSONObject();
                    excel.put("files", query2);
                    logger.warn("开始导出");
                    file_id = String.valueOf(ExceptionFile2(excel.getJSONArray("files"), type));
                    back.put("file_id", file_id);
                }
            } else if ("工作通报".equals(string)) {
                String file_id = "";
                if (isExp == 1) {
                    JSONObject excel = new JSONObject();
                    excel.put("files", query2);
                    logger.warn("开始导出");
                    file_id = String.valueOf(ExceptionFile2(excel.getJSONArray("files"), type));
                    back.put("file_id", file_id);
                }
            } else {
                String file_id = "";
                if (isExp == 1) {
                    JSONObject excel = new JSONObject();
                    excel.put("files", query3);
                    logger.warn("开始导出");
                    file_id = String.valueOf(ExceptionFile2(excel.getJSONArray("files"), type));
                    back.put("file_id", file_id);
                }
            }

            t.put("count", query3.size());
            query3 = query3.stream().skip((page - 1) * limit).limit(limit).
                    collect(Collectors.toList());
            t.put("data", query3);
            all.put("express", t);

            t = new JSONObject();
            t.put("count", query1.size());
            query1 = query1.stream().skip((page - 1) * limit).limit(limit).
                    collect(Collectors.toList());
            t.put("data", query1);
            all.put("ding_notice", t);

            List<JSONObject> query2_copy = query2;
            t = new JSONObject();
            t.put("count", query2.size());
            query2 = query2.stream().skip((page - 1) * limit).limit(limit).
                    collect(Collectors.toList());
            t.put("data", query2);
            all.put("notice_tztg", t);

            t = new JSONObject();
            t.put("count", query2_copy.size());
            query2_copy = query2_copy.stream().skip((page - 1) * limit).limit(limit).
                    collect(Collectors.toList());
            t.put("data", query2_copy);
            all.put("notice_gztb", t);

            if ("盯办任务".equals(string)) {
                back.put("data", all.getJSONObject("ding_notice").getJSONArray("data"));
                back.put("count", all.getJSONObject("ding_notice").getIntValue("count"));
            } else if ("通知通告".equals(string)) {
                back.put("data", all.getJSONObject("notice_tztg").getJSONArray("data"));
                back.put("count", all.getJSONObject("notice_tztg").getIntValue("count"));
            } else if ("工作通报".equals(string)) {
                back.put("data", all.getJSONObject("notice_gztb").getJSONArray("data"));
                back.put("count", all.getJSONObject("notice_gztb").getIntValue("count"));
            } else {
                back.put("data", all.getJSONObject("express").getJSONArray("data"));
                back.put("count", all.getJSONObject("express").getIntValue("count"));
            }

        } catch (Exception ex) {
            return ErrNo.set(new JSONObject(),607001,ex.getMessage());
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    public static JSONArray UseridToNames(HashMap<String, String> members) {
        //logger.warn(members.toString());
        JSONArray back = new JSONArray();
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();

            for (Map.Entry<String, String> one : members.entrySet()) {
                String idNum = one.getKey();

//            String sql = "select id_num from user where id = '" + id + "' ";
//            String idNum = mysql.query_one("id_num", sql);


                JSONObject u = RIUtil1.users1.get(idNum);
                // logger.warn(idNum + "->-" + u.toString());
                if (u != null) {
                    back.add(u);
                }

            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }


        return back;

    }

    private List<JSONObject> RelaInfo(List<JSONObject> list, InfoModelHelper mysql, String opt_user) {

        HashMap<String, String> dets = new HashMap<>();
        try {
            String sql = "select dets from favo where create_user='" + opt_user + "' and isdelete=0";
            List<JSONObject> ll = mysql.query(sql);
            if (ll.size() > 0) {
                for (int i = 0; i < ll.size(); i++) {
                    JSONObject oo = ll.get(i);
                    String det = oo.getString("dets");
                    dets.putAll(RIUtil.StringToList(det));
                }
            }

        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
        }


        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String id_num = one.getString("create_user");
            one.put("create_user", RIUtil1.users1.get(id_num));
            String id = one.getString("id");
            if (dets.containsKey(id)) {
                one.put("favo_id", "1");
            } else {
                one.put("favo_id", "");
            }
            back.add(one);
        }
        return back;
    }

    private static List<JSONObject> RelaInfoList(List<JSONObject> list, InfoModelHelper mysql, String opt_user) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String id = one.getString("id");
            //create_user
            String create_user = one.getString("create_user");
//            String sql = "select id_num from user where id = '" + create_user + "' ";
//            String idNum = mysql.query_one("id_num", sql);
            one.put("create_user", RIUtil1.users1.get(create_user));

            String old_author = one.getString("old_author");

            try {
                one.put("old_author", RIUtil1.users1.get(old_author));
            } catch (Exception ex) {
                one.put("old_author", new JSONObject());
            }
            String readed = one.getString("readed");
            if (readed.length() > 0) {
                one.put("readed", UseridToNames(RIUtil.StringToList(readed)));
            }
            //未读
            String reading = one.getString("reading");
            if (reading.length() > 0) {
                JSONArray readingJ = UseridToNames(RIUtil.StringToList(reading));
                one.put("reading", readingJ);
            } else {
                one.put("reading", new JSONArray());
            }

            String content = one.getString("content");
            if (content.length() > 300) {
                content = content.substring(0, 299);
            }
            one.put("content", content);

            //标签
            if (one.containsKey("label")) {
                String label = one.getString("label");
                if (label.length() > 0) {

                    String labelOne = RealDictNames(RIUtil.StringToList(label));

                    one.put("label", labelOne);
                    one.put("label_id", label);
                } else {
                    one.put("label", "");
                    one.put("label_id", "");
                }
            } else {
                one.put("label", "");
                one.put("label_id", "");
            }

            if (one.containsKey("unit")) {
                String unit = one.getString("unit");
                one.put("unit_name", RealDictNames(RIUtil.StringToList(unit)));
            } else {
                one.put("unit_name", "");
            }


            if (one.containsKey("type")) {
                String type = one.getString("type");
                one.put("type", RIUtil.dicts.get(type));
            } else {
                one.put("type_name", new JSONObject());
            }

            if (one.containsKey("subType")) {
                String subType = one.getString("subType");
                one.put("subType", RIUtil.dicts.get(subType));
            } else {
                one.put("subType_name", new JSONObject());
            }


            //关联ding
            int count = 0;
            if (opt_user != null && opt_user.length() > 0) {
                String sql =
                        "select count(id) as count from ding_msg where readed=0 and real_id='" + id + "' and " +
                                "accepter" + "='" + opt_user + "'";
                count = mysql.query_count(sql);

            }
            one.put("ding", count);

            //reading count
//            String reading = one.getString("reading");
//            if (reading.length() > 0) {
//                one.put("reading", UseridToNames(RIUtil.StringToList(reading)).size());
//            }
            //comment

            String sql = "select count(id) as count from comment where notice_id='" + id + "' and isdelete=1 ";
            count = mysql.query_count(sql);

            one.put("comment", count);

            //签收时见
            sql = "select time,opt_user,opts from ding_form_logs where ding_id='" + one.getString("id") + "' " +
                    "and opts !='签收'" + " order  by time,id desc";
            List<JSONObject> checks = mysql.query(sql);
            List<JSONObject> clogs = new ArrayList<>();
            if (checks.size() > 0) {
                for (int c = 0; c < checks.size(); c++) {
                    JSONObject cone = checks.get(c);
                    String user_id = cone.getString("opt_user");
//                    sql = "select id_num from user where id = '" + user_id + "' ";
//                    idNum = mysql.query_one("id_num", sql);
                    cone.put("user", RIUtil1.users1.get(user_id));
                    clogs.add(cone);
                }
                one.put("check_log", clogs);
            } else {
                one.put("check_log", new ArrayList<>());
            }

            back.add(one);

        }
        return back;
    }
}
