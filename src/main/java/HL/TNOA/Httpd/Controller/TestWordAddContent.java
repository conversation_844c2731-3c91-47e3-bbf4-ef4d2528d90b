package HL.TNOA.Httpd.Controller;

import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.TNOAConf;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import freemarker.cache.FileTemplateLoader;
import freemarker.cache.TemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class TestWordAddContent {
    private static Logger logger = LoggerFactory.getLogger(TestWordAddContent.class);

    public static int Word(List<JSONObject> data, InfoModelHelper mysql) {

        Map<String, Object> temp = new HashMap<String, Object>();
        String name = "";
        if (data.size() > 0) {
            for (int i = 0; i < data.size(); i++) {
                JSONObject one = data.get(i);
                if (one.containsKey("num") && one.getString("num") != null && one.getString("num").length() > 0) {
                    temp.put("num", one.getString("num"));
                } else {
                    temp.put("num", "");
                }
                if (one.containsKey("name") && one.getString("name") != null && one.getString("name").length() > 0) {
                    temp.put("name", one.getString("name"));
                } else {
                    temp.put("name", "");
                }
                if (one.containsKey("birth") && one.getString("birth") != null && one.getString("birth").length() > 0) {
                    temp.put("birth", one.getString("birth"));
                } else {
                    temp.put("birth", "");
                }
                if (one.containsKey("sex") && one.getString("sex") != null && one.getString("sex").length() > 0) {
                    String sex = one.getString("sex");
                    if ("1".equals(sex)) {
                        temp.put("sex", "女");
                    } else if ("2".equals(sex)) {
                        temp.put("sex", "男");
                    }else {
                        temp.put("sex", "");
                    }
                } else {
                    temp.put("sex", "");
                }
                if (one.containsKey("tel") && one.getString("tel") != null && one.getString("tel").length() > 0) {
                    temp.put("tel", one.getString("tel"));
                } else {
                    temp.put("tel", "");
                }
                if (one.containsKey("address") && one.getString("address") != null && one.getString("address").length() > 0) {
                    temp.put("address", one.getString("address"));
                } else {
                    temp.put("address", "");
                }
                if (one.containsKey("id_num") && one.getString("id_num") != null && one.getString("id_num").length() > 0) {
                    temp.put("id_num", one.getString("id_num"));
                } else {
                    temp.put("id_num", "");
                }
                if (one.containsKey("aj_num") && one.getString("aj_num") != null && one.getString("aj_num").length() > 0) {
                    temp.put("aj_num", one.getString("aj_num"));
                } else {
                    temp.put("aj_num", "");
                }
                if (one.containsKey("aj_reasor") && one.getString("aj_reasor") != null && one.getString("aj_reasor").length() > 0) {
                    temp.put("aj_reasor", one.getString("aj_reason"));
                } else {
                    temp.put("aj_reasor", "");
                }
                if (one.containsKey("tazs") && one.getString("tazs") != null && one.getString("tazs").length() > 0) {
                    String tazs = one.getString("tazs");
                    if (tazs.contains("1")) {
                        temp.put("tazs", "√");
                    } else {
                        temp.put("tazs", "×");
                    }
                    if (tazs.contains("2")) {
                        temp.put("zach", "√");
                    } else {
                        temp.put("zach", "×");
                    }
                    if (tazs.contains("3")) {
                        temp.put("jxpw", "√");
                    } else {
                        temp.put("jxpw", "×");
                    }
                    if (tazs.contains("4")) {
                        temp.put("xsch", "√");
                    } else {
                        temp.put("xsch", "×");
                    }
                    if (tazs.contains("5")) {
                        temp.put("jc", "√");
                    } else {
                        temp.put("jc", "×");
                    }
                    if (tazs.contains("6")) {
                        temp.put("xsjl", "√");
                    } else {
                        temp.put("xsjl", "×");
                    }
                    if (tazs.contains("7")) {
                        temp.put("qbhs", "√");
                    } else {
                        temp.put("qbhs", "×");
                    }
                    if (tazs.contains("8")) {
                        temp.put("jsjs", "√");
                    } else {
                        temp.put("jsjs", "×");
                    }
                    if (tazs.contains("9")) {
                        temp.put("db", "√");
                    } else {
                        temp.put("db", "×");
                    }
                    if (tazs.contains("10")) {
                        temp.put("bhr", "√");
                    } else {
                        temp.put("bhr", "×");
                    }
                    if (tazs.contains("11")) {
                        temp.put("zr", "√");
                    } else {
                        temp.put("zr", "×");
                    }
                    if (tazs.contains("12")) {
                        temp.put("qzns", "√");
                    } else {
                        temp.put("qzns", "×");
                    }
                    if (tazs.contains("13")) {
                        temp.put("qt1", "√");
                    } else {
                        temp.put("qt1", "×");
                    }
                    temp.put("qt2", "");
                } else {
                    temp.put("tazs", "");
                    temp.put("zach", "");
                    temp.put("jxpw", "");
                    temp.put("xsch", "");
                    temp.put("jc", "");
                    temp.put("xsjl", "");
                    temp.put("qbhs", "");
                    temp.put("jsjs", "");
                    temp.put("db", "");
                    temp.put("bhr", "");
                    temp.put("zr", "");
                    temp.put("qzns", "");
                    temp.put("qt1", "");
                    temp.put("qt2", "");
                }
                if (one.containsKey("file_num") && one.getString("file_num") != null && one.getString("file_num").length() > 0) {
                    temp.put("file_num", one.getString("file_num"));
                } else {
                    temp.put("file_num", "");
                }
                if (one.containsKey("in_time") && one.getString("in_time") != null && one.getString("in_time").length() > 0) {
                    temp.put("in_time", one.getString("in_time"));
                } else {
                    temp.put("in_time", "");
                }
                if (one.containsKey("self") && one.getString("self") != null && one.getString("self").length() > 0) {
                    temp.put("self", one.getString("self"));
                } else {
                    temp.put("self", "");
                }
                if (one.containsKey("check") && one.getString("check") != null && one.getString("check").length() > 0) {
                    temp.put("check", one.getString("check"));
                } else {
                    temp.put("check", "");
                }
                if (one.containsKey("info_catch") && one.getString("info_catch") != null && one.getString("info_catch").length() > 0) {
                    String info_catch = one.getString("info_catch");
                    if (info_catch.contains("0")) {
                        temp.put("info_catch", "否");
                    }
                    if (info_catch.contains("1")) {
                        temp.put("info_catch", "是");
                    }
                } else {
                    temp.put("info_catch", "");
                }
                if (one.containsKey("info_in") && one.getString("info_in") != null && one.getString("info_in").length() > 0) {
                    String info_in = one.getString("info_in");
                    if (info_in.contains("0")) {
                        temp.put("info_in", "否");
                    }
                    if (info_in.contains("1")) {
                        temp.put("info_in", "是");
                    }
                } else {
                    temp.put("info_in", "");
                }
                if (one.containsKey("zw") && one.getString("zw") != null && one.getString("zw").length() > 0) {
                    String zw = one.getString("zw");
                    if (zw.contains("1")) {
                        temp.put("zw", "√");
                    } else {
                        temp.put("zw", "×");
                    }
                    if (zw.contains("2")) {
                        temp.put("xy", "√");
                    } else {
                        temp.put("xy", "×");
                    }
                    if (zw.contains("3")) {
                        temp.put("bj", "√");
                    } else {
                        temp.put("bj", "×");
                    }
                    if (zw.contains("4")) {
                        temp.put("ny", "√");
                    } else {
                        temp.put("ny", "×");
                    }
                    if (zw.contains("5")) {
                        temp.put("qt", "√");
                    } else {
                        temp.put("qt", "×");
                    }
                } else {
                    temp.put("zw", "");
                    temp.put("xy", "");
                    temp.put("bj", "");
                    temp.put("ny", "");
                    temp.put("qt", "");
                }
                if (one.containsKey("check_check") && one.getString("check_check") != null && one.getString("check_check").length() > 0) {
                    String check_check = one.getString("check_check");
                    if (check_check.contains("0")) {
                        temp.put("check_check", "否");
                    }
                    if (check_check.contains("1")) {
                        temp.put("check_check", "是");
                    }
                } else {
                    temp.put("check_check", "");
                }
                if (one.containsKey("ask_log") && one.getString("ask_log").length() > 0 && one.getString("ask_log") != null) {
                    JSONArray two = one.getJSONArray("ask_log");
//					System.out.println(two.toString());
                    int tem = 0;
                    for (int k = 0; k < two.size() && k <= 10; k++) {
                        JSONObject a = (JSONObject) two.get(k);
//							System.out.println(a.toString());
//							System.out.println(a.containsKey("startTime")&&a.getString("startTime") != null);
//							System.out.println("startTime"+(k+1));
                        if (a.containsKey("startTime") && a.getString("startTime") != null && a.getString("startTime").length() > 0) {
                            temp.put("startTime" + (k + 1), a.getString("startTime"));
                        } else {
                            temp.put("startTime" + (k + 1), "");
                        }
                        if (a.containsKey("roomname") && a.getString("roomname") != null && a.getString("roomname").length() > 0) {
                            temp.put("roomname" + (k + 1), a.getString("roomname"));
                        } else {
                            temp.put("roomname" + (k + 1), "");
                        }
                        if (a.containsKey("police") && a.getString("police") != null && a.getString("police").length() > 0) {
                            temp.put("police" + (k + 1), a.getString("police"));
                        } else {
                            temp.put("police" + (k + 1), "");
                        }
                        if (a.containsKey("endTime") && a.getString("endTime") != null && a.getString("endTime").length() > 0) {
                            temp.put("endTime" + (k + 1), a.getString("endTime"));
                        } else {
                            temp.put("endTime" + (k + 1), "");
                        }
                        if (a.containsKey("recordId") && a.getString("recordId") != null && a.getString("recordId").length() > 0) {
                            temp.put("recordID" + (k + 1), a.getString("recordId"));
                        } else {
                            temp.put("recordID" + (k + 1), "");
                        }
                        if (a.containsKey("content") && a.getString("content") != null && a.getString("content").length() > 0) {
                            temp.put("content" + (k + 1), a.getString("content"));
                        } else {
                            temp.put("content" + (k + 1), "");
                        }
                        tem = k + 1;
                    }

                    for (int z = tem; z <= 10; z++) {
                        temp.put("startTime" + (z + 1), "");
                        temp.put("endTime" + (z + 1), "");
                        temp.put("content" + (z + 1), "");
                        temp.put("recordID" + (z + 1), "");
                        temp.put("roomname" + (z + 1), "");
                        temp.put("police" + (z + 1), "");
                    }

                } else {
                    for (int j = 1; j <= 11; j++) {
                        temp.put("startTime" + j, "");
                        temp.put("endTime" + j, "");
                        temp.put("content" + j, "");
                        temp.put("recordID" + j, "");
                        temp.put("roomname" + j, "");
                        temp.put("police" + j, "");
                    }
                }
                if (one.containsKey("out_ls") && one.getString("out_ls").length() > 0 && one.getJSONArray("out_ls").size() > 0) {
                    JSONArray out_ls = one.getJSONArray("out_ls");
                    System.out.println(out_ls.toString());
                    int tem2 = 0;
                    for (int m = 0; m < out_ls.size() && m <= 1; m++) {
                        JSONObject o = (JSONObject) out_ls.get(m);
                        System.out.println(o.toString());
                        System.out.println(one.containsKey("startTime") && one.getString("startTime") != null);
                        if (o.containsKey("startTime") && o.getString("startTime") != null && o.getString("startTime").length() > 0) {
                            temp.put("startTime" + (m + 12), o.getString("startTime") + " 至 ");
                        } else {
                            temp.put("startTime" + (m + 12), "");
                        }
                        if (o.containsKey("endTime") && o.getString("endTime") != null && o.getString("endTime").length() > 0) {
                            temp.put("endTime" + (m + 12), o.getString("endTime"));
                        } else {
                            temp.put("endTime" + (m + 12), "");
                        }
                        if (o.containsKey("outreason") && o.getString("outreason") != null && o.getString("outreason").length() > 0) {
                            temp.put("outreason" + (m + 1), o.getString("outreason"));
                        } else {
                            temp.put("outreason" + (m + 1), "");
                        }
                        tem2 = m + 1;
                    }
//					System.out.println(temp.get("startTime12")+":"+temp.get("endTime12"));
//					System.out.println(tem2);
                    for (int u = tem2; u <= 1; u++) {
                        temp.put("startTime" + (u + 12), "");
                        temp.put("endTime" + (u + 12), "");
                        temp.put("outreason" + (u + 1), "");
                    }
                } else {
                    temp.put("startTime12", "");
                    temp.put("startTime13", "");
                    temp.put("endTime12", "");
                    temp.put("endTime13", "");
                    temp.put("outreason1", "");
                    temp.put("outreason2", "");
                }
                System.out.println(temp.get("startTime12") + ":" + temp.get("endTime12"));
                if (one.containsKey("out_time") && one.getString("out_time") != null && one.getString("out_time").length() > 0) {
                    temp.put("out_time", one.getString("out_time"));
                } else {
                    temp.put("out_time", "");
                }
                if (one.containsKey("out_reason") && one.getString("out_reason") != null && one.getString("out_reason").length() > 0) {
                    temp.put("out_reason", one.getString("out_reason"));
                } else {
                    temp.put("out_reason", "");
                }
                if (one.containsKey("wpcl") && one.getString("wpcl") != null && one.getString("wpcl").length() > 0) {
                    String wpcl = one.getString("wpcl");
                    if (wpcl.contains("0")) {
                        temp.put("qbfh", "√");
                    } else {
                        temp.put("qbfh", "×");
                    }
                    if (wpcl.contains("1")) {
                        temp.put("bffh", "√");
                    } else {
                        temp.put("bffh", "×");
                    }
                } else {
                    temp.put("bffh", "");
                    temp.put("qbfh", "");
                }
                if (one.containsKey("notback") && one.getString("notback") != null && one.getString("notback").length() > 0) {
                    temp.put("notback", one.getString("notback"));
                } else {
                    temp.put("notback", "");
                }

            }
        }
        logger.warn(temp.toString());
//		Map<String, Object> cont = new HashMap<String, Object>();// 存储数据
//		cont.put("name", "沈方逸");
//		cont.put("sex", "女");
//		cont.put("num", "20220425001");
//		cont.put("birth", "1990-06-08");
//		cont.put("address", "山兔村18号");
        int id = 0;
        try {
            //模板的路径
            File fir = new File("./");
            String basePath = TNOAConf.get("file", "img_path");
            String filePath = new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";
            //生成文件的路径及文件名。
            SimpleDateFormat a = new SimpleDateFormat("yyyyMMddHHmmss");
            String format = a.format(new Date());
            //生成文件的路径及文件名。
            File outFile = new File(basePath + filePath + format + name + "办案区.docx");
            Writer out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile), "UTF-8"));

            // 使用FileTemplateLoader
            //指定模板路径
            TemplateLoader templateLoader = null;
            templateLoader = new FileTemplateLoader(fir);
            String tempname = "办案区.xml";

            Configuration cfg = new Configuration();
            cfg.setTemplateLoader(templateLoader);
            Template t = cfg.getTemplate(tempname, "UTF-8");

            t.process(temp, out);
            out.flush();
            out.close();
            String sql = "insert into upload (nas_id,file_path,file_name) values" + " ('1','" + filePath + "','" + format + name + "办案区.docx" + "')";
            logger.warn(sql);
            mysql.update(sql);
            sql = "select * from upload where file_name='" + format + name + "办案区.docx' AND file_path='" + filePath + "'";
            logger.warn(sql);
            List<JSONObject> result = mysql.query(sql);
            logger.warn(result.toString());
            JSONObject xJsonObject = result.get(0);
            id = xJsonObject.getIntValue("id");

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }
        return id;
    }

}


