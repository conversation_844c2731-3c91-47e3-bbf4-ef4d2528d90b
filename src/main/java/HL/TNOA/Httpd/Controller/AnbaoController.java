package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
public class AnbaoController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());

    @RequestMapping(method = {RequestMethod.POST}, path = {"/anbao_info"})
    @PassToken
    public JSONObject get_anbao_info(TNOAHttpRequest request) throws Exception {
        logger.warn("anbao_info--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_anbao_info")) {
                return getAnBao(data);
            } else if (opt.equals("create_anbao_info")) {
                return createAnBao(data, request.getRemoteAddr());
            } else if (opt.equals("update_anbao_info")) {
                return updateAnBao(data, request.getRemoteAddr());
            } else if (opt.equals("delete_anbao_info")) {
                return deleteAnBao(data, request.getRemoteAddr());
            } else {
                return ErrNo.set(503009);
            }
        } else {
            return ErrNo.set(503009);
        }
    }

    //******CREATE*******
    private JSONObject createAnBao(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String title = "";
            String start_time = "";
            String end_time = "";
            String remark = "";
            String level = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "1";
            String unit = "";
            if (data.containsKey("title") && data.getString("title").length() > 0) {
                title = data.getString("title");
            } else {
                return ErrNo.set(503002);
            }
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time");
            } else {
                return ErrNo.set(503002);
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time");
            } else {
                return ErrNo.set(503002);
            }
            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
            }
            if (data.containsKey("level") && data.getString("level").length() > 0) {
                level = data.getString("level");
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");

                JSONObject one = RIUtil.users.get(create_user);
                unit = one.getString("unit").split(",")[0];
                JSONObject done = RIUtil.dicts.get(unit);
                int type = done.getInteger("type");
                if (type == 21 || type == 22 || type == 27) {
                    unit = "3204";
                } else if (type == 23 || type == 28 || type == 24) {
                    unit = unit.substring(0, 6);
                } else {
                    unit = unit.substring(0, 8);
                }

            } else {
                return ErrNo.set(503002);
            }
            String sqls = "insert anbao_info (title,start_time,end_time,remark,level,create_user,create_time," +
                    "isdelete,unit)values('" + title + "','" + start_time + "','" + end_time + "','" + remark + "','" + level + "','" + create_user + "','" + create_time + "','" + isdelete + "','" + unit + "')";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建安保信息", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private JSONObject getAnBao(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String title = "";
            String start_time_start = "";
            String start_time_end = "";
            String remark = "";
            String level = "";
            String create_user = "";
            String create_time_start = "";
            String create_time_end = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("title") && data.getString("title").length() > 0) {
                title = data.getString("title");
                sql = sql + " title like '%" + title + "%' and ";
            }
            if (data.containsKey("start_time_start") && data.getString("start_time_start").length() > 0) {
                start_time_start = data.getString("start_time_start");
                sql = sql + " start_time<='" + start_time_start + " 23:59:59' and ";
                sql = sql + " end_time>='" + start_time_start + " 00:00:00' and ";
            }
            if (data.containsKey("start_time_end") && data.getString("start_time_end").length() > 0) {
                start_time_end = data.getString("start_time_end");

            }
            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
                sql = sql + " remark='" + remark + "' and ";
            }
            if (data.containsKey("level") && data.getString("level").length() > 0) {
                level = data.getString("level");
                sql = sql + " level='" + level + "' and ";
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' and ";
            }
            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
                create_time_start = data.getString("create_time_start");
                sql = sql + "create_time>='" + create_time_start + "' and ";
            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end");
                sql = sql + "create_time<='" + create_time_end + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }
            String opt_user = "";

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
                JSONObject one = RIUtil.users.get(opt_user);
                String unit = one.getString("unit").split(",")[0];
                JSONObject done = RIUtil.dicts.get(unit);
                int type = done.getInteger("type");
                if (type == 21 || type == 22 || type == 27) {
                    sql = sql + "  unit='3204' and ";
                } else if (type == 23 || type == 28 || type == 24) {
                    sql = sql + " (unit='3204' or  unit='" + unit.substring(0, 6) + "' ) and ";
                } else {
                    sql = sql + " (unit='3204' or  unit='" + unit.substring(0, 6) + "' or unit ='" + unit.substring(0
                            , 8) + "' ) " +
                            "and ";
                }

            } else {
                return ErrNo.set(503004);
            }

            String sqls = "select * from anbao_info where 1=1 and " + sql + " isdelete=1  limit " + limit + " offset "
                    + limit * (page - 1);
            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select count(id) as count from anbao_info where 1=1 and " + sql + " isdelete=1; ";
                back.put("count", mysql.query_count(sqls));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(503005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            one.put("create_user", RIUtil.users.get(one.getString("create_user")));
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private JSONObject updateAnBao(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String title = "";
            String start_time = "";
            String end_time = "";
            String remark = "";
            String level = "";
            String opt_user = "";
            String unit = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                if (unit.endsWith("00000000")) {
                    unit = "3204";
                } else if (unit.endsWith("000000")) {
                    unit = unit.substring(0, 6);
                } else {
                    unit = unit.substring(0, 8);
                }
            } else {
                unit = "3204";
            }
            sql = sql + " unit='" + unit + "' , ";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(503004);
            }
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' , ";
            }
            if (data.containsKey("title") && data.getString("title").length() > 0) {
                title = data.getString("title");
                sql = sql + " title='" + title + "' , ";
            }
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time");
                sql = sql + " start_time='" + start_time + "' , ";
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time");
                sql = sql + " end_time='" + end_time + "' , ";
            }
            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
                sql = sql + " remark='" + remark + "' , ";
            }
            if (data.containsKey("level") && data.getString("level").length() > 0) {
                level = data.getString("level");
                sql = sql + " level='" + level + "' , ";
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(503004);
            }
            String sqls = "update anbao_info set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新安保信息", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(503003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private JSONObject deleteAnBao(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(503008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(503008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update anbao_info set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除安保信息", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(503007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }
}
