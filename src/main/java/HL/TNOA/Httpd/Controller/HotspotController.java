package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.MysqlHelper;
import com.alibaba.fastjson.JSONObject;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class HotspotController {
    private static Logger logger = LoggerFactory.getLogger(HotspotController.class);

    @RequestMapping(method = {RequestMethod.POST}, path = {"/hotspot"})
    public JSONObject Appeal(TNOAHttpRequest request) throws Exception {
        JSONObject params = request.getRequestParams();
        String opt = "";
        if (params.containsKey("opt") && Strings.isNotEmpty(params.getString("opt"))) {
            opt = params.getString("opt");
        }

        if ("get_list".equals(opt)) {
            return get_list(params);
        } else if ("get_one".equals(opt)) {
            return get_one(params);
        }

        return ErrNo.set(150000);
    }

    public static JSONObject get_list(JSONObject params) throws Exception {


        String where_sql = "";
        String id = "";
        if (params.containsKey("id") && params.getString("id").length() > 0) {
            id = params.getString("id");
            where_sql += " and id in ('" + id.replace(",", "','") + "') ";
        }

        if (params.containsKey("start_date") && Strings.isNotEmpty(params.getString("start_date"))) {
            String start_date = params.getString("start_date");
            where_sql += " and DATE_FORMAT(date, '%Y-%m-%d') >= '" + start_date + "' ";
        }

        if (params.containsKey("end_date") && Strings.isNotEmpty(params.getString("end_date"))) {
            String end_date = params.getString("end_date");
            where_sql += " and DATE_FORMAT(date, '%Y-%m-%d') <= '" + end_date + "' ";
        }

        if (params.containsKey("title") && Strings.isNotEmpty(params.getString("title"))) {
            String title = params.getString("title");
            where_sql += " and title like '%" + title + "%' ";
        }

        if (params.containsKey("type") && params.getString("type").length() > 0) {
            String type = params.getString("type");
            where_sql += " and type in ('" + type.replace(",", "','") + "') ";
        }

        if (params.containsKey("start_see") && Strings.isNotEmpty(params.getString("start_see"))) {
            String start_see = params.getString("start_see");
            where_sql += " and see >= '" + start_see + "' ";
        }

        if (params.containsKey("end_see") && Strings.isNotEmpty(params.getString("end_see"))) {
            String end_see = params.getString("end_see");
            where_sql += " and see <= '" + end_see + "' ";
        }

        if (params.containsKey("is_new") && Strings.isNotEmpty(params.getString("is_new"))) {
            String is_new = params.getString("is_new");
            where_sql += " and is_new = '" + is_new + "' ";
        }

        if (params.containsKey("query") && Strings.isNotEmpty(params.getString("query"))) {
            String query = params.getString("query");
            where_sql += " and (title like '%" + query + "%' or content like '%" + query + "%' or html like '%" + query + "%' ) ";
        }

        String order = " date desc ";
        if (params.containsKey("order") && Strings.isNotEmpty(params.getString("order"))) {
            order = params.getString("order");
        }

        String limit_sql = "";
        int page = 1;
        int limit = 20;
        if (params.containsKey("page") && params.containsKey("limit")) {
            page = params.getIntValue("page");
            limit = params.getIntValue("limit");
        }
        int os = (page - 1) * limit;
        limit_sql = " limit " + os + "," + limit;

        MysqlHelper mysql = null;
        try {

            mysql = new MysqlHelper("mysql_jk");


            String data_sql = "select id, title, DATE_FORMAT(date, '%Y-%m-%d') as date, see, type, type_auto, " +
                    "is_new,'化龙巷' as source,substr(content from 1 for 30) as content,keyword from jingkai_hotspot " +
                    "where 1=1 " + where_sql + " order by " + order + " " + limit_sql;
            String count_sql = "select count(*) as count from jingkai_hotspot where 1=1 " + where_sql;

            logger.warn("-MAIN.HOTSPOT->" + data_sql);
            List<JSONObject> list = mysql.query(data_sql);
            int count = mysql.query_count(count_sql);
//        String sql = "";
//        for (JSONObject one : list) {
//            String _id = one.getString("id");
//            if (Strings.isEmpty(_id)) {
//                continue;
//            }
//            sql = "select * from reply where reply.hotspotid='" + _id + "' order by date desc ";
//            one.put("reply", mysql.query(sql));
//        }


            JSONObject back = ErrNo.set(0);
            back.put("data", list);
            back.put("count", count);
            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            mysql.close();
        }
    }

    public JSONObject get_one(JSONObject params) throws Exception {


        String where_sql = "";
        String id = "";
        if (params.containsKey("id") && Strings.isNotEmpty(params.getString("id"))) {
            id = params.getString("id");
            where_sql += " and id = '" + id + "' ";
        }

//        if (params.containsKey("start_date") && Strings.isNotEmpty(params.getString("start_date"))) {
//            String start_date = params.getString("start_date");
//            where_sql += " and DATE_FORMAT(date, '%Y-%m-%d') >= '" + start_date + "' ";
//        }
//
//        if (params.containsKey("end_date") && Strings.isNotEmpty(params.getString("end_date"))) {
//            String end_date = params.getString("end_date");
//            where_sql += " and DATE_FORMAT(date, '%Y-%m-%d') <= '" + end_date + "' ";
//        }
//
//        if (params.containsKey("is_new") && Strings.isNotEmpty(params.getString("is_new"))) {
//            String is_new = params.getString("is_new");
//            where_sql += " and is_new = '" + is_new + "' ";
//        }
//
//        if (params.containsKey("query") && Strings.isNotEmpty(params.getString("query"))) {
//            String query = params.getString("query");
//            where_sql += " and (title like '%" + query + "%' or content like '%" + query + "%' or html like '%" +
//            query + "%' ) ";
//        }

//        String limit_sql = "";
//        int page = 1;
//        int limit = 20;
//        if (params.containsKey("page") && params.containsKey("limit")) {
//            page = params.getIntValue("page");
//            limit = params.getIntValue("limit");
//        }
//        int os = (page - 1) * limit;
//        limit_sql = " limit " + os + "," + limit;


        MysqlHelper mysql = null;

        try {

            mysql = new MysqlHelper("mysql_jk");
            String data_sql = "select id, title, DATE_FORMAT(date, '%Y-%m-%d') as date, see, type, html, type_auto, " +
                    "content, is_new,'化龙巷' as source from jingkai_hotspot where 1=1 " + where_sql + " order by date " +
                    "desc ";

//        logger.warn("-MAIN.HOTSPOT->" + data_sql);
            JSONObject data = new JSONObject();
            List<JSONObject> list = mysql.query(data_sql);
            String sql = "";
            for (JSONObject one : list) {
                String _id = one.getString("id");
                if (Strings.isEmpty(_id)) {
                    continue;
                }
                sql = "select * from reply where reply.hotspotid='" + _id + "' order by date desc ";
                one.put("reply", mysql.query(sql));
            }

            if (list.size() > 0) {
                data = list.get(0);
            }


            JSONObject back = ErrNo.set(0);
            back.put("data", data);
            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));


        } finally {
            mysql.close();
        }
    }

}
