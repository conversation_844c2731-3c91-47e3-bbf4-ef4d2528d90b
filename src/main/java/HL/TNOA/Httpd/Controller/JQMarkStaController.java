package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.OracleHelper;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@RestController
public class JQMarkStaController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/jqmarksta"})

    public JSONObject get_dict(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        InfoModelHelper mysql = null;
        try {

            mysql = request.openInfoImpl();
            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("export_unmark_jq")) {
                    return GetUnmarkJq(data);
                } else if (opt.equals("list_unmark_jq")) {
                    return ListUnmarkJq(data);
                } else if (opt.equals("")) {
                }
                {
                    return ErrNo.set(505003);
                }
            } else {
                return ErrNo.set(505003);
            }
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private JSONObject ListUnmarkJq(JSONObject data) throws ParseException {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora = null;
        InfoModelHelper mysql = null;
        String sqls = "";
        String subSql = "";
        String pageSql = "";
        String timeSql = "";
        String fjSql = "";
        String countSql = "";
        String nowTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String start_time = RIUtil.GetNextDate(nowTime, -7);
        start_time = start_time + nowTime.substring(10);

        List<JSONObject> resList = new ArrayList<>();

        String unit = "";
        String name = "";
        String startTime = "";
        String endTime = "";
        String type = "";
        String jgType = "";
        String fj = "";
        int queryType = 1;
        int count = -1;
        int limit = 20;
        int page = 1;

        try {
            ora = new OracleHelper("ora_hl");
            mysql = InfoModelPool.getModel();


            if (data.containsKey("queryType")) {
                queryType = data.getIntValue("queryType");
            }
            if (data.containsKey("fj") && data.getString("fj").length() > 0) {
                fj = data.getString("fj");
                subSql += " and FJ = '" + fj + "' ";
            }
            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");
                subSql += " and dict_name like '%" + name + "%' ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }
            if (data.containsKey("startTime") && data.getString("startTime").length() > 0) {
                startTime = data.getString("startTime");
                timeSql += " and CJSJ >= '" + startTime + "' ";
            }
            if (data.containsKey("endTime") && data.getString("endTime").length() > 0) {
                endTime = data.getString("endTime");
                timeSql += " and CJSJ <= '" + endTime + "' ";
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject done = RIUtil.dicts.get(unit);
                String father_id = done.getString("father_id");

                try {
                    type = done.getString("type");
                } catch (Exception ex) {
                    type = "21";
                }
                logger.warn("type : " + type);

                if (type.equals("25") || type.equals("26")) {
                    subSql += " and (CJDWDM like '%" + unit.substring(0, 8) + "%') ";
                } else if (type.equals("23")) {
                    subSql += " and (CJDWDM like '%" + unit.substring(0, 6) + "%') ";
                } else if (type.equals("22")) {
                    subSql += " and (CJDWDM like '%" + unit.substring(0, 4) + "%') ";
                } else if (type.equals("24")) {
                    subSql = " and CJDWDM like '" + father_id.substring(0, 6) + "%' ";
                } else if (type.equals("28")) {
                    unit = unit.substring(0, 6);
                    subSql = " and CJDWDM like '" + unit + "%' ";
                }
            }


//            subSql += " and (PCS is not null and FJ is not null ) ";
//            subSql += " and (SFGLDZ_PDBZ is null or SFGLDZ_PDBZ = '0' or SFGLDZ_PDBZ = '1') ";

            pageSql = "  limit " + limit + " offset " + limit * (page - 1);

            if (type.equals("22") || type.equals("21")) {

                if (queryType == 1) {

                    sqls = " select count(JJBH) as COUNT, FJ as JGDM " + "from jqbz_kh " + "where TYPE = '1' " + timeSql + subSql + " " + "group by JGDM" + pageSql;


                    countSql =
                            "select count(distinct(FJ)) as COUNT " + "from jqbz_kh " + "where TYPE = '1' " + timeSql + subSql;

                } else if (queryType == 2) {

                    sqls = "select count(JJBH) as COUNT, FJ as JGDM " + "from jqbz_kh where TYPE = '2' " + timeSql + subSql + " group by JGDM" + pageSql;

                    countSql = "select count(FJ) as COUNT " + "from jqbz_kh " + "where TYPE = '2' " + timeSql + subSql;
                } else if (queryType == 3) {
                    sqls = "select count(distinct JJBH) as COUNT, FJ as JGDM " + "from jqbz_kh " + "where ZGCS>2" + timeSql + subSql + " group by JGDM" + pageSql;

                    countSql =
                            "select count(distinct(FJ)) as COUNT " + "from jqbz_kh " + "where ZGCS>2" + timeSql + subSql;
                }
            } else if (type.equals("25") || type.equals("26") || type.equals("28") || type.equals("23")) {
                //派出所
                if (queryType == 1) {

                    sqls = " select count(JJBH) as COUNT, PCS as JGDM " + "from jqbz_kh " + "where TYPE = '1' " + timeSql + subSql + " group by JGDM";

                    countSql =
                            " select count(distinct(PCS)) as COUNT " + "from jqbz_kh " + "where TYPE = '1'" + timeSql + subSql;

                } else if (queryType == 2) {

                    sqls = "select count(distinct JJBH) as COUNT, PCS as JGDM " + "from jqbz_kh " + "where TYPE = " + "'2'" + " " + timeSql + subSql + " group by JGDM" + pageSql;

                    countSql =
                            "select count(distinct(PCS)) as COUNT " + "from jqbz_kh " + "where TYPE = '2'" + timeSql + subSql;
                } else if (queryType == 3) {
                    sqls = "select count(distinct JJBH) as COUNT, PCS as JGDM " + "from jqbz_kh " + "where ZGCS>2" + timeSql + subSql + " group by JGDM" + pageSql;

                    countSql =
                            "select count(distinct(PCS)) as COUNT " + "from jqbz_kh " + "where ZGCS>2" + timeSql + subSql;
                }
            }

            logger.warn(sqls);
            //logger.warn(countSql);
            resList = mysql.query(sqls);
            logger.warn(resList.toString());
            count = mysql.query_count(countSql);

            for (JSONObject one : resList) {
                String JGMC = "";
                String JGDM = "";
                String JJBH = one.getString("JGDM");
                //不知道为啥别名不生效  在这里手动加上
                if (one.containsKey("FJ")) {
                    JGDM = one.getString("FJ");
                } else if (one.containsKey("PCS")) {
                    JGDM = one.getString("PCS");
                }
                one.put("JGDM", JGDM);

                JSONObject dictJson = RIUtil.dicts.get(JGDM);
                //  logger.warn("dict_json->" + dictJson);
                int index_no = 9999;
                if (dictJson != null) {
                    JGMC = dictJson.getString("dict_name");
                    logger.warn(JGDM + "--->" + JGMC);
                    index_no = RIUtil.dicts.get(JGDM).getInteger("index_no");
                }
                one.put("index_no", index_no);
                one.put("JGMC", JGMC);

            }

            Collections.sort(resList, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("index_no");
                    b = o2.getInteger("index_no");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });


            back.put("data", resList);
            back.put("count", count);

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(446002);
        } finally {
            if (mysql != null) {
                mysql.close();
            }
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject GetUnmarkJq(JSONObject data) throws ParseException {
        JSONObject back = ErrNo.set(0);
        InfoModelHelper mysql = null;
        OracleHelper ora = null;
        String sql = "";
        String subSql = "";
        String nowTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String start_time = RIUtil.GetNextDate(nowTime, -7);
        start_time = start_time + nowTime.substring(10);
        String startTime = "";
        String endTime = "";

        List<JSONObject> resList = new ArrayList<>();

        String unit = "";
        int queryType = 1;
        int count = -1;
        int limit = 20;
        int page = 1;

        try {
            mysql = InfoModelPool.getModel();
            ora = new OracleHelper("ora_hl");


            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject done = RIUtil.dicts.get(unit);
                String type = done.getString("type");
                if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    subSql += " and CJDWDM like '" + unit.substring(0, 6) + "%' ";
                } else if (type.equals("25")) {
                    subSql += " and CJDWDM  like '%" + unit.substring(0, 8) + "%' ";
                } else if (type.equals("26")) {
                    subSql += " and CJDWDM  = '" + unit + "' ";
                }

            }

            if (data.containsKey("startTime") && data.getString("startTime").length() > 0) {
                startTime = data.getString("startTime");
                subSql += " and CJSJ >= '" + startTime + "' ";
            }
            if (data.containsKey("endTime") && data.getString("endTime").length() > 0) {
                endTime = data.getString("endTime");
                subSql += " and CJSJ <= '" + endTime + "' ";
            }

            if (data.containsKey("queryType") && data.getString("queryType").length() > 0) {
                queryType = data.getInteger("queryType");
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            String orderSql = " order by SHSJ desc ";
            String pageSql = "  limit " + limit + " offset " + limit * (page - 1);
            subSql += " and (PCS is not null and FJ is not null ) ";
//            subSql += " and (SFGLDZ_PDBZ is null or SFGLDZ_PDBZ = '0' or SFGLDZ_PDBZ = '1') ";
            subSql += " and FJ != '320400000000' ";

            if (queryType == 1) {
//                sql = "select JJBH, CJDWMC, CHJDW_GAJGJGDM as CJDWDM, CJSJ01 as CJSJ " +
//                        "from HL.DSJ_JQ " +
//                        "where (SFGLDZ_PDBZ is null or SFGLDZ_PDBZ = 0) and CJSJ01 >= '2023-12-01 00:00:00'  " +
//                        "and CJSJ01 <= '" + start_time + "' " + subSql + " order by CJSJ01 desc " +
//                        "OFFSET " + (page - 1) * limit + " ROWS FETCH NEXT " + limit + "ROWS ONLY ";

                sql = "select JJBH, CJDWMC, CJSJ, IS_DONE as ZGQK, CJDWDM from jqbz_kh " + "where TYPE = '1' " + subSql + orderSql + pageSql;
                logger.warn(sql);
                resList = mysql.query(sql);

                for (JSONObject one : resList) {
                    String DWDM = one.getString("CJDWDM");
                    String JJBH = one.getString("JJBH");
                    String CJDWMC = "";
                    JSONObject CJDWJs = RIUtil.dicts.get(DWDM);
                    if (CJDWJs != null && CJDWJs.containsKey("dict_name")) {
                        CJDWMC = CJDWJs.getString("dict_name");
                    }
                    one.put("CJDWMC", CJDWMC);


                    //查最后一次标注时间
                    sql = "select GLDZ_DJSJ from HL.DSJ_JQ where JJBH = '" + JJBH + "'";
                    String lastBZTime = ora.query_one(sql, "GLDZ_DJSJ");
                    one.put("lastBZTime", lastBZTime);
                }

                sql = "select count(JJBH) as count from jqbz_kh where type = '1' " + subSql;
                logger.warn(sql);
                count = mysql.query_count(sql);

            } else if (queryType == 2) {


                sql = "select JJBH, CJDWMC, ZGCS , SHSJ , IS_DONE , CJDWDM,UPDATE_TIME " + "from " + "jqbz_kh " +
                        "where TYPE = '2' " + subSql + orderSql + pageSql + "";
                resList = mysql.query(sql);
                logger.warn(sql);

                for (JSONObject one : resList) {
                    String DWDM = one.getString("CJDWDM");
                    String JJBH = one.getString("JJBH");
                    String CJDWMC = "";

                    JSONObject CJDWJs = RIUtil.dicts.get(DWDM);
                    if (CJDWJs != null && CJDWJs.containsKey("dict_name")) {
                        CJDWMC = CJDWJs.getString("remark");
                    }
                    one.put("CJDWMC", CJDWMC);


                    //查最后一次标注时间
                    sql = "select GLDZ_DJSJ from HL.DSJ_JQ where JJBH = '" + JJBH + "'";
                    String lastBZTime = ora.query_one(sql, "GLDZ_DJSJ");
                    one.put("lastBZTime", lastBZTime);
                    String shsj = one.getString("SHSJ");
                    int delay = 0;
                    int isDone = one.getIntValue("IS_DONE");
                    if (isDone == 1) {

                        delay = RIUtil.get2DateBetween(shsj, lastBZTime);


                    } else {
                        delay = RIUtil.get2DateBetween(shsj,
                                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

                    }
                    one.put("delay", Math.abs(delay));


                }

                sql = "select count(JJBH) as count from jqbz_kh where TYPE = '2' " + subSql;
                logger.warn(sql);
                count = mysql.query_count(sql);
            } else if (queryType == 3) {
                {


                    sql = "select JJBH, CJDWMC, ZGCS , SHSJ , IS_DONE , CJDWDM,UPDATE_TIME " + "from " + "jqbz_kh " + "where ( ZGCS>2)" + subSql + orderSql + pageSql + "";
                    resList = mysql.query(sql);
                    logger.warn(sql);

                    for (JSONObject one : resList) {
                        String DWDM = one.getString("CJDWDM");
                        String JJBH = one.getString("JJBH");
                        String CJDWMC = "";

                        JSONObject CJDWJs = RIUtil.dicts.get(DWDM);
                        if (CJDWJs != null && CJDWJs.containsKey("dict_name")) {
                            CJDWMC = CJDWJs.getString("remark");
                        }
                        one.put("CJDWMC", CJDWMC);


                        //查最后一次标注时间
                        sql = "select GLDZ_DJSJ from HL.DSJ_JQ where JJBH = '" + JJBH + "'";
                        String lastBZTime = ora.query_one(sql, "GLDZ_DJSJ");
                        one.put("lastBZTime", lastBZTime);
                        String shsj = one.getString("SHSJ");
                        int delay = 0;
                        int isDone = one.getIntValue("IS_DONE");
                        if (isDone == 1) {

                            delay = RIUtil.get2DateBetween(shsj, lastBZTime);


                        } else {
                            delay = RIUtil.get2DateBetween(shsj,
                                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

                        }
                        one.put("delay", Math.abs(delay));


                    }

                    sql = "select count(JJBH) as count from jqbz_kh where ZGCS>2 " + subSql;
                    logger.warn(sql);
                    count = mysql.query_count(sql);
                }
            }
            back.put("data", resList);
            back.put("count", count);

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(480026);
        } finally {
            if (mysql != null) {
                mysql.close();
            }
            InfoModelPool.putModel(mysql);
        }
        return back;
    }
}