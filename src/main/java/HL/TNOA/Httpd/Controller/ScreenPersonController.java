package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.Export.ExportInterface;
import HL.TNOA.Lib.Export.ExportXlsxHelper;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;

@RestController
public class ScreenPersonController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/screen_person"})
    // @PassToken
    public JSONObject get_screenPerson(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        String token = request.getHeader("token");
        try {

            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("get_rk_hj_nlfb")) {
                    // num2  560841 200-070100
                    return GetRKHJNLFB(data);
                } else if (opt.equals("get_zdry_sfsx_gk"))//重点人员身份属性概况
                {
                    return GetZdrySfsxGk(data);
                } else if (opt.equals("get_zdry_sfsx_gfbw"))//重点人员 身份属性/ 高发部位
                {
                    return GetZdrySfsxGfxq(data);
                } else if (opt.equals("get_zdry_zall_jq")) {
                    return GetZdryZallJq(data);
                } else if (opt.equals("get_zdry_zall_jq_table")) {
                    return GetZdryZallJqTable(data);
                } else if (opt.equals("get_zdry_lxgk"))//重点人员类型概况
                {
                    return GetZdryLxgk(data);
                } else if (opt.equals("")) {// 人房监测点
                    return GetPointPerson(data);

                } else if (opt.equals("")) {// 人房监测点
                    return GetPointPersonLevel(data);

                } else if (opt.equals("get_rk_gk")) {
                    return GetLdrkGk(data);
                } else if (opt.equals("get_ldrk_hjd")) {
                    return GetLdrkHJd(data);
                } else if (opt.equals("get_ldrk_bdqk")) {
                    return GetLdrkBdqk(data, token);
                } else if (opt.equals("get_hjrk_sta")) {
                    return GetHJRKPie(data, token);
                } else if (opt.equals("get_hjrk_table")) {
                    return GetHJRKTable(data, token);
                } else if (opt.equals("get_hjrk_xl")) {
                    return getHJRKNL(data, token);
                } else if (opt.equals("get_hjrk_qrqc")) {
                    return getHJRKQRQC(data, token);
                } else if (opt.equals("get_hjrk_tj")) {
                    return getHJRKRSTJ(data);
                } else if (opt.equals("get_zdry_gk")) {
                    return getZDRYGk(data, token);
                } else if (opt.equals("get_zdry_list")) {
                    return getZdryList(data, token);
                } else if (opt.equals("get_zdry_level")) {

                    return getZdryLevel(data, token);
                } else if (opt.equals("get_zdry_wcn_sa")) {
                    return GetZdryWcnSa(data);
                } else if (opt.equals("get_zdry_wcn_sa_table")) {
                    return GetZdryWcnSaTable(data);
                } else if (opt.equals("get_zdry_wcn_yd")) {
                    return GetZdryWcnYd(data);
                } else if (opt.equals("get_zdry_wcn_yd_table")) {
                    return null;
                } else if (opt.equals("get_zdry_ydgj")) {
                    return GetZdryYdgj(data);
                } else {
                    return ErrNo.set(465009);
                }
            } else {
                return ErrNo.set(465009);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private JSONObject GetZdryYdPoint(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = data.getString("unit");
        String lx = data.getString("lx");
        int type = RIUtil.dicts.get(unit).getInteger("type");
        String startTime = data.getString("start_time");
        String endTime = data.getString("end_time");
        String sfsx = "";
        try {
            sfsx = data.getString("sfsx");
        } catch (Exception ex) {

        }
        String usql = "";
        if (type == 21 || type == 22 || type == 27) {
            unit = unit.substring(0, 4);
            usql = " 1=1 ";

        } else if (type == 23 || type == 24) {
            unit = unit.substring(0, 6);
            usql = "  xgldw like '" + unit + "%'";
        } else if (type == 25) {
            unit = unit.substring(0, 8);
            usql = "  xgldw like '" + unit + "%'";
        } else {

            usql = "  xgldw = '" + unit + "'";
        }
        MysqlHelper my143 = null;
        try {
            if (sfsx.length() > 0) {
                my143 = new MysqlHelper("mysql_zxqc");
                String sql = "select id from dict  where (type=180 and id='" + sfsx + "') or (type=181 " + "and " +
                        "father_id='" + sfsx + "')";

                List<JSONObject> dicts = my143.query(sql);
                String xlsql = " ";
                for (int d = 0; d < dicts.size(); d++) {
                    JSONObject done = dicts.get(d);
                    xlsql = xlsql + " XLGLB like '" + done.getString("id") + "' or ";

                }

                if (xlsql.length() > 2) {
                    xlsql = xlsql.substring(0, xlsql.length() - 3);
                }
                usql = usql + " and (" + xlsql + ") ";
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            if (my143 != null) {
                my143.close();
            }
        }
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            String sql =
                    "select count(1) as count ,lx from DSJ.V_ZDRY_YJ_POINT where " + usql + " and lx like '" + lx +
                            "%' " +
                            "and" + " yjsj>='" + startTime + "' and yjsj<='" + endTime + "'" + " group by lx";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            HashMap<String, String> cs = new HashMap<>();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                cs.put(one.getString("LX"), one.getString("COUNT"));
            }

            JSONArray dict16051 = RIUtil.GetDictByFather(lx);
            JSONArray dets = new JSONArray();
            for (int i = 0; i < dict16051.size(); i++) {
                JSONObject one = dict16051.getJSONObject(i);
                JSONObject det = new JSONObject();
                String title = one.getString("dict_name");
                String count = "0";
                try {
                    count = cs.get(one.getString("id"));
                    if (count == null) {
                        count = "0";
                    }
                } catch (Exception e) {

                }
                det.put("title", title);
                det.put("count", count);
                det.put("id", one.getString("id"));

                JSONObject click = new JSONObject();
                det.put("click", click);
                JSONObject dpclick = new JSONObject();
                det.put("dpclick", dpclick);

                dets.add(det);

            }
            back.put("data", dets);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            ora_hl.close();

        }
    }

    private JSONObject GetZdryZallJqTable(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        int isExp = data.getIntValue("isExp");
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");

        } else {
            return ErrNo.set(null, 2, "缺少参数unit");
        }


        int page = data.getInteger("page");
        int limit = data.getInteger("limit");
        String start_time = data.getString("start_time");
        String end_time = data.getString("end_time");

        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            String sql =
                    "select XM,SFZH,BJNR,SSSQ,RYLX ,JJBH,YJSJXX from DSJ.OBJ_BASE_2201 where yjsjxx>='" + start_time + " " + "00:00:00' and  yjsjxx<='" + end_time + " 23:59:59' and SSSQDM like  '" + unit + "%' " + "order by yjsjxx desc" + " offset " + (page - 1) * limit + " rows fetch next " + limit + " rows " + "only";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            sql =
                    "select count(1) as count from DSJ.OBJ_BASE_2201 where SSSQDM like '" + unit + "%' and " +
                            "yjsjxx" +
                            ">='" + start_time + " 00:00:00' and yjsjxx<='" + end_time + " 23:59:59'";
            int count = ora_hl.query_count(sql);
            String hds = "姓名,身份证,时间,人员类型,所属单位,接警编号";
            String hkeys = "XM,SFZH,YJSJXX,RYLX,SSSQ,JJBH";
            JSONArray heads = GetHeads(hds, hkeys);
            JSONArray dets = new JSONArray();
            JSONArray dds = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                dds.add(one);
                JSONObject det = new JSONObject();
                String[] key = hkeys.split(",");
                for (int k = 0; k < key.length; k++) {
                    String c = key[k];
                    String v = "";

                    v = one.getString(c);


                    JSONObject val = new JSONObject();
                    val.put("value", v);
                    if (c.equals("JJBH")) {
                        JSONObject click = new JSONObject();
                        click.put("type", "caseDetail");
                        JSONObject opt = new JSONObject();
                        opt.put("jjbh", v);
                        click.put("opt", opt);
                        val.put("click", click);
                    }
                    if (c.equals("SFZH")) {
                        JSONObject click = new JSONObject();

                        click.put("type", "jump_zdry");
                        click.put("url", one.getString("SFZH"));
                        val.put("click", click);
                    }
                    det.put(c, val);
                }
                dets.add(det);

            }
            JSONObject datas = new JSONObject();
            datas.put("head", heads);
            datas.put("body", dets);
            datas.put("count", count);
            int fileId = -1;
            if (isExp == 1) {
                //本页
                fileId = ExportTables(dds, hds, hkeys, "ZALLSJ");

            } else if (isExp == 2) {
                //全部
                sql =
                        "select XM,SFZH,BJNR,SSSQ,RYLX ,JJBH,YJSJXX from DSJ.OBJ_BASE_2201 where yjsjxx>='" + start_time +
                                " 00:00:00' and yjsjxx<='" + end_time + " 23:59:59' and SSSQDM  like" + " " + "'" + unit + "%'" + " "
                                + "order by yjsjxx desc";
                logger.warn(sql);
                list = ora_hl.query(sql);
                dds = new JSONArray();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    dds.add(one);
                }
                fileId = ExportTables(dds, hds, hkeys, "ZALLSJ");

            } else {

            }
            datas.put("file_id", fileId);
            back.put("data", datas);
            return back;

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            ora_hl.close();
        }
    }

    private int ExportTables(JSONArray datas, String head, String keys, String name) {


        String FileName = name + "_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        String[] heads = head.split(",");
        String key[] = keys.split(",");
        for (int i = 0; i < key.length; i++) {
            header.add(key[i]);
            headername.put(key[i], heads[i]);
        }


        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) +
                            "/";

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
//            for (int i = 0; i < datas.size(); i++) {
//                JSONObject jsonObject = datas.getJSONObject(i);
//                JSONArray jsonArray = jsonObject.getJSONArray("real_all");
            exporthelper.write_data(datas);
//            }

            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql =
                        "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");
                try {
                    if (exporthelper != null) {
                        try {
                            exporthelper.close();
                        } catch (Exception ex) {

                        }

                    }
                } catch (Exception ex) {

                }

                logger.warn("-->" + id);
                String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;
                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
                logger.warn(obsFileName + "-->" + ret);
                logger.warn("id->" + id);
                return id;
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return 0;
        } finally {

            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }
        }
    }

    private JSONObject GetZdryYdgj(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper my143 = null;
        try {
            String unit = data.getString("unit");
            String usql = " ";
            int type = RIUtil.dicts.get(unit).getInteger("type");
            if (type == 21 || type == 22 || type == 27) {
                usql = " 1=1 ";

            } else if (type == 23 || type == 24 || type == 28) {
                usql = " xgldw_gajgjgdm like '" + unit.substring(0, 6) + "%' ";
            } else if (type == 25) {
                usql = " xgldw_gajgjgdm like '" + unit.substring(0, 8) + "%' ";
            } else {
                usql = " xgldw_gajgjgdm = '" + unit + "' ";
            }

            my143 = new MysqlHelper("mysql_zxqc");
            String sql = "select * from zdry_gjyc where " + usql + " order by catch_times desc limit 20";
            logger.warn(sql);
            List<JSONObject> list = my143.query(sql);
            JSONArray dets = new JSONArray();

            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String title = one.getString("XM") + " " + one.getString("GMSFHM");
                String content = "采集次数：" + one.getString("CATCH_TIMES") + "->" + one.getString("CATCH_ADD");
                String zp = one.getString("IMG");
                String time = one.getString("CATCH_TIME");
                String sfxx = one.getString("ZDRYLB_MC");
                String gljb = one.getString("GLJB_MC");
                JSONObject det = new JSONObject();
                det.put("title", title);
                det.put("content", content);
                det.put("img", zp);
                // det.put("unit", badw);
                det.put("time", time);

                JSONArray labels = new JSONArray();
                JSONObject label = new JSONObject();

                label.put("id", 0);
                label.put("name", one.getString("ZKZT"));
                label.put("color", "#4cbb6c");
                labels.add(label);


                label = new JSONObject();
                label.put("id", 0);
                label.put("name", gljb);
                label.put("color", "#ff1515");
                labels.add(label);

                label = new JSONObject();
                label.put("id", 0);
                label.put("name", sfxx);
                label.put("color", "#ff8e15");
                labels.add(label);
                det.put("label", labels);
                JSONObject click = new JSONObject();
                click.put("type", "jump_zdry");
                click.put("url", one.getString("GMSFHM"));
                det.put("click", click);

                JSONObject dpclick = new JSONObject();
                dpclick.put("type", "OPEN_DING_DIA");
                det.put("ding", dpclick);
                dets.add(det);


            }
            back.put("data", dets);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            my143.close();
        }
    }

    private JSONObject GetZdryWcnSa(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;

        try {
            String startTime = data.getString("start_time") + " 00:00:00";
            String endTime = data.get("end_time") + " 23:59:59";
            String unit = data.getString("unit");
            int t = RIUtil.dicts.get(unit).getIntValue("type");
            if (t == 21 || t == 22 || t == 27) {
                unit = "3204";
            } else if (t == 23 || t == 24 || t == 28) {
                unit = unit.substring(0, 6);
            } else if (t == 25) {
                unit = unit.substring(0, 8);

            } else {
                unit = unit;
            }

            ora_hl = new OracleHelper("ora_hl");
            String sql = "select a.*,b.zplj from dsj.obj_base_2200 a left join czqj_ybds.yw_syrk@qjjc_ybls b on a" +
                    ".sfzh=b.gmsfhm where wygxdwdmlist like '%" + unit + "%' and yjsjxx>='" + startTime + "' and " +
                    "yjsjxx" + "<='" + endTime + "' order by yjsjxx desc offset 0 rows fetch next 20 rows only";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            JSONArray dets = new JSONArray();
            for (int a = 0; a < list.size(); a++) {

                JSONObject one = list.get(a);
                String title = one.getString("XM") + " " + one.getString("SFZH");
                String content = one.getString("YJNR").replace("无数据", "");
                String zp = one.getString("ZPLJ");
                String badw = one.getString("BADWMC");
                String rssj = one.getString("RSSJ");

                String jzbqlx = one.getString("JZBQXL");
                String rsyy = one.getString("RSYY");
                JSONObject det = new JSONObject();
                det.put("title", title);
                det.put("content", content);
                det.put("img", zp);
                det.put("unit", badw);
                det.put("time", rssj);

                JSONArray labels = new JSONArray();
                JSONObject label = new JSONObject();
                if (jzbqlx != null || jzbqlx.length() > 0) {
                    label.put("id", 0);
                    label.put("name", jzbqlx);
                    label.put("color", "#ff1515");
                    labels.add(label);
                }
                label = new JSONObject();
                label.put("id", 0);
                label.put("name", rsyy);
                label.put("color", "#ff8e15");
                labels.add(label);
                det.put("label", labels);
                JSONObject click = new JSONObject();
                click.put("type", "jump_zdry");
                click.put("url", one.getString("SFZH"));
                det.put("click", click);

                JSONObject dpclick = new JSONObject();
                dpclick.put("type", "dialog");
                dpclick.put("label", "明细");
                dpclick.put("permission", "table_page");
                JSONObject col = new JSONObject();
                col.put("unit", unit);
                col.put("isExp", 0);
                col.put("start_time", startTime);
                col.put("end_time", endTime);
                JSONObject opt = GetOpts("/screen_person", "get_zdry_wcn_sa_table", col);
                dpclick.put("remark", opt);
                det.put("dpclick", dpclick);
                dets.add(det);


            }
            back.put("data", dets);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private JSONObject GetZdryWcnYd(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;

        try {
            String startTime = data.getString("start_time") + " 00:00:00";
            String endTime = data.get("end_time") + " 23:59:59";
            String unit = data.getString("unit");
            int t = RIUtil.dicts.get(unit).getIntValue("type");
            if (t == 21 || t == 22 || t == 27) {
                unit = "3204";
            } else if (t == 23 || t == 24 || t == 28) {
                unit = unit.substring(0, 6);
            } else if (t == 25) {
                unit = unit.substring(0, 8);

            } else {
                unit = unit;
            }

            ora_hl = new OracleHelper("ora_hl");
            String sql = "select a.*,b.zplj from dsj.obj_base_2339 a left join czqj_ybds.yw_syrk@qjjc_ybls b on a" +
                    ".sbbh=b.gmsfhm where wygxdwdmlist like '%" + unit + "%' and zpsj>='" + startTime + "' and " +
                    "zpsj" +
                    "<='" + endTime + "' order by zpsj desc offset 0 rows fetch next 20 rows only";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            JSONArray dets = new JSONArray();
            for (int a = 0; a < list.size(); a++) {

                JSONObject one = list.get(a);
                String title = one.getString("XM") + " " + one.getString("SBBH");
                String content = one.getString("SBMC") + " " + one.getString("YJNR");
                String zp = one.getString("ZPLJ");
                String badw = one.getString("BADWMC");
                String rssj = one.getString("ZPSJ");

                String sfxx = one.getString("SFSX");
                String gljb = one.getString("GLJB");
                JSONObject det = new JSONObject();
                det.put("title", title);
                det.put("content", content);
                det.put("img", zp);
                // det.put("unit", badw);
                det.put("time", rssj);

                JSONArray labels = new JSONArray();
                JSONObject label = new JSONObject();

                label.put("id", 0);
                label.put("name", gljb);
                label.put("color", "#ff1515");
                labels.add(label);

                label = new JSONObject();
                label.put("id", 0);
                label.put("name", sfxx);
                label.put("color", "#ff8e15");
                labels.add(label);
                det.put("label", labels);
                JSONObject click = new JSONObject();
                click.put("type", "jump_zdry");
                click.put("url", one.getString("SBBH"));
                det.put("click", click);
                dets.add(det);


            }
            back.put("data", dets);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private JSONObject GetZdryWcnSaTable(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        int page = data.getInteger("page");
        int limit = data.getInteger("limit");
        int isExp = data.getIntValue("isExp");

        OracleHelper ora_hl = null;
        try {
            String startTime = data.getString("start_time") + " 00:00:00";
            String endTime = data.get("end_time") + " 23:59:59";
            String unit = data.getString("unit");


            String hds = "姓名,身份证号,预警内容,办案单位,入所时间,标签类型,入所原因";
            String keys = "XM,SFZH,YJNR,BADWMC,RSSJ,JZBQXL,RSYY";
            JSONArray heads = GetHeads(hds, keys);
            ora_hl = new OracleHelper("ora_hl");
            String sql = "select a.*,b.zplj from dsj.obj_base_2200 a left join czqj_ybds.yw_syrk@qjjc_ybls b on a" +
                    ".sfzh=b.gmsfhm where wygxdwdmlist like '%" + unit + "%' and yjsjxx>='" + startTime + "' and " +
                    "yjsjxx" + "<='" + endTime + "' order by yjsjxx desc offset " + (page - 1) * limit + " rows " +
                    "fetch" + " next " + limit + " rows only";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            sql = "select count(1) as count from dsj.obj_base_2200 where wygxdwdmlist like '%" + unit + "%' and " +
                    "yjsjxx>='" + startTime + "' and " + "yjsjxx" + "<='" + endTime + "' ";
            int count = ora_hl.query_count(sql);
            JSONArray dets = new JSONArray();
            JSONArray dds = new JSONArray();
            for (int a = 0; a < list.size(); a++) {

                JSONObject one = list.get(a);


                String content = one.getString("YJNR").replace("无数据", "");
                one.put("YJNR", content);
                dds.add(one);
                JSONObject det = new JSONObject();

                String[] key = keys.split(",");
                for (int k = 0; k < key.length; k++) {
                    String c = key[k];
                    String v = "";

                    v = one.getString(c);


                    JSONObject val = new JSONObject();
                    val.put("value", v);

                    if (c.equals("SFZH")) {
                        JSONObject click = new JSONObject();

                        click.put("type", "jump_zdry");
                        click.put("url", one.getString("SFZH"));
                        val.put("click", click);
                    }
                    det.put(c, val);
                }
                dets.add(det);


            }
            JSONObject datas = new JSONObject();
            datas.put("head", heads);
            datas.put("body", dets);
            datas.put("count", count);
            int fileId = -1;
            if (isExp == 1) {
                //本页
                fileId = ExportTables(dds, hds, keys, "WCNRSA");

            } else if (isExp == 2) {
                //全部
                sql = "select a.*,b.zplj from dsj.obj_base_2200 a left join czqj_ybds.yw_syrk@qjjc_ybls b on a" +
                        ".sfzh=b.gmsfhm where wygxdwdmlist like '%" + unit + "%' and yjsjxx>='" + startTime + "' and " +
                        "yjsjxx" + "<='" + endTime + "' order by yjsjxx desc";
                logger.warn(sql);
                list = ora_hl.query(sql);
                dds = new JSONArray();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    dds.add(one);
                }
                fileId = ExportTables(dds, hds, keys, "WCNRSA");

            } else {

            }
            datas.put("file_id", fileId);
            back.put("data", datas);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private JSONObject GetZdryWcnYdTable(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;

        try {
            String startTime = data.getString("start_time") + " 00:00:00";
            String endTime = data.get("end_time") + " 23:59:59";
            String unit = data.getString("unit");
            int t = RIUtil.dicts.get(unit).getIntValue("type");
            if (t == 21 || t == 22 || t == 27) {
                unit = "3204";
            } else if (t == 23 || t == 24 || t == 28) {
                unit = unit.substring(0, 6);
            } else if (t == 25) {
                unit = unit.substring(0, 8);

            } else {
                unit = unit;
            }

            ora_hl = new OracleHelper("ora_hl");
            String sql = "select a.*,b.zplj from dsj.obj_base_2339 a left join czqj_ybds.yw_syrk@qjjc_ybls b on a" +
                    ".sbbh=b.gmsfhm where wygxdwdmlist like '%" + unit + "%' and zpsj>='" + startTime + "' and " +
                    "zpsj" +
                    "<='" + endTime + "' order by zpsj desc offset 0 rows fetch next 20 rows only";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            JSONArray dets = new JSONArray();
            for (int a = 0; a < list.size(); a++) {

                JSONObject one = list.get(a);
                String title = one.getString("XM") + " " + one.getString("SBBH");
                String content = one.getString("SBMC") + " " + one.getString("YJNR");
                String zp = one.getString("ZPLJ");
                String badw = one.getString("BADWMC");
                String rssj = one.getString("ZPSJ");

                String sfxx = one.getString("SFSX");
                String gljb = one.getString("GLJB");
                JSONObject det = new JSONObject();
                det.put("title", title);
                det.put("content", content);
                det.put("img", zp);
                // det.put("unit", badw);
                det.put("time", rssj);

                JSONArray labels = new JSONArray();
                JSONObject label = new JSONObject();

                label.put("id", 0);
                label.put("name", gljb);
                label.put("color", "#ff1515");
                labels.add(label);

                label = new JSONObject();
                label.put("id", 0);
                label.put("name", sfxx);
                label.put("color", "#ff8e15");
                labels.add(label);
                det.put("label", labels);
                JSONObject click = new JSONObject();
                click.put("type", "jump_zdry");
                click.put("url", one.getString("SBBH"));
                det.put("click", click);
                dets.add(det);


            }
            back.put("data", dets);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private JSONObject getZdryLevel(JSONObject data, String token) {
        JSONObject back = ErrNo.set(0);

        try {

            String url = "http://jcgk.qjjcgzpt.czx.js/rwpt/uc/login/loginByToken";
            JSONObject d = new JSONObject();
            d.put("token", token);
            JSONObject rets = GetOkHttpQQBpost(url, token, d);
            if (rets.containsKey("code") && rets.getInteger("code") == 200) {
                JSONObject dd = rets.getJSONObject("data");
                token = dd.getString("token");
            }
            url = "http://jcgk.qjjcgzpt.czx.js/object/search/obj/objCount";
            String det = "{\"objId\": \"2027\",\"history\": 0,\"labelIds\": [],\"unionLabelIds\": []," + "\"otherList"
                    + "\":" + " {\"jhqd\": [],\"wffzqkryazbjry\": [],\"saxz\": [],\"ryzt\": []," + "\"sfsx\":" + " " + "[]," + "\"swzdry\": []," + "\"sdry\": [],\"wffzqkry\": [],\"lglay\": [],\"gljb\": " + "[],\"glzt" + "\": " + "[\"列管\"],\"cgyy1\": []," + "\"sfnrjdbf\": [],\"sfjdtsjypxxx\": [],\"ywsfsj\": " + "[]," + "\"sfbbdzzql\": " + "[],\"sfwfxypjg\": []," + "\"sfscdzda_1\": [],\"hjsx\": []}," + "\"dateMap\":" + " {}," + "\"relatedObjList\": [],\"taskIdList\": []," + "\"keyword\": \"\"," + "\"platformId\": " + "1," + "\"wygxdwdmList\": [\"320400000000\"]\n" + "}";
            JSONObject dets = JSONObject.parseObject(det);
            String unit = data.getString("unit");
            int type = RIUtil.dicts.get(unit).getInteger("type");
            int t = 21;
            if (type == 21 || type == 22 || type == 27) {
                unit = unit.substring(0, 4) + "00000000";
                t = 23;
            } else if (type == 23 || type == 24 || type == 28) {
                unit = unit.substring(0, 6) + "000000";
                t = 25;
            } else if (type == 25) {
                unit = unit.substring(0, 8) + "0000";
                t = 26;
            } else {
                unit = unit;
                t = 26;
            }
            JSONArray nexts = new JSONArray();
            if (type != 26) {
                nexts = RIUtil.GetDictByTypeFather(t, unit);
            } else {
                nexts.add(RIUtil.dicts.get(unit));
            }

            HashMap<String, String> nxs = new HashMap<>();
            for (int n = 0; n < nexts.size(); n++) {

                String id = nexts.getJSONObject(n).getString("id");
                if (!id.equals("320496000000") && !id.equals("320498000000")) {
                    nxs.put(id, "");
                }
            }
            logger.warn(nxs.toString());
            List<String> dslist = new ArrayList<>();
            dslist.add(unit);
            dets.put("wygxdwdmList", dslist);
            JSONObject otherList = dets.getJSONObject("otherList");
            List<String> lxm = new ArrayList<>();
            lxm.add(data.getString("lx"));

            otherList.put(data.getString("lxm"), lxm);

            dets.put("otherList", otherList);

            logger.warn(dets.toString());
            rets = GetOkHttpQQBpost(url, token, dets);
            if (rets.containsKey("code") && rets.getInteger("code") == 200) {

                JSONObject datas = rets.getJSONObject("data");
                JSONObject gxdwdm = new JSONObject();
                if (type == 25 || type == 26) {
                    gxdwdm = datas.getJSONObject("wyxzqhdm");
                } else {
                    gxdwdm = datas.getJSONObject("wygxdwdm");

                }
                JSONArray bodys = new JSONArray();
                int i = -1;
                for (Entry<String, Object> dw : gxdwdm.entrySet()) {
                    i++;
                    JSONObject ddd = new JSONObject();

                    String code = dw.getKey();
                    if (nxs.containsKey(code)) {
                        String count = dw.getValue().toString();
                        String name = RIUtil.dicts.get(code).getString("dict_name");

                        ddd.put("name", name);
                        ddd.put("count", count);
                        ddd.put("code", code);
                        JSONObject click = new JSONObject();
                        click.put("type", "dialog");
                        int ran = i % 5;
                        if (ran == 0) {
                            click.put("permission", "chat_bar");
                        } else if (ran == 1) {
                            click.put("permission", "chat_pie");
                        } else if (ran == 2) {
                            click.put("permission", "chat_pie_border");
                        } else if (ran == 3) {
                            click.put("permission", "chat_rose");
                        } else {
                            click.put("permission", "chat_bar_radius");
                        }
                        JSONObject cols = new JSONObject();

                        cols.put("lx", data.getString("lx"));
                        cols.put("lxm", data.getString("lxm"));
                        cols.put("unit", code);
                        cols.put("name", name);
                        JSONObject opts = GetOpts("/screen_person", "get_zdry_level", cols);
                        click.put("remark", opts);
                        click.put("label", data.getString("lx") + "_" + name);
                        click.put("id", String.valueOf(UUID.randomUUID()));
                        ddd.put("click", click);

                        JSONObject dpclick = new JSONObject();
                        dpclick.put("type", "dialog1");
                        cols = new JSONObject();
                        cols.put("lx", data.getString("lx"));
                        cols.put("lxm", data.getString("lxm"));
                        cols.put("unit", code);
                        cols.put("name", name);
                        opts = GetOpts("/screen_person", "get_zdry_list", cols);
                        dpclick.put("remark", opts);
                        dpclick.put("label", data.getString("lx") + "_" + name);
                        dpclick.put("permission", "table_page");
                        dpclick.put("id", String.valueOf(UUID.randomUUID()));

                        ddd.put("dpclick", dpclick);

                        bodys.add(ddd);
                    }

                }
                back.put("data", bodys);
                return back;

            } else {
                return ErrNo.set(null, 2, rets.toString());
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private JSONObject getZdryList(JSONObject data, String token) {
        JSONObject back = ErrNo.set(0);

        try {

            String url = "http://jcgk.qjjcgzpt.czx.js/rwpt/uc/login/loginByToken";
            JSONObject d = new JSONObject();
            d.put("token", token);
            JSONObject rets = GetOkHttpQQBpost(url, token, d);
            if (rets.containsKey("code") && rets.getInteger("code") == 200) {
                JSONObject dd = rets.getJSONObject("data");
                token = dd.getString("token");
            }
            url = "http://jcgk.qjjcgzpt.czx.js/object/search/obj/objPage";
            String det = "{\"objId\": \"2027\",\"history\": 0,\"labelIds\": [],\"unionLabelIds\": []," + "\"otherList"
                    + "\":" + " {\"jhqd\": [],\"wffzqkryazbjry\": [],\"saxz\": [],\"ryzt\": []," + "\"sfsx\":" + " " + "[]," + "\"swzdry\": []," + "\"sdry\": [],\"wffzqkry\": [],\"lglay\": [],\"gljb\": " + "[],\"glzt" + "\": " + "[\"列管\"],\"cgyy1\": []," + "\"sfnrjdbf\": [],\"sfjdtsjypxxx\": [],\"ywsfsj\": " + "[]," + "\"sfbbdzzql\": " + "[],\"sfwfxypjg\": []," + "\"sfscdzda_1\": [],\"hjsx\": []}," + "\"dateMap\":" + " {}," + "\"relatedObjList\": [],\"taskIdList\": []," + "\"keyword\": \"\"," + "\"platformId\": " + "1," + "\"wygxdwdmList\": [\"320400000000\"]\n" + "}";
            JSONObject dets = JSONObject.parseObject(det);
            String unit = data.getString("unit");
            int type = RIUtil.dicts.get(unit).getInteger("type");
            if (type == 21 || type == 22 || type == 27) {

                unit = unit.substring(0, 4) + "00000000";

            } else if (type == 23 || type == 24 || type == 28) {
                unit = unit.substring(0, 6) + "000000";
            } else if (type == 25) {

                unit = unit.substring(0, 8) + "0000";
            } else {

                unit = unit + "_xzqh";

            }
            List<String> dslist = new ArrayList<>();
            dslist.add(unit);
            dets.put("wygxdwdmList", dslist);
            JSONObject otherList = dets.getJSONObject("otherList");
            List<String> lxm = new ArrayList<>();
            lxm.add(data.getString("lx"));

            otherList.put(data.getString("lxm"), lxm);

            dets.put("otherList", otherList);
            dets.put("pageNo", data.getString("page"));
            dets.put("pageSize", data.getString("limit"));

            logger.warn(dets.toString());
            rets = GetOkHttpQQBpost(url, token, dets);
            if (rets.containsKey("code") && rets.getInteger("code") == 200) {


                String hnd = "管理级别,管理状态,身份证号,姓名,身份属性,所属社区名称";
                String keys = "gljb,glzt,bizValue,bizName,sfsxxl,lgdw";
                JSONArray heads = GetHeads(hnd, keys);
                int count = 0;
                JSONArray bodys = new JSONArray();

                JSONObject datas = rets.getJSONObject("data");
                count = datas.getInteger("total");
                if (count > 0) {
                    JSONArray records = datas.getJSONArray("records");
                    logger.warn(records.get(0).toString());
                    for (int i = 0; i < records.size(); i++) {
                        JSONObject one = records.getJSONObject(i);
                        JSONObject b = new JSONObject();

                        String key[] = keys.split(",");
                        for (int k = 0; k < key.length; k++) {
                            String kk = key[k];
                            JSONObject val = new JSONObject();


                            if (kk.equals("bizValue")) {

                                JSONObject click = new JSONObject();
                                click.put("type", "jump_zdry");
                                click.put("url", one.getString("bizValue"));
                                val.put("click", click);
                            }
                            val.put("value", one.getString(kk));


                            b.put(kk, val);


                        }
                        bodys.add(b);
                    }
                }
                JSONObject dds = new JSONObject();
                dds.put("head", heads);
                dds.put("count", count);
                dds.put("body", bodys);

                back.put("data", dds);
                return back;

            } else {
                return ErrNo.set(null, 2, rets.toString());
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private JSONObject getZDRYGk(JSONObject data, String token) {
        {

            JSONObject back = ErrNo.set(0);

            try {

                String url = "http://jcgk.qjjcgzpt.czx.js/rwpt/uc/login/loginByToken";
                JSONObject d = new JSONObject();
                d.put("token", token);
                JSONObject rets = GetOkHttpQQBpost(url, token, d);
                if (rets.containsKey("code") && rets.getInteger("code") == 200) {
                    JSONObject dd = rets.getJSONObject("data");
                    try {
                        token = dd.getString("token");
                    } catch (Exception ex) {
                        logger.error(rets.toString());
                    }
                }
                url = "http://jcgk.qjjcgzpt.czx.js/object/search/obj/objCount";
                String det = "{\"objId\": \"2027\",\"history\": 0,\"labelIds\": [],\"unionLabelIds\": []," +
                        "\"otherList\":" + " {\"jhqd\": [],\"wffzqkryazbjry\": [],\"saxz\": [],\"ryzt\": []," +
                        "\"sfsx\":" + " [],\"swzdry\": []," + "\"sdry\": [],\"wffzqkry\": [],\"lglay\": [],\"gljb\": "
                        + "[],\"glzt\": " + "[\"列管\"],\"cgyy1\": []," + "\"sfnrjdbf\": [],\"sfjdtsjypxxx\": []," +
                        "\"ywsfsj\":" + " " + "[],\"sfbbdzzql\": " + "[],\"sfwfxypjg\": []," + "\"sfscdzda_1\": []," + "\"hjsx\": []}," + "\"dateMap\": {}," + "\"relatedObjList\": [],\"taskIdList\": []," + "\"keyword\": \"\"," + "\"platformId\": 1," + "\"wygxdwdmList\": [\"320400000000\"]\n" + "}";
                JSONObject dets = JSONObject.parseObject(det);
                String unit = data.getString("unit");
                int type = RIUtil.dicts.get(unit).getInteger("type");
                if (type == 21 || type == 22 || type == 27) {

                    unit = unit.substring(0, 4) + "00000000";

                } else if (type == 23 || type == 24 || type == 28) {
                    unit = unit.substring(0, 6) + "000000";
                } else if (type == 25) {

                    unit = unit.substring(0, 8) + "0000";
                } else {

                    unit = unit;

                }
                List<String> dslist = new ArrayList<>();
                dslist.add(unit);
                dets.put("wygxdwdmList", dslist);
                logger.warn(dets.toString());
                rets = GetOkHttpQQBpost(url, token, dets);
                if (rets.containsKey("code") && rets.getInteger("code") == 200) {

                    JSONObject datas = rets.getJSONObject("data");
                    JSONObject gjlb = datas.getJSONObject("gljb");

                    JSONObject sfsx = datas.getJSONObject("sfsx");

                    JSONArray bodys = new JSONArray();
                    int i = 0;

                    for (Entry<String, Object> b : gjlb.entrySet()) {
                        i++;
                        JSONObject one = new JSONObject();
                        one.put("title", b.getKey());
                        one.put("count", b.getValue());

                        JSONObject dpclick = new JSONObject();
                        dpclick.put("type", "dialog");
                        JSONObject cols = new JSONObject();
                        cols.put("lxm", "gljb");
                        cols.put("lx", b.getKey());
                        cols.put("unit", unit);
                        JSONObject opts = GetOpts("/screen_person", "get_zdry_list", cols);
                        dpclick.put("remark", opts);
                        dpclick.put("label", b.getKey());
                        dpclick.put("permission", "table_page");
                        dpclick.put("id", String.valueOf(UUID.randomUUID()));

                        one.put("dpclick", dpclick);


                        JSONObject click = new JSONObject();
                        click.put("type", "dialog");
                        int ran = i % 5;
                        if (ran == 0) {
                            click.put("permission", "chat_bar");
                        } else if (ran == 1) {
                            click.put("permission", "chat_pie");
                        } else if (ran == 2) {
                            click.put("permission", "chat_pie_border");
                        } else if (ran == 3) {
                            click.put("permission", "chat_rose");
                        } else {
                            click.put("permission", "chat_bar_radius");
                        }
                        cols = new JSONObject();

                        cols.put("lxm", "gljb");
                        cols.put("lx", b.getKey());
                        cols.put("unit", unit);

                        opts = GetOpts("/screen_person", "get_zdry_level", cols);
                        click.put("remark", opts);
                        click.put("label", b.getKey());
                        one.put("click", click);

                        bodys.add(one);
                    }

                    for (Entry<String, Object> b : sfsx.entrySet()) {
                        i++;
                        JSONObject one = new JSONObject();
                        one.put("title", b.getKey());
                        one.put("count", b.getValue());

                        JSONObject dpclick = new JSONObject();
                        dpclick.put("type", "dialog");
                        JSONObject cols = new JSONObject();
                        cols.put("lxm", "sfsx");
                        cols.put("lx", b.getKey());
                        cols.put("unit", unit);
                        JSONObject opts = GetOpts("/screen_person", "get_zdry_list", cols);
                        dpclick.put("remark", opts);
                        dpclick.put("label", b.getKey());
                        dpclick.put("permission", "table_page");
                        dpclick.put("id", String.valueOf(UUID.randomUUID()));

                        one.put("dpclick", dpclick);


                        JSONObject click = new JSONObject();
                        click.put("type", "dialog");
                        int ran = i % 5;
                        if (ran == 0) {
                            click.put("permission", "chat_bar");
                        } else if (ran == 1) {
                            click.put("permission", "chat_pie");
                        } else if (ran == 2) {
                            click.put("permission", "chat_pie_border");
                        } else if (ran == 3) {
                            click.put("permission", "chat_rose");
                        } else {
                            click.put("permission", "chat_bar_radius");
                        }
                        cols = new JSONObject();

                        cols.put("lxm", "sfsx");
                        cols.put("lx", b.getKey());
                        cols.put("unit", unit);
                        opts = GetOpts("/screen_person", "get_zdry_level", cols);
                        click.put("remark", opts);
                        click.put("label", b.getKey());
                        one.put("click", click);

                        bodys.add(one);
                    }
                    back.put("data", bodys);
                    return back;


                } else {
                    return ErrNo.set(null, 2, rets.toString());
                }

            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
                return ErrNo.set(null, 2, Lib.getTrace(ex));
            }


        }
    }

    private JSONObject GetLdrkBdqk(JSONObject data, String token) {
        JSONObject back = ErrNo.set(0);
        try {
            String end_time = data.getString("end_time");
            long t = System.currentTimeMillis() / 1000;
            String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String time = "";
            if (today.equals(end_time)) {
                time = RIUtil.GetNextDate(today, -1);
            } else {
                time = end_time;
            }
            String lx = data.getString("lx");
            String unit = data.getString("unit");

            int type = RIUtil.dicts.get(unit).getInteger("type");
            int orgType = 0;
            if (type == 21 || type == 22 || type == 27) {
                orgType = 2;
                unit = unit.substring(0, 4) + "00000000";

            } else if (type == 23 || type == 24 || type == 28) {
                unit = unit.substring(0, 6) + "000000";
                orgType = 3;
            } else if (type == 25) {
                orgType = 4;
                unit = unit;
            } else {
                unit = unit;
                orgType = 4;
            }

            String url = data.getString("dz").replace("$_t$", String.valueOf(t)).replace("$end_time$", time).replace(
                    "$orgType$", String.valueOf(orgType)).replace("$parentOrgCode$", unit);
            logger.warn(url);
            OkHttpClient client = new OkHttpClient().newBuilder().build();
            Request request =
                    new Request.Builder().url(url).method("GET", null).addHeader("X-Access-Token", token).build();
            Response response = client.newCall(request).execute();
            String res = response.body().string();
            JSONObject resj = JSONObject.parseObject(res);
            JSONArray dets = new JSONArray();
            String detss = "同比,数量";
            String[] ds = detss.split(",");
            String detid = "1,2";
            String bxxs = "line,bar";
            String xs[] = bxxs.split(",");
            String[] dids = detid.split(",");
            if (resj.getInteger("code") == 200 && resj.getBoolean("success")) {

                JSONArray results = resj.getJSONArray("result");

                for (int i = 0; i < ds.length; i++) {
                    JSONObject det = new JSONObject();
                    det.put("id", dids[i]);
                    det.put("name", ds[i]);
                    det.put("xx", xs[i]);
                    JSONArray d = new JSONArray();

                    for (int a = 0; a < results.size(); a++) {
                        JSONObject aone = results.getJSONObject(a);
                        JSONObject dd = new JSONObject();
                        String code = aone.getString("jgbm");
                        String name = aone.getString("jgmc");
                        String count = "0";
                        if (lx.equals("xz") && ds[i].equals("同比")) {
                            count = aone.getString("ldrkDjsNotAbsBfb");
                        } else if (lx.equals("xz") && ds[i].equals("数量")) {
                            count = aone.getString("ldrkDjs");
                        } else if (lx.equals("zx") && ds[i].equals("数量")) {
                            count = aone.getString("ldrkZxs");
                        } else if (lx.equals("zx") && ds[i].equals("同比")) {
                            count = aone.getString("ldrkZxsNotAbsBfb");
                        }
                        dd.put("code", code);
                        dd.put("id", code);
                        dd.put("name", name);
                        dd.put("count", count);
                        d.add(dd);


                    }


                    det.put("det", d);
                    dets.add(det);
                }


            } else {
                return ErrNo.set(null, 2, resj.getString("messgae"));
            }

            back.put("data", dets);

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        }
    }

    private JSONObject GetLdrkHJd(JSONObject data) {


        JSONObject back = ErrNo.set(0);

        String unit = data.getString("unit");
        String lx = data.getString("lx");
        int type = RIUtil.dicts.get(unit).getInteger("type");
        String usql = "";

        if (type == 21 || type == 22 || type == 27) {


        } else if (type == 23 || type == 24) {
            usql = " and b.fjdm='" + unit.substring(0, 6) + "000000' ";


        } else if (type == 25) {
            unit = unit.substring(0, 8) + "0000";
            usql = " and b.pcsdm='" + unit + "' ";

        } else {

            unit = unit;
            usql = " and b.hjzrq='" + unit + "' ";

        }

        OracleHelper ora_gl = null;
        try {

            ora_gl = new OracleHelper("ora_bk_gl");


            String sql = "select " + lx + ",count(1) as count from czqj_ybds.yw_syrk a left join czqj_ybds" +
                    ".address_info b on a.sjjzd_dzbm=b.dzid where rkgllbdm=12 and syrkhsjg=1 " + usql + " group by " + lx +
                    " order by count " + "desc";
            logger.warn(sql);
            JSONArray dets = new JSONArray();
            List<JSONObject> list = ora_gl.query(sql);
            int size = list.size();
            if (size > 10) {
                size = 10;
            }
            for (int i = 1; i <= size; i++) {
                JSONObject one = list.get(i);
                String t = "";
                String name = "";
                if (lx.equals("HJDZ_SSXQDM")) {
                    t = "126-" + one.getString(lx);
                    name = RIUtil.dicts.get(t).getString("dict_name");
                    name = name.substring(0, name.length() - 1).replaceAll("省", "");
                } else if (lx.equals("MZDM")) {
                    t = "127-" + one.getString(lx);
                    name = RIUtil.dicts.get(t).getString("dict_name") + "族";
                }
                logger.warn(t);


                String count = one.getString("COUNT");
                JSONObject det = new JSONObject();
                det.put("name", name);
                det.put("count", count);
                dets.add(det);
            }


            back.put("data", dets);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_gl.close();
        }


    }

    private JSONObject GetLdrkGk(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        String unit = data.getString("unit");
        String lx = data.getString("lx");
        int type = RIUtil.dicts.get(unit).getInteger("type");
        String units = "";

        if (type == 21 || type == 22 || type == 27) {
            String fid = unit.substring(0, 4) + "00000000";


            JSONArray us = RIUtil.GetDictByTypeFather(23, fid);
            for (int a = 0; a < us.size(); a++) {
                JSONObject aone = us.getJSONObject(a);
                String id = aone.getString("id");
                units = units + id + ",";

            }

        } else if (type == 23 || type == 24) {
            String fid = unit.substring(0, 6) + "000000";


            JSONArray us = RIUtil.GetDictByTypeFather(25, fid);
            for (int a = 0; a < us.size(); a++) {
                JSONObject aone = us.getJSONObject(a);
                String id = aone.getString("id");
                units = units + id + ",";

            }

        } else if (type == 25) {
            String fid = unit.substring(0, 8) + "0000";


            JSONArray us = RIUtil.GetDictByTypeFather(26, fid);
            for (int a = 0; a < us.size(); a++) {
                JSONObject aone = us.getJSONObject(a);
                String id = aone.getString("id");
                units = units + id + ",";

            }
        } else {

            units = units + unit + ",";

        }
        if (units.endsWith(",")) {
            units = units.substring(0, units.length() - 1);

        }


        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");

            //流动人员数量
            String sql = "select " + lx + ",CODE from sta_gk where code in ('" + units.replace(",", "','") + "') ";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            JSONArray dets = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String code = one.getString("CODE");
                String name = RIUtil.dicts.get(code).getString("dict_name");
                String count = one.getString(lx);
                if (count == null || count.length() == 0) {
                    count = "0";
                }
                if (!"0".equals(count)) {
                    JSONObject det = new JSONObject();
                    det.put("count", count);
                    det.put("name", name);
                    det.put("id", code);
                    det.put("index_no", RIUtil.dicts.get(code).getInteger("index_no"));

                    JSONObject click = new JSONObject();
                    click.put("type", "next");
                    click.put("url", "/screen_person");

                    JSONObject cols = new JSONObject();
                    cols.put("unit", code);
                    cols.put("name", RIUtil.dicts.get(code).getString("dict_name"));
                    cols.put("lx", lx);
                    JSONObject opts = GetOpts("/screen_person", "get_rk_gk", cols);
                    click.put("opt", opts);
                    det.put("dpclick", click);
                    dets.add(det);
                }
            }
            List<JSONObject> res = new ArrayList<>();
            for (int i = 0; i < dets.size(); i++) {
                JSONObject jsonObject = dets.getJSONObject(i);
                res.add(jsonObject);
            }
            Collections.sort(res, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                String a = "";
                String b = "";

                try {
                    a = o1.getString("index_no");
                    b = o2.getString("index_no");
                } catch (Exception ex) {

                }

                int result = a.compareTo(b);
                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (result > 0) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });
            back.put("data", res);

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            ora_hl.close();
        }

        return back;
    }

    private JSONObject GetPointPersonLevel(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String lx = "";
        OracleHelper ora_gl = null;
        String subStr = "";
        String unit = "";
        String type = "";
        String uSql = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            type = RIUtil.dicts.get(unit).getString("type");
            if (type.equals("23") || type.equals("24") || type.equals("28")) {
                subStr = "substr(sszrq,1,8) ";
                uSql = " and sszrq like '" + unit.substring(0, 6) + "%' ";
            } else if (type.equals("25") || type.equals("26")) {
                subStr = "sszrq ";
                uSql = " and sszrq like '" + unit.substring(0, 8) + "%' ";
            } else {
                subStr = "substr(sszrq,1,6) ";

            }
        } else {
            return ErrNo.set(501001);
        }
        lx = data.getString("lx");

        try {
            ora_gl = new OracleHelper("ora_bk_gl");
            String sql =
                    "select count(1) as count ," + subStr + " as code from czqj_ybds.tb_data_watch_point where " +
                            "jclx_xl" + " ='" + lx + "' " + uSql + " group by " + subStr;
            logger.warn(sql);
            JSONArray dets = new JSONArray();
            List<JSONObject> list = ora_gl.query(sql);
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                logger.warn(one.toString());
                String code = one.getString("CODE");
                if (code.startsWith("3204")) {
                    if (code.length() == 6) {
                        code = code + "000000";
                    } else if (code.length() == 8) {
                        code = code + "0000";
                    } else if (code.length() == 4) {

                        code = code + "00000000";
                    } else {
                        code = code;
                    }
                    logger.warn(code);
                    String name = code;
                    try {
                        name = RIUtil.dicts.get(code).getString("dict_name");
                    } catch (Exception e) {
                    }
                    JSONObject click = new JSONObject();
                    click.put("type", "next");
                    click.put("url", "/screen_person");
                    JSONObject cols = new JSONObject();
                    cols.put("lx", lx);
                    cols.put("unit", code);
                    cols.put("name", name);

                    click.put("opt", GetOpts("/screen_person", "get_point_person_level", cols));
                    one.put("dpclick", click);
                    one.put("name", name);
                    one.put("count", one.getString("COUNT"));

                    JSONObject dpclick = new JSONObject();
                    dpclick.put("type", "dialog1");
                    cols = new JSONObject();
                    cols.put("point", lx);
//                    dpclick.put("lable", unit + "_" + RIUtil.dicts.get(lx).getString("dict_name"));
                    cols.put("unit", code);
                    cols.put("name", name);

                    JSONObject opts = GetOpts("/jqzt", "get_point_loc_table", cols);
                    dpclick.put("remark", opts);
                    dpclick.put("permission", "table_page");
                    dpclick.put("id", String.valueOf(UUID.randomUUID()));

                    one.put("click", dpclick);

                    dets.add(one);
                }
            }
            back.put("data", dets);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            ora_gl.close();

        }
    }

    private JSONObject GetPointPerson(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        MysqlHelper mysql = null;
        OracleHelper ora_gl = null;
        String unit = "";
        String type = "";
        String usql = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            type = RIUtil.dicts.get(unit).getString("type");
            if (type.equals("23") || type.equals("24") || type.equals("28")) {
                usql = "sszrq like '" + unit.substring(0, 6) + "%'";

                unit = unit.substring(0, 6) + "000000";
            } else if (type.equals("25")) {
                usql = "sszrq like '" + unit.substring(0, 8) + "%'";
                unit = unit.substring(0, 8) + "0000";
            } else if (type.equals("26")) {
                usql = "sszrq = '" + unit + "'";
                unit = unit;

            } else {
                usql = " 1=1 ";
                unit = unit.substring(0, 4) + "00000000";

            }
        } else {
            return ErrNo.set(501001);
        }
        String father_id = data.getString("father_id");
        try {

            mysql = new MysqlHelper("mysql");
            ora_gl = new OracleHelper("ora_bk_gl");
            HashMap<String, String> xls = new HashMap<>();
            String sql =
                    "select count(id) as count,JCLX_XL from czqj_ybds.tb_data_watch_point where " + usql + " " +
                            "group by " + "JCLX_XL";
            List<JSONObject> list = ora_gl.query(sql);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    xls.put(one.getString("JCLX_XL"), one.getString("COUNT"));
                }
            }
            sql = "select id,dict_name from dict where isdelete=1 and father_id='" + father_id + "'";
            List<JSONObject> roomChildList = mysql.query(sql);
            JSONArray dets = new JSONArray();
            int i = 0;
            for (JSONObject one : roomChildList) {

                String name = one.getString("dict_name");
                String id = one.getString("id");
                String ids = id.replace("160-", "");
                String count = "0";
                if (xls.containsKey(ids)) {
                    count = xls.get(ids);
                }
                one.put("count", count);
                one.put("title", name);
                one.put("id", id);
                JSONObject click = new JSONObject();
                click.put("type", "dialog");
                int ran = i % 4;
                if (ran == 0) {
                    click.put("permission", "chat_bar");
                } else if (ran == 1) {
                    click.put("permission", "chat_pie");
                } else if (ran == 2) {
                    click.put("permission", "chat_pie_border");
                } else {
                    click.put("permission", "chat_rose");
                }
                JSONObject cols = new JSONObject();

                cols.put("lx", ids);
                cols.put("unit", unit);
                JSONObject opts = GetOpts("/screen_person", "get_point_person_level", cols);
                click.put("remark", opts);
                click.put("label", RIUtil.dicts.get(id).getString("dict_name"));

                one.put("click", click);
                //双击明细列表
                JSONObject dpclick = new JSONObject();
                dpclick.put("type", "dialog");
                cols = new JSONObject();
                cols.put("point", ids);
                cols.put("unit", unit);

                opts = GetOpts("/jqzt", "get_point_loc_table", cols);
                dpclick.put("remark", opts);
                dpclick.put("label", RIUtil.dicts.get(id).getString("dict_name"));
                dpclick.put("permission", "table_page");
                one.put("dpclick", dpclick);


                if (name != null && name.length() > 0) {
                    dets.add(one);
                }
                i++;
            }
            back.put("data", dets);

            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));

        } finally {
            mysql.close();
            ora_gl.close();
        }
    }

    //户籍人口迁入迁出 死亡 出生 数
    private JSONObject getHJRKRSTJ(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String lx = "";
        if (data.containsKey("lx") && data.getString("lx").length() > 0) {
            lx = data.getString("lx");
        } else {
            return ErrNo.set(null, 2, "缺少参数");
        }

        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
        } else {
            return ErrNo.set(null, 2, "缺少参数");
        }
        String start_time = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        String end_time = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
            start_time = data.getString("start_time");
        }
        if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
            end_time = data.getString("end_time");
        }
        Integer type = RIUtil.dicts.get(unit).getInteger("type");
        int index = 12;
        if (type == 21 || type == 22 || type == 27) {
            unit = unit.substring(0, 4);
            index = 6;

        } else if (type == 23 || type == 24 || type == 28) {
            unit = unit.substring(0, 6);
            index = 8;

        } else if (type == 25) {
            unit = unit.substring(0, 8);
        } else if (type == 26) {
            unit = unit.substring(0, 8);
        } else {
            unit = unit;
        }
        OracleHelper oracle = null;
        try {
            oracle = new OracleHelper("ora_bk_gl");
            String sql = " select  \"SUM\"(HJRK_QRS_SNQR_NAM+HJRK_QRS_SNQR_NV+HJRK_QRS_SWQR_NAM+HJRK_QRS_SWQR_NV) as "
                    + "QR_COUNT , \"SUM\"(HJRK_QCS_QWSN_NAM+HJRK_QCS_QWSW_NV+HJRK_QCS_QWSW_NAM+HJRK_QCS_QWSN_NV) as " +
                    "QC_COUNT ," + " " + "\"SUM" + "\"" + "(HJRK_SWS) as SW_COUNT , \"SUM\"(HJRK_CSS) as CS_COUNT ," +
                    "\"SUM\"(HJRK_LXDH_BDS+HJRK_FWCS_BDS" + "+HJRK_RJGX_BDS) as ZXBG_COUNT ,\"SUBSTR\"(JGBM,0," + index + ")"
                    + " as unit FROM CZQJ_YBDS" + ".TB_REPORT_SYRK " + "WHERE JGBM LIKE'%" + unit + "%' and " +
                    "UPLOADTIME"
                    + " " + "> " + "TO_DATE('" + start_time + "','yyyy" + "-mm-dd') and " + "UPLOADTIME < TO_DATE('" + end_time + "'," + "'yyyy-mm-dd')" + " group by " + "\"SUBSTR" + "\"" + "(JGBM,0," + index + ") ";
            logger.warn("==========>>> sql =  " + sql);

            String[] split = lx.split(",");
            JSONArray datas = new JSONArray();

            String name = "";
            for (String s : split) {
                logger.warn("====>>>> lx: " + lx + "  ==>> s : " + s);
                List<JSONObject> jsonData = oracle.query(sql);
                JSONArray array = new JSONArray();
                for (JSONObject jsonDatum : jsonData) {
                    String jgbm = jsonDatum.getString("UNIT");
                    if (jgbm.length() == 6) {
                        jgbm = jgbm + "000000";
                    }
                    if (jgbm.length() == 8) {
                        jgbm = jgbm + "0000";
                    }
                    //  数据
                    String COUNT = "0";
                    if ("QR".equals(s)) {
                        COUNT = jsonDatum.getString("QR_COUNT");
                        name = "迁入数";
                    }
                    if ("QC".equals(s)) {
                        COUNT = jsonDatum.getString("QC_COUNT");
                        name = "迁出数";
                    }
                    if ("SW".equals(s)) {
                        COUNT = jsonDatum.getString("SW_COUNT");
                        name = "死亡数";
                    }
                    if ("CS".equals(s)) {
                        if (jsonDatum.getString("CS_COUNT") != null) {
                            COUNT = jsonDatum.getString("CS_COUNT");
                            name = "出生数";
                        }
                    }
                    if ("ZXBG".equals(s)) {
                        COUNT = jsonDatum.getString("ZXBG_COUNT");
                        name = "辅项变更数";

                    }

                    JSONObject det = new JSONObject();
                    JSONObject click = new JSONObject();
                    click.put("type", "jump");
                    click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/iframe/index?target=hjrk_gk");
                    det.put("click", click);
                    det.put("unit", jgbm);
                    if (!jgbm.equals("320496000000")) {
                        det.put("count", COUNT);
                        det.put("name", RIUtil.dicts.get(jgbm).getString("dict_name"));
                        det.put("index_no", RIUtil.dicts.get(jgbm).getString("index_no"));
                        JSONObject dpclick = new JSONObject();
                        dpclick.put("type", "next");
                        dpclick.put("url", "/screen_person");
                        JSONObject cols = new JSONObject();
                        cols.put("unit", jgbm);
                        cols.put("start_time", start_time);
                        cols.put("end_time", end_time);
                        cols.put("name", RIUtil.dicts.get(jgbm).getString("dict_name"));
                        cols.put("lx", lx);
                        JSONObject opts = GetOpts("/screen_person", "get_hjrk_tj", cols);
                        dpclick.put("opt", opts);
                        if (type != 25) {
                            det.put("dpclick", dpclick);
                        }
                        array.add(det);
                    }
                }
                // array det 排序
                List<JSONObject> res = new ArrayList<>();
                for (int i = 0; i < array.size(); i++) {
                    JSONObject jsonObject = array.getJSONObject(i);
                    res.add(jsonObject);
                }
                Collections.sort(res, (JSONObject o1, JSONObject o2) -> {
                    //转成JSON对象中保存的值类型
                    String a = "";
                    String b = "";

                    try {
                        a = o1.getString("index_no");
                        b = o2.getString("index_no");
                    } catch (Exception ex) {

                    }

                    int result = a.compareTo(b);
                    // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                    if (result > 0) {  //降序排列，升序改成a>b
                        return 1;
                    } else if (a == b) {
                        return 0;
                    } else {
                        return -1;
                    }
                });
                JSONObject dts = new JSONObject();
                dts.put("det", res);
                dts.put("id", 1);
                dts.put("name", name);
                dts.put("xx", "bar");
                datas.add(dts);

            }
            back.put("data", datas);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            oracle.close();
        }

        return back;
    }


    private JSONObject GetHJRKTable(JSONObject data, String token) {
        JSONObject back = ErrNo.set(0);
        try {
            //end
            String end = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                if (end.equals(data.getString("end_time"))) {
                    end = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                } else {
                    end = data.getString("end_time");
                }
            } else {
                end = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
            //orgCode
            String unit = data.getString("unit");
            String orgCode = data.getString("unit");

            int type = RIUtil.dicts.get(unit).getInteger("type");
            int orgType = 0;
            if (type == 21 || type == 22 || type == 27) {
                orgType = 2;
                unit = unit.substring(0, 6) + "000000";

            } else if (type == 23 || type == 24 || type == 28) {
                unit = unit.substring(0, 8) + "0000";
                orgType = 4;
            } else if (type == 25) {
                orgType = 4;
                unit = unit;
            } else {
                unit = unit;
                orgType = 4;
            }
            // _t
            long t = System.currentTimeMillis() / 1000;
            String ywlx = "hjrk";
            Integer pageNo = 1;
            Integer pageSize = 20;
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                pageNo = data.getInteger("page");
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                pageSize = data.getInteger("limit");
            }

            String hdn = "序号,人口类型,姓名,民族,证件号码,联系电话,户籍区划,实际居住地址,户籍地址,所属单位,修改时间";
            String keys =
                    "num,rkgllbdm_dictText,xm,mzdm_dictText,zjhm,lxdh,hjdzSsxqdmDictText,sjjzdDzmc,hjdzDzmc," +
                            "sjgsdwmc," + "czsj";
            JSONArray heads = GetHeads(hdn, keys);
            String url = "http://50.56.94.47/czqjpt-api/syrk/page/report/syrkgk?_t=" + t + "&end=" + end +
                    "&orgType=" + orgType + "&orgCode" + "=" + unit + "&ywlx=" + ywlx + "&field=&pageNo=" + pageNo +
                    "&pageSize=" + pageSize;
            logger.warn("===========>>> url" + url);
            JSONObject jsonData = GetOkHttpGet(url, token);
            JSONArray datas = new JSONArray();
            int count = 0;
            if (jsonData.containsKey("code") && jsonData.getInteger("code") == 200) {
                JSONObject result = jsonData.getJSONObject("result");
                count = result.getInteger("total");
                JSONArray recods = result.getJSONArray("records");
                for (int i = 0; i < recods.size(); i++) {
                    JSONObject one = recods.getJSONObject(i);
                    JSONObject det = new JSONObject();
                    //序号
                    String num = String.valueOf(i + 1);
                    JSONObject val = new JSONObject();
                    val.put("value", num);
                    det.put("num", val);
                    //ren口类型
                    String rkgllbdm = one.getString("rkgllbdm_dictText");
                    val = new JSONObject();
                    val.put("value", rkgllbdm);
                    det.put("rkgllbdm_dictText", val);
                    //姓名
                    String czrxm = one.getString("xm");
                    val = new JSONObject();
                    val.put("value", czrxm);
                    det.put("xm", val);
                    //民族
                    String mzdm_dictText = one.getString("mzdm_dictText");
                    val = new JSONObject();
                    val.put("value", mzdm_dictText);
                    det.put("mzdm_dictText", val);
                    //证件号码
                    String gmsfhm = one.getString("zjhm");
                    JSONObject click = new JSONObject();
                    val = new JSONObject();
                    val.put("value", gmsfhm);
                    click = new JSONObject();
                    click.put("type", "jump_person");
                    click.put("url", gmsfhm);
                    val.put("click", click);
                    det.put("zjhm", val);

                    //联系电话
                    String lxdh = one.getString("lxdh");
                    val = new JSONObject();
                    val.put("value", lxdh);
                    det.put("lxdh", val);
                    //户籍区划
                    String hjdzSsxqdmDictText = one.getString("hjdzSsxqdm_dictText");
                    val = new JSONObject();
                    val.put("value", hjdzSsxqdmDictText);
                    det.put("hjdzSsxqdmDictText", val);
                    //实际居住地
                    String sjjzdDzmc = one.getString("sjjzdDzmc");
                    val = new JSONObject();
                    val.put("value", sjjzdDzmc);
                    det.put("sjjzdDzmc", val);
                    //户籍地址
                    String hjdzDzmc = one.getString("hjdzDzmc");
                    val = new JSONObject();
                    val.put("value", hjdzDzmc);
                    det.put("hjdzDzmc", val);
                    //所属单位
                    String sjgsdwmc = one.getString("sjgsdwmc");
                    val = new JSONObject();
                    val.put("value", sjgsdwmc);
                    det.put("sjgsdwmc", val);
                    //时间
                    String czsj = one.getString("czsj");
                    val = new JSONObject();
                    val.put("value", czsj);
                    det.put("czsj", val);
                    datas.add(det);
                }
            } else {
                return ErrNo.set(null, 2, jsonData.getString("message"));
            }
            JSONObject dets = new JSONObject();
            dets.put("head", heads);
            dets.put("body", datas);
            dets.put("count", count);
            back.put("data", dets);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private JSONObject getHJRKQRQC(JSONObject data, String token) {
        JSONObject back = ErrNo.set(0);
        //获取 _t
        long t = System.currentTimeMillis() / 1000;
        //获取column
        String column = "createTime";
        //获取 order
        String order = "desc";

        //beginStr  endStr
        String end = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        String begin = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
            if (end.equals(data.getString("end_time"))) {
                end = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } else {
                end = data.getString("end_time");
            }
        } else {
            end = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
            if (begin.equals(data.getString("start_time"))) {
                begin = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } else {
                begin = data.getString("start_time");
            }
        } else {
            begin = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        //page
        Integer pageNo = 1;
        Integer pageSize = 99;
        if (data.containsKey("page") && data.getString("page").length() > 0) {
            pageNo = data.getInteger("page");
        }
        if (data.containsKey("limit") && data.getString("limit").length() > 0) {
            pageSize = data.getInteger("limit");
        }
        String point = data.getString("point");
        if (point.equals("1")) {
            String url =
                    "http://50.56.94.47/czqjpt-api/report/qshjqyswqrtj?_t=" + t + "&beginStr=" + begin +
                            "&endStr=" + end + "&column=" + column + "&order=" + order + "&field=&pageNo=" + pageNo +
                            "&pageSize=" + pageSize + " ";
            logger.warn("=======>>>>  " + url);
            JSONObject jsonData = GetOkHttpGet(url, token);
            JSONArray datas = new JSONArray();

            if (jsonData.containsKey("code") && jsonData.getInteger("code") == 200) {
                JSONArray result = jsonData.getJSONArray("result");
                if (result.size() <= 1) {
                    return ErrNo.set(null, 2, "时间传入错误");
                }
                for (int i = 0; i < result.size(); i++) {
                    JSONObject one = result.getJSONObject(i);
                    String swqrmc = one.getString("swqrmc");
                    Integer swqrsl = one.getInteger("swqrsl");
                    if (swqrsl > 0 && !"合计".equals(swqrmc)) {
                        JSONObject det = new JSONObject();
                        det.put("name", swqrmc);
                        det.put("count", swqrsl);
                        JSONObject click = new JSONObject();
                        click.put("type", "jump");
                        click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/iframe/index?target=hjrk_gk");
                        det.put("click", click);
                        datas.add(det);
                    }
                }
            }
            // 创建一个包装类来实现Comparable接口
            class EntryWrapper implements Comparable<EntryWrapper> {
                private Entry<String, Integer> entry;

                public EntryWrapper(Entry<String, Integer> entry) {
                    this.entry = entry;
                }

                public int getValue() {
                    return this.entry.getValue();
                }

                @Override
                public int compareTo(EntryWrapper other) {
                    // 降序排序
                    return Integer.compare(other.getValue(), this.getValue());
                }
            }

            // 使用最小堆存储前10个最大count值的条目
            PriorityQueue<EntryWrapper> topTenEntries = new PriorityQueue<>(10, Comparator.reverseOrder());

            // 遍历datas并维护最小堆
            for (int i = 0; i < datas.size(); i++) {
                JSONObject det = datas.getJSONObject(i);
                String name = det.getString("name");
                Integer count = det.getInteger("count");
                EntryWrapper entryWrapper = new EntryWrapper(new AbstractMap.SimpleEntry<>(name, count));

                if (topTenEntries.size() < 10) {
                    topTenEntries.offer(entryWrapper);
                } else if (entryWrapper.getValue() > topTenEntries.peek().getValue()) {
                    topTenEntries.poll();
                    topTenEntries.offer(entryWrapper);
                }
            }

            // 将最小堆中的条目转换为JSONArray
            JSONArray topTenData = new JSONArray();
            while (!topTenEntries.isEmpty()) {
                EntryWrapper entryWrapper = topTenEntries.poll();
                JSONObject obj = new JSONObject();
                obj.put("name", entryWrapper.entry.getKey());
                obj.put("count", entryWrapper.entry.getValue());
                JSONObject click = new JSONObject();
                click.put("type", "jump");
                click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/iframe/index?target=hjrk_gk");
                obj.put("click", click);
                topTenData.add(obj);
            }

            back.put("data", topTenData);
        } else if (point.equals("2")) {//省内他市迁入
            String url =
                    "http://50.56.94.47/czqjpt-api/report/qshjqysntsqrtj?_t=" + t + "&beginStr=" + begin +
                            "&endStr"
                            + "=" + end + "&column=" + column + "&order=" + order + "&field=&pageNo=" + pageNo +
                            "&pageSize=" + pageSize + " ";
            logger.warn("=======>>>>  " + url);
            JSONObject jsonData = GetOkHttpGet(url, token);
            JSONArray datas = new JSONArray();
            JSONObject json = new JSONObject();

            if (jsonData.containsKey("code") && jsonData.getInteger("code") == 200) {
                JSONArray result = jsonData.getJSONArray("result");
                if (result.size() <= 1) {
                    return ErrNo.set(null, 2, "时间传入错误");
                }
                JSONArray detArray = new JSONArray();
                for (int i = 0; i < result.size(); i++) {
                    JSONObject one = result.getJSONObject(i);
                    String sntsqrsmc = one.getString("sntsqrsmc");
                    Integer sntsqrsl = one.getInteger("sntsqrsl");
                    if (sntsqrsl > 0 && !"合计".equals(sntsqrsmc)) {
                        JSONObject det = new JSONObject();
                        det.put("name", sntsqrsmc.substring(0, sntsqrsmc.length() - 1).replace("江苏省", ""));
                        det.put("count", sntsqrsl);
                        JSONObject click = new JSONObject();
                        click.put("type", "jump");
                        click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/iframe/index?target=hjrk_gk");
                        det.put("click", click);
                        detArray.add(det);
                    }
                }
                json.put("det", detArray);
                json.put("name", "省内他市迁入");
                datas.add(json);
            }
            back.put("data", datas);
        } else if (point.equals("3")) {//迁往省外
            String url =
                    "http://50.56.94.47/czqjpt-api/report/qshjqyqwswtj?_t=" + t + "&beginStr=" + begin +
                            "&endStr=" + end + "&column=" + column + "&order=" + order + "&field=&pageNo=" + pageNo +
                            "&pageSize=" + pageSize + " ";
            logger.warn("=======>>>>  " + url);
            JSONObject jsonData = GetOkHttpGet(url, token);
            JSONArray datas = new JSONArray();

            if (jsonData.containsKey("code") && jsonData.getInteger("code") == 200) {
                JSONArray result = jsonData.getJSONArray("result");
                if (result.size() <= 1) {
                    return ErrNo.set(null, 2, "时间传入错误");
                }
                for (int i = 0; i < result.size(); i++) {
                    JSONObject one = result.getJSONObject(i);
                    String qwsfmc = one.getString("qwsfmc");
                    Integer qwsfsl = one.getInteger("qwsfsl");
                    if (qwsfsl > 0 && !"合计".equals(qwsfmc)) {
                        JSONObject det = new JSONObject();
                        det.put("name", qwsfmc);
                        det.put("count", qwsfsl);
                        JSONObject click = new JSONObject();
                        click.put("type", "jump");
                        click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/iframe/index?target=hjrk_gk");
                        det.put("click", click);
                        datas.add(det);
                    }
                }
            }
            // 创建一个包装类来实现Comparable接口
            class EntryWrapper implements Comparable<EntryWrapper> {
                private Entry<String, Integer> entry;

                public EntryWrapper(Entry<String, Integer> entry) {
                    this.entry = entry;
                }

                public int getValue() {
                    return this.entry.getValue();
                }

                @Override
                public int compareTo(EntryWrapper other) {
                    // 降序排序
                    return Integer.compare(other.getValue(), this.getValue());
                }
            }

            // 使用最小堆存储前10个最大count值的条目
            PriorityQueue<EntryWrapper> topTenEntries = new PriorityQueue<>(10, Comparator.reverseOrder());

            // 遍历datas并维护最小堆
            for (int i = 0; i < datas.size(); i++) {
                JSONObject det = datas.getJSONObject(i);
                String name = det.getString("name");
                Integer count = det.getInteger("count");
                EntryWrapper entryWrapper = new EntryWrapper(new AbstractMap.SimpleEntry<>(name, count));

                if (topTenEntries.size() < 10) {
                    topTenEntries.offer(entryWrapper);
                } else if (entryWrapper.getValue() > topTenEntries.peek().getValue()) {
                    topTenEntries.poll();
                    topTenEntries.offer(entryWrapper);
                }
            }

            // 将最小堆中的条目转换为JSONArray
            JSONArray topTenData = new JSONArray();
            while (!topTenEntries.isEmpty()) {
                EntryWrapper entryWrapper = topTenEntries.poll();
                JSONObject obj = new JSONObject();
                obj.put("name", entryWrapper.entry.getKey());
                obj.put("count", entryWrapper.entry.getValue());
                JSONObject click = new JSONObject();
                click.put("type", "jump");
                click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/iframe/index?target=hjrk_gk");
                obj.put("click", click);
                topTenData.add(obj);

            }
            back.put("data", topTenData);
        } else if (point.equals("4")) {
            String url =
                    "http://50.56.94.47/czqjpt-api/report/qshjqyqwsntatj?_t=" + t + "&beginStr=" + begin +
                            "&endStr"
                            + "=" + end + "&column=" + column + "&order=" + order + "&field=&pageNo=" + pageNo +
                            "&pageSize=" + pageSize + " ";
            logger.warn("=======>>>>  " + url);
            JSONObject jsonData = GetOkHttpGet(url, token);
            JSONArray datas = new JSONArray();
            JSONObject json = new JSONObject();
            if (jsonData.containsKey("code") && jsonData.getInteger("code") == 200) {
                JSONArray result = jsonData.getJSONArray("result");
                if (result.size() <= 1) {
                    return ErrNo.set(null, 2, "时间传入错误");
                }
                JSONArray detArray = new JSONArray();
                for (int i = 0; i < result.size(); i++) {
                    JSONObject one = result.getJSONObject(i);
                    String qwsnsmc = one.getString("qwsnsmc");
                    Integer qwsnssl = one.getInteger("qwsnssl");
                    if (qwsnssl > 0 && !"合计".equals(qwsnsmc)) {
                        JSONObject det = new JSONObject();
                        det.put("name", qwsnsmc.substring(0, qwsnsmc.length() - 1).replace("江苏省", ""));
                        det.put("count", qwsnssl);
                        JSONObject click = new JSONObject();
                        click.put("type", "jump");
                        click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/iframe/index?target=hjrk_gk");
                        det.put("click", click);
                        detArray.add(det);
                    }
                }
                json.put("det", detArray);
                json.put("name", "迁往省内他市");
                datas.add(json);
            }
            back.put("data", datas);
        }
        return back;
    }

    ;

    private JSONObject getHJRKNL(JSONObject data, String token) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
        } else {
            return ErrNo.set(null, 2, "缺少参数");
        }
        // end
//        String end = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
//        if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
//            if (end.equals(data.getString("end_time"))) {
//                end = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//            } else {
//                end = data.getString("end_time");
//            }
//        } else {
        String end = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//        }

        //获取 orgType
        int type = RIUtil.dicts.get(unit).getInteger("type");
        int orgType = 0;
        if (type == 21 || type == 22 || type == 27) {
            orgType = 1;
            unit = unit.substring(0, 4) + "00000000";

        } else if (type == 23 || type == 24 || type == 28) {
            unit = unit.substring(0, 6) + "000000";
            orgType = 2;
        } else if (type == 25) {
            orgType = 3;
            unit = unit;
        } else {
            unit = unit;
            orgType = 4;
        }
        //获取 _t
        long t = System.currentTimeMillis() / 1000;
        String point = data.getString("point");
        String url = "http://50.56.94.47/czqjpt-api/bigScreen/hjrktj?_t=" + t + "&end=" + end +
                "&parentOrgCode=" + unit + "&orgType=" + orgType + " ";
        JSONObject jsonData = GetOkHttpGet(url, token);
        logger.warn("===========>>> url" + url);
        JSONObject jsonObject = GetOkHttpGet(url, token);
        JSONArray datas = new JSONArray();
        if (point.equals("1")) {//年龄分布
            if (jsonObject.containsKey("code") && jsonObject.getInteger("code") == 200) {
                JSONArray result = jsonObject.getJSONArray("result");

                for (int i = 0; i < result.size(); i++) {
                    JSONObject one = result.getJSONObject(i);

                    JSONObject det = new JSONObject();
                    //0-17

                    Integer fbhjrkLdsq = one.getInteger("fbhjrkLdsq");
                    det = new JSONObject();
                    det.put("count", fbhjrkLdsq);
                    det.put("name", "0-17岁");
                    JSONObject click = new JSONObject();
                    click.put("type", "jump");
                    click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/iframe/index?target=ldrk_nld");
                    det.put("click", click);
                    datas.add(det);
                    //18-34
                    Integer fbhjrkSbdss = one.getInteger("fbhjrkSbdss");
                    det = new JSONObject();
                    det.put("count", fbhjrkSbdss);
                    det.put("name", "18-34岁");
                    click = new JSONObject();
                    click.put("type", "jump");
                    click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/iframe/index?target=ldrk_nld");
                    det.put("click", click);
                    datas.add(det);
                    //35-59
                    Integer fbhjrkSbds = one.getInteger("fbhjrkSwdwj");
                    det = new JSONObject();
                    det.put("count", fbhjrkSbds);
                    det.put("name", "35-59岁");
                    click = new JSONObject();
                    click.put("type", "jump");
                    click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/iframe/index?target=ldrk_nld");
                    det.put("click", click);
                    datas.add(det);
                    //60-79
                    Integer fbhjrkLsdpj = one.getInteger("fbhjrkLsdqj");
                    det = new JSONObject();
                    det.put("count", fbhjrkLsdpj);
                    det.put("name", "60-79岁");
                    click = new JSONObject();
                    click.put("type", "jump");
                    click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/iframe/index?target=ldrk_nld");
                    det.put("click", click);
                    datas.add(det);
                    //80以上
                    Integer fbhjrkBsys = one.getInteger("fbhjrkBsys");
                    det = new JSONObject();
                    det.put("count", fbhjrkBsys);
                    det.put("name", "80岁以上");
                    click = new JSONObject();
                    click.put("type", "jump");
                    click.put("url", "http://qjjcgzpt.czx.js/web/index.html#/iframe/index?target=ldrk_nld");
                    det.put("click", click);
                    datas.add(det);
                }
            }
            back.put("data", datas);
            return back;
        }

        if (point.equals("2")) {//少数民族分布

            if (jsonObject.containsKey("code") && jsonObject.getInteger("code") == 200) {
                JSONArray result = jsonObject.getJSONArray("result");

                Map<String, Object> fbhjrkmz = null;
                for (int i = 0; i < result.size(); i++) {
                    JSONObject one = result.getJSONObject(i);
                    JSONObject det = new JSONObject();
                    Map<String, String> ethnicGroupMap = new HashMap<>();

                    ethnicGroupMap.put("fbhjrkMzMenggu", "蒙古族");
                    ethnicGroupMap.put("fbhjrkMzHui", "回族");
                    ethnicGroupMap.put("fbhjrkMzCang", "藏族");
                    ethnicGroupMap.put("fbhjrkMzWeiwuer", "维吾尔族");
                    ethnicGroupMap.put("fbhjrkMzMiao", "苗族");
                    ethnicGroupMap.put("fbhjrkMzYi", "彝族");
                    ethnicGroupMap.put("fbhjrkMzZhuang", "壮族");
                    ethnicGroupMap.put("fbhjrkMzBuyi", "布依族");
                    ethnicGroupMap.put("fbhjrkMzChaoxian", "朝鲜族");
                    ethnicGroupMap.put("fbhjrkMzMan", "满族");
                    ethnicGroupMap.put("fbhjrkMzDong", "侗族");
                    ethnicGroupMap.put("fbhjrkMzYao", "瑶族");
                    ethnicGroupMap.put("fbhjrkMzBai", "白族");
                    ethnicGroupMap.put("fbhjrkMzTujia", "土家族");
                    ethnicGroupMap.put("fbhjrkMzHani", "哈尼族");
                    ethnicGroupMap.put("fbhjrkMzHasake", "哈萨克族");
                    ethnicGroupMap.put("fbhjrkMzDai", "傣族");
                    ethnicGroupMap.put("fbhjrkMzLi", "黎族");
                    ethnicGroupMap.put("fbhjrkMzLisu", "傈僳族");
                    ethnicGroupMap.put("fbhjrkMzWa", "佤族");
                    ethnicGroupMap.put("fbhjrkMzShe", "畲族");
                    ethnicGroupMap.put("fbhjrkMzGaoshan", "高山族");
                    ethnicGroupMap.put("fbhjrkMzLahu", "拉祜族");
                    ethnicGroupMap.put("fbhjrkMzShui", "水族");
                    ethnicGroupMap.put("fbhjrkMzDongxiang", "东乡族");
                    ethnicGroupMap.put("fbhjrkMzNaxi", "纳西族");
                    ethnicGroupMap.put("fbhjrkMzJingpo", "景颇族");
                    ethnicGroupMap.put("fbhjrkMzKeerkezi", "柯尔克孜族");
                    ethnicGroupMap.put("fbhjrkMzTu", "土族");
                    ethnicGroupMap.put("fbhjrkMzDawoer", "达斡尔族");
                    ethnicGroupMap.put("fbhjrkMzMulao", "仫佬族");
                    ethnicGroupMap.put("fbhjrkMzQiang", "羌族");
                    ethnicGroupMap.put("fbhjrkMzBulang", "布朗族");
                    ethnicGroupMap.put("fbhjrkMzSala", "撒拉族");
                    ethnicGroupMap.put("fbhjrkMzMaonan", "毛南族");
                    ethnicGroupMap.put("fbhjrkMzYilao", "仡佬族");
                    ethnicGroupMap.put("fbhjrkMzXibo", "锡伯族");
                    ethnicGroupMap.put("fbhjrkMzAchang", "阿昌族");
                    ethnicGroupMap.put("fbhjrkMzPumi", "普米族");
                    ethnicGroupMap.put("fbhjrkMzTajike", "塔吉克族");
                    ethnicGroupMap.put("fbhjrkMzNu", "怒族");
                    ethnicGroupMap.put("fbhjrkMzWuzibieke", "乌兹别克族");
                    ethnicGroupMap.put("fbhjrkMzEluosi", "俄罗斯族");
                    ethnicGroupMap.put("fbhjrkMzEwenke", "鄂温克族");
                    ethnicGroupMap.put("fbhjrkMzDeang", "德昂族");
                    ethnicGroupMap.put("fbhjrkMzBaoan", "保安族");
                    ethnicGroupMap.put("fbhjrkMzYugu", "裕固族");
                    ethnicGroupMap.put("fbhjrkMzJing", "京族");
                    ethnicGroupMap.put("fbhjrkMzTataer", "塔塔尔族");
                    ethnicGroupMap.put("fbhjrkMzDulong", "独龙族");
                    ethnicGroupMap.put("fbhjrkMzElunchun", "鄂伦春族");
                    ethnicGroupMap.put("fbhjrkMzHezhe", "赫哲族");
                    ethnicGroupMap.put("fbhjrkMzMenba", "门巴族");
                    ethnicGroupMap.put("fbhjrkMzLuoba", "珞巴族");
                    ethnicGroupMap.put("fbhjrkMzJinuo", "基诺族");
                    ethnicGroupMap.put("fbhjrkMzChuanqingren", "穿青人");
                    ethnicGroupMap.put("fbhjrkMzGejiaren", "革家人");
                    ethnicGroupMap.put("fbhjrkMzRuji", "儒家人");
                    ethnicGroupMap.put("fbhjrkMzQita", "其他");
                    datas = new JSONArray();
                    for (Entry<String, String> entry : ethnicGroupMap.entrySet()) {
                        det = new JSONObject();
                        det.put("name", entry.getValue());
                        det.put("count", one.getInteger(entry.getKey()));
                        datas.add(det);
                    }
                }
            }

// 创建一个包装类来实现Comparable接口
            class EntryWrapper implements Comparable<EntryWrapper> {
                private Entry<String, Integer> entry;

                public EntryWrapper(Entry<String, Integer> entry) {
                    this.entry = entry;
                }

                public int getValue() {
                    return this.entry.getValue();
                }

                @Override
                public int compareTo(EntryWrapper other) {
                    // 降序排序
                    return Integer.compare(other.getValue(), this.getValue());
                }
            }

            // 使用最小堆存储前10个最大count值的条目
            PriorityQueue<EntryWrapper> topTenEntries = new PriorityQueue<>(11, Comparator.reverseOrder());

// 遍历datas并维护最小堆
            for (int i = 0; i < datas.size(); i++) {
                JSONObject det = datas.getJSONObject(i);
                String name = det.getString("name");
                Integer count = det.getInteger("count");
                EntryWrapper entryWrapper = new EntryWrapper(new AbstractMap.SimpleEntry<>(name, count));

                if (topTenEntries.size() < 11) {
                    topTenEntries.offer(entryWrapper);
                } else if (entryWrapper.getValue() > topTenEntries.peek().getValue()) {
                    topTenEntries.poll();
                    topTenEntries.offer(entryWrapper);
                }
            }

            // 将最小堆中的条目转换为JSONArray
            JSONArray topTenData = new JSONArray();
            while (!topTenEntries.isEmpty()) {
                EntryWrapper entryWrapper = topTenEntries.poll();
                JSONObject obj = new JSONObject();
                obj.put("name", entryWrapper.entry.getKey());
                obj.put("count", entryWrapper.entry.getValue());
                topTenData.add(obj);
            }
            back.put("data", topTenData);

        }
        return back;

    }

    private JSONObject GetHJRKPie(JSONObject data, String token) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        // end
        String end = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

        if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
            if (end.equals(data.getString("end_time"))) {
                end = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } else {
                end = data.getString("end_time");
            }
        } else {
            end = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
        } else {
            return ErrNo.set(null, 2, "缺少参数");
        }
        //获取 orgType
        int type = RIUtil.dicts.get(unit).getInteger("type");
        int orgType = 0;
        if (type == 21 || type == 22 || type == 27) {
            orgType = 2;
            unit = unit.substring(0, 4) + "00000000";

        } else if (type == 23 || type == 24 || type == 28) {
            unit = unit.substring(0, 6) + "000000";
            orgType = 3;
        } else if (type == 25 || type == 26) {
            orgType = 4;
            unit = unit.substring(0, 8) + "0000";
        } else {
            unit = unit;
            orgType = 4;
        }
        //获取 _t
        long t = System.currentTimeMillis() / 1000;
        //获取orgCode
        int orgCode = 3;
        //获取 column
        String column = "createTime";
        //获取 order
        String order = "desc";
        //获取pageNo 与 pageSize
        Integer pageNo = 1;
        Integer pageSize = 99;
        if (data.containsKey("pageNo") && data.getString("pageNo").length() > 0) {
            pageNo = data.getInteger("pageNo");
        }
        if (data.containsKey("pageSize") && data.getString("pageSize").length() > 0) {
            pageSize = data.getInteger("pageSize");
        }
        String url =
                "http://50.56.94.47/czqjpt-api/report/syrk?_t=" + t + "&end=" + end + "&orgType=" + orgType +
                        "&orgCode=" + orgCode + "&column=" + column + "&order=" + order + "&" + "&field=&pageNo=" + pageNo +
                        "&pageSize=" + pageSize + " ";
        logger.warn("===========>>> url" + url);
        JSONObject jsonObject = GetOkHttpGet(url, token);
        JSONArray datas = new JSONArray();

        if (jsonObject.containsKey("code") && jsonObject.getInteger("code") == 200) {
            JSONArray result = jsonObject.getJSONArray("result");

            for (int i = 0; i < result.size(); i++) {
                JSONObject one = result.getJSONObject(i);
                String u_type = "";
                if (orgType == 3) {
                    u_type = one.getString("fjdm");
                }
                if (orgType == 4) {
                    u_type = one.getString("pcsdm");
                }
                if (unit.equals(u_type) || orgType == 2) {
                    //获取辖区数据
                    if (!one.getString("jgmc").equals("合计")) {
                        JSONObject det = new JSONObject();
                        det.put("unit", one.getString("jgbm"));
                        det.put("name", one.getString("jgmc"));
                        det.put("count", one.getString("hjrktotal"));
                        //单机事件

                        JSONObject click = new JSONObject();
                        click.put("type", "dialog");
                        JSONObject cols = new JSONObject();
                        cols.put("unit", det.getString("unit"));
                        cols.put("name", RIUtil.dicts.get(det.getString("unit")).getString("dict_name"));
                        JSONObject opts = GetOpts("/screen_person", "get_hjrk_table", cols);
                        click.put("remark", opts);
                        click.put("label", RIUtil.dicts.get(det.getString("unit")).getString("dict_name"));
                        click.put("permission", "table_page");
                        click.put("id", String.valueOf(UUID.randomUUID()));
                        one.put("click", click);
                        //双击事件
                        JSONObject dpclick = new JSONObject();
                        dpclick.put("type", "next");
                        dpclick.put("url", "/screen_person");
                        dpclick.put("name", RIUtil.dicts.get(det.getString("unit")).getString("dict_name"));
                        dpclick.put("id", String.valueOf(UUID.randomUUID()));
                        cols = new JSONObject();
                        cols.put("unit", det.getString("unit"));
                        cols.put("name", RIUtil.dicts.get(det.getString("unit")).getString("dict_name"));
                        cols.put("end_time", end);
                        opts = GetOpts("/screen_person", "get_hjrk_sta", cols);
                        dpclick.put("opt", opts);
                        if (orgType != 4) {
                            det.put("dpclick", dpclick);
                        }
                        det.put("click", click);
                        datas.add(det);
                    }
                }
            }
        }
        back.put("data", datas);
        return back;
    }

    ;

    private JSONObject GetOkHttpGet(String url, String token) {
        try {

            OkHttpClient client = new OkHttpClient().newBuilder().build();
            Request request =
                    new Request.Builder().url(url).method("GET", null).addHeader("X-Access-Token", token).build();
            Response response = client.newCall(request).execute();
            String res = response.body().string();
            JSONObject resj = JSONObject.parseObject(res);
            return resj;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            JSONObject back = new JSONObject();
            back.put("code", 301);
            back.put("message", Lib.getTrace(ex));
            return back;
        }
    }

    private JSONObject GetOkHttpQQBpost(String url, String token, JSONObject dets) {
        try {

            OkHttpClient client =
                    new OkHttpClient().newBuilder().connectTimeout(500, TimeUnit.SECONDS).readTimeout(500,
                            TimeUnit.SECONDS).writeTimeout(500, TimeUnit.SECONDS).build();
            MediaType mediaType = MediaType.parse("application/json");

            RequestBody body = RequestBody.create(mediaType, dets.toString());
            Request request =
                    new Request.Builder().url(url).method("POST", body).addHeader("Authorization", token).addHeader(
                            "Content"
                                    + "-Type", "application/json").build();
            Response response = client.newCall(request).execute();
            String res = response.body().string();
            JSONObject resj = JSONObject.parseObject(res);
            return resj;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            JSONObject back = new JSONObject();
            back.put("code", 301);
            back.put("message", Lib.getTrace(ex));
            return back;
        }
    }


    private JSONObject GetZdryLxgk(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        String unit = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
        } else {
            return ErrNo.set(null, 2, "缺少参数");
        }
        int type = RIUtil.dicts.get(unit).getInteger("type");

        if (type == 21 || type == 22 || type == 27) {
            unit = unit.substring(0, 4) + "00000000";
        } else if (type == 23 || type == 24 || type == 28) {
            unit = unit.substring(0, 6) + "000000";
        } else if (type == 25) {
            unit = unit.substring(0, 8) + "0000";
        } else {
            unit = unit;
        }

        String rynlfbs = "205-05007000,205-05008000";
        HashMap<String, String> dictNamdes = GetDictNames(rynlfbs);
        MysqlHelper my_143 = null;
        try {
            my_143 = new MysqlHelper("mysql_zxqc");
            String sql =
                    "select type,count from sta_zf_mid where type in ('" + rynlfbs.replace(",", "','") + "') " + "and"
                            + " " + "code='" + unit + "'";
            logger.warn(sql);
            List<JSONObject> list = my_143.query(sql);
            JSONArray dets = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String id = one.getString("type");
                String count = one.getString("count");

                JSONObject cols = new JSONObject();
                cols.put("unit", unit);
                cols.put("dict_id", id);

                JSONObject click = new JSONObject();
                click.put("type", "map");
                click.put("opt", GetOpts("/screen_map", "get_rk", cols));
                click.put("url", "/screen_map");


                JSONObject det = new JSONObject();
                det.put("name", dictNamdes.get(id));
                det.put("count", count);
                det.put("click", click);
                dets.add(det);


            }
            back.put("data", dets);
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            my_143.close();
        }


    }

    private JSONObject GetZdryZallJq(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");

        } else {
            return ErrNo.set(null, 2, "缺少参数unit");
        }

        int type = RIUtil.dicts.get(unit).getInteger("type");

        if (type == 21 || type == 22 || type == 27) {
            unit = unit.substring(0, 4);
        } else if (type == 23 || type == 24 || type == 28) {
            unit = unit.substring(0, 6);
        } else if (type == 25 || type == 26) {
            unit = unit.substring(0, 8);
        } else {
            unit = "3204";
        }
        String start_time = data.getString("start_time");
        String end_time = data.getString("end_time");

        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            String sql =
                    "select XM,SFZH,BJNR,SSSQ,RYLX ,JJBH,YJSJXX from DSJ.OBJ_BASE_2201 where yjsjxx>='" + start_time + " " + "00:00:00' and  yjsjxx<='" + end_time + " 23:59:59' and SSSQDM like  '" + unit + "%' " + "order by " + "yjsjxx desc" + " offset 0 rows " + "fetch next 10 rows only";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            JSONArray dets = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String title = one.getString("XM") + " 身份证号：" + one.getString("SFZH");
                String content = one.getString("BJNR");
                String time = one.getString("YJSJXX");
                String zrq = one.getString("SSSQ");
                String jjbh = one.getString("JJBH");
                JSONObject labels = new JSONObject();
                labels.put("id", "00");
                labels.put("name", one.getString("RYLX"));
                labels.put("color", "#ff0000");
                JSONArray labs = new JSONArray();
                labs.add(labels);
                JSONObject det = new JSONObject();
                det.put("title", title);
                det.put("content", content);
                det.put("time", time);
                det.put("unit", zrq);
                det.put("label", labs);

                JSONObject click = new JSONObject();
                click.put("type", "caseDetail");
                JSONObject cols = new JSONObject();
                cols.put("jjbh", jjbh);
                click.put("opt", cols);
                det.put("click", click);

                JSONObject dpclick = new JSONObject();
                dpclick.put("type", "dialog");
                dpclick.put("label", "明细");
                dpclick.put("permission", "table_page");
                JSONObject col = new JSONObject();
                col.put("unit", unit);
                col.put("isExp", 0);
                col.put("start_time", start_time);
                col.put("end_time", end_time);
                JSONObject opt = GetOpts("/screen_person", "get_zdry_zall_jq_table", col);
                dpclick.put("remark", opt);
                det.put("dpclick", dpclick);

                dets.add(det);


            }
            back.put("data", dets);
            return back;

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            ora_hl.close();
        }
    }

    private JSONObject GetZdrySfsxGfxq(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper my143 = null;
        try {
            String sql = "";
            String unit = data.getString("unit");
            JSONObject dict = RIUtil.dicts.get(unit);
            String type = dict.getString("type");
            if (type.equals("21") || type.equals("22") || type.equals("27")) {
                unit = "3204";
            } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                unit = unit.substring(0, 6);

            } else if (type.equals("25")) {
                unit = unit.substring(0, 8);

            } else {
                unit = unit;
            }

            my143 = new MysqlHelper("mysql_zxqc");
            if (data.containsKey("xl")) {
                String xl = data.getString("xl");
                sql = "select id from dict  where (type=180 and id='" + xl + "') or (type=181 " + "and " + "father_id"
                        + "='" + xl + "')";

                List<JSONObject> dicts = my143.query(sql);
                String xlsql = " ";
                for (int d = 0; d < dicts.size(); d++) {
                    JSONObject done = dicts.get(d);
                    xlsql = xlsql + " XLGLB like '" + done.getString("id") + "' or ";

                }

                if (xlsql.length() > 2) {
                    xlsql = xlsql.substring(0, xlsql.length() - 3);
                }

                sql =
                        "select DMDM,count(gmsfhm) as count,DZMC from   static_zdry where (" + xlsql + ") and HJZRQ " +
                                "like '%" + unit + "%' group  by dmdm order by " + "count desc limit 5";

                logger.warn(sql);

            } else if (data.containsKey("gljb")) {
                sql = "select DMDM,count(gmsfhm) as count,DZMC from   static_zdry where zdrygljb='" + data.getString(
                        "gljb") + "' and HJZRQ " + "like '%" + unit + "%' group  by dmdm order by " + "count desc " + "limit 5";
                logger.warn(sql);

            } else {
                return ErrNo.set(null, 2, "缺少管理级别或身份属性");
            }

            List<JSONObject> sds = my143.query(sql);
            JSONArray dets = new JSONArray();
            for (int i = 0; i < sds.size(); i++) {
                JSONObject one = sds.get(i);
                String name = one.getString("DZMC");
                if (name.contains("号")) {
                    name = name.split("号")[1];
                }
                String count = one.getString("count");
                String dmid = one.getString("DMDM");

                JSONObject det = new JSONObject();
                det.put("name", name);
                det.put("count", count);
                JSONObject click = new JSONObject();
                click.put("type", "dialog");
                click.put("permission", "table_page");
                String lx = "";
                if (data.containsKey("lx")) {
                    lx = RIUtil.dicts.get(data.getString("xl")).getString("dict_name");
                } else if (data.containsKey("gljb")) {
                    lx = RIUtil.dicts.get("182-" + data.getString("gljb")).getString("dict_name");
                }

                click.put("label", name + "_" + lx);
                JSONObject cols = new JSONObject();
                cols.put("xl", data.getString("xl"));
                cols.put("DMDM", dmid);
                JSONObject opt = GetOpts("/key_per", "get_zdry_mx2Tabel", cols);
                click.put("remark", opt);

                det.put("click", click);
                dets.add(det);
            }

            back.put("data", dets);

            return back;
        } catch (Exception ex) {

            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }


    }

    private JSONObject GetZdrySfsxGk(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        String unit = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
        } else {
            return ErrNo.set(null, 2, "缺少参数");
        }
        int type = RIUtil.dicts.get(unit).getInteger("type");

        if (type == 21 || type == 22 || type == 27) {
            unit = unit.substring(0, 4) + "00000000";
        } else if (type == 23 || type == 24 || type == 28) {
            unit = unit.substring(0, 6) + "000000";
        } else if (type == 25) {
            unit = unit.substring(0, 8) + "0000";
        } else {
            unit = unit;
        }

        String rynlfbs = "205-05000000,205-05003000,205-05004000,205-05006000";
        HashMap<String, String> dictNamdes = GetDictNames(rynlfbs);
        MysqlHelper my_143 = null;
        try {
            my_143 = new MysqlHelper("mysql_zxqc");
            String sql =
                    "select type,count from sta_zf_mid where type in ('" + rynlfbs.replace(",", "','") + "') " + "and"
                            + " " + "code='" + unit + "'";
            logger.warn(sql);
            List<JSONObject> list = my_143.query(sql);
            JSONArray dets = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String id = one.getString("type");
                String count = one.getString("count");

                JSONObject cols = new JSONObject();
                cols.put("unit", unit);
                cols.put("dict_id", id);

                JSONObject click = new JSONObject();
                click.put("type", "map");
                click.put("opt", GetOpts("/screen_map", "get_zdry", cols));
                click.put("url", "/screen_map");


                JSONObject det = new JSONObject();
                det.put("title", dictNamdes.get(id));
                if (dictNamdes.get(id).contains("重点人员")) {
                    det.put("icon", "zhongdianImg");
                } else if (dictNamdes.get(id).contains("个人极端")) {
                    det.put("icon", "gerenImg");
                } else if (dictNamdes.get(id).contains("精神障碍")) {
                    det.put("icon", "jingshenImg");
                } else if (dictNamdes.get(id).contains("刑事前科")) {
                    det.put("icon", "xingshiImg");
                } else {
                    det.put("icon", "zhongdianImg");
                }

                det.put("count", count);
                det.put("click", click);
                dets.add(det);


            }
            back.put("data", dets);
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            my_143.close();
        }


    }

    private JSONObject GetRKHJNLFB(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        String unit = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
        } else {
            return ErrNo.set(null, 2, "缺少参数");
        }
        int type = RIUtil.dicts.get(unit).getInteger("type");

        if (type == 21 || type == 22 || type == 27) {
            unit = unit.substring(0, 4) + "00000000";
        } else if (type == 23 || type == 24 || type == 28) {
            unit = unit.substring(0, 6) + "000000";
        } else if (type == 25) {
            unit = unit.substring(0, 8) + "0000";
        } else {
            unit = unit;
        }

        String rynlfbs = "205-01001001,205-01001002,205-01001003,205-01001004";
        HashMap<String, String> dictNamdes = GetDictNames(rynlfbs);
        MysqlHelper my_143 = null;
        try {
            my_143 = new MysqlHelper("mysql_zxqc");
            String sql =
                    "select type,count from sta_zf_mid where type in ('" + rynlfbs.replace(",", "','") + "') " + "and"
                            + " " + "code='" + unit + "'";
            logger.warn(sql);
            List<JSONObject> list = my_143.query(sql);
            JSONArray dets = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String id = one.getString("type");
                String count = one.getString("count");

                JSONObject cols = new JSONObject();
                cols.put("unit", unit);
                cols.put("dict_id", id);

                JSONObject click = new JSONObject();
                click.put("type", "map");
                click.put("opt", GetOpts("/screen_map", "get_rk", cols));
                click.put("url", "/screen_map");


                JSONObject det = new JSONObject();
                det.put("name", dictNamdes.get(id));
                det.put("count", count);
                det.put("click", click);
                dets.add(det);


            }
            back.put("data", dets);
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            my_143.close();
        }


    }

    private HashMap<String, String> GetDictNames(String id) {
        HashMap<String, String> rets = new HashMap<>();
        String[] ids = id.split(",");
        for (int i = 0; i < ids.length; i++) {
            String d = ids[i];
            String name = RIUtil.dicts.get(d).getString("dict_name");
            rets.put(d, name);


        }
        return rets;
    }

    private JSONArray GetHeads(String headN, String hdKeys) {
        JSONArray rets = new JSONArray();
        String[] hdns = headN.split(",");
        String[] hdks = hdKeys.split(",");

        for (int i = 0; i < hdks.length; i++) {
            JSONObject det = new JSONObject();
            String key = hdks[i];
            String value = hdns[i];
            det.put("key", key);
            det.put("value", value);
            rets.add(det);

        }
        return rets;
    }

    private JSONObject GetOpts(String url, String opt, JSONObject cols) {

        JSONObject opts = new JSONObject();
        opts.put("url", url);
        opts.put("opt", opt);
        opts.put("opt_user", "$opt_user$");


        opts.putAll(cols);
        return opts;
    }
}
