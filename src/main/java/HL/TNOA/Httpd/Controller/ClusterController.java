package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
public class ClusterController {
    private static Logger logger = LoggerFactory.getLogger(ClusterController.class);
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/cluster"})
    @PassToken
    public JSONObject get_cluster(TNOAHttpRequest request) throws Exception {
        logger.warn("cluster--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_cluster")) {
                return getCluster(data);
            } else if (opt.equals("create_cluster")) {
                return createCluster(data, request.getRemoteAddr());
            } else if (opt.equals("update_cluster")) {
                return updateCluster(data, request.getRemoteAddr());
            } else if (opt.equals("delete_cluster")) {
                return deleteCluster(data, request.getRemoteAddr());
            } else {
                return ErrNo.set(506009);
            }
        } else {
            return ErrNo.set(506009);
        }
    }

    //******CREATE*******
    private JSONObject createCluster(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String name = "";
            String role_id = "";
            String role_user = "";
            String users = "";
            String level = "4";
            String org = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "0";
            String remark = "";
            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");
            } else {
                return ErrNo.set(506002);
            }
            if (data.containsKey("role_id") && data.getString("role_id").length() > 0) {
                role_id = data.getString("role_id");
            } else {

            }
            if (data.containsKey("role_user") && data.getString("role_user").length() > 0) {
                role_user = data.getString("role_user");
            } else {

            }
            if (data.containsKey("users") && data.getString("users").length() > 0) {
                users = data.getString("users");
                users = GetUsers(users);
            } else {
                users = "";
            }
            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                return ErrNo.set(506002);
            }
            String sqls =
                    "insert cluster (name,role_id,role_user,users,level,org,create_user,create_time,isdelete," +
                            "remark) " + "values ('" + name + "','" + role_id + "','" + role_user + "','" + users +
                            "','" + level + "','" + org + "','" + create_user + "','" + create_time + "','" + isdelete + "','" + remark + "')";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建圈层群组", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(506001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private String GetUsers(String users) {
        String ids = "";
        String[] us = users.split(",");
        for (int i = 0; i < us.length; i++) {
            String user_id = us[i];
            JSONObject one = RIUtil.users.get(user_id);
            String name = one.getString("name");
            ids = ids + us[i] + "|" + name + ",";

        }
        return ids;
    }

    //******GET*******
    private JSONObject getCluster(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String name = "";

            String unit = "";
            String create_user = "";
            String usql = "";
            String csql = "";
            String lsql = "";
            String level = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");
                sql = sql + " name like '%" + name + "%' and ";
            }

            if (data.containsKey("level") && data.getString("level").length() > 0) {
                level = data.getString("level");
                if (level.equals("1")) {
                    lsql = " level in (1,2,3)  and ";
                } else {
                    lsql = " level ='4' and ";
                }

            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");


                JSONObject done = RIUtil.dicts.get(unit);
                int type = done.getInteger("type");
                if (type == 21 || type == 22 || type == 27) {
                    usql = usql + "  (org like '%3204%' and level=1)  ";
                } else if (type == 23 || type == 28 || type == 24) {
                    usql = usql + " ( org  like '%" + unit.substring(0, 6) + "%' and level=2 )  ";
                } else {
                    usql = usql + " (org  like '%" + unit.substring(0, 8) + "%' and level=3 ) ";
                }


            } else {

            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
                csql = csql + " ( create_user='" + create_user + "' and level=4 )  ";
            } else {
                return ErrNo.set(506002);
            }

            if (unit.length() > 0) {
                if (level.equals("1")) {
                    sql = sql + usql + " and ";
                } else if (level.equals("4")) {
                    sql = sql + csql + " and ";
                } else {
                    sql = sql + " ( " + usql + " or " + csql + " ) and ";
                }
            } else {
                sql = sql + lsql;
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select * from cluster where 1=1 and " + sql + " isdelete=0  limit " + limit + " offset " + limit * (page - 1);
            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select count(id) as count from cluster where 1=1 and " + sql + " isdelete=0; ";
                back.put("count", mysql.query_count(sqls));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(506005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            one.put("create_user", RIUtil.users.get(one.getString("create_user")));


            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private JSONObject updateCluster(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String name = "";
            String role_id = "";
            String role_user = "";
            String users = "";
            String remark = "";
            String opt_user = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");


            } else {
                return ErrNo.set(506004);
            }

            String s = "select level from cluster where id='" + id + "'";
            String lev = mysql.query_one(s, "level");

            if (data.containsKey("name") && data.getString("name").length() > 0 && lev.equals("4")) {
                name = data.getString("name");
                sql = sql + " name='" + name + "' , ";
            }
            if (data.containsKey("remark") && lev.equals("4")) {
                remark = data.getString("remark");
                sql = sql + " remark='" + remark + "' , ";
            }

            if (data.containsKey("users")) {
                users = data.getString("users");
                if (users.length() > 2) {
                    users = GetUsers(users);
                } else {
                    users = "";
                }
                sql = sql + " users='" + users + "' , ";
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(506004);
            }
            String sqls = "update cluster set " + sql + " isdelete=0 where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新圈层群组", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(506003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private JSONObject deleteCluster(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                String s = "select level from cluster where id='" + id + "'";
                String lev = mysql.query_one(s, "level");
                if (lev.equals("4")) {

                } else {
                    return ErrNo.set(506008);
                }

            } else {
                return ErrNo.set(506008);
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(506008);
            }

            String sqls =
                    "update cluster set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除圈层群组", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(506007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

}
