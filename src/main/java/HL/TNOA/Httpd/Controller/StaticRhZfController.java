package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class StaticRhZfController {
    private static Logger logger = LoggerFactory.getLogger(StaticRhZfController.class);

    @RequestMapping(method = {RequestMethod.POST}, path = {"/static_rhzf"})
    public JSONObject get_examine(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        logger.warn("data---->" + data);
        String opt = "";

        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_static_rhzf")) {
                return getStaticRhZf(data);
            } else if (opt.equals("get_static_det")) {
                return getStaticDet(data);
            } else {
                return ErrNo.set(490001);
            }
        } else {
            return ErrNo.set(490001);
        }


    }

    private static JSONObject getStaticDet(JSONObject data) {
        InfoModelHelper mysql = null;
        OracleHelper ora_gl = null;
        OracleHelper ora_hl = null;
        JSONObject back = ErrNo.set(0);


        try {
            mysql = InfoModelPool.getModel();
            ora_gl = new OracleHelper("ora_bk_gl");
            ora_hl = new OracleHelper("ora_hl");


            //组织机构代码
            String time = "";
            String org = "";
            String label = "";

            String sql = "";
            String sqls = "";

            // 2024.01.16 重点人员换表
            String zdrySql = "";

            int limit = 20;
            int page = 1;

            if (data.containsKey("time") && data.getString("time").length() > 0) {
                time = data.getString("time");
            } else {
                return ErrNo.set(480003);
            }

            if (data.containsKey("label") && data.getString("label").length() > 0) {
                label = data.getString("label");
            } else {
                return ErrNo.set(480003);
            }

            String nextMonth = RIUtil.getNextMonth(time);


            String bhg_data = "";
            String bhgs = "";
            if (data.containsKey("bhg_data") && data.getString("bhg_data").length() > 0) {
                bhg_data = data.getString("bhg_data");
                logger.warn("bhg_data ======>" + bhg_data);
                String[] split = bhg_data.split(",");
                for (int i = 0; i < split.length; i++) {
                    String rid = split[i];
                    if (rid.length() > 4) {
                        bhgs = bhgs + "'" + rid + "',";
                    }
                }
                if (bhgs.length() > 0) {
                    bhgs = bhgs.substring(0, bhgs.length() - 1);
                    logger.warn("bhg_spilt ======>" + bhgs);
                }


            }

            if (data.containsKey("org") && data.getString("org").length() > 0) {
                org = data.getString("org");
                JSONObject object = RIUtil.dicts.get(org);
                String type = object.getString("type");

                if ("23".equals(type)) {
                    sql = sql + " and SUBSTR(SSZRQ,1,6) = '" + org.substring(0, 6) + "'  ";

                    zdrySql = " and substr(rhzf.czdwdm,1,6) = '" + org.substring(0, 6) + "'  ";

                } else if ("25".equals(type)) {
                    sql = sql + " and SUBSTR(SSZRQ,1,8) = '" + org.substring(0, 8) + "'  ";
                    zdrySql = " and substr(rhzf.czdwdm,1,8) = '" + org.substring(0, 8) + "'  ";
                } else if ("26".equals(type)) {
                    sql = sql + " and SSZRQ = '" + org + "'  ";
                    zdrySql = " and rhzf.czdwdm = '" + org.substring(0, 8) + "'  ";


                } else {
                    return ErrNo.set(480003);
                }

            } else {
                return ErrNo.set(480003);
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }

            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            List<JSONObject> list = new ArrayList<>();
            int count = 0;

            String paging =
                    "ORDER BY CZSJ DESC  OFFSET " + (page - 1) * limit + " " + "ROWS FETCH NEXT" + " " + limit + " " + "ROWS " + "ONLY";

            String gaussPage = " order by rhzf.czsj desc limit " + limit + " offset " + limit * (page - 1);


            if ("zdry_gk_rs".equals(label)) { //030000

                sqls = "select * from V_zdry_rhzf  " + "where CZSJ>=TO_DATE('" + time + "-01 00:00:00'," + "'yyyy-mm" + "-dd hh24:mi:ss') " + "and  CZSJ<=TO_DATE('" + nextMonth + "','yyyy-mm-dd " + "hh24:mi:ss') " + "and ZDRYGLJB='030000' " + sql + paging;
                logger.warn(sqls);
                list = ora_hl.query(sqls);

                sqls = "select count(1) as count from V_zdry_rhzf  " + "where CZSJ>=TO_DATE('" + time + "-01 00:00:00"
                        + "'," + "'yyyy-mm-dd hh24:mi:ss') " + "and  CZSJ<=TO_DATE('" + nextMonth + "','yyyy-mm-dd " + "hh24:mi:ss') " + "and ZDRYGLJB='030000' " + sql;
                count = ora_hl.query_count(sqls);


            } else if ("zdry_gk_hs".equals(label)) { //030000

                sqls = "select * from V_zdry_rhzf  " + "where CZSJ>=TO_DATE('" + time + "-01 00:00:00'," + "'yyyy-mm" + "-dd hh24:mi:ss') " + "and  CZSJ<=TO_DATE('" + nextMonth + "','yyyy-mm-dd " + "hh24:mi:ss') " + "and ZDRYGLJB='030000' " + sql + paging;
                logger.warn(sqls);
                list = ora_hl.query(sqls);
                HashMap<String, JSONObject> rss = new HashMap<>();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String dzbm = one.getString("DZBM");
                    one.remove("GMSFHM");
                    one.remove("XM");

                    rss.put(dzbm, one);
                }
                count = rss.size();

                List<JSONObject> ll = new ArrayList<>();
                for (Map.Entry<String, JSONObject> one : rss.entrySet()) {
                    JSONObject o = one.getValue();

                    ll.add(o);
                }

                list = ListAsLimit(ll, page, limit, "");

            } else if ("zdry_gz_rs".equals(label)) {
                sqls = "select * from V_zdry_rhzf  " + "where CZSJ>=TO_DATE('" + time + "-01 00:00:00'," + "'yyyy-mm" + "-dd hh24:mi:ss') " + "and  CZSJ<=TO_DATE('" + nextMonth + "','yyyy-mm-dd " + "hh24:mi:ss') " + "and ZDRYGLJB='040000' " + sql + paging;
                logger.warn(sqls);
                list = ora_hl.query(sqls);

                sqls = "select count(1) as count from V_zdry_rhzf  " + "where CZSJ>=TO_DATE('" + time + "-01 00:00:00"
                        + "'," + "'yyyy-mm-dd hh24:mi:ss') " + "and  CZSJ<=TO_DATE('" + nextMonth + "','yyyy-mm-dd " + "hh24:mi:ss') " + "and ZDRYGLJB='040000' " + sql;
                count = ora_hl.query_count(sqls);


            } else if ("zdry_gz_hs".equals(label)) {
                sqls = "select * from V_zdry_rhzf  " + "where CZSJ>=TO_DATE('" + time + "-01 00:00:00'," + "'yyyy-mm" + "-dd hh24:mi:ss') " + "and  CZSJ<=TO_DATE('" + nextMonth + "','yyyy-mm-dd " + "hh24:mi:ss') " + "and ZDRYGLJB='040000' " + sql + paging;
                logger.warn(sqls);
                list = ora_hl.query(sqls);
                HashMap<String, JSONObject> rss = new HashMap<>();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String dzbm = one.getString("DZBM");
                    one.remove("GMSFHM");
                    one.remove("XM");

                    rss.put(dzbm, one);
                }
                count = rss.size();

                List<JSONObject> ll = new ArrayList<>();
                for (Map.Entry<String, JSONObject> one : rss.entrySet()) {
                    JSONObject o = one.getValue();

                    ll.add(o);
                }

                list = ListAsLimit(ll, page, limit, "");


            } else if ("czf_hs".equals(label)) {
                sqls = "select * from CZQJ_YBDS.V_RH_FW_JM " + "where CZSJ>=TO_DATE('" + time + "-01 00:00:00'," +
                        "'yyyy-mm-dd hh24:mi:ss') " + "and  CZSJ<=TO_DATE('" + nextMonth + "','yyyy-mm-dd " + "hh24" + ":mi:ss') " + "and (SFQZ=2 and SFHZF =2  and CZJZ_PDBS=1) " + sql + " order by CZSJ desc";
                logger.warn(sqls);
                list = ora_gl.query(sqls);

                HashMap<String, JSONObject> rss = new HashMap<>();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String dzbm = one.getString("DZBM");
                    one.remove("GMSFHM");
                    one.remove("XM");

                    rss.put(dzbm, one);
                }
                count = rss.size();

                List<JSONObject> ll = new ArrayList<>();
                for (Map.Entry<String, JSONObject> one : rss.entrySet()) {
                    JSONObject o = one.getValue();

                    ll.add(o);
                }

                list = ListAsLimit(ll, page, limit, "");

            } else if ("czf_rs".equals(label)) {

                sqls = "select * from CZQJ_YBDS.V_RH_FW_JM " + "where CZSJ>=TO_DATE('" + time + "-01 00:00:00'," +
                        "'yyyy-mm-dd hh24:mi:ss') " + "and  CZSJ<=TO_DATE('" + nextMonth + "','yyyy-mm-dd " + "hh24" + ":mi:ss') " + "and (SFQZ=2 and SFHZF =2  and CZJZ_PDBS=1) " + sql + " order by CZSJ desc";
                logger.warn(sqls);
                list = ora_gl.query(sqls);
                list = GroupByListAsLimit(list, "SFZH", page, limit);


                sqls = "select count(1) as count from CZQJ_YBDS.V_RH_FW_JM " + "where CZSJ>=TO_DATE('" + time + "-01 "
                        + "00:00:00'," + "'yyyy-mm-dd hh24:mi:ss') " + "and  CZSJ<=TO_DATE('" + nextMonth + "','yyyy" + "-mm-dd " + "hh24:mi:ss') " + "and (SFQZ=2 and SFHZF =2  and CZJZ_PDBS=1)  " + sql + " GROUP " + "BY SFZH";
                count = ora_gl.query(sqls).size();

            } else if ("qzhz_hs".equals(label)) {

                sqls = "select * from CZQJ_YBDS.V_RH_FW_JM  " + "where CZSJ>=TO_DATE('" + time + "-01 00:00:00'," +
                        "'yyyy-mm-dd hh24:mi:ss') " + "and  CZSJ<=TO_DATE('" + nextMonth + "','yyyy-mm-dd " + "hh24" + ":mi:ss') " + "and (SFQZ=1 or SFHZF =1) " + sql + " order by CZSJ desc";
                logger.warn(sqls);
                list = ora_gl.query(sqls);

                HashMap<String, JSONObject> rss = new HashMap<>();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String dzbm = one.getString("DZBM");
                    one.remove("GMSFHM");
                    one.remove("XM");

                    rss.put(dzbm, one);
                }
                count = rss.size();

                List<JSONObject> ll = new ArrayList<>();
                for (Map.Entry<String, JSONObject> one : rss.entrySet()) {
                    JSONObject o = one.getValue();

                    ll.add(o);
                }

                list = ListAsLimit(ll, page, limit, "");

            } else if ("qzhz_rs".equals(label)) {

                sqls = "select * from CZQJ_YBDS.V_RH_FW_JM  " + "where CZSJ>=TO_DATE('" + time + "-01 00:00:00'," +
                        "'yyyy-mm-dd hh24:mi:ss') " + "and  CZSJ<=TO_DATE('" + nextMonth + "','yyyy-mm-dd " + "hh24" + ":mi:ss') " + "and (SFQZ=1 or SFHZF =1) " + sql + " order by CZSJ desc";
                logger.warn(sqls);
                list = ora_gl.query(sqls);
                list = GroupByListAsLimit(list, "SFZH", page, limit);


                sqls = "select count(1) as count from CZQJ_YBDS.V_RH_FW_JM  " + "where CZSJ>=TO_DATE('" + time + "-01"
                        + " 00:00:00'," + "'yyyy-mm-dd hh24:mi:ss') " + "and  CZSJ<=TO_DATE('" + nextMonth + "','yyyy"
                        + "-mm-dd " + "hh24:mi:ss') " + "and (SFQZ=1 or SFHZF =1) " + sql + " group by SFZH";
                count = ora_gl.query(sqls).size();

            } else if ("qt_hs".equals(label)) {

                sqls = "select * from CZQJ_YBDS.V_RH_FW_JM " + "where CZSJ>=TO_DATE('" + time + "-01 00:00:00'," +
                        "'yyyy-mm-dd hh24:mi:ss') " + "and  CZSJ<=TO_DATE('" + nextMonth + "','yyyy-mm-dd " + "hh24" + ":mi:ss') " + "and CZJZ_PDBS=0 " + sql + " order by CZSJ desc";
                logger.warn(sqls);
                list = ora_gl.query(sqls);
                HashMap<String, JSONObject> rss = new HashMap<>();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String dzbm = one.getString("DZBM");
                    one.remove("GMSFHM");
                    one.remove("XM");

                    rss.put(dzbm, one);
                }
                count = rss.size();

                List<JSONObject> ll = new ArrayList<>();
                for (Map.Entry<String, JSONObject> one : rss.entrySet()) {
                    JSONObject o = one.getValue();

                    ll.add(o);
                }

                list = ListAsLimit(ll, page, limit, "");

            } else if ("qt_rs".equals(label)) {

                sqls = "select * from CZQJ_YBDS.V_RH_FW_JM " + "where CZSJ>=TO_DATE('" + time + "-01 00:00:00'," +
                        "'yyyy-mm-dd hh24:mi:ss') " + "and  CZSJ<=TO_DATE('" + nextMonth + "','yyyy-mm-dd " + "hh24" + ":mi:ss') " + "and CZJZ_PDBS=0 " + sql + " order by CZSJ desc";
                logger.warn(sqls);
                list = ora_gl.query(sqls);
                list = GroupByListAsLimit(list, "SFZH", page, limit);

                sqls = "select COUNT(1) AS COUNT from CZQJ_YBDS.V_RH_FW_JM " + "where CZSJ>=TO_DATE('" + time + "-01 "
                        + "00:00:00'," + "'yyyy-mm-dd hh24:mi:ss') " + "and  CZSJ<=TO_DATE('" + nextMonth + "','yyyy" + "-mm-dd " + "hh24:mi:ss') " + "and CZJZ_PDBS=0 " + sql + " GROUP BY SFZH";
                count = ora_gl.query(sqls).size();

            } else if ("total".equals(label)) {

                sqls = "select DZXX_ID AS DZBM,DZXX_DZMS AS DZ from CZQJ_YBDS.YW_SYFW " + "where FWZT=0 " + sql + paging;
                logger.warn(sqls);
                list = ora_gl.query(sqls);

                sqls = "select ID from CZQJ_YBDS.YW_SYFW " + "where FWZT=0 " + sql;
                count = ora_hl.query_count(sqls);

            } else if ("wsms".equals(label)) {
                sqls = "select DZXX_ID AS DZBM,DZXX_DZMS AS DZ,ID from CZQJ_YBDS.YW_SYFW " + "where FWZT=0 " + sql;
                logger.warn(sqls);
                list = ora_gl.query(sqls);


                sqls = "select SYFW_ID from CZQJ_YBDS.V_RHZF_SYFW " + "where CZSJ>=TO_DATE('" + time.substring(0, 4) + "-01" + "-01 00:00:00','yyyy-mm-dd hh24:mi:ss') " + sql;
                logger.warn(sqls);
                List<JSONObject> sms = ora_gl.query(sqls);
                HashMap<String, String> smss = new HashMap<>();
                for (int i = 0; i < sms.size(); i++) {
                    smss.put(sms.get(i).getString("SYFW_ID"), "");

                }
                List<JSONObject> ws = new ArrayList<>();
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String id = one.getString("ID");
                    if (!smss.containsKey(id)) {
                        ws.add(one);
                    }

                }

                count = ws.size();
                list = ListAsLimit(ws, page, limit, "CZSJ");

            } else if ("sms".equals(label)) {

                sqls = "select * from CZQJ_YBDS.V_RHZF_SYFW " + "where CZSJ>=TO_DATE('" + time.substring(0, 4) + "-01"
                        + "-01 00:00:00','yyyy-mm-dd hh24:mi:ss') " + sql;
                logger.warn(sqls);
                list = ora_gl.query(sqls);
                list = GroupByListAsLimit(list, "SYFW_ID", page, limit);

                sqls = "select count(1) as count from CZQJ_YBDS.V_RHZF_SYFW where CZSJ>=TO_DATE('" + time.substring(0
                        , 4) + "-01-01 " + "00:00:00','yyyy-mm-dd hh24:mi:ss') " + sql + " group by SYFW_ID";
                count = ora_gl.query(sqls).size();
            } else if ("bhg".equals(label)) {

                if (bhgs.length() > 4) {
                    sqls = "SELECT DZBM,DZMC AS DZ FROM CZQJ_YBDS.YW_RHZF WHERE ID IN (" + bhgs + ") " + paging;
                    logger.warn(sqls);
                    list = ora_gl.query(sqls);

//                sqls = "select * from CZQJ_YBDS.V_RHZF_SYFW where CZSJ>=TO_DATE('"+time.substring(0,4)+"-01-01
//                00:00:00','yyyy-mm-dd hh24:mi:ss') " + sql;
                    sqls = "SELECT COUNT(1) AS COUNT FROM CZQJ_YBDS.YW_RHZF WHERE ID IN (" + bhgs + ") ";
                    count = ora_hl.query_count(sqls);
                }
            }

            back.put("data", list);
            back.put("count", count);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
            ora_gl.close();
            ora_hl.close();
        }
        return back;
    }

    private static List<JSONObject> GroupByListAsLimit(List<JSONObject> list, String col, int page, int limit) {
        List<JSONObject> back = new ArrayList<>();
        HashMap<String, JSONObject> dets = new HashMap<>();
        int count = list.size();

        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String id = one.getString(col);
            if (!dets.containsKey(id)) {
                dets.put(id, one);
            }
        }
        int start = (page - 1) * limit;
        int end = start + limit;
        if (end > count) {
            end = count;
        }
        logger.warn(start + "-->" + end);

        List<JSONObject> rets = new ArrayList<JSONObject>();
        for (Map.Entry<String, JSONObject> det : dets.entrySet()) {

            JSONObject one = det.getValue();
            rets.add(one);

        }

        Collections.sort(rets, (JSONObject o1, JSONObject o2) -> {

            String czsja = o1.getString("CZSJ");
            long a = 0;
            try {
                a = RIUtil.dateToStamp(czsja);
            } catch (Exception e) {
                e.printStackTrace();
            }
            String czsjb = o2.getString("CZSJ");
            long b = 0;
            try {
                b = RIUtil.dateToStamp(czsjb);
            } catch (Exception e) {
                e.printStackTrace();
            }

            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (a < b) {  //降序排列，升序改成a>b
                return 1;
            } else if (a == b) {
                return 0;
            } else {
                return -1;
            }
        });

        for (int m = start; m < end; m++) {
            try {
                JSONObject one = rets.get(m);
                back.add(one);
            } catch (Exception ex) {

            }
        }
        return back;

    }

    private static List<JSONObject> ListAsLimit(List<JSONObject> list, int page, int limit, String order) {
        List<JSONObject> back = new ArrayList<>();
        int count = list.size();
        int start = (page - 1) * limit;
        int end = start + limit;
        if (end > count) {
            end = count;
        }
        logger.warn(start + "-->" + end);
        // logger.warn(list.get(0).toString());


        for (int m = start; m < end; m++) {
            try {
                JSONObject one = list.get(m);
                back.add(one);
            } catch (Exception ex) {

            }
        }
        return back;

    }

    private static JSONObject getStaticRhZf(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            //组织机构代码
            String time = "";
            String level = ""; // 1.责任区 2.派出所 3.分局
            String org = "";
            String sql = "";
            String sqls = "";
            int isExp = 0;
            int limit = 20;
            int page = 1;

            if (data.containsKey("level") && data.getString("level").length() > 0) {
                level = data.getString("level");
            } else {
                return ErrNo.set(480003);
            }

            if (data.containsKey("org") && data.getString("org").length() > 0) {
                org = data.getString("org");
                String type = RIUtil.dicts.get(org).getString("type");
                if ("21".equals(type) || "22".equals(type) || "27".equals(type)) {

                } else if ("23".equals(type) || "24".equals(type)) {
//
                    sql = sql + "SUBSTR(code from 1 for 6) ='" + org.substring(0, 6) + "' and ";

                } else if ("25".equals(type)) {

                    sql = sql + "father_id = '" + org + "' and ";

                } else if ("26".equals(type)) {

                    sql = sql + "code = '" + org + "' and ";

                } else {
                    return ErrNo.set(480003);
                }

            }

            if (data.containsKey("time") && data.getString("time").length() > 0) {
                time = data.getString("time");
                String year = time.substring(0, 4);
                String month = time.substring(4, 6);

                sql = sql + " year = '" + year + "' and ";
                sql = sql + " month = '" + month + "' and ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getInteger("isExp");
            }

            if ("1".equals(level)) {
                sqls = "select * from static_rhzf where " + sql + " 1=1 order by code " + " limit " + limit + " " +
                        "offset " + limit * (page - 1);
                logger.warn(sqls);
                List<JSONObject> list = mysql.query(sqls);

                if (list.size() > 0) {
                    for (int i = 0; i < list.size(); i++) {
                        JSONObject obj = list.get(i);
                        String code = obj.getString("code");
                        String father_id = obj.getString("father_id");

                        try {
                            obj.put("code_name", RIUtil.dicts.get(code).getString("dict_name"));
                            obj.put("father_name", RIUtil.dicts.get(father_id).getString("dict_name"));
                        } catch (Exception e) {
                            obj.put("code_name", "");
                            obj.put("father_name", "");
                        }
                    }
                    back.put("data", list);
                    sqls = "select * from static_rhzf where " + sql + " 1=1 ";
                    List<JSONObject> query = mysql.query(sqls);
                    back.put("count", query.size());
                } else {
                    back.put("data", list);
                    back.put("count", 0);
                }
                back.put("file_id", -1);
                logger.warn(String.valueOf(isExp));
                if (isExp == 1) {
                    int file_id = exportTemplate(list);
                    back.put("file_id", file_id);
                }
            } else if ("2".equals(level)) {

                sqls = "select father_id,count(father_id) as count,sum(total) as total,sum(sms) as sms,sum(wsms) as " + " wsms,sum(present) as present,sum(qzhz_hs) as qzhz_hs," + " sum(qzhz_rs) as qzhz_rs,sum(czf_hs) as czf_hs,sum(czf_rs) as czf_rs,sum(zdry_gz_hs) as " + "zdry_gz_hs," + "sum(zdry_gz_rs) as zdry_gz_rs,sum(zdry_gk_hs) as zdry_gk_hs,sum(zdry_gk_rs) as zdry_gk_rs," + "sum(qt_hs) as qt_hs,sum(qt_rs) as qt_rs,sum(bhg) as bhg" + " from static_rhzf where " + sql + " 1=1 group by father_id order by father_id " + " limit " + limit + " offset " + limit * (page - 1);
                logger.warn(sqls);
                List<JSONObject> list = mysql.query(sqls);

                if (list.size() > 0) {
                    for (int i = 0; i < list.size(); i++) {
                        JSONObject obj = list.get(i);
                        String code = obj.getString("father_id");
                        JSONObject object = RIUtil.dicts.get(code);
                        String fid = object.getString("father_id");

                        String s = "select bhg_data from static_rhzf where father_id = '" + code + "'";
                        List<JSONObject> query = mysql.query(s);

                        String bhg = "";
                        for (int j = 0; j < query.size(); j++) {
                            JSONObject o = query.get(j);
                            String bhgData = o.getString("bhg_data");
                            bhg = bhg + bhgData;
                        }

                        obj.put("bhg_data", bhg);
                        obj.put("code", code);
                        obj.put("father_id", fid);
                        obj.put("code_name", object.getString("dict_name"));
                        obj.put("father_name", RIUtil.dicts.get(fid).getString("dict_name"));

                        String persent = obj.getString("present");
                        String count = obj.getString("count");
                        BigDecimal a = new BigDecimal(persent);
                        // logger.warn(a.toString());
                        BigDecimal b = new BigDecimal(count);
                        //  logger.warn(b.toString());
                        BigDecimal res = null;
                        if (b.intValue() != 0) {
                            res = a.divide(b, 3, RoundingMode.HALF_UP);
                        } else {
                            res = new BigDecimal(0);
                        }
                        //logger.warn(res.toString());
                        obj.put("present", res);
                    }
                    back.put("data", list);
                    sqls = "select father_id from static_rhzf where " + sql + " 1=1 group by father_id ";
                    List<JSONObject> query = mysql.query(sqls);
                    back.put("count", query.size());
                } else {
                    back.put("data", list);
                    back.put("count", 0);
                }
                back.put("file_id", -1);
                //   logger.warn(String.valueOf(isExp));
                if (isExp == 1) {

                    int file_id = exportTemplate(list);
                    back.put("file_id", file_id);

                }
            } else if ("3".equals(level)) {

                sqls = "select SUBSTR(code from 1 for 6) as code,count(SUBSTR(code from 1 for 6)) as count,sum(total)"
                        + " as total,sum(sms) as sms,sum(wsms) as wsms,sum(present) as present,sum(qzhz_hs) as " +
                        "qzhz_hs," + "sum(qzhz_rs) as qzhz_rs,sum(czf_hs) as czf_hs,sum(czf_rs) as czf_rs,sum" +
                        "(zdry_gz_hs) as " + "zdry_gz_hs,sum(zdry_gz_rs) as zdry_gz_rs," + "sum(zdry_gk_hs) as " +
                        "zdry_gk_hs,sum(zdry_gk_rs) as zdry_gk_rs,sum(qt_hs) as qt_hs,sum(qt_rs) " + "as qt_rs,sum" + "(bhg) as bhg, GROUP_CONCAT(bhg_data separator ',') as bhg_data  " + "from static_rhzf where " + sql + " 1=1 group by SUBSTR(code from 1 for 6) order by SUBSTR" + "(code from 1 for 6)";
                logger.warn(sqls);
                List<JSONObject> list = mysql.query(sqls);

                for (int i = 0; i < list.size(); i++) {
                    JSONObject obj = list.get(i);
                    // logger.warn(obj.toString());

                    String code = obj.getString("code") + "000000";
                    JSONObject object = RIUtil.dicts.get(code);
                    String code_name = object.getString("dict_name");
                    String fatherId = object.getString("father_id");

                    String s =
                            "select bhg_data from static_rhzf where SUBSTR(code from 1 for 6) = '" + obj.getString(
                                    "code") + "'";
                    List<JSONObject> query = mysql.query(s);

                    String bhg = "";
                    for (int j = 0; j < query.size(); j++) {
                        JSONObject o = query.get(j);
                        String bhgData = o.getString("bhg_data");
                        bhg = bhg + bhgData;
                    }

                    obj.put("bhg_data", bhg);
                    obj.put("code", code);
                    obj.put("father_id", fatherId);
                    obj.put("code_name", code_name);
                    obj.put("father_name", RIUtil.dicts.get(fatherId).getString("dict_name"));

                    String persent = obj.getString("present");
                    String count = obj.getString("count");
                    BigDecimal a = new BigDecimal(persent);
                    //      logger.warn(a.toString());
                    BigDecimal b = new BigDecimal(count);
                    //     logger.warn(b.toString());
                    BigDecimal res = null;
                    if (b.intValue() != 0) {
                        res = a.divide(b, 3, RoundingMode.HALF_UP);
                    } else {
                        res = new BigDecimal(0);
                    }
                    obj.put("present", res);
                }
                back.put("data", list);
                back.put("count", list.size());
                back.put("file_id", -1);
                //   logger.warn(String.valueOf(isExp));
                if (isExp == 1) {
                    int file_id = exportTemplate(list);
                    back.put("file_id", file_id);
                }
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static int exportTemplate(List<JSONObject> data) {

        String sql = "";
        String FileName = "入户走访模板_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        String filePath =
                new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

        String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;

        init(endPath);

        FileOutputStream fos = null;
        SXSSFWorkbook sxssfWorkbook = null;
        InfoModelHelper info = null;
        try {
            info = InfoModelPool.getModel();
            sxssfWorkbook = new SXSSFWorkbook();

            ArrayList<String> h = new ArrayList<>();
            ArrayList<String> h2 = new ArrayList<>();
            ArrayList<String> h3 = new ArrayList<>();
            h.add("组织机构名称");
            h2.add("");
            h3.add("组织机构名称");

            h.add("组织机构代码");
            h2.add("");
            h3.add("组织机构代码");

            h.add("上级机构代码");
            h2.add("");
            h3.add("上级机构代码");

            h.add("上级机构名称");
            h2.add("");
            h3.add("上级机构名称");

            h.add("本月");
            h2.add("走访管控级别重点人员");
            h3.add("户数");
            h.add("本月");
            h2.add("走访管控级别重点人员");
            h3.add("人数");

            h.add("本月");
            h2.add("走访关注级别重点人员");
            h3.add("户数");
            h.add("本月");
            h2.add("走访关注级别重点人员");
            h3.add("人数");

            h.add("本月");
            h2.add("走访群租房、合租房");
            h3.add("户数");
            h.add("本月");
            h2.add("走访群租房、合租房");
            h3.add("人数");

            h.add("本月");
            h2.add("走访其他出租房屋");
            h3.add("户数");
            h.add("本月");
            h2.add("走访其他出租房屋");
            h3.add("人数");

            h.add("本月");
            h2.add("走访上述情况以外房屋");
            h3.add("户数");
            h.add("本月");
            h2.add("走访上述情况以外房屋");
            h3.add("人数");

            h.add("本年");
            h2.add("不合格");
            h3.add("不合格");

            h.add("本年");
            h2.add("未上门地址数");
            h3.add("未上门地址数");

            h.add("本年");
            h2.add("上门数");
            h3.add("上门数");

            h.add("本年");
            h2.add("总数");
            h3.add("总数");

            h.add("本年");
            h2.add("占比");
            h3.add("占比");

            // 获取SXSSFWorkbook实例
            Sheet sheet = sxssfWorkbook.createSheet("入户走访");

            //************** 样式一 *******************//
            CellStyle cellStyle = sxssfWorkbook.createCellStyle();
            cellStyle.setWrapText(true);
            //对齐方式
            //设置水平对齐方式
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            //设置垂直对齐方式
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //设置边框
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);

            // 设置字体
            Font font = sxssfWorkbook.createFont();
            font.setFontName("Segoe UI");
            font.setFontHeightInPoints((short) 9);
            font.setBold(true);
            cellStyle.setFont(font);
            //************** 样式一 *******************//


            //************** 样式二 *******************//
            CellStyle cellStyle2 = sxssfWorkbook.createCellStyle();
            cellStyle2.setWrapText(true);
            //对齐方式
            //设置水平对齐方式
            cellStyle2.setAlignment(HorizontalAlignment.CENTER);
            //设置垂直对齐方式
            cellStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
            cellStyle2.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.000"));
            //设置边框
            cellStyle2.setBorderTop(BorderStyle.THIN);
            cellStyle2.setBorderRight(BorderStyle.THIN);
            cellStyle2.setBorderBottom(BorderStyle.THIN);
            cellStyle2.setBorderLeft(BorderStyle.THIN);
            //************** 样式二 *******************//

            // 冻结最左边的两列、冻结最上面的一行
            // 即：滚动横向滚动条时，左边的第一、二列固定不动;滚动纵向滚动条时，上面的第一行固定不动。I
            sheet.createFreezePane(0, 3);
            for (int l = 0; l < 2; l++) {
                sheet.setColumnWidth(l, 3600);
            }

            // 创建第二行,作为header表头 0为第一行
            Row header = sheet.createRow(0);
            header.setHeight((short) 800);
            for (int i = 0; i < h.size(); i++) {
                Cell cell = header.createCell(i);
                cell.setCellValue(h.get(i));
                cell.setCellStyle(cellStyle);
            }

            Row header2 = sheet.createRow(1);
            header2.setHeight((short) 800);
            for (int i = 0; i < h2.size(); i++) {
                Cell cell = header2.createCell(i);
                cell.setCellValue(h2.get(i));
                cell.setCellStyle(cellStyle);
            }

            Row header3 = sheet.createRow(2);
            header3.setHeight((short) 800);
            for (int i = 0; i < h3.size(); i++) {
                Cell cell = header3.createCell(i);
                cell.setCellValue(h3.get(i));
                cell.setCellStyle(cellStyle);
            }

            CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 2, 0, 0);
            sheet.addMergedRegionUnsafe(cellRangeAddress);

            CellRangeAddress cellRangeAddress2 = new CellRangeAddress(0, 2, 1, 1);
            sheet.addMergedRegionUnsafe(cellRangeAddress2);

            CellRangeAddress cellRangeAddress3 = new CellRangeAddress(0, 2, 2, 2);
            sheet.addMergedRegionUnsafe(cellRangeAddress3);

            CellRangeAddress cellRangeAddress4 = new CellRangeAddress(0, 2, 3, 3);
            sheet.addMergedRegionUnsafe(cellRangeAddress4);

            CellRangeAddress cellRangeAddress5 = new CellRangeAddress(0, 0, 4, 13);
            sheet.addMergedRegionUnsafe(cellRangeAddress5);

            CellRangeAddress cellRangeAddress11 = new CellRangeAddress(0, 0, 14, 18);
            sheet.addMergedRegionUnsafe(cellRangeAddress11);

            CellRangeAddress cellRangeAddress6 = new CellRangeAddress(1, 1, 4, 5);
            sheet.addMergedRegionUnsafe(cellRangeAddress6);

            CellRangeAddress cellRangeAddress7 = new CellRangeAddress(1, 1, 6, 7);
            sheet.addMergedRegionUnsafe(cellRangeAddress7);

            CellRangeAddress cellRangeAddress8 = new CellRangeAddress(1, 1, 8, 9);
            sheet.addMergedRegionUnsafe(cellRangeAddress8);

            CellRangeAddress cellRangeAddress9 = new CellRangeAddress(1, 1, 10, 11);
            sheet.addMergedRegionUnsafe(cellRangeAddress9);

            CellRangeAddress cellRangeAddress10 = new CellRangeAddress(1, 1, 12, 13);
            sheet.addMergedRegionUnsafe(cellRangeAddress10);

            CellRangeAddress cellRangeAddress12 = new CellRangeAddress(1, 2, 14, 14);
            sheet.addMergedRegionUnsafe(cellRangeAddress12);

            CellRangeAddress cellRangeAddress13 = new CellRangeAddress(1, 2, 15, 15);
            sheet.addMergedRegionUnsafe(cellRangeAddress13);

            CellRangeAddress cellRangeAddress14 = new CellRangeAddress(1, 2, 16, 16);
            sheet.addMergedRegionUnsafe(cellRangeAddress14);

            CellRangeAddress cellRangeAddress15 = new CellRangeAddress(1, 2, 17, 17);
            sheet.addMergedRegionUnsafe(cellRangeAddress15);

            CellRangeAddress cellRangeAddress16 = new CellRangeAddress(1, 2, 18, 18);
            sheet.addMergedRegionUnsafe(cellRangeAddress16);

            for (int rownum = 0; rownum < data.size(); rownum++) {
                Row row = sheet.createRow(rownum + 3);
                row.setHeight((short) 700);
                // 循环创建单元格
                JSONObject obj = data.get(rownum);
                String orgId = obj.getString("code");
                String orgName = obj.getString("code_name");
                String fatherId = obj.getString("father_id");
                String fatherName = obj.getString("father_name");
                String zdry_gk_hs = obj.getString("zdry_gk_hs");
                String zdry_gk_rs = obj.getString("zdry_gk_rs");
                String zdry_gz_hs = obj.getString("zdry_gz_hs");
                String zdry_gz_rs = obj.getString("zdry_gz_rs");
                String qzhz_hs = obj.getString("qzhz_hs");
                String qzhz_rs = obj.getString("qzhz_rs");
                String czf_hs = obj.getString("czf_hs");
                String czf_rs = obj.getString("czf_rs");
                String qt_hs = obj.getString("qt_hs");
                String qt_rs = obj.getString("qt_rs");
                String bhg = obj.getString("bhg");
                String wsms = obj.getString("wsms");
                String sms = obj.getString("sms");
                String total = obj.getString("total");
                String present = obj.getString("present");


                Cell cell0 = row.createCell(0);
                cell0.setCellValue(orgName);
                cell0.setCellStyle(cellStyle);

                Cell cell1 = row.createCell(1);
                cell1.setCellValue(orgId);
                cell1.setCellStyle(cellStyle);

                Cell cell2 = row.createCell(2);
                cell2.setCellValue(fatherId);
                cell2.setCellStyle(cellStyle);

                Cell cell3 = row.createCell(3);
                cell3.setCellValue(fatherName);
                cell3.setCellStyle(cellStyle);

                Cell cell4 = row.createCell(4);
                cell4.setCellValue(zdry_gk_hs);
                cell4.setCellStyle(cellStyle);

                Cell cell5 = row.createCell(5);
                cell5.setCellValue(zdry_gk_rs);
                cell5.setCellStyle(cellStyle);

                Cell cell6 = row.createCell(6);
                cell6.setCellValue(zdry_gz_hs);
                cell6.setCellStyle(cellStyle);

                Cell cell7 = row.createCell(7);
                cell7.setCellValue(zdry_gz_rs);
                cell7.setCellStyle(cellStyle);

                Cell cell8 = row.createCell(8);
                cell8.setCellValue(qzhz_hs);
                cell8.setCellStyle(cellStyle);

                Cell cell9 = row.createCell(9);
                cell9.setCellValue(qzhz_rs);
                cell9.setCellStyle(cellStyle);

                Cell cell10 = row.createCell(10);
                cell10.setCellValue(czf_hs);
                cell10.setCellStyle(cellStyle);

                Cell cell11 = row.createCell(11);
                cell11.setCellValue(czf_rs);
                cell11.setCellStyle(cellStyle);

                Cell cell12 = row.createCell(12);
                cell12.setCellValue(qt_hs);
                cell12.setCellStyle(cellStyle);

                Cell cell13 = row.createCell(13);
                cell13.setCellValue(qt_rs);
                cell13.setCellStyle(cellStyle);

                Cell cell14 = row.createCell(14);
                cell14.setCellValue(bhg);
                cell14.setCellStyle(cellStyle);

                Cell cell15 = row.createCell(15);
                cell15.setCellValue(wsms);
                cell15.setCellStyle(cellStyle);

                Cell cell16 = row.createCell(16);
                cell16.setCellValue(sms);
                cell16.setCellStyle(cellStyle);

                Cell cell17 = row.createCell(17);
                cell17.setCellValue(total);
                cell17.setCellStyle(cellStyle);

                Cell cell18 = row.createCell(18);
                cell18.setCellValue(present);
                cell18.setCellStyle(cellStyle);

            }

            fos = new FileOutputStream(endPath);
            sxssfWorkbook.write(fos);

            String sqls = "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "'," +
                    "'" + FileName + "',999)";
            logger.warn(sqls);
            info.update(sqls);
            sql = "select * from upload where file_name='" + FileName + "'";
            List<JSONObject> result = info.query(sql);
            JSONObject xJsonObject = result.get(0);
            int id = xJsonObject.getIntValue("id");

            logger.warn("id->" + id);
            //String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;


            String endPoint = "http://10.34.251.34:50101";
            String ak = "Q7D3OUVIRBEZEB4RWVSJ";
            String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
            String bucketName = "obs-qjjc-tyyh";
            ObsServer obsServ = new ObsServer();
            String obsFileName = "hl/" + filePath + FileName;
            System.out.println(obsFileName);
            boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName,
                    TNOAConf.get("file", "img_path") + filePath + FileName);
            logger.warn(obsFileName + "-->" + ret);
            return id;

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return -2;
        } finally {
            InfoModelPool.putModel(info);
            try {
                if (sxssfWorkbook != null) {
                    sxssfWorkbook.dispose();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private JSONObject exportTemplate2(List<JSONObject> data) {

        String sql = "";
        String FileName = "积分导入模板_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        String filePath =
                new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

        String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;

        init(endPath);

        FileOutputStream fos = null;
        SXSSFWorkbook sxssfWorkbook = null;
        InfoModelHelper info = null;
        try {
            info = InfoModelPool.getModel();
            sxssfWorkbook = new SXSSFWorkbook();


            ArrayList<String> h = new ArrayList<>();
            ArrayList<String> h2 = new ArrayList<>();
            h.add("本年");
            h.add("本月");
            h2.add("单位名称");
            h2.add("单位代码");
            h2.add("父单位代码");
            h2.add("父单位名称");

            h2.add("qzhz_hs");
            h2.add("qzhz_rs");
            h2.add("dwdm");
            h2.add("dwdm");


//            for (int i = 0; i < configs.size(); i++) {
//
//                JSONObject object = configs.getJSONObject(i);
//                String config_id = object.getString("id");
//                String kh_name = object.getString("kh_name");
//                String fullMark = object.getString("full_mark");
//
//                h.add(kh_name+"(" + fullMark + "分)");
//                h2.add(config_id);
//
//                h.add("");
//                h2.add("");
//
//                h3.add("得分");
//                h3.add("明细");
//
//            }

            // 获取SXSSFWorkbook实例
            Sheet sheet = sxssfWorkbook.createSheet("入户走访");

            //************** 样式一 *******************//
            CellStyle cellStyle = sxssfWorkbook.createCellStyle();
            cellStyle.setWrapText(true);
            //对齐方式
            //设置水平对齐方式
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
            //设置垂直对齐方式
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            //设置边框
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);

            // 设置字体
            Font font = sxssfWorkbook.createFont();
            font.setFontName("Segoe UI");
            font.setFontHeightInPoints((short) 9);
            font.setBold(true);
            cellStyle.setFont(font);
            //************** 样式一 *******************//


            //************** 样式二 *******************//
            CellStyle cellStyle2 = sxssfWorkbook.createCellStyle();
            cellStyle2.setWrapText(true);
            //对齐方式
            //设置水平对齐方式
            cellStyle2.setAlignment(HorizontalAlignment.CENTER);
            //设置垂直对齐方式
            cellStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
            cellStyle2.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.000"));
            //设置边框
            cellStyle2.setBorderTop(BorderStyle.THIN);
            cellStyle2.setBorderRight(BorderStyle.THIN);
            cellStyle2.setBorderBottom(BorderStyle.THIN);
            cellStyle2.setBorderLeft(BorderStyle.THIN);
            //************** 样式二 *******************//

            // 冻结最左边的两列、冻结最上面的一行
            // 即：滚动横向滚动条时，左边的第一、二列固定不动;滚动纵向滚动条时，上面的第一行固定不动。I
            sheet.createFreezePane(2, 3);
            for (int l = 0; l < 2; l++) {
                sheet.setColumnWidth(l, 3600);
            }

            // 创建第二行,作为header表头 0为第一行
            Row header = sheet.createRow(0);
            header.setHeight((short) 800);
            for (int i = 0; i < h.size(); i++) {
                Cell cell = header.createCell(i);
                cell.setCellValue(h.get(i));
                cell.setCellStyle(cellStyle);

            }

            for (int i = 2; i < h.size(); i = i + 2) {
                CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 0, i, i + 1);
                sheet.addMergedRegionUnsafe(cellRangeAddress);
            }

            Row header2 = sheet.createRow(1);
            header2.setHeight((short) 800);
            for (int i = 0; i < h2.size(); i++) {
                Cell cell = header2.createCell(i);
                cell.setCellValue(h2.get(i));
                cell.setCellStyle(cellStyle);
            }
            for (int i = 2; i < h2.size(); i = i + 2) {
                CellRangeAddress cellRangeAddress = new CellRangeAddress(1, 1, i, i + 1);
                sheet.addMergedRegionUnsafe(cellRangeAddress);
            }

            // 遍历创建行,导出数据
            for (int rownum = 0; rownum < data.size(); rownum++) {
                Row row = sheet.createRow(rownum + 3);
                row.setHeight((short) 700);
                // 循环创建单元格
                JSONObject org = data.get(rownum);
                String org_id = org.getString("id");
//                    String org_name = org.getString("dict_name");
                String org_name = org.getString("remark");

                Cell cell0 = row.createCell(0);
                cell0.setCellValue(org_name);
                cell0.setCellStyle(cellStyle);
                Cell cell1 = row.createCell(1);
                cell1.setCellValue(org_id);
                cell1.setCellStyle(cellStyle);

                for (int cellnum = 2; cellnum < h2.size(); cellnum++) {
                    Cell cell = row.createCell(cellnum);
                    cell.setCellStyle(cellStyle2);
                    sheet.setColumnWidth(cellnum, 1600);
                }

                for (int cellnum = 2; cellnum < h2.size(); cellnum = cellnum + 2) {
                    Cell cell = row.getCell(cellnum);

                    String id = h2.get(cellnum);
                    String point = RIUtil.kh_configs.get(id).getString("point");
                    String full_mark = RIUtil.kh_configs.get(id).getString("full_mark");
                    if (point.equals("2")) {
                        cell.setCellValue(full_mark);
                    } else {
                        cell.setCellValue("0");
                    }
                }
            }


            fos = new FileOutputStream(endPath);
            sxssfWorkbook.write(fos);

            String sqls =
                    "insert into upload (nas_id,file_path,file_name) values" + " ('1','" + filePath + "','" + FileName + "')";
            logger.warn(sqls);
            info.update(sqls);
            sql = "select * from upload where file_name='" + FileName + "'";
            List<JSONObject> result = info.query(sql);
            JSONObject xJsonObject = result.get(0);
            int id = xJsonObject.getIntValue("id");

            logger.warn("id->" + id);

            JSONObject back = new JSONObject();
            back.put("id", id);
            BashExecutor bash = new BashExecutor();
            String cmd = "cp -r " + endPath + " " + TNOAConf.get("file", "bk_path");
            logger.warn(cmd);
            bash.exec(cmd, -1, true);
            return back;

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(info);
            try {
                if (sxssfWorkbook != null) {
                    sxssfWorkbook.dispose();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void init(String path) {
        try {
            if (!new File("tmp").exists()) {
                new File("tmp").mkdir();
            }
            if (!new File(path).getParentFile().exists()) {
                new File(path).getParentFile().mkdirs();
            }
            if (!new File(path).exists()) {
                new File(path).createNewFile();
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }

    }


}
