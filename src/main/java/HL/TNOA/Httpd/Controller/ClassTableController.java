package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class ClassTableController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());

    @RequestMapping(method = {RequestMethod.POST}, path = {"/class_table"})
    public JSONObject get_class_table(TNOAHttpRequest request) throws Exception {

        String ip = request.getRemoteAddr();
        String opt = "";
        JSONObject data = request.getRequestParams();

        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if ("get_class_table".equals(opt)) {
                return getClaassTable(data);
            } else if ("create_class_table".equals(opt)) {
                logger.warn("class_table--->" + data.toString());
                return createClaassTable(data);
            } else if ("update_class_table".equals(opt)) {
                logger.warn("class_table--->" + data.toString());
                return updateClaassTable(data);
            } else if ("delete_class_table".equals(opt)) {
                return deleteClaassTable(data, ip);
            } else if ("change_class".equals(opt)) {
                logger.warn("class_table--->" + data.toString());
                return createChangeLog(data, ip);
            } else if ("get_change_class".equals(opt)) {
                return getChangeLog(data);
            } else if ("get_class_table_from_date".equals(opt)) {
                return getClassFromDate(data);
            } else if ("update_class_table_one".equals(opt)) {
                logger.warn("class_table--->" + data.toString());
                return updateClassTableOne(data);
            } else {
                return ErrNo.set(437009);
            }
        } else {
            return ErrNo.set(437009);
        }
    }

    private JSONObject updateClassTableOne(JSONObject data) {
        String id = "";
        String user_id = "";

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(435004);
        }
        if (data.containsKey("user_id")) {
            user_id = data.getString("user_id");
        } else {
            return ErrNo.set(435004);
        }

        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            JSONObject back = ErrNo.set(0);
            String sql =
                    "select decode(class_name,'1n2a3m4e') as class_name,user_id,class_id,group_id,on_time,off_time," +
                            "class_type,isCheck,date,att_result,unit,id from class_table where id='" + id +
                    "'";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                JSONObject one = list.get(0);
                String class_id = one.getString("class_id");
                String on_time = one.getString("on_time");
                String off_time = one.getString("off_time");
                String class_type = one.getString("class_type");
                String isCheck = one.getString("isCheck");
                String date = one.getString("date");
                String class_name = one.getString("class_name");
                String group_id = one.getString("group_id");

                sql = "update class_table set isdelete =2 where id='" + id + "'";
                mysql.update(sql);
                if (user_id.length() > 0) {
                    String users[] = user_id.split(",");
                    for (int i = 0; i < users.length; i++) {
                        String uid = users[i];
                        String sqls = "insert class_table (user_id,class_id,on_time,off_time,class_type,isCheck,date,"
                                + "isdelete,class_name,group_id)" + "values('" + uid + "','" + class_id + "','" + on_time + "','" + off_time + "','" + class_type + "','" + isCheck + "','" + date + "','" + 1 + "',encode('" + class_name + "','" + RIUtil.enName + "'),'" + group_id + "')";
                        // logger.warn(sqls);
                        mysql.update(sqls);
                    }
                }
            }
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(435003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private JSONObject getClassFromDate(JSONObject data) {
        String class_id = "";
        String start_date = "";

        if (data.containsKey("class_id") && data.getString("class_id").length() > 0) {
            class_id = data.getString("class_id");
        } else {
            return ErrNo.set(435006);
        }
        if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
            start_date = data.getString("start_date");
        } else {
            return ErrNo.set(435006);
        }

        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            JSONObject back = ErrNo.set(0);
            String sql = "select cycle from class where id='" + class_id + "'";
            String cycle = mysql.query_one(sql, "cycle");
            int cyc = Integer.parseInt(cycle);
            JSONArray datas = new JSONArray();
            if (cyc == 0) {
                sql = "select user_id from class_table where date='" + start_date + "' and class_id='" + class_id +
                        "' and isdelete=1";
                List<JSONObject> list = mysql.query(sql);

                JSONObject one = new JSONObject();
                one.put("date", start_date);
                one.put("classes", list);
                logger.warn(one.toString());
                datas.add(one);

            } else {
                for (int i = 0; i < cyc; i++) {
                    String date = RIUtil.GetNextDate(start_date, i);

                    sql = "select user_id from class_table where date='" + date + "' and class_id='" + class_id + "' "
                            + "and isdelete=1";
                    List<JSONObject> list = mysql.query(sql);
                    JSONObject one = new JSONObject();
                    one.put("date", date);
                    one.put("classes", list);

                    logger.warn(one.toString());
                    datas.add(one);

                }
            }

            back.put("data", datas);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(435005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    //******CREATE*******
    private JSONObject createClaassTable(JSONObject data) throws Exception {
        InfoModelHelper mysql = null;
        // JSONObject back = ErrNo.set(0);
        String class_id = "";
        String accepter = "";
        int class_length = 30;
        String create_user = "";
        String class_name = "";
        String unit = "";
        JSONArray datas = new JSONArray();
        String start_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());


        if (data.containsKey("class_id") && data.getString("class_id").length() > 0) {
            class_id = data.getString("class_id");

        } else {
            return ErrNo.set(437002);
        }
        if (data.containsKey("data") && data.getJSONArray("data").size() > 0) {
            String d = data.getString("data").replace("|", "\"");
            logger.warn(d);
            datas = JSONArray.parseArray(d);
        } else {
            return ErrNo.set(437002);
        }
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit").split(",")[0];
        }

        if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
            create_user = data.getString("create_user");
            if (unit.length() == 0) {
                unit = RIUtil.users.get(create_user).getString("unit").split(",")[0];
            }
        } else {
            return ErrNo.set(437002);
        }


        String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

        try {

            List<String> recoverList = new ArrayList<>();

            for (int i = 0; i < datas.size(); i++) {
                JSONObject one = datas.getJSONObject(i);
                logger.warn(one.toString());
                if (one.containsKey("accepter") && one.getString("accepter").length() > 0) {
                    accepter = one.getString("accepter");
                } else {
                    return ErrNo.set(437002);
                }

                if (one.containsKey("start_date") && one.getString("start_date").length() > 0) {
                    start_date = one.getString("start_date");
                }
                mysql = InfoModelPool.getModel();
                class_name = RIUtil.IdToName(class_id, mysql, "decode(class_name,'" + RIUtil.enName + "') as " +
                        "class_name", "class");
                HashMap<String, String> users = RIUtil.StringToList(accepter);
                for (Map.Entry<String, String> u : users.entrySet()) {
                    String user = u.getKey();
                    String sql =
                            "select count(id) as count from class_group where class_id='" + class_id + "' and " +
                                    "users like '%" + user + "%' and isdelete=1";
                    int count = mysql.query_count(sql);
                    if (count > 0) {
                        recoverList.add(RIUtil.IdToName(user, mysql, "name", "user") + " " + start_date + ":" + class_name + "重复");
                    }
                }

                if (recoverList.size() == 0) {
                    String group_id = String.valueOf(UUID.randomUUID());
                    String sql = "insert class_group (id,class_id,start_date,users,create_user,create_time,unit) " +
                            "values('" + group_id + "','" + class_id + "','" + start_date + "','" + accepter + "','" + create_user + "','" + create_time + "','" + unit + "');";
                    logger.warn(sql);
                    mysql.update(sql);
                }

            }
            createTables(unit);
            if (recoverList.size() > 0) {
                JSONObject back = ErrNo.set(437011);
                back.put("data", recoverList);
                return back;
            } else {

                return ErrNo.set(0);
            }

        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(437001);
        }

    }

    private void createTables(String unit) {
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String endTime = RIUtil.GetNextDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()), 10);
            String sql =
                    "select * from class_group where start_date<'" + endTime + "' and isdelete=1 and unit='" + unit + "'";
            logger.warn(sql);
            List<JSONObject> groups = mysql.query(sql);
            logger.warn(groups.toString());
            if (groups.size() > 0) {

                for (int g = 0; g < groups.size(); g++) {
                    JSONObject o = groups.get(g);
                    String class_id = o.getString("class_id");
                    String accepter = o.getString("users");
                    String start_date = o.getString("start_date");
                    endTime = RIUtil.GetNextDate(start_date, 30);
                    String group_id = o.getString("id");

                    sql =
                            "select decode(class_name,'" + RIUtil.enName + "') as class_name,id,on_time,off_time," +
                                    "cycle,cycle_week,create_user,create_time,isCheck,unit from class where " +
                                    "id='" + class_id + "' and isdelete=1 and unit='" + unit + "'";

                    logger.warn(sql);
                    List<JSONObject> list = mysql.query(sql);
                    if (list.size() > 0) {
                        JSONObject one = list.get(0);
                        try {
                            String class_name = one.getString("class_name");
                            String on_time = one.getString("on_time");
                            String off_time = one.getString("off_time");
                            int cycle = one.getInteger("cycle");
                            String cycle_week = one.getString("cycle_week");
                            int class_type = one.getInteger("class_type");
                            int isCheck = one.getInteger("isCheck");

                            List<String> accList = RIUtil.HashToList(RIUtil.StringToList(accepter));
                            List<String> dataList = GetDateList(start_date, endTime, cycle, cycle_week);

                            for (int i = 0; i < dataList.size(); i++) {
                                String date = dataList.get(i);

                                for (int d = 0; d < accList.size(); d++) {
                                    String user_id = accList.get(d);

                                    String sqls =
                                            "insert class_table (user_id,class_id,on_time,off_time,class_type," +
                                                    "isCheck,date,isdelete,class_name,group_id,unit)" + "values('" + user_id + "','" + class_id + "','" + on_time + "','" + off_time + "','" + class_type + "','" + isCheck + "','" + date + "','" + 1 + "',encode('" + class_name + "','" + RIUtil.enName + "'),'" + group_id + "','" + unit + "')";
                                    logger.warn(sqls);
                                    mysql.update(sqls);

                                }
                            }
                        } catch (Exception e) {
                            logger.error(Lib.getTrace(e));
                        }

                        sql = "update class_group set start_date='" + endTime + "' where id='" + group_id + "'";
                        mysql.update(sql);
                    }
                }
            }
        } catch (Exception ex) {

            logger.error(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }
    }


    private List<String> GetDateList(String start_date, String endTime, int cycle, String cycle_week) throws Exception {
        List<String> back = new ArrayList<>();
        back.add(start_date);
        long start = RIUtil.dateToStamp(start_date + " 00:00:00");
        long end = RIUtil.dateToStamp(endTime + " 00:00:00") - 1;
        long time = start;
        String timeStr = start_date;
        while (time < end) {
            if (cycle > 0) {
                time = time + cycle * 1000 * 60 * 60 * 24;
                if (time < end) {
                    timeStr = RIUtil.stampToTime(time);
                    //logger.warn(timeStr);
                    back.add(timeStr.substring(0, 10));
                }
            } else {
                time = time + 1 * 1000 * 60 * 60 * 24;
                if (time < end) {
                    timeStr = RIUtil.stampToTime(time);
                    int week = RIUtil.dateToWeek(timeStr);
                    if (week == 0) {
                        week = 7;
                    }
                    //logger.warn(timeStr + "-" + week);
                    if (cycle_week.contains(String.valueOf(week))) {
                        back.add(timeStr.substring(0, 10));
                    }
                }
            }

        }

        return back;
    }

    //******GET*******
    private JSONObject getClaassTable(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String sql = "";
            int limit = 999;
            int page = 1;
            String id = "";
            String user_id = "";
            String class_id = "";
            String class_type = "";
            String start_date = "";
            String end_date = "";
            String unit = "";


            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
                sql = sql + " user_id ='" + user_id + "' and ";
            }
            if (data.containsKey("class_id") && data.getString("class_id").length() > 0) {
                class_id = data.getString("class_id");
                sql = sql + " class_id='" + class_id + "' and ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit").split(",")[0];
                sql = sql + " unit='" + unit + "' and ";
            }
            if (data.containsKey("class_type") && data.getString("class_type").length() > 0) {
                class_type = data.getString("class_type");
                sql = sql + " class_type='" + class_type + "' and ";
            }

            if (data.containsKey("start_date") && data.getString("start_date").length() > 0 && data.containsKey(
                    "end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
                start_date = data.getString("start_date");
                sql = sql + " (date>='" + start_date + "' and date<='" + end_date + "') and";
            }


            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select decode(class_name,'" + RIUtil.enName + "') as class_name,user_id,class_id,group_id," +
                            "on_time,off_time,class_type,isCheck,date,att_result,unit,id from class_table where " +
                            "1=1 and " + sql + " isdelete=1 limit " + limit + " offset " + limit * (page - 1);
            // logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select id from class_table where 1=1 and " + sql + " isdelete=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(437005);
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return back;
    }

    //*****relainfo************
    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String user_id = one.getString("user_id");
            one.put("user_id", RIUtil.users.get(user_id));

            String create_user = one.getString("create_user");
            one.put("create_user", RIUtil.users.get(create_user));

            String id = one.getString("id");
            String sqls = "select * from class_change_log where class_table_id='" + id + "' order by create_time desc ";
            // logger.warn(sqls);
            List<JSONObject> clist = new ArrayList<>();
            clist = mysql.query(sqls);
            if (list.size() > 0) {
                one.put("change", RelaInfo_change(clist, mysql));
            } else {
                one.put("change", clist);
            }
            if (one.containsKey("unit")) {
                String unit = one.getString("unit").split(",")[0];
                String unit_name = RIUtil.dicts.get(unit).getString("dict_name");
                one.put("unit_name", unit_name);
            }
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private JSONObject updateClaassTable(JSONObject data) {
        InfoModelHelper mysql = null;
        // JSONObject back = ErrNo.set(0);
        String class_id = "";
        String accepter = "";
        int class_length = 30;
        String create_user = "";
        String class_name = "";
        JSONArray datas = new JSONArray();
        String start_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());


        if (data.containsKey("class_id") && data.getString("class_id").length() > 0) {
            class_id = data.getString("class_id");

        } else {
            return ErrNo.set(437004);
        }
        if (data.containsKey("data") && data.getString("data").length() > 2) {
            String d = data.getString("data").replace("|", "\"");

            datas = JSONArray.parseArray(d);
            logger.warn(datas.toString());
        } else {
            return ErrNo.set(437004);
        }
        String unit = "";
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            create_user = data.getString("opt_user");
            unit = RIUtil.users.get(create_user).getString("unit").split(",")[0];
            logger.warn(unit);
        } else {
            return ErrNo.set(437004);
        }


        String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

        try {
            mysql = InfoModelPool.getModel();
            class_name = RIUtil.IdToName(class_id, mysql, "decode(class_name,'" + RIUtil.enName + "') as class_name",
                    "class");
            List<String> recoverList = new ArrayList<>();
            String sql = "delete from  class_group  where class_id='" + class_id + "' and isdelete=1";
            mysql.update(sql);
            sql = "delete from class_table where class_id='" + class_id + "' and date>='" + start_date + "'";
            mysql.update(sql);
            for (int i = 0; i < datas.size(); i++) {
                JSONObject one = datas.getJSONObject(i);

                if (one.containsKey("accepter") && one.getString("accepter").length() > 0) {
                    accepter = one.getString("accepter");
                } else {
                    return ErrNo.set(437004);
                }

                if (one.containsKey("start_date") && one.getString("start_date").length() > 0) {
                    start_date = one.getString("start_date");
                }
                logger.warn(start_date);


                HashMap<String, String> users = RIUtil.StringToList(accepter);
                int mark = 0;
                for (Map.Entry<String, String> u : users.entrySet()) {
                    String user = u.getKey();
                    sql = "select id from class_group where class_id='" + class_id + "' and users like '%" + user +
                            "%' and isdelete=1";
                    //System.out.println(sql);
                    List<JSONObject> list = mysql.query(sql);
                    int count = list.size();
                    if (count > 0) {

                        recoverList.add(RIUtil.IdToName(user, mysql, " name", "user") + " " + start_date + ":" + class_name + "重复");
                        logger.warn(RIUtil.IdToName(user, mysql, " name", "user") + " " + start_date + ":" + class_name + "重复");

                    }
                }

                String group_id = String.valueOf(UUID.randomUUID());
                sql = "insert class_group (id,class_id,start_date,users,create_user,create_time,unit) values('" + group_id + "','" + class_id + "','" + start_date + "','" + accepter + "','" + create_user + "','" + create_time + "','" + unit + "');";
                logger.warn(sql);
                mysql.update(sql);

            }

            createTables(unit);
            if (recoverList.size() > 0) {
                JSONObject back = ErrNo.set(437011);
                back.put("data", recoverList);
                return back;
            } else {
                return ErrNo.set(0);
            }

        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
        }
        return ErrNo.set(0);
    }

    //*********delete**********
    private JSONObject deleteClaassTable(JSONObject data, String ip) throws Exception {
        String id = "";
        String opt_user = "";
        String class_id = "";
        String start_time = "";
        String end_time = "";
        String user_id = "";
        String sql = "";

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
            sql = sql + " id='" + id + "' and ";
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(437008);
        }
        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");
            sql = sql + " user_id='" + user_id + "' and ";
        }
        if (data.containsKey("class_id") && data.getString("class_id").length() > 0) {
            class_id = data.getString("class_id");
            sql = sql + " class_id='" + class_id + "' and ";
        }

        if (data.containsKey("start_time") && data.getString("start_time").length() > 0 && data.containsKey("end_time"
        ) && data.getString("end_time").length() > 0) {
            start_time = data.getString("start_time");
            end_time = data.getString("end_time");
            sql = sql + "(date>='" + start_time + "' and date<='" + end_time + "') and ";
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update class_table set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where 1=1 and" + sql + " isdelete='1'";
            logger.warn(sqls);
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除排班", userlog.TYPE_OPERATE, ip);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(437007);
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return ErrNo.set(0);
    }


    private JSONObject createChangeLog(JSONObject data, String ip) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String new_user_id = "";
            String change_date = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String class_table_id = "";

            if (data.containsKey("new_user_id") && data.getString("new_user_id").length() > 0) {
                new_user_id = data.getString("new_user_id");
            } else {
                return ErrNo.set(438002);
            }
            String unit = "";
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");

                unit = RIUtil.users.get(create_user).getString("unit").split(",")[0];

            } else {
                return ErrNo.set(438002);
            }

            if (data.containsKey("class_table_id") && data.getString("class_table_id").length() > 0) {
                class_table_id = data.getString("class_table_id");
            } else {
                return ErrNo.set(438002);
            }
            String time_now = new SimpleDateFormat("HH:mm").format(new Date());
            String date_now = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String sqls = "select * from class_table where id='" + class_table_id + "' and date>'" + date_now + "'";
            List<JSONObject> list = mysql.query(sqls);
            if (list.size() > 0) {
                JSONObject one = list.get(0);
                String user_id = one.getString("user_id");
                String date = one.getString("date");
                String class_id = one.getString("class_id");
                int class_type = one.getInteger("class_type");
                change_date = date;
                if (create_user.equals(user_id)) {

                    sqls = "select id from class_table where user_id='" + new_user_id + "' and class_id='" + class_id + "' and class_type=" + class_type + " and date='" + date + "' and isdelete=1";
                    logger.warn(sqls);
                    List<JSONObject> classes = mysql.query(sqls);
                    if (classes.size() == 0) {

                        sqls = "update class_table set user_id='" + new_user_id + "' where id='" + class_table_id + "'";
                        mysql.update(sqls);
                    } else {
                        return ErrNo.set(438007);
                    }

                } else {
                    return ErrNo.set(438004);
                }
            } else {
                return ErrNo.set(438003);
            }


            sqls = "insert class_change_log (new_user_id,change_date,create_user,create_time,class_table_id,unit)" +
                    "values('" + new_user_id + "','" + change_date + "','" + create_user + "','" + create_time + "'," + "'" + class_table_id + "','" + unit + "')";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建调班", userlog.TYPE_OPERATE, ip);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(438001);
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return back;
    }

    //******GET*******
    private JSONObject getChangeLog(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String new_user_id = "";
            String change_start_date = "";
            String change_end_date = "";
            String create_user = "";
            String start_time = "";
            String end_time = "";
            String class_table_id = "";
            String unit = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit").split(",")[0];
                sql = sql + " unit='" + unit + "' and ";
            }
            if (data.containsKey("new_user_id") && data.getString("new_user_id").length() > 0) {
                new_user_id = data.getString("new_user_id");
                sql = sql + " new_user_id='" + new_user_id + "' and ";
            }
            if (data.containsKey("change_start_date") && data.getString("change_start_date").length() > 0 && data.containsKey("change_end_date") && data.getString("change_endt_date").length() > 0) {
                change_start_date = data.getString("change_start_date");
                change_end_date = data.getString("change_end_date");
                sql = sql + " (change_date>='" + change_start_date + "' and change_date<='" + change_end_date + "') " + "and";
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' and ";
            }
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0 && data.containsKey(
                    "end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time").replace("|", " ");
                start_time = data.getString("start_time").replace("|", " ");
                sql = sql + " (create_time>='" + start_time + "' and create_time<='" + end_time + "') and";
            }
            if (data.containsKey("class_table_id") && data.getString("class_table_id").length() > 0) {
                class_table_id = data.getString("class_table_id");
                sql = sql + " class_table_id='" + class_table_id + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select *  from class_change_log where 1=1 and " + sql + " 1=1 order by create_time desc " +
                            "limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo_change(list, mysql));
                sqls = "select id from class_change_log where 1=1 and " + sql + " 1=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(438005);
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return back;
    }

    //*****relainfo************
    private List<JSONObject> RelaInfo_change(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String create_user = one.getString("create_user");
            // logger.warn(create_user);
            one.put("create_user", RIUtil.users.get(create_user));
            String new_user_id = one.getString("new_user_id");
            //  logger.warn(new_user_id);
            one.put("new_user_id", RIUtil.users.get(new_user_id));
            String class_table_id = one.getString("class_table_id");
            //   logger.warn(class_table_id);
            String class_id = RIUtil.IdToName(class_table_id, mysql, "class_id", "class_table");
            one.put("class_name", RIUtil.IdToName(class_id, mysql,
                    "decode(class_name,'" + RIUtil.enName + "') as " + "class_name", "class"));
            if (one.containsKey("unit")) {
                String unit = one.getString("unit").split(",")[0];
                String unit_name = RIUtil.dicts.get(unit).getString("dict_name");
                one.put("unit_name", unit_name);
            }
            //   logger.warn(one.toString());
            back.add(one);
        }
        return back;
    }
}
