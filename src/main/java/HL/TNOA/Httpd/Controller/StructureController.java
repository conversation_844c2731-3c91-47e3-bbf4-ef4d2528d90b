package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

@RestController
public class StructureController {
    private static Logger logger = LoggerFactory.getLogger(StructureController.class);


    @RequestMapping(method = {RequestMethod.POST}, path = {"/struct"})
    @PassToken
    public JSONObject get_struct(TNOAHttpRequest reuest) throws Exception {
        JSONObject data = reuest.getRequestParams();

        String opt = "";

        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_struct")) {
                return GetStruct(data);
            } else if (opt.equals("update_struct")) {
                logger.warn("struce-->" + data);
                return UpdateStruct(data, reuest.getRemoteAddr());
            } else if (opt.equals("update_struct_desc")) {
                return updateStructDesc(data, reuest.getRemoteAddr());
            } else {
                return ErrNo.set(460001);
            }
        } else {
            return ErrNo.set(460001);
        }
    }

    private JSONObject updateStructDesc(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);

        String user_id = "";
        String desc = "";
        String opt_user = "";

        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");
        } else {
            return ErrNo.set(460005);
        }
        if (data.containsKey("desc") && data.getString("desc").length() > 0) {
            desc = data.getString("desc");
        } else {
            return ErrNo.set(460005);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(460005);
        }
        try {
            mysql = InfoModelPool.getModel();
            String sql =
                    "update `user` set desciption=encode('" + desc + "','" + RIUtil.enContent + "') where id='" + user_id + "'";
            mysql.update(sql);

            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新架构信息", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(460004);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    public static void main(String[] args) {
        System.out.println(GetStruct(new JSONObject()));
    }

    private static JSONObject GetStruct(JSONObject data) {

        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            //民警 辅警层
            JSONArray first = new JSONArray();
            String sql = "select * from struct where isMain=0 and father_id=0";
            JSONObject first1 = new JSONObject();
            first1.put("name", "民警");
            first1.put("color", "#0F5DC6");
            String s = "select sum(count) as count from struct where isMain=0 and father_id=0";
            first1.put("count", mysql.query_count(s));

            List<JSONObject> list = mysql.query(sql);
            JSONArray second = new JSONArray();
            int count2 = 0;
            if (list.size() > 0) {

                for (int i = 0; i < list.size(); i++) {
                    JSONObject one2 = list.get(i);

                    String id = one2.getString("id");
                    String struct = one2.getString("struct");
                    one2.remove("struct");
                    one2.put("name", struct);
                    sql = "select * from struct where father_id='" + id + "'";
                    List<JSONObject> list3 = mysql.query(sql);

                    if (list3.size() > 0) {
                        List<JSONObject> third = relaInfo(list3, mysql);
                        one2.put("children", third);
                    } else {
                        one2.put("children", new JSONArray());

                    }
                    second.add(one2);
                }
            }
            JSONArray sec = relaInfo_json(second, mysql);

            first1.put("children", sec);

            first.add(first1);

            sql = "select * from struct where isMain=1 and father_id=0";
            JSONObject first2 = new JSONObject();
            first2.put("name", "辅警");
            first2.put("color", "#0F5DC6");

            s = "select sum(count) as count from struct where isMain=1 and father_id=0";
            first2.put("count", mysql.query_count(s));
            list = mysql.query(sql);
            second = new JSONArray();
            if (list.size() > 0) {

                for (int i = 0; i < list.size(); i++) {
                    JSONObject one2 = list.get(i);

                    String id = one2.getString("id");
                    String struct = one2.getString("struct");
                    one2.remove("struct");
                    one2.put("name", struct);
                    sql = "select * from struct where father_id='" + id + "'";
                    List<JSONObject> list3 = mysql.query(sql);
                    if (list3.size() > 0) {
                        List<JSONObject> third = relaInfo(list3, mysql);
                        one2.put("children", third);
                    } else {
                        one2.put("children", new JSONArray());
                    }
                    second.add(one2);
                }
            }

            first2.put("children", relaInfo_json(second, mysql));
            first.add(first2);

            back.put("data", first);
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(460003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static List<JSONObject> relaInfo(List<JSONObject> structure, InfoModelHelper mysql) throws Exception {
        //JSONArray
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < structure.size(); i++) {
            JSONObject one = structure.get(i);
            String users = one.getString("users");
            String struct = one.getString("struct");
            one.remove("struct");
            one.put("name", struct);
            if (users != null && users.length() > 0) {
                JSONArray plist = OrderByPosition(RIUtil.UseridToNames(RIUtil.StringToList(users)));
                one.put("persons", plist);

            } else {
                one.put("persons", new JSONArray());

            }
            one.put("color", "#48B95D");

            back.add(one);
        }


        return back;
    }

    private static JSONArray relaInfo_json(JSONArray structure, InfoModelHelper mysql) throws Exception {
        //JSONArray
        JSONArray back = new JSONArray();
        for (int i = 0; i < structure.size(); i++) {
            JSONObject one = structure.getJSONObject(i);
            String users = one.getString("users");
            /*String struct = one.getString("struct");
            one.remove("struct");
            one.put("name", struct);*/
            if (users != null && users.length() > 0) {
                JSONArray plist = OrderByPosition(RIUtil.UseridToNames(RIUtil.StringToList(users)));
                one.put("persons", plist);
            } else {
                one.put("persons", new JSONArray());
            }

            back.add(one);
        }


        return back;
    }

    private static JSONArray OrderByPosition(JSONArray back) {
        List<JSONObject> list = JSONObject.parseArray(back.toJSONString(), JSONObject.class);
        Collections.sort(list, (JSONObject o1, JSONObject o2) -> {
            //转成JSON对象中保存的值类型
            String p1 = "18";
            if (o1.containsKey("position") && o1.getString("position").length() > 0) {
                p1 = o1.getString("position").substring(0, 2);
            }
            String p2 = "18";
            if (o2.containsKey("position") && o2.getString("position").length() > 0) {
                p2 = o2.getString("position").substring(0, 2);
            }
            int a = Integer.parseInt(p1);
            int b = Integer.parseInt(p2);

            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (a > b) {  //降序排列，升序改成a>b
                return 1;
            } else if (a == b) {
                return 0;
            } else {
                return -1;
            }
        });
        back = JSONArray.parseArray(JSON.toJSONString(list));
        return back;
    }

    private JSONObject UpdateStruct(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String id = "";
        String users = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(460005);
        }
        if (data.containsKey("users")) {
            users = data.getString("users");
        } else {
            return ErrNo.set(460005);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(460005);
        }
        try {
            mysql = InfoModelPool.getModel();
            HashMap<String, String> ulist = RIUtil.StringToList(users);
            int count = ulist.size();
            if (users.length() > 0) {
                String[] split = users.split(",");
                logger.warn(split.toString());
                for (int i = 0; i < split.length; i++) {
                    String sql2 = "select * from struct where users like '%" + split[i] + "%'";
                    logger.warn(sql2);
                    List<JSONObject> query = mysql.query(sql2);
                    logger.warn(query.toString());
                    if (query != null && query.size() > 0) {
                        return ErrNo.set(420305);
                    }
                }
            }

            String sql = "update struct set users='" + users + "',count='" + count + "' where id=" + id + "";
            logger.warn(sql);
            mysql.update(sql);
            String father_id = RIUtil.IdToName(id, mysql, "father_id", "struct");
            if (!father_id.equals("0") && father_id.length() > 0) {
                sql = "select sum(count) as count from struct where father_id=" + father_id + "";
                logger.warn(sql);
                count = mysql.query_count(sql);
                sql = "update struct set count='" + count + "' where id='" + father_id + "'";
                mysql.update(sql);
            }
           /* String isMain = RIUtil.IdToName(father_id, mysql, "isMain", "struct");

            sql = "select sum(count) as count from struct where isMain=" + isMain + "";
            count = mysql.query_count(sql);
            sql = "update struct set count='" + count + "' where isMain='" + isMain + "'";
            mysql.update(sql);*/


            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新架构信息", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(460004);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

}
