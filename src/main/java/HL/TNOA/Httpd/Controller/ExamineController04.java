package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.wechat.HttpConnection;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
public class ExamineController04 {
    private static Logger logger = LoggerFactory.getLogger(ExamineController04.class);

    @RequestMapping(method = {RequestMethod.POST}, path = {"/examine_data"})
    public JSONObject get_examine(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        logger.warn("data---->" + data);
        String opt = "";

        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("create_score_det")) {
                return createScoreDet(data, request.getRemoteAddr());
            } else if (opt.equals("get_config")) {
                return getConfig(request);
            } else if (opt.equals("get_score_det")) {
                return getScoreDet(data, request);
            } else if (opt.equals("update_score_det")) {
                return updateScoreDet(data, request.getRemoteAddr());
            } else if (opt.equals("delete_score_det")) {
                return deleteScoreDet(data, request.getRemoteAddr());
            } else if (opt.equals("export_template")) {
                return exportTemplate(request);
            } else if (opt.equals("upload_annex_det")) {
                return uploadAnnexDet(data, request.getRemoteAddr());
            } else if (opt.equals("delete_annex_det")) {
                return deleteAnnexDet(data, request.getRemoteAddr());
            } else if (opt.equals("get_annex_det")) {
                return getAnnexDet(data, request.getRemoteAddr());
            } else if (opt.equals("export_score_det")) {
                return exportScoreDet04(request);
            } else {
                return ErrNo.set(490001);
            }
        } else {
            return ErrNo.set(490001);
        }


    }


    /**
     * 模板生成
     *
     * @return
     */
    private JSONObject exportTemplate(TNOAHttpRequest request) {

        String sql = "";
        String policeId = "";

        boolean flag = false;
        boolean flag2 = false;
        boolean isAdmin = false;

        JSONObject userInfo = getUserInfo(request);
        JSONArray roleId = userInfo.getJSONArray("role_id");
        policeId = userInfo.getString("id_num");
        logger.warn("id_num--->" + policeId);
        for (Object o : roleId) {
            logger.warn("role_id---->" + o.toString());
            //管理员
            if ("hlcheckmanager".equals(o.toString()) || "ztchecksmanager".equals(o.toString())) {
                flag = true;
            }
            //分管民警
            if ("hlcheckpolice".equals(o.toString())) {
                flag2 = true;
            }
        }

        if (flag && flag2) {
            isAdmin = true;
        } else if (flag) {
            isAdmin = true;
        }

        String FileName = "积分导入模板_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        String filePath =
                new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

        String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;

        init(endPath);

        FileOutputStream fos = null;
        SXSSFWorkbook sxssfWorkbook = null;
        InfoModelHelper info = null;
        try {
            info = InfoModelPool.getModel();
            sxssfWorkbook = new SXSSFWorkbook();
            sql = "select id,type,name,value from kh_control where type = 'kh_total'";
            List<JSONObject> query = info.query(sql);

            String time = "";
            String pid = "";
            for (int i = 0; i < query.size(); i++) {
                JSONObject object = query.get(i);
                String name = object.getString("name");
                String value = object.getString("value");
                if (name.equals("total_month")) {
                    time = value;
                }
                if (name.equals("total_plan")) {
                    pid = value;
                }
            }

            sql = "select * from kh_plan where id = '" + pid + "'";
            List<JSONObject> list = info.query(sql);

            ArrayList<JSONObject> fj = new ArrayList<>();
            ArrayList<JSONObject> pcs = new ArrayList<>();
            ArrayList<JSONObject> zrq = new ArrayList<>();

            if (list.size() == 0) {
                return ErrNo.set(490017);
            } else {

                JSONObject obj = list.get(0);
                String fjConfig = obj.getString("fj_config");
                String pcsConfig = obj.getString("pcs_config");
                String zrqConfig = obj.getString("zrq_config");
                if (isAdmin) {
                    fj = getConfigDetById2(fjConfig.split(","));
                    pcs = getConfigDetById2(pcsConfig.split(","));
                    zrq = getConfigDetById2(zrqConfig.split(","));
                } else {
                    fj = getConfigDetById(fjConfig.split(","), policeId);
                    pcs = getConfigDetById(pcsConfig.split(","), policeId);
                    zrq = getConfigDetById(zrqConfig.split(","), policeId);
                }

            }

            JSONObject one = new JSONObject();
            one.put("pg_object", "3");
            one.put("name", "分局");
            one.put("type", "23");
            one.put("configs", fj);
            JSONObject two = new JSONObject();
            two.put("pg_object", "2");
            two.put("name", "派出所");
            two.put("type", "25");
            two.put("configs", pcs);
            JSONObject three = new JSONObject();
            three.put("pg_object", "1");
            three.put("name", "责任区");
            three.put("type", "26");
            three.put("configs", zrq);
            ArrayList<JSONObject> arr = new ArrayList<>();
            arr.add(one);
            arr.add(two);
            arr.add(three);

            for (int s = 0; s < arr.size(); s++) {
                JSONObject ob = arr.get(s);
                JSONArray configs = ob.getJSONArray("configs");

                if (configs.size() == 0) {
                    continue;
                }

                String name = ob.getString("name");
                String type = ob.getString("type");
                sql = "select * from dict where type = '" + type + "' and isdelete = '1' and is_kh = '1'";
                List<JSONObject> orgs = info.query(sql);

                ArrayList<String> h = new ArrayList<>();
                ArrayList<String> h2 = new ArrayList<>();
                ArrayList<String> h3 = new ArrayList<>();
                h.add("单位名称");
                h.add("单位代码");

                h2.add("dwmc");
                h2.add("dwdm");

                h3.add("");
                h3.add("");

                for (int i = 0; i < configs.size(); i++) {

                    JSONObject object = configs.getJSONObject(i);
                    String config_id = object.getString("id");
                    String kh_name = object.getString("kh_name");
                    String fullMark = object.getString("full_mark");

                    h.add(kh_name + "(" + fullMark + "分)");
                    h2.add(config_id);

                    h.add("");
                    h2.add("");

                    h3.add("得分");
                    h3.add("明细");

                }

                // 获取SXSSFWorkbook实例
                Sheet sheet = sxssfWorkbook.createSheet(name);

                //************** 样式一 *******************//
                CellStyle cellStyle = setCellStyle(sxssfWorkbook);
                //************** 样式一 *******************//


                //************** 样式二 *******************//
                CellStyle cellStyle2 = setCellStyle2(sxssfWorkbook);
                //************** 样式二 *******************//

                // 冻结最左边的两列、冻结最上面的一行
                // 即：滚动横向滚动条时，左边的第一、二列固定不动;滚动纵向滚动条时，上面的第一行固定不动。I
                sheet.createFreezePane(2, 3);
                for (int l = 0; l < 2; l++) {
                    sheet.setColumnWidth(l, 3600);
                }

                // 创建第二行,作为header表头 0为第一行
                Row header = sheet.createRow(0);
                header.setHeight((short) 800);
                for (int i = 0; i < h.size(); i++) {
                    Cell cell = header.createCell(i);
                    cell.setCellValue(h.get(i));
                    cell.setCellStyle(cellStyle);

                }

                for (int i = 2; i < h.size(); i = i + 2) {
                    CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 0, i, i + 1);
                    sheet.addMergedRegionUnsafe(cellRangeAddress);
                }

                Row header2 = sheet.createRow(1);
                header2.setHeight((short) 800);
                for (int i = 0; i < h2.size(); i++) {
                    Cell cell = header2.createCell(i);
                    cell.setCellValue(h2.get(i));
                    cell.setCellStyle(cellStyle);
                }
                for (int i = 2; i < h2.size(); i = i + 2) {
                    CellRangeAddress cellRangeAddress = new CellRangeAddress(1, 1, i, i + 1);
                    sheet.addMergedRegionUnsafe(cellRangeAddress);
                }

                Row header3 = sheet.createRow(2);
                header3.setHeight((short) 800);
                for (int i = 0; i < h3.size(); i++) {
                    Cell cell = header3.createCell(i);
                    cell.setCellValue(h3.get(i));
                    cell.setCellStyle(cellStyle);
                }

                // 遍历创建行,导出数据
                for (int rownum = 0; rownum < orgs.size(); rownum++) {
                    Row row = sheet.createRow(rownum + 3);
                    row.setHeight((short) 700);
                    // 循环创建单元格
                    JSONObject org = orgs.get(rownum);
                    String org_id = org.getString("id");
//                    String org_name = org.getString("dict_name");
                    String org_name = org.getString("remark");

                    Cell cell0 = row.createCell(0);
                    cell0.setCellValue(org_name);
                    cell0.setCellStyle(cellStyle);
                    Cell cell1 = row.createCell(1);
                    cell1.setCellValue(org_id);
                    cell1.setCellStyle(cellStyle);

                    for (int cellnum = 2; cellnum < h2.size(); cellnum++) {
                        Cell cell = row.createCell(cellnum);
                        cell.setCellStyle(cellStyle);
                        sheet.setColumnWidth(cellnum, 1600);
                    }

                    for (int cellnum = 2; cellnum < h2.size(); cellnum = cellnum + 2) {
                        Cell cell = row.getCell(cellnum);
                        cell.setCellStyle(cellStyle2);
                        String id = h2.get(cellnum);
                        String point = RIUtil.kh_configs.get(id).getString("point");
                        String full_mark = RIUtil.kh_configs.get(id).getString("full_mark");
                        if (point.equals("2")) {
                            cell.setCellValue(full_mark);
                        } else {
                            cell.setCellValue("0");
                        }
                    }
                }
            }

            fos = new FileOutputStream(endPath);
            sxssfWorkbook.write(fos);

            String sqls =
                    "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "','" + FileName + "',999)";
            logger.warn(sqls);
            info.update(sqls);
            sql = "select * from upload where file_name='" + FileName + "'";
            List<JSONObject> result = info.query(sql);
            JSONObject xJsonObject = result.get(0);
            int id = xJsonObject.getIntValue("id");

            logger.warn("id->" + id);
            JSONObject back = new JSONObject();
            back.put("id", id);
           /* BashExecutor bash = new BashExecutor();
            String cmd = "cp -r " + endPath + " " + TNOAConf.get("file", "bk_path");
            logger.warn(cmd);
            bash.exec(cmd, -1, true);*/

            String endPoint = "http://10.34.251.34:50101";
            String ak = "Q7D3OUVIRBEZEB4RWVSJ";
            String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
            String bucketName = "obs-qjjc-tyyh";
            ObsServer obsServ = new ObsServer();
            String obsFileName = "hl/" + filePath + FileName;
            System.out.println(obsFileName);
            boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
            logger.warn(obsFileName + "-->" + ret);
            return back;

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(info);
            try {
                if (sxssfWorkbook != null) {
                    sxssfWorkbook.dispose();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private CellStyle setCellStyle(SXSSFWorkbook sxssfWorkbook) {
        // 获取SXSSFWorkbook实例

        //************** 样式一 *******************//
        CellStyle cellStyle = sxssfWorkbook.createCellStyle();
        cellStyle.setWrapText(true);
        //对齐方式
        //设置水平对齐方式
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        //设置垂直对齐方式
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //设置边框
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        // 设置字体
        Font font = sxssfWorkbook.createFont();
        font.setFontName("Segoe UI");
        font.setFontHeightInPoints((short) 9);
        font.setBold(true);
        cellStyle.setFont(font);
        //************** 样式一 *******************//

        return cellStyle;
    }

    private CellStyle setCellStyle2(SXSSFWorkbook sxssfWorkbook) {

        //************** 样式二 *******************//
        CellStyle cellStyle2 = sxssfWorkbook.createCellStyle();
        cellStyle2.setWrapText(true);

        DataFormat df = sxssfWorkbook.createDataFormat();
        cellStyle2.setDataFormat(df.getFormat("0.000_ "));//保留两位小数点

        //对齐方式
        //设置水平对齐方式
        cellStyle2.setAlignment(HorizontalAlignment.CENTER);
        //设置垂直对齐方式
        cellStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
//        cellStyle2.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.000"));
        //设置边框
        cellStyle2.setBorderTop(BorderStyle.THIN);
        cellStyle2.setBorderRight(BorderStyle.THIN);
        cellStyle2.setBorderBottom(BorderStyle.THIN);
        cellStyle2.setBorderLeft(BorderStyle.THIN);
        // 设置字体
        Font font = sxssfWorkbook.createFont();
        font.setFontName("Segoe UI");
        font.setFontHeightInPoints((short) 9);
        font.setBold(true);
        cellStyle2.setFont(font);
        //************** 样式二 *******************//
        return cellStyle2;
    }

    public void init(String path) {
        try {
            if (!new File("tmp").exists()) {
                new File("tmp").mkdir();
            }
            if (!new File(path).getParentFile().exists()) {
                new File(path).getParentFile().mkdirs();
            }
            if (!new File(path).exists()) {
                new File(path).createNewFile();
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }

    }

    /**
     * 删除上传附件
     *
     * @param data
     * @return
     */
    private JSONObject deleteAnnexDet(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            //组织机构代码
            String file_id = "";

            if (data.containsKey("file_id") && data.getString("file_id").length() > 0) {
                file_id = data.getString("file_id");
            } else {
                return ErrNo.set(480023);
            }

            String sql = " select id,file_id from kh_det where file_id like '%" + file_id + "%' and isdelete = '1'";
            List<JSONObject> query = mysql.query(sql);
            for (JSONObject jsonObject : query) {
                String fileId = jsonObject.getString("file_id");
                String id = jsonObject.getString("id");
                Set<String> set = new HashSet<>(Arrays.asList(fileId.split(",")));
                if (set.contains(file_id)) {
                    set.remove(file_id);
                    String collect = String.join(",", set);
                    sql = " update kh_det set file_id = '" + collect + "' where id = '" + id + "'";
                    mysql.update(sql);
                }
            }

//            String sql = "update kh_det set file_id = '' where file_id = '" + file_id + "' and isdelete = '1'";
//            mysql.update(sql);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    /**
     * 获取上传附件
     *
     * @param data
     * @return
     */
    private JSONObject getAnnexDet(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            //组织机构代码

            String sql = "select file_id from kh_det where isdelete = '1' and file_id is not null and file_id != '' ";
            List<JSONObject> list = mysql.query(sql);
            ArrayList<JSONObject> arr = new ArrayList<>();

            if (!list.isEmpty()) {
                Set<String> set = new HashSet<>();
                for (JSONObject jsonObject : list) {
                    String fileId = jsonObject.getString("file_id");
                    String[] split = fileId.split(",");
                    set.addAll(Arrays.asList(split));
                }
                String collect = set.stream().map(s -> "'" + s + "'").collect(Collectors.joining(","));
                sql = "select * from upload where id in (" + collect + ")";
                List<JSONObject> query = mysql.query(sql);
                if (!query.isEmpty()) {
                    for (JSONObject jsonObject : query) {
                        JSONObject one = new JSONObject();
                        one.put("file_id", jsonObject.getString("id"));
                        one.put("file_name", jsonObject.getString("file_name"));
                        arr.add(one);
                    }
                }


            }

//            if (list.size() > 0) {
//                for (int i = 0; i < list.size(); i++) {
//                    JSONObject one = new JSONObject();
//                    JSONObject obj = list.get(i);
//                    String fileId = obj.getString("file_id");
//                    if (fileId.length() > 0) {
//                        sql = "select * from upload where id = '" + fileId + "'";
//                        List<JSONObject> query = mysql.query(sql);
//                        if (query.size() > 0) {
//                            String fileName = query.get(0).getString("file_name");
//                            one.put("file_id", fileId);
//                            one.put("file_name", fileName);
//
//                        }
//                        arr.add(one);
//                    }
//                }
//            }
            back.put("data", arr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    /**
     * 上传得分明细附件
     *
     * @param data
     * @return
     */
    private JSONObject uploadAnnexDet(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            //组织机构代码
            String file_id = "";
            String time = "";
            String configs = "";

            if (data.containsKey("file_id") && data.getString("file_id").length() > 0) {
                file_id = data.getString("file_id");
            } else {
                return ErrNo.set(480015);
            }

            if (data.containsKey("time") && data.getString("time").length() > 0) {
                time = data.getString("time");
            } else {
                return ErrNo.set(480015);
            }

            if (data.containsKey("configs") && data.getString("configs").length() > 0) {
                configs = data.getString("configs");
            } else {
                return ErrNo.set(480015);
            }

            String[] split = configs.split(",");
            String conf = "";
            for (int i = 0; i < split.length; i++) {
                String config = split[i];

                conf = conf + "'" + config + "',";

            }

            // 上传多个附件
            for (String s : split) {
                String file = "";
                String sql = " select file_id from kh_det where time = '" + time + "' and config_id = '" + s + "' and" +
                        " isdelete = '1'";
                List<JSONObject> query = mysql.query(sql);
                if (!query.isEmpty()) {
                    String fileId = query.get(0).getString("file_id");
                    Set<String> set = new HashSet<>(Arrays.asList(fileId.split(",")));
                    set.add(file_id);
                    String collect = set.stream().collect(Collectors.joining(","));
                    sql = "update kh_det set file_id = '" + collect + "' where time = '" + time + "' and config_id = " +
                            "'" + s + "' and isdelete = '1'";
                    mysql.update(sql);

                }
            }

//            String sql = "update kh_det set file_id = '" + file_id + "' where time = '" + time + "' and config_id
//            in " +
//                    "(" + conf.substring(0, conf.length() - 1) + ") and isdelete = '1'";


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480016);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject getScoreDet(JSONObject data, TNOAHttpRequest request) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String policeId = "";
            boolean flag = false;
            boolean flag2 = false;
            boolean isAdmin = false;

            JSONObject userInfo = getUserInfo(request);
            JSONArray roleId = userInfo.getJSONArray("role_id");
            policeId = userInfo.getString("id_num");
            logger.warn("id_num--->" + policeId);
            for (Object o : roleId) {
                logger.warn("role_id---->" + o.toString());
                //管理员
                if ("hlcheckmanager".equals(o.toString()) || "ztchecksmanager".equals(o.toString())) {
                    flag = true;
                }
                //分管民警
                if ("hlcheckpolice".equals(o.toString())) {
                    flag2 = true;
                }
            }

            if (flag && flag2) {
                isAdmin = true;
            } else if (flag) {
                isAdmin = true;
            }

            int limit = 20;
            int page = 1;

            String sql = "";
            String id = "";
            String config_id = "";
            String org_id = "";
            String detail = "";
            String time = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " a.id ='" + id + "' and ";
            }
            if (data.containsKey("config_id") && data.getString("config_id").length() > 0) {
                config_id = data.getString("config_id");
                sql = sql + " a.config_id ='" + config_id + "' and ";
            }
            if (data.containsKey("org_id") && data.getString("org_id").length() > 0) {
                org_id = data.getString("org_id");
                sql = sql + " a.org_id = '" + org_id + "' and ";
            }
            if (data.containsKey("detail") && data.getString("detail").length() > 0) {
                detail = data.getString("detail");
                sql = sql + " a.detail like '%" + detail + "%' and ";
            }
            if (data.containsKey("time") && data.getString("time").length() > 0) {
                time = data.getString("time");
                sql = sql + " a.time ='" + time + "' and ";
            }
            boolean isVerify = true;
            if (data.containsKey("passVerify") && data.getBoolean("passVerify")) {
                isVerify = false;
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            if (isVerify) {
                if (!isAdmin) {
                    sql = sql + " b.resp_police ='" + policeId + "' and ";
                }
            }

            String sqls =
                    "select a.*,b.resp_police,b.kh_name,b.full_mark,b.point,b.static_type,b.pg_rules from kh_det a " +
                            "left join kh_config b on a.config_id=b.id where 1=1 and " + sql +
                            " a.isdelete=1  limit " + limit + " offset " + limit * (page - 1);
            logger.warn(sqls);
            List<JSONObject> list = mysql.query(sqls);

            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select a.*,b.kh_name,b.full_mark,b.point from kh_det a left join kh_config b on a.config_id=b" +
                        ".id where 1=1 and " + sql +
                        " a.isdelete=1";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-getScoreDet", userlog.TYPE_OPERATE, request.getRemoteAddr());


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(490004);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    /**
     * 获取方案指标
     *
     * @param request
     * @return
     */
    private JSONObject getConfig(TNOAHttpRequest request) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String policeId = "";
            String sqls = "";

            boolean flag = false;
            boolean flag2 = false;
            boolean isAdmin = false;

            JSONObject userInfo = getUserInfo(request);
            JSONArray roleId = userInfo.getJSONArray("role_id");
            policeId = userInfo.getString("id_num");
            logger.warn("id_num--->" + policeId);
            for (Object o : roleId) {
                logger.warn("role_id---->" + o.toString());
                //管理员
                if ("hlcheckmanager".equals(o.toString()) || "ztchecksmanager".equals(o.toString())) {
                    flag = true;
                }
                //分管民警
                if ("hlcheckpolice".equals(o.toString())) {
                    flag2 = true;
                }
            }

            if (flag && flag2) {
                isAdmin = true;
            } else if (flag) {
                isAdmin = true;
            }


            sqls = "select id,type,name,value from kh_control where type = 'kh_total'";
            List<JSONObject> query = mysql.query(sqls);


            String pid = "";
            for (int i = 0; i < query.size(); i++) {
                JSONObject object = query.get(i);
                String name = object.getString("name");
                String value = object.getString("value");

                if (name.equals("total_plan")) {
                    pid = value;
                }
            }

            sqls = "select * from kh_plan where id = '" + pid + "'";
            List<JSONObject> list = mysql.query(sqls);

            if (list.size() > 0) {
                JSONObject obj = list.get(0);
                JSONArray fj = obj.getJSONArray("fj");
                JSONArray pcs = obj.getJSONArray("pcs");
                JSONArray zrq = obj.getJSONArray("zrq");

                if (isAdmin) {
                    obj.put("fj", hasNext(planInfo(fj)));
                    obj.put("pcs", hasNext(planInfo(pcs)));
                    obj.put("zrq", hasNext(planInfo(zrq)));
                } else {
                    obj.put("fj", hasNext2(planInfo(fj), policeId));
                    obj.put("pcs", hasNext2(planInfo(pcs), policeId));
                    obj.put("zrq", hasNext2(planInfo(zrq), policeId));
                }

                back.put("data", obj);
            } else {
                return ErrNo.set(490017);
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(480049);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONArray hasNext(JSONArray data) {
        ArrayList<Integer> ids = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            JSONObject obj = data.getJSONObject(i);
            if (obj.containsKey("next") && obj.getJSONArray("next").size() > 0) {
                JSONArray arr = hasNext(obj.getJSONArray("next"));
                if (arr.size() == 0) {
                    ids.add(i);
                }
            } else {
                if (obj.containsKey("label") && obj.getBoolean("label")) {
                    String staticType = obj.getString("static_type");
                    try {
                        if (!"2".equals(staticType)) {
                            ids.add(i);
                        }
                    } catch (Exception ex) {
                        ids.add(i);
                    }

                } else {
                    ids.add(i);
                }
            }
        }
        if (ids.size() > 0) {
            for (int i = ids.size() - 1; i >= 0; i--) {
                int index = ids.get(i);
                data.remove(index);
            }
        }
        return data;
    }

    private JSONArray hasNext2(JSONArray data, String policeId) {
        ArrayList<Integer> ids = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            JSONObject obj = data.getJSONObject(i);
            if (obj.containsKey("next") && obj.getJSONArray("next").size() > 0) {
                JSONArray arr = hasNext2(obj.getJSONArray("next"), policeId);
                if (arr.size() == 0) {
                    ids.add(i);
                }
            } else {
                if (obj.containsKey("label") && obj.getBoolean("label")) {
                    String respPolice = obj.getString("resp_police");
                    String staticType = obj.getString("static_type");

                    try {
                        if (!respPolice.equals(policeId) || !"2".equals(staticType)) {
                            ids.add(i);
                        }
                    } catch (Exception ex) {
                        ids.add(i);
                    }

                } else {
                    ids.add(i);
                }
            }
        }
        if (ids.size() > 0) {
            for (int i = ids.size() - 1; i >= 0; i--) {
                int index = ids.get(i);
                data.remove(index);
            }
        }
        return data;
    }

    private JSONArray planInfo(JSONArray data) {
        for (int i = 0; i < data.size(); i++) {
            JSONObject obj = data.getJSONObject(i);
            if (obj.containsKey("next") && obj.getJSONArray("next").size() > 0) {
                planInfo(obj.getJSONArray("next"));
            } else {
                if (obj.containsKey("label") && obj.getBoolean("label")) {
                    String id = obj.getString("id");
                    JSONObject conf = RIUtil.kh_configs.get(id);

                    obj.put("pg_rules", conf.getString("pg_rules"));
                    obj.put("resp_dept", conf.getString("resp_dept"));
                    obj.put("resp_police", conf.getString("resp_police"));
                    obj.put("full_mark", conf.getString("full_mark"));
                    obj.put("point", conf.getString("point"));
                    obj.put("static_type", conf.getString("static_type"));
                    obj.put("kh_name", conf.getString("kh_name"));
                }
            }
        }
        return data;
    }

    private JSONObject exportScoreDet04(TNOAHttpRequest request) {
        long start = System.currentTimeMillis();
        String FileName = "指标得分_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        String filePath =
                new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

        String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;

        logger.warn(endPath);

        FileOutputStream fos = null;
        SXSSFWorkbook sxssfWorkbook = null;
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String policeId = "";
            String sqls = "";
            sxssfWorkbook = new SXSSFWorkbook();

            boolean flag = false;
            boolean flag2 = false;
            boolean isAdmin = false;

            JSONObject userInfo = getUserInfo(request);
            JSONArray roleId = userInfo.getJSONArray("role_id");
            policeId = userInfo.getString("id_num");
            logger.warn("id_num--->" + policeId);
            for (Object o : roleId) {
                logger.warn("role_id---->" + o.toString());
                //管理员
                if ("hlcheckmanager".equals(o.toString()) || "ztchecksmanager".equals(o.toString())) {
                    flag = true;
                }
                //分管民警
                if ("hlcheckpolice".equals(o.toString())) {
                    flag2 = true;
                }
            }

            if (flag && flag2) {
                isAdmin = true;
            } else if (flag) {
                isAdmin = true;
            }

            String s = "select type,name,value from kh_control where type = 'kh_total'";
            List<JSONObject> control = mysql.query(s);

            String pid = "";
            String time = "";

            for (int i = 0; i < control.size(); i++) {
                JSONObject c = control.get(i);
                String name = c.getString("name");
                String value = c.getString("value");
                if ("total_plan".equals(name)) {
                    pid = value;
                }
                if ("total_month".equals(name)) {
                    time = value;
                }
            }

            s = "select * from kh_plan where id = '" + pid + "'";
            List<JSONObject> list = mysql.query(s);

            ArrayList<JSONObject> fj;
            ArrayList<JSONObject> pcs;
            ArrayList<JSONObject> zrq;
            if (list.size() > 0) {
                JSONObject obj = list.get(0);
                String fjConfig = obj.getString("fj_config");
                String pcsConfig = obj.getString("pcs_config");
                String zrqConfig = obj.getString("zrq_config");
                if (isAdmin) {
                    fj = getConfigDetById2(fjConfig.split(","));
                    pcs = getConfigDetById2(pcsConfig.split(","));
                    zrq = getConfigDetById2(zrqConfig.split(","));
                } else {
                    fj = getConfigDetById(fjConfig.split(","), policeId);
                    pcs = getConfigDetById(pcsConfig.split(","), policeId);
                    zrq = getConfigDetById(zrqConfig.split(","), policeId);
                }
            } else {
                return ErrNo.set(490017);
            }

            exportScore(fj, sxssfWorkbook, "分局", "23", time);
            exportScore(pcs, sxssfWorkbook, "派出所", "25", time);
            exportScore(zrq, sxssfWorkbook, "责任区", "26", time);

            // 在后面设置sheet
            fos = new FileOutputStream(endPath);
            sxssfWorkbook.write(fos);

            sqls =
                    "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "','" + FileName + "',999)";
            logger.warn(sqls);
            mysql.update(sqls);
            sqls = "select * from upload where file_name='" + FileName + "'";
            List<JSONObject> result = mysql.query(sqls);
            JSONObject xJsonObject = result.get(0);
            int id = xJsonObject.getIntValue("id");

            logger.warn("id->" + id);
            JSONObject back = new JSONObject();
            back.put("id", id);
            long end = System.currentTimeMillis();
            back.put("time", end - start);

            String endPoint = "http://10.34.251.34:50101";
            String ak = "Q7D3OUVIRBEZEB4RWVSJ";
            String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
            String bucketName = "obs-qjjc-tyyh";
            ObsServer obsServ = new ObsServer();
            String obsFileName = "hl/" + filePath + FileName;
            System.out.println(obsFileName);
            boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
            logger.warn(obsFileName + "-->" + ret);
            /*BashExecutor bash = new BashExecutor();
            String cmd = "cp -r " + endPath + " " + TNOAConf.get("file", "bk_path");
            logger.warn(cmd);
            bash.exec(cmd, -1, true);*/

            return back;

        } catch (Exception e) {
            e.printStackTrace();
            return ErrNo.set(480026);
        } finally {
            InfoModelPool.putModel(mysql);
            try {
                if (sxssfWorkbook != null) {
                    sxssfWorkbook.dispose();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    private ArrayList<JSONObject> getConfigDetById(String[] data, String policeId) {
        ArrayList<JSONObject> back = new ArrayList<>();
        try {
            for (int i = 0; i < data.length; i++) {
                String id = data[i];
                JSONObject object = RIUtil.kh_configs.get(id);
                String staticType = object.getString("static_type");
                String respPolice = object.getString("resp_police");
                if (respPolice.equals(policeId)) {
                    if (staticType.equals("2")) {
                        back.add(object);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return back;
    }

    private ArrayList<JSONObject> getConfigDetById2(String[] data) {
        ArrayList<JSONObject> back = new ArrayList<>();
        try {
            for (int i = 0; i < data.length; i++) {
                String id = data[i];
                JSONObject object = RIUtil.kh_configs.get(id);
                String staticType = object.getString("static_type");

                if (staticType.equals("2")) {
                    back.add(object);
                }

            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return back;
    }

    private void exportScore(ArrayList<JSONObject> configs, SXSSFWorkbook sxssfWorkbook, String name, String type,
                             String time) {
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls = "";

            sqls = "select * from kh_det where  time = '" + time + "' and  isdelete=1";
            logger.warn(sqls);
            List<JSONObject> list = mysql.query(sqls);

            sqls = "select * from dict where type = '" + type + "' and isdelete = '1' and is_kh = '1'";
            logger.warn(sqls);
            List<JSONObject> orgs = mysql.query(sqls);

            ArrayList<String> h = new ArrayList<String>() {{
                add("单位名称");
                add("单位代码");
                add("考核月份");
            }};
            ArrayList<String> h2 = new ArrayList<String>() {{
                add("dwmc");
                add("dwdm");
                add("khyf");
            }};
            ArrayList<String> h3 = new ArrayList<String>() {{
                add("");
                add("");
                add("");
            }};

            for (int i = 0; i < configs.size(); i++) {

                JSONObject object = configs.get(i);
                String config_id = object.getString("id");
                String kh_name = object.getString("kh_name");
                String resp_police = object.getString("resp_police");
                h.add(kh_name);
                h2.add(config_id);
                try {
                    h3.add(RIUtil1.users1.get(resp_police).getString("name"));
                } catch (Exception e) {
                    h3.add(resp_police);
                }
                h.add("");
                h2.add("");
                h3.add("");

            }
            // 获取SXSSFWorkbook实例
            Sheet sheet = sxssfWorkbook.createSheet(name);
            sheet.createFreezePane(3, 3);
            CellStyle cellStyle = setCellStyle(sxssfWorkbook);
            CellStyle cellStyle2 = setCellStyle2(sxssfWorkbook);

            // 创建第一行,作为header表头 0为第一行
            Row header = sheet.createRow(0);
            header.setHeight((short) 1000);
            for (int i = 0; i < h.size(); i++) {
                Cell cell = header.createCell(i);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(h.get(i));
            }
            for (int i = 1; i < h.size(); i = i + 2) {
                if (i > 2) {
                    CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 0, i, i + 1);
                    sheet.addMergedRegionUnsafe(cellRangeAddress);
                }

            }
            Row header2 = sheet.createRow(1);
            header2.setHeight((short) 1000);
            for (int i = 0; i < h2.size(); i++) {
                Cell cell = header2.createCell(i);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(h2.get(i));
            }
            for (int i = 1; i < h2.size(); i = i + 2) {
                if (i > 2) {
                    CellRangeAddress cellRangeAddress = new CellRangeAddress(1, 1, i, i + 1);
                    sheet.addMergedRegionUnsafe(cellRangeAddress);
                }

            }

            Row header3 = sheet.createRow(2);
            header3.setHeight((short) 1000);
            for (int i = 0; i < h3.size(); i++) {
                Cell cell = header3.createCell(i);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(h3.get(i));
            }
            for (int i = 1; i < h3.size(); i = i + 2) {
                if (i > 2) {
                    CellRangeAddress cellRangeAddress = new CellRangeAddress(2, 2, i, i + 1);
                    sheet.addMergedRegionUnsafe(cellRangeAddress);
                }

            }

            // 遍历创建行,导出数据
            for (int rownum = 0; rownum < orgs.size(); rownum++) {
                Row row = sheet.createRow(rownum + 3);
                row.setHeight((short) 600);
                // 循环创建单元格
                JSONObject org = orgs.get(rownum);
                String orgId = "";
                String orgName = "";
                orgId = org.getString("id");
//                orgName = org.getString("dict_name");
                orgName = org.getString("remark");

                List<JSONObject> list2 = getListByOrgAndTime(list, orgId);

                Cell dwmc = row.createCell(0);
                dwmc.setCellStyle(cellStyle);
                sheet.setColumnWidth(0, 4500);
                dwmc.setCellValue(orgName);
                Cell dwdm = row.createCell(1);
                dwdm.setCellStyle(cellStyle);
                dwdm.setCellValue(orgId);
                sheet.setColumnWidth(1, 2600);
                Cell khyf = row.createCell(2);
                khyf.setCellStyle(cellStyle);
                khyf.setCellValue(time);
                sheet.setColumnWidth(2, 1800);

                for (int cellnum = 3; cellnum < h2.size(); cellnum++) {
                    Cell cell = row.createCell(cellnum);
                    cell.setCellStyle(cellStyle2);
                    String lab = h2.get(cellnum);
                    JSONObject det = getScoreByConfigId(list2, lab);
                    sheet.setColumnWidth(cellnum, 1800);
                    if (det != null) {
                        String score = det.getString("score");
                        cell.setCellValue(score);
                    }
                }

                for (int cellnum = 0; cellnum < h2.size(); cellnum = cellnum + 2) {
                    if (cellnum > 3) {
                        String lab = h2.get(cellnum - 1);
                        JSONObject det = getScoreByConfigId(list2, lab);
                        if (det != null) {
                            String detail = det.getString("detail");
                            String fileId = det.getString("file_id");
                            Cell cell = row.getCell(cellnum);
                            cell.setCellStyle(cellStyle);
                            if (fileId.length() > 0) {
                                cell.setCellValue("批量明细");
                            } else {
                                cell.setCellValue(detail);
                            }
                        }
                    }

                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }


    //*****relainfo************
    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String orgId = one.getString("org_id");
            String policeId = one.getString("resp_police");

            try {
                one.put("org", RIUtil.dicts.get(orgId).getString("dict_name"));
            } catch (Exception e) {
                one.put("org", orgId);
            }

            try {
                String sql = "select id,police_id,id_num,name from user where id_num = '" + policeId + "'";
                List<JSONObject> users = mysql.query(sql);
                JSONObject user = users.get(0);
                String name = user.getString("name");
                one.put("resp_police_name", name);
            } catch (Exception ex) {
                one.put("resp_police_name", policeId);
            }

            back.add(one);
        }
        return back;
    }


    private static List<JSONObject> getListByOrgAndTime(List<JSONObject> arrayList, String org) {
        ArrayList<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < arrayList.size(); i++) {
            JSONObject object = arrayList.get(i);
            String org_id = object.getString("org_id");
            if (org_id.equals(org)) {
                back.add(object);
            }
        }
        return back;
    }

    private JSONObject createScoreDet(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String config_id = "";
            String org_id = "";
            String detail = "";
            String file_id = "";
            double score = 0.00;
            String time = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                return ErrNo.set(490005);
            }
            if (data.containsKey("config_id") && data.getString("config_id").length() > 0) {
                config_id = data.getString("config_id");
            } else {
                return ErrNo.set(490005);
            }
            if (data.containsKey("org_id") && data.getString("org_id").length() > 0) {
                org_id = data.getString("org_id");
            } else {
                return ErrNo.set(490005);
            }
            if (data.containsKey("detail") && data.getString("detail").length() > 0) {
                detail = data.getString("detail");
            }
            if (data.containsKey("file_id") && data.getString("file_id").length() > 0) {
                file_id = data.getString("file_id");
            }

            if (data.containsKey("score") && data.getString("score").length() > 0) {
                score = data.getDouble("score");
            } else {
                return ErrNo.set(490005);
            }
            if (data.containsKey("time") && data.getString("time").length() > 0) {
                time = data.getString("time");
            } else {
                return ErrNo.set(490005);
            }


            String sql = "";
            sql = "select * from kh_det where config_id = '" + config_id + "' and org_id = '" + org_id + "' and time " +
                    "= '" + time + "' and isdelete = '1'";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                for (JSONObject object : list) {
                    String id = object.getString("id");
                    String sqls =
                            "update kh_det set isdelete =2,delete_user='" + create_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
                    mysql.update(sqls);
                }
            }

            sql = "insert kh_det(config_id,org_id,detail,file_id,score,time,create_user,create_time,isdelete) values" +
                    "('" + config_id + "','" + org_id + "','" + detail + "','" + file_id + "','" + score + "','" + time + "','" + create_user + "','" + create_time + "','1') ";
            logger.warn(sql);
            mysql.update(sql);

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-createScoreDet", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(490006);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject updateScoreDet(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String sql = "";
            String sqls = "";
            String id = "";
            String detail = "";
            String file_id = "";
            String score = "0.00";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(490009);
            }

            if (data.containsKey("detail")) {
                detail = data.getString("detail");
                sql = sql + "detail = '" + detail + "',";
            }
            if (data.containsKey("file_id")) {
                file_id = data.getString("file_id");
                sql = sql + "file_id = '" + file_id + "',";
            }
            if (data.containsKey("score")) {
                score = data.getString("score");
                sql = sql + "score = '" + score + "',";
            }

            if (sql.length() > 0) {

                sqls = "update kh_det set " + sql.substring(0, sql.length() - 1) + " where id = '" + id + "'";
                logger.warn(sqls);
                mysql.update(sqls);

            } else {
                return ErrNo.set(490009);
            }


            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-updateScoreDet", userlog.TYPE_OPERATE, remoteAddr);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(490010);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject deleteScoreDet(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(490007);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(490007);
        }

        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update kh_det set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
//            UserLog.log(mysql, opt_user, "删除字典", UserLog.TYPE_DELETE, remoteAddr,"/dict","");
//            RIUtil.dicts.remove(id);

            // 添加日志
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-deleteScoreDet", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(490008);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }


    private static JSONObject getScoreByConfigId(List<JSONObject> arrayList, String id) {

        for (int i = 0; i < arrayList.size(); i++) {
            JSONObject object = arrayList.get(i);
            String configId = object.getString("config_id");
            if (configId.equals(id)) {
                return object;
            }
        }

        return null;
    }


    private JSONObject getUserInfo(TNOAHttpRequest request) {
        InfoModelHelper mysql = null;
        JSONObject uu = null;
        try {
            mysql = InfoModelPool.getModel();
            String token = request.getHeader("token");
            String unUrl = TNOAConf.get("HttpServ", "uni_url") + "/token";

            String back = HttpConnection.post_token(unUrl, token, new JSONObject(),
                    request.getRequestParams().getString("X-Real-IP"));
            JSONObject ret = JSONObject.parseObject(back);
            if (ret.containsKey("errno") && ret.getInteger("errno") == 0) {
                JSONObject dd = ret.getJSONObject("data");
                String police_id = dd.getString("police_id");

                JSONArray roleid = dd.getJSONArray("role_id");
                JSONObject org = dd.getJSONArray("organization").getJSONObject(0);

                String sql = "select * from user where police_id='" + police_id + "'";
                List<JSONObject> list = mysql.query(sql);
                if (list.size() != 1) {
                    logger.warn("200014");
                    return ErrNo.set(200014);
                } else {

                    int status = list.get(0).getInteger("status");
                    if (status == 3) {
                        logger.warn("200012");
                        return ErrNo.set(200012);
                    }
                }

                uu = list.get(0);

                uu.put("role_id", roleid);
                uu.put("organization", org);

                return uu;
            } else {
                logger.error(String.valueOf(ret));
                return ret;
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(490004);
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }


}
