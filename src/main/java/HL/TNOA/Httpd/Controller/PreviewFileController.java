package HL.TNOA.Httpd.Controller;

import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.Lib.TNOAConf;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

@RestController
public class PreviewFileController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());

    @RequestMapping(method = {RequestMethod.GET}, path = {"/preview"})

    public void previewfile(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // /download?file=0.jpg&url=http%3A%2F%2F192.168.10.103%3A1198%2FzipFIYmgGQZQ.jpg
        Map<String, String[]> map = request.getParameterMap();

        String id = map.get("id")[0];
        String big = "0";
        try {
            big = map.get("big")[0];
        } catch (Exception ex) {

        }
        String userid = "";
        try {
            userid = map.get("user_id")[0];
        } catch (Exception ex) {

        }
        String token = "";
        try {
            token = map.get("token")[0];
            String uid = "";

            System.out.println(uid);
            if (uid == null) {
                return;
            }
        } catch (Exception ex) {
        }

        // logger.warn("id->" + id);
        //  logger.warn("user_id", userid);
        //  logger.warn("token", token);
        InfoModelHelper info = null;

        try {
            info = InfoModelPool.getModel();
            String online = RIUtil.IdToName(userid, info, "online", "user");
            String userName = RIUtil.IdToName(userid, info, " name", "user");
            if (online.equals("1") || token.length() > 0) {

                String sql = "select * from upload where id='" + id + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = null;

                xJsonObject = result.get(0);

                // if (request.getHeader("User-Agent").toUpperCase().indexOf("MSIE")>0) {
                // response.setHeader("Content-Disposition", "attachment;"+"filename="+new
                // String(filename.getBytes("GBK"),"ISO8859-1"));
                // }else {//firefox、chrome、safari、opera
                // response.setHeader("Content-Disposition", "attachment;"+"filename="+new
                // String(filename.getBytes("UTF8"),"ISO8859-1"));
                // }
                // filename=new String(filename.getBytes("UTF-8"),"UTF-8");
                String file_path = xJsonObject.getString("file_path");
                int nas_id = xJsonObject.getIntValue("nas_id");
                String file_name = xJsonObject.getString("file_name");

                String local_path = TNOAConf.get("file", "img_path");
                int type = xJsonObject.getIntValue("type");


                if (big.equals("1")) {
                    file_path = file_path + "big/";
                    String path = local_path + file_path + file_name;
                    if (!new File(path).exists()) {
                        file_path = file_path.replace("big/", "");
                    }
                }
                String path = local_path + file_path + file_name;
                Thread.sleep(1000 * 2);

                if (path.endsWith(".png") || path.endsWith(".jpg") || path.endsWith(".jpeg") || path.endsWith(".PNG") || path.endsWith(".JPG")) {
                    // path = AddWaterMark(userName, local_path + file_path, file_name);
                    //logger.warn("waterMark->" + path);
                }

                // 设置下载头
                // response.addHeader("Content-Disposition", "attachment;filename="+filename);
                // response.setContentType("multipart/form-data");
                response.setHeader("Accept-Ranges", "bytes");
                if (type != 999) {
                    file_name = StringToURL(file_name);
                    String filePName = file_path + file_name;
                    String endPoint = TNOAConf.get("HttpServ", "uni_url").replace("tyyh", "tymh");
                    ;

                    String url = endPoint + "/images/" + filePName;
                    response.sendRedirect(url);
                } else {//obs获取
                    file_name = StringToURL(file_name);
                    String filePName = "hl/" + file_path + file_name;
                    String endPoint = "http://************:50101/obs-qjjc-tyyh";
                   /* String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                    String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                    String bucketName = "obs-qjjc-tyyh";
                    ObsClient obsClient = new ObsClient(ak, sk, endPoint);

                    logger.warn("obs.fileName-->" + filePName);
                    ObsObject obsObj = obsClient.getObject(bucketName, filePName);
                    System.out.println("Obj Content:");
                    InputStream input = obsObj.getObjectContent();
                    byte[] b = new byte[1024];
                    //ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    OutputStream bos = new BufferedOutputStream(response.getOutputStream());
                    try {
                        int len;
                        while ((len = input.read(b)) != -1) bos.write(b, 0, len);
                        //System.out.println(new String(bos.toByteArray()));
                        bos.close();
                        input.close();
                    } catch (Exception e) {
                        logger.error(Lib.getTrace(e));
                    }
*/
                    String url = endPoint + "/" + filePName;
                    response.sendRedirect(url);
                }
            }

        } catch (Exception e) {
            // logger.error(Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(info);

        }

    }

    private String AddWaterMark(String name, String path, String fileName) {

        try {
            File file = new File(path + fileName);
            Image src = ImageIO.read(file);
            int weidth = src.getWidth(null);
            int height = src.getHeight(null);
            //logger.warn(weidth + "-" + height);

            int fontSize = (int) (weidth * 0.05 / 2 + height * 0.05 / 2);

            int fwid = (int) (fontSize * 4);
            String nameWater = TNOAConf.get("file", "img_path") + "namew/" + name + "_" + fontSize + "_water.png";

            if (!new File(nameWater).exists()) {

                int heigth = fwid;
                //logger.warn(String.valueOf(fontSize));
                Font font = new Font("宋体", Font.ROMAN_BASELINE, fontSize);//字体

                BufferedImage bi1 = waterMarkByText(fwid, heigth, name, Color.GRAY, font, -30d, 0.5f);
                //给图片添加文字水印//BufferedImage bi = waterMarkByText(width, heigth, "测试aB~,", Color.GRAY, -30d,//0.2f);
                // 给图片添加文字水印//BufferedImage bi2 = waterMarkByText(width, heigth, "测试aB~,");//给图片添加文字水印//BufferedImage
                // bi3 = waterMarkByText("测试aB~,");

                try {
                    ImageIO.write(bi1, "png",
                            new File(TNOAConf.get("file", "img_path") + "namew/" + name + "_" + fontSize + "_water" + ".png"));//写入文件

                    //System.out.println(Color.decode("#dcdcdc"));

                } catch (Exception e) {
                    e.printStackTrace();

                }
            }
            if (!new File(path + name + fileName).exists()) {
                markImageByIcon(nameWater, path + fileName, path + name + fileName, 0, weidth, height, fwid);
            }
            return path + name + fileName;
        } catch (Exception ex) {
            //System.out.println(ex);
            return path + fileName;
        }
    }

    public static BufferedImage waterMarkByText(int width, int heigth, String text, Color color, Font font,
                                                double degree, float alpha) {

        BufferedImage buffImg = new BufferedImage(width, heigth, BufferedImage.TYPE_INT_RGB);
        /**2、得到画笔对象*/
        Graphics2D g2d = buffImg.createGraphics();//---------- 增加下面的代码使得背景透明 -----------------

        buffImg = g2d.getDeviceConfiguration()

                .createCompatibleImage(width, heigth, Transparency.TRANSLUCENT);

        g2d.dispose();

        g2d = buffImg.createGraphics();//---------- 背景透明代码结束 -----------------//设置对线段的锯齿状边缘处理

        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION,

                RenderingHints.VALUE_INTERPOLATION_BILINEAR);//把源图片写入//g2d.drawImage(srcImg.getScaledInstance(srcImg
        // .getWidth(null),//srcImg.getHeight(null), Image.SCALE_SMOOTH), 0, 0,null);//设置水印旋转


        //注意rotate函数参数theta，为弧度制，故需用Math.toRadians转换一下//以矩形区域中央为圆心旋转

        g2d.rotate(Math.toRadians(degree), (double) buffImg.getWidth() / 2,

                (double) buffImg.getHeight() / 2);

        //设置颜色

        g2d.setColor(color);//设置 Font

        g2d.setFont(font);//设置透明度:1.0f为透明度 ，值从0-1.0，依次变得不透明

        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha));//获取真实宽度

        float realWidth = getRealFontWidth(text);
        float fontSize = font.getSize();
        //计算绘图偏移x、y，使得使得水印文字在图片中居中//这里需要理解x、y坐标是基于Graphics2D.rotate过后的坐标系

        float x = 0.5f * width - 0.5f * fontSize * realWidth;
        float y = 0.5f * heigth + 0.5f * fontSize;//取绘制的字串宽度、高度中间点进行偏移，使得文字在图片坐标中居中

        g2d.drawString(text, x, y);//释放资源

        g2d.dispose();//System.out.println("添加水印文字完成!");

        return buffImg;

    }

    private static float getRealFontWidth(String text) {
        int len = text.length();
        float width = 0f;
        for (int i = 0; i < len; i++) {
            if (text.charAt(i) < 256) {
                width += 0.5f;

            } else {
                width += 1.0f;

            }

        }
        return width;

    }

    /**
     * 给图片添加水印图片、可设置水印图片旋转角度
     *
     * @param iconPath   水印图片路径
     * @param srcImgPath 源图片路径
     * @param targerPath 目标图片路径
     * @param degree     水印图片旋转角度
     */
    public static void markImageByIcon(String iconPath, String srcImgPath, String targerPath, Integer degree,
                                       int weidth, int height, int fw) {
        OutputStream os = null;
        try {

            Image srcImg = ImageIO.read(new File(srcImgPath));

            BufferedImage buffImg = new BufferedImage(srcImg.getWidth(null), srcImg.getHeight(null),
                    BufferedImage.TYPE_INT_RGB);

            // 1、得到画笔对象
            Graphics2D g = buffImg.createGraphics();

            // 2、设置对线段的锯齿状边缘处理
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);

            g.drawImage(srcImg.getScaledInstance(srcImg.getWidth(null), srcImg.getHeight(null), Image.SCALE_SMOOTH),
                    0, 0, null);


            // 4、水印图片的路径 水印图片一般为gif或者png的，这样可设置透明度
            ImageIcon imgIcon = new ImageIcon(iconPath);

            // 5、得到Image对象。
            Image img = imgIcon.getImage();

            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, 1));

            // 6、水印图片的位置
            int x = 2;
            int y = 2;
            while (x <= weidth && y <= height) {

                g.drawImage(img, x, y, null);
                x = (int) (x + fw * 1.5);

                if (x >= weidth) {
                    y = (int) (y + fw * 1.5);
                    x = 2;
                }
                if (y >= height) {
                    break;
                }
            }

            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
            // 7、释放资源
            g.dispose();

            // 8、生成图片
            os = new FileOutputStream(targerPath);
            ImageIO.write(buffImg, "JPG", os);

            System.out.println("图片完成添加水印图片");

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != os) os.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private static String StringToURL(String s) {

        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            if (c >= 0 && c <= 255) {
                sb.append(c);
            } else {
                byte[] b;
                try {
                    b = String.valueOf(c).getBytes("utf-8");
                } catch (Exception ex) {
                    System.out.println(ex);
                    b = new byte[0];
                }
                for (int j = 0; j < b.length; j++) {
                    int k = b[j];
                    if (k < 0) k += 256;
                    sb.append("%" + Integer.toHexString(k).toUpperCase());
                }
            }
        }
        return sb.toString();

    }
}
