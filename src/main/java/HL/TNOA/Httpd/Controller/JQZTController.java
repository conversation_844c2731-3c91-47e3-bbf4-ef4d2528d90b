package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@RestController
public class JQZTController {
    private static Logger logger = LoggerFactory.getLogger(JQZTController.class);
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/jqzt"})
    @PassToken
    public JSONObject get_dict(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        InfoModelHelper mysql = null;
        String token = request.getHeader("token");
        try {

            mysql = request.openInfoImpl();
            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("get_gk")) {
                    return getGK(data, mysql);
                } else if (opt.equals("get_zdjq")) {
                    return getZDJQ(data);
                } else if (opt.equals("get_gfdfqy")) {
                    return GetGFDF(data);
                } else if (opt.equals("get_jq_loc")) {
                    return GetJQLoc(data);
                } else if (opt.equals("get_jq_total")) {
                    return GetJQTotal(data);
                } else if (opt.equals("get_mgjq")) {

                    return GetMGDet(data);
                } else if (opt.equals("get_gfbw")) {
                    return GetGFBW(data);
                } else if (opt.equals("get_qcry_list")) {
                    return GetQCRY(data);
                } else if (opt.equals("get_wcnr_list")) {
                    return GETwcnr(data);
                } else if (opt.equals("get_zddw")) {
                    return GetZDDW(data);
                } else if (opt.equals("get_qcry_area")) {
                    return GetQCRY_Area(data);
                } else if (opt.equals("get_point_loc")) {
                    return GetPointLoc(data);
                } else {
                    return ErrNo.set(505003);
                }
            } else {
                return ErrNo.set(505003);
            }
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }


    private JSONObject GetFW(int orgType, String unit, long t, int page, int limit, String point) {
        JSONObject data = new JSONObject();
        try {


            return data;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        }
    }

    private JSONObject GetOkHttpBT(JSONObject data, String lm, String token) {
        try {
            String url = data.getString("dz").replace("$token$", token);
            int page = data.getInteger("page");
            int limit = data.getInteger("limit");
            String unit = data.getString("unit");
            JSONObject dets = new JSONObject();
            JSONObject val = new JSONObject();
            val.put("curr", page);
            val.put("limit", limit);
            dets.put("page", val);


            val = new JSONObject();
            val.put("zzjgdm", unit);
            val.put("kssj", "");
            val.put("jssj", "");
            val.put("lm", lm);

            dets.put("params", val);
            dets.put("isQCount", true);

            OkHttpClient client = new OkHttpClient().newBuilder().build();

            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, dets.toString());
            Request request = new Request.Builder().url(url).method("POST", body).addHeader("Content-Type",
                    "application/json").build();
            Response response = client.newCall(request).execute();
            String res = response.body().string();
            JSONObject resj = JSONObject.parseObject(res);
            resj.put("code", resj.getString("status"));
            return resj;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            JSONObject back = new JSONObject();
            back.put("code", 301);
            back.put("message", Lib.getTrace(ex));
            return back;
        }
    }


    private JSONArray GetHeads(String headN, String hdKeys) {
        JSONArray rets = new JSONArray();
        String[] hdns = headN.split(",");
        String[] hdks = hdKeys.split(",");

        for (int i = 0; i < hdks.length; i++) {
            JSONObject det = new JSONObject();
            String key = hdks[i];
            String value = hdns[i];
            det.put("key", key);
            det.put("value", value);
            rets.add(det);

        }
        return rets;
    }

    public static JSONObject GetPointLoc(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String point = "";

        OracleHelper ora_gl = null;
        MysqlHelper my143 = null;
        String sql = "";
        String csql = "";
        String table = "";
        List<JSONObject> list = null;
        int page = data.getInteger("page");
        int limit = data.getInteger("limit");


        try {
            ora_gl = new OracleHelper("ora_bk_gl");
            my143 = new MysqlHelper("mysql_zxqc");

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                String type = uone.getString("type");

                if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    // type = "25";
                    csql = " and SSZRQ LIKE'" + unit.substring(0, 6) + "%'";
                } else if (type.equals("25")) {
                    csql = " and SSZRQ LIKE '" + unit.substring(0, 8) + "%'";
                } else if (type.equals("26")) {
                    csql = " and SSZRQ = '" + unit + "'";
                }
            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("point") && data.getString("point").length() > 0) {
                point = data.getString("point");
                point = point.replace("160-", "");
                if (point.startsWith("2")) {
                    table = " CZQJ_YBDS.V_POINT_FW ";
                } else if (point.startsWith("3")) {
                    table = " CZQJ_YBDS.V_POINT_RK ";
                } else if (point.startsWith("1")) {
                    table = " CZQJ_YBDS.V_POINT_DZ ";
                } else if (point.startsWith("4")) {
                    table = " point_dw";
                } else {
                    return ErrNo.set(505001);
                }

            } else {
                return ErrNo.set(505001);

            }

            int count = 0;

            if (point.startsWith("4")) {

                sql = "select * from " + table + " WHERE JCLX_XL='" + point + "' " + csql + " limit " + (page - 1) * limit + "," + limit;
                logger.warn(sql);
                list = my143.query(sql);
                if (list.size() > 0) {
                    sql = "select count(*) as count from " + table + " WHERE JCLX_XL='" + point + "' " + csql;
                    logger.warn(sql);
                    count = my143.query_count(sql);
                }
            } else {
                sql = "select * from " + table + " WHERE JCLX_XL='" + point + "' " + csql + " offset " + (page - 1) * limit + " rows fetch " + "next " + limit + " rows only";
                logger.warn(sql);
                list = ora_gl.query(sql);
                if (list.size() > 0) {
                    sql = "select count(1) as count from " + table + " WHERE JCLX_XL='" + point + "' " + csql;
                    count = ora_gl.query_count(sql);
                }
            }
            back.put("count", count);
            if (count > 0) {
                back.put("data", list);
            } else {
                back.put("data", new JSONArray());
            }


            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_gl.close();
        }
    }

    private JSONObject GetQCRY_Area(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String type = "";
        OracleHelper ora_hl = null;
        String csql = "";
        try {
            String end_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String start_date = RIUtil.GetNextDate(end_date, -6);

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                type = uone.getString("type");

                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    type = "23";
                    // csql = " and JJBH LIKE'" + unit.substring(0, 6) + "%'";
                } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    // type = "25";
                    csql = " and XZD_ZRQ LIKE'" + unit.substring(0, 6) + "%'";
                } else if (type.equals("25") || type.equals("26")) {
                    csql = " and XZD_ZRQ LIKE '" + unit.substring(0, 8) + "%'";
                } else if (type.equals("26")) {
                    csql = " and XZD_ZRQ = '" + unit + "'";
                }
            } else {
                return ErrNo.set(505001);
            }

            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date");
            }
            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
            }


            ora_hl = new OracleHelper("ora_hl");
            String sql = "select count(JJBH) as count ,SUBSTR(GMSFHM,1,6) AS CODE from HL.V_QCRY where SUBSTR(GMSFHM,"
                    + "1,4)" + "!='3204' and " + "  CJSJ>='" + start_date + " 00:00:00.0' AND CJSJ<='" + end_date +
                    " " + "23:59:59.0' " + csql + "GROUP BY " + "SUBSTR(GMSFHM,1,6) " + "ORDER BY COUNT DESC";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            if (list.size() > 0) {
                back.put("data", RelaSQ(list, 5));
            } else {
                back.put("data", new JSONArray());
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private JSONObject GetZDDW(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String type = "";
        OracleHelper ora_hl = null;
        String csql = "";
        try {
            String end_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String start_date = RIUtil.GetNextDate(end_date, -6);

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                type = uone.getString("type");

                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    type = "23";
                    // csql = " and TYPE=23 ";
                } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    // type = "25";
                    csql = " and FJ ='" + unit.substring(0, 6) + "000000'";
                } else if (type.equals("25")) {
                    csql = " and PCS ='" + unit.substring(0, 8) + "0000'";
                } else if (type.equals("26")) {
                    csql = " and ZRQ= '" + unit + " '";
                }
            } else {
                return ErrNo.set(505001);
            }

            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date");
            }
            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
            }


            ora_hl = new OracleHelper("ora_hl");
            String sql =
                    "select count(JJBH) as count ,JGBH from HL.DSJ_JQ where TYPE=1 AND CJSJ01>='" + start_date + " " + "00:00:00.0' AND CJSJ01<='" + end_date + " 23:59:59.0' " + csql + " " + "GROUP BY JGBH " + "order BY " + "COUNT DESC";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            if (list.size() > 0) {
                back.put("data", Reladw(list, 5));
            } else {
                back.put("data", new JSONArray());
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private JSONObject GETwcnr(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String type = "";
        OracleHelper ora_hl = null;
        String csql = "";
        try {
            String end_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String start_date = RIUtil.GetNextDate(end_date, -6);

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                type = uone.getString("type");

                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    type = "23";
                    // csql = " and JJBH LIKE'" + unit.substring(0, 6) + "%'";
                } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    // type = "25";
                    csql = " and XGLDW_PCSDM ='" + unit.substring(0, 6) + "000000'";
                } else if (type.equals("25")) {
                    csql = " and XGLDW_PCSDM ='" + unit + "'";
                } else if (type.equals("26")) {
                    csql = " and XGLDW_GAJGJGDM ='" + unit + "'";
                }
            } else {
                return ErrNo.set(505001);
            }

            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date");
            }
            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
            }
            start_date = start_date.replace("-", "");


            end_date = end_date.replace("-", "");


            ora_hl = new OracleHelper("ora_hl");
            String sql =
                    "select XM ,GMSFHM,GLJB_MC,RSYY,RSSJ,SFLG_PDBZ,IMG from HL.DATA_WCNRRSSC where " + " RSSJ" +
                            ">='" + start_date + "000000" + "' AND " + "RSSJ<='" + end_date + "235959' " + csql + " " + "ORDER " + "BY RSSJ DESC";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            if (list.size() > 0) {
                back.put("data", list);
            } else {
                back.put("data", new JSONArray());
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    public static JSONObject GetQCRY(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String type = "";
        OracleHelper ora_hl = null;
        String csql = "";
        try {
            String end_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String start_date = RIUtil.GetNextDate(end_date, -6);

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                type = uone.getString("type");

                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    type = "23";
                    // csql = " and JJBH LIKE'" + unit.substring(0, 6) + "%'";
                } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    // type = "25";
                    csql = " and XZD_ZRQ LIKE'" + unit.substring(0, 6) + "%'";
                } else if (type.equals("25") || type.equals("26")) {
                    csql = " and XZD_ZRQ LIKE '" + unit.substring(0, 8) + "%'";
                } else if (type.equals("26")) {
                    csql = " and XZD_ZRQ = '" + unit + "'";
                }
            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {
                String cjlb = data.getString("cjlb");

                if (cjlb.endsWith("0000")) {
                    cjlb = cjlb.substring(0, cjlb.length() - 4);
                    csql = csql + " and cjlb like '" + cjlb + "%' ";
                } else if (cjlb.endsWith("00")) {
                    cjlb = cjlb.substring(0, cjlb.length() - 2);
                    csql = csql + " and cjlb like '" + cjlb + "%' ";
                } else {

                    csql = csql + " and cjlb='" + cjlb + "' ";
                }
            }

            if (data.containsKey("rylx") && data.getString("rylx").length() > 0) {
                String rylx = data.getString("rylx");
                csql = csql + " and rylx='" + rylx + "' ";
            }

            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date");
            }
            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
            }


            ora_hl = new OracleHelper("ora_hl");
            String sql =
                    "select count(GMSFHM) as count ,GMSFHM from HL.JQ_SJRY where gmsfhm is not null and gmsfhm " +
                            "!='null'" + " and  " + "CJSJ>='" + start_date + " 00:00" + ":00'" + " AND " + "CJSJ<='" + end_date + " 23:59:59' " + csql + " " + "GROUP BY " + "GMSFHM " + "ORDER " + "BY" + " COUNT DESC";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            if (list.size() > 0) {
                back.put("data", RelaPerSon(list, ora_hl));
            } else {
                back.put("data", new JSONArray());
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private static List<JSONObject> RelaPerSon(List<JSONObject> list, OracleHelper ora_hl) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);

            int count = one.getInteger("COUNT");
            String GMSFHM = one.getString("GMSFHM");
            try {
                if (count > 2) {
                    String sql =
                            "select CJSJ,IMG,XM,jjbh,rylx,bq from HL.JQ_SJRY where GMSFHM ='" + GMSFHM + "'  " +
                                    "order by" + " " + "CJSJ " + "DESC";
                    // logger.warn(sql);
                    List<JSONObject> dets = ora_hl.query(sql);
                    JSONObject d = dets.get(0);
                    String sj = d.getString("CJSJ");
                    String img = d.getString("IMG");
                    one.put("TIME", sj);
                    one.put("IMG", img);
                    one.put("XM", d.getString("XM"));
                    String jqs = "";
                    String rylxs = "";
                    for (int a = 0; a < dets.size(); a++) {
                        JSONObject dd = dets.get(a);
                        jqs = jqs + dd.getString("JJBH") + ",";
                        if (!rylxs.contains(dd.getString("RYLX"))) {
                            rylxs = rylxs + dd.getString("RYLX") + ",";
                        }
                    }
                    jqs = jqs.substring(0, jqs.length() - 1);
                    rylxs = rylxs.substring(0, rylxs.length() - 1);
                    one.put("jqs", jqs);
                    one.put("rylx", rylxs);

                    one.put("BQ", d.getString("BQ"));

                    back.add(one);
                }

            } catch (Exception e) {
                logger.error(Lib.getTrace(e));
            }
        }
        return back;
    }

    private JSONObject GetGFBW(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String type = "";
        OracleHelper ora_hl = null;
        String csql = "";
        try {
            String end_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String start_date = RIUtil.GetNextDate(end_date, -6);

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                type = uone.getString("type");

                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    type = "23";
                    // csql = " and TYPE=23 ";
                } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    // type = "25";
                    csql = " and FJ ='" + unit.substring(0, 6) + "000000'";
                } else if (type.equals("25")) {
                    csql = " and PCS= '" + unit.substring(0, 8) + "0000'";
                } else if (type.equals("26")) {
                    csql = " and ZRQ ='" + unit + " '";
                }
            } else {
                return ErrNo.set(505001);
            }

            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date");
            }
            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
            }


            ora_hl = new OracleHelper("ora_hl");
            String sql = "select count(JJBH) as count ,DMDM,DZ from HL.V_JQ_DM where " + " CJSJ01>='" + start_date +
                    " 00:00:00.0' AND CJSJ01<='" + end_date + " 23:59:59.0' " + csql + " " + "GROUP BY DMDM,DZ ORDER "
                    + "BY COUNT DESC";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            if (list.size() > 0) {
                back.put("data", RelaUnit(list, 6));
            } else {
                back.put("data", new JSONArray());
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    public static JSONObject GetMGDet(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String type = "";
        OracleHelper ora_hl = null;
        String csql = "";
        try {
            String end_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String start_date = RIUtil.GetNextDate(end_date, -6);

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                type = uone.getString("type");

                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    type = "23";
                    // csql = " and TYPE=23 ";
                } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    // type = "25";
                    csql = " and FJ ='" + unit.substring(0, 6) + "000000'";
                } else if (type.equals("25")) {
                    csql = " and PCS ='" + unit.substring(0, 8) + "0000'";
                } else if (type.equals("26")) {
                    csql = " and ZRQ ='" + unit + "'";
                }
            } else {
                return ErrNo.set(505001);
            }

            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date");
            }
            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
            }


            ora_hl = new OracleHelper("ora_hl");
            String sql =
                    "select count(JJBH) as count ,concat(substr(BJLX,1,2),'0000') AS CODE from HL.DSJ_JQ where " +
                            "MG=1 AND CJSJ01>='" + start_date + " 00:00:00.0' AND CJSJ01<='" + end_date + " 23:59:59" + ".0' " + csql + " " + "GROUP BY  substr(BJLX,1,2) ORDER BY COUNT DESC";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            if (list.size() > 0) {
                back.put("data", RelaUnit(list, 6));
            } else {
                back.put("data", new JSONArray());
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private JSONObject GetJQTotal(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String type = "";
        OracleHelper ora_hl = null;
        try {
            String end_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String start_date = RIUtil.GetNextDate(end_date, -6);

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                type = uone.getString("type");

                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    type = "320400000000";
                } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    type = unit.substring(0, 6) + "000000";
                } else if (type.equals("25")) {
                    type = unit.substring(0, 8) + "0000";
                } else if (type.equals("26")) {
                    type = unit;
                }
            } else {
                return ErrNo.set(505001);
            }

            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date");
            }
            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
            }


            ora_hl = new OracleHelper("ora_hl");
            String sql = "select SUM(TOTAL) AS TOTAL,SUM(WFFZ) AS WFFZ,SUM(MG) AS MG,SUM(DQ) AS DQ,SUM(RSDQ) AS " +
                    "RSDQ," + "SUM(DQDDC) AS DQDDC,SUM(HZ) AS HZ,SUM(DJDO) AS DJDO,SUM(DZ) AS DZ,SUM(SD) AS SD,SUM" + "(CD)" + " AS CD,sum(ZA) AS ZA,sum(YX) AS YX,SUM(QZ) AS QZ,SUM(JF) as JF,SUM(JB) AS JB,sum(SC) AS SC,SUM(YXJQ) AS YXJQ FROM HL.JQ_LB_STA where SHOW=0 AND \"DATE\">='" + start_date + "' AND " + "\"DATE\"<='" + end_date + "' AND CODE='" + type + "' ";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            if (list.size() > 0) {
                back.put("data", list);
            } else {
                back.put("data", new JSONArray());
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private JSONObject GetJQLoc(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String type = "";
        String sqlT = " and 1=1 ";
        OracleHelper ora_hl = null;
        try {
            String end_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String start_date = RIUtil.GetNextDate(end_date, -6);

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                type = uone.getString("type");

                if (type.equals("21") || type.equals("22") || type.equals("27")) {

                } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    sqlT = " and FJ LIKE '" + unit.substring(0, 6) + "%' ";
                } else if (type.equals("25")) {
                    sqlT = " and PCS LIKE  '" + unit.substring(0, 8) + "%' ";
                } else if (type.equals("26")) {
                    sqlT = " and ZRQ = '" + unit + "' ";
                }
            } else {
                return ErrNo.set(505001);
            }

            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date");
            }
            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
            }

            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                if (type.equals("MG")) {
                    sqlT = sqlT + " and MG=1 ";
                } else if (type.equals("WFFZ")) {
                    sqlT = sqlT + " and CJLB LIKE '50-01%'";
                } else if (type.equals("ZA")) {
                    sqlT = sqlT + " and CJLB LIKE '50-02%'";
                } else if (type.equals("RSDQ")) {
                    sqlT = sqlT + " and (CJLB= '50-020208' OR CJLB='50-011701')";
                } else if (type.equals("DQDDC")) {
                    sqlT = sqlT + " and (CJLB= '50-020209' OR CJLB='50-011709')";
                } else if (type.equals("HZ")) {
                    sqlT = sqlT + " and CJLB LIKE '50-04%'";
                } else if (type.equals("DZ")) {
                    sqlT = sqlT + "  and (CJLB= '50-011805' OR CJLB='50-011806' OR CJLB='50-011807')";
                } else if (type.equals("SC")) {
                    sqlT = sqlT + "  and (CJLB LIKE  '50-0121%' OR CJLB='50-0217%')";
                } else if (type.equals("SD")) {
                    sqlT = sqlT + " and CJLB LIKE '50-0215%'";
                } else if (type.equals("YX")) {

                } else if (type.equals("CD")) {
                    sqlT = sqlT + "  and (CJLB LIKE  '50-0121%' OR CJLB LIKE '50-0217%' or CJLB LIKE '50-0215%')";
                } else if (type.equals("QZ")) {
                    sqlT = sqlT + " and CJLB LIKE '50-05%'";
                } else if (type.equals("JB")) {
                    sqlT = sqlT + " and CJLB LIKE '50-06%'";
                } else if (type.equals("JF")) {
                    sqlT = sqlT + " and CJLB LIKE '50-08%'";
                }
            }


            ora_hl = new OracleHelper("ora_hl");
            String sql =
                    "select JJBH,LAT,LNG,TYPE52,CJSJ01 FROM HL.V_DSJ_JQ_LOC where  CJSJ01>='" + start_date + " " +
                            "00:00:00" + ".0' " + "AND " + "CJSJ01<='" + end_date + " 23:58:59.0' " + sqlT;
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            if (list.size() > 0) {
                back.put("data", RelaInfo_null(list));
            } else {
                back.put("data", new JSONArray());
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private List<JSONObject> RelaInfo_null(List<JSONObject> list) {

        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String lat = one.getString("LAT");
            String type = one.getString("TYPE52");
            String name = "其";
            String fullName = "";
            try {
                name = RIUtil.dicts.get(type).getString("gadm");
                fullName = RIUtil.dicts.get(type).getString("dict_name");
            } catch (Exception ex) {

            }
            one.put("TYPE", name);
            one.put("FULLTYPE", fullName);
            if (lat != null && lat.length() > 2) {
                back.add(one);
            }

        }
        return back;
    }

    private JSONObject GetGFDF(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String type = "";
        String sqlT = " and 1=1 ";
        OracleHelper ora_hl = null;
        try {
            String end_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String start_date = RIUtil.GetNextDate(end_date, -6);

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                type = uone.getString("type");

                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    sqlT = " and TYPE='" + 25 + "' ";
                } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    sqlT = " and TYPE='" + 25 + "'  AND CODE LIKE '" + unit.substring(0, 6) + "%' ";
                } else if (type.equals("25") || type.equals("26")) {
                    sqlT = " and TYPE='" + 26 + "'  AND CODE LIKE '" + unit.substring(0, 8) + "%' ";
                }
            } else {
                return ErrNo.set(505001);
            }

            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date");
            }
            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
            }

            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");

            } else {
                return ErrNo.set(505001);
            }
            String order = "";

            if (type.equals("1")) {
                order = " order by TOTAL DESC ";
            } else {
                order = " order by TOTAL  ";
            }


            ora_hl = new OracleHelper("ora_hl");
            String sql =
                    "select CODE,SUM(TOTAL) AS TOTAL FROM HL.JQ_LB_STA where SHOW=0 AND \"DATE\">='" + start_date +
                            "' AND " + "\"DATE\"<='" + end_date + "' " + sqlT + " GROUP BY CODE " + order;
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            if (list.size() > 0) {
                back.put("data", RelaUnit(list, 5));
            } else {
                back.put("data", new JSONArray());
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private JSONObject getZDJQ(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String type = "";
        OracleHelper ora_hl = null;
        String csql = "";
        try {
            String end_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String start_date = RIUtil.GetNextDate(end_date, -6);

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                type = uone.getString("type");

                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    type = "23";
                    csql = " and TYPE=23 ";
                } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    type = "25";
                    csql = " and TYPE=25 AND CODE LIKE '" + unit.substring(0, 6) + "%'";
                } else if (type.equals("25") || type.equals("26")) {
                    csql = " and TYPE=26 AND CODE LIKE '" + unit.substring(0, 8) + "%'";
                }
            } else {
                return ErrNo.set(505001);
            }

            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date");
            }
            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
            }


            ora_hl = new OracleHelper("ora_hl");
            String sql = "select CODE,SUM(TOTAL) AS TOTAL,SUM(WFFZ) AS WFFZ,SUM(MG) AS MG,SUM(DQ) AS DQ,SUM(RSDQ) AS "
                    + "RSDQ,SUM(DQDDC) AS DQDDC,SUM(HZ) AS HZ,SUM(DJDO) AS DJDO,SUM(DZ) AS DZ,SUM(SD) AS SD,SUM" +
                    "(SC)" + " AS SC ,SUM(ZA) AS ZA,sum(YX) AS YX,SUM(QZ) AS QZ,SUM(JF) as JF,SUM(JB) AS JB,SUM(CD) " + "AS CD,sum(YXJQ) AS YXJQ " + " FROM HL.JQ_LB_STA where SHOW=0 AND \"DATE\">='" + start_date + "' AND " + "\"DATE\"<='" + end_date + "' " + csql + " GROUP BY CODE,INX ORDER BY INX";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            if (list.size() > 0) {
                back.put("data", RelaUnit(list, list.size()));
            } else {
                back.put("data", new JSONArray());
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }
    }

    private static List<JSONObject> RelaUnit(List<JSONObject> list, int count) {

        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            if (one.containsKey("CODE")) {
                String code = one.getString("CODE");
                String name = RIUtil.dicts.get(code).getString("dict_name");
                one.put("NAME", name);
            }
            if (i < count) {
                back.add(one);
            }
        }
        return back;

    }

    private List<JSONObject> Reladw(List<JSONObject> list, int count) {

        List<JSONObject> back = new ArrayList<>();
        logger.warn("-->" + list.size());
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            if (one.containsKey("JGBH")) {
                String code = one.getString("JGBH");
                GaussHelper gs_hl = null;
                try {
                    gs_hl = new GaussHelper("gauss_hl");
                    String sql = "select dwmc from qjjc.CZJG_JBXX where JGBH='" + code + "'";
                    // logger.warn(sql);
                    String name = gs_hl.query_one(sql, "dwmc");
                    one.put("NAME", name);
                } catch (Exception ex) {
                    logger.error(Lib.getTrace(ex));
                } finally {
                    gs_hl.close();
                }

            }
            if (i < count) {
                back.add(one);
            } else {
                break;
            }
        }
        logger.warn(String.valueOf(back));
        return back;

    }

    private List<JSONObject> RelaSQ(List<JSONObject> list, int count) {
        HashMap<String, String> dicts = new HashMap<>();
        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");

            String sql = "select id,dict_name from dict where type=126";
            List<JSONObject> dis = mysql.query(sql);
            for (int i = 0; i < dis.size(); i++) {
                JSONObject one = dis.get(i);
                String id = one.getString("id");
                String dict_name = one.getString("dict_name");

                dicts.put(id, dict_name);

            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            mysql.close();
        }
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String code = "126-" + one.getString("CODE");
            // System.out.println(code);
            one.put("CODE_NAME", dicts.get(code));
            if (i < count) {
                back.add(one);
            }
        }
        return back;

    }

    private JSONObject getGK(JSONObject data, InfoModelHelper mysql) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String sqlT = " and 1=1 ";

        OracleHelper ora_hl = null;
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            JSONObject uone = RIUtil.dicts.get(unit);
            String type = uone.getString("type");

            if (type.equals("21") || type.equals("22") || type.equals("27")) {
                sqlT = " and CODE='320400000000'";
            } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                sqlT = " and CODE ='" + unit.substring(0, 6) + "000000'";
            } else if (type.equals("25")) {
                sqlT = " and CODE = '" + unit.substring(0, 8) + "0000'";
            } else {
                sqlT = " and CODE = '" + unit + "'";
            }
        } else {
            return ErrNo.set(505001);
        }
        try {
            String tjrq = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            tjrq = RIUtil.GetNextDate(tjrq, -1);
            tjrq = tjrq.substring(0, 10).replace("-", "");
            ora_hl = new OracleHelper("ora_hl");
            String sql = "select *  from " + "HL.STA_GK where 1=1 " + sqlT;
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            if (list.size() > 0) {
                back.put("data", list.get(0));
            } else {
                back.put("data", new JSONObject());
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(505002);
        } finally {
            ora_hl.close();
        }
    }

}
