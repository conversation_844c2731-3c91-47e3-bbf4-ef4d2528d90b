package HL.TNOA.Httpd.Controller;/*
package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
public class ClassModelController {
    private Logger logger = LoggerFactory.getLogger(ClassModelController.class);
    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/class_model"})
    @PassToken
    public JSONObject get_class_model(TNOAHttpRequest request) throws Exception {
        logger.warn("class_model--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_class_model")) {
                return getClassModel(data);
            } else if (opt.equals("create_class_model")) {
                return createClassModel(data, request.getRemoteAddr());
            } else if (opt.equals("update_class_model")) {
                return updateClassModel(data, request.getRemoteAddr());
            } else if (opt.equals("delete_class_model")) {
                return updateClassModel(data, request.getRemoteAddr());
            } else {
                return ErrNo.set(436009);
            }
        } else {
            return ErrNo.set(436009);
        }
    }

    //******CREATE*******
    private JSONObject createClassModel(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String accepter = "";
            String class_id = "";

            String create_user = "";
            String create_time = sdf.format(new Date());
            String isdelete = "1";
            if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
                accepter = data.getString("accepter");
            } else {
                return ErrNo.set(436002);
            }
            if (data.containsKey("class_id") && data.getString("class_id").length() > 0) {
                class_id = data.getString("class_id");
            } else {
                return ErrNo.set(436002);
            }

            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
            } else {
                return ErrNo.set(436002);
            }

            String sqls = "insert class_model (accepter,class_id,start_date,create_user,create_time,isdelete)values('" + accepter + "','" + class_id + "','','" + create_user + "','" + create_time + "','" + isdelete + "')";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建排班模板", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(436001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private JSONObject getClassModel(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String accepter = "";
            String class_id = "";

            String create_user = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
                accepter = data.getString("accepter");
                sql = sql + " accepter='" + accepter + "' and ";
            }
            if (data.containsKey("class_id") && data.getString("class_id").length() > 0) {
                class_id = data.getString("class_id");
                sql = sql + " class_id='" + class_id + "' and ";
            }

            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' and ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls = "select * from class_model where 1=1 and " + sql + " isdelete=1  limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select id from class_model where 1=1 and " + sql + " isdelete=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(436005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String accepter = one.getString("accepter");
            if (accepter.length() > 0) {
                one.put("accepter", RIUtil.UseridToNames(RIUtil.StringToList(accepter), mysql, 0));
            }

            String create_user = one.getString("create_user");
            one.put("create_user", RIUtil.UseridToName(create_user, mysql, 0));


            back.add(one);
        }
        return back;
    }
//******UPDATE*******

    private JSONObject updateClassModel(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String accepter = "";
            String class_id = "";

            String create_user = "";
            String create_time = "";
            String opt_user = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            }
            if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
                accepter = data.getString("accepter");
                sql = sql + " accepter='" + accepter + "' , ";
            }
            if (data.containsKey("class_id") && data.getString("class_id").length() > 0) {
                class_id = data.getString("class_id");
                sql = sql + " class_id='" + class_id + "' , ";
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            String sqls = "update class_model set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新排班模板", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(436003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private JSONObject deleteClassModel(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(436008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(436008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls = "update class_model set isdelete =2,delete_user='" + opt_user + "',delete_time='" + sdf.format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除排班模板", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(436007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }


}
*/
