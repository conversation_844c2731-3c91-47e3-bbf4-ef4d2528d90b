package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.Export.ExportInterface;
import HL.TNOA.Lib.Export.ExportXlsxHelper;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class ZDRYController {
    public static Logger logger = LoggerFactory.getLogger(ZDRYController.class);
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/key_per"})
    @PassToken
    public JSONObject get_zdry(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        InfoModelHelper mysql = null;
        try {

            mysql = request.openInfoImpl();
            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("get_zdry_xqfb")) {
                    return getZdryXq(data);
                } else if (opt.equals("get_zdry_mx")) {
                    return getZDRYMX(data);
                } else if (opt.equals("get_zdry_mx2Tabel")) {
                    return getZDRYMX2Table(data);
                } else {
                    return ErrNo.set(null, 2, "opt错误");
                }
            } else {
                return ErrNo.set(null, 2, "opt错误");
            }
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private JSONArray GetHeads(String headN, String hdKeys) {
        JSONArray rets = new JSONArray();
        String[] hdns = headN.split(",");
        String[] hdks = hdKeys.split(",");

        for (int i = 0; i < hdks.length; i++) {
            JSONObject det = new JSONObject();
            String key = hdks[i];
            String value = hdns[i];
            det.put("key", key);
            det.put("value", value);
            rets.add(det);

        }
        return rets;
    }

    private JSONObject getZDRYMX2Table(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        try {
            JSONObject rets = getZDRYMX(data);

            String headN = "序号,姓名,身份证号,身份属性,管理级别,责任区,现住地名称";
            String keys = "num,XM,GMSFHM,XLMC,GLJBMC,ZRQ,SJJZD_DZMC";
            JSONArray heads = GetHeads(headN, keys);
            JSONArray bodys = new JSONArray();
            int count = 0;

            if (rets.containsKey("errno") && rets.getInteger("errno") == 0) {

                count = rets.getInteger("count");
                JSONArray datas = rets.getJSONArray("data");
                for (int i = 0; i < datas.size(); i++) {
                    JSONObject one = datas.getJSONObject(i);
                    JSONObject det = new JSONObject();

                    String num = String.valueOf(i + 1);
                    JSONObject val = new JSONObject();
                    val.put("value", num);
                    det.put("num", val);

                    String XM = one.getString("XM");
                    val = new JSONObject();
                    val.put("value", XM);
                    det.put("XM", val);

                    String GMSFHM = one.getString("GMSFHM");
                    val = new JSONObject();
                    val.put("value", GMSFHM);
                    JSONObject click = new JSONObject();
                    click.put("type", "jump_zdry");
                    click.put("url", GMSFHM);
                    val.put("click", click);
                    det.put("GMSFHM", val);

                    String XLMC = one.getString("XLMC");
                    val = new JSONObject();
                    val.put("value", XLMC);
                    det.put("XLMC", val);

                    String GLJBMC = one.getString("GLJBMC");
                    val = new JSONObject();
                    val.put("value", GLJBMC);
                    det.put("GLJBMC", val);

                    String ZRQ = one.getString("ZRQ");
                    val = new JSONObject();
                    val.put("value", ZRQ);
                    det.put("ZRQ", val);

                    String SJJZD_DZMC = one.getString("SJJZD_DZMC");
                    val = new JSONObject();
                    val.put("value", SJJZD_DZMC);
                    det.put("SJJZD_DZMC", val);
                    bodys.add(det);
                }
                JSONObject d = new JSONObject();
                d.put("head", heads);
                d.put("body", bodys);
                d.put("count", count);
                back.put("data", d);

                return back;
            } else {
                return rets;
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        }
    }

    private JSONObject getZDRYMX(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String dmdm = "";
        int limit = 20;
        int page = 1;
        String s = "";
        int file_id = -1;
        int isExp = 0;
        MysqlHelper my143 = null;
        try {
            my143 = new MysqlHelper("mysql_zxqc");
            if (data.containsKey("DMDM") && data.getString("DMDM").length() > 0) {
                dmdm = data.getString("DMDM");
            } else {
                return ErrNo.set(null, 2, "缺少小区代码DMDM");
            }
            if (data.containsKey("gljb") && data.getString("gljb").length() > 0) {
                String gljb = data.getString("gljb");
                s = s + " and ZDRYGLJB='" + gljb + "' ";
            }
            if (data.containsKey("xl") && data.getString("xl").length() > 0) {
                String xl = data.getString("xl");
                String sql = "select id from dict  where (type=180 and id='" + xl + "') or (type=181 " + "and " +
                        "father_id='" + xl + "')";

                List<JSONObject> dicts = my143.query(sql);
                String xlsql = " ";
                for (int d = 0; d < dicts.size(); d++) {
                    JSONObject done = dicts.get(d);
                    xlsql = xlsql + " XLGLB like '" + done.getString("id") + "' or ";

                }

                if (xlsql.length() > 2) {
                    xlsql = xlsql.substring(0, xlsql.length() - 3);
                }
                s = s + " and (" + xlsql + ") ";
            }
            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getInteger("isExp");
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                String type = data.getString("type");
                if (type.equals("02")) {
                    s = s + " and TYPE ='" + type + "' ";
                } else if (type.equals("03")) {
                    s = s + " and TYPE ='" + type + "' ";
                } else {
                    s = s + " and (TYPE ='01' or type='04' )";
                }
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }

            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }
            String sql =
                    "select * from static_zdry where dmdm='" + dmdm + "' " + s + " limit " + limit + " offset " + limit * (page - 1);
            logger.warn(sql);
            List<JSONObject> list = my143.query(sql);
            if (list.size() > 0) {
                back.put("data", RelaInf(list));
                sql = "select * from static_zdry where dmdm='" + dmdm + "' " + s;
                list = my143.query(sql);
                back.put("count", list.size());
                if (isExp == 1) {

                    file_id = ExportFile_MX((List<JSONObject>) RelaInf(list));
                }
            } else {
                back.put("data", new ArrayList<>());
                back.put("count", 0);
            }
            back.put("file_id", file_id);

            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            my143.close();
        }


    }

    private Object RelaInf(List<JSONObject> list) {
        List<JSONObject> back = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            try {
                String xl = one.getString("XLGLB").replace("，", ",");
                String xlmc = "";
                String[] xls = xl.split(",");
                for (int x = 0; x < xls.length; x++) {
                    xlmc = xlmc + RIUtil.dicts.get(xls[x]).getString("dict_name") + ",";
                }
                if (xlmc.endsWith(",")) {
                    xlmc = xlmc.substring(0, xlmc.length() - 1);
                }
                one.put("XLMC", xlmc);
            } catch (Exception ex) {
                one.put("XLMC", "");
            }
            String gljb = one.getString("ZDRYGLJB");
            String jbmc = "";
            if (gljb.equals("030000")) {
                jbmc = "管控";

            } else if (gljb.equals("040000")) {
                jbmc = "关注";
            }
            one.put("GLJBMC", jbmc);

            String hjzrq = RIUtil.dicts.get(one.getString("HJZRQ")).getString("remark");
            one.put("ZRQ", hjzrq);
            back.add(one);
        }


        return back;
    }


    public static JSONObject getZdryXq(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "3204";
        String gljb = "";
        String xl = "";

        String s = "";
        MysqlHelper my143 = null;
        int isExp = 0;
        int file_id = -1;
        try {
            my143 = new MysqlHelper("mysql_zxqc");
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject dict = RIUtil.dicts.get(unit);
                String type = dict.getString("type");
                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    unit = "3204";
                } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    unit = unit.substring(0, 6);

                } else if (type.equals("25")) {
                    unit = unit.substring(0, 8);

                } else {
                    unit = unit;
                }
            } else {
                return ErrNo.set(null, 2, "缺少unit");
            }
            if (data.containsKey("gljb") && data.getString("gljb").length() > 0) {
                gljb = data.getString("gljb");
                s = s + " and ZDRYGLJB='" + gljb + "' ";
            }
            if (data.containsKey("xl") && data.getString("xl").length() > 0) {
                xl = data.getString("xl");
                xl = xl.substring(0, xl.length() - 4);
                s = s + " and XLGLB like '%" + xl + "%' ";
            }

            if (data.containsKey("type") && data.getString("type").length() > 0) {
                String type = data.getString("type");
                if (type.equals("02")) {
                    s = s + " and TYPE ='" + type + "' ";
                } else if (type.equals("03")) {
                    s = s + " and TYPE ='" + type + "' ";
                } else {
                    s = s + " and (TYPE ='01' or type='04' )";
                }
            }
            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getInteger("isExp");
            }

            String sql = "select DZMC ,count(gmsfhm) as count,DMDM from static_zdry where HJZRQ like '%" + unit +
                    "%'" + " " + s + " " + "group" + " by dmdm order by count desc limit 500";

            logger.warn(sql);
            List<JSONObject> list = my143.query(sql);
            HashMap<String, JSONObject> tots = new HashMap<>();
            int mark = 0;
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String dmdm = one.getString("DMDM");
                    if (gljb.length() == 0) {
                        one.put("lg", 0);
                        one.put("gz", 0);

                        one.put("fk", 0);
                        one.put("grjd", 0);
                        one.put("sw", 0);
                        one.put("sd", 0);
                        one.put("jsb", 0);
                        one.put("wffz", 0);
                    }
                    tots.put(dmdm, one);
                }


                //管控
                if (gljb.length() == 0) {
                    mark = 1;
                    sql = "select dmdm,count(gmsfhm) as count from   static_zdry where ZDRYGLJB='030000' and HJZRQ " + "like" + " " + "'%" + unit + "%'" + " " + s + " " + "group" + " by dmdm order by " + "count desc";

                    List<JSONObject> lgs = my143.query(sql);
                    if (lgs.size() > 0) {

                        for (int i = 0; i < lgs.size(); i++) {
                            JSONObject one = lgs.get(i);
                            String dmdm = one.getString("DMDM");
                            JSONObject det = new JSONObject();
                            if (tots.containsKey(dmdm)) {
                                det = tots.get(dmdm);
                                det.put("lg", one.getString("count"));
                                tots.put(dmdm, det);
                            }

                        }
                    }
                    //关注
                    sql = "select dmdm,count(gmsfhm) as count from   static_zdry where ZDRYGLJB='040000' and HJZRQ " + "like" + " " + "'%" + unit + "%'" + " " + s + " " + "group" + " by dmdm order by " + "count desc";

                    List<JSONObject> gzs = my143.query(sql);
                    if (lgs.size() > 0) {

                        for (int i = 0; i < gzs.size(); i++) {
                            JSONObject one = gzs.get(i);
                            String dmdm = one.getString("DMDM");
                            JSONObject det = new JSONObject();
                            if (tots.containsKey(dmdm)) {
                                det = tots.get(dmdm);
                                det.put("gz", one.getString("count"));
                                tots.put(dmdm, det);
                            }

                        }
                    }

                    //违法犯罪前科及嫌疑人员 060000000000
                    sql = "select id from dict  where (type=180 and id='060000000000') or (type=181 " + "and " +
                            "father_id='060000000000')";

                    List<JSONObject> dicts = my143.query(sql);
                    String xlsql = " ";
                    for (int d = 0; d < dicts.size(); d++) {
                        JSONObject done = dicts.get(d);
                        xlsql = xlsql + " XLGLB like '" + done.getString("id") + "' or ";

                    }

                    if (xlsql.length() > 2) {
                        xlsql = xlsql.substring(0, xlsql.length() - 3);
                    }

                    sql = "select dmdm,count(gmsfhm) as count from   static_zdry where (" + xlsql + ") and HJZRQ " +
                            "like '%" + unit + "%'" + " " + s + " " + "group" + " by dmdm order by " + "count desc";

                    logger.warn(sql);
                    List<JSONObject> wffzs = my143.query(sql);
                    if (lgs.size() > 0) {

                        for (int i = 0; i < wffzs.size(); i++) {
                            JSONObject one = wffzs.get(i);
                            String dmdm = one.getString("DMDM");
                            JSONObject det = new JSONObject();
                            if (tots.containsKey(dmdm)) {
                                det = tots.get(dmdm);
                                det.put("wffz", one.getString("count"));
                                tots.put(dmdm, det);
                            }

                        }
                    }
                    //涉毒 04
                    sql = "select id from dict  where (type=180 and id='040000000000') or (type=181 " + "and " +
                            "father_id='040000000000')";

                    dicts = my143.query(sql);
                    xlsql = " ";
                    for (int d = 0; d < dicts.size(); d++) {
                        JSONObject done = dicts.get(d);
                        xlsql = xlsql + " XLGLB like '" + done.getString("id") + "' or ";

                    }

                    if (xlsql.length() > 2) {
                        xlsql = xlsql.substring(0, xlsql.length() - 3);
                    }

                    sql = "select dmdm,count(gmsfhm) as count from   static_zdry where (" + xlsql + ") and HJZRQ " +
                            "like '%" + unit + "%'" + " " + s + " " + "group" + " by dmdm order by " + "count desc";

                    logger.warn(sql);
                    List<JSONObject> sds = my143.query(sql);
                    if (lgs.size() > 0) {

                        for (int i = 0; i < sds.size(); i++) {
                            JSONObject one = sds.get(i);
                            String dmdm = one.getString("DMDM");
                            JSONObject det = new JSONObject();
                            if (tots.containsKey(dmdm)) {
                                det = tots.get(dmdm);
                                det.put("sd", one.getString("count"));
                                tots.put(dmdm, det);
                            }

                        }
                    }
                    //个人极端 02
                    sql = "select id from dict  where (type=180 and id='020000000000') or (type=181 " + "and " +
                            "father_id='020000000000')";

                    dicts = my143.query(sql);
                    xlsql = " ";
                    for (int d = 0; d < dicts.size(); d++) {
                        JSONObject done = dicts.get(d);
                        xlsql = xlsql + " XLGLB like '" + done.getString("id") + "' or ";

                    }

                    if (xlsql.length() > 2) {
                        xlsql = xlsql.substring(0, xlsql.length() - 3);
                    }

                    sql = "select dmdm,count(gmsfhm) as count from   static_zdry where (" + xlsql + ") and HJZRQ " +
                            "like '%" + unit + "%'" + " " + s + " " + "group" + " by dmdm order by " + "count desc";

                    logger.warn(sql);
                    List<JSONObject> grjd = my143.query(sql);
                    if (lgs.size() > 0) {

                        for (int i = 0; i < grjd.size(); i++) {
                            JSONObject one = grjd.get(i);
                            String dmdm = one.getString("DMDM");
                            JSONObject det = new JSONObject();
                            if (tots.containsKey(dmdm)) {
                                det = tots.get(dmdm);
                                det.put("grjd", one.getString("count"));
                                tots.put(dmdm, det);
                            }

                        }
                    }
                    //易肇事肇祸净胜障碍人员 05
                    sql = "select id from dict  where (type=180 and id='050000000000') or (type=181 " + "and " +
                            "father_id='050000000000')";

                    dicts = my143.query(sql);
                    xlsql = " ";
                    for (int d = 0; d < dicts.size(); d++) {
                        JSONObject done = dicts.get(d);
                        xlsql = xlsql + " XLGLB like '" + done.getString("id") + "' or ";

                    }

                    if (xlsql.length() > 2) {
                        xlsql = xlsql.substring(0, xlsql.length() - 3);
                    }

                    sql = "select dmdm,count(gmsfhm) as count from   static_zdry where (" + xlsql + ") and HJZRQ " +
                            "like '%" + unit + "%'" + " " + s + " " + "group" + " by dmdm order by " + "count desc";

                    logger.warn(sql);
                    List<JSONObject> jsbs = my143.query(sql);
                    if (lgs.size() > 0) {

                        for (int i = 0; i < jsbs.size(); i++) {
                            JSONObject one = jsbs.get(i);
                            String dmdm = one.getString("DMDM");
                            JSONObject det = new JSONObject();
                            if (tots.containsKey(dmdm)) {
                                det = tots.get(dmdm);
                                det.put("jsb", one.getString("count"));
                                tots.put(dmdm, det);
                            }

                        }
                    }
                    //涉稳重点人员03
                    sql = "select id from dict  where (type=180 and id='030000000000') or (type=181 " + "and " +
                            "father_id='030000000000')";

                    dicts = my143.query(sql);
                    xlsql = " ";
                    for (int d = 0; d < dicts.size(); d++) {
                        JSONObject done = dicts.get(d);
                        xlsql = xlsql + " XLGLB like '" + done.getString("id") + "' or ";

                    }

                    if (xlsql.length() > 2) {
                        xlsql = xlsql.substring(0, xlsql.length() - 3);
                    }

                    sql = "select dmdm,count(gmsfhm) as count from   static_zdry where (" + xlsql + ") and HJZRQ " +
                            "like '%" + unit + "%'" + " " + s + " " + "group" + " by dmdm order by " + "count desc";

                    logger.warn(sql);
                    List<JSONObject> sws = my143.query(sql);
                    if (lgs.size() > 0) {

                        for (int i = 0; i < sws.size(); i++) {
                            JSONObject one = sws.get(i);
                            String dmdm = one.getString("DMDM");
                            JSONObject det = new JSONObject();
                            if (tots.containsKey(dmdm)) {
                                det = tots.get(dmdm);
                                det.put("sw", one.getString("count"));
                                tots.put(dmdm, det);
                            }

                        }
                    }
                    //F17   01
                    sql = "select id from dict  where (type=180 and id='010000000000') or (type=181 " + "and " +
                            "father_id='010000000000')";

                    dicts = my143.query(sql);
                    xlsql = " ";
                    for (int d = 0; d < dicts.size(); d++) {
                        JSONObject done = dicts.get(d);
                        xlsql = xlsql + " XLGLB like '" + done.getString("id") + "' or ";

                    }

                    if (xlsql.length() > 2) {
                        xlsql = xlsql.substring(0, xlsql.length() - 3);
                    }

                    sql = "select dmdm,count(gmsfhm) as count from   static_zdry where (" + xlsql + ") and HJZRQ " +
                            "like '%" + unit + "%'" + " " + s + " " + "group" + " by dmdm order by " + "count desc";

                    logger.warn(sql);
                    List<JSONObject> fks = my143.query(sql);
                    if (lgs.size() > 0) {

                        for (int i = 0; i < fks.size(); i++) {
                            JSONObject one = fks.get(i);
                            String dmdm = one.getString("DMDM");
                            JSONObject det = new JSONObject();
                            if (tots.containsKey(dmdm)) {
                                det = tots.get(dmdm);
                                det.put("fk", one.getString("count"));
                                tots.put(dmdm, det);
                            }

                        }
                    }

                }
                JSONArray ll = new JSONArray();
                List<JSONObject> lll = new ArrayList<>();
                for (Map.Entry<String, JSONObject> dets : tots.entrySet()) {
                    JSONObject one = dets.getValue();
                    ll.add(one);
                    lll.add(one);
                }
                Collections.sort(lll, (JSONObject o1, JSONObject o2) -> {

                    long a = o1.getLong("count");
                    long b = o2.getLong("count");

                    // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                    if (a < b) {  //降序排列，升序改成a>b
                        return 1;
                    } else if (a == b) {
                        return 0;
                    } else {
                        return -1;
                    }
                });

                back.put("data", lll);
                if (isExp == 1) {
                    file_id = ExportFile_sta(lll, mark);
                }
            } else {
                back.put("data", new ArrayList<>());
            }
            back.put("file_id", file_id);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            ;
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            my143.close();
        }


    }

    private static int ExportFile_sta(List<JSONObject> list, int mark) {

        JSONArray datas = new JSONArray();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            datas.add(one);
        }

        String FileName = "重点人员统计" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        header.add("DZMC");
        headername.put("DZMC", "居住地");
        header.add("count");
        headername.put("count", "总数");
        if (mark == 1) {


            header.add("lg");
            headername.put("lg", "管控");
            header.add("gz");
            headername.put("gz", "关注");
            header.add("fk");
            headername.put("fk", "F17");
            header.add("grjd");
            headername.put("grjd", "个人极端暴力犯罪高危人员");
            header.add("sw");
            headername.put("sw", "涉稳重点人员");
            header.add("sd");
            headername.put("sd", "涉毒人员");
            header.add("jsb");
            headername.put("jsb", "易肇事肇祸精神障碍患者");
            header.add("wffz");
            headername.put("wffz", "违法犯罪前科及嫌疑人员");
        }

        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
//            for (int i = 0; i < datas.size(); i++) {
//                JSONObject jsonObject = datas.getJSONObject(i);
//                JSONArray jsonArray = jsonObject.getJSONArray("real_all");
            exporthelper.write_data(datas);
//            }

            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql =
                        "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");
                if (exporthelper != null) {
                    try {
                        exporthelper.close();
                    } catch (Exception ex) {

                    }
                }
                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, TNOAConf.get("file",
                        "img_path") + filePath + FileName);
                logger.warn(obsFileName + "-->" + ret);
                logger.warn("id->" + id);
                return id;
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return 0;
        } finally {

            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }
        }
    }


    private int ExportFile_MX(List<JSONObject> list) {

        JSONArray datas = new JSONArray();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            datas.add(one);
        }


        String FileName = "重点人员明细" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        header.add("XM");
        headername.put("XM", "姓名");
        header.add("GMSFHM");
        headername.put("GMSFHM", "身份证号");
        header.add("GLJBMC");
        headername.put("GLJBMC", "管理类别");
        header.add("XLMC");
        headername.put("XLMC", "身份属性");
        header.add("SJJZD_DZMC");
        headername.put("SJJZD_DZMC", "现住地");
        header.add("ZRQ");
        headername.put("ZRQ", "所属责任区");


        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
//            for (int i = 0; i < datas.size(); i++) {
//                JSONObject jsonObject = datas.getJSONObject(i);
//                JSONArray jsonArray = jsonObject.getJSONArray("real_all");
            exporthelper.write_data(datas);
//            }

            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql =
                        "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");
                if (exporthelper != null) {
                    try {
                        exporthelper.close();
                    } catch (Exception ex) {

                    }
                }
                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, TNOAConf.get("file",
                        "img_path") + filePath + FileName);
                logger.warn(obsFileName + "-->" + ret);
                logger.warn("id->" + id);
                return id;
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return 0;
        } finally {

            try {
                if (exporthelper != null) {
                    exporthelper.close();
                }
            } catch (Exception ex) {

            }
        }
    }


}
