package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
public class ScreenController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/screen"})
    @PassToken
    public Object get_screen(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();

        try {


            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("get_screen")) {
                    return get_screenView(data);
                } else if (opt.equals("create_screen")) {

                    return createScreen(data);
                } else if (opt.equals("update_screen")) {

                    return updateScreen(data);
                } else if (opt.equals("delete_screen")) {

                    return deleteScreen(data);
                } else if (opt.equals("get_my_screen")) {
                    return GetMyScreen(data);
                } else if (opt.equals("rss_screen")) {
                    return RssScreen(data);
                } else if (opt.equals("getDictTree")) {
                    return getDictTree(data);
                } else if (opt.equals("getMidTree")) {
                    return getMidTree(data);
                } else if (opt.equals("update_dict_table")) {
                    return updateDictTable(data);
                } else if (opt.equals("set_my_main")) {
                    return setMyMain(data);
                } else {
                    return ErrNo.set(465009);
                }
            } else {
                return ErrNo.set(465009);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private Object setMyMain(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String opt_user = data.getString("opt_user");
        String screen_id = "";
        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");


            if (data.containsKey("id") && data.getString("id").length() > 0) {
                screen_id = data.getString("id");
            } else {
                return ErrNo.set(465002);
            }


            String sql = "update screen_rss set isMain=0 where accepter ='" + opt_user + "' ";
            logger.warn(sql);
            mysql.update(sql);
            sql = "update screen_rss set isMain=1 where accepter='" + opt_user + "' and screen_id='" + screen_id + "'";
            logger.warn(sql);
            mysql.update(sql);

            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            mysql.close();
        }
    }

    private Object RssScreen(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String accepters = "";
        String screen_id = "";

        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");

            if (data.containsKey("accepters") && data.getString("accepters").length() > 0) {
                accepters = data.getString("accepters");
            } else {
                return ErrNo.set(465002);
            }

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                screen_id = data.getString("id");
            } else {
                return ErrNo.set(465002);
            }

            HashMap<String, String> olds = new HashMap<>();
            String sql = "select accepter from screen_rss where screen_id='" + screen_id + "'";
            List<JSONObject> list = mysql.query(sql);
            for (int i = 0; i < list.size(); i++) {
                olds.put(list.get(i).getString("accepter"), "");
            }
            List<String> inss = new ArrayList<>();

            long start = System.currentTimeMillis();
            String accs[] = accepters.split(",");
            for (int i = 0; i < accs.length; i++) {
                String acc = accs[i];
                if (!olds.containsKey(acc)) {
                    sql = "insert into screen_rss(screen_id,accepter) values('" + screen_id + "','" + acc + "')";
                    mysql.update(sql);

                    String det = acc + "," + screen_id;
                    inss.add(det);
                }
            }
            logger.warn(accs.length + "-->" + (System.currentTimeMillis() - start) + "ms");
            start = System.currentTimeMillis();


            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            mysql.close();
        }

    }

    private Object deleteScreen(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String opt_user = "";

        String type = "";
        String det = "";
        String model_name = "";
        MysqlHelper mysql = null;
        String id = "";
        try {
            mysql = new MysqlHelper("mysql");

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(465002);
            }

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(465002);
            }

            String sql = "update screen_view set isdelete=1 where id='" + id + "'";


            logger.warn(sql);
            mysql.update(sql);
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            mysql.close();
        }
        return back;

    }

    private JSONObject GetMyScreen(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");
            String opt_user = data.getString("opt_user");
            String user_id = "";
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");

            }
            String sql = "";
            if (user_id.length() > 0) {
                sql = "select b.*,a.isMain from screen_rss a left join screen_view b on a.screen_id=b.id where a" +
                        ".accepter='" + user_id + "' and b.isdelete=0 order by isMain desc, create_time desc";
            } else {
                sql = "select *,'0' as isMain from screen_view where isdelete=0 order by create_time desc";
            }
            logger.warn(sql);
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                back.put("data", RealInfo(list));

            } else {
                back.put("data", new JSONArray());
            }

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            mysql.close();
        }
    }

    private List<JSONObject> RealInfo(List<JSONObject> list) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            //logger.warn(one.getString("create_user"));
           // logger.warn(RIUtil1.users1.get(one.getString("create_user")).toString());
            one.put("create_user_name", RIUtil1.users1.get(one.getString("create_user")).getString("name"));
            back.add(one);
        }
        return back;
    }

    private JSONObject updateDictTable(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String name = "";
        String img = "";
        String gadm = "";
        String label_jz = "";
        String label_yw = "";
        String id = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(465009);
        }
        if (data.containsKey("name") && data.getString("name").length() > 0) {
            name = data.getString("name");
        }
        if (data.containsKey("label_jz") && data.getString("label_jz").length() > 0) {
            label_jz = data.getString("label_jz");
        }
        if (data.containsKey("label_yw") && data.getString("label_yw").length() > 0) {
            label_yw = data.getString("label_yw");
        }
        if (data.containsKey("img") && data.getString("img").length() > 0) {
            img = data.getString("img");
        }
        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");
            String sql = "update dict set dict_name ='" + name + "',color='" + img + "',label_jz = '" + label_jz +
                    "',label_yw='" + label_yw + "'  where id ='" + id + "'";
            logger.warn("======>>>" + sql);
            mysql.update(sql);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            mysql.close();
        }

        return back;
    }

    private JSONObject getMidTree(JSONObject data) {

        JSONObject back = ErrNo.set(0);
        String id = "";
        MysqlHelper mysql = null;
        String us = "";
        try {
            mysql = new MysqlHelper("mysql");
            List<JSONObject> list = new ArrayList<>();

            if (data.containsKey("name") && data.getString("name").length() > 0) {
                String name = data.getString("name");
                us = " and dict_name like '%" + name + "%'";
            }


            if (data.containsKey("dict_id") && data.getString("dict_id").length() > 0) {
                id = data.getString("dict_id");
                if (id.endsWith("000000")) {  //第二层

                    String sql =
                            "select id,dict_name,remark,color from dict where type='205' and father_id='" + id + "' " + "and " + "isdelete=1  " + us + " order by  id";
                    list = mysql.query(sql);

                } else {  //第三层
                    String sql = "select permission from dict where id='" + id + "'";
                    logger.warn(sql);
                    String pers = mysql.query_one(sql, "permission");
                    if (pers != null && pers.length() > 1) {
                        sql = "select id,dict_name,remark,color from dict where type in (" + pers + ") " + us + " and"
                                + " " + "isdelete=1 order" + " by id";
                        logger.warn(sql);
                        list = mysql.query(sql);
                    } else {
                        list = new ArrayList<>();
                    }
                }

            } else {

                String sql = "select id,dict_name,remark,color from dict where type='205' and father_id='' and " +
                        "isdelete=1  " + us + " order by  id";
                list = mysql.query(sql);


            }


            if (list.size() > 0) {

                back.put("data", list);

            } else {
                back.put("data", new ArrayList<>());

            }
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            mysql.close();
        }

    }

    private JSONObject get_screenView(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String opt_user = "";

        String type = "";
        MysqlHelper mysql = null;
        String id = "";
        String unit = "";
        try {
            mysql = new MysqlHelper("mysql");

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(465002);
            }

            String sql = "select * from screen_view where id='" + id + "'";
          //  logger.warn(sql);
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {


                back.put("data", RealTabs(list, mysql));
            } else {
                back.put("data", new ArrayList<>());
            }

        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            mysql.close();
        }
        return back;

    }

    private List<JSONObject> RealTabs(List<JSONObject> list, MysqlHelper mysql) {
        List<JSONObject> back = new ArrayList<>();
        for (int j = 0; j < list.size(); j++) {
            JSONObject lone = list.get(j);

            lone.put("create_user", RIUtil.users.get(lone.getString("create_user")));


            String det = lone.getString("det");
            try {
                JSONObject dets = JSONObject.parseObject(det);

                JSONObject ones = new JSONObject();
                for (Map.Entry<String, Object> d : dets.entrySet()) {
                    JSONObject views = (JSONObject) d.getValue();

                    JSONArray tabs = views.getJSONArray("tabs");
                    JSONArray tbs = new JSONArray();

                    if (tabs.size() > 0) {

                        for (int i = 0; i < tabs.size(); i++) {
                            JSONObject one = tabs.getJSONObject(i);
                            JSONArray pers = one.getJSONArray("permissions");
                            JSONArray pms = new JSONArray();

                            for (int a = 0; a < pers.size(); a++) {
                                String id = pers.get(a).toString();

                                String sql = "select * from dict where id='" + id + "' and isdelete=1";
                                try {
                                    List<JSONObject> ids = mysql.query(sql);
                                    if (ids.size() > 0) {
                                        JSONObject ione = ids.get(0);
                                        String rem = ione.getString("remark");
                                        JSONObject remark = JSONObject.parseObject(rem);
                                        ione.put("remark", remark);

                                        pms.add(ione);

                                    }
                                } catch (Exception ex) {
                                    logger.error(Lib.getTrace(ex));

                                }


                                one.put("permissions", pms);


                            }
                            tbs.add(one);

                        }
                        views.put("tabs", tbs);
                    }

                    ones.put(d.getKey(), views);

                    //  logger.warn(views.toString());

                }
                lone.put("det", ones.toString());
            } catch (Exception ex) {

            }
            back.add(lone);


        }

        return back;
    }

    private JSONArray GetMinCounts(JSONArray dets, String unit) {
        String t = RIUtil.dicts.get(unit).getString("type");
        String code = "320400000000";
        if (t.equals("23") || t.equals("24")) {
            code = unit.substring(0, 6) + "000000";

        } else if (t.equals("25")) {
            code = unit.substring(0, 8) + "0000";
        } else if (t.equals("26")) {
            code = unit;
        }


        JSONArray rets = new JSONArray();
        MysqlHelper my143 = null;
        HashMap<String, Integer> counts = new HashMap<>();
        try {

            my143 = new MysqlHelper("mysql_zxqc");
            String sql = "select type,count from sta_zf_mid where code='" + code + "'";
            logger.warn(sql);
            List<JSONObject> list = my143.query(sql);

            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String type = one.getString("type");
                int count = one.getIntValue("count");
                counts.put(type, count);

            }
        } catch (Exception e) {
            System.out.println(Lib.getTrace(e));
        } finally {
            my143.close();
        }

        if (dets.size() > 0) {


            for (int i = 0; i < dets.size(); i++) {
                JSONObject one = dets.getJSONObject(i);

                String id = one.getString("id");

                try {
                    JSONArray child1s = one.getJSONArray("children");
                    JSONArray c1 = new JSONArray();

                    for (int a = 0; a < child1s.size(); a++) {
                        JSONObject one1 = child1s.getJSONObject(a);

                        String id1 = one1.getString("id");
                        try {

                            JSONArray child2s = one1.getJSONArray("children");
                            JSONArray c2 = new JSONArray();

                            for (int b = 0; b < child2s.size(); b++) {
                                JSONObject one2 = child2s.getJSONObject(b);
                                String id2 = one2.getString("id");
                                int count = -1;
                                if (counts.containsKey(id2)) {
                                    count = counts.get(id2);
                                }
                                one2.put("count", count);
                                c2.add(one2);
                            }

                            one1.put("children", c2);
                        } catch (Exception e) {
                            // TODO: handle exception
                        }

                        int count = -1;
                        if (counts.containsKey(id1)) {
                            count = counts.get(id1);
                        }
                        one1.put("count", count);
                        c1.add(one1);
                    }

                    one.put("children", c1);
                } catch (Exception e) {
                    // TODO: handle exception
                }

                int count = -1;
                if (counts.containsKey(id)) {
                    count = counts.get(id);
                }
                one.put("count", count);
                rets.add(one);
                System.out.println(one);

            }

        }
        return rets;
    }

    private JSONObject createScreen(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String opt_user = "";


        String det = "";
        String id = "";
        String model_name = "";
        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(465002);
            }
            if (data.containsKey("det") && data.getString("det").length() > 0) {
                det = data.getString("det");
                det = DealDet(det);
            }
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(465002);
            }
            if (data.containsKey("model_name") && data.getString("model_name").length() > 0) {
                model_name = data.getString("model_name");
            }

            String sql =
                    "insert into screen_view (model_name,det,id,create_user) values ('" + model_name + "','" + det +
                            "'," + "'" + id + "','" + opt_user + "')";


            logger.warn(sql);
            mysql.update(sql);
            sql = "insert into screen_rss (screen_id,accepter) values('" + id + "','" + opt_user + "')";
            logger.warn(sql);
            mysql.update(sql);
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            mysql.close();
        }
        return back;

    }


    private JSONObject updateScreen(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String opt_user = "";

        String type = "";
        String det = "";
        String model_name = "";
        MysqlHelper mysql = null;
        String s = "";
        try {
            mysql = new MysqlHelper("mysql");

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(465002);
            }
            if (data.containsKey("det") && data.getString("det").length() > 0) {
                det = data.getString("det");

                det = DealDet(det);


                s = s + " det='" + det + "',";
            }
            if (data.containsKey("model_name") && data.getString("model_name").length() > 0) {
                model_name = data.getString("model_name");
                s = s + " model_name='" + model_name + "',";
            }
            if (data.containsKey("img") && data.getString("img").length() > 0) {
                String img = data.getString("img");
                s = s + " img='" + img + "',";
            }

            String id = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(465002);
            }

            String sql = "update screen_view set " + s + " isdelete=0 where id='" + id + "'";


            logger.warn(sql);
            mysql.update(sql);
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            mysql.close();
        }
        return back;

    }

    private String DealDet(String det) {
        JSONObject dets = JSONObject.parseObject(det);
        for (Map.Entry<String, Object> d : dets.entrySet()) {

            JSONObject views = (JSONObject) d.getValue();
            JSONArray tabs = views.getJSONArray("tabs");
            JSONArray tbs = new JSONArray();
            for (int i = 0; i < tabs.size(); i++) {
                String one = tabs.getString(i);
                JSONObject aone = tabs.getJSONObject(i);
                if (one.contains("dict_name")) {

                    JSONArray pers = aone.getJSONArray("permissions");
                    JSONArray per = new JSONArray();
                    for (int a = 0; a < pers.size(); a++) {
                        JSONObject ao = pers.getJSONObject(a);
                        String id = ao.getString("id");
                        per.add(id);
                    }
                    aone.put("permissions", per);
                    tbs.add(aone);
                } else {
                    tbs.add(aone);
                }

            }

            views.put("tabs", tbs);
            dets.put(d.getKey(), views);

        }

        return dets.toString();
    }


    private JSONObject getDictTree(JSONObject data) {
        MysqlHelper mysql = null;
        String sql = "";
        String query = "";
        String subSql = "";

        JSONObject back = ErrNo.set(0);
        List<JSONObject> resList = new ArrayList<>();

        try {

            mysql = new MysqlHelper("mysql");

            if (data.containsKey("query") && data.getString("query").length() > 0) {
                subSql += " and dict_name like '%" + data.getString("query") + "%' ";
            }

            if (data.containsKey("label_jz") && data.getString("label_jz").length() > 0) {
                String[] split = data.getString("label_jz").split(",");
                subSql += " and (";
                for (int i = 0; i < split.length; i++) {
                    if (i == split.length - 1) {
                        subSql += "  label_jz like '%" + split[i] + "%' ";
                    } else {
                        subSql += "  label_jz like '%" + split[i] + "%' or ";
                    }
                }

                subSql += ") ";
            }

            if (data.containsKey("label_yw") && data.getString("label_yw").length() > 0) {
                String[] split = data.getString("label_yw").split(",");
                subSql += " and (";
                for (int i = 0; i < split.length; i++) {
                    if (i == split.length - 1) {
                        subSql += "  label_yw like '%" + split[i] + "%' ";
                    } else {
                        subSql += "  label_yw like '%" + split[i] + "%' or ";
                    }
                }
                subSql += ") ";

            }

            sql = "select dict_name as label, id as value from dict " + "where isdelete = '1' and  type = '200' ";
            resList = mysql.query(sql);
            for (JSONObject record : resList) {
                logger.warn(record.toJSONString());
                String dictId = record.getString("id");
                String dictName = record.getString("dict_name");
                record.put("label", dictName);
                record.put("value", dictId);
                sql = "select permission , label_jz, label_yw , color, remark, dict_name as label, id as value " +
                        "from dict " + "where " + "isdelete = 1  " + " and type = 201  and father_id = '" + dictId +
                        "' and " + "is_show = '1' " + subSql;
                logger.warn("===========>>>>>>>>>>>>> sql :   " + sql);

                List<JSONObject> roomChildList = mysql.query(sql);

                //如果下一级没有is_show = '1' 填充空数组

                for (JSONObject one : roomChildList) {

                    //remark中的内容转为json格式
                    String remarkJs = one.getString("remark");
                    if (one.containsKey("remark") && one.getString("remark").length() > 0) {
                        JSONObject remark = new JSONObject();
                        try {
                            remark = JSONObject.parseObject(remarkJs);
                        } catch (Exception es) {
                            logger.warn(one.getString("id"));
                            logger.warn(remarkJs);
                        }
                        one.put("remark", remark);
                    } else {
                        one.put("remark", "{}");
                    }

                    dictName = one.getString("dict_name");
                    dictId = one.getString("id");
                    one.put("label", dictName);
                    one.put("value", dictId);
                    one.put("img", "");

                    // 警种标签
                    getLabelJz(one);
                    //业务标签
                    getLabelYw(one);

                }
                record.put("children", roomChildList);
            }

            back.put("data", resList);

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 501002, Lib.getTrace(e));
        } finally {
            if (mysql != null) {
                mysql.close();
            }
        }
        return back;
    }

    private void getLabelYw(JSONObject one) {
        List<JSONObject> labelJzList = new ArrayList<>();
        if (StrUtil.isNotBlank(one.getString("label_yw"))) {
            String[] split = one.getString("label_yw").split(",");
            for (String s : split) {
                if (RIUtil.dicts.containsKey(s)) {
                    labelJzList.add(RIUtil.dicts.get(s));
                }
            }
        }
        one.put("label_yw", labelJzList);
    }


    private void getLabelJz(JSONObject one) {
        List<JSONObject> labelJzList = new ArrayList<>();
        if (StrUtil.isNotBlank(one.getString("label_jz"))) {
            String[] split = one.getString("label_jz").split(",");
            for (String s : split) {
                if (RIUtil.dicts.containsKey(s)) {
                    labelJzList.add(RIUtil.dicts.get(s));
                }
            }
        }
        one.put("label_jz", labelJzList);
    }


}
