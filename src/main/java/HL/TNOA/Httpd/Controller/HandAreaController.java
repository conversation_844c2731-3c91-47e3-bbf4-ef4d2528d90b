package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
public class HandAreaController {
    private static Logger logger = LoggerFactory.getLogger(HandAreaController.class);
    private static String ip = "";

    @PassToken
    @RequestMapping(method = {RequestMethod.POST}, path = {"/hand_area"})
    public static JSONObject get_use(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        String ip = request.getRemoteAddr();
        logger.warn("hand_area--->" + data);
        String opt = "";
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("hand_area_get")) {
                return getHandArea(data);
            } else if (opt.equals("hand_area_create")) {
                return createHandArea(data, ip);
            } else if (opt.equals("hand_area_update")) {
                return updateHandArea(data, ip);
            } else if (opt.equals("hand_area_delete")) {
                return deleteHandArea(data, ip);
            } else if (opt.equals("hand_area_get_pic")) {
                return getHandAreaPic(data);
            } else if (opt.equals("hand_area_work")) {
                return WorkDownload(data);
            } else {
                return ErrNo.set(467009);
            }
        } else {
            return ErrNo.set(467009);
        }
    }

    private static JSONObject WorkDownload(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            String id = "";
            mysql = InfoModelPool.getModel();
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(467009);
            }
            String sql =
                    "select *,decode(`name`,'" + RIUtil.enName + "') as `name`,decode(id_num,'" + RIUtil.enNum + "') "
                            + "as id_num," + "decode(address,'" + RIUtil.enContent + "') as address,decode(tel,'" + RIUtil.enTele + "') as tel," + "decode(aj_reason,'" + RIUtil.enContent + "') as aj_reason,decode(self,'" + RIUtil.enContent + "') as self," + "decode(`check`,'" + RIUtil.enContent + "') as `check`,decode(get_user,'" + RIUtil.enName + "') as get_user," + "decode(get_user_id,'" + RIUtil.enNum + "') as get_user_id,decode(birth,'" + RIUtil.enDate + "') as birth " + "from hand_area where isdelete=1 and id=" + id;
            logger.warn(sql);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sql);
            if (list.size() == 0) {
                return ErrNo.set(4670010);
            }
            logger.warn(list.toString());
            int word = TestWordAddContent.Word(list, mysql);
            back.put("id", word);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(467005);
        }
        return back;
    }

    private static JSONObject getHandAreaPic(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String today = new SimpleDateFormat("yyyyMMdd").format(new Date());
            if (data.containsKey("date") && data.getString("date").length() > 0) {
                today = data.getString("date").replace("-", "");
            }
            String unit = "";
            String fileName = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                String s = "select face_name from dict where id='" + unit + "'";
                fileName = mysql.query_one(s, "face_name");
                if (fileName == null) {
                    fileName =
                            "";
                }

            } else {
                return ErrNo.set(467005);
            }
            String sql = "select id,file_name,isBig,rela_id from upload where file_name like '%" + today + "%'  "
                    + "and isBig>7 and file_name like '%" + fileName + "%' order by time desc";
           // logger.warn(sql);
            List<JSONObject> list = mysql.query(sql);
            JSONArray datas = new JSONArray();
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String file_name = one.getString("file_name");
                    //czpcs_J35230463_20220415125502_FACE_SNAP_310.png
                    String names[] = file_name.split("\\_");
                    String time = names[3];
                    time = time.substring(0, 4) + "-" + time.substring(4, 6) + "-" + time.substring(6, 8) + " " + time.substring(8, 10) + ":" + time.substring(10, 12) + ":" + time.substring(12, 14);
                    one.put("time", time);
                    String isBig = one.getString("isBig");
                    if (isBig.equals("8")) {
                        one.put("dic", "进");
                    } else {
                        one.put("dic", "出");
                    }
                    String rela_id = one.getString("rela_id");
                    String name = RIUtil.IdToName(rela_id, mysql, " name",
                            "hand_area");
                    one.put("name", name);
                    datas.add(one);
                }
            }
            back.put("data", datas);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(467007);
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    //******CREATE*******
    private static JSONObject createHandArea(JSONObject da, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String num = "";
            String police_mian = "";
            String name = "";
            String sex = "0";
            String birth = "";
            String id_num = "";
            String address = "";
            String tel = "";
            String aj_num = "";
            String aj_reason = "";
            String tazs = "";
            String zach = "";
            String jxpw = "";
            String xsch = "";
            String jc = "";
            String xsjl = "";
            String qbhs = "";
            String jsjz = "";
            String daibu = "";
            String bhr = "";
            String zr = "";
            String qzns = "";
            String qt1 = "";
            String qt_det = "";
            String file_num = "";
            String police_ba = "";
            String in_time = "";
            String manager = "";
            String self = "";
            String check = "";
            String police_check = "";
            String police_meet = "";
            String check_time = "";
            String info_catch = "";
            String zw = "";
            String xy = "";
            String bj = "";
            String ny = "";
            String catch_other = "";
            String info_in = "";
            String check_check = "";
            String check_time_start = "";
            String check_time_end = "";
            String ask_log = "";
            String out_ls = "";
            String out_time = "";
            String out_reason = "";
            String wpcl = "";
            String notback = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "1";
            String pic = "";
            String get_user = "";
            String get_user_id = "";
            String get_time = "";
            JSONObject data = da.getJSONObject("data");
            if (data.containsKey("num") && data.getString("num").length() > 0) {
                num = data.getString("num");
            } else {
                String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
                String sql =
                        "select count(id) as count from hand_area where create_time like '%" + today + "%' and " +
                                "isdelete=1";
                int count = mysql.query_count(sql);
                count = count + 1;
                today = today.replace("-", "");
                if (count < 10) {
                    num = today + "00" + count;

                } else if (count >= 10 && count < 100) {
                    num = today + "0" + count;
                } else {
                    num = today + count;
                }


            }
            if (data.containsKey("police_mian") && data.getString("police_mian").length() > 0) {
                police_mian = data.getString("police_mian");
            } else {

            }
            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");
            } else {

            }
            if (data.containsKey("sex") && data.getString("sex") != null && data.getString("sex").length() > 0) {
                sex = data.getString("sex");
            }
            if (data.containsKey("birth") && data.getString("birth").length() > 0) {
                birth = data.getString("birth");
            }

            if (data.containsKey("id_num") && data.getString("id_num").length() > 0) {
                id_num = data.getString("id_num");
                if (id_num.length() != 15 || id_num.length() != 18) {
                    JSONObject idinfo = RIUtil.GetIDinfo(id_num);
                    sex = idinfo.getString("sex");
                    birth = idinfo.getString("birth");
                } else {
                    logger.warn("*");
                    return ErrNo.set(467002);
                }
            } else {

            }

            if (data.containsKey("address") && data.getString("address").length() > 0) {
                address = data.getString("address");
            } else {

            }
            if (data.containsKey("tel") && data.getString("tel").length() > 0) {
                tel = data.getString("tel");
            } else {

            }
            if (data.containsKey("aj_num") && data.getString("aj_num").length() > 0) {
                aj_num = data.getString("aj_num");
            } else {

            }

            if (data.containsKey("aj_reason") && data.getString("aj_reason").length() > 0) {
                aj_reason = data.getString("aj_reason");
            } else {

            }
            if (data.containsKey("tazs") && data.getString("tazs").length() > 0) {
                tazs = data.getString("tazs");
            } else {

            }
            if (data.containsKey("zach") && data.getString("zach").length() > 0) {
                zach = data.getString("zach");
            } else {

            }
            if (data.containsKey("jxpw") && data.getString("jxpw").length() > 0) {
                jxpw = data.getString("jxpw");
            } else {

            }
            if (data.containsKey("xsch") && data.getString("xsch").length() > 0) {

            } else {

            }
            if (data.containsKey("jc") && data.getString("jc").length() > 0) {
                jc = data.getString("jc");
            } else {

            }
            if (data.containsKey("xsjl") && data.getString("xsjl").length() > 0) {
                xsjl = data.getString("xsjl");
            } else {

            }
            if (data.containsKey("qbhs") && data.getString("qbhs").length() > 0) {
                qbhs = data.getString("qbhs");
            } else {

            }
            if (data.containsKey("jsjz") && data.getString("jsjz").length() > 0) {

            } else {

            }
            if (data.containsKey("daibu") && data.getString("daibu").length() > 0) {
                daibu = data.getString("daibu");
            } else {

            }
            if (data.containsKey("bhr") && data.getString("bhr").length() > 0) {
                bhr = data.getString("bhr");
            } else {

            }
            if (data.containsKey("zr") && data.getString("zr").length() > 0) {
                zr = data.getString("zr");
            } else {

            }
            if (data.containsKey("qzns") && data.getString("qzns").length() > 0) {
                qzns = data.getString("qzns");
            } else {

            }
            if (data.containsKey("qt1") && data.getString("qt1").length() > 0) {
                qt1 = data.getString("qt1");
            } else {

            }
            if (data.containsKey("qt_det") && data.getString("qt_det").length() > 0) {
                qt_det = data.getString("qt_det");
            } else {

            }
            if (data.containsKey("file_num") && data.getString("file_num").length() > 0) {
                file_num = data.getString("file_num");
            } else {

            }
            if (data.containsKey("police_ba") && data.getString("police_ba").length() > 0) {
                police_ba = data.getString("police_ba");
            } else {

            }
            if (data.containsKey("in_time") && data.getString("in_time").length() > 0) {
                in_time = data.getString("in_time");
            } else {

            }
            if (data.containsKey("manager") && data.getString("manager").length() > 0) {
                manager = data.getString("manager");
            } else {

            }
            if (data.containsKey("self") && data.getString("self").length() > 0) {
                self = data.getString("self");
            } else {

            }
            if (data.containsKey("check") && data.getString("check").length() > 0) {
                check = data.getString("check");
            } else {

            }
            if (data.containsKey("police_check") && data.getString("police_check").length() > 0) {
                police_check = data.getString("police_check");
            } else {

            }
            if (data.containsKey("police_meet") && data.getString("police_meet").length() > 0) {
                police_meet = data.getString("police_meet");
            } else {

            }
            if (data.containsKey("check_time") && data.getString("check_time").length() > 0) {
                check_time = data.getString("check_time");
            } else {

            }
            if (data.containsKey("info_catch") && data.getString("info_catch").length() > 0) {
                info_catch = data.getString("info_catch");
            } else {

            }
            if (data.containsKey("zw") && data.getString("zw").length() > 0) {
                zw = data.getString("zw");
            } else {

            }
            if (data.containsKey("xy") && data.getString("xy").length() > 0) {
                xy = data.getString("xy");
            } else {

            }
            if (data.containsKey("bj") && data.getString("bj").length() > 0) {
                bj = data.getString("bj");
            } else {

            }
            if (data.containsKey("ny") && data.getString("ny").length() > 0) {
                ny = data.getString("ny");
            } else {

            }
            if (data.containsKey("catch_other") && data.getString("catch_other").length() > 0) {
                catch_other = data.getString("catch_other");
            } else {

            }
            if (data.containsKey("info_in") && data.getString("info_in").length() > 0) {
                info_in = data.getString("info_in");
            } else {

            }
            if (data.containsKey("check_check") && data.getString("check_check").length() > 0) {
                check_check = data.getString("check_check");
            } else {

            }
            if (data.containsKey("check_time_start") && data.getString("check_time_start").length() > 0) {
                check_time_start = data.getString("check_time_start");
            } else {

            }
            if (data.containsKey("check_time_end") && data.getString("check_time_end").length() > 0) {
                check_time_end = data.getString("check_time_end");
            } else {

            }
            if (data.containsKey("ask_log") && data.getString("ask_log").length() > 0) {
                ask_log = data.getString("ask_log");
            } else {

            }
            if (data.containsKey("out_ls") && data.getString("out_ls").length() > 0) {
                out_ls = data.getString("out_ls");
            } else {

            }
            if (data.containsKey("out_time") && data.getString("out_time").length() > 0) {
                out_time = data.getString("out_time");
            } else {

            }
            if (data.containsKey("out_reason") && data.getString("out_reason").length() > 0) {
                out_reason = data.getString("out_reason");
            } else {

            }
            if (data.containsKey("wpcl") && data.getString("wpcl").length() > 0) {
                wpcl = data.getString("wpcl");
            } else {

            }
            if (data.containsKey("notback") && data.getString("notback").length() > 0) {
                notback = data.getString("notback");
            } else {

            }
            if (data.containsKey("pic") && data.getString("pic").length() > 0) {
                pic = data.getString("pic");
            }

            if (data.containsKey("get_user") && data.getString("get_user").length() > 0) {
                get_user = data.getString("get_user");
            }

            if (data.containsKey("get_user_id") && data.getString("get_user_id").length() > 0) {
                get_user_id = data.getString("get_user_id");
            }
            if (data.containsKey("get_time") && data.getString("get_time").length() > 0) {
                get_time = data.getString("get_time");
            }
            String unit = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            }

            if (da.containsKey("opt_user") && da.getString("opt_user").length() > 0) {
                create_user = da.getString("opt_user");
                if (unit.length() == 0) {
                    unit = RIUtil.users.get(create_user).getString("unit");

                }
            } else {
                return ErrNo.set(467002);
            }

            String sqls = "insert hand_area (num,police_mian,name,sex," + "birth,id_num," + "address,tel,aj_num," +
                    "aj_reason,tazs,zach,jxpw," + "xsch,jc,xsjl,qbhs,jsjz,daibu,bhr," + "zr,qzns,qt1,qt_det,file_num,"
                    + "police_ba," + "in_time,manager,self,`check`,police_check," + "police_meet,check_time," +
                    "info_catch," + "zw,xy,bj," + "ny,catch_other,info_in,check_check,check_time_start," +
                    "check_time_end,ask_log," + "out_ls,out_time,out_reason," + "wpcl,notback,create_user," +
                    "create_time,isdelete,pic,get_user," + "get_user_id,get_time,unit)" + "values('" + num + "','" + police_mian + "',encode('" + name + "'," + "'" + RIUtil.enName + "'),'" + sex + "'," + "encode('" + birth + "','" + RIUtil.enDate + "')," + "encode('" + id_num + "','" + RIUtil.enNum + "')," + "encode('" + address + "','" + RIUtil.enContent + "'),encode('" + tel + "','" + RIUtil.enTele + "'),'" + aj_num + "'," + "encode('" + aj_reason + "','" + RIUtil.enContent + "'),'" + tazs + "','" + zach + "','" + jxpw + "'," + "'" + xsch + "','" + jc + "','" + xsjl + "','" + qbhs + "','" + jsjz + "','" + daibu + "','" + bhr + "'," + "'" + zr + "','" + qzns + "','" + qt1 + "','" + qt_det + "','" + file_num + "','" + police_ba + "'," + "'" + in_time + "','" + manager + "',encode('" + self + "','" + RIUtil.enContent + "'),encode('" + check + "','" + RIUtil.enContent + "'),'" + police_check + "'," + "'" + police_meet + "','" + check_time + "','" + info_catch + "','" + zw + "','" + xy + "','" + bj + "'," + "'" + ny + "','" + catch_other + "','" + info_in + "','" + check_check + "','" + check_time_start + "'," + "'" + check_time_end + "','" + ask_log + "','" + out_ls + "','" + out_time + "','" + out_reason + "'," + "'" + wpcl + "','" + notback + "','" + create_user + "','" + create_time + "','" + isdelete + "','" + pic + "'," + "encode('" + get_user + "','" + RIUtil.enName + "'),encode('" + get_user_id + "','" + RIUtil.enNum + "'),'" + get_time + "','" + unit + "')";
            logger.warn(sqls);
            mysql.update(sqls);
            sqls = "select id from hand_area where pic='" + pic + "'";
            String id = mysql.query_one(sqls, "id");
            sqls = "update upload set rela_id='" + id + "' where id='" + pic + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建办案区使用记录", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(467001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private static JSONObject getHandArea(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String num = "";
            String police_mian = "";
            String name = "";
            String sex = "";
            String birth = "";
            String id_num = "";
            String address = "";
            String tel = "";
            String aj_num = "";
            String in_time_start = "";
            String in_time_end = "";
            String create_time_start = "";
            String create_time_end = "";

            String out_time_start = "";
            String out_time_end = "";
            String unit = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("num") && data.getString("num").length() > 0) {
                num = data.getString("num");
                sql = sql + " num='" + num + "' and ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " unit='" + unit + "' and ";
            }
            if (data.containsKey("police_mian") && data.getString("police_mian").length() > 0) {
                police_mian = data.getString("police_mian");
                sql = sql + " police_mian='" + police_mian + "' and ";
            }
            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");
                sql = sql + " name='" + name + "' and ";
            }
            if (data.containsKey("sex") && data.getString("sex").length() > 0) {
                sex = data.getString("sex");
                sql = sql + " sex='" + sex + "' and ";
            }
            if (data.containsKey("birth") && data.getString("birth").length() > 0) {
                birth = data.getString("birth");
                sql = sql + "  decode(birth,'" + RIUtil.enDate + "')='" + birth + "' and ";
            }
            if (data.containsKey("id_num") && data.getString("id_num").length() > 0) {
                id_num = data.getString("id_num");
                sql = sql + "  decode(id_num,'" + RIUtil.enNum + "')='" + id_num + "' and ";
            }
            if (data.containsKey("address") && data.getString("address").length() > 0) {
                address = data.getString("address");
                sql = sql + "  decode(address,'" + RIUtil.enContent + "') like '%" + address + "%' and ";
            }
            if (data.containsKey("tel") && data.getString("tel").length() > 0) {
                tel = data.getString("tel");
                sql = sql + "  decode(tel,'" + RIUtil.enTele + "') like '%" + tel + "%' and ";
            }
            if (data.containsKey("aj_num") && data.getString("aj_num").length() > 0) {
                aj_num = data.getString("aj_num");
                sql = sql + " aj_num='" + aj_num + "' and ";
            }
            if (data.containsKey("in_time_start") && data.getString("in_time_start").length() > 0) {
                in_time_start = data.getString("in_time_start");
                sql = sql + " in_time>='" + in_time_start + "' and ";
            }
            if (data.containsKey("in_time_end") && data.getString("in_time_end").length() > 0) {
                in_time_end = data.getString("in_time_end");
                sql = sql + " in_time<='" + in_time_end + "' and ";
            }

            if (data.containsKey("out_time_start") && data.getString("out_time_start").length() > 0) {
                out_time_start = data.getString("out_time_start");
                sql = sql + " out_time>='" + out_time_start + "' and ";
            }
            if (data.containsKey("out_time_end") && data.getString("out_time_end").length() > 0) {
                out_time_end = data.getString("out_time_end");
                sql = sql + " out_time<='" + out_time_end + "' and ";
            }
            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
                create_time_start = data.getString("create_time_start");
                sql = sql + " create_time>='" + create_time_start + "' and ";
            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end");
                sql = sql + " create_time<='" + create_time_end + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select *,decode(`name`,'" + RIUtil.enName + "') as `name`,decode(id_num,'" + RIUtil.enNum + "') "
                            + "as id_num," + "decode(address,'" + RIUtil.enContent + "') as address,decode(tel,'" + RIUtil.enTele + "') as tel," + "decode(aj_reason,'" + RIUtil.enContent + "') as aj_reason,decode(self,'" + RIUtil.enContent + "') as self," + "decode(`check`,'" + RIUtil.enContent + "') as `check`,decode(get_user,'" + RIUtil.enName + "') as get_user," + "decode(get_user_id,'" + RIUtil.enNum + "') as get_user_id,decode(birth,'" + RIUtil.enDate + "') as birth " + "from hand_area " + "where 1=1 and " + sql + " isdelete=1 order by create_time desc limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();

            list = mysql.query(sqls);
            //            TestWordAddContent.Word(list);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select id from hand_area where 1=1 and " + sql + " isdelete=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(467005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private static Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            //police_mian	主办民警
            String police_mian = one.getString("police_mian");
            one.put("police_mian", RIUtil.users.get(police_mian));
            //police_ba	办案民警
            String police_ba = one.getString("police_ba");
            one.put("police_ba", RIUtil.users.get(police_ba));
            //manager	管理员
            String manager = one.getString("manager");
            one.put("manager", RIUtil.users.get(manager));
            //police_check	检查民警
            String police_check = one.getString("police_check");
            one.put("police_check", RIUtil.users.get(police_check));
            //police_meet	见证人
            String police_meet = one.getString("police_meet");
            one.put("police_meet", RIUtil.users.get(police_meet));
            //create_user
            String create_user = one.getString("create_user");
            one.put("create_user", RIUtil.users.get(create_user));
            String tel = one.getString("tel");
            if (tel.length() > 4) {
                tel = tel.substring(0, tel.length() - 4) + "****";
                one.put("tel*", tel);
            }
            String id_num = one.getString("id_num");
            if (id_num.length() >= 15) {
                id_num = id_num.substring(0, id_num.length() - 4) + "****";
                one.put("id_num*", id_num);
            }
            if (one.containsKey("unit")) {
                String unit = one.getString("unit");
                one.put("unit_name", RIUtil.RealDictNames(RIUtil.StringToList(unit)));
            } else {
                one.put("unit_name", "");
            }
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private static JSONObject updateHandArea(JSONObject dd, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String num = "";
            String police_mian = "";
            String name = "";
            String sex = "";
            String birth = "";
            String id_num = "";
            String address = "";
            String tel = "";
            String aj_num = "";
            String aj_reason = "";
            String tazs = "";
            String zach = "";
            String jxpw = "";
            String xsch = "";
            String jc = "";
            String xsjl = "";
            String qbhs = "";
            String jsjz = "";
            String daibu = "";
            String bhr = "";
            String zr = "";
            String qzns = "";
            String qt1 = "";
            String qt_det = "";
            String file_num = "";
            String police_ba = "";
            String in_time = "";
            String manager = "";
            String self = "";
            String check = "";
            String police_check = "";
            String police_meet = "";
            String check_time = "";
            String info_catch = "";
            String zw = "";
            String xy = "";
            String bj = "";
            String ny = "";
            String catch_other = "";
            String info_in = "";
            String check_check = "";
            String check_time_start = "";
            String check_time_end = "";
            String ask_log = "";
            String out_ls = "";
            String out_time = "";
            String out_reason = "";
            String wpcl = "";
            String notback = "";
            String create_user = "";
            String create_time = "";
            String isdelete = "";
            String opt_user = "";
            String get_user = "";
            String get_user_id = "";
            String get_time = "";
            JSONObject data = dd.getJSONObject("data");
            if (dd.containsKey("id") && dd.getString("id").length() > 0) {
                id = dd.getString("id");
                sql = sql + " id='" + id + "' , ";
            } else {
                return ErrNo.set(467004);
            }
            if (data.containsKey("num") && data.getString("num").length() > 0) {
                num = data.getString("num");
                sql = sql + " num='" + num + "' , ";
            }
            if (data.containsKey("police_mian") && data.getString("police_mian").length() > 0) {
                police_mian = data.getString("police_mian");
                sql = sql + " police_mian='" + police_mian + "' , ";
            }
            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");
                sql = sql + " name=encode('" + name + "','" + RIUtil.enName + "') , ";
            }
            if (data.containsKey("sex") && data.getString("sex") != null) {
                sex = data.getString("sex");
                sql = sql + " sex='" + sex + "', ";
            }
            if (data.containsKey("birth")) {
                birth = data.getString("birth");
                sql = sql + " birth=encode('" + birth + "','" + RIUtil.enDate + "'), ";
            }
            if (data.containsKey("id_num")) {
                id_num = data.getString("id_num");

                if (id_num.length() == 15 || id_num.length() == 18) {
                    JSONObject idinfo = RIUtil.GetIDinfo(id_num);
                    sex = idinfo.getString("sex");
                    birth = idinfo.getString("birth");
                } else if ((id_num.length() != 15 || id_num.length() != 18) && id_num.length() > 0) {
                    logger.warn("*");
                    return ErrNo.set(467002);
                }
                sql = sql + " id_num=encode('" + id_num + "','" + RIUtil.enNum + "'),sex='" + sex + "',birth=encode" + "('" + birth + "','" + RIUtil.enDate + "'),";
            }

            if (data.containsKey("address") && data.getString("address").length() > 0) {
                address = data.getString("address");
                sql = sql + " address=encode('" + address + "','" + RIUtil.enContent + "') , ";
            }
            if (data.containsKey("tel") && data.getString("tel").length() > 0) {
                tel = data.getString("tel");
                sql = sql + " tel=encode('" + tel + "','" + RIUtil.enTele + "') , ";
            }
            if (data.containsKey("aj_num") && data.getString("aj_num").length() > 0) {
                aj_num = data.getString("aj_num");
                sql = sql + " aj_num='" + aj_num + "' , ";
            }
            if (data.containsKey("aj_reason") && data.getString("aj_reason").length() > 0) {
                aj_reason = data.getString("aj_reason");
                sql = sql + " aj_reason=encode('" + aj_reason + "','" + RIUtil.enContent + "'), ";
            }
            if (data.containsKey("tazs") && data.getString("tazs").length() > 0) {
                tazs = data.getString("tazs");
                sql = sql + " tazs='" + tazs + "' , ";
            }
            if (data.containsKey("zach") && data.getString("zach").length() > 0) {
                zach = data.getString("zach");
                sql = sql + " zach='" + zach + "' , ";
            }
            if (data.containsKey("jxpw") && data.getString("jxpw").length() > 0) {
                jxpw = data.getString("jxpw");
                sql = sql + " jxpw='" + jxpw + "' , ";
            }
            if (data.containsKey("xsch") && data.getString("xsch").length() > 0) {
                xsch = data.getString("xsch");
                sql = sql + " xsch='" + xsch + "' , ";
            }
            if (data.containsKey("jc") && data.getString("jc").length() > 0) {
                jc = data.getString("jc");
                sql = sql + " jc='" + jc + "' , ";
            }
            if (data.containsKey("xsjl") && data.getString("xsjl").length() > 0) {
                xsjl = data.getString("xsjl");
                sql = sql + " xsjl='" + xsjl + "' , ";
            }
            if (data.containsKey("qbhs") && data.getString("qbhs").length() > 0) {
                qbhs = data.getString("qbhs");
                sql = sql + " qbhs='" + qbhs + "' , ";
            }
            if (data.containsKey("jsjz") && data.getString("jsjz").length() > 0) {
                jsjz = data.getString("jsjz");
                sql = sql + " jsjz='" + jsjz + "' , ";
            }
            if (data.containsKey("daibu") && data.getString("daibu").length() > 0) {
                daibu = data.getString("daibu");
                sql = sql + " daibu='" + daibu + "' , ";
            }
            if (data.containsKey("bhr") && data.getString("bhr").length() > 0) {
                bhr = data.getString("bhr");
                sql = sql + " bhr='" + bhr + "' , ";
            }
            if (data.containsKey("zr") && data.getString("zr").length() > 0) {
                zr = data.getString("zr");
                sql = sql + " zr='" + zr + "' , ";
            }
            if (data.containsKey("qzns") && data.getString("qzns").length() > 0) {
                qzns = data.getString("qzns");
                sql = sql + " qzns='" + qzns + "' , ";
            }
            if (data.containsKey("qt1") && data.getString("qt1").length() > 0) {
                qt1 = data.getString("qt1");
                sql = sql + " qt1='" + qt1 + "' , ";
            }
            if (data.containsKey("qt_det") && data.getString("qt_det").length() > 0) {
                qt_det = data.getString("qt_det");
                sql = sql + " qt_det='" + qt_det + "' , ";
            }
            if (data.containsKey("file_num") && data.getString("file_num").length() > 0) {
                file_num = data.getString("file_num");
                sql = sql + " file_num='" + file_num + "' , ";
            }
            if (data.containsKey("police_ba") && data.getString("police_ba").length() > 0) {
                police_ba = data.getString("police_ba");
                sql = sql + " police_ba='" + police_ba + "' , ";
            }
            if (data.containsKey("in_time") && data.getString("in_time").length() > 0) {
                in_time = data.getString("in_time");
                sql = sql + " in_time='" + in_time + "' , ";
            }
            if (data.containsKey("manager") && data.getString("manager") != null && data.getString("manager").length() > 0) {
                manager = data.getString("manager");
                sql = sql + " manager='" + manager + "' , ";
            }
            if (data.containsKey("self") && data.getString("self").length() > 0) {
                self = data.getString("self");
                sql = sql + " self=encode('" + self + "','" + RIUtil.enContent + "') , ";
            }
            if (data.containsKey("check") && data.getString("check").length() > 0) {
                check = data.getString("check");
                sql = sql + " `check`=encode('" + check + "' ,'" + RIUtil.enContent + "'), ";
            }
            if (data.containsKey("police_check") && data.getString("police_check").length() > 0) {
                police_check = data.getString("police_check");
                sql = sql + " police_check='" + police_check + "' , ";
            }
            if (data.containsKey("police_meet") && data.getString("manager") != null && data.getString("police_meet").length() > 0) {
                police_meet = data.getString("police_meet");
                sql = sql + " police_meet='" + police_meet + "' , ";
            }
            if (data.containsKey("check_time") && data.getString("check_time").length() > 0) {
                check_time = data.getString("check_time");
                sql = sql + " check_time='" + check_time + "' , ";
            }
            if (data.containsKey("info_catch") && data.getString("info_catch").length() > 0) {
                info_catch = data.getString("info_catch");
                sql = sql + " info_catch='" + info_catch + "' , ";
            }
            if (data.containsKey("zw") && data.getString("zw").length() > 0) {
                zw = data.getString("zw");
                sql = sql + " zw='" + zw + "' , ";
            }
            if (data.containsKey("xy") && data.getString("xy").length() > 0) {
                xy = data.getString("xy");
                sql = sql + " xy='" + xy + "' , ";
            }
            if (data.containsKey("bj") && data.getString("bj").length() > 0) {
                bj = data.getString("bj");
                sql = sql + " bj='" + bj + "' , ";
            }
            if (data.containsKey("ny") && data.getString("ny").length() > 0) {
                ny = data.getString("ny");
                sql = sql + " ny='" + ny + "' , ";
            }
            if (data.containsKey("catch_other") && data.getString("catch_other").length() > 0) {
                catch_other = data.getString("catch_other");
                sql = sql + " catch_other='" + catch_other + "' , ";
            }
            if (data.containsKey("info_in") && data.getString("info_in").length() > 0) {
                info_in = data.getString("info_in");
                sql = sql + " info_in='" + info_in + "' , ";
            }
            if (data.containsKey("check_check") && data.getString("check_check").length() > 0) {
                check_check = data.getString("check_check");
                sql = sql + " check_check='" + check_check + "' , ";
            }
            if (data.containsKey("check_time_start") && data.getString("check_time_start").length() > 0) {
                check_time_start = data.getString("check_time_start");
                sql = sql + " check_time_start='" + check_time_start + "' , ";
            }
            if (data.containsKey("check_time_end") && data.getString("check_time_end").length() > 0) {
                check_time_end = data.getString("check_time_end");
                sql = sql + " check_time_end='" + check_time_end + "' , ";
            }
            if (data.containsKey("ask_log") && data.getString("ask_log").length() > 0) {
                ask_log = data.getString("ask_log");
                sql = sql + " ask_log='" + ask_log + "' , ";
            }
            if (data.containsKey("out_ls") && data.getString("out_ls").length() > 0) {
                out_ls = data.getString("out_ls");
                sql = sql + " out_ls='" + out_ls + "' , ";
            }
            if (data.containsKey("out_time") && data.getString("out_time").length() > 0) {
                out_time = data.getString("out_time");
                sql = sql + " out_time='" + out_time + "' , ";
            }
            if (data.containsKey("out_reason") && data.getString("out_reason").length() > 0) {
                out_reason = data.getString("out_reason");
                sql = sql + " out_reason='" + out_reason + "' , ";
            }
            if (data.containsKey("wpcl") && data.getString("wpcl").length() > 0) {
                wpcl = data.getString("wpcl");
                sql = sql + " wpcl='" + wpcl + "' , ";
            }
            if (data.containsKey("notback") && data.getString("notback").length() > 0) {
                notback = data.getString("notback");
                sql = sql + " notback='" + notback + "' , ";
            }

            if (data.containsKey("get_user") && data.getString("get_user").length() > 0) {
                get_user = data.getString("get_user");
                sql = sql + " get_user=encode('" + get_user + "','" + RIUtil.enName + "') , ";
            }

            if (data.containsKey("get_user_id") && data.getString("get_user_id").length() > 0) {
                get_user_id = data.getString("get_user_id");
                sql = sql + " get_user_id=encode('" + get_user_id + "','" + RIUtil.enNum + "'), ";
            }
            if (data.containsKey("get_time") && data.getString("get_time").length() > 0) {
                get_time = data.getString("get_time");
                sql = sql + " get_time='" + get_time + "' , ";
            }
            if (dd.containsKey("opt_user") && dd.getString("opt_user").length() > 0) {
                opt_user = dd.getString("opt_user");
            } else {
                return ErrNo.set(467004);
            }
            String sqls = "update hand_area set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);


            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新办案区使用记录", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(467003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private static JSONObject deleteHandArea(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(467008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(467008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update hand_area set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除办案区使用记录", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(467007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

//    public static void main(String[] args) {
//        String sqls = "select *,decode(`name`,'" + RIUtil.enName + "') as `name`,decode(id_num,'" + RIUtil.enNum +
//        "') as id_num," + "decode(address,'" + RIUtil.enContent + "') as address,decode(tel,'" + RIUtil.enTele +
//        "') as tel," + "decode(aj_reason,'" + RIUtil.enContent + "') as aj_reason,decode(self,'" + RIUtil.enContent
//        + "') as self," + "decode(`check`,'" + RIUtil.enContent + "') as `check`,decode(get_user,'" + RIUtil.enName
//        + "') as get_user," + "decode(get_user_id,'" + RIUtil.enNum + "') as get_user_id,decode(birth,'" + RIUtil
//        .enDate + "') as birth " + "from hand_area " + "where 1=1 and id=" + 47 + " order by create_time desc ";
//        List<JSONObject> list = new ArrayList<>();
//        InfoModelHelper mysql = null;
//        try {
//            mysql = InfoModelPool.getModel();
//            list = mysql.query(sqls);
//            TestWordAddContent.Word(list);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//    }

}
