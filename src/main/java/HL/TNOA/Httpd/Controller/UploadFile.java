package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.TNOAConf;
import com.alibaba.fastjson.JSONObject;
import net.coobird.thumbnailator.Thumbnails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServlet;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class UploadFile extends HttpServlet {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());


    //	   ***************************计算文件夹大小**********************
    public double getDirSize(File file) {
        //判断文件是否存在
        if (file.exists()) {
            //如果是目录则递归计算其内容的总大小
            if (file.isDirectory()) {
                File[] children = file.listFiles();
                double size = 0;
                for (File f : children) {
                    size += getDirSize(f);
                }
                return size;
            } else {//如果是文件则直接返回其大小,以“兆”为单位
                double size = (double) file.length() / 1024 / 1024;
                return size;
            }
        } else {
            System.out.println("文件或者文件夹不存在，请检查路径是否正确！");
            return 0.0;
        }
    }

    //	    ***********************随机数******************************
    public StringBuffer RandomNum() {
        int maxNum = 62;
        int i;
        int count = 0;
        int randomlength;
        char[] str = {'z', 'x', 'c', 'v', 'b', 'n', 'm', 'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'q',
                'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', 'Z', 'X', 'C', 'V', 'B', 'N', 'M', 'A', 'S', 'D',
                'F', 'G', 'H', 'J', 'K', 'L', 'Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '0', '1', '2', '3',
                '4', '5', '6', '7', '8', '9'};
        StringBuffer uuid = new StringBuffer();
        Random r = new Random();
        randomlength = r.nextInt(8) + 7;
        while (count < randomlength) {
            i = Math.abs(r.nextInt(maxNum));//最大61
            if (i >= 0 && i < str.length) {
                uuid.append(str[i]);
                count++;
            }
        }
        return uuid;
    }

//	********************************类结尾***************************


    @RequestMapping(method = {RequestMethod.POST}, path = {"/upload_64"})
    @PassToken
    public JSONObject upload_64(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        JSONObject back = ErrNo.set(0);
        String img = "";
        String suffix = "";
        String name = "";
        if (data.containsKey("img") && data.getString("img").length() > 0) {
            img = data.getString("img").replace("|", "/");


        } else {
            return ErrNo.set(439001);
        }
        if (data.containsKey("suffix") && data.getString("suffix").length() > 0) {
            suffix = data.getString("suffix");
        } else {
            return ErrNo.set(439001);
        }
        if (data.containsKey("name") && data.getString("name").length() > 0) {
            name = data.getString("name") + "_" + new SimpleDateFormat("MMddHHmmss").format(new Date());
        } else {
            name = String.valueOf(UUID.randomUUID());
        }
        img = savePic(name + "." + suffix, img);
        back.put("img_id", img);
        return back;
    }

    public String savePic(String imgName, String imgData) throws Exception {

        SimpleDateFormat sdfM = new SimpleDateFormat("MM");
        SimpleDateFormat sdfY = new SimpleDateFormat("yyyy");

        InfoModelHelper infoModel = InfoModelPool.getModel();
        String filePath = TNOAConf.get("file", "img_path") +
                sdfY.format(new Date()) + "/" +
                sdfM.format(new Date()) + "/";


        int id = 0;
        String sql = "";

        if (imgName.endsWith("jpg") || imgName.endsWith("png")) {

            if (generateImage(imgData, imgName, filePath)) {

                sql = "insert into upload (nas_id,file_path,file_name) values" +
                        " ('1','" + sdfY.format(new Date()) + "/" + sdfM.format(new Date()) + "/','" + imgName + "')";

                infoModel.update(sql);
                logger.warn(sql);

                sql = "select * from upload where file_name='" + imgName + "'";

                List<JSONObject> result = infoModel.query(sql);
                JSONObject xJsonObject = result.get(0);
                id = xJsonObject.getIntValue("id");
            } else {
                return "0";
            }
        } else if (imgName.endsWith(".mp4")) {
            if (generteMp4(imgData, imgName, filePath)) {

                sql = "insert into upload (nas_id,file_path,file_name) values" + " ('1','" + sdfY.format(new Date()) + "/" + sdfM.format(new Date()) + "/','" + imgName.replace(".mp4", ".m3u8") + "')";

                infoModel.update(sql);
                logger.warn(sql);

                sql = "select * from upload where file_name='" + imgName.replace(".mp4", ".m3u8") + "'";

                List<JSONObject> result = infoModel.query(sql);
                JSONObject xJsonObject = result.get(0);
                id = xJsonObject.getIntValue("id");
                new File(filePath + imgName).delete();
            } else {
                return "0";
            }
        } else {
            if (generateFile(imgData, imgName, filePath)) {

                sql = "insert into upload (nas_id,file_path,file_name) values" + " ('1','" + sdfY.format(new Date()) + "/" + sdfM.format(new Date()) + "/','" + imgName + "')";

                infoModel.update(sql);
                logger.warn(sql);

                sql = "select * from upload where file_name='" + imgName + "'";

                List<JSONObject> result = infoModel.query(sql);
                JSONObject xJsonObject = result.get(0);
                id = xJsonObject.getIntValue("id");
            } else {
                return "0";
            }
        }

        InfoModelPool.putModel(infoModel);
        return String.valueOf(id);
    }

    private boolean generteMp4(String imgStr, String filename, String file_path) {
        logger.warn(file_path + filename);
        OutputStream out = null;
        try {
            if (imgStr == null) {
                logger.warn("imgstr null");
                return false;
            }
            File f = new File(file_path);
            if (!f.exists()) {
                logger.warn("==flord==:" + file_path);
                f.mkdirs();
                f.setWritable(true, false);
            }
            if (!new File(file_path + filename).exists()) {

                new File(file_path + filename);
            }

            // 解密
            byte[] b = Base64.getMimeDecoder().decode(imgStr);
            logger.warn(String.valueOf(b.length));
            // 处理数据
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;
                }
            }
            out = new FileOutputStream(file_path + filename);
            out.write(b);
            out.flush();

           /* try {
                // 起作用的代码其实就下面这一行, 参数是linux中要执行的代码
                String newFileName = filename.replace("mp4", "m3u8");
                Runtime.getRuntime().exec("./ffmpeg -i " + file_path + filename + " -c:v libx264 -profile:v baseline
                -level 3.0 -b 600k -start_number 0 -hls_time 10 -hls_list_size 0 -f hls " + file_path + newFileName)
                .waitFor();
                logger.warn("./ffmpeg -i " + file_path + filename + " -c:v libx264 -profile:v baseline -level 3.0 -b
                600k -start_number 0 -hls_time 10 -hls_list_size 0 -f hls " + file_path + newFileName);
            } catch (Exception e) {
                logger.error(Lib.getTrace(e));
                return false;
            }
*/

            return true;
        } catch (IOException e) {
            logger.error(Lib.getTrace(e));
            return false;
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    logger.error(Lib.getTrace(e));
                }
            }
        }

    }

    private boolean generateFile(String imgStr, String filename, String file_path) {
        logger.warn(file_path + filename);
        OutputStream out = null;
        try {
            if (imgStr == null) {
                logger.warn("imgstr null");
                return false;
            }
            File f = new File(file_path);
            if (!f.exists()) {
                logger.warn("==flord==:" + file_path);
                f.mkdirs();
                f.setWritable(true, false);
            }
            if (!new File(file_path + filename).exists()) {

                new File(file_path + filename);
            }

            // 解密
            byte[] b = Base64.getMimeDecoder().decode(imgStr);
            logger.warn(String.valueOf(b.length));
            // 处理数据
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;
                }
            }
            out = new FileOutputStream(file_path + filename);
            out.write(b);
            out.flush();
            return true;
        } catch (IOException e) {
            logger.error(Lib.getTrace(e));
            return false;
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    logger.error(Lib.getTrace(e));
                }
            }
        }

    }

    public boolean generateImage(String imgStr, String filename1, String file_path) {
        SimpleDateFormat sdfM = new SimpleDateFormat("MM");
        SimpleDateFormat sdfY = new SimpleDateFormat("yyyy");
        String big_filePath = file_path + "big/";

        logger.warn(big_filePath + filename1);
        OutputStream out = null;
        try {
            if (imgStr == null) {
                logger.warn("imgstr null");
                return false;
            }
            File f = new File(big_filePath);
            if (!f.exists()) {
                logger.warn("==flord==:" + big_filePath);
                f.mkdirs();
                f.setWritable(true, false);
            }
            if (!new File(big_filePath + filename1).exists()) {

                new File(big_filePath + filename1);
            }

            // 解密
            byte[] b = Base64.getMimeDecoder().decode(imgStr);
            // 处理数据
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;
                }
            }
            out = new FileOutputStream(big_filePath + filename1);
            out.write(b);
            out.flush();
            String filePath =
                    TNOAConf.get("file", "img_path") + sdfY.format(new Date()) + "/" + sdfM.format(new Date()) + "/";

            //图片尺寸不变，压缩图片文件大小outputQuality实现,参数1为最高质量
            Thumbnails.of(big_filePath + filename1).scale(1f).outputQuality(0.3f).toFile(file_path + filename1);

            return true;
        } catch (IOException e) {
            logger.error(Lib.getTrace(e));
            return false;
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    logger.error(Lib.getTrace(e));
                }
            }
        }

    }
}
