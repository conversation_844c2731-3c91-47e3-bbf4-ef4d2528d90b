package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
public class CarController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    // private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/car"})
    public JSONObject get_key_info(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();


        String opt = "";

        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_car_info")) {
                return getKeyInfo(data);
            } else if (opt.equals("get_car_event")) {
                return getKeyEvent(data);
            } else if (opt.equals("get_car_gate")) {
                return getGateEvent(data);
            } else if (opt.equals("get_car_use")) {
                return getCarUse(data);
            } else if ("update_car_image".equals(opt)) {
                return updateCarImage(data);
            } else {
                return ErrNo.set(440009);
            }
        } else {
            return ErrNo.set(440009);
        }
    }


    private JSONObject updateCarImage(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String id = "";
        String img = "";
        String keyName = "";
        String sql = "";
        String keyType = "1";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");

        } else {
            return ErrNo.set(440001);
        }
        if (data.containsKey("img") && data.getString("img").length() > 0) {
            img = data.getString("img");
            sql = sql + " img='" + img + "', ";

        } else {

        }
        if (data.containsKey("keyName") && data.getString("keyName").length() > 0) {
            keyName = data.getString("keyName");
            sql = sql + " keyName=encode('" + keyName + "', '" + RIUtil.enNum + "'),";

        } else {

        }

        if (data.containsKey("keyType") && data.getString("keyType").length() > 0) {
            keyType = data.getString("keyType");
            sql = sql + " keyType='" + keyType + "',";

        }
        try {
            mysql = InfoModelPool.getModel();
            String sqls = "update key_info set " + sql + "  isdelete=1 where id='" + id + "'";
            mysql.update(sqls);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(440002);

        } finally {
            InfoModelPool.putModel(mysql);
        }

    }


    private JSONObject getCarUse(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            String user_id = "";

            int limit = 20;
            int page = 1;


            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");

            } else {
                return ErrNo.set(441006);
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }


            mysql = InfoModelPool.getModel();

            String sqls =
                    "select *  from car_use_info where  user_id='" + user_id + "' order by key_get_time desc limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", list);
                sqls = "select count(id) as count from car_use_info where  user_id='" + user_id + "'";

                back.put("count", mysql.query_count(sqls));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(440005);
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return back;
    }

    private JSONObject getGateEvent(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            String car_num = "";
            int limit = 20;
            int page = 1;
            String sqlu = " and 1=1 ";

            if (data.containsKey("car_num") && data.getString("car_num").length() > 0) {
                car_num = data.getString("car_num");

            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                String unit = data.getString("unit");
                sqlu = sqlu + " and unit='" + unit + "'";

            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }


            mysql = InfoModelPool.getModel();

            String sqls =
                    "select * from gate_event where car_num like '%" + car_num + "%' " + sqlu + "order by time desc " +
                            "limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", list);
                sqls = "select count(id)as count from gate_event where car_num like " + sqlu + "'%" + car_num + "%'";

                back.put("count", mysql.query_count(sqls));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(440005);
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return back;
    }

    private JSONObject getKeyInfo(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {


            mysql = InfoModelPool.getModel();
            String sql = "";


            String keyCard = "";
            String keyName = "";
            String keyType = "1";
            String status = "";
            String userName = "";
            String event_id = "";
            String last_time = "";
            String unit = "";


            if (data.containsKey("keyCard") && data.getString("keyCard").length() > 0) {
                keyCard = data.getString("keyCard");
                sql = sql + " keyCard='" + keyCard + "' and ";
            }
            if (data.containsKey("keyName") && data.getString("keyName").length() > 0) {
                keyName = data.getString("keyName");
                sql = sql + "keyName like'%" + keyName + "%' and ";
            }

            if (data.containsKey("keyType") && data.getString("keyType").length() > 0) {
                keyType = data.getString("keyType");
                sql = sql + " keyType='" + keyType + "' and ";
            }

            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getString("status");
                sql = sql + " status='" + status + "' and ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " unit='" + unit + "' and ";
            }
            if (data.containsKey("userName") && data.getString("userName").length() > 0) {
                userName = data.getString("userName");
                sql = sql + " userName like'%" + userName + "%' and ";
            }

            String sqls =
                    "select * from key_info where 1=1 and " + sql + " isdelete=1";
            List<JSONObject> list = new ArrayList<>();
            logger.warn(sqls);
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql, 1));
                sqls = "select id from key_info where 1=1 and " + sql + " isdelete=1";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
            //logger.warn(back.toString());
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(440005);
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return back;
    }

    private JSONObject getKeyEvent(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {


            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;

            String car_num = "";

            String start_time = "";
            String end_time = "";
            String user_id = "";
            String gate_sql = "";
            String sqls = "";
            String sqlu = " and 1=1 ";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                String unit = data.getString("unit");
                sqlu = sqlu + " and unit='" + unit + "' ";
            }
            if (data.containsKey("car_num") && data.getString("car_num").length() > 0) {
                car_num = data.getString("car_num");
                if (car_num.contains("装备")) {
                    car_num = "枪柜钥匙";
                }

            }
            String timeSql = " 1=1";
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0 && data.containsKey(
                    "end_time") && data.getString("end_time").length() > 0) {
                start_time = data.getString("start_time").replace("|", " ");
                end_time = data.getString("end_time").replace("|", " ");
                timeSql = "  (time>='" + start_time + "' and time<='" + end_time + "')";
            }
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
                sqls = "select  id ,keyName as car_num,event as status,time,userName," + "userId from key_event where" +
                        " userId='" + user_id + "' and keyName ='" + car_num + "' and " + timeSql + sqlu;
            } else {
                sqls = "select  id ,keyName as car_num,event as status,time,userName," + "userId from key_event where" +
                        "  keyName='" + car_num + "' and " + timeSql + sqlu;

                gate_sql =
                        " UNION select id,car_num as car_num,direction as status," +
                                "time,'道闸'as userName,'0'as userId from gate_event where car_num='" + car_num + "' " +
                                "and " + timeSql + sqlu;
            }


            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }

            String s = sqls + gate_sql + " order by time desc limit " + limit + " offset " + limit * (page - 1);
            logger.warn(s);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(s);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql, 0));
                s = sqls + gate_sql;
                list = mysql.query(s);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(441005);
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return back;
    }

    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql, int mark) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            //logger.warn(one.toString());
            String car_img = one.getString("img");
            one.remove("img");
            one.put("car_img", car_img);
            String userid = one.getString("userId");
            String username = one.getString("userName");
            if ("管理员".equals(username) || "紧急".equals(username) || "道闸".equals(username)) {

                JSONObject d = new JSONObject();
                d.put("id", "");
                d.put("name", username);

                d.put("position", "");
                d.put("tele_long", "");
                d.put("tele_sort", "");
                d.put("img", "");
                d.put("car_img", "");
                d.put("community", "");
                d.put("isMain", "");
                d.put("birth", "");
                d.put("assType", "");
                one.put("userName", d);

            } else {
                one.put("userName", RIUtil.users.get(userid));
            }
            String keyName = one.getString("keyName");
            String car_type = one.getString("carType");
            if (mark == 1) {
                String sql =
                        "select time from gate_event where car_num='" + keyName + "' order by " +
                                "time desc limit 1; ";
                one.put("gate_time", mysql.query_one(sql, "time"));
                sql = "select img from car_mileage where keyName='" + keyName + "'  order by " +
                        "create_time desc limit 1; ";
                one.put("img", mysql.query_one(sql, "img"));
                sql = "select create_time from car_mileage where keyName='" + keyName + "'  order " +
                        "by" + " create_time desc limit 1; ";
                one.put("img_time", mysql.query_one(sql, "create_time"));
                sql = "select dest from car_mileage where keyName='" + keyName + "'  order by " +
                        "create_time desc limit 1; ";
                one.put("dest", mysql.query_one(sql, "dest"));
            }
            back.add(one);
        }
        return back;
    }
}

