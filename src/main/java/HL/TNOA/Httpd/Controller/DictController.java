package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class DictController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/dict"})
    @PassToken
    public JSONObject get_dict(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        InfoModelHelper mysql = null;
        try {

            mysql = request.openInfoImpl();
            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("get_dict")) {
                    return getDict(data, mysql);
                } else if (opt.equals("create_dict")) {
                    logger.warn("dict--->" + request.getRequestParams().toString());
                    return createDict(data, request.getRemoteAddr(), mysql);
                } else if (opt.equals("update_dict")) {
                    logger.warn("dict--->" + request.getRequestParams().toString());
                    return updateDict(data, request.getRemoteAddr(), mysql);
                } else if (opt.equals("delete_dict")) {
                    logger.warn("dict--->" + request.getRequestParams().toString());
                    return deleteDict(data, request.getRemoteAddr(), mysql);
                } else if (opt.equals("get_yewu")) {
                    return getYW(mysql);
                } else if (opt.equals("get_bzTree")) {
                    return GetBzTree(data);
                } else {
                    return ErrNo.set(465009);
                }
            } else {
                return ErrNo.set(465009);
            }
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private JSONObject GetBzTree(JSONObject data) {
        String type = data.getString("type");

        return getTreeBZType(type);


    }

    private JSONObject getTreeBZType(String type) {
        int t = Integer.parseInt(type);
        JSONObject back = ErrNo.set(0);
        JSONArray tw1 = new JSONArray();
        if (type.equals("11")) {
            String[] types = {"13", "14", "16", "17", "18", "19"};
            for (int a = 0; a < types.length; a++) {
                String ty = types[a];
                JSONArray ones = RIUtil.GetDictByTypeFather(Integer.parseInt(ty), "-1");
                JSONObject one = ones.getJSONObject(0);

                List<JSONObject> nodes = (List<JSONObject>) getTreeBZTypeInf(ty);
                one.put("dets", nodes);
                tw1.add(one);
            }

            back.put("data", tw1);
            return back;
        } else {
            List<JSONObject> nodes = (List<JSONObject>) getTreeBZTypeInf(type);
            for (JSONObject one : nodes) {
                tw1.add(one);
            }
            back.put("data", tw1);
            return back;


        }

    }

    private List<JSONObject> getTreeBZTypeInf(String type) {
        int t = Integer.parseInt(type);
        String fid = "-1";
        JSONArray tw1 = new JSONArray();
        if (Integer.parseInt(type) < 20) {
            JSONArray ones = RIUtil.GetDictByTypeFather(Integer.parseInt(type), "-1");

            JSONObject one = ones.getJSONObject(0);
            System.out.println(one);
            fid = one.getString("id");
            tw1 = RIUtil.GetDictByType(Integer.parseInt(type));
        } else if (t > 1100) {
            t = t - 1100;
            tw1 = RIUtil.GetDictByType(t);
        } else {

            tw1 = RIUtil.GetDictByType(Integer.parseInt(type));
        }

        List<JSONObject> nodes = new ArrayList<>();
        for (int i = 0; i < tw1.size(); i++) {
            JSONObject one = tw1.getJSONObject(i);
            nodes.add(one);
        }

        List<JSONObject> treeNodes = buildTree(nodes, fid);


        return treeNodes;

    }


    private List<JSONObject> getTreeBZTypeInf10(String type, int last) {
        int t = Integer.parseInt(type);
        String fid = "-1";
        JSONArray tw1 = new JSONArray();

        JSONArray ones = RIUtil.GetDictByTypeFather(Integer.parseInt(type), "-1");
        JSONObject one = ones.getJSONObject(0);
        fid = one.getString("id");
        tw1 = RIUtil.GetDictByType(Integer.parseInt(type));


        List<JSONObject> nodes = new ArrayList<>();
        for (int i = 0; i < tw1.size(); i++) {
            JSONObject o = tw1.getJSONObject(i);
            int index = o.getIntValue("static_index");
            if (index != last) {
                nodes.add(o);
            }
        }

        List<JSONObject> treeNodes = buildTree(nodes, fid);


        return treeNodes;

    }

    public static List<JSONObject> buildTree(List<JSONObject> nodeList, String fid) {
        List<JSONObject> tree = new ArrayList<>();
        for (JSONObject node : nodeList) {
            String id = node.getString("id");
            String f_id = node.getString("father_id");
            if (f_id.equals(fid)) {
                //使用递归方法构建子节点树
                List<JSONObject> children = buildTree(nodeList, id);
                children = GetSort(children, "index_no", 1);


                //node.setChildren(children);
                node.put("dets", children);
                tree.add(node);
            }
        }
        return tree;
    }

    public static List<JSONObject> GetSort(List<JSONObject> tw1, String col, int mark) {

        List<JSONObject> list = new ArrayList<>();
        for (int i = 0; i < tw1.size(); i++) {
            JSONObject one = tw1.get(i);
            if (!one.containsKey("index_no") || one.getString("index_no") == null || one.getString("index_no").length() == 0) {
                String id = one.getString("id");
                String index = "";
                if (id.contains("-")) {
                    index = id.split("\\-")[1];
                } else {
                    index = id;
                }
                one.put("index_no", index);

            }
            list.add(one);
        }

        Collections.sort(list, (JSONObject o1, JSONObject o2) -> {
            //转成JSON对象中保存的值类型
            int a = 0;
            int b = 0;

            try {
                a = o1.getInteger(col);
                b = o2.getInteger(col);
            } catch (Exception ex) {

            }

            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (a > b) {
                //降序排列，升序改成a>b
                if (mark == 1) {
                    return 1;
                } else {
                    return -1;
                }
            } else if (a == b) {
                return 0;
            } else {
                if (mark == 1) {
                    return -1;
                } else {
                    return 1;
                }
            }
        });


        return tw1;
    }


    private JSONObject getYW(InfoModelHelper mysql) {
        JSONObject back = ErrNo.set(0);
        String sql = "select id ,dict_name, father_id, type ,label_jz,label_yw from dict where " + " type = 300 ";
        logger.warn("=======>> sql : " + sql);
        try {
            List<JSONObject> query = mysql.query(sql);
            back.put("data", query);
            back.put("count", query.size());
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(465001);
        }
        return back;
    }

    //******CREATE*******
    private JSONObject createDict(JSONObject data, String remoteAddr, InfoModelHelper mysql) {

        JSONObject back = ErrNo.set(0);
        try {

            String father_id = "";
            String type = "";
            String dict_name = "";
            String color = "";
            String index_no = "0";
            String permission = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "1";
            String is_show = "0";
            String remark = "";
            String unit = "";
            if (data.containsKey("father_id") && data.getString("father_id").length() > 0) {
                father_id = data.getString("father_id");
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
            } else {
                return ErrNo.set(465002);
            }
            if (data.containsKey("dict_name") && data.getString("dict_name").length() > 0) {
                dict_name = data.getString("dict_name");
            } else {
                return ErrNo.set(465002);
            }
            if (data.containsKey("color") && data.getString("color").length() > 0) {
                color = data.getString("color");

            }

            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            }
            String id = String.valueOf(UUID.randomUUID());
            String sqls = "insert dict (id,father_id,type," + "dict_name,color,is_show,index_no," + "permission," +
                    "create_user,create_time,isdelete,remark,unit)" + "values('" + id + "','" + father_id + "','" + type + "'," + "'" + dict_name + "','" + color + "','" + is_show + "','" + index_no + "'," + "'" + permission + "','" + create_user + "','" + create_time + "','" + isdelete + "','" + remark + "','" + unit + "')";
            mysql.update(sqls);
            String sql = "select * from dict where id='" + id + "'";
            List<JSONObject> list = mysql.query(sql);

            RIUtil.dicts.put(id, list.get(0));

            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建字典", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(465001);
        }
        return back;
    }

    //******GET*******
    private JSONObject getDict(JSONObject data, InfoModelHelper mysql) {

        JSONObject back = ErrNo.set(0);
        try {

            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String father_id = "";
            String type = "";
            String dict_name = "";
            String is_show = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("father_id")) {
                father_id = data.getString("father_id");
                sql = sql + " father_id='" + father_id + "' and ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type in (" + type + ") and ";
            }
            if (data.containsKey("dict_name") && data.getString("dict_name").length() > 0) {
                dict_name = data.getString("dict_name");
                sql = sql + "dict_name like '%" + dict_name + "%' and ";
            }
            if (data.containsKey("is_show") && data.getString("is_show").length() > 0) {
                is_show = data.getString("is_show");
                sql = sql + "is_show = '" + is_show + "' and ";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select * from dict where 1=1 and " + sql + " isdelete=1 order by index_no limit " + limit + " " +
                            "offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select id from dict where 1=1 and " + sql + " isdelete=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(465005);
        }
        return back;
    }

    //*****relainfo************
    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            back.add(one);
        }

        Collections.sort(back, (JSONObject o1, JSONObject o2) -> {
            //转成JSON对象中保存的值类型
            int a = 0;
            int b = 0;

            try {
                a = o1.getInteger("index_no");
                b = o2.getInteger("index_no");
            } catch (Exception ex) {

            }

            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (a > b) {  //降序排列，升序改成a>b
                return 1;
            } else if (a == b) {
                return 0;
            } else {
                return -1;
            }
        });


        return back;
    }

    //******UPDATE*******
    private JSONObject updateDict(JSONObject data, String remoteAddr, InfoModelHelper mysql) {

        JSONObject back = ErrNo.set(0);
        try {

            String sql = "";
            String id = "";
            String father_id = "";
            String type = "";
            String dict_name = "";
            String color = "";
            String index_no = "0";
            String permission = "";
            String create_user = "";
            String create_time = "";
            String opt_user = "";
            String is_show = "";
            String remark = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(465004);
            }
            if (data.containsKey("father_id") && data.getString("father_id").length() > 0) {
                father_id = data.getString("father_id");
                sql = sql + " father_id='" + father_id + "' , ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type='" + type + "' , ";
            }
            if (data.containsKey("dict_name") && data.getString("dict_name").length() > 0) {
                dict_name = data.getString("dict_name");
                sql = sql + " dict_name='" + dict_name + "' , ";

            }
            if (data.containsKey("color") && data.getString("color").length() > 0) {
                color = data.getString("color");
                sql = sql + " color='" + color + "' , ";

            }
            if (data.containsKey("is_show") && data.getString("is_show").length() > 0) {
                is_show = data.getString("is_show");
                sql = sql + " is_show='" + is_show + "' , ";

            }
            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
                sql = sql + " remark='" + remark + "', ";

            }
            if (data.containsKey("index_no") && data.getString("index_no").length() > 0) {
                index_no = data.getString("index_no");
                sql = sql + " index_no='" + index_no + "' , ";
            }
            if (data.containsKey("permission") && data.getString("permission").length() > 0) {
                permission = data.getString("permission");
                sql = sql + " permission='" + permission + "' , ";

            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(465004);
            }
            String sqls = "update dict set " + sql + " isdelete=1  where id='" + id + "'";
            System.out.println(sqls);
            mysql.update(sqls);

            sql = "select * from dict where id='" + id + "'";
            List<JSONObject> list = mysql.query(sql);
            RIUtil.dicts.put(id, list.get(0));

            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新字典", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(465003);
        }
        return back;
    }

    //*********delete**********
    private JSONObject deleteDict(JSONObject data, String remoteAddr, InfoModelHelper mysql) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(465008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(465008);
        }

        try {

            String sqls =
                    "update dict set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除字典", userlog.TYPE_OPERATE, remoteAddr);
            RIUtil.dicts.remove(id);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(465007);
        }
        return ErrNo.set(0);
    }


}
