package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.eclipse.jetty.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.awt.geom.Point2D;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class MapController {
    private static Logger logger = LoggerFactory.getLogger(MapController.class);
    private static String ip = "";

    @RequestMapping(method = {RequestMethod.POST}, path = {"/map"})
    public static JSONObject get_map(TNOAHttpRequest request) throws Exception {

        String opt = "";
        ip = request.getRemoteAddr();
        JSONObject data = request.getRequestParams();

        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if ("get_map_tree".equals(opt)) {
                return getMapTree(data);
            } else if (opt.equals("get_map_data")) {
                return getMapData(data);
            } else if (opt.equals("get_map_point")) {
                return getMapPoint(data);
            } else if (opt.equals("get_map_rss")) {
                return GetMapRss(data);
            } else if (opt.equals("create_map_rss")) {
                return CreateMapRss(data);
            } else if (opt.equals("delete_map_rss")) {
                return DeleteMapRss(data);
            } else {
                return ErrNo.set(null, 2, "OPT错误");
            }
        } else {
            return ErrNo.set(null, 2, "OPT错误");
        }
    }

    private static JSONObject DeleteMapRss(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        String id = "";
        MysqlHelper mysql = null;
        try {
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(null, 2, "缺少id");
            }

            mysql = new MysqlHelper("mysql");
            String sql = "update map_rss set is_delete=1 where id='" + id + "'";
            mysql.update(sql);

            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));

        } finally {
            mysql.close();
        }
    }

    private static JSONObject CreateMapRss(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        String name = "";
        String opts = "";
        String create_user = "";

        MysqlHelper mysql = null;
        try {
            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");
            } else {
                return ErrNo.set(null, 2, "缺少name");
            }
            if (data.containsKey("opts") && data.getString("opts").length() > 0) {
                opts = data.getString("opts");

            } else {
                return ErrNo.set(null, 2, "opts");
            }

            create_user = data.getString("opt_user");


            mysql = new MysqlHelper("mysql");
            String sql =
                    "insert into map_rss (name,opts,create_user) values ('" + name + "','" + opts + "','" + create_user + "')";
            logger.warn(sql);
            mysql.update(sql);

            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));

        } finally {
            mysql.close();
        }
    }

    private static JSONObject GetMapRss(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String optUser = data.getString("opt_user");
        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("mysql");
            String sql = "select * from map_rss where is_delete=0 and create_user='" + optUser + "'";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                back.put("data", list);
            } else {
                back.put("data", new JSONArray());
            }
            return back;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));

        } finally {
            mysql.close();
        }
    }

    private static JSONObject getMapPoint(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String xyid = "";
        String id = "";
        int page = 1;
        int limit = 20;
        String start_time = "";
        String end_time = "";
        String xys = "";
        String unit = "";

        if (data.containsKey("xyid") && data.getString("xyid").length() > 0) {
            xyid = data.getString("xyid");

        } else {
            // return ErrNo.set(null,2,"缺少参数xyid");
        }
        if (data.containsKey("xys") && data.getString("xys").length() > 0) {
            xys = data.getString("xys");


        } else {
            // return ErrNo.set(null,2,"缺少参数xyid");
        }

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");

        } else {
            return ErrNo.set(null, 2, "缺少参数id");
        }
        unit = data.getString("unit");

        if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
            start_time = data.getString("start_time") + " 00:00:00";
        } else {
            start_time = new SimpleDateFormat("yyyy-MM").format(new Date()) + "-01 00:00:00";
        }
        if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
            end_time = data.getString("end_time");
        } else {
            end_time = RIUtil.getLMonthEnd(start_time);
        }
        String currYYMM = new SimpleDateFormat("YYMM").format(new Date());
        String seaYYMM = start_time.substring(2, 7).replace("-", "");

        String table = "qjjc.sta_xy";
        if (!currYYMM.equals(seaYYMM)) {
            table = table + "_" + seaYYMM;
        }
        if (xys.length() > 0) {

            xyid = GetXYid(xys, unit, id, table);
            if (xyid.length() == 0) {
                xyid = "113,35";
            }
            System.out.println(xyid);
        }

        logger.warn(unit);
        int type = RIUtil.dicts.get(unit).getIntValue("type");
        if (type == 21 || type == 22 || type == 27) {
            unit = unit.substring(0, 4);
        } else if (type == 23 || type == 24 || type == 28) {
            unit = unit.substring(0, 6);
        } else if (type == 25) {
            unit = unit.substring(0, 8);
        } else {

        }
        logger.warn(unit);
        if (data.containsKey("limit") && data.getString("limit").length() > 0) {
            limit = data.getInteger("limit");

        }
        if (data.containsKey("page") && data.getString("page").length() > 0) {
            page = data.getInteger("page");

        }


        MysqlHelper my143 = null;
        OracleHelper ora_hl = null;
        GaussHelper gauss = null;


        try {

            my143 = new MysqlHelper("mysql_zxqc");
            ora_hl = new OracleHelper("ora_hl");
            gauss = new GaussHelper("gauss_hl");
            String sqls = "";
            sqls = RIUtil.dicts.get(id).getString("remark");
            if (xyid.length() > 0) {

                String sql = "select dzids from " + table + " where id in ('" + xyid.replace(",", "','") + "')";
                List<JSONObject> dzs = gauss.query(sql);
                String dzids = "";

                for (int i = 0; i < dzs.size(); i++) {
                    dzids = dzids + dzs.get(i).getString("dzids").replace("|", ",") + ",";
                }

                if (dzids.endsWith(",")) {
                    dzids = dzids.substring(0, dzids.length() - 1);
                }

                dzids = dzids.replace(",", "','");

                sqls = sqls.replace("$dzids$", dzids).replace("$start_time$", start_time).replace("$end_time$",
                        end_time).replace("$xyids$", xyid.replace(",", "','")).replace("$unit$", unit).replace(
                        "$xyid$", xyid.replace(",", "','")).replace("$unit%$", unit + "%");
            }
            else {
                logger.warn("---");
                sqls = sqls.replace("dzid in ('$dzids$')", "1=1").replace("(SJJZD_DZbm in ('$dzids$') or  HJDZ_DZBM " + "in ('$dzids$')  )", "1=1 ").replace("$start_time$", start_time).replace("$end_time$", end_time).replace("CONCAT(lng,lat) in ('$xyids$')", "1=1 ").replace("CONCAT(gpsx,gpsy) in " + "('$xyids$')", "1=1").replace("$unit$", unit).replace("xyid in ('$xyids$')", "1=1").replace("CONCAT(DZZBX, dzzby) in ('$xyid$')", "1=1 ").replace("$unit$", unit).replace("$unit" + "%$", unit + "%").replace("CONCAT(X,Y) in ('$xyid$')", "1=1 ");

            }


            String sql = "";
            if (sqls.contains("camera_div") || sqls.contains("DSJ_JQ") || sqls.contains("@") || sqls.contains(
                    "kjxx_jbxx")) {
                sql = sqls + " offset " + (page - 1) * limit + " rows fetch next " + limit + " rows only";

            } else {
                sql = sqls + " limit " + limit + " offset " + (page - 1) * limit;
            }
            logger.warn(sql);
            List<JSONObject> list = new ArrayList<>();
            if (sqls.contains("zfjly_device_info") || sqls.contains("xsq_curr") || sqls.contains("point_dw") || sqls.contains("zxqc")) {
                list = my143.query(sql);
            } else if (sqls.contains("camera_div") || sqls.contains("DSJ_JQ") || sqls.contains("@") || sqls.contains(
                    "kjxx_jbxx")) {
                list = ora_hl.query(sql);
            } else {

                list = gauss.query(sql);

            }
            if (list.size() > 0) {

                back.put("data", Change2Key(list));
                List<JSONObject> ll = new ArrayList<>();

                if (sqls.contains("zfjly_device_info") || sqls.contains("xsq_curr") || sqls.contains("point_dw")) {
                    ll = my143.query(sqls);
                } else if (sqls.contains("camera_div") || sqls.contains("DSJ_JQ") || sqls.contains("@") || sqls.contains("kjxx_jbxx")) {
                    ll = ora_hl.query(sqls);
                } else {
                    ll = gauss.query(sqls);
                }


                back.put("count", ll.size());
            } else {
                back.put("data", new ArrayList<>());
                back.put("count", 0);
            }

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            my143.close();
            ora_hl.close();
            gauss.close();
        }


    }

    private static String GetXYid(String xys, String unit, String id, String table) {
        logger.warn(xys);
        int type = RIUtil.dicts.get(unit).getIntValue("type");

        String usql = "";
        if (type == 21 || type == 22 || type == 27) {

        } else if (type == 23 || type == 24 || type == 28) {
            usql = " and zrq like '" + unit.substring(0, 6) + "%'";

        } else if (type == 25) {
            usql = " and zrq like '" + unit.substring(0, 8) + "%'";
        } else {
            usql = " and zrq='" + unit + "'";
        }
        GaussHelper gauss = null;
        String xyids = "";
        try {
            gauss = new GaussHelper("gauss_hl");

            String sql = "select id,x,y from " + table + " where dzids is not null and \"" + id + "\">0 " + usql + " "
                    + "order" + " by \"" + id + "\" desc ";
            logger.warn(sql);
            List<JSONObject> list = gauss.query(sql);
            if (list.size() > 0) {


                list = GetRange(xys, list);

                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);

                    xyids = xyids + one.getString("id") + ",";
                }
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            gauss.close();
        }
        //  logger.warn(xys);
        return xyids;
    }

    private static List<JSONObject> Change2Key(List<JSONObject> list) {
        List<JSONObject> back = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);

            if (one.containsKey("name")) {
                one.put("NAME", one.getString("name"));

            }
            if (one.containsKey("id")) {
                one.put("ID", one.getString("id"));
            }
            if (one.containsKey("deviceId")) {
                one.put("ID", one.getString("deviceId"));
            }

            if (one.containsKey("xsqid")) {
                one.put("ID", one.getString("xsqid"));
            }
            String name = one.getString("NAME");
            // logger.warn(name);
            if (name.contains("51-")) {
                one.put("NAME", RIUtil.dicts.get(name).getString("dict_name"));
            }

            back.add(one);

        }

        return back;

    }

    private static JSONObject getMapData(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = data.getString("unit");
        int type = RIUtil.dicts.get(unit).getIntValue("type");
        String usql = "";

        String xys = "";
        if (data.containsKey("xys") && data.getString("xys").length() > 0) {
            xys = data.getString("xys");
        }
        String ids = "";
        String isql = "";
        String cols = "";
        if (data.containsKey("ids") && data.getString("ids").length() > 0) {
            ids = data.getString("ids");
            String[] idds = ids.split(",");
            for (int i = 0; i < idds.length; i++) {
                String id = idds[i];

                isql = isql + " \"" + id + "\">0 or ";
                cols = cols + " \"" + id + "\",";
            }
            if (cols.endsWith(",")) {
                cols = cols.substring(0, cols.length() - 1);
                isql = " and (" + isql.substring(0, isql.length() - 3) + ") ";
            }


        } else {
            return ErrNo.set(null, 2, "缺少参数ids");
        }
        if (type == 21 || type == 22 || type == 27) {

        } else if (type == 23 || type == 24 || type == 28) {
            usql = " and zrq like '" + unit.substring(0, 6) + "%'";

        } else if (type == 25) {
            usql = " and zrq like '" + unit.substring(0, 8) + "%'";
        } else {
            usql = " and zrq='" + unit + "'";
        }
        String start_time = "";
        if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
            start_time = data.getString("start_time");
        } else {
            start_time = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        }
        String currYYMM = new SimpleDateFormat("YYMM").format(new Date());
        String seaYYMM = start_time.substring(2, 7).replace("-", "");


//        MysqlHelper my143=null;
        GaussHelper gauss = null;
        try {
//             my143=new MysqlHelper("mysql_zxqc");
            gauss = new GaussHelper("gauss_hl");
            String tables = "qjjc.sta_xy";
//            String tables = "sta_xy";
            if (!currYYMM.equals(seaYYMM)) {
                tables = tables + "_" + seaYYMM;
            }
            String sql = "select id,x,y,name," + cols + " from " + tables + " where dzids is not null " + usql + isql;
            logger.warn(sql);

            List<JSONObject> list = gauss.query(sql);
//            List<JSONObject> list = my143.query(sql);
            if (list.size() > 0) {

                if (xys.length() > 0) {
                    list = GetRange(xys, list);

                }

                JSONArray datas = GetDataList(list, ids);

                back.put("data", datas);

            } else {
                back.put("data", new JSONArray());
            }

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
//             my143.close();
            gauss.close();
        }
    }

    private static JSONArray GetDataList1(List<JSONObject> list, String ids) {
        String[] idds = ids.split(",");
        HashMap<String, JSONArray> dets = new HashMap<>();
        for (int i = 0; i < idds.length; i++) {
            String id = idds[i];

            dets.put(id, new JSONArray());
            if (i == 0) {
                System.out.println(dets);
            }

        }

        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            if (i == 0) {
                System.out.println(one);
            }
            for (int a = 0; a < idds.length; a++) {
                String id = idds[a];
                if (one.containsKey(id)) {
                    int count = one.getIntValue(id);
                    if (count > 0) {
                        JSONObject d = new JSONObject();

                        d.put("c", count);

                        d.put("x", one.getString("x"));
                        d.put("y", one.getString("y"));


                        JSONArray det = dets.get(id);
                        det.add(d);
                        dets.put(id, det);
                    }
                }

            }

        }
        JSONArray back = new JSONArray();
        for (Map.Entry<String, JSONArray> d : dets.entrySet()) {
            JSONObject one = RIUtil.dicts.get(d.getKey());
            one.remove("dets");
            one.put("det", d.getValue());
            back.add(one);
        }
        return back;


    }

    private static JSONArray GetDataList(List<JSONObject> list, String ids) {
        JSONArray back = new JSONArray();

        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);

            JSONObject det = new JSONObject();
            det.put("x", one.getString("x"));
            det.put("y", one.getString("y"));
            one.remove("id");
            one.remove("x");
            one.remove("y");

            JSONArray dets = new JSONArray();
            String name = "";
            for (Map.Entry<String, Object> o : one.entrySet()) {
                String key = o.getKey();
                if (!"name".equals(key)) {
                    int c = 0;
                    if (!ObjectUtil.isEmpty(o.getValue())) {
                        c = Integer.parseInt(o.getValue().toString());
                    }
                    JSONObject d = new JSONObject();
                    if (c > 0) {
                        d.put("i", key);
                        if (c > 1 || StringUtil.isBlank(name)) {
                            d.put("n", RIUtil.dicts.get(key).getString("dict_name"));
                        } else {
                            d.put("n", name);
                        }
                        d.put("c", o.getValue());
                        dets.add(d);
                    }
                    else {
                        d.put("n", RIUtil.dicts.get(key).getString("dict_name"));
                    }
                }
                else {
                    if (!ObjectUtil.isEmpty(o.getValue()))
                        name = o.getValue().toString();
                }
            }

            det.put("d", dets);
            back.add(det);

        }

        return back;


    }

    private static List<JSONObject> GetRange(String xxyy, List<JSONObject> list) {
        List<JSONObject> back = new ArrayList<>();
        String lll[] = xxyy.split("\\|");
        for (int b = 0; b < lll.length; b++) {
            String xys = lll[b];

            String[] ll = xys.split("\\;");

            double[] longa = new double[ll.length];
            double[] lati = new double[ll.length];

            for (int a = 0; a < ll.length; a++) {
                String[] l = ll[a].split(",");
                String oo = l[0];
                String aa = l[1];
                longa[a] = Double.parseDouble(oo);
                lati[a] = Double.parseDouble(aa);
            }
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                String lng = one.getString("x");
                String lat = one.getString("y");

                try {
                    if (isInPolygon(Double.parseDouble(lng), Double.parseDouble(lat), longa, lati)) {
                        back.add(one);
                    } else {
                    }
                } catch (Exception ex) {

                }
            }
        }
        return back;

    }

    private static JSONObject getMapTree(JSONObject data) {

        JSONObject back = ErrNo.set(0);
        try {
            JSONArray type1 = RIUtil.GetDictByType(230);

            JSONArray type2 = RIUtil.GetDictByType(231);

            JSONArray type3 = RIUtil.GetDictByType(232);

            JSONArray type4 = RIUtil.GetDictByType(233);

            JSONArray rest = new JSONArray();
            JSONArray t1 = new JSONArray();
            for (int i = 0; i < type1.size(); i++) {
                JSONObject one1 = type1.getJSONObject(i);

                String id1 = one1.getString("id");

                JSONArray t2 = new JSONArray();
                for (int a = 0; a < type2.size(); a++) {
                    JSONObject one2 = type2.getJSONObject(a);
                    if (one2.getString("father_id").equals(id1)) {
                        String id2 = one2.getString("id");

                        JSONArray t3 = new JSONArray();

                        for (int b = 0; b < type3.size(); b++) {
                            JSONObject one3 = type3.getJSONObject(b);
                            if (one3.getString("father_id").equals(id2)) {
                                String id3 = one3.getString("id");

                                JSONArray t4 = new JSONArray();

                                for (int c = 0; c < type4.size(); c++) {
                                    JSONObject one4 = type4.getJSONObject(c);
                                    if (one4.getString("father_id").equals(id3)) {

                                        t4.add(one4);
                                    }
                                }

                                one3.put("dets", t4);
                                t3.add(one3);
                            }
                        }

                        one2.put("dets", t3);
                        t2.add(one2);
                    }

                }

                one1.put("dets", t2);
                t1.add(one1);


            }
            rest.add(t1);
            back.put("data", rest);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }

    }

    public static boolean isInPolygon(double pointLon, double pointLat, double[] lon, double[] lat) {
        // 将要判断的横纵坐标组成一个点
        Point2D.Double point = new Point2D.Double(pointLon, pointLat);
        // 将区域各顶点的横纵坐标放到一个点集合里面
        List<Point2D.Double> pointList = new ArrayList<Point2D.Double>();
        double polygonPoint_x = 0.0, polygonPoint_y = 0.0;
        for (int i = 0; i < lon.length; i++) {
            polygonPoint_x = lon[i];
            polygonPoint_y = lat[i];
            Point2D.Double polygonPoint = new Point2D.Double(polygonPoint_x, polygonPoint_y);
            pointList.add(polygonPoint);
        }
        return check(point, pointList);
    }

    /**
     * 一个点是否在多边形内
     *
     * @param point   要判断的点的横纵坐标
     * @param polygon 组成的顶点坐标集合
     * @return
     */
    private static boolean check(Point2D.Double point, List<Point2D.Double> polygon) {
        java.awt.geom.GeneralPath peneralPath = new java.awt.geom.GeneralPath();

        Point2D.Double first = polygon.get(0);
        // 通过移动到指定坐标（以双精度指定），将一个点添加到路径中
        peneralPath.moveTo(first.x, first.y);
        polygon.remove(0);
        for (Point2D.Double d : polygon) {
            // 通过绘制一条从当前坐标到新指定坐标（以双精度指定）的直线，将一个点添加到路径中。
            peneralPath.lineTo(d.x, d.y);
        }
        // 将几何多边形封闭
        peneralPath.lineTo(first.x, first.y);
        peneralPath.closePath();
        // 测试指定的 Point2D 是否在 Shape 的边界内。
        return peneralPath.contains(point);
    }

}
