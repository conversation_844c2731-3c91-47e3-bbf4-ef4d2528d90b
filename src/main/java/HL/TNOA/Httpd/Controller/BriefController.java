package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.wechat.wechatMsgTemp;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class BriefController {
    private static Logger logger = LoggerFactory.getLogger(BriefController.class);
    // private SimpleDateFormat sdf =;
    private static String ip = "";

    @RequestMapping(method = {RequestMethod.POST}, path = {"/brief"})
    public JSONObject get_brief(TNOAHttpRequest request) throws Exception {

        String opt = "";
        ip = request.getRemoteAddr();
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("brief_get")) {
                return getBrief(data);
            } else if (opt.equals("brief_create")) {
                logger.warn("brief--->" + data.toString());
                return createBrief(data);
            } else if (opt.equals("brief_delete")) {
                return deleteBrief(data);
            } else if (opt.equals("brief_claim")) {
                logger.warn("brief--->" + data.toString());
                return claimBrief(data);
            } else if (opt.equals("brief_up_status")) {
                logger.warn("brief--->" + data.toString());
                return upStatus(data);
            } else if (opt.equals("get_attendance")) {
                return getAttendance(data);
            } else if (opt.equals("brief_imp_static")) {
                return getBriefImpStatic(data);
            } else if (opt.equals("brief_trend")) {
                return getBriefTrend(data);
            } else if (opt.equals("brief_update")) {
                logger.warn("brief--->" + data.toString());
                return updateBreif(data);
            } else {
                return ErrNo.set(434009);
            }
        } else {
            return ErrNo.set(434009);
        }


    }

    private static JSONObject updateBreif(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String title = "";
            String content = "";
            String level = "";
            String create_user = "";
            String accepter = "";
            String create_time = "";
            String rela_id = "";
            String type = "";
            String opt_user = "";
            String img = "";
            String accessory = "";
            String link = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(434013);
            }
            if (data.containsKey("title") && data.getString("title").length() > 0) {
                title = data.getString("title");
                sql = sql + " title=encode('" + title + "','" + RIUtil.enTitle + "') , ";
            }
            if (data.containsKey("content") && data.getString("content").length() > 0) {
                content = data.getString("content");
                sql = sql + " content=encode('" + content + "','" + RIUtil.enContent + "') , ";
            }
            if (data.containsKey("level") && data.getString("level").length() > 0) {
                level = data.getString("level");
                sql = sql + " level='" + level + "' , ";
            }

            if (data.containsKey("accepter")) {
                accepter = data.getString("accepter");
                sql = sql + " accepter='" + accepter + "' , ";
            }

            if (data.containsKey("rela_id")) {
                rela_id = data.getString("rela_id");
                sql = sql + " rela_id='" + rela_id + "' , ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type='" + type + "' , ";
            }
            if (data.containsKey("img")) {
                img = data.getString("img");
                sql = sql + " img='" + img + "' , ";
            }
            if (data.containsKey("accessory")) {
                accessory = data.getString("accessory");
                sql = sql + " accessory='" + accessory + "' , ";
            }
            if (data.containsKey("link")) {
                link = data.getString("link");
                sql = sql + " link='" + link + "' , ";
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(434013);
            }
            String sqls = "update brief set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新通报", userlog.TYPE_OPERATE, "");
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(434012);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject getBriefTrend(JSONObject data) throws ParseException {
        String start_date = RIUtil.GetNextDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()), -7);
        String end_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        String type = "4";
        String sql = "";
        String usql = " and 1=1";
        if (data.containsKey("start_date") && data.getString("start_date").length() > 0
                && data.containsKey("end_date") && data.getString("end_date").length() > 0) {
            start_date = data.getString("start_date");
            end_date = data.getString("end_date");
        }

        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
        }
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            String unit = data.getString("unit");

            usql = " and unit='" + unit + "'";
        }

        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String date = start_date;
            JSONArray datas = new JSONArray();
            JSONObject ond = new JSONObject();


            while (!date.equals(end_date)) {
                ond = new JSONObject();
                sql = "select count(id) as count " +
                        "from brief " +
                        "where isdelete=1 and type ='" + type + "' and " +
                        "create_time >='" + date + " 00:00:00' and create_time<='" + date + " 23:59:59'" + usql;
                ond.put("date", date);
                ond.put("count", mysql.query_count(sql));
                logger.warn(ond.toString());
                datas.add(ond);
                date = RIUtil.GetNextDate(date, +1);

            }
            sql = "select count(id) as count from brief" +
                    " where isdelete=1 and type ='" + type + "' and " +
                    "create_time >='" + end_date + " 00:00:00' and create_time<='" + date + " 23:59:59'" + usql;
            ond = new JSONObject();
            ond.put("date", date);
            ond.put("count", mysql.query_count(sql));
            datas.add(ond);


            back.put("data", datas);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(431010);
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private static JSONObject getBriefImpStatic(JSONObject data) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        String today1 = sd.format(new Date()) + " 00:00:00";
        String today_e = sd.format(new Date()) + " 23:59:59";
        String month1 = sdf.format(getmindate());
        String month_e = sdf.format(getmaxdate());
        String week1 = getWeekStart();
        String week_end = getWeekEnd();

        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String usql = " and 1=1 ";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            String unit = data.getString("unit");
            usql = " and unit='" + unit + "'";

        }
        try {
            mysql = InfoModelPool.getModel();
            JSONObject datas = new JSONObject();
            String sql = "select count(id) as count from brief where isdelete=1 and type =4" + usql;
            int count = mysql.query_count(sql);
            datas.put("total", count);
            //month
            sql = "select count(id) as count from brief " +
                    "where isdelete=1 and type =4 and create_time >='" + month1 + "' and create_time<='" + month_e +
                    "'" + usql;
            datas.put("month", mysql.query_count(sql));
            //week
            sql = "select count(id) as count  from brief " +
                    "where isdelete=1 and type =4 and create_time >='" + week1 + "' and create_time<='" + week_end +
                    "'" + usql;
            datas.put("week", mysql.query_count(sql));
            //today
            sql = "select count(id) as count from brief " +
                    "where isdelete=1 and type =4 and create_time >='" + today1 + "' and create_time<='" + today_e +
                    "'" + usql;
            datas.put("today", mysql.query_count(sql));

            back.put("data", datas);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(431010);
        } finally {
            InfoModelPool.putModel(mysql);
        }


    }

    private static JSONObject getAttendance(JSONObject data) {
        String user_id = "";
        String start_time = "";
        String end_time = "";
        String sql = "";

        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");
            sql = " user_id='" + user_id + "' and ";
        }

        if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
            start_time = data.getString("start_time").replace("|", " ");
            sql = sql + " time>='" + start_time + "' and ";
        }
        if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
            end_time = data.getString("end_time").replace("|", " ");
            sql = sql + "  time<='" + end_time + "' and ";
        }
        sql = sql + " (time>='" + start_time + "' and time<='" + end_time + "')  ";
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sqls = "select * from attendance where 1=1 and " + sql + " order by time ";
            List<JSONObject> list = mysql.query(sqls);
            back.put("data", list);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(431010);
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    private static JSONObject upStatus(JSONObject data) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");

        } else {
            return ErrNo.set(434011);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");

        } else {
            return ErrNo.set(434011);
        }

        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls = "update brief set status =2 where id='" + id + "'";
            mysql.update(sqls);
            String comment =
                    RIUtil.IdToName(opt_user, mysql, " name", "user") + "结束该通报";
            sqls = "insert comment (notice_id,comment,comment_user," +
                    "create_time,father_comment_id,comment_type,isdelete,source)" +
                    "values('" + id + "',encode('" + comment + "','" + RIUtil.enContent + "'),'" + opt_user + "'," +
                    "'" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "','','" + 1 + "','" + 1 + "','" + 3 + "') ";
            mysql.update(sqls);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(431010);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);


    }


    private static JSONObject getBrief(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {

            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String title = "";
            String content = "";
            String level = "";
            String create_user = "";
            String accepter = "";
            String create_time_start = "";
            String create_time_end = "";
            String rela_id = "";
            String type = "0";
            String status = "0";
            String unit = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " unit='" + unit + "' and ";
            }

            if (data.containsKey("content") && data.getString("content").length() > 0) {
                content = data.getString("content");
                sql = sql + " (decode(content,'" + RIUtil.enContent + "') like'%" + content + "%' " +
                        "or decode(title,'" + RIUtil.enTitle + "') like '%" + content + "%')and ";
            }
            if (data.containsKey("level") && data.getString("level").length() > 0) {
                level = data.getString("level");
                sql = sql + " level='" + level + "' and ";
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' and ";
            }
            if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
                accepter = data.getString("accepter");
                sql = sql + " (accepter like'%" + accepter + "%' or accepter='') and";
            }
            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {

                create_time_start = data.getString("create_time_start").replace("|", " ");

                sql = sql + " create_time>='" + create_time_start + "'  and ";
            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end").replace("|", " ");
                sql = sql + " create_time<='" + create_time_end + "' and ";
            }
            if (data.containsKey("rela_id") && data.getString("rela_id").length() > 0) {
                rela_id = data.getString("rela_id");
                sql = sql + " rela_id='" + rela_id + "' and ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type='" + type + "' and ";
            }
            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getString("status");
                sql = sql + " status='" + status + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select *,decode(content,'" + RIUtil.enContent + "') as content,decode(title,'" + RIUtil.enTitle +
                            "') as title " +
                            "from brief where 1=1 and " + sql +
                            " isdelete=1 order by create_time desc limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            ///logger.warn(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select count(id) as count from brief where 1=1 and " + sql + " isdelete=1 ";

                back.put("count", mysql.query_count(sqls));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(433001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            // logger.warn(one.toString());
            //等级
            String level = one.getString("level");
            JSONArray le = new JSONArray();
            if (level.startsWith("-1")) {
                level = level.replace("-1", "");
            } else {
                if (level.length() > 0) {
                    le = RIUtil.RealDictNameList(RIUtil.StringToList(level));
                }
            }
            one.put("level", le);
            //下发人
            String create_user = one.getString("create_user");
            try {
                JSONObject cus = RIUtil.users.get(create_user);
                one.put("create_user", cus);
            } catch (Exception ex) {

            }
            //accepter
            String accepter = one.getString("accepter");
            //logger.warn("accepter->" + accepter);
            JSONArray acc = new JSONArray();
            if (accepter.length() > 0) {
                acc = RIUtil.UseridToNames(RIUtil.StringToList(accepter));
            }
            one.put("accepter", acc);

            //comments
            one.put("comment_text", NoticeController.GetCommentText(one.getString("id"), mysql, 3, 1));

            //claiming
            String claiming = one.getString("claiming");
            JSONArray cing = new JSONArray();
            if (claiming.length() > 0) {
                cing = RIUtil.UseridToNames(RIUtil.StringToList(claiming));
            }
            one.put("claiming", cing);

            //claimed
            String claimed = one.getString("claimed");
            JSONArray ced = new JSONArray();
            if (claimed.length() > 0) {
                ced = RIUtil.UseridToNames(RIUtil.StringToList(claimed));
            }
            one.put("claimed", ced);

            //accessory
            String accessory = one.getString("accessory");
            JSONArray access = new JSONArray();
            if (accessory.length() > 0) {

                HashMap<String, String> accesss = RIUtil.StringToList(accessory);
                for (Map.Entry<String, String> a : accesss.entrySet()) {
                    String d = a.getKey();
                    String name = RIUtil.IdToName(d, mysql, "file_name", "upload");
                    JSONObject aone = new JSONObject();
                    aone.put("file_id", d);
                    aone.put("file_name", name);
                    access.add(aone);
                }
            }
            one.put("accessory", access);
            //img
            String img = one.getString("img");
            JSONArray imgs = new JSONArray();
            if (img.length() > 0) {

                HashMap<String, String> imgss = RIUtil.StringToList(img);
                for (Map.Entry<String, String> a : imgss.entrySet()) {
                    String d = a.getKey();
                    String name = RIUtil.IdToName(d, mysql, "file_name", "upload");
                    JSONObject aone = new JSONObject();
                    aone.put("file_id", d);
                    aone.put("file_name", name);
                    String path = RIUtil.IdToName(d, mysql, "file_path", "upload");
                    aone.put("file_path", path);
                    imgs.add(aone);
                }
            }
            one.put("img", imgs);
            back.add(one);
        }
        return back;

    }

    private static JSONObject createBrief(JSONObject data) {

        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {

            mysql = InfoModelPool.getModel();

            String title = "";
            String content = "";
            String level = "";
            String create_user = "";
            String accepter = "";
            String create_time = "";
            String rela_id = "0";
            String type = "4";
            String isdelete = "1";
            String img = "";
            String accessory = "";
            String link = "";

            String unit = "";
            if (data.containsKey("title") && data.getString("title").length() > 0) {
                title = data.getString("title");
            }
            if (data.containsKey("content") && data.getString("content").length() > 0) {
                content = data.getString("content");
            }
            if (data.containsKey("level") && data.getString("level").length() > 0) {
                level = data.getString("level");
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
            }
            if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
                accepter = data.getString("accepter");

            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");

            }
            if (data.containsKey("create_time") && data.getString("create_time").length() > 0) {
                create_time = data.getString("create_time").replace("|", " ");
            }
            if (data.containsKey("rela_id") && data.getString("rela_id").length() > 0) {
                rela_id = data.getString("rela_id");
            }
            if (data.containsKey("img") && data.getString("img").length() > 0) {
                img = data.getString("img");
            }
            if (data.containsKey("accessory") && data.getString("accessory").length() > 0) {
                accessory = data.getString("accessory");
            }
            if (data.containsKey("link") && data.getString("link").length() > 0) {
                link = data.getString("link");
            }
            String sqls = "insert brief (title,content,level," +
                    "create_user,accepter,create_time,rela_id,type,isdelete,claiming," +
                    "img,accessory,link,unit)" +
                    "values(encode('" + title + "','" + RIUtil.enTitle + "'),encode('" + content + "','" + RIUtil.enContent + "'),'" + level + "'," +
                    "'" + create_user + "','" + accepter + "','" + create_time + "','" + rela_id + "','" + type + "'," +
                    "'" + isdelete + "','" + accepter + "'," +
                    "'" + img + "','" + accessory + "','" + link + "','" + unit + "')";
            mysql.update(sqls);
            sqls = "select id from brief where decode(title,'')='" + title + "' and create_time='" + create_time + "'" +
                    " and create_user='" + create_user + "'";
            String brief_id = mysql.query_one(sqls, "id");
            if (accepter.length() > 0) {
                List<String> acc = RIUtil.HashToList(RIUtil.StringToList(accepter));
                for (int i = 0; i < acc.size(); i++) {
                    wechatMsgTemp.createMessage_brief(acc.get(i), brief_id, title, create_user, mysql);
                }
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(434001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    private static JSONObject claimBrief(JSONObject data) throws Exception {
        String id = "";
        String user_id = "";
        String status = "1";
        String comment = "认领该通报";

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");

        } else {
            return ErrNo.set(434007);
        }
        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");

        } else {
            return ErrNo.set(434007);
        }
        if (data.containsKey("status") && data.getString("status").length() > 0) {
            status = data.getString("status");

        }
        InfoModelHelper mysql = null;
        List<String> claiming = new ArrayList<>();
        List<String> claimed = new ArrayList<>();
        try {
            mysql = InfoModelPool.getModel();
            String sqls = "select * from brief where id='" + id + "' and isdelete=1";
            List<JSONObject> list = mysql.query(sqls);
            if (list.size() > 0) {
                JSONObject one = list.get(0);
                String accepter = one.getString("accepter");
                String cing = one.getString("claiming");
                HashMap<String, String> cingh = new HashMap<>();
                HashMap<String, String> cedh = new HashMap<>();
                HashMap<String, String> acc = new HashMap<>();
                if (accepter.length() > 0) {
                    acc = RIUtil.StringToList(accepter);
                }
                if (cing.length() > 2) {
                    cingh = RIUtil.StringToList(cing);
                }
                String ced = one.getString("claimed");
                if (ced.length() > 2) {
                    cedh = RIUtil.StringToList(ced);
                }
                if (status.equals("1")) {
                    if (accepter.length() <= 2) {
                        accepter = user_id;
                        claimed.add(accepter);

                    } else {
                        cingh.remove(user_id);
                        cedh.put(user_id, "");
                        acc.put(user_id, "");
                        claiming = RIUtil.HashToList(cingh);
                        claimed = RIUtil.HashToList(cedh);
                        accepter = RIUtil.HashToList(acc).toString();
                    }
                } else {
                    comment = "撤回认领通报";

                    cingh.put(user_id, "");
                    cedh.remove(user_id);
                    claiming = RIUtil.HashToList(cingh);
                    claimed = RIUtil.HashToList(cedh);
                    acc.remove(user_id);
                    accepter = RIUtil.HashToList(acc).toString();
                    sqls = "delete  from `comment` " +
                            "where notice_id='" + id + "' and comment_user='" + user_id + "' " +
                            "and DECODE(`comment`,'" + RIUtil.enContent + "') like '%认领该通报%'";
                    mysql.update(sqls);

                }

                sqls = "update brief set accepter='" + accepter + "',claiming='" + claiming + "',claimed='" + claimed + "' where id='" + id + "'";
                mysql.update(sqls);

                String sql = "insert comment (notice_id,comment,comment_user," +
                        "create_time,father_comment_id,comment_type,isdelete,source)" +
                        "values('" + id + "',encode('" + comment + "','" + RIUtil.enContent + "'),'" + user_id + "'," +
                        "'" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "','','" + 1 + "','" + 1 + "','" + 3 + "') ";
                mysql.update(sql);
            } else {
                return ErrNo.set(434009);
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(434008);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

    private static JSONObject deleteBrief(JSONObject data) {
        String id = "";
        String opt_user = "";
        InfoModelHelper mysql = null;
        try {

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(434006);
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");

            } else {
                return ErrNo.set(434006);
            }

            mysql = InfoModelPool.getModel();
            String sqls = "update brief set isdelete =2,delete_user='" + opt_user + "'," +
                    "delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);

            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除通报",
                    userlog.TYPE_OPERATE, ip);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(431005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

    public static Date getmindate() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));

        return calendar.getTime();
    }

    /**
     * 获取本月最后一天
     *
     * @return
     */
    public static Date getmaxdate() {
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(new Date());
        calendar2.set(Calendar.DAY_OF_MONTH, calendar2.getActualMaximum(Calendar.DAY_OF_MONTH));
        return calendar2.getTime();
    }

    /**
     * 获取本周的第一天
     *
     * @return String
     **/
    public static String getWeekStart() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.WEEK_OF_MONTH, 0);
        cal.set(Calendar.DAY_OF_WEEK, 2);
        Date time = cal.getTime();
        return new SimpleDateFormat("yyyy-MM-dd").format(time) + " 00:00:00";
    }

    /**
     * 获取本周的最后一天
     *
     * @return String
     **/
    public static String getWeekEnd() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, cal.getActualMaximum(Calendar.DAY_OF_WEEK));
        cal.add(Calendar.DAY_OF_WEEK, 1);
        Date time = cal.getTime();
        return new SimpleDateFormat("yyyy-MM-dd").format(time) + " 23:59:59";
    }

}
