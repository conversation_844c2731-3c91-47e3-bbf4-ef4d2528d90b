package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import com.alibaba.fastjson.JSONObject;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
public class UserLogController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());

    @RequestMapping(method = {RequestMethod.POST}, path = {"/user_log"})
    public JSONObject get_user(TNOAHttpRequest request) throws Exception {
        Map<String, String[]> map = request.getParameterMap();
        StringBuffer sql = new StringBuffer(
                "select user_log.*, user.username from user_log left join user on user.id=user_log.user_id ");
        boolean hasTerm = false;
        if (map.containsKey("user_id")) {
            String user_id = map.get("user_id")[0].trim();
            if (Strings.isNotEmpty(user_id)) {
                if (!hasTerm) {
                    sql.append(" where ");
                    hasTerm = true;
                } else {
                    sql.append(" and ");
                }
                sql.append(" user_log.user_id like '%")
                        .append(user_id)
                        .append("%' ");
            }
        }
        if (map.containsKey("username")) {
            String username = map.get("username")[0];
            if (Strings.isNotEmpty(username)) {
                if (!hasTerm) {
                    sql.append(" where ");
                    hasTerm = true;
                } else {
                    sql.append(" and ");
                }
                sql.append(" user.username like '%")
                        .append(username)
                        .append("%' ");
            }
        }
        if (map.containsKey("type")) {
            String type = map.get("type")[0];
            if (Strings.isNotEmpty(type)) {
                if (!hasTerm) {
                    sql.append(" where ");
                    hasTerm = true;
                } else {
                    sql.append(" and ");
                }
                sql.append(" user_log.type like '")
                        .append(type)
                        .append("' ");
            }
        }
        if (map.containsKey("start_time")) {
            String start_time = request.getParameter("start_time");

            if (Strings.isNotEmpty(start_time)) {
                if (!hasTerm) {
                    sql.append(" where ");
                    hasTerm = true;
                } else {
                    sql.append(" and ");
                }
                sql.append(" time >='")
                        .append(start_time)
                        .append("' ");
            }
        }
        if (map.containsKey("end_time")) {
            String end_time = request.getParameter("end_time");
            if (Strings.isNotEmpty(end_time)) {
                if (!hasTerm) {
                    sql.append(" where ");
                    hasTerm = true;
                } else {
                    sql.append(" and ");
                }
                sql.append(" time <='")
                        .append(end_time)
                        .append("' ");
            }
        }

        if (map.containsKey("source")) {
            String source = request.getParameter("end_time");
            if (Strings.isNotEmpty(source)) {
                if (!hasTerm) {
                    sql.append(" where ");
                    hasTerm = true;
                } else {
                    sql.append(" and ");
                }
                sql.append(" source ='")
                        .append(source)
                        .append("' ");
            }
        }

        sql.append(" order by user_log.time desc ");

        InfoModelHelper infoModel = request.openInfoImpl();
        List<JSONObject> list = infoModel.query(sql.toString());
        int count = list.size();

        if (map.containsKey("page") && map.containsKey("limit")) {
            int page = Integer.parseInt(request.getParameter("page"));
            int limit = Integer.parseInt(request.getParameter("limit"));
            int os = (page - 1) * limit;
            sql.append(" limit ")
                    .append(os)
                    .append(", ")
                    .append(limit)
                    .append(" ");
            list.clear();
            list = infoModel.query(sql.toString());
        }

        logger.warn(sql.toString());

        JSONObject retJSON = ErrNo.set(0);
        retJSON.put("count", count);
        retJSON.put("data", list);

        return retJSON;
    }
}
