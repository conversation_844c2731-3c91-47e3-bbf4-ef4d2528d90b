package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.wechat.ConnectWeChat;
import HL.TNOA.wechat.HttpConnection;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class StatisticController {
    private static Logger logger = LoggerFactory.getLogger(StatisticController.class);
    // private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static String ip = "";

    @RequestMapping(method = {RequestMethod.POST}, path = {"/static"})
    public static JSONObject getResult(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        logger.warn("sta_--->" + data.toString());
        ip = request.getRemoteAddr();
        String opt = "";

        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("sta_notice_count")) {
                return getNoticeCount(data);
            } else if (opt.equals("sta_task_count")) {
                return getTaskCount(data);
            } else if (opt.equals("sta_visit_count")) {
                return getVisitCount(data);
            } else if (opt.equals("sta_adv")) {
                return getAdvance(data);
            } else if (opt.equals("sta_adv_count")) {
                return getAdvanceCount(data);
            } else if (opt.equals("sta_check")) {
                return getBriefStatistic(data);
            } else if (opt.equals("sta_rank")) {
                return getRank(data);
            } else if (opt.equals("sta_check_detail")) {
                return getCheckDetail(data);
            } else {
                return ErrNo.set(446001);
            }
        } else {
            return ErrNo.set(446001);
        }
    }



    private static JSONObject getCheckDetail(JSONObject data) {
        String time = new SimpleDateFormat("yyMM").format(new Date());
        String user_id = "";
        String usql = "1=1 and";
        String start_time = "";
        String end_time = "";
        String unit = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            usql = usql + " a.unit='" + unit + "' and ";
        }
        if (data.containsKey("time") && data.getString("time").length() > 0) {
            time = data.getString("time");

            if (time.contains("_"))//季度
            {
                String[] t = time.split("\\_");
                if (t[1].equals("01")) {
                    start_time = "20" + t[0] + "-01-01 00:00:00";
                    end_time = "20" + t[0] + "-03-31 23:59:59";
                }
                if (t[1].equals("02")) {
                    start_time = "20" + t[0] + "-04-01 00:00:00";
                    end_time = "20" + t[0] + "-06-30 23:59:59";
                }
                if (t[1].equals("03")) {
                    start_time = "20" + t[0] + "-07-01 00:00:00";
                    end_time = "20" + t[0] + "-09-30 23:59:59";
                }
                if (t[1].equals("04")) {
                    start_time = "20" + t[0] + "-10-01 00:00:00";
                    end_time = "20" + t[0] + "-12-31 23:59:59";
                }
            } else if (time.contains(".")) {//月
                time = time.replace(".", "");
                start_time = "20" + time.substring(0, 2) + "-" + time.substring(2, 4) + "-01 00:00:00";
                end_time = RIUtil.getLMonthEnd(start_time);
            } else {//年
                start_time = time + "-01-01 00:00:00";
                end_time = time + "-12-31 23:59:59";
                time = time + "Y";
            }
            initCheckStatic(time, start_time, end_time, unit);

        }
        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");
            usql = usql + " a.user_id='" + user_id + "' and ";
        }

        String isMain = "";
        if (data.containsKey("isMain") && data.getString("isMain").length() > 0) {
            isMain = data.getString("isMain");
            usql = usql + " b.isMain='" + isMain + "' and ";
        }

        int limit = 20;
        int page = 1;
        if (data.containsKey("limit") && data.getString("limit").length() > 0) {
            limit = data.getInteger("limit");
        }
        if (data.containsKey("page") && data.getString("page").length() > 0) {
            page = data.getInteger("page");
        }
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String sql = "";
        try {
            mysql = InfoModelPool.getModel();
            /*String y = time.substring(0, 2);
            String m = time.substring(2, 4);
            String start_time = "20" + y + "-" + m + "-01 00:00:00";
            String end_time = RIUtil.getLMonthEnd(start_time);*/
            sql = "select user_id, DECODE(b.name,'1n2a3m4e') as name,DECODE(a.title,'1t2i3t4l5e') as title,d_time," + "finish_time,rela_id,brief_type,a.id from brief_static a LEFT JOIN user b on a.user_id=b.id " + "where a.create_time>='" + start_time + "' and a.create_time<='" + end_time + "' and " + usql + " 1=1  order by a.create_time limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            logger.warn(sql);
            list = mysql.query(sql);
            if (list.size() > 0) {
                back.put("data", relaInfo(list, mysql));
                sql = "select count(a.id) as count from brief_static a LEFT JOIN user b on a.user_id=b.id where a" +
                        ".create_time>='" + start_time + "' and a.create_time<='" + end_time + "' and " + usql + " 1=1";

                back.put("count", mysql.query_count(sql));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(446002);
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    private static JSONObject getRank(JSONObject data) {
        String time = new SimpleDateFormat("yyMM").format(new Date());
        String isMain = " and 1=1 ";
        String unit = "";
        if (data.containsKey("isMain") && data.getString("isMain").length() > 0) {
            String main = data.getString("isMain");
            isMain = " and b.isMain='" + main + "' ";
        }
        if (data.containsKey("time") && data.getString("time").length() > 0) {
            time = data.getString("time");
            String start_time = "";
            String end_time = "";
            if (time.contains("_"))//季度
            {
                String[] t = time.split("\\_");
                if (t[1].equals("01")) {
                    start_time = "20" + t[0] + "-01-01 00:00:00";
                    end_time = "20" + t[0] + "-03-31 23:59:59";
                }
                if (t[1].equals("02")) {
                    start_time = "20" + t[0] + "-04-01 00:00:00";
                    end_time = "20" + t[0] + "-06-30 23:59:59";
                }
                if (t[1].equals("03")) {
                    start_time = "20" + t[0] + "-07-01 00:00:00";
                    end_time = "20" + t[0] + "-09-30 23:59:59";
                }
                if (t[1].equals("04")) {
                    start_time = "20" + t[0] + "-10-01 00:00:00";
                    end_time = "20" + t[0] + "-12-31 23:59:59";
                }
            } else if (time.contains(".")) {//月
                time = time.replace(".", "");
                start_time = "20" + time.substring(0, 2) + "-" + time.substring(2, 4) + "-01 00:00:00";
                end_time = RIUtil.getLMonthEnd(start_time);
            } else {//年
                start_time = time + "-01-01 00:00:00";
                end_time = time + "-12-31 23:59:59";
                time = time + "Y";
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                JSONObject uone = RIUtil.users.get(data.getString("opt_user"));
                unit = uone.getString("unit");
            }
            initCheckStatic(time, start_time, end_time, unit);

        }
        int limit = 20;
        int page = 1;
        if (data.containsKey("limit") && data.getString("limit").length() > 0) {
            limit = data.getInteger("limit");
        }
        if (data.containsKey("page") && data.getString("page").length() > 0) {
            page = data.getInteger("page");
        }
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String sql = "";
        try {
            mysql = InfoModelPool.getModel();
            String usql = " and 1=1 ";
            if (unit.length() > 0
            ) {

                usql = " and b.unit='" + unit + "' ";
            }
            sql = "select a.user_id,b.name,a.rank,a.point " + "from " +
                    "check_static a left join user b on a.user_id=b.id where a.time='" + time + "'" + isMain + usql +
                    "order by rank limit " + limit + " offset " + limit * (page - 1);
            System.out.println(sql);
            List<JSONObject> list = new ArrayList<>();

            list = mysql.query(sql);
            if (list.size() > 0) {
                back.put("data", relaInfo(list, mysql));
                sql = "select count(a.id) as count from check_static a left join user b on a.user_id=b.id  where " +
                        "a.time='" + time + "' " + isMain + usql;
                //logger.warn(sql);
                back.put("count", mysql.query_count(sql));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(446002);
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    public static void initCheckStatic(String time, String start_time, String end_time, String unit) {
        InfoModelHelper mysql = null;
        String usql = " and 1=1 ";
        if (unit.length() > 0) {
            usql = "  and unit='" + unit + "' ";
        }

        try {
            mysql = InfoModelPool.getModel();
            String sql =
                    "delete from brief_static where create_time>='" + start_time + "' and create_time<='" + end_time + "' ";
            mysql.update(sql);

            sql = "delete from  check_static where time='" + time + "'";
            mysql.update(sql);


            //明细
            String sqls = "select id,type,accepter,create_time,decode(title,'" + RIUtil.enTitle + "') as title," +
                    "rela_id,level from brief where type in (1,2,3,4) and create_time>='" + start_time + "' and " +
                    "create_time<='" + end_time + "'" + usql + " order by create_time";
            List<JSONObject> ll = mysql.query(sqls);
            if (ll.size() > 0) {
                for (int i = 0; i < ll.size(); i++) {
                    JSONObject one = ll.get(i);
                    String id = one.getString("id");
                    String type = one.getString("type");
                    String accepter = one.getString("accepter");
                    String create_time = one.getString("create_time");
                    String title = one.getString("title");
                    String rela_id = one.getString("rela_id");
                    String level = one.getString("level");
                    sqls =
                            "select count(id) as count from brief_static where brief_id='" + id + "' and " +
                                    "brief_type='" + type + "'" + usql;
                    int count = mysql.query_count(sqls);
                    if (count == 0) {
                        HashMap<String, String> accs = RIUtil.StringToList(accepter);

                        for (Map.Entry<String, String> a : accs.entrySet()) {
                            String user_id = a.getKey();
                            String d_time = "";
                            String check_time = "";
                            if (type.equals("1")) {
                                sqls = "select notice_time from notice where id='" + rela_id + "'";

                                String notice_time = mysql.query_one(sqls, "notice_time");
                                if (notice_time.length() > 5) {
                                    d_time = RIUtil.GetNextDateTime(notice_time, 1);
                                }
                                sqls = "select time from notice_check_log where rela_id='" + rela_id + "' and " +
                                        "user_id='" + user_id + "'";
                                check_time = mysql.query_one(sqls, "time");

                            }
                            if (type.equals("2")) {
                                sqls = "select end_time from task where id='" + rela_id + "'";
                                d_time = mysql.query_one(sqls, "end_time");

                                sqls = "select time from task_log where task_id='" + rela_id + "' and user_id='" + user_id + "' and task_type=2 " + "order by time desc limit 1";
                                check_time = mysql.query_one(sqls, "time");

                            }
                            if (type.equals("3")) {
                                if (title.contains("迟到")) {
                                    String create = create_time.substring(0, 10);
                                    String yest = RIUtil.GetNextDate(create, -1);
                                    sqls = "select time from attendance where user_id='" + user_id + "' and time " +
                                            "like '" + yest + "' order by time limit 1";
                                    check_time = mysql.query_one(sqls, "time");

                                } else {
                                    type = "8";

                                }
                            }

                            if (type.equals("4")) {

                                String level_name = RIUtil.RealDictNames(RIUtil.StringToList(level));
                                if (level_name.contains("分局")) {
                                    type = "5";
                                } else if (level_name.equals("市局")) {
                                    type = "6";
                                } else if (level_name.equals("省厅")) {
                                    type = "7";
                                }

                            }

                            sqls =
                                    "insert brief_static(user_id,brief_id,brief_type,create_time," + "title,rela_id," + "d_time,finish_time,unit) " + "values('" + user_id + "','" + id + "','" + type + "','" + create_time + "'," + "encode('" + title + "','" + RIUtil.enTitle + "'),'" + rela_id + "','" + d_time + "','" + check_time + "','" + unit + "')";
                            mysql.update(sqls);
                            //logger.warn(sqls);

                        }
                    }
                }
            }


            sql = "select id from user where status=1 and isdelete=1 and id!='1'" + usql;
            // logger.warn(sql);
            List<JSONObject> list = mysql.query(sql);

            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                // logger.warn(one.toString());
                String id = one.getString("id");
                //1公告 2 事项 3 迟到 4 其他/派出所负面 扣1分
                sql = "select count(id) count from brief_static " + "where user_id='" + id + "' and create_time>='" + start_time + "' and create_time<='" + end_time + "' and (brief_type=1 or brief_type=2 or brief_type=4)";
                int count = 100 - mysql.query_count(sql);
                //5 分局负面 -2
                sql = "select count(id) count from brief_static " + "where user_id='" + id + "' and create_time>='" + start_time + "' and create_time<='" + end_time + "' and brief_type=5 ";
                count = count - mysql.query_count(sql) * 2;
                //6 市局负面和8旷工 -3
                sql = "select count(id) count from brief_static " + "where user_id='" + id + "' and create_time>='" + start_time + "' and create_time<='" + end_time + "' and (brief_type=6 ) ";
                count = count - mysql.query_count(sql) * 3;

                //7 省厅通报 -5
                sql = "select count(id) count from brief_static " + "where user_id='" + id + "' and create_time>='" + start_time + "' and create_time<='" + end_time + "' and (brief_type=7) ";
                count = count - mysql.query_count(sql) * 5;

                sql = "select count(id) count from brief_static " + "where user_id='" + id + "' and create_time>='" + start_time + "' and create_time<='" + end_time + "' and (brief_type=3) ";
                int late = mysql.query_count(sql);

                sql = "select count(id) count from brief_static " + "where user_id='" + id + "' and create_time>='" + start_time + "' and create_time<='" + end_time + "' and (brief_type=8) ";
                int not_check = mysql.query_count(sql);

                sql = "select count(id) count from brief_static " + "where user_id='" + id + "' and create_time>='" + start_time + "' and create_time<='" + end_time + "' and (brief_type>=4 and brief_type<=7) ";
                int imp = mysql.query_count(sql);
                sql = "insert check_static(user_id,point,time,late,not_check,imp) values('" + id + "','" + count +
                        "','" + time + "','" + late + "','" + not_check + "','" + imp + "')";
                // logger.warn(sql);
                mysql.update(sql);

            }

            //排名
            sql = "select id,point,user_id  from check_static where time='" + time + "'" + usql + " order by point " +
                    "desc";
            // logger.warn(sql);
            List<JSONObject> pp = mysql.query(sql);
            int lastPoint = 0;
            int lastRank = 1;
            if (pp.size() > 0) {
                for (int i = 0; i < pp.size(); i++) {
                    JSONObject one = pp.get(i);
                    String id = one.getString("id");
                    int point = one.getInteger("point");
                    String user_id = one.getString("user_id");
                    if (lastPoint == 0) {
                        lastPoint = point;
                        lastRank = 1;
                    }
                    if (lastPoint > point) {
                        lastPoint = point;
                        lastRank = lastRank + 1;
                    } else {
                        lastPoint = point;
                        lastRank = lastRank;
                    }
                    //notice
                    sql = "select count(id) as count from notice where (reading like '%" + user_id + "%' or readed " + "like '%" + user_id + "%') " + "and notice_time>='" + start_time + "' and notice_time<='" + end_time + "' and isdelete=1";
                    int notice_all = mysql.query_count(sql);
                    //logger.warn(sql);
                    sql = "select count(id) as count from brief_static " + "where user_id='" + user_id + "' and " +
                            "brief_type=1 and create_time>='" + start_time + "' " + "and create_time<='" + end_time + "'";
                    int notice_not = mysql.query_count(sql);
                    if (notice_not >= notice_all) {
                        notice_not = notice_all;
                    }
                    //task
                    sql = "select count(id) as count from task where (accepter like '%" + user_id + "%' or finished " + "like '%" + user_id + "%' or checked like '%" + user_id + "%' ) " + "and start_time>='" + start_time + "' and start_time<='" + end_time + "' and isdelete=1";
                    int task_all = mysql.query_count(sql);
                    // logger.warn(sql);
                    sql = "select count(id) as count from brief_static where user_id='" + user_id + "' and " +
                            "brief_type=2 and create_time>='" + start_time + "' and create_time<='" + end_time + "'";
                    int task_not = mysql.query_count(sql);
                    if (task_not >= task_all) {
                        task_not = task_all;
                    }

                    sql = "update check_static set rank='" + lastRank + "',notice_all='" + notice_all + "',notice_not"
                            + "='" + notice_not + "',task_all='" + task_all + "',task_not='" + task_not + "' where " + "id='" + id + "'";
                    mysql.update(sql);
                }
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }
    }


    private static Object relaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String user_id = one.getString("user_id");
            JSONObject user = RIUtil.users.get(user_id);
            one.put("user_name", user);
            back.add(one);

        }
        return back;
    }


    private static JSONObject getBriefStatistic(JSONObject data) {
        String time = new SimpleDateFormat("yyMM").format(new Date());
        String sql = "";
        String user_id = "";

        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");
        } else {
            return ErrNo.set(446001);
        }

        if (data.containsKey("time") && data.getString("time").length() > 0) {
            time = data.getString("time");
            if (time.contains(".")) {
                time = time.replace(".", "");
            } else if (time.contains("_")) {

            } else {
                time = time + "Y";
            }
        }
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            sql = "select * from check_static where user_id='" + user_id + "' and time='" + time + "'";
            List<JSONObject> list = mysql.query(sql);
            JSONObject one = new JSONObject();

            if (list.size() > 0) {
                one = list.get(0);
                back.put("data", one);
            } else {
                back.put("data", one);
            }
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(446002);
        } finally {
            InfoModelPool.putModel(mysql);
        }


    }


    private static JSONObject getAdvanceCount(JSONObject data) {
        {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
            String today1 = sd.format(new Date()) + " 00:00:00";
            String today_e = sd.format(new Date()) + " 23:59:59";
            String month1 = sdf.format(getmindate());
            String month_e = sdf.format(getmaxdate());
            String week1 = getWeekStart();
            String week_end = getWeekEnd();
            InfoModelHelper mysql = null;
            String sqls = "";
            String type = "1";//1公告2党建4帮助
            JSONObject back = ErrNo.set(0);
            try {

                String unit = "";
                String usql = "";
                if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                    unit = data.getString("unit");
                    usql = " unit='" + unit + "' and ";
                } else {
                    usql = " 1=1 and ";
                }
                mysql = InfoModelPool.getModel();

                JSONObject tp = new JSONObject();

//公告
                JSONObject d = new JSONObject();
                sqls = "select count(id)as count from notice where type='" + type + "'and  is_notice=1 and " +
                        "isdelete=1" + " and (notice_time>='" + today1 + "' and notice_time<='" + today_e + "')";
                d.put("today", mysql.query_count(sqls));
                sqls = "select count(id)as count from notice where type='" + type + "'and is_notice=1 and isdelete=1 "
                        + "and (notice_time>='" + week1 + "' and notice_time<='" + week_end + "')";
                d.put("week", mysql.query_count(sqls));
                sqls = "select count(id)as count from notice where type='" + type + "'and is_notice=1 and isdelete=1 "
                        + "and (notice_time>='" + month1 + "' and notice_time<='" + month_e + "')";
                d.put("month", mysql.query_count(sqls));
                sqls = "select count(id)as count from notice where type='" + type + "'and is_notice=1 and isdelete=1 ";
                d.put("total", mysql.query_count(sqls));
                tp.put("notice", d);

                //事项
                d = new JSONObject();
                sqls = "select count(id)as count from task where" + unit + " isPublish=1 and isdelete=1 and " +
                        "(start_time>='" + today1 + "' and start_time<='" + today_e + "')";
                d.put("today", mysql.query_count(sqls));
                sqls = "select count(id)as count from task where" + unit + " isPublish=1 and isdelete=1 and " +
                        "(start_time>='" + week1 + "' and start_time<='" + week_end + "')";
                d.put("week", mysql.query_count(sqls));
                sqls = "select count(id)as count from task where" + unit + " isPublish=1 and isdelete=1 and " +
                        "(start_time>='" + month1 + "' and start_time<='" + month_e + "')";
                d.put("month", mysql.query_count(sqls));
                sqls = "select count(id)as count from task where" + unit + " isPublish=1 and isdelete=1 ";
                d.put("total", mysql.query_count(sqls));
                tp.put("task", d);

                //门禁
                d = new JSONObject();
                sqls = "select count(id) as count from attendance where" + unit + "  (time>='" + today1 + "' and " +
                        "time<='" + today_e + "') ";
                d.put("today", mysql.query_count(sqls));
                sqls = "select count(id) as count from attendance where" + unit + "  (time>='" + week1 + "' and " +
                        "time<='" + week_end + "') ";
                d.put("week", mysql.query_count(sqls));
                sqls = "select count(id) as count from attendance where" + unit + "  (time>='" + month1 + "' and " +
                        "time<='" + month_e + "') ";
                d.put("month", mysql.query_count(sqls));
                sqls = "select count(id) as count from attendance wehre " + unit;
                d.put("total", mysql.query_count(sqls));
                tp.put("door", d);

                //道闸
                d = new JSONObject();
                sqls = "select count(id) as count from gate_event where " + unit + " (time>='" + today1 + "' and " +
                        "time<='" + today_e + "') ";
                d.put("today", mysql.query_count(sqls));
                sqls = "select count(id) as count from gate_event where " + unit + " (time>='" + week1 + "' and " +
                        "time<='" + week_end + "') ";
                d.put("week", mysql.query_count(sqls));
                sqls = "select count(id) as count from gate_event where " + unit + " (time>='" + month1 + "' and " +
                        "time<='" + month_e + "') ";
                d.put("month", mysql.query_count(sqls));
                sqls = "select count(id) as count from gate_event" + unit;
                d.put("total", mysql.query_count(sqls));
                tp.put("gate", d);
                back.put("data", tp);

                logger.warn(back.toString());
                return back;
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
                return ErrNo.set(446002);

            } finally {
                InfoModelPool.putModel(mysql);
            }

        }


    }

    private static JSONObject getAdvance(JSONObject data) {
        String type = "";

        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();

            String start_date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String end_date = RIUtil.GetNextDate(start_date, -7);

            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                if (type.equals("3")) {
                    type = type + "','4";
                }
                if (type.equals("5")) {
                    type = type + "','6";
                }
            } else {
                return ErrNo.set(446001);
            }
            if (data.containsKey("start_date") & data.getString("start_date").length() > 0 && data.containsKey(
                    "end_date") & data.getString("end_date").length() > 0) {
                start_date = data.getString("start_date");
                end_date = data.getString("end_date");
            }
            String unit = "";
            String usql = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                usql = " unit='" + unit + "' and ";
            } else {
                usql = " 1=1 and ";
            }

            String sql =
                    "select * from static_info where" + unit + " type in ('" + type + "') and date>='" + start_date + "' " + "and" + " date<='" + end_date + "' order by date";
            List<JSONObject> list = mysql.query(sql);
            JSONObject back = ErrNo.set(0);
            back.put("data", list);
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(446002);
        } finally {
            InfoModelPool.putModel(mysql);
        }


    }

    private static JSONObject getVisitCount(JSONObject data) {
        String type = "daily";//weekly,monthly
        String start_date = "";
        String end_date = "";
        try {
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");

            }
            if (data.containsKey("start_date") && data.getString("start_date").length() > 0 && data.containsKey(
                    "end_date") && data.getString("end_date").length() > 0) {
                start_date = data.getString("start_date").replace("-", "");
                end_date = data.getString("end_date").replace("-", "");
            } else {
                return ErrNo.set(460001);
            }


            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat s = new SimpleDateFormat("yyyyMMdd");

            String access[] = ConnectWeChat.GetAccessToken().split("\\|");
            String access_token = access[0];
            String url =
                    "https://api.weixin.qq.com/datacube/getweanalysisappid" + type + "visittrend?access_token=" + access_token;

            logger.warn(url);
            JSONObject d = new JSONObject();
            d.put("begin_date", start_date);
            d.put("end_date", end_date);
            logger.warn(d.toString());
            String back = HttpConnection.post(url, d);
            logger.warn(back);
            JSONObject b = ErrNo.set(0);
            b.putAll(JSONObject.parseObject(back));
            return b;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(446002);
        }

    }

    private static JSONObject getNoticeCount(JSONObject data) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        String today1 = sd.format(new Date()) + " 00:00:00";
        String today_e = sd.format(new Date()) + " 23:59:59";
        String month1 = sdf.format(getmindate());
        String month_e = sdf.format(getmaxdate());
        String week1 = getWeekStart();
        String week_end = getWeekEnd();
        InfoModelHelper mysql = null;
        String sqls = "";
        String type = "1";//1公告2党建4帮助

        JSONObject back = ErrNo.set(0);
        try {
            if (data.containsKey("type")) {
                type = data.getString("type");
            }
            String unit = "";
            String usql = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                usql = " unit='" + unit + "' and ";
            } else {
                usql = " 1=1 and ";
            }


            mysql = InfoModelPool.getModel();
            JSONObject d = new JSONObject();
            sqls = "select count(id)as count from notice where " + usql + " type='" + type + "'and  is_notice=1 and " + "isdelete=1 " + "and" + " (notice_time>='" + today1 + "' and notice_time<='" + today_e + "') and type=1";
            d.put("today", mysql.query_count(sqls));
            sqls = "select count(id)as count from notice where" + usql + " type='" + type + "'and is_notice=1 and " + "isdelete=1 and " + "(notice_time>='" + week1 + "' and notice_time<='" + week_end + "') and type=1";
            d.put("week", mysql.query_count(sqls));
            sqls = "select count(id)as count from notice where" + usql + " type='" + type + "'and is_notice=1 and " + "isdelete=1 and " + "(notice_time>='" + month1 + "' and notice_time<='" + month_e + "') and type=1";
            d.put("month", mysql.query_count(sqls));
            sqls = "select count(id)as count from notice where" + usql + " type='" + type + "'and is_notice=1 and " + "isdelete=1 and type=1";
            d.put("total", mysql.query_count(sqls));
            logger.warn(sqls);
            back.put("data", d);
            logger.warn(back.toString());
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(446002);

        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    private static JSONObject getTaskCount(JSONObject data) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        String today1 = sd.format(new Date()) + " 00:00:00";
        String today_e = sd.format(new Date()) + " 23:59:59";
        String month1 = sdf.format(getmindate());
        String month_e = sdf.format(getmaxdate());
        String week1 = getWeekStart();
        String week_end = getWeekEnd();
        InfoModelHelper mysql = null;
        String sqls = "";
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String unit = "";
            String usql = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                usql = " unit='" + unit + "' and ";
            } else {
                usql = " 1=1 and ";
            }

            JSONObject d = new JSONObject();
            sqls = "select count(id)as count from task where" + usql + " isPublish=1 and isdelete=1 and " +
                    "(start_time>='" + today1 + "' and start_time<='" + today_e + "') and type!='9999999' and " +
                    "type!='22222'";
            d.put("today", mysql.query_count(sqls));
            sqls = "select count(id)as count from task where" + usql + " isPublish=1 and isdelete=1 and " +
                    "(start_time>='" + week1 + "' and start_time<='" + week_end + "') and type!='9999999' and " +
                    "type!='22222'";
            d.put("week", mysql.query_count(sqls));
            sqls = "select count(id)as count from task where" + usql + " isPublish=1 and isdelete=1 and " +
                    "(start_time>='" + month1 + "' and start_time<='" + month_e + "') and type!='9999999' and " +
                    "type!='22222'";
            d.put("month", mysql.query_count(sqls));
            sqls = "select count(id)as count from task where" + usql + " isPublish=1 and isdelete=1 and " + "type" +
                    "!='9999999' and type!='22222'";
            d.put("total", mysql.query_count(sqls));
            logger.warn(sqls);
            back.put("data", d);
            logger.warn(back.toString());
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(446002);

        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    public static Date getmindate() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));

        return calendar.getTime();
    }

    /**
     * 获取本月最后一天
     *
     * @return
     */
    public static Date getmaxdate() {
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(new Date());
        calendar2.set(Calendar.DAY_OF_MONTH, calendar2.getActualMaximum(Calendar.DAY_OF_MONTH));
        return calendar2.getTime();
    }

    /**
     * 获取本周的第一天
     *
     * @return String
     **/
    public static String getWeekStart() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.WEEK_OF_MONTH, 0);
        cal.set(Calendar.DAY_OF_WEEK, 2);
        Date time = cal.getTime();
        return new SimpleDateFormat("yyyy-MM-dd").format(time) + " 00:00:00";
    }

    /**
     * 获取本周的最后一天
     *
     * @return String
     **/
    public static String getWeekEnd() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, cal.getActualMaximum(Calendar.DAY_OF_WEEK));
        cal.add(Calendar.DAY_OF_WEEK, 1);
        Date time = cal.getTime();
        return new SimpleDateFormat("yyyy-MM-dd").format(time) + " 23:59:59";
    }

}

