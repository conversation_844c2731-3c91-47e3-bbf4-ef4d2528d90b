package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.wechat.TestMsgLine;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class QAForumController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/qa_forum"})

    public JSONObject get_qa_forum(TNOAHttpRequest request) throws Exception {
        logger.warn("qa_forum--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_qa_forum")) {
                return getQAForum(data);
            } else if (opt.equals("create_qa_forum")) {
                String token = request.getHeader("token");
                return createQAForum(data, request.getRemoteAddr(), token);
            } else if (opt.equals("update_qa_forum")) {
                return updateQAForum(data, request.getRemoteAddr());
            } else if (opt.equals("delete_qa_forum")) {
                return deleteQAForum(data, request.getRemoteAddr());
            } else if (opt.equals("update_qa_forum_answer")) {
                return updateQAForumAnswer(data, request.getRemoteAddr());
            } else if (opt.equals("static_qa_forum")) {
                return StaticQAForum(data);
            } else if (opt.equals("static_qa_top")) {
                return StaticQATop(data);
            } else if (opt.equals("static_qa_zx")) {
                return StaticQAZX(data);
            } else if (opt.equals("static_qa_zz")) {
                return StaticQAZZ(data);
            } else if (opt.equals("static_qa_bin1")) {
                return StaticQABin1(data);
            } else if (opt.equals("can_zan")) {
                return FbackAnswer(data);
            } else if (opt.equals("static_qa_bin2")) {
                return StaticQABin2(data);
            } else if (opt.equals("process_data")) {
                return processData(data);
            } else if (opt.equals("get_qa_source_count")) {
                return getQaSourceCount(data);
            } else {
                return ErrNo.set(472009);
            }
        } else {
            return ErrNo.set(472009);
        }
    }

    private JSONObject getQaSourceCount(JSONObject data) throws Exception {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        mysql = InfoModelPool.getModel();
        List<JSONObject> res = new ArrayList<>();
        try {
            String sql = "select count(id) as count, type_father from qa_forum where isdelete=1 group by type_father";
            res = mysql.query(sql);
            back.put("data", res);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //处理数据更新create_user
    private JSONObject processData(JSONObject data) throws Exception {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        mysql = InfoModelPool.getModel();

        try {
            String sql = "select qa_forum.create_user as create_user, `user`.id as user_id , qa_forum.id " +
                    "from qa_forum " +
                    "left join user_copy on qa_forum.create_user = user_copy.id " +
                    "left join `user` on `user`.id_num = user_copy.id_num ";
            List<JSONObject> res = mysql.query(sql);
            logger.warn(sql);
            for (JSONObject one : res) {
                logger.warn(one.toString());
                if (one.containsKey("id3") && one.getString("id3").length() > 0) {
                    String qaId = one.getString("id");
                    String id3 = one.getString("id3");
                    sql = "update qa_forum set create_user = '" + qaId + "' where id = " + id3;
                    logger.warn(sql);
                    mysql.update(sql);
                }
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }
        return back;
    }

    private JSONObject StaticQABin2(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String unit = "";
        try {
            mysql = InfoModelPool.getModel();

            String sql = "select count(status) as count,status from qa_answer_fback group by status";
            List<JSONObject> list = mysql.query(sql);

            back.put("data", list);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject FbackAnswer(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String answer_id = "";
        String status = "";
        String user_id = "";
        try {
            mysql = InfoModelPool.getModel();

            if (data.containsKey("answer_id") && data.getString("answer_id").length() > 0) {
                answer_id = data.getString("answer_id");
            } else {
                return ErrNo.set(472004);
            }
            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getString("status");
            } else {
                return ErrNo.set(472004);
            }
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
            } else {
                return ErrNo.set(472004);
            }


            String sql =
                    "select status from qa_answer_fback where answer_id='" + answer_id + "' and opt_user='" + user_id + "'";

            String s = mysql.query_one(sql, "status");

            data.remove("opt");
            data.put("opt_user", user_id);
            data.remove("user_id");
            data.put("create_time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            if (s == null || s.length() == 0) {

                RIUtil.JsonInsert(data, "qa_answer_fback");


            } else {
                sql = "delete from qa_answer_fback where answer_id='" + answer_id + "' and opt_user='" + user_id + "'";
                mysql.update(sql);

                if (!s.equals(status)) {
                    RIUtil.JsonInsert(data, "qa_answer_fback");
                }
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(471005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject StaticQABin1(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String unit = "";
        try {
            mysql = InfoModelPool.getModel();
            JSONArray type36 = RIUtil.GetDictByType(23);
            JSONArray rets = new JSONArray();
            for (int i = 0; i < type36.size(); i++) {
                JSONObject one = type36.getJSONObject(i);
                String id = one.getString("id").substring(0, 6);


                String sql =
                        "select count(a.id) as count from qa_forum a right join user b on a.create_user=b.id " + "and"
                                + " b.unit like '%" + id + "%' and a.isdelete=1  ";
                int count = mysql.query_count(sql);

                one.put("count", count);

                if (count > 0) {
                    rets.add(one);
                }
            }

            back.put("data", rets);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(471005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject StaticQAZZ(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String unit = "";
        try {
            mysql = InfoModelPool.getModel();
            JSONArray type36 = RIUtil.GetDictByType(22);
            JSONArray rets = new JSONArray();
            for (int i = 0; i < type36.size(); i++) {
                JSONObject one = type36.getJSONObject(i);
                String id = one.getString("id");

                String sql = "select count(id) as count from qa_forum where procurator='" + id + "' and isdelete=1";
                int count = mysql.query_count(sql);

                one.put("count", count);

                if (count > 0) {
                    rets.add(one);
                }
            }

            back.put("data", rets);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(471005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject StaticQAZX(JSONObject data) {

        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String cMonth = new SimpleDateFormat("yyyy-MM-").format(new Date()) + "01";

        try {
            mysql = InfoModelPool.getModel();

            JSONArray rets = new JSONArray();
            for (int i = 0; i < 6; i++) {
                String month = cMonth.substring(0, cMonth.length() - 3);
                JSONObject one = new JSONObject();
                String sql =
                        "select count(id) as count from qa_forum where create_time like '%" + month + "%' and " +
                                "isdelete=1";
                System.out.println(sql);
                int count = mysql.query_count(sql);

                one.put("count", count);
                one.put("month", month);

                rets.add(one);

                cMonth = RIUtil.GetNextDate(month + "-01", -1);

            }

            back.put("data", rets);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(471005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject StaticQATop(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String typeFather = "";
        String subSql = "";
        try {
            mysql = InfoModelPool.getModel();
            if (data.containsKey("type_father") && data.getString("type_father").length() > 0) {
                typeFather = data.getString("type_father");
                subSql += " and type_father='" + typeFather + "'";
            }
            String sql = "select count(id) as count,type from qa_forum where isdelete=1 " + subSql + " group by type";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                back.put("data", list);
            } else {
                back.put("data", new JSONArray());
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(471005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject StaticQAForum(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String start_date = new SimpleDateFormat("yyyy-MM").format(new Date()) + "-01";
        String end_date = RIUtil.getLMonthEnd(start_date).substring(0, 10);
        String model = "";//casename 罪名 //suggest 意见关键词 //feedback反馈
        try {
            mysql = InfoModelPool.getModel();
            if (data.containsKey("start_date") && data.getString("start_date").length() > 0) {
                start_date = data.getString("start_date");
            }
            if (data.containsKey("end_date") && data.getString("end_date").length() > 0) {
                end_date = data.getString("end_date");
            }
            JSONObject datas = new JSONObject();
            //as 民警身份
            JSONArray unit = GetcreateUserbyunit(start_date, end_date, mysql);
            datas.put("unit", unit);

            //as 标签
            JSONArray suggest = GetStatic(start_date, end_date, mysql, "36", "type");
            datas.put("suggest", suggest);

            //as 评论次数 ？？？ all 还是每个检察官
            //as feedback

            JSONArray feedback = GetStatic(start_date, end_date, mysql, "37", "fback");
            datas.put("feedback", feedback);
            back.put("data", datas);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(471005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONArray GetcreateUserbyunit(String start_date, String end_date, InfoModelHelper mysql) throws Exception {


        HashMap<String, String> labels = new HashMap<>();
        for (Map.Entry<String, JSONObject> one : RIUtil.dicts.entrySet()) {

            JSONObject det = one.getValue();
            String type = det.getString("type");
            String dict_name = det.getString("dict_name");
            String det_id = det.getString("id");
            //logger.warn(type + "-" + dict_name);
            if (type.equals("1")) {
                labels.put(det_id, dict_name);
            }
        }

        JSONArray datas = new JSONArray();
        for (Map.Entry<String, String> one : labels.entrySet()) {
            String id = one.getKey();
            String name = one.getValue();

            String sql =
                    "select count(a.id) as count from qa_forum a left join user b on a.create_user=b.id where " + "a" + ".create_time>='" + start_date + " 00:00" + ":00' and " + "a.create_time<='" + end_date + " 23:59:59' and a.isdelete=1 and b.unit='" + id + "'";

            int count = mysql.query_count(sql);

            JSONObject det = new JSONObject();
            det.put(name, count);
            datas.add(det);

        }

        return datas;

    }

    private JSONArray GetStatic(String start_date, String end_date, InfoModelHelper mysql, String dictType,
                                String colName) throws Exception {


        HashMap<String, String> labels = new HashMap<>();
        for (Map.Entry<String, JSONObject> one : RIUtil.dicts.entrySet()) {

            JSONObject det = one.getValue();
            String type = det.getString("type");
            String dict_name = det.getString("dict_name");
            String det_id = det.getString("id");
            //logger.warn(type + "-" + dict_name);
            if (type.equals(dictType)) {
                labels.put(det_id, dict_name);
            }
        }


        JSONArray datas = new JSONArray();
        for (Map.Entry<String, String> one : labels.entrySet()) {
            String id = one.getKey();
            String name = one.getValue();

            String sql =
                    "select count(id) as count from qa_forum where create_time>='" + start_date + " 00:00:00' " +
                            "and " + "create_time<='" + end_date + " 23:59:59' and isdelete=1 and " + colName + "='" + id + "'";

            int count = mysql.query_count(sql);

            JSONObject det = new JSONObject();
            det.put(name, count);
            datas.add(det);

        }

        return datas;

    }

    private JSONObject updateQAForumAnswer(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        MysqlHelper my_sso = null;
        try {
            mysql = InfoModelPool.getModel();
            my_sso = new MysqlHelper("mysql_sso");
            String id = "";
            String opt_user = "";
            String answer = "";

            data.remove("zan");

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(472004);
            }

            String police_id = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
                police_id = RIUtil1.users1.get(opt_user).getString("police_id");
                JSONObject ouser = RIUtil1.users1.get(opt_user);
                JSONObject unit = ouser.getJSONObject("unit_name");
                try {
                    if (unit.containsKey("dets")) {
                        unit.remove("dets");
                    }
                } catch (Exception e) {

                }
                ouser.put("unit_name", unit);
                data.put("opt_user", ouser);
            } else {
                return ErrNo.set(472004);
            }
            if (data.containsKey("answer") && data.getString("answer").length() > 0) {
                answer = data.getString("answer");
            } else {
                return ErrNo.set(472004);
            }
            String fback = "";
            if (data.containsKey("fback") && data.getString("fback").length() > 0) {
                fback = data.getString("fback");
                data.put("fback", RIUtil.dicts.get(fback));
                data.put("fback_time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            }
            String answer_id = "";
            if (data.containsKey("answer_id") && data.getString("answer_id").length() > 0) {
                answer_id = data.getString("answer_id");
                logger.warn(answer_id);

            } else {
                data.put("answer_id", String.valueOf(UUID.randomUUID()));
                data.put("time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            }


            data.remove("opt");
            data.remove("id");


            //

            String sql = "select ipaddr from user_log where police_id='" + police_id + "' order by time desc limit 1";
           // logger.warn(sql);
            String ipp = my_sso.query_one(sql, "ipaddr");
            data.put("ip", ipp);


            sql = "select create_user,decode(title,'"+RIUtil.enTitle+"') as title from qa_forum where id='" + id + "'";
            String create_user = mysql.query_one(sql, "create_user");
            String title=mysql.query_one(sql,"title");
            JSONObject hdr = new JSONObject();
            String answers = RIUtil.IdToName(id, mysql, "decode(answers,'" + RIUtil.enContent + "') as answers",
                    "qa_forum");
            //logger.warn(answers);
            JSONArray ans = new JSONArray();
            String opUser="";
            if (answers.length() > 2) {
               // System.out.println(answers);
                ans = JSONArray.parseArray(answers);
               // logger.warn(ans.toString());
                if (answer_id.length() > 0) {
                    for (int a = 0; a < ans.size(); a++) {
                        try {
                            JSONObject aone = ans.getJSONObject(a);
                            JSONObject optUser=aone.getJSONObject("opt_user");

                            logger.warn(aone.getString("answer_id"));
                            if (aone.getString("answer_id").equals(answer_id)) {

                                hdr = aone.getJSONObject("opt_user");
                                // data.putAll(aone);
                                ans.remove(a);
                                break;
                            }
                        } catch (Exception ex) {
                            logger.warn(Lib.getTrace(ex));
                            break;
                        }
                    }

                }

            }
            logger.warn(ans.toString());
            for (int a = 0; a < ans.size(); a++) {
                try {
                    JSONObject aone = ans.getJSONObject(a);
                    logger.warn(aone.toString());
                    JSONObject optUser=aone.getJSONObject("opt_user");
                    String id_num=optUser.getString("id_num");
                    logger.warn(id_num);
                    if(!id_num.equals(create_user)){
                        opUser=id_num;
                    }

                } catch (Exception ex) {
                    logger.warn(Lib.getTrace(ex));
                    break;
                }
            }
            //logger.warn(data.toString());
            if (hdr.containsKey("name")) {
                data.remove("opt_user");
                data.put("opt_user", hdr);
            }
            ans.add(data);
           // logger.warn(ans.toString());
            String reading = RIUtil.IdToName(id, mysql, "reading", "qa_forum");
            String readed = RIUtil.IdToName(id, mysql, "readed", "qa_forum");
            HashMap ing = RIUtil.StringToList(reading);
            HashMap ed = RIUtil.StringToList(readed);
            if (ing.containsKey(opt_user)) {
                ing.remove(opt_user);
                ed.put(opt_user, "");
            }

            sql = "update qa_forum set answers=encode('" + ans + "','" + RIUtil.enContent + "'),reading='" + RIUtil.HashToList(ing) + "',readed='" + RIUtil.HashToList(ed) + "' " + "where id='" + id + "'";
            mysql.update(sql);

            // wechatMsgTemp.sendNoticeZX("问答论坛", "您的问题有回复啦，请查收！", create_user, 8, id);
List<String>userList=new ArrayList<>();
userList.add(create_user);
            if(opt_user.equals(create_user)){
                if(opUser.length()>0){
                userList.add(opUser);
                TestMsgLine send = new TestMsgLine();
                send.sendMSG("您的互动答疑已回复，请查收-->"+title, userList);}
            }else{
                userList.add(create_user);
                TestMsgLine send = new TestMsgLine();
                send.sendMSG("您的互动答疑已回复，请查收-->"+title, userList);
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(472001);
        } finally {
            InfoModelPool.putModel(mysql);
            my_sso.close();
        }
        return back;
    }

    //******CREATE*******
    private JSONObject createQAForum(JSONObject data, String remoteAddr, String token) {
        InfoModelHelper mysql = null;
        MysqlHelper my_sso = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            my_sso = new MysqlHelper("mysql_sso");
            String question = "";
            String type = "";
            String procurator = "";

            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "1";
            String case_type = "0";
            String case_id = "";
            int isOpen = 0;
            String title = "";

            String ip = "";
            String img = "";
            String video = "";
            String accessory = "";
            String type_son = "";
            String unit_name = "";
            String type_father = "3";
            if (data.containsKey("question") && data.getString("question").length() > 0) {
                question = data.getString("question");
            } else {
                return ErrNo.set(472002);
            }
            if (data.containsKey("title") && data.getString("title").length() > 0) {
                title = data.getString("title");
            } else {
                return ErrNo.set(472002);
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
            } else {
                return ErrNo.set(472002);
            }
            if (data.containsKey("type_son") && data.getString("type_son").length() > 0) {
                type_son = data.getString("type_son");
            }
            if (data.containsKey("procurator") && data.getString("procurator").length() > 0) {
                procurator = data.getString("procurator");
            }

            if (data.containsKey("procurator") && data.getString("procurator").length() > 0) {
                procurator = data.getString("procurator");
            }
            if (data.containsKey("procurator") && data.getString("procurator").length() > 0) {
                procurator = data.getString("procurator");
            }
            if (data.containsKey("accessory") && data.getString("accessory").length() > 0) {
                accessory = data.getString("accessory");
            }

            if (data.containsKey("case_type") && data.getString("case_type").length() > 0) {
                case_type = data.getString("case_type");
            }

            if (data.containsKey("img") && data.getString("img").length() > 0) {
                img = data.getString("img");
            }
            if (data.containsKey("video") && data.getString("video").length() > 0) {
                video = data.getString("video");
            }
            if (data.containsKey("isOpen") && data.getString("isOpen").length() > 0) {
                isOpen = data.getInteger("isOpen");
            }
            if (data.containsKey("type_father") && data.getString("type_father").length() > 0) {
                type_father = data.getString("type_father");
            }
            String police_id = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
                police_id = RIUtil1.users1.get(create_user).getString("police_id");

            } else {
                return ErrNo.set(472002);
            }
            if (data.containsKey("unit_name") && data.getString("unit_name").length() > 0) {
                unit_name = data.getString("unit_name");
            } else {
                try {
                    JSONObject uone = RIUtil1.users1.get(create_user);
                    System.out.println(uone);
                    String unit = uone.getString("unit").split(",")[0];
                    System.out.println(unit);
                    if (!unit.endsWith("0000")) {
                        String u1 = unit.substring(0, 8) + "0000";
                        unit_name = unit_name + RIUtil.dicts.get(u1).getString("dict_name");
                        unit_name = unit_name + RIUtil.dicts.get(unit).getString("dict_name");
                    } else {
                        unit_name = unit_name + RIUtil.dicts.get(unit).getString("dict_name");
                    }

                    System.out.println(unit_name);
                } catch (Exception ex) {
                    logger.error(Lib.getTrace(ex));
                }
            }

            List<String> accepters = new ArrayList<>();
            if (procurator.length() > 0) {
                String sqls = "select id from user where  isdelete=1 and status=1 and unit='" + procurator + "'";
                List<JSONObject> list = mysql.query(sqls);
                if (list.size() > 0) {
                    for (int i = 0; i < list.size(); i++) {
                        accepters.add(list.get(i).getString("id"));
                    }
                }
            }

            String sql = "select ipaddr from user_log where police_id='" + police_id + "' order by time desc limit 1";
            logger.warn(sql);
            ip = my_sso.query_one(sql, "ipaddr");


            String sqls =
                    "insert qa_forum (question,type,procurator,create_user,create_time,isdelete,reading,title," +
                            "case_type,case_id,isOpen,ip,img,video,accessory,type_son,unit_name,type_father)" +
                            "values" +
                            "(encode('" + question + "','" + RIUtil.enTitle + "'),'" + type + "','" + procurator +
                            "'," + "'" + create_user + "','" + create_time + "','" + isdelete + "','" + accepters +
                            "',encode('" + title + "','" + RIUtil.enTitle + "'),'" + case_type + "','" + case_id +
                            "','" + isOpen + "','" + ip + "','" + img + "','" + video + "','" + accessory + "','" + type_son + "','" + unit_name
                            + "','" + type_father + "')";
            mysql.update(sqls);
            sql = "select id from qa_forum where type='" + type + "' and create_user='" + create_user + "' " + "and" + " " + "create_time='" + create_time + "'";
            String id = mysql.query_one(sql, "id");
            if (isOpen == 0) {
                accepters.add(create_user);
                sqls = "update qa_forum set accepter='" + accepters + "' where id='" + id + "'";
                mysql.update(sqls);
            }

            List<String> userList = new ArrayList<>();
            userList.add("320404198701012513");
            userList.add("32040219900112281X");
            userList.add("411081199304051277");
            userList.add("320282198608165011");
            userList.add("320804198901112916");


            TestMsgLine send = new TestMsgLine();
            send.sendMSG("您有一条互动答疑，请查收", userList);


            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建问答论坛", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(472001);
        } finally {
            InfoModelPool.putModel(mysql);
            my_sso.close();
        }
        return back;
    }

    //******GET*******
    private JSONObject getQAForum(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String question = "";
            String type = "";
            String procurator = "";
            String case_type = "";
            String fback = "";
            String create_user = "";
            String create_time_start = "";
            String create_time_end = "";
            String isOpen = "";
            String type_father = "";
            int mark = 0;
            String opt_user = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
                mark++;


            }
            if (data.containsKey("question") && data.getString("question").length() > 0) {
                question = data.getString("question");
                sql = sql + " (decode(question,'" + RIUtil.enTitle + "') like '%" + question + "%' or decode(title," + "'" + RIUtil.enTitle + "') like '%" + question + "%'  or decode(answers,'" + RIUtil.enContent + "') like '%" + question + "%') and ";
                mark++;
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type='" + type + "' and ";
                mark++;
            }
            String accepter = "";
            if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
                accepter = data.getString("accepter");
                mark++;
            }
            if (data.containsKey("isOpen") && data.getString("isOpen").length() > 0) {
                isOpen = data.getString("isOpen");

            }
            if (data.containsKey("case_type") && data.getString("case_type").length() > 0) {
                case_type = data.getString("case_type");
                sql = sql + " case_type in (" + case_type + ") and ";
            }

            if (data.containsKey("procurator") && data.getString("procurator").length() > 0) {
                procurator = data.getString("procurator");
                sql = sql + " procurator like '%" + procurator + "%' and ";
                mark++;

            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' and ";
                mark++;

            }


            if (data.containsKey("fback") && data.getString("fback").length() > 0) {
                fback = data.getString("fback");
                sql = sql + " fback='" + fback + "' and ";
                mark++;
            }

            if (data.containsKey("type_father") && data.getString("type_father").length() > 0) {
                type_father = data.getString("type_father");
                sql += " type_father = '" + type_father + "' and ";
            }


            if (isOpen.equals("") && mark == 0) {
                sql = sql + " isOpen='1' or (isOpen=0 and accepter like '%" + accepter + "%' ) and ";
            } else if (isOpen.equals("0")) {
                sql = sql + "isOpen=0 and accepter like '%" + accepter + "%' and ";
            } else if (isOpen.equals("1")) {
                sql = sql + " isOpen='1' and ";
            }

            //  logger.warn(sql);

            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
                create_time_start = data.getString("create_time_start");
                sql = sql + "create_time>='" + create_time_start + "' and ";
                mark++;
            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end");
                sql = sql + "create_time<='" + create_time_end + "' and ";
                mark++;
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }


            String sqls =
                    "select id,decode(question,'" + RIUtil.enTitle + "') as question,type,procurator ,decode" +
                            "(answers,'" + RIUtil.enContent + "') as answers ,fback,create_user,create_time," +
                            "readed,reading,decode(title,'" + RIUtil.enTitle + "') as title,case_type,case_id,isOpen," +
                            "accepter,ip,img,video,accessory,type_son,unit_name,type_father" +
                            " from qa_forum where " +
                            "1=1 and " + sql + " isdelete=1 order by create_time desc limit " + limit + " " + "offset" +
                            " " + limit * (page - 1);
            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);

            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select count(id) as count from qa_forum where 1=1 and " + sql + " isdelete=1; ";
                back.put("count", mysql.query_count(sqls));

            } else {
                back.put("data", list);
                back.put("count", 0);
            }
            if (id.length() > 0) {
                String reading = RIUtil.IdToName(id, mysql, "reading", "qa_forum");
                String readed = RIUtil.IdToName(id, mysql, "readed", "qa_forum");
                HashMap ing = RIUtil.StringToList(reading);
                HashMap ed = RIUtil.StringToList(readed);
                if (ing.containsKey(opt_user)) {
                    ing.remove(opt_user);

                }
                ed.put(opt_user, "");

                sql = "update qa_forum set reading='" + RIUtil.HashToList(ing) + "',readed='" + RIUtil.HashToList(ed) + "' " + "where id='" + id + "'";
                logger.warn(sql);
                mysql.update(sql);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(472005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            one.put("create_user", RIUtil1.users1.get(one.getString("create_user")));
            one.put("procurator", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("procurator"))));
            JSONObject det = new JSONObject();
            det.put("id", one.getString("case_id"));


            List<JSONObject> answers = new ArrayList<>();
            if (one.containsKey("answers") && one.getString("answers").length() > 2) {
                String aaa = one.getString("answers");

                try {
                    JSONArray ans = one.getJSONArray("answers");
//                    ans = ans.replac
                    //  logger.warn(ans.toString());
                    for (int a = 0; a < ans.size(); a++) {
                        JSONObject aone = ans.getJSONObject(a);
                        String ans_id = aone.getString("answer_id");

                        String sql = "select * from qa_answer_fback where answer_id='" + ans_id + "' order by " +
                                "create_time" + " desc";
                        List<JSONObject> afList = mysql.query(sql);
                        if (afList.size() > 0) {
                            aone.put("zan", afList);
                        } else {
                            aone.put("zan", new JSONArray());
                        }
                        answers.add(aone);

                    }
                } catch (Exception ex) {
                    logger.warn(aaa);
                    logger.error(Lib.getTrace(ex));
                }
            }
            Collections.sort(answers, (JSONObject o1, JSONObject o2) -> {

                String a = o1.getString("time");
                String b = o2.getString("time");
                long ta = 0;
                long tb = 0;
                try {
                    ta = RIUtil.dateToStamp(a);
                    tb = RIUtil.dateToStamp(b);
                } catch (Exception ex) {

                }
                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (ta < tb) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });


            one.put("answers", answers);

            if (one.containsKey("readed") && one.getString("readed").length() > 0) {
                try {
                    one.put("readed", JSONArray.parseArray(one.getString("readed")));
                } catch (Exception ex) {
                    one.put("readed", new JSONArray());
                }
            } else {
                one.put("readed", new JSONArray());
            }


            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private JSONObject updateQAForum(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String question = "";
            String type = "";
            String procurator = "";
            String answers = "";
            String fback = "";
            String opt_user = "";
            String id = "";
            String case_type = "0";
            String case_id = "";
            int isOpen = 0;
            String title = "";
            String img = "";
            String video = "";
            String accessory = "";
            String type_son = "";
            String unit_name = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(472004);
            }
            if (data.containsKey("unit_name") && data.getString("unit_name").length() > 0) {
                unit_name = data.getString("unit_name");
                sql = sql + " unit_name='" + unit_name + "' , ";
            }
            if (data.containsKey("question") && data.getString("question").length() > 0) {
                question = data.getString("question");
                sql = sql + " question=encode('" + question + "','" + RIUtil.enTitle + "') , ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type='" + type + "' , ";
            }
            if (data.containsKey("case_type") && data.getString("case_type").length() > 0) {
                case_type = data.getString("case_type");
                sql = sql + " case_type='" + case_type + "' , ";
            }
            if (data.containsKey("type_son") && data.getString("type_son").length() > 0) {
                type_son = data.getString("type_son");
                sql = sql + " type_son='" + type_son + "' , ";
            }
            if (data.containsKey("procurator") && data.getString("procurator").length() > 0) {
                procurator = data.getString("procurator");
                sql = sql + " procurator='" + procurator + "' , ";
            }
            if (data.containsKey("title") && data.getString("title").length() > 0) {
                title = data.getString("title");
                sql = sql + " title=encode('" + title + "','" + RIUtil.enTitle + "') , ";
            }

            if (data.containsKey("fback") && data.getString("fback").length() > 0) {
                fback = data.getString("fback");
                sql = sql + " fback='" + fback + "' , ";
            }

            if (data.containsKey("case_type") && data.getString("case_type").length() > 0) {
                case_type = data.getString("case_type");
                sql = sql + " case_type='" + case_type + "' , ";
            }
            if (data.containsKey("case_id") && data.getString("case_id").length() > 0) {
                case_id = data.getString("case_id");
                sql = sql + " case_id='" + case_id + "' , ";
            }
            if (data.containsKey("isOpen") && data.getString("isOpen").length() > 0) {
                isOpen = data.getInteger("isOpen");
                sql = sql + " isOpen='" + isOpen + "' , ";
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(472004);
            }

            if (data.containsKey("accessory") && data.getString("accessory").length() > 0) {
                accessory = data.getString("accessory");

                sql = sql + " accessory='" + accessory + "' , ";
            }


            if (data.containsKey("img") && data.getString("img").length() > 0) {
                img = data.getString("img");
                sql = sql + " img='" + img + "' , ";
            }
            if (data.containsKey("video") && data.getString("video").length() > 0) {
                video = data.getString("video");
                sql = sql + " video='" + video + "' , ";
            }
            String sqls = "update qa_forum set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);

            String[] accepters = null;
            if (procurator.length() == 0) {
                sqls = "select id from user where org=1 and isdelete=1 and status=1";
                List<JSONObject> list = mysql.query(sqls);
                if (list.size() > 0) {
                    for (int i = 0; i < list.size(); i++) {
                        accepters[i] = list.get(i).getString("id");
                    }
                }

            } else {
                accepters = procurator.split(",");
            }


            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新问答论坛", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(472003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private JSONObject deleteQAForum(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(472008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(472008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update qa_forum set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除问答论坛", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(472007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }


}
