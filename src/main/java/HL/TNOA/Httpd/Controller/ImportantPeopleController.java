package HL.TNOA.Httpd.Controller;


import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.GaussHelper;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/important-people")
public class ImportantPeopleController {

    private static Logger log = LoggerFactory.getLogger(ImportantPeopleController.class);


    @PostMapping("/query")
    public JSONObject query(@RequestBody JSONObject data) throws Exception {

        JSONObject back = ErrNo.set(0);

        GaussHelper gaussHelper = null;
        try {
            gaussHelper = new GaussHelper("gauss_hl");

            String baseSql = "select jlbh,rybh,gmsfhm,xm,xlglb,dzzbx as \"X\" ,dzzby as \"Y\" from qjjc.zdry_location where 1=1 ";

            if (StrUtil.isNotBlank(data.getString("unit_type")) && StrUtil.isNotBlank(data.getString("unit"))) {
                String unitType = data.getString("unit_type");
                if ("23".equals(unitType) || "24".equals(unitType) || "28".equals(unitType)) {
                    baseSql += " and bclgdw like '" + data.getString("unit").substring(0, 6) + "%'";
                } else if ("21".equals(unitType) || "22".equals(unitType) || "27".equals(unitType)) {
//                    baseSql += " and bclgdw like '" + data.getString("unit").substring(0, 4) + "%'";
                } else if ("25".equals(unitType)) {
                    baseSql += " and bclgdw like '" + data.getString("unit").substring(0, 8) + "%'";
                } else if ("26".equals(unitType)) {
                    baseSql += " and bclgdw = '" + data.getString("unit") + "'";
                }

            }

            if (StrUtil.isNotBlank(data.getString("type"))) {
                baseSql += " and xlglb like '%" + data.getString("type") + "%'";
            }

            log.warn("重点人员地址--->\n" + baseSql);
            List<JSONObject> query = gaussHelper.query(baseSql);
            back.put("data", query);
        } finally {
            if (gaussHelper != null) {
                gaussHelper.close();
            }
        }
        return back;
    }

    @PostMapping("/count")
    public JSONObject count(@RequestBody JSONObject data) throws Exception {
        JSONObject back = ErrNo.set(0);

        GaussHelper gaussHelper = null;
        try {
            gaussHelper = new GaussHelper("gauss_hl");

            Map<String, Integer> map = new HashMap<>();

            String baseSql = "select xlglb from qjjc.zdry_location where 1=1 ";

            if (StrUtil.isNotBlank(data.getString("unit_type")) && StrUtil.isNotBlank(data.getString("unit"))) {
                String unitType = data.getString("unit_type");
                if ("23".equals(unitType) || "24".equals(unitType) || "28".equals(unitType)) {
                    baseSql += " and bclgdw like '" + data.getString("unit").substring(0, 6) + "%'";
                } else if ("21".equals(unitType) || "22".equals(unitType) || "27".equals(unitType)) {
//                    baseSql += " and bclgdw like '" + data.getString("unit").substring(0, 4) + "%'";
                } else if ("25".equals(unitType)) {
                    baseSql += " and bclgdw like '" + data.getString("unit").substring(0, 8) + "%'";
                } else if ("26".equals(unitType)) {
                    baseSql += " and bclgdw = '" + data.getString("unit") + "'";
                }

            }

            log.warn("重点人员countsql ------>"+baseSql);
            List<JSONObject> query = gaussHelper.query(baseSql);
            map.put("total", query.size());

            for (JSONObject jsonObject : query) {
                String[] split = jsonObject.getString("xlglb").split(",");
                for (String s : split) {
                    if(StrUtil.isNotBlank(s.trim())){
                        map.put(s.trim(), map.getOrDefault(s.trim(), 0) + 1);
                    }
                }
            }
            //优化代码为stream流
//            query.stream()
//                    .map(x -> x.getString("xlglb").split(","))
//                    .flatMap(Arrays::stream)
//                    .forEach(x -> map.put(x.trim(), map.getOrDefault(x.trim(), 0) + 1));

            back.put("data", map);
        } finally {
            if (gaussHelper != null) {
                gaussHelper.close();
            }
        }

        return back;

    }



}
