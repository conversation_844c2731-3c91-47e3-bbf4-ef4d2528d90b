package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.wechat.HttpConnection;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

@RestController
public class CameraController {
    private static Logger logger = LoggerFactory.getLogger(CameraController.class);
    // private SimpleDateFormat sdf =;

    @RequestMapping(method = {RequestMethod.POST}, path = {"/camera"})
    @PassToken
    public JSONObject get_camera(TNOAHttpRequest request) throws Exception {
        logger.warn("camera_info--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_camera_info")) {
                return getCameraInfo(data);
            } else if (opt.equals("update_camera_info")) {
                return updateCameraInfo(data, request.getRemoteAddr());
            } else if (opt.equals("get_camera_url")) {
                return getCamerUrl(data);
            } else if (opt.equals("get_camera_list")) {
                return getCameraList(data);
            } else if (opt.equals("get_camera_history_url")) {
                return getCameraHistoryUrl(data);
            } else if (opt.equals("get_div_url")) {
                return getDivUrl(data);
            } else if (opt.equals("get_div_loc")) {
                return getDivLoc(data);
            } else {
                return ErrNo.set(462009);
            }
        } else {
            return ErrNo.set(462009);
        }
    }

    private JSONObject getDivLoc(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String usql = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            String type = RIUtil.dicts.get(unit).getString("type");
            if (type.equals("21") || type.equals("22") || type.equals("27")) {

            } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                usql = " and code like '" + unit.substring(0, 6) + "%' ";
            } else {
                usql = " and code='" + unit.substring(0, 8) + "0000' ";
            }
        }

        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            String sql =
                    "select NAME,CHANID,GPSX,GPSY from CAMERA_DIV where STATUS=0 and LX='chan' and GPSX is not null " + usql;
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            if (list.size() > 0) {
                back.put("data", list);
            } else {
                back.put("data", new ArrayList<>());
            }
            return back;

        } catch (Exception ex) {

            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }

    }

    private JSONObject getDivUrl(JSONObject data) {
        String token = "";
        String chanId = "";
        MysqlHelper mysql = null;
        if (data.containsKey("chanId") && data.getString("chanId").length() > 0) {
            chanId = data.getString("chanId");


        }


        try {
            mysql = new MysqlHelper("mysql");
            String sql = "select content from temp where id=2";
            token = mysql.query_one(sql, "content");
            logger.warn(token);

            OkHttpClient client = new OkHttpClient().newBuilder().build();
            MediaType mediaType = MediaType.parse("application/json");

            JSONObject det = new JSONObject();
            det.put("chanId", chanId);
            det.put("streamAgentType", 5);
            JSONObject encode = new JSONObject();
            encode.put("standardStream", 5);
            det.put("encodeInfo", encode);

            RequestBody body = RequestBody.create(mediaType, det.toString());
            Request request = new Request.Builder().url("http://50.56.93.125:9998/vid-video-deal-service/app" +
                    "/getRealTimeStreamForApp").method("POST", body).addHeader("Authorization", "bearer " + token).addHeader("Content-Type", "application/json").addHeader("Cookie", "JSESSIONID=C569FE5C9555C48FDBD843222F756998").build();
            Response response = client.newCall(request).execute();
            String bk = response.body().string();

            System.out.println(bk);
            JSONObject bkk = JSONObject.parseObject(bk);
            JSONObject back = ErrNo.set(0);
            if (bkk.containsKey("returnCode") && bkk.getInteger("returnCode") == 0) {
                bk = bkk.getString("data");
                back.put("data", bk);
            } else {
                bk = bkk.getString("returnMsg");
                return ErrNo.set(null, 2, bk);
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            mysql.close();
        }


    }

//    public static void main(String[] args) {
//        CameraController cm = new CameraController();
//        JSONObject a = new JSONObject();
//        a.put("did", "3YSB04533401RJ6");
//        a.put("type", "2");
//        a.put("start_time", "202204181000");
//        a.put("end_time", "202204181200");
//        JSONObject camerUrl = cm.getCamerUrl(a);
//        System.out.println(camerUrl);
//        JSONObject b = new JSONObject();
//        b.put("did","3YSB04533401RJ6");
//        b.put("id", "690221_13961418808_3YSB04533401RJ6-1649994672000");
//        JSONObject cameraHistoryUrl = cm.getCameraHistoryUrl(b);
//        System.out.println(cameraHistoryUrl);
//        JSONObject c = new JSONObject();
//        c.put("name", "水清木华商铺26号小螃蟹");
//        JSONObject cameraList = cm.getCameraList(c);
//        System.out.println(cameraList);
//    }


    private JSONObject getCameraList(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String qname = "";
        String s = " and 1=1 ";

        if (data.containsKey("name") && data.getString("name").length() > 0) {
            qname = data.getString("name");

            s = " and deviceName like '%" + qname + "%'";
        }
        try {
            mysql = InfoModelPool.getModel();
            JSONObject datas = new JSONObject();

            JSONArray types = new JSONArray();
            for (int t = 1; t < 4; t++) {
                JSONObject tt = new JSONObject();
                if (t == 1) {
                    tt.put("type_name", "棋牌室");
                    tt.put("type", "1");
                } else if (t == 2) {
                    tt.put("type_name", "烧烤店");
                    tt.put("type", "2");
                } else {
                    tt.put("type_name", "无人机");
                    tt.put("type", "3");
                    List<JSONObject> comm2 = new ArrayList<>();
                    List<JSONObject> list = new ArrayList<>();
                    JSONObject one2 = new JSONObject();
                    one2.put("cameras", list);
                    one2.put("camera_count", "1");
                    one2.put("task_type_name", "翠竹无人机1号");
                    comm2.add(one2);
                    tt.put("comms", comm2);
                    types.add(tt);
                    datas.put("type", types);
                    back.put("data", datas);
                    return back;
                }
                String sql =
                        "select id,decode(dict_name,'" + RIUtil.enName + "') as dict_name " + "from " + "dict " +
                                "where type=3 and isdelete=1";
                List<JSONObject> list = mysql.query(sql);
                List<JSONObject> comms = new ArrayList<>();
                if (list.size() > 0) {

                    for (int i = 0; i < list.size(); i++) {
                        JSONObject one = list.get(i);
                        String id = one.getString("id");

                        sql = "select * from camera_info where community='" + id + "' and type='" + t + "' and " +
                                "isdelete=1" + s;
                        List<JSONObject> clist = mysql.query(sql);

                        if (clist.size() > 0) {

                            one.put("cameras", clist);
                            one.put("camera_count", clist.size());

                        } else {
                            one.put("cameras", new ArrayList<>());
                            one.put("camera_count", 0);
                        }
                        comms.add(one);

                    }
                }
                sql = "select * from camera_info where (length(community)=0 or community is null) and type='" + t +
                        "' and isdelete=1" + s;
                List<JSONObject> clist = mysql.query(sql);
                JSONObject one = new JSONObject();

                if (clist.size() > 0) {
                    one.put("cameras", clist);
                    one.put("camera_count", clist.size());
                    one.put("id", "");
                    one.put("dict_name", "其他");
                    comms.add(one);

                }
                tt.put("comms", comms);

                types.add(tt);
            }

            datas.put("type", types);
            back.put("data", datas);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(462005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    private JSONObject getCamerUrl(JSONObject data) {

        String did = "";
        String type = "1";
        if (data.containsKey("did") && data.getString("did").length() > 0) {
            did = data.getString("did");
        } else {
            return ErrNo.set(462004);
        }
        if (data.containsKey("type") && data.getString("type").length() > 0) {
            type = data.getString("type");
        }
        JSONObject back = ErrNo.set(0);

        try {
            if (type.equals("1")) {//实时
                String url = "http://218.90.228.169:9000/udp/smartHome/device/getMediaServer";
                HashMap<String, String> pars = new HashMap<>();
                String pstr = "";
                JSONObject map = params_url(did, "0");
                for (Map.Entry<String, Object> one : map.entrySet()) {
                    String key = one.getKey();
                    String value = one.getValue().toString();

                    pars.put(key, value);
                    pstr = pstr + key + "=" + value + "&";
                }
                //logger.warn(pstr);
                System.out.println(pstr);
                String bkStr = HttpConnection.post_urlencoded(url, pstr);
                //logger.warn(bkStr);
                try {
                    JSONObject bk = JSONObject.parseObject(bkStr);
                    JSONObject body = bk.getJSONObject("body");
                    String str = String.valueOf(body).replace(TNOAConf.get("HttpServ", "old_url"), TNOAConf.get(
                            "HttpServ", "proxy_url"));
                    back.put("data", str);
                    logger.warn(back.toString());
                    return back;
                } catch (Exception ex) {
                    return ErrNo.set(462005);
                }
            } else if (type.equals("2")) {//回放列表
                String start_time = "";
                String end_time = "";
                if (data.containsKey("start_time") && data.getString("start_time").length() > 0 && data.containsKey(
                        "end_time") && data.getString("end_time").length() > 0 && data.getString("start_time").substring(0, 7).equals(data.getString("end_time").substring(0, 7))) {
                    start_time = data.getString("start_time");
                    end_time = data.getString("end_time");
                    String url = "http://218.90.228.169:9000/udp/smartHome/device/getCloudFileList";

                    HashMap<String, String> pars = new HashMap<>();
                    String pstr = "";
                    JSONObject map = params_his_list(did, start_time, end_time, 1);
                    for (Map.Entry<String, Object> one : map.entrySet()) {
                        String key = one.getKey();
                        String value = one.getValue().toString();

                        pars.put(key, value);
                        pstr = pstr + key + "=" + value + "&";
                    }
                    //pstr = pstr.substring(0, pstr.length() - 1);
                    String bkStr = HttpConnection.post_urlencoded(url, pstr);
                    try {

                        JSONObject bk = JSONObject.parseObject(bkStr);
                        String msg = bk.getString("msg");
                        String count = bk.getString("count");
                        JSONArray data2 = bk.getJSONArray("data");
                        int temp = 0;
                        JSONArray data3 = new JSONArray();
                        for (int i = 0; i < data2.size(); i++) {
                            JSONObject a = (JSONObject) data2.get(i);
                            String createDate = a.getString("createDate");
                            String t = createDate.substring(11, 13);
                            if (Integer.valueOf(t) > temp) {
                                temp = Integer.valueOf(t);
                            }
                        }
                        String substring = start_time.substring(8, 10);
                        Integer substring2 = Integer.valueOf(substring);
                        for (int i = substring2; i <= temp; i++) {
                            List<JSONObject> temp2 = new ArrayList<>();
                            for (int j = 0; j < data2.size(); j++) {
                                List<JSONObject> list = new ArrayList<>();
                                JSONObject a = (JSONObject) data2.get(j);
                                String createDate = (String) a.get("createDate");
                                if (Integer.valueOf(createDate.substring(11, 13)) == i) {
                                    temp2.add(a);
                                }
                            }
                            JSONObject c = new JSONObject();
                            String ch = String.valueOf(i) + ":00-" + String.valueOf(i + 1) + ":00";
                            c.put("time", ch);
                            c.put("file", temp2);
                            data3.add(c);
                        }

                        back.put("msg", msg);
                        back.put("count", count);
                        back.put("data", data3);

                    } catch (Exception ex) {
                        return ErrNo.set(462005);
                    }
                } else {
                    return ErrNo.set(462004);
                }


            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(462005);
        }
        return back;
    }

    private JSONObject getCameraHistoryUrl(JSONObject data) {
        String did = "";
        String id = "";
        if (data.containsKey("did") && data.getString("did").length() > 0) {
            did = data.getString("did");
        } else {
            return ErrNo.set(462004);
        }
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        }
        JSONObject back = ErrNo.set(0);
        try {
            String url = "http://218.90.228.169:9000/udp/smartHome/device/getCloudFileUrl";
            JSONObject map = params_history_url1(did, id);
            String pstr = "";
            HashMap<String, String> pars = new HashMap<>();
            for (Map.Entry<String, Object> one : map.entrySet()) {
                String key = one.getKey();
                String value = one.getValue().toString();
                pars.put(key, value);
                pstr = pstr + key + "=" + value + "&";
            }
            String bkStr = HttpConnection.post_urlencoded(url, pstr);
            try {
                JSONObject bk = JSONObject.parseObject(bkStr);
                String code = bk.getString("code");
                String msg = bk.getString("msg");
                String url2 = bk.getString("url");
                back.put("code", code);
                back.put("msg", msg);
                back.put("data", url2);

            } catch (Exception ex) {
                return ErrNo.set(462005);
            }

        } catch (Exception e) {
            return ErrNo.set(462005);
        }
        logger.warn(back.toJSONString());
        return back;
    }

    public static JSONObject params_his_list(String did, String startDate, String endDate, int pageNo) throws Exception {
        String appKey = "867990543";
        String appSecret = "t5jhew64h68o9v3452bmv0444y25lwp9nim5zw88";
        String nonce = UUID.randomUUID().toString().replaceAll("-", "");
        long curTime = new Date().getTime();
        // System.out.println("nonce -> " + nonce);
        //System.out.println("curTime -> " + curTime);
        //System.out.println(getCheckSum(nonce, appSecret, curTime + ""));

        JSONObject params = new JSONObject();
        params.put("appKey", appKey);
        params.put("nonce", nonce);
        params.put("curTime", curTime);
        params.put("checkNum", getCheckSum(nonce, appSecret, curTime + ""));
        System.out.println(params);
        //        getMediaServer
        params.put("DID", did);
        params.put("startDate", startDate);

        params.put("endDate", endDate);
        params.put("pageNo", pageNo);
        params.put("pageSize", 50);


        System.out.println(params);
        return params;
    }

    public static JSONObject params_history_url1(String did, String id) throws Exception {
        String appKey = "867990543";
        String appSecret = "t5jhew64h68o9v3452bmv0444y25lwp9nim5zw88";
        String nonce = UUID.randomUUID().toString().replaceAll("-", "");
        long curTime = new Date().getTime();
        //System.out.println("nonce -> " + nonce);
        // System.out.println("curTime -> " + curTime);
        //System.out.println(getCheckSum(nonce, appSecret, curTime + ""));

        JSONObject params = new JSONObject();
        params.put("appKey", appKey);
        params.put("nonce", nonce);
        params.put("curTime", curTime);
        params.put("checkNum", getCheckSum(nonce, appSecret, curTime + ""));

        //        getMediaServer
        params.put("DID", did);
        params.put("id", id);
        return params;
    }


    private static JSONObject getCameraInfo(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String dID = "";
            String deviceName = "";
            String community = "";
            String type = "";
            String urlTime = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("dID") && data.getString("dID").length() > 0) {
                dID = data.getString("dID");
                sql = sql + " dID='" + dID + "' and ";
            }
            if (data.containsKey("deviceName") && data.getString("deviceName").length() > 0) {
                deviceName = data.getString("deviceName");
                sql = sql + " deviceName like '%" + deviceName + "%' and ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type='" + type + "' and ";
            }
            if (data.containsKey("community") && data.getString("community").length() > 0) {
                community = data.getString("community");

                if (community.equals("other")) {
                    sql = sql + " (length(community)=0 or community is null) and ";
                } else {
                    sql = sql + " community='" + community + "' and ";
                }
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls = "select * from camera_info where 1=1 and " + sql + " isdelete=1  limit " + limit + " offset"
                    + " " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select id from camera_info where 1=1 and " + sql + " isdelete=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(462005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private static List<JSONObject> RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String community = one.getString("community");
            one.put("community", RIUtil.RealDictNames(RIUtil.StringToList(community)));
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private JSONObject updateCameraInfo(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";

            String community = "";

            String opt_user = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            }

            if (data.containsKey("community")) {
                community = data.getString("community");
                sql = sql + " community='" + community + "' , ";
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            String sqls = "update camera_info set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新摄像头信息", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(462003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    public static JSONObject params_url(String did, String StreamID) throws Exception {
        String appKey = "867990543";
        String appSecret = "t5jhew64h68o9v3452bmv0444y25lwp9nim5zw88";
        String nonce = UUID.randomUUID().toString().replaceAll("-", "");
        long curTime = new Date().getTime();
        //System.out.println("nonce -> " + nonce);
        // System.out.println("curTime -> " + curTime);
        //System.out.println(getCheckSum(nonce, appSecret, curTime + ""));

        JSONObject params = new JSONObject();
        params.put("appKey", appKey);
        params.put("nonce", nonce);
        params.put("curTime", curTime);
        params.put("checkNum", getCheckSum(nonce, appSecret, curTime + ""));

        //getMediaServer
        params.put("DID", did);
        params.put("Proto", "5");
        params.put("StreamID", StreamID);


        return params;
    }


    private static String getCheckSum(String nonce, String appSecret, String curTime) throws Exception {
        return SHA1(nonce + appSecret + curTime);
    }

    private static String SHA1(String decript) throws Exception {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            digest.update(decript.getBytes());
            byte messageDigest[] = digest.digest();
            // Create Hex String
            StringBuffer hexString = new StringBuffer();
            // 字节数组转换为 十六进制 数
            for (int i = 0; i < messageDigest.length; i++) {
                String shaHex = Integer.toHexString(messageDigest[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexString.append(0);
                }
                hexString.append(shaHex);
            }
            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static JSONObject params_history_url(String did, String id) throws Exception {
        String appKey = "867990543";
        String appSecret = "t5jhew64h68o9v3452bmv0444y25lwp9nim5zw88";
        String nonce = UUID.randomUUID().toString().replaceAll("-", "");
        long curTime = new Date().getTime();
        //System.out.println("nonce -> " + nonce);
        // System.out.println("curTime -> " + curTime);
        //System.out.println(getCheckSum(nonce, appSecret, curTime + ""));

        JSONObject params = new JSONObject();
        params.put("appKey", appKey);
        params.put("nonce", nonce);
        params.put("curTime", curTime);
        params.put("checkNum", getCheckSum(nonce, appSecret, curTime + ""));

        //        getMediaServer
        params.put("DID", did);
        params.put("id", id);
        return params;
    }


}
