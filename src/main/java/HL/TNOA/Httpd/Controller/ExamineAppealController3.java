package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.User;
import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.Lib.TNOAConf;
import HL.TNOA.wechat.HttpConnection;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import static HL.TNOA.Httpd.Controller.StaticRhZfController.init;

@RestController
public class ExamineAppealController3 {
    private Logger logger = LoggerFactory.getLogger(ExamineAppealController3.class);
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/examine_appeal03"})

    public JSONObject examine_appeal(TNOAHttpRequest request) throws Exception {
        logger.warn("examine_appeal--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject data = request.getRequestParams();
//        JSONObject userInfo = getUserInfo(request);
//        logger.warn("examine_appeal ----> userInfo--->" + userInfo);
        User user = request.getUser();
        logger.warn("examine_appeal ----> User--->" + user);
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("create_appeal_data")) {
                return createAppealData(data, request);
            } else if (opt.equals("get_appeal_data")) {
                return getAppealData(data, request);
            } else if (opt.equals("update_appeal_data")) {
                return updateAppealData(data, request.getRemoteAddr());
            } else if (opt.equals("delete_appeal_data")) {
                return deleteAppealData(data, request.getRemoteAddr());
            } else if (opt.equals("submit_appeal_data")) {
                return submitAppealData(data, request.getRemoteAddr());
            } else if (opt.equals("export_appeal_event")) {
                return exportAppealEvent(data, request);
            } else if (opt.equals("get_appeal_event")) {
                return getAppealEvent(data, request);
            } else {
                return ErrNo.set(490001);
            }
        } else {
            return ErrNo.set(490001);
        }
    }


    private JSONObject deleteAppealData(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            //组织机构代码
            String id = "";
            String opt_user = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(490021);
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(490021);
            }
            String sqls =
                    "update kh_appeal set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);

            // 添加日志

            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-deleteAppealData", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject getUserInfo(TNOAHttpRequest request) {
        InfoModelHelper mysql = null;
        JSONObject uu = null;
        try {
            mysql = InfoModelPool.getModel();
            String token = request.getHeader("token");
            String unUrl = TNOAConf.get("HttpServ", "uni_url") + "/token";

            System.out.println(request.getRequestParams().getString("X-Real-IP"));
            String back = HttpConnection.post_token(unUrl, token, new JSONObject(),
                    request.getRequestParams().getString("X-Real-IP"));
            JSONObject ret = JSONObject.parseObject(back);
            if (ret.containsKey("errno") && ret.getInteger("errno") == 0) {
                JSONObject dd = ret.getJSONObject("data");
                String police_id = dd.getString("police_id");

                JSONArray roleid = dd.getJSONArray("role_id");
                JSONObject org = dd.getJSONArray("organization").getJSONObject(0);

                String sql = "select * from user where police_id='" + police_id + "'";
                List<JSONObject> list = mysql.query(sql);
                if (list.size() != 1) {
                    logger.warn("200014");
                    return ErrNo.set(200014);
                } else {
                    int status = list.get(0).getInteger("status");
                    if (status == 3) {
                        logger.warn("200012");
                        return ErrNo.set(200012);
                    }
                }

                uu = list.get(0);

                uu.put("role_id", roleid);
                uu.put("organization", org);

                return uu;
            } else {
                logger.error(String.valueOf(ret));
                return ret;
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }


    private JSONObject getAppealData(JSONObject data, TNOAHttpRequest request) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();


            JSONObject userInfo = getUserInfo(request);
            boolean flag = false;
            boolean flag2 = false;
            boolean flag3 = false;
            boolean flag4 = false;
            boolean complete = false;
            JSONArray roleId = userInfo.getJSONArray("role_id");
            String policeId = userInfo.getString("id_num");
            String orgId = userInfo.getJSONObject("organization").getString("organization_id");


            for (Object o : roleId) {
                // logger.warn("role_id---->" + o.toString());
                //管理员
                if ("hlcheckmanager".equals(o.toString()) || "ztchecksmanager".equals(o.toString())) {
                    flag = true;
                    complete = true;
                }
                //分管民警
                if ("hlcheckpolice".equals(o.toString())) {
                    flag2 = true;
                    complete = true;
                }
                //社区副所长
                if ("sqfsz".equals(o.toString()) || "ZrqFgZrr".equals(o.toString())) {
                    flag3 = true;
                }
                if ("SqPolice".equals(o.toString())) {
                    flag4 = true;
                }
            }

            int limit = 20;
            int page = 1;
            String sql = "";
            String label = "";
            String id = "";
            String resp_police = "";
            String org_id = "";
            String config_id = "";
            String time = "";
            String status = "";

            if (data.containsKey("label") && data.getString("label").length() > 0) {
                label = data.getString("label");
            }

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id ='" + id + "' and ";
            }

            if (data.containsKey("config_id") && data.getString("config_id").length() > 0) {
                config_id = data.getString("config_id");
                sql = sql + " config_id = '" + config_id + "' and ";
            }

            if (data.containsKey("org_id") && data.getString("org_id").length() > 0) {
                org_id = data.getString("org_id");
                String type = RIUtil.dicts.get(org_id).getString("type");

                if ("21".equals(type) || "22".equals(type) || "27".equals(type)) {
                    sql = sql + " 1=1 and ";
                } else if (type.equals("23") || type.equals("24") || "28".equals(type)) {
                    org_id = org_id.substring(0, 6);
                    sql = sql + " org_id like '%" + org_id + "%' and ";
                } else if (type.equals("25")) {
                    org_id = org_id.substring(0, 8);
                    sql = sql + " org_id like '%" + org_id + "%' and ";
                } else if (type.equals("26")) {
                    sql = sql + " org_id ='" + org_id + "' and ";
                } else {
                    sql = sql + " org_id ='" + org_id + "' and ";
                }
            }

            if (data.containsKey("time") && data.getString("time").length() > 0) {
                time = data.getString("time");
                sql = sql + " time ='" + time + "' and ";
            }

            if (data.containsKey("isCheck") && data.getBoolean("isCheck")) {
                sql = sql + " (instructions is not null and instructions != '' and score_check != '' and score_check " +
                        "is not null) and ";
            }

            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getString("status");

                if ("123".equals(status)) {
                    sql = sql + " (status ='1' or status ='2' or status ='3')and ";
                } else {
                    sql = sql + " status ='" + status + "' and ";
                }

            }

            if (data.containsKey("resp_police") && data.getString("resp_police").length() > 0) {
                resp_police = data.getString("resp_police");
                sql = sql + " resp_police = '" + resp_police + "' and ";
            }

            String isExp = "";
            if (data.containsKey("isExp") && data.getString("isExp").length() > 0) {
                isExp = data.getString("isExp");
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            //isdelete
//            sql = sql + " isdelete ='1' and ";

            back.put("data", new ArrayList<>());
            back.put("count", 0);
            back.put("complete", complete);

            if ("jfss".equals(label)) {

                if (flag2) {

//                    String sqls = "select a.*,b.kh_name,b.resp_dept,b.resp_police,b.full_mark,b.static_type,b
//                    .pg_rules,b.remark from kh_appeal a " +
//                            "left join kh_config b on a.config_id = b.id left join user c on b.resp_police = c
//                            .id_num where b.resp_police  = '"+policeId+"' and " + sql +
//                            " 1=1  limit " + limit + " offset " + limit * (page - 1);

                    String sqls = "select * from v_appeal_info where resp_police  = '" + policeId + "' and " + sql +
                            " isdelete ='1'  limit " + limit + " offset " + limit * (page - 1);
                    logger.warn(sqls);
                    List<JSONObject> list = mysql.query(sqls);
                    back.put("data", RelaInfo(list, mysql));

                    sqls = "select * from v_appeal_info where resp_police  = '" + policeId + "' and " + sql + " " +
                            "isdelete ='1' ";
                    list = mysql.query(sqls);
                    back.put("count", list.size());
                    if ("1".equals(isExp)) {
                        int file_id = exportTemplate3(list);
                        back.put("file_id", file_id);
                    }

                } else if (flag3) {

                    if (org_id.isEmpty()) {
                        org_id = orgId;

                        String type = RIUtil.dicts.get(org_id).getString("type");
                        if ("21".equals(type) || "22".equals(type) || "27".equals(type)) {
                            sql = sql + " 1=1 and ";
                        } else if (type.equals("23") || type.equals("24") || "28".equals(type)) {
                            org_id = org_id.substring(0, 6);
                            sql = sql + " org_id like '%" + org_id + "%' and ";
                        } else if (type.equals("25")) {
                            org_id = org_id.substring(0, 8);
                            sql = sql + " org_id like '%" + org_id + "%' and ";
                        } else if (type.equals("26")) {
                            sql = sql + " org_id ='" + org_id + "' and ";
                        } else {
                            sql = sql + " org_id ='" + org_id + "' and ";
                        }
                    }

                    String sqls = "select * from v_appeal_info where " + sql +
                            " isdelete=1  limit " + limit + " offset " + limit * (page - 1);
                    logger.warn(sqls);
                    List<JSONObject> list = mysql.query(sqls);

                    back.put("data", RelaInfo(list, mysql));
                    sqls = "select * from v_appeal_info where " + sql + " isdelete=1 ";
                    list = mysql.query(sqls);
                    back.put("count", list.size());
                    if ("1".equals(isExp)) {
                        int file_id = exportTemplate3(list);
                        back.put("file_id", file_id);
                    }
                } else if (flag4) {

                    String sqls = "select * from v_appeal_info where org_id = '" + orgId + "' and " + sql +
                            " isdelete=1  limit " + limit + " offset " + limit * (page - 1);
                    logger.warn(sqls);
                    List<JSONObject> list = mysql.query(sqls);

                    back.put("data", RelaInfo(list, mysql));
                    sqls = "select * from v_appeal_info where org_id = '" + orgId + "' and " + sql + " isdelete=1 ";
                    list = mysql.query(sqls);
                    back.put("count", list.size());
                    if ("1".equals(isExp)) {
                        int file_id = exportTemplate3(list);
                        back.put("file_id", file_id);
                    }
                } else {
                    back.put("data", new ArrayList<>());
                    back.put("count", 0);
                    back.put("file_id", -1);
                }

            } else if ("sssp".equals(label)) {

                if (flag) {
                    String sqls =
                            "select * from v_appeal_info where " + sql + " isdelete=1  limit " + limit + " offset " + limit * (page - 1);
                    logger.warn(sqls);
                    List<JSONObject> list = mysql.query(sqls);

                    if (list.size() > 0) {

                        back.put("data", RelaInfo(list, mysql));
                        sqls = "select * from v_appeal_info where " + sql + " isdelete=1 ";
                        list = mysql.query(sqls);
                        back.put("count", list.size());

                        if ("1".equals(isExp)) {
                            int file_id = exportTemplate3(list);
                            back.put("file_id", file_id);
                        }
                    }
                } else {
                    back.put("data", new ArrayList<>());
                    back.put("count", 0);
                    back.put("file_id", -1);
                }

            }

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-getAppealData", userlog.TYPE_OPERATE, request.getRemoteAddr());

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    private JSONObject createAppealData(JSONObject data, TNOAHttpRequest request) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String config_id = "";
            String org_id = "";
            String score = "";
            String reason = "";
            String file_id = "";
            String score_new = "";
            String time = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

            //根据token获取当前用户身份证
//            JSONObject userInfo = getUserInfo(request);
//            create_user = userInfo.getString("id_num");

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                return ErrNo.set(490014);
            }
            if (data.containsKey("config_id") && data.getString("config_id").length() > 0) {
                config_id = data.getString("config_id");
            } else {
                return ErrNo.set(490014);
            }
            if (data.containsKey("org_id") && data.getString("org_id").length() > 0) {
                org_id = data.getString("org_id");
            } else {
                return ErrNo.set(490014);
            }
            if (data.containsKey("time") && data.getString("time").length() > 0) {
                time = data.getString("time");
            } else {
                return ErrNo.set(490014);
            }
            if (data.containsKey("score") && data.getString("score").length() > 0) {
                score = data.getString("score");
            } else {
                return ErrNo.set(490014);
            }
            if (data.containsKey("reason") && data.getString("reason").length() > 0) {
                reason = data.getString("reason");
            } else {
                return ErrNo.set(490014);
            }
            if (data.containsKey("file_id") && data.getString("file_id").length() > 0) {
                file_id = data.getString("file_id");
            }
            if (data.containsKey("score_new") && data.getString("score_new").length() > 0) {
                score_new = data.getString("score_new");
            } else {
                return ErrNo.set(490014);
            }

            String sql = "";
            sql = "select id from kh_appeal where config_id = '" + config_id + "' and org_id = '" + org_id + "' and " +
                    "time = '" + time + "' and isdelete = '1' and status != '4'";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                return ErrNo.set(490016);
            }

            sql = "insert kh_appeal(config_id,org_id,time,score,score_new,reason,file_id,status,create_user," +
                    "create_time,isdelete) " +
                    "values('" + config_id + "','" + org_id + "'," + time + ",'" + score + "','" + score_new + "','" + reason +
                    "','" + file_id + "','1','" + create_user + "','" + create_time + "','1') ";
            logger.warn(sql);
            mysql.update(sql);


            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-createAppealData", userlog.TYPE_OPERATE, request.getRemoteAddr());

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(490015);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject exportAppealEvent(JSONObject data, TNOAHttpRequest request) {

        String sql = "";
        FileOutputStream fos = null;
        SXSSFWorkbook sxssfWorkbook = null;
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            sxssfWorkbook = new SXSSFWorkbook();

            boolean flag = false;
            boolean flag2 = false;
            boolean flag3 = false;
            JSONObject userInfo = getUserInfo(request);
            JSONArray roleId = userInfo.getJSONArray("role_id");
            for (Object o : roleId) {
                logger.warn("role_id---->" + o.toString());
                if ("hlcheckmanager".equals(o.toString()) || "hlcheckpolice".equals(o.toString()) || "ztchecksmanager"
                        .equals(o.toString())) {
                    flag = true;
                } else if ("sqfsz".equals(o.toString()) || "ZrqFgZrr".equals(o.toString())) {
                    flag2 = true;
                } else if ("SqPoliceZz".equals(o.toString())) {
                    flag3 = true;
                }
            }

            String orgId = userInfo.getJSONObject("organization").getString("organization_id");
            logger.warn("orgId---->" + orgId);

            //组织机构代码

            String sqls = "";
            ArrayList<JSONObject> fj = new ArrayList<>();
            ArrayList<JSONObject> pcs = new ArrayList<>();
            ArrayList<JSONObject> zrq = new ArrayList<>();

            if (data.containsKey("filter") && data.getBoolean("filter")) {
                sql = sql + " IF(b.point = '2',a.score < b.full_mark, a.score > 0) and";
            }

            String s = "select type,name,value from kh_control where type = 'kh_total'";
            List<JSONObject> control = mysql.query(s);

            String pid = "";
            String time = "";

            for (int i = 0; i < control.size(); i++) {
                JSONObject c = control.get(i);
                String name = c.getString("name");
                String value = c.getString("value");
                if ("total_plan".equals(name)) {
                    pid = value;
                }
                if ("total_month".equals(name)) {
                    time = value;
                }
            }

            s = "select * from kh_plan where id = '" + pid + "'";
            List<JSONObject> list = mysql.query(s);

            JSONObject plan = list.get(0);
            String fjConfig = plan.getString("fj_config");
            String pcsConfig = plan.getString("pcs_config");
            String zrqConfig = plan.getString("zrq_config");
            fj = getConfigDetById2(fjConfig.split(","));
            pcs = getConfigDetById2(pcsConfig.split(","));
            zrq = getConfigDetById2(zrqConfig.split(","));

            sqls = "select * from dict where type = '23' and isdelete = '1' and is_kh = '1'";
            List<JSONObject> orgs_fj = mysql.query(sqls);
            sqls = "select * from dict where type = '25' and isdelete = '1' and is_kh = '1'";
            List<JSONObject> orgs_pcs = mysql.query(sqls);
            sqls = "select * from dict where type = '26' and isdelete = '1' and is_kh = '1'";
            List<JSONObject> orgs_zrq = mysql.query(sqls);

            if (flag) {

                String con = " ( " + divideData(fjConfig) + divideData(pcsConfig) + divideData(zrqConfig);
                con = con.substring(0, con.length() - 1) + ") ";
                sqls = "select a.*,b.kh_name,b.pg_rules,b.resp_dept,b.resp_police,b.full_mark,b.static_type from " +
                        "kh_det a join kh_config b on a.config_id = b.id " +
                        "where a.time = '" + time + "' and a.config_id in " + con + " and  " + sql + " a.isdelete=1 ";
                logger.warn(sqls);
                List<JSONObject> result = mysql.query(sqls);

                exportScore(fj, result, orgs_fj, sxssfWorkbook, "分局", time);
                exportScore(pcs, result, orgs_pcs, sxssfWorkbook, "派出所", time);
                exportScore(zrq, result, orgs_zrq, sxssfWorkbook, "责任区", time);

            } else if (flag2) {

                JSONObject org = RIUtil.dicts.get(orgId);
                String type = org.getString("type");
                String fatherId = org.getString("father_id");

                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    String con = " ( " + divideData(fjConfig) + divideData(pcsConfig) + divideData(zrqConfig);
                    con = con.substring(0, con.length() - 1) + ") ";
                    sqls = "select a.*,b.kh_name,b.pg_rules,b.resp_dept,b.resp_police,b.full_mark,b.static_type from " +
                            "kh_det a join kh_config b on a.config_id = b.id " +
                            "where a.time = '" + time + "' and a.config_id in " + con + " and  " + sql + " a.isdelete" +
                            "=1 ";
                    logger.warn(sqls);
                    List<JSONObject> result = mysql.query(sqls);

                    exportScore(fj, result, orgs_fj, sxssfWorkbook, "分局", time);
                    exportScore(pcs, result, orgs_pcs, sxssfWorkbook, "派出所", time);
                    exportScore(zrq, result, orgs_zrq, sxssfWorkbook, "责任区", time);

                } else if (type.equals("23")) {

                    String con = " ( " + divideData(fjConfig) + divideData(pcsConfig) + divideData(zrqConfig);
                    con = con.substring(0, con.length() - 1) + ") ";
                    sqls = "select a.*,b.kh_name,b.pg_rules,b.resp_dept,b.resp_police,b.full_mark,b.static_type from " +
                            "kh_det a join kh_config b on a.config_id = b.id " +
                            "where a.time = '" + time + "' and a.config_id in " + con + " and  " + sql + " a.isdelete" +
                            "=1 ";
                    logger.warn(sqls);
                    List<JSONObject> result = mysql.query(sqls);

                    exportScore(fj, result, new ArrayList<JSONObject>() {{
                        add(org);
                    }}, sxssfWorkbook, "分局", time);
                    exportScore(pcs, result, orgs_pcs, sxssfWorkbook, "派出所", time);
                    exportScore(zrq, result, orgs_zrq, sxssfWorkbook, "责任区", time);

                } else if (type.equals("24") || type.equals("28")) {

                    String con = " ( " + divideData(fjConfig) + divideData(pcsConfig) + divideData(zrqConfig);
                    con = con.substring(0, con.length() - 1) + ") ";
                    sqls = "select a.*,b.kh_name,b.pg_rules,b.resp_dept,b.resp_police,b.full_mark,b.static_type from " +
                            "kh_det a join kh_config b on a.config_id = b.id " +
                            "where a.time = '" + time + "' and a.config_id in " + con + " and  " + sql + " a.isdelete" +
                            "=1 ";
                    logger.warn(sqls);
                    List<JSONObject> result = mysql.query(sqls);
                    fatherId = fatherId.substring(0, 6) + "000000";
                    JSONObject father_org = RIUtil.dicts.get(fatherId);
                    exportScore(fj, result, new ArrayList<JSONObject>() {{
                        add(father_org);
                    }}, sxssfWorkbook, "分局", time);
                    exportScore(pcs, result, orgs_pcs, sxssfWorkbook, "派出所", time);
                    exportScore(zrq, result, orgs_zrq, sxssfWorkbook, "责任区", time);

                } else if (type.equals("25")) {

                    String con = " ( " + divideData(pcsConfig) + divideData(zrqConfig);
                    con = con.substring(0, con.length() - 1) + ") ";
                    sqls = "select a.*,b.kh_name,b.pg_rules,b.resp_dept,b.resp_police,b.full_mark,b.static_type from " +
                            "kh_det a join kh_config b on a.config_id = b.id " +
                            "where a.time = '" + time + "' and a.config_id in " + con + " and  " + sql + " a.isdelete" +
                            "=1 ";
                    logger.warn(sqls);
                    List<JSONObject> result = mysql.query(sqls);


                    exportScore(pcs, result, new ArrayList<JSONObject>() {{
                        add(org);
                    }}, sxssfWorkbook, "派出所", time);
                    exportScore(zrq, result, orgs_zrq, sxssfWorkbook, "责任区", time);

                } else if (type.equals("26")) {

                    String con = " ( " + divideData(zrqConfig);
                    con = con.substring(0, con.length() - 1) + ") ";
                    sqls = "select a.*,b.kh_name,b.pg_rules,b.resp_dept,b.resp_police,b.full_mark,b.static_type from " +
                            "kh_det a join kh_config b on a.config_id = b.id " +
                            "where a.time = '" + time + "' and a.config_id in " + con + " and  " + sql + " a.isdelete" +
                            "=1 ";
                    logger.warn(sqls);
                    List<JSONObject> result = mysql.query(sqls);

                    exportScore(zrq, result, new ArrayList<JSONObject>() {{
                        add(org);
                    }}, sxssfWorkbook, "责任区", time);

                }
//
            }
            String FileName = "积分申诉_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

            String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;
            init(endPath);
            // 在后面设置sheet
            fos = new FileOutputStream(endPath);
            sxssfWorkbook.write(fos);

            sqls =
                    "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "','" + FileName + "',999)";
            logger.warn(sqls);
            mysql.update(sqls);
            sqls = "select * from upload where file_name='" + FileName + "'";
            List<JSONObject> result = mysql.query(sqls);
            JSONObject xJsonObject = result.get(0);
            int id = xJsonObject.getIntValue("id");

            logger.warn("id->" + id);
            JSONObject back = new JSONObject();
            back.put("id", id);
            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-exportAppealEvent", userlog.TYPE_OPERATE, request.getRemoteAddr());
            /*BashExecutor bash = new BashExecutor();
            String cmd = "cp -r " + endPath + " " + TNOAConf.get("file", "bk_path");
            logger.warn(cmd);
            bash.exec(cmd, -1, true);*/
            String endPoint = "http://10.34.251.34:50101";
            String ak = "Q7D3OUVIRBEZEB4RWVSJ";
            String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
            String bucketName = "obs-qjjc-tyyh";
            ObsServer obsServ = new ObsServer();
            String obsFileName = "hl/" + filePath + FileName;
            System.out.println(obsFileName);
            boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
            logger.warn(obsFileName + "-->" + ret);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(490050);
        } finally {
            InfoModelPool.putModel(mysql);
            try {
                assert fos != null;
                fos.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                sxssfWorkbook.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private JSONObject exportAppealEvent2(JSONObject data, TNOAHttpRequest request) {

        String sql = "";
        FileOutputStream fos = null;
        SXSSFWorkbook sxssfWorkbook = null;
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            sxssfWorkbook = new SXSSFWorkbook();

            boolean flag = false;
            boolean flag2 = false;
            boolean flag3 = false;
            JSONObject userInfo = getUserInfo(request);
            JSONArray roleId = userInfo.getJSONArray("role_id");
            for (Object o : roleId) {
                logger.warn("role_id---->" + o.toString());
                if ("hlcheckmanager".equals(o.toString()) || "hlcheckpolice".equals(o.toString()) || "ztchecksmanager"
                        .equals(o.toString())) {
                    flag = true;
                } else if ("sqfsz".equals(o.toString()) || "ZrqFgZrr".equals(o.toString())) {
                    flag2 = true;
                } else if ("SqPolice".equals(o.toString())) {
                    flag3 = true;
                }
            }

            String orgId = userInfo.getJSONObject("organization").getString("organization_id");
            logger.warn("orgId---->" + orgId);

            //组织机构代码

            String sqls = "";
            ArrayList<JSONObject> fj = new ArrayList<>();
            ArrayList<JSONObject> pcs = new ArrayList<>();
            ArrayList<JSONObject> zrq = new ArrayList<>();

            sql = sql + " IF(b.point = '2',a.score < b.full_mark, a.score > 0) and";
//            sql = sql + " IF(b.point = '2',a.score < b.full_mark, a.score > 0) and";

            String s = "select type,name,value from kh_control where type = 'kh_total'";
            List<JSONObject> control = mysql.query(s);

            String pid = "";
            String time = "";

            for (int i = 0; i < control.size(); i++) {
                JSONObject c = control.get(i);
                String name = c.getString("name");
                String value = c.getString("value");
                if ("total_plan".equals(name)) {
                    pid = value;
                }
                if ("total_month".equals(name)) {
                    time = value;
                }
            }

            s = "select * from kh_plan where id = '" + pid + "'";
            List<JSONObject> list = mysql.query(s);

            JSONObject plan = list.get(0);
            String fjConfig = plan.getString("fj_config");
            String pcsConfig = plan.getString("pcs_config");
            String zrqConfig = plan.getString("zrq_config");
            fj = getConfigDetById2(fjConfig.split(","));
            pcs = getConfigDetById2(pcsConfig.split(","));
            zrq = getConfigDetById2(zrqConfig.split(","));

            sqls = "select * from dict where type = '23' and isdelete = '1' and is_kh = '1'";
            List<JSONObject> orgs_fj = mysql.query(sqls);
            sqls = "select * from dict where type = '25' and isdelete = '1' and is_kh = '1'";
            List<JSONObject> orgs_pcs = mysql.query(sqls);
            sqls = "select * from dict where type = '26' and isdelete = '1' and is_kh = '1'";
            List<JSONObject> orgs_zrq = mysql.query(sqls);

            if (flag) {

                String con = " ( " + divideData(fjConfig) + divideData(pcsConfig) + divideData(zrqConfig);
                con = con.substring(0, con.length() - 1) + ") ";
                sqls = "select a.*,b.kh_name,b.pg_rules,b.resp_dept,b.resp_police,b.full_mark,b.static_type from " +
                        "kh_det a join kh_config b on a.config_id = b.id " +
                        "where a.time = '" + time + "' and a.config_id in " + con + " and  " + sql + " 1=1 ";
                logger.warn(sqls);
                List<JSONObject> result = mysql.query(sqls);

                exportScore(fj, result, orgs_fj, sxssfWorkbook, "分局", time);
                exportScore(pcs, result, orgs_pcs, sxssfWorkbook, "派出所", time);
                exportScore(zrq, result, orgs_zrq, sxssfWorkbook, "责任区", time);

            } else if (flag2) {

                JSONObject org = RIUtil.dicts.get(orgId);
                String type = org.getString("type");
                String fatherId = org.getString("father_id");

                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    String con = " ( " + divideData(fjConfig) + divideData(pcsConfig) + divideData(zrqConfig);
                    con = con.substring(0, con.length() - 1) + ") ";
                    sqls = "select a.*,b.kh_name,b.pg_rules,b.resp_dept,b.resp_police,b.full_mark,b.static_type from " +
                            "kh_det a join kh_config b on a.config_id = b.id " +
                            "where a.time = '" + time + "' and a.config_id in " + con + " and  " + sql + " 1=1 ";
                    logger.warn(sqls);
                    List<JSONObject> result = mysql.query(sqls);

                    exportScore(fj, result, orgs_fj, sxssfWorkbook, "分局", time);
                    exportScore(pcs, result, orgs_pcs, sxssfWorkbook, "派出所", time);
                    exportScore(zrq, result, orgs_zrq, sxssfWorkbook, "责任区", time);

                } else if (type.equals("23")) {

                    String con = " ( " + divideData(fjConfig) + divideData(pcsConfig) + divideData(zrqConfig);
                    con = con.substring(0, con.length() - 1) + ") ";
                    sqls = "select a.*,b.kh_name,b.pg_rules,b.resp_dept,b.resp_police,b.full_mark,b.static_type from " +
                            "kh_det a join kh_config b on a.config_id = b.id " +
                            "where a.time = '" + time + "' and a.config_id in " + con + " and  " + sql + " 1=1 ";
                    logger.warn(sqls);
                    List<JSONObject> result = mysql.query(sqls);

                    exportScore(fj, result, new ArrayList<JSONObject>() {{
                        add(org);
                    }}, sxssfWorkbook, "分局", time);
                    exportScore(pcs, result, orgs_pcs, sxssfWorkbook, "派出所", time);
                    exportScore(zrq, result, orgs_zrq, sxssfWorkbook, "责任区", time);

                } else if (type.equals("24") || type.equals("28")) {

                    String con = " ( " + divideData(fjConfig) + divideData(pcsConfig) + divideData(zrqConfig);
                    con = con.substring(0, con.length() - 1) + ") ";
                    sqls = "select a.*,b.kh_name,b.pg_rules,b.resp_dept,b.resp_police,b.full_mark,b.static_type from " +
                            "kh_det a join kh_config b on a.config_id = b.id " +
                            "where a.time = '" + time + "' and a.config_id in " + con + " and  " + sql + " 1=1 ";
                    logger.warn(sqls);
                    List<JSONObject> result = mysql.query(sqls);
                    fatherId = fatherId.substring(0, 6) + "000000";
                    JSONObject father_org = RIUtil.dicts.get(fatherId);
                    exportScore(fj, result, new ArrayList<JSONObject>() {{
                        add(father_org);
                    }}, sxssfWorkbook, "分局", time);
                    exportScore(pcs, result, orgs_pcs, sxssfWorkbook, "派出所", time);
                    exportScore(zrq, result, orgs_zrq, sxssfWorkbook, "责任区", time);

                } else if (type.equals("25")) {

                    String con = " ( " + divideData(pcsConfig) + divideData(zrqConfig);
                    con = con.substring(0, con.length() - 1) + ") ";
                    sqls = "select a.*,b.kh_name,b.pg_rules,b.resp_dept,b.resp_police,b.full_mark,b.static_type from " +
                            "kh_det a join kh_config b on a.config_id = b.id " +
                            "where a.time = '" + time + "' and a.config_id in " + con + " and  " + sql + " 1=1 ";
                    logger.warn(sqls);
                    List<JSONObject> result = mysql.query(sqls);


                    exportScore(pcs, result, new ArrayList<JSONObject>() {{
                        add(org);
                    }}, sxssfWorkbook, "派出所", time);
                    exportScore(zrq, result, orgs_zrq, sxssfWorkbook, "责任区", time);

                } else if (type.equals("26")) {

                    String con = " ( " + divideData(zrqConfig);
                    con = con.substring(0, con.length() - 1) + ") ";
                    sqls = "select a.*,b.kh_name,b.pg_rules,b.resp_dept,b.resp_police,b.full_mark,b.static_type from " +
                            "kh_det a join kh_config b on a.config_id = b.id " +
                            "where a.time = '" + time + "' and a.config_id in " + con + " and  " + sql + " 1=1 ";
                    logger.warn(sqls);
                    List<JSONObject> result = mysql.query(sqls);

                    exportScore(zrq, result, new ArrayList<JSONObject>() {{
                        add(org);
                    }}, sxssfWorkbook, "责任区", time);

                }
//
            }
            String FileName = "积分申诉_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

            String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;
            init(endPath);
            // 在后面设置sheet
            fos = new FileOutputStream(endPath);
            sxssfWorkbook.write(fos);

            sqls =
                    "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "','" + FileName + "',999)";
            logger.warn(sqls);
            mysql.update(sqls);
            sqls = "select * from upload where file_name='" + FileName + "'";
            List<JSONObject> result = mysql.query(sqls);
            JSONObject xJsonObject = result.get(0);
            int id = xJsonObject.getIntValue("id");

            logger.warn("id->" + id);
            JSONObject back = new JSONObject();
            back.put("id", id);

            String endPoint = "http://10.34.251.34:50101";
            String ak = "Q7D3OUVIRBEZEB4RWVSJ";
            String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
            String bucketName = "obs-qjjc-tyyh";
            ObsServer obsServ = new ObsServer();
            String obsFileName = "hl/" + filePath + FileName;
            System.out.println(obsFileName);
            boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
            logger.warn(obsFileName + "-->" + ret);
           /* BashExecutor bash = new BashExecutor();
            String cmd = "cp -r " + endPath + " " + TNOAConf.get("file", "bk_path");
            logger.warn(cmd);
            bash.exec(cmd, -1, true);*/
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(490050);
        } finally {
            InfoModelPool.putModel(mysql);
            try {
                assert fos != null;
                fos.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                sxssfWorkbook.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private int exportTemplate3(List<JSONObject> list) {

        String sql = "";
        FileOutputStream fos = null;
        SXSSFWorkbook sxssfWorkbook = null;
        InfoModelHelper info = null;
        try {
            info = InfoModelPool.getModel();
            sxssfWorkbook = new SXSSFWorkbook();

            ArrayList<String> h = new ArrayList<>();
            ArrayList<String> h2 = new ArrayList<>();
            h.add("涉及分局");
            h2.add("");
            h.add("申诉项目名称");
            h2.add("");
            h.add("申诉理由");
            h2.add("");
            h.add("申诉项目分管民警");
            h2.add("");
            h.add("单位申诉");
            h2.add("单位名称");
            h.add("单位申诉");
            h2.add("申诉增补分值");
            h.add("支队核准结果");
            h2.add("核准增补分值");
            h.add("支队核准结果");
            h2.add("核查说明");

            // 获取SXSSFWorkbook实例
            Sheet sheet = sxssfWorkbook.createSheet("申诉结果");

            CellStyle cellStyle = initCellStyle(sxssfWorkbook);

            // 冻结最左边的两列、冻结最上面的一行
            // 即：滚动横向滚动条时，左边的第一、二列固定不动;滚动纵向滚动条时，上面的第一行固定不动。I
            sheet.createFreezePane(0, 2);

            // 创建第二行,作为header表头 0为第一行
            Row header = sheet.createRow(0);
            for (int i = 0; i < h.size(); i++) {
                Cell cell = header.createCell(i);
                cell.setCellValue(h.get(i));
                cell.setCellStyle(cellStyle);
            }

            Row header2 = sheet.createRow(1);
            for (int i = 0; i < h2.size(); i++) {
                Cell cell = header2.createCell(i);
                cell.setCellValue(h2.get(i));
                cell.setCellStyle(cellStyle);
            }

            CellRangeAddress cellRangeAddress1 = new CellRangeAddress(0, 1, 0, 0);
            sheet.addMergedRegionUnsafe(cellRangeAddress1);
            CellRangeAddress cellRangeAddress2 = new CellRangeAddress(0, 1, 1, 1);
            sheet.addMergedRegionUnsafe(cellRangeAddress2);
            CellRangeAddress cellRangeAddress3 = new CellRangeAddress(0, 1, 2, 2);
            sheet.addMergedRegionUnsafe(cellRangeAddress3);
            CellRangeAddress cellRangeAddress4 = new CellRangeAddress(0, 1, 3, 3);
            sheet.addMergedRegionUnsafe(cellRangeAddress4);

            CellRangeAddress cellRangeAddress14 = new CellRangeAddress(0, 0, 4, 5);
            sheet.addMergedRegionUnsafe(cellRangeAddress14);

            CellRangeAddress cellRangeAddress15 = new CellRangeAddress(0, 0, 6, 7);
            sheet.addMergedRegionUnsafe(cellRangeAddress15);


            for (int rownum = 0; rownum < list.size(); rownum++) {
                Row row = sheet.createRow(rownum + 2);
                // 循环创建单元格
                JSONObject obj = list.get(rownum);
                logger.warn(obj.toString());
                String orgId = obj.getString("org_id");
                String fj = orgId.substring(0, 6) + "000000";
                String remark = "";
                try {
                    remark = RIUtil.dicts.get(fj).getString("dict_name");
                } catch (Exception e) {
                    e.printStackTrace();
                    remark = fj;
                }

                String configId = obj.getString("config_id");
                String khName = "";
                try {
                    khName = RIUtil.kh_configs.get(configId).getString("kh_name");
                } catch (Exception e) {
                    e.printStackTrace();
                    khName = configId;
                }

                String reason = obj.getString("reason");
                String police = "";
                String respPolice = obj.getString("resp_police");
                try {
                    String s = "select name from user where id_num = '" + respPolice + "'";
                    logger.warn(s);
                    List<JSONObject> query = info.query(s);
                    police = query.get(0).getString("name");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                String orgName = "";
                try {
                    orgName = RIUtil.dicts.get(orgId).getString("dict_name");
                } catch (Exception e) {
                    e.printStackTrace();
                    orgName = orgId;
                }
                String score_new = obj.getString("score_new");
                String score_check = obj.getString("score_check");
                String instructions = obj.getString("instructions");

                Cell one = row.createCell(0);
                one.setCellValue(remark);
                one.setCellStyle(cellStyle);

                Cell two = row.createCell(1);
                two.setCellValue(khName);
                two.setCellStyle(cellStyle);

                Cell three = row.createCell(2);
                three.setCellValue(reason);
                three.setCellStyle(cellStyle);

                Cell four = row.createCell(3);
                four.setCellValue(police);
                four.setCellStyle(cellStyle);

                Cell five = row.createCell(4);
                five.setCellValue(orgName);
                five.setCellStyle(cellStyle);

                Cell six = row.createCell(5);
                six.setCellValue(score_new);
                six.setCellStyle(cellStyle);

                Cell seven = row.createCell(6);
                seven.setCellValue(score_check);
                seven.setCellStyle(cellStyle);

                Cell eight = row.createCell(7);
                eight.setCellValue(instructions);
                eight.setCellStyle(cellStyle);

            }


            String FileName = "申诉管理_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

            String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;

            init(endPath);
            fos = new FileOutputStream(endPath);
            sxssfWorkbook.write(fos);

            String sqls =
                    "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath + "','" + FileName + "',999)";
            logger.warn(sqls);
            info.update(sqls);
            sql = "select * from upload where file_name='" + FileName + "'";
            List<JSONObject> result = info.query(sql);
            JSONObject xJsonObject = result.get(0);
            int id = xJsonObject.getIntValue("id");

            logger.warn("id->" + id);
           /* BashExecutor bash = new BashExecutor();
            String cmd = "cp -r " + endPath + " " + TNOAConf.get("file", "bk_path");
            logger.warn(cmd);
            bash.exec(cmd, -1, true);*/

            String endPoint = "http://10.34.251.34:50101";
            String ak = "Q7D3OUVIRBEZEB4RWVSJ";
            String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
            String bucketName = "obs-qjjc-tyyh";
            ObsServer obsServ = new ObsServer();
            String obsFileName = "hl/" + filePath + FileName;
            System.out.println(obsFileName);
            boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
            logger.warn(obsFileName + "-->" + ret);

            return id;

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return -1;
        } finally {
            InfoModelPool.putModel(info);
            try {
                if (sxssfWorkbook != null) {
                    sxssfWorkbook.dispose();
                }
                if (fos != null) {
                    fos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private CellStyle initCellStyle(SXSSFWorkbook sxssfWorkbook) {
        //************** 样式一 *******************//
        CellStyle cellStyle = sxssfWorkbook.createCellStyle();
        cellStyle.setWrapText(true);
        //对齐方式
        //设置水平对齐方式
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        //设置垂直对齐方式
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        //设置边框
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        // 设置字体
        Font font = sxssfWorkbook.createFont();
        font.setFontName("Segoe UI");
        font.setFontHeightInPoints((short) 9);
        font.setBold(true);
        cellStyle.setFont(font);
        //************** 样式一 *******************//
        return cellStyle;
    }

    private CellStyle initCellStyle2(SXSSFWorkbook sxssfWorkbook) {
        //************** 样式二 *******************//
        CellStyle cellStyle2 = sxssfWorkbook.createCellStyle();
        cellStyle2.setWrapText(true);
        //对齐方式
        //设置水平对齐方式
        cellStyle2.setAlignment(HorizontalAlignment.CENTER);
        //设置垂直对齐方式
        cellStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle2.setDataFormat(HSSFDataFormat.getBuiltinFormat("0.000_ "));
        //设置边框
        cellStyle2.setBorderTop(BorderStyle.THIN);
        cellStyle2.setBorderRight(BorderStyle.THIN);
        cellStyle2.setBorderBottom(BorderStyle.THIN);
        cellStyle2.setBorderLeft(BorderStyle.THIN);
        // 设置字体
        Font font = sxssfWorkbook.createFont();
        font.setFontName("Segoe UI");
        font.setFontHeightInPoints((short) 9);
        font.setBold(true);
        cellStyle2.setFont(font);
        //************** 样式二 *******************//
        return cellStyle2;
    }


    private JSONObject getAppealEvent(JSONObject data, TNOAHttpRequest request) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            boolean flag = false;
            boolean flag2 = false;
            boolean flag3 = false;
            boolean flag4 = false;
            boolean complete = false;

            JSONObject userInfo = getUserInfo(request);
//            logger.warn(userInfo.toString());
            JSONArray roleId = userInfo.getJSONArray("role_id");
            for (Object o : roleId) {
                //  logger.warn("role_id---->" + o.toString());
                if ("hlcheckmanager".equals(o.toString()) || "ztchecksmanager".equals(o.toString())) {
                    flag = true;
                } else if ("hlcheckpolice".equals(o.toString())) {
                    flag2 = true;
                } else if ("sqfsz".equals(o.toString()) || "ZrqFgZrr".equals(o.toString())) {
                    flag3 = true;
                } else if ("SqPolice".equals(o.toString())) {
                    flag4 = true;
                }
            }
            String policeId = userInfo.getString("id_num");
            String orgId = userInfo.getJSONObject("organization").getString("organization_id");
            logger.warn("orgId---->" + orgId);

            //组织机构代码
            String sql = "";
            String sqls = "";
            String code = "";
            String time = "";
            String kh_name = "";
            String static_type = "";
            int limit = 20;
            int page = 1;

            if (data.containsKey("time") && data.getString("time").length() > 0) {
                time = data.getString("time");
                sql = sql + " a.time ='" + time + "' and ";
            } else {
                return ErrNo.set(490027);
            }
            if (data.containsKey("kh_name") && data.getString("kh_name").length() > 0) {
                kh_name = data.getString("kh_name");
                sql = sql + " b.kh_name like '%" + kh_name + "%' and ";
            }
            if (data.containsKey("static_type") && data.getString("static_type").length() > 0) {
                static_type = data.getString("static_type");
                sql = sql + " b.static_type ='" + static_type + "' and ";
            }
            if (data.containsKey("filter") && data.getBoolean("filter")) {
                sql = sql + " IF(b.point = '2',a.score < b.full_mark, a.score > 0) and";
            }

            if (data.containsKey("code") && data.getString("code").length() > 0) {
                code = data.getString("code");

                String type = RIUtil.dicts.get(code).getString("type");
                if ("21".equals(type) || "22".equals(type) || "27".equals(type)) {
                    sql = sql + " 1=1 and ";
                } else if (type.equals("23") || type.equals("24") || "28".equals(type)) {
                    code = code.substring(0, 6);
                    sql = sql + " a.org_id like '%" + code + "%' and ";
                } else if (type.equals("25")) {
                    code = code.substring(0, 8);
                    sql = sql + " a.org_id like '%" + code + "%' and ";
                } else if (type.equals("26")) {
                    sql = sql + " a.org_id ='" + code + "' and ";
                } else {
                    sql = sql + " a.org_id ='" + code + "' and ";
                }

            }

            sql = sql + " a.isdelete ='1' and ";

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            String s = "select id,plan from kh_history where time = '" + time + "'";
            List<JSONObject> p = mysql.query(s);

            JSONObject plan = p.get(0).getJSONObject("plan");
            Map<String, JSONObject> oldConfig = getOldConfig(plan);
            boolean isShow = false;


            if (flag) {

                sqls = "select a.*,b.kh_name,b.resp_dept,b.resp_police,b.full_mark,b.static_type,b.pg_rules,b.remark " +
                        "from kh_det a join kh_config b on a.config_id = b.id " +
                        "where " + sql + " 1=1 order by a.org_id ";

            } else if (flag2) {

                String fjConfig = plan.getString("fj_config");
                String pcsConfig = plan.getString("pcs_config");
                String zrqConfig = plan.getString("zrq_config");

                String con =
                        " ( " + divideData2(fjConfig, policeId) + divideData2(pcsConfig, policeId) + divideData2(zrqConfig, policeId);
                con = con.substring(0, con.length() - 1) + ") ";

                sqls = "select a.*,b.kh_name,b.resp_dept,b.resp_police,b.full_mark,b.static_type,b.pg_rules,b.remark " +
                        "from kh_det a join kh_config b on a.config_id = b.id " +
                        "where a.config_id in " + con + " and  " + sql + " 1=1 order by a.org_id ";

            } else if (flag3) {

                if (code.isEmpty()) {
                    code = orgId;
                    String type = RIUtil.dicts.get(code).getString("type");
                    if ("21".equals(type) || "22".equals(type) || "27".equals(type)) {
                        sql = sql + " 1=1 and ";
                    } else if (type.equals("23") || type.equals("24") || "28".equals(type)) {
                        code = code.substring(0, 6);
                        sql = sql + " a.org_id like '%" + code + "%' and ";
                    } else if (type.equals("25")) {
                        code = code.substring(0, 8);
                        sql = sql + " a.org_id like '%" + code + "%' and ";
                    } else if (type.equals("26")) {
                        sql = sql + " a.org_id ='" + code + "' and ";
                    } else {
                        sql = sql + " a.org_id ='" + code + "' and ";
                    }
                }

                sqls = "select a.*,b.kh_name,b.resp_dept,b.resp_police,b.full_mark,b.static_type,b.pg_rules,b.remark " +
                        "from kh_det a join kh_config b on a.config_id = b.id " +
                        "where " + sql + " 1=1 order by a.org_id ";
                isShow = true;

            } else if (flag4) {

                if (code.isEmpty()) {
                    code = orgId;
                }

                sqls = "select a.*,b.kh_name,b.resp_dept,b.resp_police,b.full_mark,b.static_type,b.pg_rules,b.remark " +
                        "from kh_det a join kh_config b on a.config_id = b.id " +
                        "where a.org_id = '" + code + "' and  " + sql + " 1=1 order by a.org_id ";

            }

            if (sqls.length() > 0) {

                List<JSONObject> query = mysql.query(sqls);

                sqls = sqls + " limit " + limit + " offset " + limit * (page - 1);
                logger.warn(sqls);
                List<JSONObject> res = mysql.query(sqls);

                if (res.size() > 0) {

                    for (int i = 0; i < res.size(); i++) {
                        JSONObject object = res.get(i);

                        setOldConfig(oldConfig, object);
                        String pgObject = object.getString("org_id");
                        String police = object.getString("resp_police");
                        String respDept = object.getString("resp_dept");
                        String det_file = object.getString("file_id");
                        try {
                            object.remove("file_id");
                            object.put("det_file", det_file);
                        } catch (Exception e) {
                            object.put("det_file", "");
                        }

                        try {
                            object.put("unitName", RIUtil.dicts.get(pgObject).getString("remark"));
                        } catch (Exception ex) {
                            object.put("unitName", pgObject);
                        }
                        try {
                            object.put("resp_dept_name", RIUtil.dicts.get(respDept).getString("dict_name"));
                        } catch (Exception ex) {
                            object.put("resp_dept_name", respDept);
                        }
                        try {
                            object.put("resp_police_name", RIUtil.users1.get(police).getString("name"));
                        } catch (Exception ex) {
                            object.put("resp_police_name", police);
                        }
                        object.put("isShow", isShow);
                    }
                    back.put("count", query.size());
                    back.put("data", res);

                } else {
                    back.put("count", 0);
                    back.put("data", new ArrayList<>());
                }

            } else {
                back.put("count", 0);
                back.put("data", new ArrayList<>());
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(490028);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private void setOldConfig(Map<String, JSONObject> config,
                              JSONObject object) {

        JSONObject jsonObject = config.get(object.getString("config_id"));
        object.put("kh_name", jsonObject.getString("kh_name"));
        object.put("resp_dept", jsonObject.getString("resp_dept"));
        object.put("resp_police", jsonObject.getString("resp_police"));
        object.put("full_mark", jsonObject.getString("full_mark"));
//        object.put("static_type",jsonObject.getString("static_type"));
        object.put("pg_rules", jsonObject.getString("pg_rules"));
//        object.put("remark",jsonObject.getString("remark"));
    }


    private Map<String, JSONObject> getOldConfig(JSONObject plan) {
        Map<String, JSONObject> map = new HashMap<>();
        JSONArray fj = plan.getJSONArray("fj");
        for (int i = 0; i < fj.size(); i++) {
            JSONObject jsonObject = fj.getJSONObject(i);
            JSONArray next = jsonObject.getJSONArray("next");
            for (int i1 = 0; i1 < next.size(); i1++) {
                JSONObject object = next.getJSONObject(i1);
                map.put(object.getString("id"), object);
            }
        }
        JSONArray pcs = plan.getJSONArray("pcs");
        for (int i = 0; i < pcs.size(); i++) {
            JSONObject jsonObject = pcs.getJSONObject(i);
            JSONArray next = jsonObject.getJSONArray("next");
            for (int i1 = 0; i1 < next.size(); i1++) {
                JSONObject object = next.getJSONObject(i1);
                map.put(object.getString("id"), object);
            }
        }

        JSONArray zrq = plan.getJSONArray("zrq");
        for (int i = 0; i < zrq.size(); i++) {
            JSONObject jsonObject = zrq.getJSONObject(i);
            JSONArray next = jsonObject.getJSONArray("next");
            for (int i1 = 0; i1 < next.size(); i1++) {
                JSONObject object = next.getJSONObject(i1);
                map.put(object.getString("id"), object);
            }
        }
        return map;
    }


    private JSONObject updateAppealData(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String sql = "";
            String sqls = "";
            String id = "";

            String reason = "";
            String file_id = "";
            String score_new = "";
            String status = "";
            String score_check = "";
            String instructions = "";

            String update_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(490021);
            }

            if (data.containsKey("reason") && data.getString("reason").length() > 0) {
                reason = data.getString("reason");
                sql = sql + "reason = '" + reason + "',";
            }

            if (data.containsKey("file_id") && data.getString("file_id").length() > 0) {
                file_id = data.getString("file_id");
                sql = sql + "file_id = '" + file_id + "',";
            }

            boolean flag = false;
            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getString("status");
                sql = sql + "status = '" + status + "',";
                if (status.equals("4")) {
                    flag = true;
                }
            }
            if (data.containsKey("score_check") && data.getString("score_check").length() > 0) {
                score_check = data.getString("score_check");
                sql = sql + "score_check = '" + score_check + "',";
            }
            if (data.containsKey("instructions") && data.getString("instructions").length() > 0) {
                instructions = data.getString("instructions");
                sql = sql + "instructions = '" + instructions + "',";
            }


            sqls = "update kh_appeal set " + sql + " update_time = '" + update_time + "' where id ='" + id + "' ";
            logger.warn(sqls);
            mysql.update(sqls);

            if (flag) {
                String s = "select * from kh_appeal where id = '" + id + "'";
                List<JSONObject> query = mysql.query(s);
                resetScoreDet(query);
            }

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-updateAppealData", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(490022);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    private JSONObject submitAppealData(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();

            String sql = "";
            String sqls = "";
            String id = "";
            String status = "";
            String update_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

            String ids = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

                ids = ids + " id in ('" + id.replace(",", "','") + "') ";

            } else {
                return ErrNo.set(490023);
            }
            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getString("status");
                sql = sql + "status = '" + status + "',";
            } else {
                return ErrNo.set(490023);
            }

            sqls = "update kh_appeal set " + sql + " update_time = '" + update_time + "' where " + ids;
            logger.warn(sqls);
            mysql.update(sqls);

            if ("4".equals(status)) {
                sqls = "select * from kh_appeal where " + ids;
                logger.warn(sqls);
                List<JSONObject> query = mysql.query(sqls);
                resetScoreDet(query);
            }

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-submitAppealData", userlog.TYPE_OPERATE, remoteAddr);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(490024);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private void resetScoreDet(List<JSONObject> data) {
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";

            for (int i = 0; i < data.size(); i++) {
                JSONObject object = data.get(i);
                String configId = object.getString("config_id");
                String orgId = object.getString("org_id");
                String time = object.getString("time");

                BigDecimal score = new BigDecimal(object.getString("score"));
                BigDecimal score_check = new BigDecimal(object.getString("score_check"));
                BigDecimal fullMark = new BigDecimal(RIUtil.kh_configs.get(configId).getString("full_mark"));
                BigDecimal add = score.add(score_check);
                BigDecimal result = new BigDecimal(0);
                if (add.compareTo(fullMark) <= 0) {
                    result = add;
                } else {
                    result = fullMark;
                }

                sql = "update kh_det set score = '" + result + "' " +
                        "where config_id = '" + configId + "' and org_id = '" + orgId + "' and time = '" + time + "' " +
                        "and isdelete = '1'";
                logger.warn(sql);
                mysql.update(sql);
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }


    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String configId = one.getString("config_id");
            String orgId = one.getString("org_id");
            String time = one.getString("time");

//            try {
//                String sql = "select file_id,detail from kh_det where config_id = '"+configId+"' and org_id =
//                '"+orgId+"' and time = '"+time+"' and isdelete = 1";
//                List<JSONObject> query = mysql.query(sql);
//                JSONObject object = query.get(0);
//                one.put("det_detail",object.getString("detail"));
//                one.put("det_file",object.getString("file_id"));
//            }catch (Exception e) {
//                one.put("det_detail","");
//                one.put("det_file","");
//            }

            try {

                one.put("org", RIUtil.dicts.get(orgId).getString("remark"));
                one.put("config", RIUtil.kh_configs.get(configId).getString("kh_name"));

            } catch (Exception e) {

                one.put("org", orgId);
                one.put("config", configId);
            }

            String respDept = one.getString("resp_dept");
            try {
                one.put("resp_dept_name", RIUtil.dicts.get(respDept).getString("dict_name"));
            } catch (Exception e) {
                one.put("resp_dept_name", respDept);
            }

            String respPolice = one.getString("resp_police");
            String create_user = one.getString("create_user");

            try {
                one.put("resp_police_name", RIUtil.users1.get(respPolice).getString("name"));
            } catch (Exception ex) {
                one.put("resp_police_name", respPolice);
            }

            try {
                one.put("create_user_name", RIUtil.users1.get(create_user).getString("name"));
            } catch (Exception ex) {
                one.put("create_user_name", create_user);
            }

            back.add(one);
        }
        return back;
    }

    private String divideData(String data) {
        String[] split = data.split(",");
        String con = "";
        for (int j = 0; j < split.length; j++) {
            String id = split[j];
            try {
                if (id.length() > 0) {
                    con = con + "'" + id + "',";
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return con;
    }

    private String divideData2(String data, String police) {
        String[] split = data.split(",");
        String con = "";
        for (int j = 0; j < split.length; j++) {
            String id = split[j];
            try {
                if (id.length() > 0) {
                    JSONObject obj = RIUtil.kh_configs.get(id);
                    String respPolice = obj.getString("resp_police");
                    if (police.equals(respPolice)) {
                        con = con + "'" + id + "',";
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return con;
    }

    private ArrayList<JSONObject> getConfigDetById2(String[] data) {
        ArrayList<JSONObject> back = new ArrayList<>();
        try {
            for (int i = 0; i < data.length; i++) {
                String id = data[i];
                JSONObject object = RIUtil.kh_configs.get(id);
                back.add(object);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return back;
    }

    private static List<JSONObject> getListByOrg(List<JSONObject> arrayList, String org) {
        ArrayList<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < arrayList.size(); i++) {
            JSONObject object = arrayList.get(i);
            String org_id = object.getString("org_id");
            if (org_id.equals(org)) {
                back.add(object);
            }
        }
        return back;
    }

    private static JSONObject getScoreByConfigId(List<JSONObject> arrayList, String id) {

        for (int i = 0; i < arrayList.size(); i++) {
            JSONObject object = arrayList.get(i);
            String configId = object.getString("config_id");
            if (configId.equals(id)) {
                return object;
            }
        }

        return null;
    }

    private void exportScore(ArrayList<JSONObject> configs, List<JSONObject> result, List<JSONObject> orgs,
                             SXSSFWorkbook sxssfWorkbook, String name, String time) {
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();

            ArrayList<String> h = new ArrayList<>();
            ArrayList<String> h2 = new ArrayList<>();
            ArrayList<String> h3 = new ArrayList<>();
            h.add("单位名称");
            h.add("评估月份");

            h2.add("dwmc");
            h2.add("dwdm");

            h3.add("");
            h3.add("");

            for (int i = 0; i < configs.size(); i++) {

                JSONObject object = configs.get(i);
                String config_id = object.getString("id");
                String kh_name = object.getString("kh_name");
                String fullMark = object.getString("full_mark");

                h.add(kh_name);
                h2.add(config_id);
                h3.add("权重：" + fullMark + "分");
            }
            // 获取SXSSFWorkbook实例
            Sheet sheet = sxssfWorkbook.createSheet(name);

            CellStyle cellStyle = initCellStyle(sxssfWorkbook);
            CellStyle cellStyle2 = initCellStyle2(sxssfWorkbook);
            // 冻结最左边的两列、冻结最上面的一行
            // 即：滚动横向滚动条时，左边的第一、二列固定不动;滚动纵向滚动条时，上面的第一行固定不动。
            sheet.createFreezePane(2, 2);

            // 创建第一行,作为header表头 0为第一行
            Row header = sheet.createRow(0);
            header.setHeight((short) 1000);
            for (int i = 0; i < h.size(); i++) {
                Cell cell = header.createCell(i);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(h.get(i));
            }

            Row header2 = sheet.createRow(1);
            header2.setHeight((short) 1000);
            for (int i = 0; i < h3.size(); i++) {
                Cell cell = header2.createCell(i);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(h3.get(i));
            }

            // 遍历创建行,导出数据
            for (int rownum = 0; rownum < orgs.size(); rownum++) {
                Row row = sheet.createRow(rownum + 2);
                row.setHeight((short) 600);
                // 循环创建单元格
                JSONObject org = orgs.get(rownum);
                String orgId = "";
                String orgName = "";
                orgId = org.getString("id");
                orgName = org.getString("remark");

                List<JSONObject> list2 = getListByOrg(result, orgId);

                Cell dwmc = row.createCell(0);
                dwmc.setCellStyle(cellStyle);
                sheet.setColumnWidth(0, 4500);
                dwmc.setCellValue(orgName);

                Cell pgyf = row.createCell(1);
                pgyf.setCellStyle(cellStyle);
                pgyf.setCellValue(time);
                sheet.setColumnWidth(1, 2600);

                for (int cellnum = 2; cellnum < h2.size(); cellnum++) {
                    Cell cell = row.createCell(cellnum);

                    cell.setCellStyle(cellStyle2);
                    String lab = h2.get(cellnum);
                    JSONObject det = getScoreByConfigId(list2, lab);
                    sheet.setColumnWidth(cellnum, 2600);
                    if (det != null) {
                        String score = det.getString("score");
                        cell.setCellValue(score);
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

}
