package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.Utils.ReadFilesbak;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import org.eclipse.jetty.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.thymeleaf.util.StringUtils;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
public class ExpressInfoController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/express"})
    public JSONObject get_express_info(TNOAHttpRequest request) throws Exception {
        logger.warn("express_info--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_express_info_list")) {
                return getExpressList(data);
            } else if (opt.equals("get_express")) {
                return getExpress(data);
            } else if (opt.equals("create_express_info")) {
                return createExpress(data, request.getRemoteAddr());
            } else if (opt.equals("update_express_info")) {
                return updateExpress(data);
            } else if (opt.equals("update_express_updo")) {
                return updateExpressUpDo(data);
            } else if (opt.equals("update_express_offer")) {
                return updateExpressOffer(data);
            } else if (opt.equals("delete_express_info")) {
                return deleteExpress(data, request.getRemoteAddr());
            } else if (opt.equals("get_favo")) {
                return getFavo(data);
            } else if (opt.equals("get_favo_det")) {
                return getFavoDet(data);
            } else if (opt.equals("create_favo")) {
                return createFavo(data, request.getRemoteAddr());
            } else if (opt.equals("update_favo")) {
                return updateFavo(data, request.getRemoteAddr());
            } else if (opt.equals("delete_favo")) {
                return deleteFavo(data, request.getRemoteAddr());
            } else if (opt.equals("get_favo_list")) {
                return getFavoList(data);
            }else if (opt.equals("forward_express")) {
                return getFavoList(data);
            }else {
                return ErrNo.set(607009);
            }
        } else {
            return ErrNo.set(607009);
        }
    }
    private JSONObject getFavoList(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String query = "";
            String create_time_start = "";
            String create_time_end = "";
            String create_user = "";

            String opt_user = data.getString("opt_user");

            if (data.containsKey("query") && data.getString("query").length() > 0) {
                query = data.getString("query");
                sql = sql + "( title like '%" + query + "%' or content like '%" + query + "%' or file_content like " + "'%" + query + "%') " + "and ";
            }
            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
                create_time_start = data.getString("create_time_start");
                sql = sql + "create_time>='" + create_time_start + "' and ";
            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end");
                sql = sql + "create_time<='" + create_time_end + "' and ";
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }

            String sqls = "select * from favo where create_user = '"+opt_user+"' and isdelete = 0";
            List<JSONObject> query1 = mysql.query(sqls);
            back.put("data", new JSONArray());
            back.put("count", 0);
            if (query1.size() > 0) {
                Set<String> favoList = new HashSet<>();
                for (JSONObject jsonObject : query1) {
                    String dets = jsonObject.getString("dets");
                    if (!StringUtils.isEmpty(dets)){
                        List<String> list = Arrays.asList(dets.replace("[","").replace("]","").trim().split(","));
                        if (list.size() > 0){
                            favoList.addAll(list);
                        }
                    }
                }
                if (!favoList.isEmpty()){
                    sql = sql + "(";
                    for (String string : favoList) {
                        sql = sql + "id = '"+ string +"' or ";
                    }
                    sql = sql + " 1=2 ) and ";
                }
                else {
                    sql = sql + "(id = '') and ";
                }
                sqls = "select id,title,content,flow,unit,offer_level,offer_type,isShow,isModify,create_user," +
                        "create_time,favo_id from  express_info where " + "1=1 and" + " " + sql + " " + "isdelete=0 " +
                        "order" + " by create_time desc" + " " + "limit " + limit + " " + "offset " + limit * (page - 1);
                logger.warn(sqls);
                List<JSONObject> list = new ArrayList<>();
                list = mysql.query(sqls);
                if (list.size() > 0) {
                    for (JSONObject jsonObject : list) {
                        jsonObject.put("favo",1);
                    }
                    back.put("data", RelaInfo(list, mysql, opt_user));
                    sqls = "select count(id) as count from express_info where 1=1 and " + sql + " isdelete=0; ";
                    logger.warn(sqls);
                    back.put("count", mysql.query_count(sqls));
                } else {
                    back.put("data", list);
                    back.put("count", 0);
                }
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(607005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }
    private JSONObject updateExpressUpDo(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String id = "";
        String flow = "";
        String backReason = "";
        String opt_user = "";
        String unit = "";
        String offer_type = "";
        String f = "";

        try {
            mysql = InfoModelPool.getModel();
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(607004);
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(607004);
            }
            if (data.containsKey("flow") && data.getString("flow").length() > 0) {
                flow = data.getString("flow");
                f = data.getString("flow");
            }
            String ofts = "";
            if (data.containsKey("offer_type") && data.getString("offer_type").length() > 0) {
                offer_type = data.getString("offer_type");
                ofts = "offer_type='" + offer_type + "', ";

            } else {
                if (flow.equals("1")) {
                    return ErrNo.set(607004);
                }
            }

            if (data.containsKey("back_reason") && data.getString("back_reason").length() > 0) {
                backReason = data.getString("back_reason");
            } else {
                if (flow.equals("2")) {
                    return ErrNo.set(607004);
                }
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(607004);
            }
            logger.warn(unit);
            logger.warn(RIUtil.dicts.get(unit).toString());

            String sqls = "select flow,create_user,isModify from express_info where id='" + id + "'";
            String oldF = mysql.query_one(sqls, "flow");
            String create_user = mysql.query_one(sqls, "create_user");
            String modify = mysql.query_one(sqls, "isModify");
            int mo = Integer.parseInt(modify);

            String type = RIUtil.dicts.get(unit).getString("type");
            if (type.equals("25") || type.equals("26")) {
                if (flow.equals("1")) {
                    flow = "1";
                } else if (flow.equals("3") && opt_user.equals(create_user)) {
                    flow = "0";
                }
            } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                if (flow.equals("1")) {
                    flow = "2";
                } else if (flow.equals("2")) {

                    flow = "3";
                } else if (flow.equals("3")) {
                    if (opt_user.equals(create_user)) {
                        flow = "0";
                    } else {
                        flow = "1";
                    }
                }
            } else if (type.equals("27") || type.equals("22") || type.equals("21")) {
                if (flow.equals("1")) {
                    flow = "5";
                } else if (flow.equals("2")) {
                    if (oldF.equals("2")) {
                        flow = "4";
                    } else {
                        flow = "6";
                    }
                } else if (flow.equals("3")) {
                    if (opt_user.equals(create_user)) {
                        flow = "0";
                    }
                }
            }

            String moSql = "   ";
            if (f.equals("2")) {
                mo = mo - 1;
                moSql = " , isModify='" + mo + "' ";
            }

            String update_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            ofts = ofts + " update_time='" + update_time + "' , ";
            sqls = "update express_info set " + ofts + " flow='" + flow + "' " + moSql + "  where id='" + id + "'";
            logger.warn(sqls);

            mysql.update(sqls);


            JSONObject det = new JSONObject();
            det.put("real_id", id);
            det.put("opt_user", opt_user);
            det.put("opt_time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            det.put("back_reason", backReason);
            det.put("offer_type", offer_type);
            det.put("flow", flow);
            RIUtil.JsonInsert(det, "express_his");


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(607001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject updateExpressOffer(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String id = "";
        String offer_level = "";

        String online = "0";
        String opt_user = "";
        String unit = "";
        double offer_score = 0;
        String online_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String offer_type = "";

        try {
            mysql = InfoModelPool.getModel();
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(607004);
            }
            if (data.containsKey("offer_level") && data.getString("offer_level").length() > 0) {
                offer_level = data.getString("offer_level");
            }

            if (data.containsKey("online") && data.getString("online").length() > 0) {
                online = data.getString("online");
            } else {
                return ErrNo.set(607004);
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(607004);
            }
            if (data.containsKey("online_time") && data.getString("online_time").length() > 0) {
                online_time = data.getString("online_time");
            }

            if (data.containsKey("offer_type") && data.getString("offer_type").length() > 0) {
                offer_type = data.getString("offer_type");
            }

            String update_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String sqls =
                    "update express_info set flow=9, offer_level='" + offer_level + "',online" + "='" + online + "', update_time='" + update_time + "' ,  offer_type = '"+offer_type+"'  "
                            + "where id='" + id + "'";

            mysql.update(sqls);

            sqls = "select offer_type,unit,title,content,sj_ids from express_info where id='" + id + "'";
            List<JSONObject> dets = mysql.query(sqls);
            JSONObject d = dets.get(0);
            unit = d.getString("unit").substring(0, 6) + "000000";
            offer_type = d.getString("offer_type");
            String title = d.getString("title");
            String content = d.getString("content");
            String sj_ids = d.getString("sj_ids");
            if (offer_type.length() > 0) {

                if (online.endsWith(",")) {

                    online = online.substring(0, online.length() - 1);
                }

                String sql = "select remark from dict where id in('" + online.replace(",", "','") + "') and " +
                        "isdelete=1 and " + "remark=1 ";
                logger.warn(sql);
                String isScore = mysql.query_one(sql, "remark");
                sql = "select * from express_his where real_id='" + id + "' and flow=9 and status=1 order by " +
                        "opt_time " + "desc limit 1";
                List<JSONObject> list = mysql.query(sql);
                if (list.size() > 0)//有录用历史
                {
                    JSONObject hone = list.get(0);
                    logger.warn(hone.toString());
                    String offer_month = hone.getString("offer_month");
                    String month = new SimpleDateFormat("yyyyMM").format(new Date());
                    if (offer_month.equals(month)) {
                        sqls = "update express_his set status=1 where id='" + hone.getString("id") + "'";
                        mysql.update(sqls);

                    } else {
                        if (isScore.equals("1") && !unit.endsWith("00000000")) {

                            offer_score = RIUtil.dicts.get(offer_type + "_" + unit).getDouble("remark");
                            logger.warn(RIUtil.dicts.get(offer_type + "_" + unit).toString());

                            if (offer_type.equals("2")) {
                                if (offer_level.contains("3")) {
                                    logger.warn(offer_score + "*" + 1.4);
                                    offer_score = offer_score * 1.4;
                                } else if (offer_level.contains("2")) {
                                    logger.warn(offer_score + "*" + 1.2);
                                    offer_score = offer_score * 1.2;
                                } else {
                                    logger.warn(offer_score + "*" + 1);
                                    offer_score = offer_score;
                                }
                            }

                            double score_old = hone.getDouble("offer_score");
                            offer_score = offer_score - score_old;

                        }

                    }


                } else {//无录用历史

                    if (isScore.equals("1") && !unit.endsWith("00000000")) {
                        logger.warn(offer_type + "_" + unit);

                        offer_score = RIUtil.dicts.get(offer_type + "_" + unit).getDouble("remark");
                        logger.warn(RIUtil.dicts.get(offer_type + "_" + unit).toString());

                        if (offer_type.equals("2")) {
                            if (offer_level.contains("3")) {
                                logger.warn(offer_score + "*" + 1.4);
                                offer_score = offer_score * 1.4;
                            } else if (offer_level.contains("2")) {
                                logger.warn(offer_score + "*" + 1.2);
                                offer_score = offer_score * 1.2;
                            } else {
                                logger.warn(offer_score + "*" + 1);
                                offer_score = offer_score;
                            }
                        }
                        logger.warn("score-->" + offer_score);

                    }

                }

            }

            if (online.length() > 0) {
                //挂网
                if (sj_ids.length() > 2) {
                    backFsj(sj_ids, opt_user);
                }
                JSONArray ids = Send2SJ(online, opt_user, title, content);

                sqls = "update express_info set sj_ids ='" + ids + "'where id='" + id + "'";
                mysql.update(sqls);

            }

            DecimalFormat df = new DecimalFormat("#0.000");
            JSONObject det = new JSONObject();
            det.put("real_id", id);
            det.put("opt_user", opt_user);
            det.put("opt_time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            det.put("flow", 9);
            det.put("online", online);
            det.put("offer_score", df.format(offer_score));
            det.put("offer_month", new SimpleDateFormat("yyyyMM").format(new Date()));
            det.put("offer_level", offer_level);

            logger.warn(det.toString());
            RIUtil.JsonInsert(det, "express_his");


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(607001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private void backFsj(String sj_ids, String opt_user) {

        JSONArray ids = JSONArray.parseArray(sj_ids);
        for (int i = 0; i < ids.size(); i++) {
            JSONObject one = ids.getJSONObject(i);
            try {
                OkHttpClient client = new OkHttpClient().newBuilder().build();

                MediaType mediaType = MediaType.parse("text/plain");
                String url = TNOAConf.get("HttpServ", "sj_url") + "delete-news?id=" + one.get("sjid") + "&category_id"
                        + "=" + one.get("classId") + "&username=" + opt_user + "&&key=rkzd2024";
                logger.warn(url);

                RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM).addFormDataPart("key",
                        "rkzd2024").build();
                Request request = new Request.Builder().url(url).method("POST", body).build();

                Response response = client.newCall(request).execute();

                String back = response.body().string();

                logger.warn(back);
            } catch (Exception ex) {
                logger.warn(Lib.getTrace(ex));
            }
        }

    }

    private JSONArray Send2SJ(String online, String opt_user, String title, String content) {

        JSONArray ids = new JSONArray();
        String onlines[] = online.split(",");
        for (int i = 0; i < onlines.length; i++) {
            String onone = onlines[i];
            try {
                String classId = RIUtil.dicts.get(onone).getString("permission");

                OkHttpClient client = new OkHttpClient().newBuilder().build();

                MediaType mediaType = MediaType.parse("text/plain");
                String url =
                        TNOAConf.get("HttpServ", "sj_url") + "create-news?title=" + title + "&category_id=" + classId + "&username=" + opt_user + "&&key=rkzd2024";
                logger.warn(url);

                RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM).addFormDataPart("key",
                        "rkzd2024").addFormDataPart("newstext", content).build();
                Request request = new Request.Builder().url(url).method("POST", body).build();

                Response response = client.newCall(request).execute();

                String back = response.body().string();

                logger.warn(back);
                JSONObject bks = JSONObject.parseObject(back);
                if (bks.containsKey("status") && bks.getInteger("status") == 1) {

                    JSONObject id = new JSONObject();
                    id.put("dict_id", onone);
                    id.put("sjid", bks.getString("data"));
                    id.put("classId", classId);
                    ids.add(id);
                }

            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            }

        }

        logger.warn(ids.toString());
        return ids;

    }

    //******CREATE*******
    private JSONObject createExpress(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String title = "";
            String content = "";
            String unit = "";
            String flow = "0";
            String forward_unit = "";//转发到哪些单位
            String forward_id = "";//转发的哪条
            int is_save = 0;//是否保留；

            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "0";
            String accessory = "";

            if (data.containsKey("is_save") && data.getString("is_save").length() > 0) {
                is_save = data.getInteger("is_save");
            }

            if (data.containsKey("forward_id") && data.getString("forward_id").length() > 0) {
                forward_id = data.getString("forward_id");
            }
            if (data.containsKey("forward_unit") && data.getString("forward_unit").length() > 0) {
                forward_unit = data.getString("forward_unit");
            }

            if (StringUtil.isBlank(forward_id)){
                if (data.containsKey("title") && data.getString("title").length() > 0) {
                    title = data.getString("title");
                } else {
                    return ErrNo.set(607002);
                }
            }

            if (data.containsKey("content") && data.getString("content").length() > 0) {
                content = data.getString("content");
            } else {
                //return ErrNo.set(607002);
            }
            int ut = 0;
            int modify = 0;
            if (StringUtil.isBlank(forward_id)){
                if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                    unit = data.getString("unit");
                    ut = RIUtil.dicts.get(unit).getInteger("type");
                    if (ut == 27 || ut == 22) {
                        unit = unit;
                        modify = 1;
                    } else if (ut == 26 || ut == 25) {
                        unit = unit.substring(0, 8) + "0000";
                    } else {
                        unit = unit.substring(0, 6) + "000000";
                        modify = 1;
                    }
                } else {
                    return ErrNo.set(607002);
                }
            }

            if (data.containsKey("flow") && data.getString("flow").length() > 0) {
                flow = data.getString("flow");
            }
            String fcon = "";
            if (data.containsKey("accessory") && data.getString("accessory").length() > 0) {
                accessory = data.getString("accessory");


                String[] fs = accessory.split(",");
                for (int a = 0; a < fs.length; a++) {
                    fcon = fcon + ReadFilesbak.getContent(fs[a]);
                }
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                return ErrNo.set(607002);
            }
            String sqls = "";
            //转发创建
            if (StringUtil.isNotBlank(forward_id)){
                for (String org : forward_unit.split(",")) {
                    sqls = "select * from express_info where id = '"+forward_id+"'";
                    List<JSONObject> query = mysql.query(sqls);
                    sqls = "insert express_info (title,content,unit,flow,offer_level,create_user,online,create_time,isdelete,files,file_content, offer_type,favo_id,update_time,update_user,isShow,isModify,sj_ids,forward_id,forward_unit) " +
                            "values" + " (" + (query.get(0).containsKey("title")?("'"+query.get(0).getString("title")+"'"):null) + "," +
                            (query.get(0).containsKey("content")?("'"+query.get(0).getString("content")+"'"):null) + "," +
                            (query.get(0).containsKey("unit")?("'"+query.get(0).getString("unit")+"'"):null) + "," +
                            (query.get(0).containsKey("flow")?("'"+query.get(0).getString("flow")+"'"):null) + "," +
                            (query.get(0).containsKey("offer_level")?("'"+query.get(0).getString("offer_level")+"'"):null)+ "," +
                            (query.get(0).containsKey("create_user")?("'"+query.get(0).getString("create_user")+"'"):null)+ "," +
                            (query.get(0).containsKey("online")?("'"+query.get(0).getString("online")+"'"):null) + "," +
                            (query.get(0).containsKey("create_time")?("'"+query.get(0).getString("create_time")+"'"):null)+ ",'" +
                            isdelete + "'," +
                            (query.get(0).containsKey("files")?("'"+query.get(0).getString("files")+"'"):null) + ","+
                            (query.get(0).containsKey("file_content")?("'"+query.get(0).getString("file_content")+"'"):null) + ","+
                            (query.get(0).containsKey("offer_type")?("'"+query.get(0).getString("offer_type")+"'"):null) + ","
                            + (query.get(0).containsKey("favo_id")?("'"+query.get(0).getString("favo_id")+"'"):null) + ","+
                            (query.get(0).containsKey("update_time")?("'"+query.get(0).getString("update_time")+"'"):null) + ","+
                            (query.get(0).containsKey("update_user")?("'"+query.get(0).getString("update_user")+"'"):null)+ ","
                            + (query.get(0).containsKey("isShow")?("'"+query.get(0).getString("isShow")+"'"):null) + ","+
                            (query.get(0).containsKey("isModify")?("'"+query.get(0).getString("isModify")+"'"):null) + ","+
                            (query.get(0).containsKey("sj_ids") && StringUtil.isNotBlank(query.get(0).getString("sj_ids")) ?("'"+ query.get(0).getJSONArray("sj_ids") +"'"):null) +",'"+forward_id+"','"+org+"')";
                    logger.warn(sqls);
                    mysql.update(sqls);
                }

                JSONObject det = new JSONObject();
                det.put("real_id", forward_id);
                det.put("opt_user", create_user);
                det.put("opt_time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                det.put("flow",7);
                RIUtil.JsonInsert(det, "express_his");

                //转发后删除原来的
                if (is_save == 0){
                    sqls = "update express_info set isdelete = 1,delete_user = '"+create_user+"',delete_time = '"+create_time+"' where id = '"+forward_id+"'";
                    mysql.update(sqls);
                    logger.warn(sqls);
                }

                //添加流程
                sqls = "select id from express_info where forward_id = '"+forward_id+"' and create_time = (select create_time from express_info where id = '"+forward_id+"')";
                List<JSONObject> query = mysql.query(sqls);
                sqls = "select * from express_his where real_id = '"+forward_id+"'";
                List<JSONObject> his = mysql.query(sqls);
                if (!query.isEmpty()){
                    for (JSONObject jsonObject : query) {
                        for (JSONObject hi : his) {
                            sqls = "insert into express_his (real_id,opt_user,opt_time,flow,content,back_reason,online,offer_score,status,offer_month,offer,offer_level,offer_type) " +
                                    "values('"+jsonObject.getString("id")+"',"+
                                    (hi.containsKey("opt_user")?("'"+hi.getString("opt_user")+"'"):null)+","+
                                    (hi.containsKey("opt_time")?("'"+hi.getString("opt_time")+"'"):null)+","+
                                    (hi.containsKey("flow")?("'"+hi.getString("flow")+"'"):null)+","+
                                    (hi.containsKey("content")?("'"+hi.getString("content")+"'"):null)+","+
                                    (hi.containsKey("back_reason")?("'"+hi.getString("back_reason")+"'"):null)+","+
                                    (hi.containsKey("online")?("'"+hi.getString("online")+"'"):null)+","+
                                    (hi.containsKey("offer_score")?("'"+hi.getString("offer_score")+"'"):null)+","+
                                    (hi.containsKey("status")?("'"+hi.getString("status")+"'"):null)+","+
                                    (hi.containsKey("offer_month")&& StringUtil.isNotBlank(hi.getString("offer_month"))?("'"+hi.getInteger("offer_month")+"'"):null)+","+
                                    (hi.containsKey("offer")?("'"+hi.getString("offer")+"'"):null)+","+
                                    (hi.containsKey("offer_level")?("'"+hi.getString("offer_level")+"'"):null)+","+
                                    (hi.containsKey("offer_type")?("'"+hi.getString("offer_type")+"'"):null)+")";
                            logger.warn(sqls);
                            mysql.update(sqls);

                        }
                    }
                }
            }
            else {
                sqls = "insert express_info (title,content,unit,flow,create_user," + "create_time,isdelete,files," +
                                "file_content,  isModify) " + "values" + " ('" + title + "','" + content + "','" + unit + "'," + "'" + flow + "','" + create_user + "','" + create_time + "','" + isdelete + "','" + accessory + "','" + fcon + "','" + modify + "')";
                logger.warn(sqls);
                mysql.update(sqls);
                sqls = "select id from express_info where create_user='" + create_user + "' and create_time='" + create_time + "' " + "and title='" + title + "' and isdelete=0";
                String id = mysql.query_one(sqls, "id");

                if (ut > 25 || ut == 27) {
                    JSONObject det = new JSONObject();
                    det.put("real_id", id);
                    det.put("opt_user", create_user);
                    det.put("opt_time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                    det.put("content", title + "||" + content + "||" + accessory);
                    det.put("flow", -2);
                    RIUtil.JsonInsert(det, "express_his");
                }
                back.put("data", id);
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(607001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private JSONObject getExpressList(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String query = "";

            String unit = "";
            String create_unit = "";
            String flow = "";
            String offer_type = "";
            String online = "";
            String offer_sub_type = "";
            String create_user = "";
            String create_time_start = "";
            String create_time_end = "";
            String type = "1";
            String uType = "";
            String favo_id = "";
            String opt_user = data.getString("opt_user");

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " a.id ='" + id + "' and ";
            }
            if (data.containsKey("query") && data.getString("query").length() > 0) {
                query = data.getString("query");
                sql = sql + "( a.title like '%" + query + "%' or a.content like '%" + query + "%' or a.file_content like " + "'%" + query + "%') " + "and ";
            }
            int ut = 21;
            int tt = -1;

            if (data.containsKey("create_unit") && data.getString("create_unit").length() > 0) {
                create_unit = data.getString("create_unit");
                tt = RIUtil.dicts.get(create_unit).getIntValue("type");
                if (tt == 22 || tt == 27 || tt == 21) {//支队
                    create_unit = "320400000000";
                    sql = sql + "a.unit like concat('%','" + create_unit.substring(0, 6) + "','%') and ";
                } else if (tt == 23 || tt == 24 || tt == 28) {
                    create_unit = create_unit.substring(0, 6) + "000000";
                    sql = sql + "a.unit like concat('%','" + create_unit.substring(0, 6) + "','%') and ";
                } else if (tt == 25 || tt == 26) {
                    create_unit = create_unit.substring(0, 8) + "0000";
                    sql = sql + "a.unit like concat('%','" + create_unit.substring(0, 8) + "','%') and ";
                }
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject d = RIUtil.dicts.get(unit);
                //  String t = d.getString("type");
                ut = d.getInteger("type");
            } else {
                return ErrNo.set(607004);
            }

            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                if (type.equals("1")) {//草稿箱
                    sql = sql + " a.flow=0 and a.create_user='" + opt_user + "' and ";
                }
                else if (type.equals("2")) {//我收到的上报
                    if (ut == 23 || ut == 24 || ut == 28) {//分局层
                        sql = sql + " ((a.unit like '%" + unit.substring(0, 6) + "%' and a.flow=1 and isnull(a.forward_id)) or (a.forward_unit like '%" + unit.substring(0, 6) + "%' and a.flow=1 )) and ";
                    } else if (unit.endsWith("140100") || unit.endsWith("140700") || unit.endsWith("140200")) {//综合科(本层及以下)
                        String s = "select * from dict where type = 23 or type = 24 or type = 25 or type = 26 or type = 28";
                        List<JSONObject> query1 = mysql.query(s);
                        String org = "";
                        for (JSONObject jsonObject : query1) {
                            org += "'"+jsonObject.getString("id")+"',";
                        }
                        sql = sql + " ((a.unit like '%" + unit.substring(0, 8) + "%' and a.flow in (2,5) and isnull(a.forward_id)) or " +
                                "(a.forward_unit like '%" + unit.substring(0, 8) + "%' and a.flow in (2,5)) or " +
                                "(a.unit in ("+org.substring(0,org.length()-1)+") and a.flow in (2,5) and isnull(a.forward_id)) or " +
                                "(a.forward_unit in ("+org.substring(0,org.length()-1)+") and a.flow in (2,5))) and  ";
                    } else {
                        //水上警察
                        if (unit.contains("320499")){
                            sql = sql + " ((a.unit like '%" + unit.substring(0, 6) + "%' and a.flow=5 and isnull(a.forward_id)) or (a.forward_unit like '%" + unit.substring(0, 6) + "%' and a.flow !=3 and a.flow !=4 and a.flow !=6))  and ";
                        }
                        else {
                            sql = sql + " ((a.unit like '%" + unit.substring(0, 8) + "%' and a.flow=5 and isnull(a.forward_id)) or (a.forward_unit like '%" + unit.substring(0, 8) + "%' and a.flow !=3 and a.flow !=4 and a.flow !=6))  and ";
                        }
                    }
                }
                else if (type.equals("4"))//我上报的
                {
                    if (ut == 25) {//派出所层
                        sql = sql + " a.unit ='" + unit + "' and (a.flow=1 or a.flow=2 or a.flow=5) and ";
                    } else if (ut == 26) {
                        sql = sql + " a.unit ='" + unit.substring(0, 8) + "0000' and (a.flow=1 or a.flow=2 or a.flow=5) and ";
                    } else if (ut == 23 || ut == 24 || ut == 28) {
                        sql = sql + " a.unit  like '%" + unit.substring(0, 6) + "%' and ( a.flow=2 or a.flow=5) and ";
                    } else if (ut == 27) {
                        sql = sql + " a.unit ='" + unit + "' and a.flow=5 and ";
                    } else {
                        sql = sql + " a.flow=99 and ";
                    }
                } else if (type.equals("5"))//我收到的撤回
                {
                    if (ut == 25) {//派出所层
                        sql = sql + " a.unit ='" + unit + "' and (a.flow=3 ) and ";
                    } else if (ut == 26) {
                        sql = sql + " a.unit ='" + unit.substring(0, 8) + "0000' and (a.flow=3 ) and ";
                    } else if (ut == 23 || ut == 24 || ut == 28) {
                        sql = sql + " a.unit  like '%" + unit.substring(0, 6) + "%' and  a.flow=4 and ";
                    } else if (ut == 27) {
                        sql = sql + " a.unit ='" + unit + "' and a.flow=6 and ";
                    } else {
                        sql = sql + " a.flow=99 and ";
                    }
                } else if (type.equals("6")) {//录用
                    if (create_unit.length() == 0 ) {
                        if (ut == 26) {//派出所层
                            sql = sql + " a.unit ='" + unit.substring(0, 8) + "0000' and a.flow=9 and ";
                        } else if (ut == 23 || ut == 24 || ut == 28) {
                            sql = sql + " a.unit  like '%" + unit.substring(0, 6) + "%' and  a.flow=9 and ";
                        } else if (ut == 25) {
                            sql = sql + " a.unit ='" + unit + "' and a.flow=9 and ";
                        } else {
                            sql = sql + " a.flow=9 and ";
                        }
                    }
                } else if (type.equals("7"))//我退回去的
                {
                    if (ut == 23 || ut == 24 || ut == 28) {//分局层
                        sql = sql + " a.unit like '%" + unit.substring(0, 6) + "%' and a.flow=3 and ";
                    } else if (unit.endsWith("140100") || unit.endsWith("140700")) {//综合科
                        sql = sql + " (a.flow=4 or a.flow=6)  and ";
                    } else {
                        String s= "select * from express_his where opt_user = '"+opt_user+"' and (flow = 3 or flow = 4 or flow = 6)";
                        List<JSONObject> query1 = mysql.query(s);
                        String real_id = "";
                        if (!query1.isEmpty()){
                            real_id += "(";
                            for (JSONObject jsonObject : query1) {
                                real_id += jsonObject.getString("real_id")+",";
                            }
                            real_id = real_id.substring(0,real_id.length()-1);
                            real_id += ")";
                        }
                        sql = sql + " a.id in "+real_id + " and ";
                    }
                }


            }

            if (data.containsKey("flow") && data.getString("flow").length() > 0) {
                flow = data.getString("flow");
                sql = sql + " a.flow='" + flow + "' and ";
            }
            if (data.containsKey("offer_type") && data.getString("offer_type").length() > 0) {
                offer_type = data.getString("offer_type");
                sql = sql + " a.offer_type='" + offer_type + "' and ";
            }
            if (data.containsKey("online") && data.getString("online").length() > 0) {
                online = data.getString("online");
                sql = sql + " a.online='" + online + "' and ";
            }
            if (data.containsKey("offer_sub_type") && data.getString("offer_sub_type").length() > 0) {
                offer_sub_type = data.getString("offer_sub_type");
                sql = sql + " a.offer_sub_type='" + offer_sub_type + "' and ";
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " a.create_user='" + create_user + "' and ";
            }
            if (data.containsKey("favo_id") && data.getString("favo_id").length() > 0) {
                favo_id = data.getString("favo_id");
                sql = sql + " a.favo_id like '%" + favo_id + "%' and ";
            }
            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
                create_time_start = data.getString("create_time_start");
                sql = sql + " a.create_time>='" + create_time_start + "' and ";
            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end");
                sql = sql + " a.create_time<='" + create_time_end + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }

            String sqls = "select a.id,a.title,a.content,a.flow,a.unit,a.offer_level,a.offer_type,a.isShow,a.isModify,a.create_user," +
                    "a.create_time,a.update_time,a.favo_id,a.online,a.forward_id from  express_info a  where " + "1=1 and" + " " + sql + " " + "a.isdelete=0 " +
                    "order by a.update_time desc,a.create_time desc";
//            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                for (JSONObject jsonObject : list) {
                    JSONArray jsonArray = new JSONArray();
                    if (jsonObject.containsKey("online") && !"0".equals(jsonObject.getString("online"))){
                        String[] split = jsonObject.getString("online").split(",");
                        for (String string : split) {
                            if (ObjectUtil.isEmpty(RIUtil.dicts.get(string))){
                                jsonArray.add(new JSONObject());
                            }
                            else {
                                jsonArray.add(RIUtil.dicts.get(string));
                            }
                        }
                    }
                    jsonObject.put("online_dict",jsonArray);
                }

                if (create_unit.length() > 0){
                    int finalTt = tt;
                    list = list.stream().filter(ding ->{
                        ding.put("create_unit","");
                        int  t = 1;
                        if (ding.containsKey("unit") && StringUtil.isNotBlank(ding.getString("unit"))) {
                            if (RIUtil.dicts.get(ding.getString("unit")).containsKey("type")) {
                                t = RIUtil.dicts.get(ding.getString("unit")).getIntValue("type");
                            }
                        }

                        if ((t == 22 || t == 27 || t == 21) && (finalTt == 22 || finalTt == 27 || finalTt == 21)) {//支队
                            return true;
                        } else if ((t == 23 || t == 24 || t == 28) && (finalTt == 23 || finalTt == 24 || finalTt == 28) ) {
                            return true;
                        } else if ((t == 25 || t == 26) && (finalTt == 25 || finalTt == 26)) {
                            return true;
                        }
                        return false;
                    }).collect(Collectors.toList());
                }


                back.put("count",list.size());
                list= list.stream().skip((page-1)*limit).limit(limit).
                        collect(Collectors.toList());
                back.put("data", RelaInfo(list, mysql, opt_user));

            } else {
                list= list.stream().skip((page-1)*limit).limit(limit).
                        collect(Collectors.toList());
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(607005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql, String opt_user) {

        HashMap<String, String> dets = new HashMap<>();
        try {
            String sql = "select dets from favo where create_user='" + opt_user + "' and isdelete=0";
            List<JSONObject> ll = mysql.query(sql);
            if (ll.size() > 0) {
                for (int i = 0; i < ll.size(); i++) {
                    JSONObject oo = ll.get(i);
                    String det = oo.getString("dets");
                    dets.putAll(RIUtil.StringToList(det));
                }
            }

        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
        }


        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String id_num = one.getString("create_user");
//            logger.warn(id_num);
            one.put("create_user", RIUtil1.users1.get(id_num));
            String id = one.getString("id");
            if (dets.containsKey(id)) {
                one.put("favo_id", "1");
            } else {
                one.put("favo_id", "");
            }
            back.add(one);
        }
        return back;
    }

    private Object RelaInfoDet(List<JSONObject> list, InfoModelHelper mysql, String opt_user) {
        HashMap<String, String> favos = new HashMap<>();
        try {
            String sql = "select dets from favo where create_user='" + opt_user + "' and isdelete=0";
            logger.warn(sql);
            List<JSONObject> ll = mysql.query(sql);
            if (ll.size() > 0) {
                for (int i = 0; i < ll.size(); i++) {
                    JSONObject oo = ll.get(i);
                    String det = oo.getString("dets");
                    favos.putAll(RIUtil.StringToList(det));
                }
            }

        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
        }
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            one.put("create_user", RIUtil1.users1.get(one.getString("create_user")));
            try {
                String sql = "select a.*,b.name,b.police_id from express_his a left join user b on a.opt_user =b" +
                        ".id_num " + "where" + " " + "real_id='" + one.getString("id") + "' order by opt_time";
                List<JSONObject> dets = mysql.query(sql);
                if (dets.size() > 0) {
                    one.put("his", RelaInfoHis(dets, mysql, one.getString("id")));
                } else {
                    one.put("his", new JSONArray());
                }
                String files = one.getString("files");
                JSONArray access = new JSONArray();
                if (files.length() > 0) {

                    HashMap<String, String> accesss = RIUtil.StringToList(files);
                    for (Map.Entry<String, String> a : accesss.entrySet()) {
                        String d = a.getKey();
                        String name = RIUtil.IdToName(d, mysql, "file_name", "upload");
                        JSONObject aone = new JSONObject();
                        aone.put("file_id", d);
                        aone.put("file_name", name);
                        access.add(aone);
                    }
                }
                one.put("accessory", access);

                one.put("unit", RIUtil.dicts.get(one.getString("unit")));
                String id = one.getString("id");
                if (favos.containsKey(id)) {
                    one.put("favo_id", "1");
                } else {
                    one.put("favo_id", "");
                }

            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
                one.put("his", new JSONArray());
            }


            back.add(one);
        }
        return back;
    }

    private Object RelaInfoHis(List<JSONObject> dets, InfoModelHelper mysql, String real_id) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < dets.size(); i++) {
            JSONObject one = dets.get(i);
            String content = one.getString("content");
            String id = one.getString("id");
            String hisCon = "";
            if (content.length() > 1) {

                String sql =
                        "select content from express_his where real_id='" + real_id + "' and id<" + id + " and " +
                                "length(content)>0 order by opt_time desc limit 1";
                logger.warn(sql);
                hisCon = mysql.query_one(sql, "content");


            }
            one.put("his_content", hisCon);
            back.add(one);
        }
        return back;
    }

    private JSONObject getExpress(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";

            String id = "";
            String opt_user = "";
            String unit = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(607004);
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");

                int type = RIUtil.dicts.get(unit).getInteger("type");

                String s = "select unit from express_info where id='" + id + "'";
                String u = mysql.query_one(s, "unit");
                int t = RIUtil.dicts.get(u).getInteger("type");

                if (t == 25 && (type == 23 || type == 24)) {
                    s = "update express_info set isModify=1 where id='" + id + "' and isModify<1";
                    logger.warn(s);
                    mysql.update(s);

                } else if ((t == 25 || t == 23 || t == 24 || t == 27) && (unit.equals("320400140100") || unit.endsWith("140700"))) {
                    s = "update express_info set isModify=2 where id='" + id + "' and isModify<2";
                    logger.warn(s);
                    mysql.update(s);
                }


            }
            String sqls =
                    "select id,title,content,unit,flow,offer_level,online,create_user,create_time,update_time,offer_type," +
                            "favo_id," + "isShow,isModify,files" + " from express_info where " + "1=1 and" + " " + sql + " " + "isdelete=0  ";
            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfoDet(list, mysql, data.getString("opt_user")));

            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(607005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******UPDATE*******
    private JSONObject updateExpress(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String title = "";
            String content = "";
            String unit = "";
            String files = "";
            String update_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            sql = sql + " update_time='" + update_time + "' , ";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(607004);
            }

            if (data.containsKey("title") && data.getString("title").length() > 0) {
                title = data.getString("title");
                sql = sql + " title='" + title + "' , ";
            }
            if (data.containsKey("content") && data.getString("content").length() > 0) {
                content = data.getString("content");
                sql = sql + " content='" + content + "' , ";
            }
            String favo_id = "";
            if (data.containsKey("favo_id")) {
                favo_id = data.getString("favo_id");

            }

            if (data.containsKey("accessory")) {
                files = data.getString("accessory");


                String fcon = "";
                String[] fs = files.split(",");
                for (int a = 0; a < fs.length; a++) {
                    fcon = fcon + ReadFilesbak.getContent(fs[a]);
                }

                sql = sql + " files='" + files + "' , file_content='" + fcon + "',";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");

            } else {
                return ErrNo.set(607004);
            }

            int ut = RIUtil.dicts.get(unit).getInteger("type");
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");

            } else {
                return ErrNo.set(607004);
            }
            String isShow = "";
            if (data.containsKey("isShow") && data.getString("isShow").length() > 0) {
                isShow = data.getString("isShow");
                sql = sql + " isShow='" + isShow + "' , ";
            }

            String sqls = "update express_info set " + sql + " isdelete=0  where id='" + id + "'";
            mysql.update(sqls);


            if (content.length() > 0 && (ut != 25 || ut != 26)) {
                JSONObject det = new JSONObject();
                det.put("real_id", id);
                det.put("opt_user", opt_user);
                det.put("opt_time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                det.put("content", title + "||" + content);
                det.put("flow", -1);
                RIUtil.JsonInsert(det, "express_his");

            }

            String fid = "";
            if (favo_id.length() == 0) {
                sqls = "select id,dets from favo where dets like '%" + id + "%'";
                logger.warn(sqls);
                List<JSONObject> ll = mysql.query(sqls);
                if (ll.size() > 0) {
                    for (int i = 0; i < ll.size(); i++) {
                        JSONObject one = ll.get(i);
                        if (one.containsKey("dets") && one.getString("dets").length() > 2) {
                            HashMap<String, String> det = RIUtil.StringToList(one.getString("dets"));
                            if (det.containsKey(id)) {
                                fid = one.getString("id");
                                break;
                            }
                        }
                    }
                }
            } else {
                fid = favo_id;
            }
            sqls = "select dets from favo where id='" + fid + "'";

            String dets = mysql.query_one(sqls, "dets");
            HashMap<String, String> ddd = new HashMap<>();
            if (dets != null && dets.length() > 0) {
                ddd = RIUtil.StringToList(dets);
            }
            if (favo_id.length() > 0) {


                if (!ddd.containsKey(id)) {
                    ddd.put(id, "");
                }


            } else {


                ddd.remove(id);
            }
            dets = RIUtil.HashToList(ddd).toString();

            sqls = "update favo set dets='" + dets + "' where id='" + fid + "'";
            logger.warn(sqls);
            mysql.update(sqls);
            sqls = "select offer_type,unit,title,content,sj_ids from express_info where id='" + id + "'";
            List<JSONObject> dds = mysql.query(sqls);
            JSONObject d = dds.get(0);
            String sj_ids = d.getString("sj_ids");
            if (isShow.equals("1")) {
                logger.warn(sj_ids);
                backFsj(sj_ids, opt_user);

            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(607003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private JSONObject deleteExpress(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(607008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(607008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update express_info set isdelete =1,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "删除信息速递", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(607007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

    private JSONObject createFavo(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String favo_name = "";
            String favo_remark = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "0";
            if (data.containsKey("favo_name") && data.getString("favo_name").length() > 0) {
                favo_name = data.getString("favo_name");
            } else {
                return ErrNo.set(608002);
            }
            if (data.containsKey("favo_remark") && data.getString("favo_remark").length() > 0) {
                favo_remark = data.getString("favo_remark");
            } else {

            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                return ErrNo.set(608002);
            }
            String sqls =
                    "insert favo (favo_name,favo_remark,create_user,create_time,isdelete)values('" + favo_name + "'," + "'" + favo_remark + "','" + create_user + "','" + create_time + "','" + isdelete + "')";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, create_user, "创建收藏夹", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(608001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private JSONObject getFavo(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String query = "";
            String unit = "";

            String create_user = "";
            String create_time_start = "";
            String create_time_end = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");

            } else {

            }
            if (data.containsKey("query") && data.getString("query").length() > 0) {
                query = data.getString("query");
                sql = sql + " (favo_name  like '%" + query + "%' or favo_remark like '%" + query + "%') and ";
            }

            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' and ";
            }
            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
                create_time_start = data.getString("create_time_start");
                sql = sql + "create_time>='" + create_time_start + "' and ";
            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end");
                sql = sql + "create_time<='" + create_time_end + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select * from favo where 1=1 and " + sql + " isdelete=0  limit " + limit + " offset " + limit * (page - 1);
            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo_favo(list, mysql, ""));
                sqls = "select count(id) as count from favo where 1=1 and " + sql + " isdelete=0; ";
                logger.warn(sqls);
                back.put("count", mysql.query_count(sqls));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(608005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject getFavoDet(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String query = "";
            String unit = "";

            String create_user = "";
            String create_time_start = "";
            String create_time_end = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(608004);
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String opt_user = data.getString("opt_user");
            String sqls =
                    "select * from express_info  where favo_id like '%," + id + "%' and isdelete=0  limit " + limit + " " + "offset" + " " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql, opt_user));
                sqls = "select count(id) as count from express_info  where favo_id like '%" + id + "%' and " +
                        "isdelete=0 ";
                back.put("count", mysql.query_count(sqls));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(608005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    //*****relainfo************
    private Object RelaInfo_favo(List<JSONObject> list, InfoModelHelper mysql, String unit) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            one.put("create_user", RIUtil1.users1.get(one.getString("create_user")));

            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private JSONObject updateFavo(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String favo_name = "";
            String favo_remark = "";
            String opt_user = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(608004);
            }

            if (data.containsKey("favo_name") && data.getString("favo_name").length() > 0) {
                favo_name = data.getString("favo_name");
                sql = sql + " favo_name='" + favo_name + "' , ";
            }
            if (data.containsKey("favo_remark") && data.getString("favo_remark").length() > 0) {
                favo_remark = data.getString("favo_remark");
                sql = sql + " favo_remark='" + favo_remark + "' , ";
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(608004);
            }
            String sqls = "update favo set " + sql + " isdelete=0  where id='" + id + "'";
            mysql.update(sqls);
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新收藏夹", userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(608003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private JSONObject deleteFavo(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(608008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(608008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update favo set isdelete =1,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(608007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }
}
