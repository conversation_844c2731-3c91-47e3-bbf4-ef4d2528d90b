package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.ModelSet;
import HL.TNOA.Httpd.Models.User;
import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.Utils.ChineseToFirstLetterUtil;
import HL.TNOA.Httpd.javajwt.AuthorizationApi;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.wechat.HttpConnection;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;

import static HL.TNOA.Lib.RIUtil.*;

@RestController
public class UserController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/create_user"})
    @PassToken
    public JSONObject create_user(TNOAHttpRequest request) throws Exception {
        JSONObject params = request.getRequestParams();


        if (params == null || !params.containsKey("opt") || params.getString("opt") == null || params.getString("opt").trim().length() == 0) {
            return ErrNo.set(410021);
        }

        String opt = params.getString("opt").trim();

        //创建用户
        if (opt.equals("user_create")) {
            logger.warn(params.toString());
            return user_create(request, params, request.getRemoteAddr());
        } else {
            return ErrNo.set(410021);
        }
    }

    @RequestMapping(method = {RequestMethod.POST}, path = {"/user"})

    public JSONObject operate_user(TNOAHttpRequest request, HttpServletResponse response) throws Exception {
        JSONObject params = request.getRequestParams();


        if (params == null || !params.containsKey("opt") || params.getString("opt") == null || params.getString("opt").trim().length() == 0) {
            return ErrNo.set(410021);
        }

        String opt = params.getString("opt").trim();

        //创建用户
        if (opt.equals("user_create")) {
            logger.warn(params.toString());
            return user_create(request, params, request.getRemoteAddr());
        }
        //删除用户
        else if (opt.equals("user_delete")) {
            return user_stop(request, params, request.getRemoteAddr());
        }
        //修改个人信息
        else if (opt.equals("user_update")) {
            return user_update(request, params, request.getRemoteAddr());
        }
        //修改个人信息
        else if (opt.equals("user_temp")) {
            return user_temp(request, params, request.getRemoteAddr());
        }

        //修改密码
        else if (params.getString("opt").equals("update_password")) {
            return user_update_password(request, params, request.getRemoteAddr());
        } else if (opt.equals("user_get")) {
            return user_get(request, params, request.getRemoteAddr());
        } else if ("user_onePoneR".equals(opt)) {
            logger.warn("user--->" + params.toString());
            return GetOnePoliceOneRecord(params);
        } else if (opt.equals("get_trail")) {
            return getTrail(params, request.getRemoteAddr());
        } else if (opt.equals("get_user_onoff5")) {
            return getUserOnOff5();
        } else if (opt.equals("user_bind")) {
            return UserBind(params, request, response);
        } else if (opt.equals("get_next_user")) {
            return GetNextUser(params, request);
        } else {
            return ErrNo.set(410021);
        }
    }

    private JSONObject GetNextUser(JSONObject params, TNOAHttpRequest request) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        InfoModelHelper mysql = null;
        int isNext = 1;
        int isMain = 0;
        try {
            mysql = request.openInfoImpl();
            if (params.containsKey("unit") && params.getString("unit").length() > 0) {
                unit = params.getString("unit");
            } else {
                return ErrNo.set(410068);
            }
            if (params.containsKey("isNext") && params.getString("isNext").length() > 0) {
                isNext = params.getInteger("isNext");
            }
            if (params.containsKey("isMain") && params.getString("isMain").length() > 0) {
                isMain = params.getInteger("isMain");
            }

            JSONObject done = dicts.get(unit);


            String type = "";
            try {
                type = done.getString("type");
            } catch (Exception ex) {
                type = "21";
            }

            String u = "";
            if (isNext == 1) {
                if (type.equals("21")) {
                    u = "3204";
                } else if (type.equals("23")) {
                    u = unit.substring(0, 6);
                } else if (type.equals("25") || type.equals("24") || type.equals("22")) {
                    u = unit.substring(0, 8);
                } else {
                    u = unit;
                }
            } else {
                u = unit;
            }
            String sql = "select id,police_id,name,id_num from user where unit like '%" + u + "%' and isdelete=1 and "
                    + "status=1 and isMain='" + isMain + "' order by " + "police_id";
            //System.out.println(sql);
            List<JSONObject> list = mysql.query(sql);
            //  System.out.println(list.size());
            if (list.size() > 0) {

                back.put("data", list);
            } else {
                back.put("data", new JSONArray());
            }
            return back;


        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 410068, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private JSONObject UserBind(JSONObject params, TNOAHttpRequest request, HttpServletResponse response) {

        String org_id = "";
        String job_id = "";

        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String token = request.getHeader("token");

            if (params.containsKey("organization_id") && params.getString("organization_id").length() > 0) {
                org_id = params.getString("organization_id");
            } else {
                return ErrNo.set(410021);
            }
            if (params.containsKey("job_id") && params.getString("job_id").length() > 0) {
                job_id = params.getString("job_id");
            } else {
                return ErrNo.set(410021);
            }

            String unUrl = TNOAConf.get("HttpServ", "uni_url") + "/user";


            String back = HttpConnection.post_token(unUrl, token, params, request.getRequestParams().getString("X" +
                    "-Real-IP"));
            // logger.warn(back);
            JSONObject ret = JSONObject.parseObject(back);
            if (ret.containsKey("errno") && ret.getInteger("errno") == 0) {
                JSONObject dd = ret.getJSONObject("data");
                String police_id = dd.getString("police_id");
                JSONArray orgs = new JSONArray();
                try {
                    orgs = dd.getJSONArray("all_organization");
                } catch (Exception ex) {
                }
                JSONObject org = dd.getJSONArray("organization").getJSONObject(0);
                JSONArray roleid = dd.getJSONArray("role_id");
                JSONArray resid = dd.getJSONArray("resources_id");
                token = ret.getString("token");


                String sql = "select * from user where police_id='" + police_id + "'";
                List<JSONObject> list = mysql.query(sql);
                if (list.size() != 1) {
                    logger.warn("200014");
                    return ErrNo.set(200014);
                } else {

                    int startus = list.get(0).getInteger("status");
                    if (startus == 3) {
                        logger.warn("200012");
                        return ErrNo.set(200012);
                    }
                }
                User user = new User();
                ModelSet.set(user, list.get(0));

                JSONObject uu = list.get(0);
                AuthorizationApi tokenserv = new AuthorizationApi();
                return doLoginuni(user, request, mysql, tokenserv, response, uu, token, org, orgs, resid, roleid);
            } else {
                logger.error(String.valueOf(ret));
                return ret;
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 200099, Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    public JSONObject doLoginuni(User user, TNOAHttpRequest request, InfoModelHelper infomodel,
                                 AuthorizationApi tokenserv, HttpServletResponse response, JSONObject uu,
                                 String token, JSONObject organization, JSONArray orgs, JSONArray resid,
                                 JSONArray roleid) throws Exception {
        boolean needUpdate = false;
        String sql = "";

        UserLog userlog = new UserLog();
        userlog.log(infomodel, uu.getString("id"), "登录", userlog.TYPE_OPERATE, request.getRemoteAddr());

        //tokenserv.setLoginAuthorUni(request, response, user, token);

        JSONObject retjson = ErrNo.set(0);
        try {
            //  logger.warn(uu.toString());
            JSONObject datas = new JSONObject();
            datas.put("id", uu.getString("id"));
            datas.put("name", uu.get("name"));
            datas.put("police_id", uu.getString("police_id"));
            datas.put("id_num", uu.getString("id_num"));
            datas.put("unit", uu.getString("unit"));

            String org_id = organization.getString("organization_id");

            datas.put("unit_name", dicts.get(org_id));

            String position = uu.getString("position");
            if (position != null || position.length() > 0) {
                datas.put("position_name", RIUtil.RealDictNameList(RIUtil.StringToList(position)));
            } else {
                datas.put("position_name", new JSONArray());
            }
            datas.put("position", position);
            datas.put("tele_long", uu.getString("tele_long"));
            datas.put("tele_sort", uu.getString("tele_sort"));
            datas.put("tele_home", uu.getString("tele_home"));
            datas.put("address_home", uu.getString("address_home"));
            datas.put("status", uu.getString("status"));
            datas.put("role", uu.getString("role"));
            datas.put("org", uu.getString("org"));
            String org = uu.getString("org");
            datas.put("isMain", uu.getString("isMain"));

            datas.put("org_name", RIUtil.dicts.get(org));
            datas.put("user_token", token);
            datas.put("img", uu.getString("img"));
            datas.put("permission", uu.getString("permission"));
            datas.put("isFamily", uu.getString("isFamily"));
            datas.put("organization", organization);
            datas.put("orgs", orgs);
            datas.put("resources_id", resid);
            datas.put("role_id", roleid);


            try {
                //考核集中配置
                JSONObject kh_control = new JSONObject();
                datas.put("kh_control", kh_control);
            } catch (Exception ex) {
                datas.put("kh_control", "");
            }

            try {

                JSONObject orgJS = RIUtil.dicts.get(org_id);
                String type = orgJS.getString("type");
                String faid = orgJS.getString("father_id");

                if (type.equals("21") || type.equals("22")) {
                    datas.put("rank", 1);
                    datas.put("score", 100);
                    datas.put("scoreLevel", 1);
                } else if (type.equals("23") || type.equals("25") || type.equals("26"))//分局
                {
                    sql = "select score,rank_level,rank from kh_res where pg_object='" + org_id + "' order by month " + "desc,score desc limit 1";
                    logger.warn(sql);
                    List<JSONObject> ss = infomodel.query(sql);
                    if (ss.size() > 0) {
                        JSONObject sone = ss.get(0);
                        sone.put("scoreLevel", sone.getString("rank_level"));
                        datas.putAll(sone);
                    } else {
                        datas.put("rank", 1);
                        datas.put("score", 100);
                        datas.put("scoreLevel", 1);
                    }
                } else if (type.equals("24")) {

                    sql = "select score,rank_level,rank from kh_res where pg_object='" + faid + "' order by month " + "desc,score desc limit 1";
                    logger.warn(sql);
                    List<JSONObject> ss = infomodel.query(sql);
                    if (ss.size() > 0) {
                        JSONObject sone = ss.get(0);
                        sone.put("scoreLevel", sone.getString("rank_level"));
                        datas.putAll(sone);
                    } else {
                        datas.put("rank", 1);
                        datas.put("score", 100);
                        datas.put("scoreLevel", 1);
                    }
                } else if (type.equals("28")) {
                    faid = faid.substring(0, 6) + "000000";
                    sql = "select score,rank_level,rank from kh_res where pg_object='" + faid + "' order by month " + "desc,score desc limit 1";
                    logger.warn(sql);
                    List<JSONObject> ss = infomodel.query(sql);
                    if (ss.size() > 0) {
                        JSONObject sone = ss.get(0);
                        sone.put("scoreLevel", sone.getString("rank_level"));
                        datas.putAll(sone);
                    } else {
                        datas.put("rank", 1);
                        datas.put("score", 100);
                        datas.put("scoreLevel", 1);
                    }
                } else {
                    datas.put("rank", 1);
                    datas.put("score", 100);
                    datas.put("scoreLevel", 1);
                }


            } catch (Exception ex) {


                datas.put("rank", 1);
                datas.put("score", 100);
                datas.put("scoreLevel", 1);
            }

            try {
                String unit = organization.getString("organization_id");
                String job_name = organization.getString("job_name");
                datas.put("qqb_id", GetQQBUserId(unit, uu.getString("id_num"), job_name));
            } catch (Exception ex) {
                datas.put("qqb_id", "");
            }

            logger.warn(datas.toString());

            try {
                String s = "select id,type,name,value from kh_control";
                List<JSONObject> list = infomodel.query(s);
                datas.put("kh_control", list);
            } catch (Exception e) {
                datas.put("kh_control", new ArrayList<>());
            }

            retjson.put("data", datas);


            //retjson.put("location", TNOAConf.get("basic", "location"));

            sql = "delete from login_code where tele='" + uu.getString("tele_long").trim() + "'";
            infomodel.update(sql);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }
        return retjson;
    }

    private String GetQQBUserId(String unit, String user_id, String job_name) {
        MysqlHelper qqb_user = null;
        String exeid = "";
        try {
            qqb_user = new MysqlHelper("mysql_qqb_user");

            JSONObject done = RIUtil.dicts.get(unit);
            int type = done.getInteger("type");
            int t = 0;
            if (type == 21 || type == 22 || type == 27) {
                t = 1;
            } else if (type == 23 || type == 24 || type == 28) {
                t = 2;
            } else if (type == 25 && (job_name.contains("所长") || job_name.contains("教导员"))) {
                t = 3;
            } else {
                t = 13;
            }

            String sql = "select id from auth.SYS_AUTH_USER where idcard_no='" + user_id + "' and deleted=0 and " +
                    "(IS_TEMPORARY  is null or IS_TEMPORARY <>1)" +

                    " and " + "type=" + t;

            exeid = qqb_user.query_one(sql, "ID");
            logger.warn(sql + "--->" + exeid);
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
        } finally {
            qqb_user.close();


        }
        return exeid;
    }

    private JSONObject user_temp(TNOAHttpRequest request, JSONObject params, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = request.openInfoImpl();
            JSONObject data = request.getRequestParams();
            if (data.containsKey("police_id") && data.getString("police_id").length() > 0) {
                String police_id = data.getString("police_id");

                String sql = "select * from user where police_id='" + police_id + "'";
                List<JSONObject> list = mysql.query(sql);
                if (list.size() > 0) {
                    JSONObject one = list.get(0);
                    one.put("org_name", dicts.get(one.getString("org")).getString("dict_name"));
                    sql = "select id from dict where dict_name'" + one.getString("unit") + "' and isdelete=1";
                    String unit = mysql.query_one(sql, "id");
                    one.put("unit_name", one.getString("unit"));
                    one.put("unit", unit);
                    back.put("data", one);
                } else {
                    JSONObject one = new JSONObject();
                    one.put("unit", "");
                    one.put("unit_name", "");
                    one.put("police_id", police_id);
                    one.put("org", "");
                    one.put("org_name", "");
                    back.put("data", one);
                }

                return back;

            } else {
                return ErrNo.set(200005);
            }
        } catch (Exception ex) {
            return ErrNo.set(200005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private JSONObject getUserOnOff5() {
        JSONObject back = ErrNo.set(0);
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select  name,onoff,onoff_time,position  from  user where onoff<3 " + "and position not " +
                    "like" + " '%01%' and position not like '%02%' and position not like '%03%' and " + "position " + "not" + " like " + "'%04%' and position not like '%05%' order by onoff_time desc limit 5";
            List<JSONObject> list = mysql.query(sql);
            back.put("data", list);


        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            ErrNo.set(null, 410070, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);

        }
        //logger.warn(back.getString("data").toString());
        return back;

    }

    private JSONObject GetOnePoliceOneRecord(JSONObject data) {
        String user_id = "";
        String date = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        int limit = 20;
        int page = 1;
        String isMain = "";
        String sql = "";
        String name = "";
        String order = "";
        String time = "";
        String start_time = "";
        String end_time = "";
        String unit = "";
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
                sql = sql + " a.id ='" + user_id + "' and ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " a.unit ='" + unit + "' and ";
            }
            if (data.containsKey("isMain") && data.getString("isMain").length() > 0) {
                isMain = data.getString("isMain");
                sql = sql + " a.isMain ='" + isMain + "' and ";
            }
            if (data.containsKey("name") && data.getString("name").length() > 0) {
                name = data.getString("name");
                sql = sql + "a.name  like '%" + name + "%' and ";
            }

            if (data.containsKey("date") && data.getString("date").length() > 0) {
                date = data.getString("date");
                String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
                // logger.warn(today);
                if (date.contains("-")) {
                    time = date.replace("-", "").substring(2, 8);
                    start_time = date + " 00:00:00";
                    end_time = date + " 23:59:59";
                } else if (date.equals("2"))//本周
                {
                    int week = RIUtil.GetWeekNum(today);
                    time = new SimpleDateFormat("yy").format(new Date()) + "w" + week;
                    start_time = RIUtil.getWeekStart(new SimpleDateFormat("yyyy-MM-dd").format(new Date())) + " 00:00"
                            + ":00";
                    end_time = RIUtil.getWeekEnd(start_time);
                } else if (date.equals("3"))//本月
                {
                    time = new SimpleDateFormat("yyMM").format(new Date());
                    start_time = new SimpleDateFormat("yyyy-MM-").format(new Date()) + "01 00:00:00";
                    end_time = RIUtil.getLMonthEnd(start_time);

                } else if (date.equals("4"))//本季度
                {
                    String quarter = RIUtil.GetQuarterNum(new SimpleDateFormat("MM").format(new Date()));
                    String year = new SimpleDateFormat("yyyy").format(new Date());
                    time = new SimpleDateFormat("yy").format(new Date()) + "_" + quarter;
                    String[] quTime = RIUtil.GetQuarterDate(quarter, year).split(",");
                    start_time = quTime[0];
                    end_time = quTime[1];
                }

            } else {
                time = date.replace("-", "").substring(2, 8);
                start_time = date + " 00:00:00";
                end_time = date + " 23:59:59";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");

            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");

            }
            if (data.containsKey("order") && data.getString("order").length() > 0) {
                order = data.getString("order");
                if (order.equals("ace")) {
                    order = "ASC";
                }

            }

            StatisticController.initCheckStatic(time, start_time, end_time, unit);

            String sqls = "select a.id,a.name, a.position, a.tele_long,a.tele_sort, a.onoff from user a left " +
                    "join check_static b on a.id=b.user_id where 1=1 and " + sql + "  a.id!='1' and a.isdelete=1 and "
                    + "a.status=1 and length(isFamily)=0 and b.time='" + time + "' " + " group by a.id order by a" +
                    ".position " + order + " limit " + limit + " offset " + (page - 1) * limit;

            mysql = InfoModelPool.getModel();
            List<JSONObject> list = mysql.query(sqls);
            // logger.warn(sqls);
            // logger.warn(list.toString());
            if (list.size() > 0) {
                back.put("data", RelaInfo_oneponer(list, mysql, time, start_time, end_time));
                sqls = "select count(a.id) as count from user a " + "where 1=1 and " + sql + "  id!='1' and  a" +
                        ".isdelete=1 and a.status=1";
                //logger.warn(sqls);
                back.put("count", mysql.query_count(sqls));
            } else {
                back.put("data", new ArrayList<>());
                back.put("count", 0);
            }
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 410070, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private List<JSONObject> RelaInfo_oneponer(List<JSONObject> list, InfoModelHelper mysql, String time,
                                               String start_time, String end_time) throws Exception {
        String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + " 23:59:59";
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String id = one.getString("id");
            //one.put("user_id", id);
            //logger.warn(id);

            String position = one.getString("position");

            one.put("position_name", RIUtil.RealDictNames(RIUtil.StringToList(position)));

            int onoff = one.getInteger("onoff");
            if (onoff == 3) {
                onoff = 1;
            }
            one.put("onoff", onoff);
            //排班
            String sqls = "select decode(class_name,'" + RIUtil.enName + "') as class_name " + "from class_table " +
                    "where user_id='" + id + "'";
            one.put("class_name", mysql.query_one(sqls, "class_name"));

            //排名 积分
            sqls = "select rank,point from check_static where time='" + time + "' and user_id='" + id + "'";
            List<JSONObject> checks = mysql.query(sqls);
            JSONObject cone = checks.get(0);
            one.putAll(cone);

            //公告
            //未签收
            sqls = "select id,decode(title,'" + RIUtil.enTitle + "') as title from notice " + "where reading like " + "'%" + id + "%' and  check_time>='" + start_time + "' " + "and check_time<='" + end_time + "' " + "and  is_notice=1 and isdelete=1 and type!=6";
            // System.out.println("269-->" + sqls);
            List<JSONObject> nlist = mysql.query(sqls);
            if (nlist.size() > 0) {
                one.put("notice_unread", nlist.size());
                //logger.warn(String.valueOf(nlist.size()));
                one.put("notice_unread_detail", nlist);
            } else {
                one.put("notice_unread", 0);
                one.put("notice_unread_detail", new ArrayList<>());
            }
            //已签收
            sqls = "select b.id,decode(b.title,'" + RIUtil.enTitle + "') as title " + "from notice_check_log a left " + "join notice b on a.rela_id=b.id where a.time>='" + start_time + "' and a.time <='" + end_time + "' and a.user_id='" + id + "' and b.isdelete=1 and b.type!='6'";

            nlist = mysql.query(sqls);
            if (nlist.size() > 0) {
                one.put("notice_readed", nlist.size());

                one.put("notice_readed_detail", nlist);
            } else {
                one.put("notice_readed", 0);
                one.put("notice_readed_detail", new ArrayList<>());
            }
            //信息推送
            //未签收
            sqls = "select id,decode(title,'" + RIUtil.enTitle + "') as title,decode(content,'" + RIUtil.enContent +
                    "') as content from notice where reading like " + "'%" + id + "%' and  " + "check_time>='" + start_time + "' and check_time<='" + end_time + "' and is_notice=1 and " + "isdelete=1 and type=6";
            nlist = mysql.query(sqls);
            if (nlist.size() > 0) {
                one.put("xxts_unread", nlist.size());
                //logger.warn(String.valueOf(nlist.size()));
                one.put("xxts_unread_detail", nlist);
            } else {
                one.put("xxts_unread", 0);
                one.put("xxts_unread_detail", new ArrayList<>());
            }
            //已签收
            sqls = "select b.id,decode(b.title,'" + RIUtil.enTitle + "') as title,decode(content,'" + enContent + "')"
                    + " as " + "content from notice_check_log a left join notice b on a.rela_id=b.id where a.time>='" + start_time + "' and a.time <='" + end_time + "' and a.user_id='" + id + "' and b.isdelete=1 and b.type='6'";

            nlist = mysql.query(sqls);
            if (nlist.size() > 0) {
                one.put("xxts_readed", nlist.size());

                one.put("xxts_readed_detail", nlist);
            } else {
                one.put("xxts_readed", 0);
                one.put("xxts_readed_detail", new ArrayList<>());
            }
            //事项
            //未完成
            sqls = "select b.id,decode(b.title,'" + RIUtil.enTitle + "') as title " + "from task_accepter a left " +
                    "join" + " task b on a.rela_id=b.id " + "where a.status!=2 and a.user_id='" + id + "' and b" +
                    ".end_time<='" + end_time + "' and b.end_time>='" + start_time + "' and b.isdelete=1 and a" +
                    ".isdelete=1 and (type!='9999999' and type!='22222')";

            nlist = mysql.query(sqls);
            if (nlist.size() > 0) {
                one.put("task_undo", nlist.size());
                //logger.warn(String.valueOf(nlist.size()));
                one.put("task_undo_detail", nlist);
            } else {
                one.put("task_undo", 0);
                one.put("task_undo_detail", new ArrayList<>());
            }
            //已完成
            sqls = "select b.id,decode(b.title,'" + RIUtil.enTitle + "') as title from task_log a left join " + "task"
                    + " b on a.task_id=b.id where time>='" + start_time + "' and time <='" + end_time + "' " + "and " + "task_type=2 and user_id='" + id + "' and b.isdelete=1 and (type!='9999999' and type!='22222')";
            //System.out.println("241-->" + sqls);
            nlist = mysql.query(sqls);
            //System.out.println("341->" + nlist.size());


            if (nlist.size() > 0) {
                one.put("task_finish", nlist.size());

                one.put("task_finish_detail", nlist);
            } else {
                one.put("task_finish", 0);
                one.put("task_finish_detail", new ArrayList<>());
            }
            //指令核查
            //未完成
            sqls = "select b.id,decode(b.title,'" + RIUtil.enTitle + "') as title " + "from task_accepter a left " +
                    "join" + " task b on a.rela_id=b.id " + "where a.status!=2 and a.user_id='" + id + "' and b" +
                    ".end_time<='" + end_time + "' and b.end_time>='" + start_time + "' and b.isdelete=1 and a" +
                    ".isdelete=1 and  type='22222'";

            nlist = mysql.query(sqls);
            if (nlist.size() > 0) {
                one.put("zlhc_undo", nlist.size());
                //logger.warn(String.valueOf(nlist.size()));
                one.put("zlhc_undo_detail", nlist);
            } else {
                one.put("zlhc_undo", 0);
                one.put("zlhc_undo_detail", new ArrayList<>());
            }
            //已完成
            sqls = "select b.id,decode(b.title,'" + RIUtil.enTitle + "') as title " + "from task_log a left join " +
                    "task" + " b on a.task_id=b.id " + "" + "where time>='" + start_time + "' and time <='" + end_time + "' " + "and task_type=2 " + "and user_id='" + id + "' and b.isdelete=1 and  type='22222'";

            nlist = mysql.query(sqls);
            if (nlist.size() > 0) {
                one.put("zlhc_finish", nlist.size());

                one.put("zlhc_finish_detail", nlist);
            } else {
                one.put("zlhc_finish", 0);
                one.put("zlhc_finish_detail", new ArrayList<>());
            }
//社区基础
            //未完成
            sqls = "select b.id,decode(b.title,'" + RIUtil.enTitle + "') as title,cycle,type from task_accepter a " + "left join  task b on a.rela_id=b.id where a.status!=2 and a.user_id='" + id + "' and b" + ".end_time<='" + end_time + "' and b.end_time>='" + start_time + "' and b.isdelete=1 and a" + ".isdelete=1 and  type='9999999'";

            nlist = mysql.query(sqls);
            if (nlist.size() > 0) {
                one.put("sqjc_undo", nlist.size());
                //logger.warn(String.valueOf(nlist.size()));
                one.put("sqjc_undo_detail", nlist);
            } else {
                one.put("sqjc_undo", 0);
                one.put("sqjc_undo_detail", new ArrayList<>());
            }
            //已完成
            sqls = "select b.id,decode(b.title,'" + RIUtil.enTitle + "') as title,cycle,type from task_log a left " + "join task b on a.task_id=b.id where time>='" + start_time + "' and time <='" + end_time + "' " + "and task_type=2 and user_id='" + id + "' and b.isdelete=1 and  type='9999999'";

            nlist = mysql.query(sqls);
            if (nlist.size() > 0) {
                one.put("sqjc_finish", nlist.size());

                one.put("sqjc_finish_detail", nlist);
            } else {
                one.put("sqjc_finish", 0);
                one.put("sqjc_finish_detail", new ArrayList<>());
            }

            //负面清单条数

            sqls = "select count(id) as count from brief where create_time>='" + start_time + "' and create_time" +
                    "<='" + end_time + "'and accepter like '%" + id + "%' and type=4 and isdelete=1;";
            // logger.warn(sqls);
            int count = mysql.query_count(sqls);
            //logger.warn(id + "-" + count);
            one.put("brief_count", count);

            //打处
            sqls = "select *,decode(list_name,'" + enName + "') as list_name from case_list where end_time >='" + start_time + "' and end_time<='" + end_time + "' and length(finish_time)=0 and create_user='" + id + "' and isdelete=1";
            //logger.warn(sqls);
            nlist = mysql.query(sqls);
            if (nlist.size() > 0) {
                one.put("case_undo", nlist.size());
                one.put("case_undo_detail", nlist);
            } else {

                one.put("case_undo", 0);
                one.put("case_undo_detail", new ArrayList<>());
            }
            sqls = "select *,decode(list_name,'" + enName + "') as list_name from case_list where end_time >='" + start_time + "' and end_time<='" + end_time + "' and length(finish_time)>5 and create_user='" + id + "' and isdelete=1";
            //logger.warn(sqls);
            nlist = mysql.query(sqls);
            if (nlist.size() > 0) {
                one.put("case_finish", nlist.size());
                one.put("case_finish_detail", nlist);
            } else {

                one.put("case_finish", 0);
                one.put("case_finish_detail", new ArrayList<>());
            }
            back.add(one);

        }
        return back;
    }

    private JSONObject user_get(TNOAHttpRequest request, JSONObject data, String ip) {
        String id = "";
        String police_id = "";
        String id_num = "";
        String open_id = "";
        String name = "";
        String unit = "";
        String position = "";
        String tele_long = "";
        String tele_sort = "";
        String tele_home = "";
        String address_home = "";
        String pwd = "";
        String status = "";
        String role = "";
        String org = "";
        String sql = "";
        String isFamily = "0";
        int search_type = 0;
        int limit = -1;
        int page = -1;
        int isMain = 0;
        String opt_user = "";
        String birth = "";
        int onoff = 0;
        String desciption = "";

        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");

        }

        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
            sql = sql + "id in ('" + id.replace(",", "','") + "') and ";
        }
        if (data.containsKey("police_id") && data.getString("police_id").length() > 0) {
            police_id = data.getString("police_id");
            sql = sql + "police_id='" + police_id + "' and ";
        }
        if (data.containsKey("id_num") && data.getString("id_num").length() > 0) {
            id_num = data.getString("id_num");
            sql = sql + "id_num='" + id_num + "' and ";
        }
        if (data.containsKey("open_id") && data.getString("open_id").length() > 0) {
            open_id = data.getString("open_id");
            sql = sql + "open_id='" + open_id + "' and ";
        }
        if (data.containsKey("name") && data.getString("name").length() > 0) {
            name = data.getString("name");
            sql = sql + "name like'%" + name + "%' and ";
        } else {
            String jsid = RIUtil.GetJSid();
            if (jsid.length() > 0) {
                sql = sql + "position not like '%" + RIUtil.GetJSid() + "%' and ";
            }
        }
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            sql = sql + "unit like '%" + unit + "%' and ";
        }

        if (data.containsKey("position") && data.getString("position").length() > 0) {
            position = data.getString("position");
            sql = sql + "position like '%" + position + "%' and ";
        }
        if (data.containsKey("tele_long") && data.getString("tele_long").length() > 0) {
            tele_long = data.getString("tele_long");
            sql = sql + "tele_long ='" + tele_long + "' and ";
        }
        if (data.containsKey("tele_sort") && data.getString("tele_sort").length() > 0) {
            tele_sort = data.getString("tele_sort");
            sql = sql + "tele_sort='" + tele_sort + "' and ";
        }
        if (data.containsKey("tele_home") && data.getString("tele_home").length() > 0) {
            tele_home = data.getString("tele_home");
            sql = sql + "tele_home='" + tele_home + "' and ";
        }
        if (data.containsKey("address_home") && data.getString("address_home").length() > 0) {
            address_home = data.getString("address_home");
            sql = sql + "address_home='" + address_home + "' and ";
        }

        if (data.containsKey("status") && data.getString("status").length() > 0) {
            status = data.getString("status");
            sql = sql + "status='" + status + "' and ";
        }
        if (data.containsKey("isMain") && data.getString("isMain").length() > 0) {
            isMain = data.getInteger("isMain");
            sql = sql + "isMain='" + isMain + "' and ";
        }
        if (data.containsKey("role") && data.getString("role").length() > 0) {
            role = data.getString("role");
            sql = sql + "role='" + role + "' and ";
        }
        if (data.containsKey("org") && data.getString("org").length() > 0) {
            org = data.getString("org");
            sql = sql + "org='" + org + "' and ";
        }
        if (data.containsKey("search_type") && data.getString("search_type").length() > 0) {
            search_type = data.getInteger("search_type");
        }
        if (data.containsKey("birth") && data.getString("birth").length() > 0) {
            birth = data.getString("birth");
            sql = sql + "decode(birth,'" + RIUtil.enDate + "') like '%" + birth + "%' and ";
        }
        if (data.containsKey("desciption") && data.getString("desciption").length() > 0) {
            desciption = data.getString("desciption");
            sql = sql + "decode(desciption,'" + RIUtil.enContent + "') like '%" + desciption + "%' and ";

        }
        if (data.containsKey("onoff") && data.getString("onoff").length() > 0) {
            onoff = data.getInteger("onoff");
            sql = sql + "onoff = '" + onoff + "' and ";
        }
        if (data.containsKey("isFamily") && data.getString("isFamily").length() > 0) {
            isFamily = data.getString("isFamily");
            if (isFamily.contains("1")) {
                sql = sql + "length(isFamily) >9 and ";
            } else {
                sql = sql + "length(isFamily) =0 and ";
            }
        }

        if (data.containsKey("limit") && data.getString("limit").length() > 0) {
            limit = data.getInteger("limit");
        }
        if (data.containsKey("page") && data.getString("page").length() > 0) {
            page = data.getInteger("page");
        }
        String lim = "";
        if (limit > 0 && page > 0) {
            lim = "limit " + limit + " offset " + limit * (page - 1);
        }
        JSONObject back = ErrNo.set(0);
        InfoModelHelper mysql = null;
        try {

            mysql = InfoModelPool.getModel();
            if (search_type == 1) {
                back.put("data", GetByPosition(sql, mysql, unit));
            } else if (search_type == 2)//按圈层
            {

            } else if (search_type == 3) {//首字母
                back.put("data", GetByFirstName(sql, mysql));
            } else if (search_type == 4)//进出所时间倒序
            {
                String sqls =
                        "select * from user where 1=1 and " + sql + "  isdelete = 1 order by onoff_time desc " + lim;
                logger.warn(sqls);
                List<JSONObject> list = mysql.query(sqls);
                //logger.warn(list.toString());
                back.put("count", 0);
                back.put("data", list);
                if (list.size() > 0) {
                    list = RelaInfo(list, mysql);
                    back.put("data", list);
                    sqls = "select count(id) as count from user where 1=1 and " + sql + "  isdelete = 1 ";

                    back.put("count", mysql.query_count(sqls));
                }
            } else if (search_type == 5) {
                String sqls =
                        "select * from user where 1=1 and " + sql + "  isdelete = 1 order by create_time desc " + lim;
                //logger.warn(sqls);
                List<JSONObject> list = mysql.query(sqls);
                //logger.warn(list.toString());
                back.put("count", 0);
                back.put("data", list);
                if (list.size() > 0) {
                    list = RelaInfo(list, mysql);
                    back.put("data", list);
                    sqls = "select count(id) as count from user where 1=1 and " + sql + "  isdelete = 1 ";

                    back.put("count", mysql.query_count(sqls));
                }
            } else if (search_type == 9) {
                String sqls = "select id,name,unit,id_num  from user where 1=1 and " + sql + "  isdelete = 1 order " +
                        "by" + " " + "create_time " + "desc " + lim;
                //logger.warn(sqls);
                List<JSONObject> list = mysql.query(sqls);
                //logger.warn(list.toString());
                back.put("count", 0);
                back.put("data", list);
                if (list.size() > 0) {

                    back.put("data", list);
                    sqls = "select count(id) as count from user where 1=1 and " + sql + "  isdelete = 1 ";

                    back.put("count", mysql.query_count(sqls));
                }
            } else {
                String sqls = "select * from user where 1=1 and " + sql + "  isdelete = 1 order by all_first " + lim;
                // logger.warn(sqls);
                List<JSONObject> list = mysql.query(sqls);
                //logger.warn(list.toString());
                back.put("count", 0);
                back.put("data", list);
                if (list.size() > 0) {
                    list = RelaInfo(list, mysql);
                    back.put("data", list);
                    sqls = "select count(id) as count from user where 1=1 and " + sql + "  isdelete = 1 ";

                    back.put("count", mysql.query_count(sqls));
                }

            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            ErrNo.set(null, 410070, Lib.getTrace(e));
        } finally {
            InfoModelPool.putModel(mysql);

        }
        //logger.warn(back.getString("data").toString());
        return back;
    }

    private List<JSONObject> RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        int filter = -1;
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String id = one.getString("id");

            if ("99887766".equals(id)) {
                filter = i;
            }
            if (one.containsKey("position") && one.getString("position").length() > 0) {
                String p1 = one.getString("position");//logger.warn(p1);
                one.put("position_name", RIUtil.RealDictNameList(RIUtil.StringToList(p1)));
            }
            if (one.containsKey("community")) {
                String community = one.getString("community");
                if (community.length() > 0) {
                    one.put("community", RIUtil.RealDictNameList(RIUtil.StringToList(community)));
                }
            }
            if (one.containsKey("assType")) {
                String assType = one.getString("assType");

                if (assType.length() > 0) {
                    one.put("assType_name", RIUtil.RealDictNameList(RIUtil.StringToList(assType)));
                } else {
                    one.put("assType_name", "");
                }
            }
            if (one.containsKey("onoff") && one.getString("onoff").length() > 0) {
                int onoff = one.getInteger("onoff");
                if (onoff == 3) {
                    onoff = 1;
                }
                one.put("onoff", onoff);
            }

            if (one.containsKey("unit")) {
                String unit = one.getString("unit");
                one.put("unit_name", dicts.get(unit));
            } else {
                one.put("unit_name", "");
            }
            back.add(one);
        }
        if (filter > -1) {
            back.remove(filter);
        }
        return back;
    }

    private Object GetByPosition(String sql, InfoModelHelper mysql, String unit) {
        List<JSONObject> back = new ArrayList<>();
        try {
            for (Map.Entry<String, JSONObject> pone : RIUtil.dicts.entrySet()) {
                JSONObject p = pone.getValue();
                String type = p.getString("type");
                int index = 99;
                try {

                    index = p.getInteger("index_no");
                } catch (Exception ex) {
                }

                if (type.equals("13")) {
                    String id = pone.getKey();
                    String pos_name = p.getString("dict_name");
                    String sqls =
                            "select *  from user where position like'%" + id + "%' and " + sql + " status=1 " + "and" + " " + "id!='1' and isdelete=1 order by all_first";
                    //logger.warn(sqls);

                    List<JSONObject> list = new ArrayList<>();
                    list = mysql.query(sqls);
                    // logger.warn(sqls);

                    JSONObject one = new JSONObject();
                    one.put("id", id);
                    one.put("pos_name", pos_name);
                    one.put("index_no", index);

                    //排序
                    list = RelaInfo(list, mysql);

                    one.put("users", list);
                    back.add(one);
                }

            }

            Collections.sort(back, (JSONObject o1, JSONObject o2) -> {

                int a = o1.getInteger("index_no");
                int b = o2.getInteger("index_no");

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });


        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }
        return back;
    }

    private JSONArray GetByFirstName(String sql, InfoModelHelper mysql) {
        JSONArray back = new JSONArray();
        try {
            //获取所有首字母
            String sqls =
                    "select `first`from user a where 1=1 and " + sql + " `status`=1 and isdelete = 1 GROUP BY " +
                            "`first`";
            List<JSONObject> firsts = mysql.query(sqls);
            if (firsts.size() > 0) {
                for (int i = 0; i < firsts.size(); i++) {
                    String first = firsts.get(i).getString("first");
                    sqls = "select *  from user a where 1=1 and " + sql + " first='" + first + "' and `status`=1 and "
                            + "isdelete " + "= 1 order by all_first";
                    //logger.warn(sqls);
                    List<JSONObject> list = mysql.query(sqls);
                    JSONObject o = new JSONObject();
                    o.put(first, RelaInfo(list, mysql));
                    back.add(o);
                }
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }

        return back;

    }


    public JSONObject user_create(TNOAHttpRequest request, JSONObject data, String remoteAddr) throws Exception {
        //logger.warn(data.toString());
        String police_id = "";
        String id_num = "";
        String open_id = "";
        String js_code = "";
        String name = "";
        String unit = "";
        String position = "";
        String tele_long = "";
        String tele_sort = "";
        String tele_home = "";
        String address_home = "";
        String pwd = "";
        String status = "1";
        String role = "0";
        String create_time = "";
        String create_user = "";
        String isdelete = "1";
        String img = "";
        String first = "";
        String allFirst = "";
        String community = "0";
        String urgent = "";
        String birth = "";
        String assType = "0";
        String car_num = "";
        String isFamily = "";
        String rela_label = "0";
        String work_id = "0";
        String org = "";
        String user_id = "";
        String permission = "";
        String xz_police = TNOAConf.get("HttpServ", "xz_police");
        String xz_leader = TNOAConf.get("HttpServ", "xz_leader");
        if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
            user_id = data.getString("user_id");
        } else {
            logger.warn("×->user_id");
            return ErrNo.set(410022);
        }
        if (data.containsKey("police_id") && data.getString("police_id").length() > 0) {
            police_id = data.getString("police_id");
        } else {
            logger.warn("×->police_id");
            return ErrNo.set(410022);
        }
        if (data.containsKey("id_num") && data.getString("id_num").length() > 0) {
            id_num = data.getString("id_num");
        }

        if (data.containsKey("name") && data.getString("name").length() > 0) {
            name = data.getString("name");
            allFirst = ChineseToFirstLetterUtil.ChineseToFirstLetter(name);
            logger.warn(allFirst);
            first = allFirst.substring(0, 1);

        } else {
            logger.warn("×->name");
            return ErrNo.set(410022);
        }
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
        }
        if (data.containsKey("org") && data.getString("org").length() > 0) {
            org = data.getString("org");
        }
        int main = 0;
        if (data.containsKey("position") && data.getString("position").length() > 0) {
            position = data.getString("position");
            try {
                String per = RIUtil.dicts.get(position).getString("permission");
                if (per.contains("A")) {
                    main = 1;
                    status = "3";
                }
            } catch (Exception ex) {
                main = 0;
            }
        }
        if (data.containsKey("tele_long") && data.getString("tele_long").length() > 0) {
            tele_long = data.getString("tele_long");
        } else {
            logger.warn("×->tele_long");
            return ErrNo.set(410022);
        }
        if (data.containsKey("birth") && data.getString("birth").length() > 0) {
            birth = data.getString("birth");
        } else {

        }
        if (data.containsKey("permission") && data.getString("permission").length() > 0) {
            permission = data.getString("permission");
        }
        if (data.containsKey("tele_sort") && data.getString("tele_sort").length() > 0) {
            tele_sort = data.getString("tele_sort");
        }
        if (data.containsKey("tele_home") && data.getString("tele_home").length() > 0) {
            tele_home = data.getString("tele_home");
        }
        if (data.containsKey("address_home") && data.getString("address_home").length() > 0) {
            address_home = data.getString("address_home");
        }
        if (data.containsKey("urgent") && data.getString("urgent").length() > 0) {
            urgent = data.getString("urgent");
        } else {
            return ErrNo.set(410076);
        }
        if (data.containsKey("pwd") && data.getString("pwd").length() > 0) {
            pwd = data.getString("pwd");
        }
        if (data.containsKey("community") && data.getString("community").length() > 0) {
            community = data.getString("community");
        }

        if (data.containsKey("assType") && data.getString("assType").length() > 0) {
            assType = data.getString("assType");
        }
        if (data.containsKey("car_num") && data.getString("car_num").length() > 0) {
            car_num = data.getString("car_num");
        }
        if (data.containsKey("isFamily") && data.getString("isFamily").length() > 0) {
            isFamily = data.getString("isFamily");
        }
        if (data.containsKey("rela_label") && data.getString("rela_label").length() > 0) {
            rela_label = data.getString("rela_label");
        }
        if (data.containsKey("urgent") && data.getString("urgent").length() > 0) {
            urgent = data.getString("urgent");
        } else {
            return ErrNo.set(410076);
        }
        InfoModelHelper infoModel = null;
        try {
            infoModel = InfoModelPool.getModel();
            if (data.containsKey("img") && data.getString("img").length() > 0) {
                img = data.getString("img").replace("|", "/");

            }
       /* if (data.containsKey("role") && data.getString("role").length() > 0) {
            role = data.getString("role");
        }*/
            try {
                if (position.contains(RIUtil.GetSZid(unit, "所长"))) {
                    role = "10";
                    status = "1";
                }
            } catch (Exception ex) {

            }


            create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());


            String sql = "select id from user where police_id='" + police_id + "'  and isdelete=1";
            logger.warn(sql);
            List<JSONObject> list1 = infoModel.query(sql);
            sql = "select id from user where tele_long='" + tele_long + "'   and " + "isdelete=1";
            logger.warn(sql);
            List<JSONObject> list2 = infoModel.query(sql);
            sql = "select id from user where id='" + user_id + "' and " + "isdelete=1";
            List<JSONObject> list3 = infoModel.query(sql);
            logger.warn(sql);


            if (list1.size() > 0) {

                return ErrNo.set(410069);
            }
            if (list2.size() > 0) {

                return ErrNo.set(410072);

            }
            if (list3.size() > 0) {

                return ErrNo.set(410069);

            }

            try {
                logger.warn(RIUtil.GetSZid(unit, "所长"));
                sql = "select * from user where position like '%" + RIUtil.GetSZid(unit, "所长") + "%' and status=1 " + "and" + " " + "isdelete=1";
                List<JSONObject> pp = infoModel.query(sql);
                if (pp.size() > 0 && position.contains(RIUtil.GetSZid(unit, "所长"))) {
                    return ErrNo.set(200007);
                }
            } catch (Exception ex) {

            }


            sql = "insert user (id,police_id,id_num,open_id,name,unit,position,tele_long,tele_sort,tele_home," +
                    "address_home,pwd,status,role,create_time,create_user,isdelete,img,first,all_first,work_id," +
                    "isMain,community,urgent,birth,assType,car_num,isFamily,rela_label,org,permission)" + "values('" + user_id + "'," + "'" + police_id + "','" + id_num + "','" + open_id + "',encode('" + name + "','" + RIUtil.enName + "'),'" + unit + "','" + position + "',encode('" + tele_long + "','" + RIUtil.enTele + "'),'" + tele_sort + "','" + tele_home + "','" + address_home + "',encode('" + pwd + "','" + RIUtil.enNum + "'),'" + status + "','" + role + "','" + create_time + "','" + create_user + "','" + isdelete + "','" + img + "','" + first + "','" + allFirst + "','" + work_id + "','" + main + "','" + community + "',encode('" + urgent + "','" + RIUtil.enTele + "'),encode('" + birth + "','" + RIUtil.enDate + "'),'" + assType + "',encode('" + car_num + "','" + RIUtil.enNum + "'),'" + isFamily + "','" + rela_label + "','" + org + "','" + permission + "')";
            logger.warn(sql);
            infoModel.update(sql);
            if (isFamily.length() == 0) {
                sql = "select id,users,reading,is_notice from notice where isAll=1 and isdelete=1";
                List<JSONObject> list = infoModel.query(sql);
                if (list.size() > 0) {
                    for (int i = 0; i < list.size(); i++) {
                        JSONObject so = list.get(i);
                        String sid = so.getString("id");
                        logger.warn("notice-->" + sid);
                        String susers = so.getString("users");
                        String sreading = so.getString("reading");
                        int isP = so.getInteger("is_notice");
                        HashMap<String, String> uus = RIUtil.StringToList(susers);
                        uus.put(user_id, "");
                        List<String> ulist = RIUtil.HashToList(uus);
                        List<String> rlist = new ArrayList<>();
                        if (sreading.length() > 0) {
                            HashMap<String, String> rrs = RIUtil.StringToList(sreading);
                            rrs.put(user_id, "");
                            rlist = RIUtil.HashToList(rrs);
                        }

                        sql = "update notice set users='" + ulist + "',reading='" + rlist + "' where id='" + sid + "'";
                        infoModel.update(sql);

                    }
                }
            }

            //关联案件
            logger.warn(position + "|" + xz_police + "|" + xz_leader);
            if (position.contains(xz_police) || position.contains(xz_leader)) {
                sql = "update case_info set create_user='" + user_id + "',accepter='" + user_id + "' where unit='" + unit + "' and create_user='" + name + "' and isdelete=1";
                logger.warn(sql);
                infoModel.update(sql);

            }

        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(300000);
        } finally {
            InfoModelPool.putModel(infoModel);
        }
        return ErrNo.set(0);
    }


    /**
     * 删除用户
     * JSON示例：{"opt":"stop","opt_userid":"xxxx","id":"xxxx"}
     */
    public JSONObject user_stop(TNOAHttpRequest request, JSONObject params, String remoteAddr) throws Exception {
        if (!params.containsKey("id") || params.getString("id") == null || params.getString("id").trim().length() == 0) {
            return ErrNo.set(410025);
        }
        if (!params.containsKey("opt_user") || params.getString("opt_user") == null || params.getString("opt_user").trim().length() == 0) {
            return ErrNo.set(410025);
        }
        String id = params.getString("id").trim();
        String opt_user = params.getString("opt_user");
        InfoModelHelper infomodel = request.openInfoImpl();
        String sql = "select *  from user where id = '" + id + "'";
        List<JSONObject> list = infomodel.query(sql);
        if (list.size() != 1) {
            return ErrNo.set(410025);
        }
        JSONObject user = list.get(0);
        sql = "update user set status=2,isdelete=2,delete_time='" + sdf.format(new Date()) + "',delete_user='" + opt_user + "' where id='" + id + "'";
        infomodel.update(sql);
        //  sql = "delete from user_token where userid='" + id + "'";
        //infomodel.update(sql);
        UserLog userLog = new UserLog();
        userLog.log(infomodel, params.getString("opt_userid"), "删除用户：" + id, userLog.TYPE_OPERATE, remoteAddr);
        RIUtil.users.remove(id);
        InfoModelPool.putModel(infomodel);
        return ErrNo.set(0);
    }

    public JSONObject user_update(TNOAHttpRequest request, JSONObject data, String remoteAddr) throws Exception {
        String id = "";
        String police_id = "";
        String id_num = "";
        String open_id = "";
        String name = "";
        String unit = "";
        String position = "";
        String tele_long = "";
        String tele_sort = "";
        String tele_home = "";
        String address_home = "";
        String img = "";
        String status = "";
        String role = "";
        String opt_user = "";
        String sql = "";
        int main = 0;
        String community = "";
        String urgent = "";
        String birth = "";
        String assType = "";
        String car_num = "";
        String org = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
            sql = sql + "id='" + id + "' , ";
        }
        if (data.containsKey("police_id") && data.getString("police_id").length() > 0) {
            police_id = data.getString("police_id");
            sql = sql + "police_id='" + police_id + "' , ";
        }
        if (data.containsKey("id_num") && data.getString("id_num").length() > 0) {
            id_num = data.getString("id_num");
            sql = sql + "id_num='" + id_num + "' , ";
        }
        if (data.containsKey("open_id") && data.getString("open_id").length() > 0) {
            open_id = data.getString("open_id");
            sql = sql + "open_id='" + open_id + "' , ";
        }
        if (data.containsKey("name") && data.getString("name").length() > 0) {
            name = data.getString("name");
            sql = sql + "name =encode('" + name + "','" + RIUtil.enName + "') , ";
        }
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            sql = sql + "unit='" + unit + "' , ";
        }
        if (data.containsKey("position") && data.getString("position").length() > 0) {
            position = data.getString("position");
            try {
                String per = RIUtil.dicts.get(position).getString("permission");
                if (per.contains("A")) {
                    main = 1;
                }
            } catch (Exception ex) {
                main = 0;
            }
            sql = sql + "isMain='" + main + "' , position='" + position + "', ";
        }
        if (data.containsKey("tele_long") && data.getString("tele_long").length() > 0) {
            tele_long = data.getString("tele_long");
            sql = sql + "tele_long=encode('" + tele_long + "','" + RIUtil.enTele + "') , ";
        }
        if (data.containsKey("tele_sort") && data.getString("tele_sort").length() > 0) {
            tele_sort = data.getString("tele_sort");
            sql = sql + "tele_sort='" + tele_sort + "' , ";
        }
        if (data.containsKey("tele_home") && data.getString("tele_home").length() > 0) {
            tele_home = data.getString("tele_home");
            sql = sql + "tele_home='" + tele_home + "' , ";
        }
        if (data.containsKey("address_home") && data.getString("address_home").length() > 0) {
            address_home = data.getString("address_home");
            sql = sql + "address_home='" + address_home + "' , ";
        }

        if (data.containsKey("status") && data.getString("status").length() > 0) {
            status = data.getString("status");
            sql = sql + "status='" + status + "' , ";
        }
        if (data.containsKey("role") && data.getString("role").length() > 0) {
            role = data.getString("role");
            sql = sql + "role='" + role + "' , ";
        }
        if (data.containsKey("img") && data.getString("img").length() > 0) {
            img = data.getString("img");
            sql = sql + "img='" + img + "' , ";
        }

        if (data.containsKey("community")) {
            community = data.getString("community");
            sql = sql + " community='" + community + "',";
        }

        if (data.containsKey("org") && data.getString("org").length() > 0) {
            org = data.getString("org");
            sql = sql + " org='" + org + "',";
        }
        if (data.containsKey("assType")) {
            assType = data.getString("assType");
            sql = sql + " assType='" + assType + "',";
        }
        if (data.containsKey("urgent") && data.getString("urgent").length() > 0) {
            urgent = data.getString("urgent");
            sql = sql + " urgent=encode('" + urgent + "','" + RIUtil.enTele + "'),";
        }
        if (data.containsKey("car_num")) {
            car_num = data.getString("car_num");
            sql = sql + " car_num=encode('" + car_num + "','" + RIUtil.enNum + "'),";
        }
        if (data.containsKey("birth") && data.getString("birth").length() > 0) {
            birth = data.getString("birth");
            sql = sql + " birth=encode('" + birth + "','" + RIUtil.enDate + "'),";

        }
        if (data.containsKey("permission") && data.getString("permission").length() > 0) {
            String permission = data.getString("permission");
            sql = sql + " permission='" + permission + "',";

        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        }
        InfoModelHelper mysql = null;
        try {
            mysql = request.openInfoImpl();
            String sqls = "update user  set " + sql + " isdelete=1 where id='" + id + "'";
            logger.warn(sqls);
            mysql.update(sqls);
            initUsers();

            //保存操作日志
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "更新用户信息：" + name, userlog.TYPE_OPERATE, remoteAddr);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 410026, Lib.getTrace(ex));
        } finally {

        }
        return ErrNo.set(0);
    }

    /**
     * 修改用户密码
     *
     * @param params
     * @param remoteAddr
     * @return
     * @throws Exception
     */
    public JSONObject user_update_password(TNOAHttpRequest request, JSONObject params, String remoteAddr) throws Exception {
        if (!params.containsKey("id") || params.getString("id") == null || params.getString("id").trim().length() == 0) {
            return ErrNo.set(410063);
        }
        InfoModelHelper info = request.openInfoImpl();
        String id = params.getString("id");
        String sql = "select id,decode(pwd,'" + RIUtil.enNum + "') as pwd from user where id='" + id + "'";
        List<JSONObject> list = info.query(sql);
        if (list.size() == 0) {
            return ErrNo.set(410064);
        }
        JSONObject user = list.get(0);

        if (params.containsKey("check") && params.getBoolean("check")) {
            if (!user.getString("pwd").equals(params.getString("oraginPassword"))) {
                return ErrNo.set(410065);
            }
        }
        if (params.getString("newPassword").trim().length() > 0 && params.getString("newPassword") != null) {
            String newpass = params.getString("newPassword");
            sql = "update user set pwd=encode('" + newpass + "','" + RIUtil.enNum + "') where id='" + id + "'";
            info.update(sql);
        }


        UserLog userLog = new UserLog();
        userLog.log(info, params.getString("opt_user"), "修改用户 " + user.getString("id") + " 密码", userLog.TYPE_OPERATE,
                remoteAddr);

        return ErrNo.set(0);
    }

    public static void initUsers() {
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select * from user where status=1 and isdelete=1 and permission not like '%web%'";
            List<JSONObject> list = mysql.query(sql);
            for (int i = 0; i < list.size(); i++) {
                JSONObject one = list.get(i);
                if (!one.containsKey("name")) {
                    break;
                }
                if (!one.containsKey("tele_long")) {
                    one.put("tele_long", "");
                }
                if (!one.containsKey("position")) {
                    one.put("position", "");
                } else {
                    String position = one.getString("position");

                    one.put("position_name", RealDictNames(RIUtil.StringToList(position)));
                }
                if (!one.containsKey("org")) {
                    one.put("org", "");
                    one.put("org_name", "");
                } else {
                    String org = one.getString("org");

                    one.put("org_name", RealDictNames(RIUtil.StringToList(org)));
                }

                if (!one.containsKey("tele_sort")) {
                    one.put("tele_sort", "");
                }
                if (!one.containsKey("id_num")) {
                    one.put("id_num", "");
                }
                if (!one.containsKey("img")) {
                    one.put("img", "");
                }
                if (!one.containsKey("community")) {
                    one.put("community", "");
                } else {
                    String community = one.getString("community");
                    one.put("community_name", RealDictNameList(RIUtil.StringToList(community)));
                }
                if (!one.containsKey("isMain")) {
                    one.put("isMain", "");
                }
                if (!one.containsKey("assType")) {
                    one.put("accType", "");
                    one.put("assType_name", "");
                } else {
                    String accType = one.getString("assType");
                    one.put("assType_name", RealDictNameList(RIUtil.StringToList(accType)));
                }
                if (!one.containsKey("open_id")) {
                    one.put("open_id", "");
                }
                if (!one.containsKey("unit")) {
                    one.put("unit", "");
                    one.put("unit_name", "");
                } else {
                    String unit = one.getString("unit");
                    one.put("unit_name", RIUtil.dicts.get(unit));
                }
                if (!one.containsKey("car_num")) {
                    one.put("car_num", "");
                }

                RIUtil.users.put(one.getString("id"), one);
            }

            JSONObject one = new JSONObject();
            one.put("name", "全警小喇叭");
            one.put("position", "");
            one.put("position_name", "");
            one.put("tele_long", "");
            one.put("tele_sort", "");
            one.put("img", "");
            one.put("community", "");
            one.put("community_name", "");
            one.put("isMain", "");
            one.put("assType", "");
            one.put("assType_name", "");
            one.put("onoff", "");
            one.put("onoff_time", "");
            one.put("isFamily", "");
            one.put("open_id", "");
            one.put("unit", "");
            one.put("unit_name", "");
            one.put("car_num", "");
            one.put("org", "");
            one.put("org_name", "");
            one.put("id_num", "");

            RIUtil.users.put("999999999", one);
            //logger.warn("init.users->" + RIUtil.dicts.size());
            //logger.warn(RIUtil.users.toString());

        } catch (Exception ex) {

        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    public JSONObject getTrail(JSONObject data, String clientIP) {
        String user_id = "";

        int page = 1;
        int limit = 20;
        String car_num = "";
        String start_time = "";
        String end_time = "";
        String sql = "";
        String timeSql = "";
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0 && data.containsKey(
                    "end_time") && data.getString("end_time").length() > 0) {
                start_time = data.getString("start_time").replace("|", " ");
                end_time = data.getString("end_time").replace("|", " ");
                timeSql = "and (time>='" + start_time + "' and time<='" + end_time + "') ";

            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0 && data.containsKey("page") && data.getString("page").length() > 0) {
                limit = data.getInteger("limit");
                page = data.getInteger("page");
            }
            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
                car_num = RIUtil.IdToName(user_id, mysql, "car_num", "user");
                car_num = car_num.replace(",", "','");
                if (car_num.length() > 0) {
                    sql = " UNION SELECT time,direction, '道闸' as type from gate_event where car_num in('" + car_num + "') " + timeSql;
                }
            } else {
                return ErrNo.set(445001);
            }

            String sqls = "select time,dir,'门禁' as type from attendance where user_id='" + user_id + "'" + timeSql +
                    "   " + sql + " order by time desc limit " + limit + " offset " + (page - 1) * limit;
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            logger.warn(sqls);
            back.put("data", list);
            sqls = "select time,dir,'门禁' as type from attendance where user_id='" + user_id + "'" + timeSql + "   " + sql;
            list = mysql.query(sqls);
            back.put("count", list.size());
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 445002, Lib.getTrace(ex));

        } finally {
            InfoModelPool.putModel(mysql);
        }


    }
}
