package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.MysqlHelper;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@RestController
public class SyncFj {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());

    @RequestMapping(method = {RequestMethod.POST}, path = {"/getFj"})
    public JSONObject SyncFj(TNOAHttpRequest request) throws Exception {
        logger.warn("SyncFj--->" + request.getRequestParams().toString());
        JSONObject data = request.getRequestParams();
        String opt = "";
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_fj_list")) {
                return get_fj_list(data);
            } else if (opt.equals("get_fj")) {
                return get_fj(data);
            }else {
                return ErrNo.set(607009);
            }
        }else {
            return ErrNo.set(607009);
        }
    }

    private JSONObject get_fj(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper mysql = null;
        String id_card = "";
        String sql = "";
        try {
            mysql = new MysqlHelper("mysql_syncfj");
            if (data.containsKey("id_card") && data.getString("id_card").length() > 0) {
                id_card = data.getString("id_card");
                sql = sql + " and id_card like concat('%','" + id_card + "','%') ";
            }
            else {
                return ErrNo.set(607002);
            }
            String sqls = "select * from user where 1=1 "+ sql;
            logger.warn(sqls);
            List<JSONObject> query = mysql.query(sqls);
            if (query.size() > 0) {
                query.get(0).put("organization", RIUtil.dicts.get(query.get(0).getString("organization")));
                query.get(0).put("police",query.get(0).getJSONArray("police"));
                back.put("data",query.get(0));
            } else {
                back.put("data", new JSONObject());
                back.put("count", 0);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            mysql.close();
        }
        return back;
    }

    private JSONObject get_fj_list(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper mysql = null;
        String query = "";
        String organization = "";
        String sql = "";
        int limit = 20;
        int page = 1;
        try {
            mysql = new MysqlHelper("mysql_syncfj");
            if (data.containsKey("query") && data.getString("query").length() > 0) {
                query = data.getString("query");
                sql = sql + " and (username like concat('%','" + query + "','%') or phone like concat('%','" + query + "','%') or " +
                        "name like concat('%','" + query + "','%') or police_id like concat('%','" + query + "','%') or id_card like concat('%','" + query + "','%') or " +
                        "organization like concat('%','" + query + "','%')) ";
            }
            if (data.containsKey("organization") && data.getString("organization").length() > 0) {
                organization = data.getString("organization");
                sql = sql + " and organization like concat('%','" + organization + "','%') ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls = "select * from user where 1=1 " + sql ;
            logger.warn(sqls);
            List<JSONObject> list = mysql.query(sqls);
            if (list.size() > 0) {
                for (JSONObject jsonObject : list) {
                    jsonObject.put("organization", RIUtil.dicts.get(jsonObject.getString("organization")));
                    jsonObject.put("police",jsonObject.getJSONArray("police"));
                }
                back.put("count",list.size());
                list= list.stream().skip((page-1)*limit).limit(limit).
                        collect(Collectors.toList());
                back.put("data",list);
            } else {
                back.put("data", new JSONArray());
                back.put("count", 0);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            mysql.close();
        }
        return back;
    }
}
