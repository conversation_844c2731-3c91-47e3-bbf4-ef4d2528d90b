package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.ModelSet;
import HL.TNOA.Httpd.Models.User;
import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.AuthorizationApi;
import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.wechat.ConnectWeChat;
import HL.TNOA.wechat.HttpConnection;
import HL.TNOA.wechat.SenSMS;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedWriter;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

import static HL.TNOA.Lib.RIUtil.dicts;


@RestController
public class LoginController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());

    @RequestMapping(method = {RequestMethod.POST}, path = {"/login"})
    @PassToken
    public JSONObject login(TNOAHttpRequest request, HttpServletResponse response) {
        try {
            logger.warn("login--->" + request.getRequestParams().toString());
            JSONObject data = request.getRequestParams();
            InfoModelHelper infomodel = request.openInfoImpl();
            String tele = "";
            String code = "";
            String user_agent = "";
            String user_token = "";
            int count = 0;
            String create_time = RIUtil.GetNextDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), 15);

            if (data.containsKey("user_agent") && data.getString("user_agent").length() > 0) {
                user_agent = data.getString("user_agent");
            } else {
                return ErrNo.set(200019);
            }
            if (data.containsKey("user_token") && data.getString("user_token").length() > 0) {
                user_token = data.getString("user_token");

                String sql =
                        "select userid from user_token where user_agent='" + user_agent + "' and token='" + user_token + "' and createtime<='" + create_time + "'";
                String userid = infomodel.query_one(sql, "userid");
                if (userid.equals("")) {
                    writeLog(request.getRemoteAddr(), "overdue token");
                    return ErrNo.set(200021);
                } else {
                    count = 1;
                    tele = userid;
                    sql = "update user_token set createtime='" + create_time + "' where token='" + user_token + "' " + "and user_agent='" + user_agent + "'";
                    infomodel.update(sql);

                }

            } else {
                if (data.containsKey("tele") && data.getString("tele").length() > 0) {
                    tele = data.getString("tele");
                } else {
                    return ErrNo.set(200019);
                }
                if (data.containsKey("code") && data.getString("code").length() > 0) {
                    code = data.getString("code");
                } else {
                    return ErrNo.set(200019);
                }
                String sql =
                        "select count(id) as count from login_code where tele='" + tele + "' and code='" + code + "' "
                                + "and time>=" + (System.currentTimeMillis() - 5 * 60 * 1000);
                logger.warn(sql);
                count = infomodel.query_count(sql);
                sql = "delete from user_token where userid='" + tele + "'";
                infomodel.update(sql);
            }

            if (count > 0) {
                user_token = getMD5Str(tele);
                String sql =
                        "insert user_token (user_agent,token,userid,createtime) values ('" + user_agent + "','" + user_token + "','" + tele + "','" + create_time + "')";
                infomodel.update(sql);
                sql = "select  * from user " + "where tele_long='" + tele + "'  and " + "isdelete=1";
                List<JSONObject> list = infomodel.query(sql);
                logger.warn(sql);
                if (list.size() != 1) {
                    writeLog(request.getRemoteAddr(), "overdue token");
                    return ErrNo.set(200005);
                } else {

                    int startus = list.get(0).getInteger("status");
                    if (startus == 3) {
                        writeLog(request.getRemoteAddr(), tele + " no access");
                        return ErrNo.set(200012);
                    }
                }
                User user = new User();
                ModelSet.set(user, list.get(0));

                AuthorizationApi tokenserv = new AuthorizationApi();
                return doLogin(user, request, infomodel, tokenserv, response, list.get(0), user_token);
            } else {
                writeLog(request.getRemoteAddr(), tele + " no access");
                return ErrNo.set(200020);
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {

        }
    }

    @RequestMapping(method = {RequestMethod.POST}, path = {"/login_wx"})
    @PassToken
    public JSONObject login_wx(TNOAHttpRequest request, HttpServletResponse response) {
        try { //logger.warn("login--->" + request.getRequestParams().toString());
            JSONObject data = request.getRequestParams();
            InfoModelHelper infomodel = request.openInfoImpl();

            String code = "";

            if (data.containsKey("login_code") && data.getString("login_code").length() > 0) {
                code = data.getString("login_code");
            } else {
                writeLog(request.getRemoteAddr(), "no promission");
                return ErrNo.set(200019);
            }
           /* String sql = "select tele from login_code where code='" + code + "'";
            //logger.warn(sql);

            String tele = infomodel.query_one(sql, "tele");
            if (tele.length() > 0) {

            } else {
                return ErrNo.set(200020);
            }*/
            String sql = "select  * from user where open_id_ys='" + code + "'  and " + " status=1 and isdelete=1 and "
                    + "role=10";
            List<JSONObject> list = infomodel.query(sql);
            // logger.warn(list.toString());
            if (list.size() != 1) {
                writeLog(request.getRemoteAddr(), "no promission");
                return ErrNo.set(200005);
            }
            User user = new User();
            ModelSet.set(user, list.get(0));

            AuthorizationApi tokenserv = new AuthorizationApi();


            return doLogin(user, request, infomodel, tokenserv, response, list.get(0), "");

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {

        }
    }

    @RequestMapping(method = {RequestMethod.POST}, path = {"/login_app"})
    @PassToken
    public JSONObject login_app(TNOAHttpRequest request, HttpServletResponse response) {
        InfoModelHelper infomodel = null;
        try {
            JSONObject data = request.getRequestParams();
            logger.warn("login--->" + data.toString() + request.getRemoteAddr());

            JSONObject contextjson = data;


            infomodel = InfoModelPool.getModel();

            String token = "";
            String username = "";
            String password = "";
            String jsCode = "";
            String open_id = "";
            String open_id_ys = "";
            String union_id = "";
            JSONObject uu = new JSONObject();
            User user = new User();
            //判断格式

            if ((contextjson.containsKey("js_code") && contextjson.getString("js_code").length() > 0) && !contextjson.containsKey("username")) {
                jsCode = contextjson.getString("js_code");
                open_id = ConnectWeChat.GetOpenId(jsCode);
                //union_id = ConnectWeChat.GetUnionId(open_id);
                //logger.warn("微信统一id-->" + union_id);

                String sql = "select * from user where open_id='" + open_id + "' and isdelete=1";
                List<JSONObject> list = infomodel.query(sql);
                if (list.size() != 1) {
                    logger.warn("200014");
                    writeLog(request.getRemoteAddr(), "no promission");
                    return ErrNo.set(200014);
                } else {

                    int startus = list.get(0).getInteger("status");
                    if (startus == 3) {
                        logger.warn("200012");
                        writeLog(request.getRemoteAddr(), "no promission");
                        return ErrNo.set(200012);
                    }
                }

                ModelSet.set(user, list.get(0));

                uu = list.get(0);
                AuthorizationApi tokenserv = new AuthorizationApi();
                return doLogin(user, request, infomodel, tokenserv, response, uu, token);
            } else {
                if (contextjson.containsKey("username") && contextjson.getString("username").length() > 0) {
                    username = contextjson.getString("username");

                } else {
                    logger.warn("200005");
                    writeLog(request.getRemoteAddr(), "no promission");
                    return ErrNo.set(200005);
                }

                if (contextjson.containsKey("password") && contextjson.getString("password") != null) {
                    password = contextjson.getString("password");
                } else {

                }
                String user_id = "";
                if (contextjson.containsKey("user_id") && contextjson.getString("user_id") != null) {
                    user_id = contextjson.getString("user_id");
                } else {
                    if (password.length() == 0) {
                        logger.warn("200005");
                        writeLog(request.getRemoteAddr(), "no promission");
                        return ErrNo.set(200005);
                    }
                }

                if (contextjson.containsKey("js_code") && contextjson.getString("js_code").length() > 0) {
                    jsCode = contextjson.getString("js_code");
                    open_id = ConnectWeChat.GetOpenId(jsCode);
                }

                String sql =
                        "select decode(pwd,'" + RIUtil.enNum + "') as pwd,*,id from user where (police_id='" + username + "' or" + " " + "tele_long='" + username + "')  and" + " isdelete=1 and status=1";

                List<JSONObject> list = infomodel.query(sql);


                if (list.size() != 1) {
                    logger.warn("200005");
                    writeLog(request.getRemoteAddr(), "no promission");
                    return ErrNo.set(200005);
                } else {
                    ModelSet.set(user, list.get(0));
                    if (password.length() > 0) {//网页端密码登录
                        if (!user.getPwd().equals(password)) {
                            logger.warn("200005");
                            writeLog(request.getRemoteAddr(), "no promission");
                            return ErrNo.set(200005);
                        }
                    }
                    //     logger.warn(user_id);
                    if (user_id.length() > 0) {
                        if (!user_id.equals(user.getId())) {

                            logger.warn("200005");
                            writeLog(request.getRemoteAddr(), "no promission");
                            return ErrNo.set(200005);
                        }
                    }
                    if (open_id.length() > 0) {
                        String opid = list.get(0).getString("open_id");
                        if (opid.length() > 0 && open_id.length() > 0) {
                            if (!open_id.equals(opid)) {
                                logger.warn("200013");
                                writeLog(request.getRemoteAddr(), "no promission");
                                return ErrNo.set(200013);
                            }
                        }
                    }

                    int startus = list.get(0).getInteger("status");
                    if (startus == 3) {
                        logger.warn("200012");
                        writeLog(request.getRemoteAddr(), "no promission");
                        return ErrNo.set(200012);
                    }
                }

                AuthorizationApi tokenserv = new AuthorizationApi();
                return doLogin(user, request, infomodel, tokenserv, response, list.get(0), token);
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            writeLog(request.getRemoteAddr(), "no promission");
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(infomodel);
        }
    }

    private JSONObject doLogin(User user, TNOAHttpRequest request, InfoModelHelper infomodel,
                               AuthorizationApi tokenserv, HttpServletResponse response, JSONObject uu, String token) throws Exception {
        boolean needUpdate = false;
        String sql = "";

        UserLog userlog = new UserLog();
        userlog.log(infomodel, uu.getString("id"), "登录", userlog.TYPE_OPERATE, request.getRemoteAddr());

        //tokenserv.setLoginAuthor(request, response, user);

        JSONObject retjson = ErrNo.set(0);
        try {
            //  logger.warn(uu.toString());
            JSONObject datas = new JSONObject();
            datas.put("id", uu.getString("id"));
            datas.put("name", uu.get("name"));
            datas.put("police_id", uu.getString("police_id"));
            datas.put("id_num", uu.getString("id_num"));
            datas.put("unit", uu.getString("unit"));
            String unit = uu.getString("unit");

            datas.put("unit_name", RIUtil.dicts.get(unit));
            String position = uu.getString("position");
            if (position != null || position.length() > 0) {
                datas.put("position_name", RIUtil.RealDictNameList(RIUtil.StringToList(position)));
            } else {
                datas.put("position_name", new JSONArray());
            }
            datas.put("position", position);
            datas.put("tele_long", uu.getString("tele_long"));
            datas.put("tele_sort", uu.getString("tele_sort"));
            datas.put("tele_home", uu.getString("tele_home"));
            datas.put("address_home", uu.getString("address_home"));
            datas.put("status", uu.getString("status"));
            datas.put("role", uu.getString("role"));
            datas.put("org", uu.getString("org"));
            String org = uu.getString("org");
            datas.put("isMain", uu.getString("isMain"));

            datas.put("org_name", RIUtil.dicts.get(org));
            datas.put("user_token", token);
            datas.put("img", uu.getString("img"));
            datas.put("permission", uu.getString("permission"));
            datas.put("isFamily", uu.getString("isFamily"));
            //考核集中配置
            try {
                String s = "select id,type,name,value from kh_control";
                List<JSONObject> list = infomodel.query(s);
                datas.put("kh_control", list);
            } catch (Exception e) {
                datas.put("kh_control", new ArrayList<>());
            }


            retjson.put("data", datas);

            //retjson.put("location", TNOAConf.get("basic", "location"));

            sql = "delete from login_code where tele='" + uu.getString("tele_long").trim() + "'";
            infomodel.update(sql);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }
        return retjson;
    }

    public JSONObject doLoginuni(User user, TNOAHttpRequest request, InfoModelHelper infomodel,
                                 AuthorizationApi tokenserv, HttpServletResponse response, JSONObject uu,
                                 String token, JSONObject organization, JSONArray orgs, JSONArray resid,
                                 JSONArray roleid, JSONObject fzry) throws Exception {
        boolean needUpdate = false;
        String sql = "";

        UserLog userlog = new UserLog();
        userlog.log(infomodel, uu.getString("id"), "登录", userlog.TYPE_OPERATE, request.getRemoteAddr());

        //  tokenserv.setLoginAuthorUni(request, response, user, token);

        JSONObject retjson = ErrNo.set(0);
        try {
            //  logger.warn(uu.toString());
            JSONObject datas = new JSONObject();
            datas.put("id", uu.getString("id"));
            datas.put("name", uu.get("name"));
            datas.put("police_id", uu.getString("police_id"));
            datas.put("id_num", uu.getString("id_num"));
            datas.put("unit", uu.getString("unit"));

            String org_id = organization.getString("organization_id");

            datas.put("unit_name", dicts.get(org_id));

            String position = uu.getString("position");
            if (position != null || position.length() > 0) {
                datas.put("position_name", RIUtil.RealDictNameList(RIUtil.StringToList(position)));
            } else {
                datas.put("position_name", new JSONArray());
            }
            datas.put("position", position);
            datas.put("tele_long", uu.getString("tele_long"));
            datas.put("tele_sort", uu.getString("tele_sort"));
            datas.put("tele_home", uu.getString("tele_home"));
            datas.put("address_home", uu.getString("address_home"));
            datas.put("status", uu.getString("status"));
            datas.put("role", uu.getString("role"));
            datas.put("org", uu.getString("org"));
            String org = uu.getString("org");
            datas.put("isMain", uu.getString("isMain"));

            datas.put("org_name", RIUtil.dicts.get(org));
            datas.put("user_token", token);
            datas.put("img", uu.getString("img"));
            datas.put("permission", uu.getString("permission"));
            datas.put("isFamily", uu.getString("isFamily"));
            datas.put("organization", organization);
            datas.put("orgs", orgs);
            datas.put("resources_id", resid);
            datas.put("role_id", roleid);
            datas.put("fzry_info", fzry);
            try {
                String unit = organization.getString("organization_id");
                String job_name = organization.getString("job_name");
                datas.put("qqb_id", GetQQBUserId(unit, uu.getString("id_num"), job_name));
            } catch (Exception ex) {
                datas.put("qqb_id", "");
            }

            // logger.warn("role_id--442->" + roleid);

            //考核集中配置
            try {
                String s = "select id,type,name,value from kh_control";
                List<JSONObject> list = infomodel.query(s);
                datas.put("kh_control", list);
            } catch (Exception e) {
                datas.put("kh_control", new ArrayList<>());
            }

            try {

                JSONObject orgJS = RIUtil.dicts.get(org_id);
                String type = orgJS.getString("type");
                String faid = orgJS.getString("father_id");

                if (type.equals("21") || type.equals("22")) {
                    datas.put("rank", 1);
                    datas.put("score", 100);
                    datas.put("scoreLevel", 1);
                } else if (type.equals("23") || type.equals("25") || type.equals("26"))//分局
                {
                    sql = "select score,rank_level,rank from kh_res where pg_object='" + org_id + "' order by month " + "desc,score desc limit 1";
                    logger.warn(sql);
                    List<JSONObject> ss = infomodel.query(sql);
                    if (ss.size() > 0) {
                        JSONObject sone = ss.get(0);
                        sone.put("scoreLevel", sone.getString("rank_level"));
                        datas.putAll(sone);
                    } else {
                        datas.put("rank", 1);
                        datas.put("score", 100);
                        datas.put("scoreLevel", 1);
                    }
                } else if (type.equals("24")) {

                    sql = "select score,rank_level,rank from kh_res where pg_object='" + faid + "' order by month " + "desc,score desc limit 1";
                    logger.warn(sql);
                    List<JSONObject> ss = infomodel.query(sql);
                    if (ss.size() > 0) {
                        JSONObject sone = ss.get(0);
                        sone.put("scoreLevel", sone.getString("rank_level"));
                        datas.putAll(sone);
                    } else {
                        datas.put("rank", 1);
                        datas.put("score", 100);
                        datas.put("scoreLevel", 1);
                    }
                } else if (type.equals("28")) {
                    faid = faid.substring(0, 6) + "000000";
                    sql = "select score,rank_level,rank from kh_res where pg_object='" + faid + "' order by month " + "desc,score desc limit 1";
                    logger.warn(sql);
                    List<JSONObject> ss = infomodel.query(sql);
                    if (ss.size() > 0) {
                        JSONObject sone = ss.get(0);
                        sone.put("scoreLevel", sone.getString("rank_level"));
                        datas.putAll(sone);
                    } else {
                        datas.put("rank", 1);
                        datas.put("score", 100);
                        datas.put("scoreLevel", 1);
                    }
                } else {
                    datas.put("rank", 1);
                    datas.put("score", 100);
                    datas.put("scoreLevel", 1);
                }


            } catch (Exception ex) {


                datas.put("rank", 1);
                datas.put("score", 100);
                datas.put("scoreLevel", 1);
            }

            retjson.put("data", datas);


        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }

        return retjson;
    }

    private String GetQQBUserId(String unit, String user_id, String job_name) {
        MysqlHelper qqb_user = null;
        String exeid = "";
        try {
            qqb_user = new MysqlHelper("mysql_qqb_user");

            JSONObject done = RIUtil.dicts.get(unit);
            int type = done.getInteger("type");
            int t = 0;
            if (type == 21 || type == 22 || type == 27) {
                t = 1;
            } else if (type == 23 || type == 24 || type == 28) {
                t = 2;
            } else if (type == 25 && (job_name.contains("所长") || job_name.contains("教导员"))) {
                t = 3;
            } else {
                t = 13;
            }

            String sql = "select id from auth.SYS_AUTH_USER where idcard_no='" + user_id + "' and deleted=0 and " +
                    "(IS_TEMPORARY  is null or IS_TEMPORARY <>1)" +

                    " and " + "type=" + t;
            logger.warn(sql);
            exeid = qqb_user.query_one(sql, "ID");

        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
        } finally {
            qqb_user.close();


        }
        return exeid;
    }

    @RequestMapping(method = {RequestMethod.POST}, path = {"/logout"})
    @PassToken
    public JSONObject logout(TNOAHttpRequest request) throws Exception {
        String token = request.getHeader("token");
       /* //检查有没有需要用户权限的注解
        AuthorizationApi author = new AuthorizationApi();
        if (token != null) {
           // String userId = JWT.decode(token).getAudience().get(0);
           // author.delAuthor(userId);

            UserLog userlog = new UserLog();
            userlog.log(request.openInfoImpl(), userId, "注销", userlog.TYPE_OPERATE, request.getRemoteAddr());*/
        //}
        return ErrNo.set(0);
    }


    @RequestMapping(method = {RequestMethod.POST}, path = {"/xzj_token"})
    //@PassToken
    public String token2xjz(TNOAHttpRequest request) throws Exception {
        String token = request.getHeader("token");

        try {
            String url = TNOAConf.get("Httpd", "Token2Xjz");
            logger.warn(url);
            JSONObject data = new JSONObject();
            data.put("token", token);
            data.put("type", "0");

            logger.warn(url + "->" + data);
            String back = HttpConnection.post(url, data);
            return back;

        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return "";
        }
    }


    @RequestMapping(method = {RequestMethod.POST}, path = {"/checkRedirect"})

    public JSONObject CheckRedirect(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();

        String token = "Bearer " + data.getString("token");
        String url = TNOAConf.get("Httpd", "CheckRedirect");
        logger.warn(url);
        JSONObject one = new JSONObject();
        one.put("clientId", TNOAConf.get("Httpd", "clientId"));
        logger.warn(url + "->" + one);
        String back = HttpConnection.post_head(url, token, one, "Authorization");
        JSONObject ret = JSONObject.parseObject(back);
        System.out.println(ret);

        return ret;
    }

    @RequestMapping(method = {RequestMethod.POST}, path = {"/token2xjz"})

    public JSONObject Token2Xjz(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();

        String token = "Bearer " + data.getString("token");
        String url = TNOAConf.get("Httpd", "Token2Xjz");
        logger.warn(url);

        logger.warn(url + "->" + data);
        String back = HttpConnection.post(url, data);
        logger.warn(back);
        JSONObject bk = JSONObject.parseObject(back);
        if (bk.getInteger("status") == 200) {
            JSONObject da = bk.getJSONObject("data");


            JSONObject ret = ErrNo.set(0);
            ret.put("data", da.getString("results"));
            return ret;
        } else {
            return ErrNo.set(null, 2, back);
        }

    }

    @RequestMapping(method = {RequestMethod.POST}, path = {"/code"})
    @PassToken
    public JSONObject getCode(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        logger.warn(data.toString());
        if (data.containsKey("tele") && data.getString("tele").length() == 11) {
            String tele = data.getString("tele");
            InfoModelHelper mysql = request.openInfoImpl();
            String sql = "select position from user where tele_long='" + tele + "'";
            String position = mysql.query_one(sql, "position");
            String per = "";
            try {
                per = RIUtil.dicts.get(position).getString("permission");
            } catch (Exception ex) {

            }
            if (per.contains("M") || per.contains("J")) {
                sql = "select time from login_code where tele='" + tele + "'";
                String time = mysql.query_one(sql, "time");
                if (time.equals("")) {
                    time = "0";
                }
                long t = Long.parseLong(time);
                if (System.currentTimeMillis() - t > 1 * 60 * 1000) {
                    sql = "delete from login_code where tele='" + tele + "'";
                    mysql.update(sql);
                    Random random = new Random();

                    String code = String.valueOf(random.nextInt(1000000));
                    sql = "insert login_code (code,time,tele) values('" + code + "','" + System.currentTimeMillis() + "','" + tele + "')";
                    mysql.update(sql);
                    logger.warn(code);
                    SenSMS send = new SenSMS();
                    String msg = "您正在登录系统，验证码：" + code + "，该验证码5分钟内有效。";
                    send.sendSMS(msg, tele);

                    return ErrNo.set(0);
                } else {
                    writeLog(request.getRemoteAddr(), tele + " get codes frequently");
                    return ErrNo.set(200018);
                }


            } else {
                writeLog(request.getRemoteAddr(), tele + " no access");
                return ErrNo.set(200017);
            }

        } else {
            writeLog(request.getRemoteAddr(), "error tele");
            return ErrNo.set(200016);
        }

    }

    @RequestMapping(method = {RequestMethod.POST}, path = {"/code_wx"})
    @PassToken
    public JSONObject getwxCode(TNOAHttpRequest request, HttpServletResponse response) throws Exception {
        JSONObject data = request.getRequestParams();
        InfoModelHelper mysql = request.openInfoImpl();
        logger.warn("code_wx-->" + data.toString());
        String code = "";
        String jsCode = "";
        String open_id = "";
        String tele = "";
        if (data.containsKey("code") && data.getString("code").length() > 10) {
            code = data.getString("code");

        } else {

            return ErrNo.set(200016);
        }
        if ((data.containsKey("js_code") && data.getString("js_code").length() > 0)) {
            jsCode = data.getString("js_code");
            if (jsCode.equals("20190216")) {
                open_id = "ouxga5ZBH52yW-1Ww5OOIHr1YyoM";
            } else {
                open_id = ConnectWeChat.GetOpenId(jsCode);
            }
            //union_id = ConnectWeChat.GetUnionId(open_id);
            //logger.warn("微信统一id-->" + union_id);


            String sql = "select * from user where open_id='" + open_id + "' and isdelete=1";
            List<JSONObject> list = mysql.query(sql);
            if (list.size() != 1) {
                logger.warn("200014");
                writeLog(request.getRemoteAddr(), "no promission");
                return ErrNo.set(200014);
            } else {

                int startus = list.get(0).getInteger("status");
                if (startus == 3) {
                    logger.warn("200012");
                    writeLog(request.getRemoteAddr(), "no promission");
                    return ErrNo.set(200012);
                }
            }
            tele = list.get(0).getString("tele_long");
            String id = list.get(0).getString("id");

            sql = "insert login_code(tele,`code`,time) values ('" + tele + "','" + code + "','" + System.currentTimeMillis() + "');";
            logger.warn(sql);
            mysql.update(sql);
            sql = "update user set open_id_ys='" + code + "' where id='" + id + "'";
            logger.warn(sql);
            mysql.update(sql);

            User user = new User();
            ModelSet.set(user, list.get(0));

            JSONObject uu = list.get(0);
            AuthorizationApi tokenserv = new AuthorizationApi();
            return doLogin(user, request, mysql, tokenserv, response, uu, "");

        } else {
            writeLog(request.getRemoteAddr(), "no promission");
            return ErrNo.set(200016);
        }


    }


    @RequestMapping(method = {RequestMethod.POST}, path = {"/login_token"})
    @PassToken
    public JSONObject tokenLogin(TNOAHttpRequest request, HttpServletResponse response) throws Exception {
        JSONObject data = request.getRequestParams();
        logger.warn(data.toString());
        InfoModelHelper infomodel = null;

        JSONObject uu = new JSONObject();
        User user = new User();
        try {
            infomodel = request.openInfoImpl();
            String token = data.getString("token");

//            logger.warn(token);

            if (token == null || token.length() == 0) {
                logger.error(data.toString());

                return ErrNo.set(200008);
            } else {
                String unUrl = TNOAConf.get("HttpServ", "uni_url") + "/token";

                String back = HttpConnection.post_token(unUrl, token, new JSONObject(), data.getString("X-Real-IP"));


//                logger.warn(back);

                JSONObject ret = JSONObject.parseObject(back);
                if (ret.containsKey("errno") && (ret.getInteger("errno") == 0 || ret.getInteger("errno") == 200)) {
                    JSONObject dd = ret.getJSONObject("data");
                    String police_id = dd.getString("police_id");
                    String id_card = dd.getString("id_card");
                    JSONArray orgs = new JSONArray();
                    try {
                        orgs = dd.getJSONArray("all_organization");
                    } catch (Exception ex) {
                    }
                    JSONArray resid = dd.getJSONArray("resources_id");
                    JSONArray roleid = dd.getJSONArray("role_id");
                    JSONObject org = dd.getJSONArray("organization").getJSONObject(0);
                    JSONObject fzry = dd.getJSONObject("fzry_info");


                    String sql = "select * from user where (police_id='" + police_id + "' or id_num='" + id_card +
                            "') " + "order by id desc limit 1";
                    logger.warn(sql);
                    List<JSONObject> list = infomodel.query(sql);
                    if (list.size() == 0) {
                        sql = "select * from user where id='99887766'";
                        list = infomodel.query(sql);
                        logger.warn(list.toString());
                        uu = list.get(0);
                        uu.put("police_id", police_id);
                        uu.put("name", dd.getString("name"));

                        uu.put("id_num", id_card);
                        uu.put("unit", dd.getString("organization_id"));
                    } else {

                        int startus = list.get(0).getInteger("status");
                        if (startus == 3) {
                            logger.warn("200012");
                            return ErrNo.set(200012);
                        }
                        uu = list.get(0);
                    }

                    ModelSet.set(user, list.get(0));


                    AuthorizationApi tokenserv = new AuthorizationApi();
                    return doLoginuni(user, request, infomodel, tokenserv, response, uu, token, org, orgs, resid,
                            roleid, fzry);
                } else {
                    logger.error(String.valueOf(ret));
                    return ret;
                }
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(200014);
        } finally {
            InfoModelPool.putModel(infomodel);
        }

        //10.105:11010


    }

    @RequestMapping(method = {RequestMethod.POST}, path = {"/token"})
    public JSONObject get_new_token(TNOAHttpRequest request, HttpServletResponse response) throws Exception {
        User user = request.getUser();
        AuthorizationApi auth = new AuthorizationApi();
        // auth.setRefeshTokenAuthor(request, response, user);
        try {
            String token = request.getHeader("token");
            String unUrl = TNOAConf.get("HttpServ", "uni_url") + "/flush";
            logger.warn(request.getRequestParams().getString("X-Real-IP"));

            String back = HttpConnection.post_token(unUrl, token, new JSONObject(),
                    request.getRequestParams().getString("X-Real-IP"));
            //  logger.warn(back);
            JSONObject ret = JSONObject.parseObject(back);
            return ret;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(200099);
        }


    }

    @RequestMapping(method = {RequestMethod.POST}, path = {"/check_token"})
    @PassToken
    public JSONObject check_token(TNOAHttpRequest request, HttpServletResponse response) throws Exception {
        User user = request.getUser();
        AuthorizationApi auth = new AuthorizationApi();
        //    auth.setRefeshTokenAuthor(request, response, user);
        try {
            String token = request.getHeader("token");
            String unUrl = TNOAConf.get("HttpServ", "uni_url") + "/token";
            logger.warn(request.getRequestParams().getString("X-Real-IP"));

            String back = HttpConnection.post_token(unUrl, token, new JSONObject(),
                    request.getRequestParams().getString("X-Real-IP"));

            //    logger.warn(back);
            JSONObject ret = JSONObject.parseObject(back);
            return ret;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(200099);
        }


    }


    public static String getMD5Str(String str) {
        byte[] digest = null;
        try {
            MessageDigest md5 = MessageDigest.getInstance("md5");
            digest = md5.digest(str.getBytes("utf-8"));
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        //16是表示转换为16进制数
        String md5Str = new BigInteger(1, digest).toString(16);
        return md5Str;
    }

    private boolean CheckExpires(String createT) {
        try {
            int expiresTime = TNOAConf.getInt("basic", "expires") + 1;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            int diff = differentDays(new Date(), sdf.parse(createT));
            if (diff < expiresTime) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return false;
        }

    }

    public static int differentDays(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if (year1 != year2) //同一年
        {
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0) //闰年
                {
                    timeDistance += 366;
                } else //不是闰年
                {
                    timeDistance += 365;
                }
            }

            return timeDistance + (day2 - day1);
        } else //不同年
        {
            //System.out.println("判断day2 - day1 : " + (day2 - day1));
            return day2 - day1;
        }
    }

    private void writeLog(String s, String logs) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sdf.format(new Date());
        String file = "/var/log/hl_" + date + ".log";
        BufferedWriter bw = null;
        try {
            bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8));
            bw.write("[" + sd.format(new Date()) + "]Failed SOA_https from " + s + " " + logs + "\n");

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            if (bw != null) {
                try {
                    bw.flush();
                    bw.close();
                } catch (Exception ex) {
                    logger.error(Lib.getTrace(ex));
                }
            }

        }

    }

}
