package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.Models.UserLog;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.OracleHelper;
import HL.TNOA.Lib.RIUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@RestController
public class ExamineDynamicsController {
    private static Logger logger = LoggerFactory.getLogger(ExamineDynamicsController.class);

    @RequestMapping(method = {RequestMethod.POST}, path = {"/examine_dynamics"})
    public JSONObject get_examine(TNOAHttpRequest request) throws Exception {
        JSONObject data = request.getRequestParams();
        logger.warn("data---->" + data);
        String opt = "";

        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("create_score_det")) {
                return createDynamicsDet(data,request.getRemoteAddr());
            } else if (opt.equals("get_dynamics_det")) {
                return getDynamicsDet(data, request.getRemoteAddr());
            } else if (opt.equals("update_config_jjl")) {
                return updateJJL(data, request.getRemoteAddr());
            } else {
                return ErrNo.set(490001);
            }
        } else {
            return ErrNo.set(490001);
        }


    }



    private JSONObject getDynamicsDet(JSONObject data,String remoteAddr) {
        InfoModelHelper mysql = null;
        OracleHelper oracle = null;
        JSONObject back = ErrNo.set(0);


        try {
            mysql = InfoModelPool.getModel();
            String time = "";
            String label = "";
            String org = "";
            int limit = 20;
            int page = 1;

            if (data.containsKey("label") && data.getString("label").length() > 0) {
                label = data.getString("label");
            } else {
                return ErrNo.set(490031);
            }

            if (data.containsKey("time") && data.getString("time").length() > 0) {
                time = data.getString("time");
            } else {
                return ErrNo.set(490031);
            }

            if (data.containsKey("org") && data.getString("org").length() > 0) {
                org = data.getString("org");
            } else {
                return ErrNo.set(490031);
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }

            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            String nextMonth = getNextMonth(time);

            String paging = "  OFFSET " + (page - 1) * limit + " " + "ROWS FETCH NEXT" + " " + limit + " " + "ROWS " +
                    "ONLY";


            List<JSONObject> list = new ArrayList<>();
            try {
                JSONObject obj = RIUtil.kh_configs.get(label);
                logger.warn(obj.toString());
                JSONObject realDb = obj.getJSONObject("real_db");
                String name = realDb.getString("name");
                String type = realDb.getString("type");

                if ("oracle".equals(type)) {
                    oracle = new OracleHelper(name);

                    if (obj.getString("real_table").length() > 0 || obj.getString("real_field").length() > 0) {
                        String realTable = obj.getString("real_table");
                        String realSql = obj.getString("real_sql");
                        String remark = obj.getString("remark");
                        JSONArray realField = obj.getJSONArray("real_field");

                        if (realSql.contains("$time")) {
                            realSql = realSql.replace("$time", time);
                        }
                        if (realSql.contains("$org")) {
                            realSql = realSql.replace("$org", org);
                        }
                        if (realSql.contains("$nextTime")) {
                            realSql = realSql.replace("$nextTime", nextMonth);
                        }
                        String sql = "select * from " + realTable + " " + realSql + paging;
                        logger.warn(sql);
                        list = oracle.query(sql);

                        back.put("data", list);
                        sql = "select * from " + realTable + " " + realSql;
                        list = oracle.query(sql);
                        back.put("count", list.size());
                        back.put("head", realField);
                        back.put("remark", remark);

                    }
                } else if ("mysql".equals(type)) {
                    if (obj.getString("real_table").length() > 0 || obj.getString("real_field").length() > 0) {
                        String realTable = obj.getString("real_table");
                        String realSql = obj.getString("real_sql");
                        String remark = obj.getString("remark");
//                        logger.warn("测试--->1");
                        JSONArray realField = obj.getJSONArray("real_field");
//                        String realField = obj.getString("real_field");

                        if (realSql.contains("$time")) {
                            realSql = realSql.replace("$time", time);
                        }
                        if (realSql.contains("$org")) {
                            realSql = realSql.replace("$org", org);
                        }
                        if (realSql.contains("$nextTime")) {
                            realSql = realSql.replace("$nextTime", nextMonth);
                        }


//                        logger.warn("测试--->2");

                        String mysqlPage = "limit " + limit + " offset " + limit * (page - 1);

                        String sql = "select * from " + realTable + " " + realSql + mysqlPage;
                        logger.warn(sql);
                        list = mysql.query(sql);

                        back.put("data", list);
                        sql = "select * from " + realTable + " " + realSql;
                        list = mysql.query(sql);
                        back.put("count", list.size());
                        back.put("head", realField);
                        back.put("remark", remark);
                    }
                }

                // 添加日志
                String opt_user = "";
                if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                    opt_user = data.getString("opt_user");
                }
                UserLog userlog = new UserLog();
                userlog.log(mysql, opt_user, "考核-getDynamicsDet", userlog.TYPE_OPERATE, remoteAddr);

            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
                back.put("data", list);
                back.put("count", 0);
            }

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
            if (oracle != null) {
                oracle.close();
            }
        }
        return back;
    }

    private JSONObject createDynamicsDet(JSONObject data,String remoteAddr) {
        InfoModelHelper mysql = null;

        JSONObject back = ErrNo.set(0);
        String time = "";
        String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

        try {
            mysql = InfoModelPool.getModel();
            List<JSONObject> configs_zrq = new ArrayList<>();
            List<JSONObject> configs_pcs = new ArrayList<>();
            List<JSONObject> configs_fj = new ArrayList<>();
            List<JSONObject> zrqs = null;
            List<JSONObject> pcss = null;
            List<JSONObject> fjs = null;

            String s = "select type,name,value from kh_control where type = 'kh_total'";
            List<JSONObject> control = mysql.query(s);

            String pid = "";

            for (int i = 0; i < control.size(); i++) {
                JSONObject c = control.get(i);
                String name = c.getString("name");
                String value = c.getString("value");
                if ("total_plan".equals(name)) {
                    pid = value;
                }
                if ("total_month".equals(name)) {
                    time = value;
                }
            }

            s = "select * from kh_plan where id = '" + pid + "'";
            List<JSONObject> list = mysql.query(s);

            JSONObject plan = list.get(0);

            String fjConfig = plan.getString("fj_config");
            String[] split = fjConfig.split(",");
            String c1 = "";
            for (int i = 0; i < split.length; i++) {
                c1 = c1 + "'" + split[i] + "',";
            }
            if (c1.length() > 0) {
                s = "select * from kh_config where id in (" + c1.substring(0, c1.length() - 1) + ") and static_type =" +
                        " '1'";
                configs_fj = mysql.query(s);
            }

            String pcsConfig = plan.getString("pcs_config");
            String[] split2 = pcsConfig.split(",");
            String c2 = "";
            for (int i = 0; i < split2.length; i++) {
                c2 = c2 + "'" + split2[i] + "',";
            }
            if (c2.length() > 0) {
                s = "select * from kh_config where id in (" + c2.substring(0, c2.length() - 1) + ") and static_type =" +
                        " '1'";
                configs_pcs = mysql.query(s);
            }

            String zrqConfig = plan.getString("zrq_config");
            String[] split3 = zrqConfig.split(",");
            String c3 = "";
            for (int i = 0; i < split3.length; i++) {
                c3 = c3 + "'" + split3[i] + "',";
            }
            if (c3.length() > 0) {
                s = "select * from kh_config where id in (" + c3.substring(0, c3.length() - 1) + ") and static_type =" +
                        " '1'";
                configs_zrq = mysql.query(s);
            }

            String sql = "";
            //获取所有的责任区
            sql = "select * from dict where type = 26 and isdelete='1' and is_kh = '1'";
            zrqs = mysql.query(sql);

            //获取所有的派出所
            sql = "select * from dict where type = 25 and isdelete='1' and is_kh = '1'";
            pcss = mysql.query(sql);

            //获取所有的分局
            sql = "select * from dict where type = 23 and isdelete='1' and is_kh = '1'";
            fjs = mysql.query(sql);

            for (int i = 0; i < configs_zrq.size(); i++) {
                JSONObject conf = configs_fj.get(i);
                String kh_name = conf.getString("kh_name");
                if (kh_name.equals("实有人口电话采集率")) {

                }

            }

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-createDynamicsDet", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    private JSONObject updateJJL(JSONObject data,String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);

        try {
            mysql = InfoModelPool.getModel();
            String id = "";
            String status = "";
            if (data.containsKey("id") && data.getString("id").length()>0) {
                id = data.getString("id");
            }
            if (data.containsKey("status") && data.getString("status").length()>0) {
                status = data.getString("status");
            }

            String sql = "update kh_config set static_status = '"+status+"' where id = '"+id+"'";
            logger.warn(sql);
            mysql.update(sql);

            // 添加日志
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            UserLog userlog = new UserLog();
            userlog.log(mysql, opt_user, "考核-updateJJL", userlog.TYPE_OPERATE, remoteAddr);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(490006);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    public static String getNextMonth(String time) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        try {
            time = time.substring(0, 4) + "-" + time.substring(4, 6);
            Date date = format.parse(time);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date); // 设置为当前时间
            calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + 1); // 设置为下一个月
            date = calendar.getTime();
            return format.format(date) + "-01 00:00:00";
        } catch (Exception e) {
            return "";
        }
    }

}
