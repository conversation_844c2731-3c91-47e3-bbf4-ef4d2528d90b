package HL.TNOA.Httpd.Controller;

import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.wechat.HttpConnection;
import HL.TNOA.wechat.wechatMsgTemp;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class CaseController {
    private static Logger logger = LoggerFactory.getLogger(CaseController.class);

    @RequestMapping(method = {RequestMethod.POST}, path = {"/case"})
    public static JSONObject get_case(TNOAHttpRequest request) throws Exception {


        String opt = "";
        JSONObject data = request.getRequestParams();
        String ip = request.getRemoteAddr();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if ("get_case".equals(opt)) {
                logger.warn("case--->" + data.toString());
                return getCase(data);
            } else if ("get_case_detail".equals(opt)) {
                logger.warn("case--->" + data.toString());
                return getCaseDetail(data);
            } else if ("create_case".equals(opt)) {
                logger.warn("case--->" + data.toString());
                return createCase(data, ip);
            } else if ("update_case".equals(opt)) {
                logger.warn("case--->" + data.toString());
                return updateCase(data, ip);
            } else if ("delete_case".equals(opt)) {
                logger.warn("case--->" + data.toString());
                return deleteCase(data, ip);
            } else if ("get_case_static".equals(opt)) {
                return getStatic(data);
            } else if ("get_case_object".equals(opt)) {
                return CaseObjectController.getCaseObject(data);
            } else if ("create_case_object".equals(opt)) {
                logger.warn("case--->" + data.toString());
                return CaseObjectController.createCaseObject(data, ip);
            } else if ("update_case_object".equals(opt)) {
                logger.warn("case--->" + data.toString());
                return CaseObjectController.updateCaseObject(data, ip);
            } else if ("delete_case_object".equals(opt)) {
                logger.warn("case--->" + data.toString());
                return CaseObjectController.deleteCaseObject(data, ip);
            } else if ("update_case_step".equals(opt)) {
                logger.warn("case--->" + data.toString());
                return CaseStepController.updateCaseStep(data);
            } else if ("back_case_step".equals(opt)) {
                logger.warn("case--->" + data.toString());
                return CaseStepController.backeCaseStep(data);
            } else if ("get_case_step".equals(opt)) {
                return CaseStepController.getCaseStep(data);
            } else if ("get_case_list".equals(opt)) {
                return CaseListController.getCaseList(data);
            } else if ("create_case_list".equals(opt)) {
                logger.warn("case--->" + data.toString());
                return CaseListController.createCaseList(data, ip);
            } else if ("update_case_list".equals(opt)) {
                logger.warn("case--->" + data.toString());
                return CaseListController.updateCaseList(data, ip);
            } else if ("delete_case_list".equals(opt)) {
                logger.warn("case--->" + data.toString());
                return CaseListController.deleteCaseList(data, ip);
            } else if ("get_case_group".equals(opt)) {
                return GetCaseGroup(data, ip);
            } else if ("get_case_url".equals(opt)) {
                //return GetCaseByUrl(data, ip);
                return null;
            } else if ("get_caseObject_url".equals(opt)) {
                //return CaseObjectController.getCaseObjectByUrl(data, ip);
                return null;
            } else {
                return ErrNo.set(448009);
            }
        } else {
            return ErrNo.set(448009);
        }
    }

    private static JSONObject GetCaseByUrl(JSONObject data, String ip) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String pageSize = "10";
            String pageNum = "1";
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMddHHmmss");
            SimpleDateFormat d = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String sql = "";
            String ZBDW = "";
            String DJSJ = "";
            String max = "";
            if (data.containsKey("DJSJ") && data.getString("DJSJ").length() > 0) {
                DJSJ = data.getString("DJSJ");
            }
            if (data.containsKey("ZBDW") && data.getString("ZBDW").length() > 0) {
                ZBDW = data.getString("ZBDW");
            }
            if (data.containsKey("Max_time") && data.getString("Max_time").length() > 0) {
                max = data.getString("Max_time");
            }
            if (data.containsKey("pageSize") && data.getString("pageSize").length() > 0) {
                pageSize = data.getString("pageSize");
            }
            if (data.containsKey("pageNum") && data.getString("pageNum").length() > 0) {
                pageNum = data.getString("pageNum");
            }
            String condition = "ZBDW like '%" + ZBDW + "%' and DJSJ>='" + DJSJ + "' and DJSJ<='" + max + "'";
            logger.warn(condition);
            sql = "select id from dict where gadm like '%" + ZBDW + "%'";
            String s = mysql.query_one(sql, "id");
            int page = 1;
            while (true) {
                JSONObject one = new JSONObject();
                JSONObject two = new JSONObject();
                JSONObject three = new JSONObject();
                one.put("fwbh", "S-320400230000-0100-00307");
                one.put("lx", "02");
                one.put("ds", "3204");
                one.put("type", "0");
                two.put("condition", condition);
                two.put("requiredItems", "AJBH,DJSJ,ZBDRXM,AJMC,AJFAB,AJLB,AJZT,ZBDW,AJDL");
                two.put("pageSize", pageSize);
                two.put("pageNum", String.valueOf(page));
                one.put("required", two);
                three.put("dyrdwbm", "320402580000");
                three.put("dyrdwmc", "翠竹派出所");
                three.put("dyrip", "***********");
                three.put("dyyy", "智慧派出所打处功能案件调用");
                three.put("dyrlxfs", "13951212059");
                three.put("dyrxm", "易鑫");
                three.put("dyrgmsfhm", "362201199505150613");
                one.put("realInfo", three);
                logger.warn(one.toString());
                String post = HttpConnection.post("http://************:18888/serverInterface/dataserver" +
                        "/getParamAndContent", one);
                JSONObject url = JSONObject.parseObject(post);
                logger.warn(post);
                String total = url.getString("total");
                JSONArray results = url.getJSONArray("results");
                for (int i = 0; i < results.size(); i++) {
                    JSONObject o = results.getJSONObject(i);
                    String type = "1";
                    String ajbh = o.getString("AJBH");
                    String ajmc = o.getString("AJMC");
                    String zbdw = o.getString("ZBDW");
                    String zbrxm = o.getString("ZBRXM");
                    String ajzt = o.getString("AJZT");
                    String ajlb = o.getString("AJLB");
                    String djsj = o.getString("DJSJ");
                    String ajdl = o.getString("AJDL");
                    if (ajdl.contains("行政")) {
                        type = "1";
                    } else if (ajdl.contains("刑事")) {
                        type = "2";
                    } else {
                        type = "3";
                    }
                    Date parse = sdf2.parse(djsj);
                    String format = d.format(parse);
                    sql = "insert into case_info (case_number,case_name,ajzt,create_user,create_time,unit,form,type) "
                            + "values('" + ajbh + "',encode('" + ajmc + "','" + RIUtil.enTitle + "'),'" + ajzt + "'," + "'" + zbrxm + "','" + format + "','" + s + "','" + ajlb + "','" + type + "')";
                    logger.warn(sql);
                    mysql.update(sql);
                }
                Integer integer = Integer.valueOf(total);
                if (integer != 10) {
                    break;
                }
                page++;
                logger.warn(String.valueOf(page));
            }


        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(1);
        }
        return back;
    }

    private static JSONObject GetCaseGroup(JSONObject data, String ip) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        String unit = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
        } else {
            return ErrNo.set(448006);
        }
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select accepter from case_info where isdelete=1 and unit='" + unit + "'  ";
            List<JSONObject> as = mysql.query(sql);
            HashMap<String, String> others = new HashMap<>();
            if (as.size() > 0) {

                for (int i = 0; i < as.size(); i++) {
                    JSONObject one = as.get(i);
                    String accepter = one.getString("accepter");

                    others.putAll(RIUtil.StringToList(accepter));
                }

            }
            sql = "select id,decode(dict_name,'" + RIUtil.enName + "') as dict_name from dict where type=15 and " +
                    "father_id='" + unit + "' and isdelete=1";

            List<JSONObject> list = mysql.query(sql);
            List<JSONObject> datas = new ArrayList<>();
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    String group_id = one.getString("id");

                    sql = "select id from user where community like '%" + group_id + "%' and isdelete=1 and status=1";
                    List<JSONObject> guser = mysql.query(sql);
                    String users = "";

                    if (guser.size() > 0) {
                        JSONArray us = new JSONArray();
                        for (int g = 0; g < guser.size(); g++) {
                            String user_id = guser.get(g).getString("id");

                            users = users + user_id + ",";
                            others.remove(user_id);

                        }
                    }

                    one.put("users", RIUtil.UseridToNames(RIUtil.StringToList(users)));
                    String accs[] = users.split(",");
                    String s = "(";
                    for (int a = 0; a < accs.length; a++) {
                        s = s + "accepter like '%" + accs[a] + "%' or ";
                    }
                    s = s.substring(0, s.length() - 3) + ")";
                    if (s.length() > 22) {
                        sql = "select count(id) as count from case_info where " + s + " and isdelete=1 and unit='" + unit + "'";
                        //logger.warn(sql);
                        int count = mysql.query_count(sql);
                        //logger.warn(String.valueOf(count));
                        one.put("count", count);
                    } else {
                        one.put("count", 0);
                    }

                    datas.add(one);
                }

                //其他

                JSONObject one = new JSONObject();
                one.put("id", "");
                one.put("dict_name", "其他");

                String s = "(";

                for (Map.Entry<String, String> acc : others.entrySet()) {
                    s = s + "accepter like '%" + acc.getKey() + "%' or ";
                }
                s = s.substring(0, s.length() - 3) + ")";
                if (s.length() > 22) {
                    sql = "select count(id) as count from case_info where " + s + " and isdelete=1 and unit='" + unit + "'";
                    //logger.warn(sql);
                    int count = mysql.query_count(sql);
                    // logger.warn(String.valueOf(count));
                    one.put("count", count);
                } else {
                    one.put("count", 0);

                }
                one.put("users", RIUtil.UseridToNames(others));

                datas.add(one);

            }
            back.put("data", datas);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(448006);
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    private static JSONObject getCaseDetail(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String case_id = "";
            if (data.containsKey("case_id") && data.getString("case_id").length() > 0) {
                case_id = data.getString("case_id");

            } else {
                return ErrNo.set(448006);
            }
            String sqls = "select *,decode(case_name,'" + RIUtil.enTitle + "') as case_name from case_info where " +
                    "id='" + case_id + "' and isdelete=1";
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                list = RelaInfo_detail(list, mysql);

            }
            back.put("data", list);
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(448006);
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private static JSONObject getStatic(JSONObject date) {
        int count = 0;
        String start_time = "";
        String end_time = "";
        String start_c_sql = " and 1=1";
        String start_sql = " and 1=1";
        String end_c_sql = " and 1=1";
        String end_sql = " and 1=1";
        String usql = " and 1=1 ";
        String usqlb = " and 1=1 ";
        if (date.containsKey("unit") && date.getString("unit").length() > 0) {
            String unit = date.getString("unit");
            usql = " and unit='" + unit + "' ";
            usqlb = " and b.unit='" + unit + "' ";
        } else {
            return ErrNo.set(448006);
        }

        if (date.containsKey("start_time") && date.getString("start_time").length() > 0) {
            start_time = date.getString("start_time");

            start_sql = " and a.time>='" + start_time + " 00:00:00' ";
            start_c_sql = " and b.create_time>='" + start_time + " 00:00:00' ";
        }
        if (date.containsKey("end_time") && date.getString("end_time").length() > 0) {

            end_time = date.getString("end_time");
            end_sql = " and a.time<='" + end_time + " 00:00:00' ";
            end_c_sql = " and b.create_time<='" + end_time + " 00:00:00' ";
        }
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            JSONArray data = new JSONArray();
            String sql =
                    "select b.id as case_id,'' as obj_id from case_info b where  isdelete= 1" + start_c_sql + end_c_sql + usql;
            //  logger.warn(sql);
            JSONObject o = new JSONObject();
            o.put("index", 1);
            o.put("name", "案件总数");
            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, "case_id"));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "起");
            o.put("home", "1");
            data.add(o);


            sql = "select case_id,obj_id from case_step a left join case_info b on a.case_id=b.id where " + "a" +
                    ".index_no='4'" + " and a.select_id='1'  and a.isdelete= 1" + start_sql + end_sql + usqlb;
            // logger.warn(sql);
            o = new JSONObject();
            o.put("index", 2);
            o.put("name", "拘留");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, ""));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);

            sql = "select sum(amount) as amount from case_step a left join case_info b on a.case_id=b.id where " + "a"
                    + ".isdelete= 1" + start_sql + end_sql + usql;
            String amount = mysql.query_one(sql, "amount");
            double a = 0.0;
            if (amount == null || amount.length() == 0) {
                a = 0;
            } else {
                a = Double.parseDouble(amount);
            }
            o = new JSONObject();
            o.put("index", 3);
            o.put("name", "罚款金额");
            o.put("count", a);
            sql = "select case_id,obj_id from case_step  a left join case_info b on a.case_id=b.id where amount>0 " + "and" + " a.isdelete= 1" + start_sql + end_sql + usqlb;
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, ""));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("unit", "元");
            o.put("home", "0");
            data.add(o);

            sql = "select case_id,obj_id from case_step a left join case_info b on a.case_id=b.id where " + "a" +
                    ".index_no='4'" + " and a.select_id='3'  and a.isdelete= 1" + start_sql + end_sql + usqlb;
            o = new JSONObject();
            o.put("index", 4);
            o.put("name", "警告");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, ""));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);

            sql = "select case_id,obj_id from case_step a left join case_info b on a.case_id=b.id where " + "a" +
                    ".index_no='6'" + " and a.select_id='1'  and a.isdelete= 1" + start_sql + end_sql + usqlb;
            o = new JSONObject();
            o.put("index", 5);
            o.put("name", "社区戒毒");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, ""));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);

            sql = "select case_id ,obj_id from case_step  a left join case_info b on a.case_id=b.id where " + "a" +
                    ".index_no='6' and a.select_id='2'  and a.isdelete= 1" + start_sql + end_sql + usqlb;
            o = new JSONObject();
            o.put("index", 6);
            o.put("name", "强制隔离戒毒");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, ""));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);

            sql = "select a.id from case_object a left join case_info b on a.case_id=b.id where a.isdelete=1" + start_c_sql + end_c_sql + usqlb;
            int xj = 0;
            List<JSONObject> xjList = new ArrayList<>();
            int jj = 0;
            List<JSONObject> jjList = new ArrayList<>();
            int qb = 0;
            List<JSONObject> qbList = new ArrayList<>();
            list = mysql.query(sql);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    String obj_id = list.get(i).getString("id");
                    sql = "select a.select_id,a.index_no,a.case_id,a.obj_id " + "from case_step a left join " +
                            "case_object b on a.obj_id=b.id " + "where a.obj_id='" + obj_id + "'and (a.index_no=9 " + "or" + " a.index_no=17) and a.isdelete=1  and b.isdelete=1" + usqlb + " order by a.id " + "desc ";
                    List<JSONObject> iss = mysql.query(sql);
                    if (iss.size() > 0) {
                        String select = iss.get(0).getString("select_id");
                        String index = iss.get(0).getString("index_no");
                        if ("9".equals(index) && "1".equals(select)) {
                            xj++;
                            xjList.add(iss.get(0));
                        }
                        if ("9".equals(index) && "2".equals(select)) {
                            jj++;
                            jjList.add(iss.get(0));
                        }
                        if ("9".equals(index) && "3".equals(select)) {
                            qb++;
                            qbList.add(iss.get(0));
                        }
                        if ("17".equals(index) && "1".equals(select)) {
                            jj++;
                            jjList.add(iss.get(0));
                        }
                        if ("17".equals(index) && "2".equals(select)) {
                            qb++;
                            qbList.add(iss.get(0));
                        }
                    }
                }
            }
            o = new JSONObject();
            o.put("index", 7);
            o.put("name", "刑拘");
            o.put("detail", RelaInfo_static(xjList, mysql, ""));
            o.put("count", xj);
            o.put("unit", "人");
            o.put("home", "1");
            data.add(o);

            o = new JSONObject();
            o.put("index", 8);
            o.put("name", "监居");
            o.put("detail", RelaInfo_static(jjList, mysql, ""));
            o.put("count", jj);
            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);


            o = new JSONObject();
            o.put("index", 9);
            o.put("name", "取保");
            o.put("detail", RelaInfo_static(qbList, mysql, ""));
            o.put("count", qb);
            o.put("unit", "人");
            o.put("home", "1");
            data.add(o);

            sql = "select a.case_id,a.obj_id from case_step a left join case_info b on a.case_id=b.id where (" + "(a" + ".index_no='13' and a.select_id='1') or  a.index_no='14')" + "  " + "and a.isdelete= 1" + start_sql + end_sql + usqlb;
            o = new JSONObject();
            o.put("index", 10);
            o.put("name", "移诉");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, ""));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);

            sql = "select a.case_id,a.obj_id from case_step a left join case_info b on a.case_id=b.id where " + "a" + ".index_no='18' and a.select_id='1'  and a.isdelete= 1" + start_sql + end_sql + usqlb;
            o = new JSONObject();
            o.put("index", 11);
            o.put("name", "逮捕");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, ""));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());

            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);

            sql = "select a.case_id,a.obj_id from case_step  a left join case_info b on a.case_id=b.id where " + "a" + ".index_no='15' and a.select_id='1'  and a.isdelete= 1" + start_sql + end_sql + usqlb;
            o = new JSONObject();
            o.put("index", 12);
            o.put("name", "公诉");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, ""));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "人");
            o.put("home", "0");

            data.add(o);


            sql = "select count(id) as count from case_info b where  isdelete= 1" + start_c_sql + end_c_sql + usql;
            int count_step = mysql.query_count(sql);
            sql = "select count(id) as count from case_info b where  isdelete= 1 and status=1" + start_c_sql + end_c_sql + usql;
            int count_step_finish = mysql.query_count(sql);
            DecimalFormat df = new DecimalFormat("0.00");//格式化小数
            String num = df.format((float) count_step_finish / count_step * 100);//返回的是String类型
            o = new JSONObject();
            o.put("index", 13);
            o.put("name", "结案率");
            o.put("count", num);
            o.put("unit", "%");
            o.put("detial", new ArrayList<>());
            o.put("home", "1");
            data.add(o);
            sql = "select id,'' as obj_id from case_info b where cycle>0  and isdelete= 1" + start_c_sql + end_c_sql + usql;
            o = new JSONObject();
            o.put("index", 14);
            o.put("name", "待侦案件");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, "case_id"));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "起");
            o.put("home", "0");
            data.add(o);
            sql = "select case_id,id from case_object b where current_index!='99' and current_index!='1' and " +
                    "current_index!='102'" + "and current_index!='802'and current_index!='8' and isdelete= 1" + start_c_sql + end_c_sql + usql;
            o = new JSONObject();
            o.put("index", 15);
            o.put("name", "在侦人数");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, "obj_id"));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);
            sql = "select id,case_id from case_object b where current_index='1' and current_index='8' and isdelete=" + " " + "1" + start_c_sql + end_c_sql + usql;
            o = new JSONObject();
            o.put("index", 16);
            o.put("name", "待抓对象");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, "obj_id"));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("count", mysql.query_count(sql));
            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);
            sql = "select id,case_id from case_object b where current_index='99'  and isdelete= 1" + start_c_sql + end_c_sql + usql;
            o = new JSONObject();
            o.put("index", 17);
            o.put("name", "对象闭环");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, ""));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);
            sql = "select id,case_id from case_object b  where  isdelete= 1" + start_c_sql + end_c_sql + usql;
            o = new JSONObject();
            o.put("index", 18);
            o.put("name", "对象总数");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, ""));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);

            sql = "select case_id,obj_id from case_step a left join case_info b on a.case_id=b.id where " + "a" +
                    ".index_no='4' and a.select_id='2'and  a.isdelete= 1" + start_sql + end_sql + usqlb;
            o = new JSONObject();
            o.put("index", 19);
            o.put("name", "罚款人数");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, ""));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);

            sql = "select a.case_id,a.obj_id from case_step  a left join case_info b on a.case_id=b.id  where (" +
                    "(a.index_no='3' and a.select_id='1') or(a.index_no='9' " + "and a.select_id='4') )" + "and  a" + ".isdelete= " + "1" + start_sql + end_sql + usqlb;
            o = new JSONObject();
            o.put("index", 20);
            o.put("name", "不予处罚");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, ""));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);

            sql = "select a.case_id,a.obj_id from case_step a left join case_info b on a.case_id=b.id  where " + "a" + ".index_no='10' and a.select_id='1'and a.isdelete= 1" + start_sql + end_sql + usqlb;
            o = new JSONObject();
            o.put("index", 21);
            o.put("name", "延长人数");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, ""));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);
            sql = "select a.case_id,a.obj_id from case_step a left join case_info b on a.case_id=b.id where " + "a" + ".index_no='11' and a.select_id='1'and a.isdelete= 1" + start_sql + end_sql + usqlb;
            o = new JSONObject();
            o.put("index", 22);
            o.put("name", "释放人数");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, ""));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);
            sql = "select a.case_id,a.obj_id from case_step  a left join case_info b on a.case_id=b.id where " + "a" + ".index_no='109' and a.select_id='1'and a.isdelete= 1" + start_sql + end_sql + usqlb;
            o = new JSONObject();
            o.put("index", 23);
            o.put("name", "处理结束");
            list = mysql.query(sql);
            if (list.size() > 0) {
                o.put("detail", RelaInfo_static(list, mysql, ""));
            } else {
                o.put("detail", new ArrayList<JSONObject>());
            }
            o.put("count", list.size());
            o.put("unit", "人");
            o.put("home", "0");
            data.add(o);

            back.put("data", data);

            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(448011);
        } finally {
            InfoModelPool.putModel(mysql);
        }

    }

    private static List<JSONObject> RelaInfo_static(List<JSONObject> list, InfoModelHelper mysql, String id_name) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String case_id = "";
            String obj_id = "";
            String id = "";
            if (one.containsKey("id")) {
                id = one.getString("id");
                one.remove("id");
            }
            if (id_name.equals("case_id")) {
                case_id = id;
            }
            if (one.containsKey("case_id")) {
                case_id = one.getString("case_id");
            }

            if (id_name.equals("obj_id")) {
                obj_id = id;
            }
            if (one.containsKey("obj_id")) {
                obj_id = one.getString("obj_id");
            }
            one.put("case_id", case_id);
            String sqls = "select id,accepter,status,decode(case_name,'" + RIUtil.enTitle + "') as case_name," +
                    "create_time " + "from case_info where id='" + case_id + "'";
            List<JSONObject> caseList = mysql.query(sqls);
            if (caseList.size() > 0) {
                JSONObject cone = caseList.get(0);
                String accepter = cone.getString("accepter");
                one.putAll(cone);
                one.put("accepter", RIUtil.UseridToNames(RIUtil.StringToList(accepter)));

                back.add(one);
            }
        }
        return back;
    }


    private static List<JSONObject> RelaInfo_detail(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String case_id = one.getString("id");
            String delete_user = one.getString("delete_user");

            one.put("delete_user", RIUtil.users.get(delete_user));
            String create_user = one.getString("create_user");
            one.put("create_user", RIUtil.users.get(create_user));
            String accepter = one.getString("accepter");
            one.put("accepter", RIUtil.UseridToNames(RIUtil.StringToList(accepter)));
            //嫌疑人
            JSONObject s = new JSONObject();
            s.put("case_id", case_id);
            s.put("limit", 1000);
            JSONArray objs = new JSONArray();
            JSONObject objBack = CaseObjectController.getCaseObject(s);
            if (objBack.containsKey("data") && objBack.getString("data").length() > 0) {
                objs = objBack.getJSONArray("data");
            }
            one.put("objs", RelaInfo_objs(objs, mysql));
            //list
            JSONArray lists = new JSONArray();
            JSONObject listBack = CaseListController.getCaseList(s);
            if (listBack.containsKey("data") && listBack.getString("data").length() > 0) {
                lists = listBack.getJSONArray("data");
            }
            one.put("list", lists);
            //logs
            String sqls =
                    "select *,decode(opt,'" + RIUtil.enContent + "')  from case_log  where case_id='" + case_id + "' "
                            + "and isdelete=1";
            List<JSONObject> logs = new ArrayList<>();
            logs = mysql.query(sqls);
            if (logs.size() > 0) {
                one.put("logs", RelaInfo_log(logs, mysql));
            } else {
                one.put("logs", logs);
            }
//form
            String form = one.getString("form");
            String f = RIUtil.RealDictNames(RIUtil.StringToList(form));
            if (f.length() == 0) {
                f = form;
            }
            one.put("form", f);


            back.add(one);
        }
        return back;
    }

    private static JSONArray RelaInfo_objs(JSONArray list, InfoModelHelper mysql) throws Exception {
        JSONArray back = new JSONArray();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.getJSONObject(i);
            String case_id = one.getString("case_id");
            String obj_id = one.getString("id");
            String current_index = one.getString("current_index");
            String sql = "select count(id) as  count from case_step " + "where case_id='" + case_id + "' and " +
                    "obj_id='" + obj_id + "' and isdelete=1 and index_no=99";
            int count = mysql.query_count(sql);
            if (count == 0 && current_index.equals("99")) {

                sql = "INSERT INTO `case_step`( `case_id`, `obj_id`, `index_no`, `select_id`, `time`, `opt_user`,  " + "`isdelete`) " + "select case_id,obj_id,'99' as index_no,'1' as select_id,time,opt_user,isdelete " + "from case_step where case_id='" + case_id + "' and obj_id='" + obj_id + "' and isdelete=1 " + "order by  id desc limit 1 ;";
                logger.warn(sql);
                mysql.update(sql);

            }

            sql = "select a.*,b.step_name,b.multi " + "from case_step a left join case_step_model b on a.index_no=b" + ".index_no " + "where a.case_id='" + case_id + "' and a.obj_id='" + obj_id + "' and a.isdelete=1" + " order by id";
            List<JSONObject> steps = mysql.query(sql);
            if (steps.size() > 0) {
                one.put("steps", steps);

            } else {
                one.put("steps", new ArrayList<>());
            }

            back.add(one);
        }
        return back;
    }

    private static Object RelaInfo_log(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String opt_user = one.getString("opt_user");

            one.put("opt_user", RIUtil.users.get(opt_user));

            back.add(one);
        }
        return back;
    }


    //******CREATE*******
    private static JSONObject createCase(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String case_name = "";
            String accepter = "";
            String moveEffice = "";
            String aging = "";
            String evaluate = "";
            String status = "2";
            String type = "0";
            String create_user = "";
            String form = "";
            String cycle = "0";
            String unit = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "1";
            if (data.containsKey("case_name") && data.getString("case_name").length() > 0) {
                case_name = data.getString("case_name");
            } else {
                return ErrNo.set(448002);
            }
            if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
                accepter = data.getString("accepter");
            } else {
                return ErrNo.set(448002);
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                return ErrNo.set(448002);
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
            } else {
                return ErrNo.set(448002);
            }
            if (data.containsKey("form") && data.getString("form").length() > 0) {
                form = data.getString("form");
            } else {
                return ErrNo.set(448002);
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(448002);
            }
            if (data.containsKey("cycle") && data.getString("cycle").length() > 0) {
                cycle = data.getString("cycle");
            }
            String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String cycle_notice = today + " 09:00:00";
            String sqls =
                    "insert case_info (case_name,accepter,moveEffice,aging,evaluate" + ",status,create_user," +
                            "create_time,isdelete,type,form," + "cycle,cycle_notice,unit)" + "values(encode('" + case_name + "','" + RIUtil.enTitle + "'),'" + accepter + "','" + moveEffice + "','" + aging + "','" + evaluate + "'" + ",'" + status + "','" + create_user + "','" + create_time + "','" + isdelete + "','" + type + "','" + form + "'," + "'" + cycle + "','" + cycle_notice + "','" + unit + "')";
            logger.warn(sqls);
            mysql.update(sqls);
            sqls = "select id from case_info where decode(case_name,'" + RIUtil.enTitle + "')='" + case_name + "' " + "and" + " create_time='" + create_time + "' and unit='" + unit + "'";
            String id = mysql.query_one(sqls, "id");

            addLogs(new ArrayList<>(), mysql, id, "", "", "案件添加", create_user);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(448001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    public static void addLogs(List<String> logs, InfoModelHelper mysql, String id, String index, String obj_id,
                               String opt, String opt_user) throws Exception {
        String sql = "";
        if (index.length() == 0) {
            sql = "select type from case_info where id='" + id + "'";
            String type = mysql.query_one(sql, "type");
            index = type;

        }

        if (obj_id.length() == 0) {
            obj_id = "0";
        }

        String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        if (logs.size() == 0) {
            logs.add(opt);
        }
        if (logs.size() > 0) {
            for (int i = 0; i < logs.size(); i++) {
                if (id.contains(",")) {
                    id = id.replace("','", ",");
                    String[] ids = id.split(",");
                    for (int ds = 0; ds < ids.length; ds++) {
                        sql = "insert case_log(case_id,index_no,time,opt_user,opt,obj_id)" + "values('" + ids[ds] +
                                "','" + index + "','" + create_time + "','" + opt_user + "',encode('" + logs.get(i) + "','" + RIUtil.enContent + "'),'" + obj_id + "')";
                        mysql.update(sql);
                    }
                } else {
                    sql = "insert case_log(case_id,index_no,time,opt_user,opt,obj_id)values('" + id + "','" + index + "','" + create_time + "','" + opt_user + "',encode('" + logs.get(i) + "','" + RIUtil.enContent + "'),'" + obj_id + "')";

                    mysql.update(sql);
                }

            }
        }
        sql = "select accepter,create_user,decode(case_name,'" + RIUtil.enTitle + "') as case_name from " +
                "case_info where id='" + id + "'";
        String title = "";
        String accepter = "";
        HashMap<String, String> notices = new HashMap<>();
        List<JSONObject> list = mysql.query(sql);
        if (list.size() > 0) {
            JSONObject one = list.get(0);
            title = one.getString("case_name");
            if (title.length() > 15) {
                title = title.substring(0, 15) + "...";
            }

            accepter = one.getString("accepter");
            notices = RIUtil.StringToList(accepter);
            notices.put(one.getString("create_user"), "");

        }

        wechatMsgTemp.createMessage_case(mysql, title, opt, opt_user, notices, id);

    }


    //******GET*******
    private static JSONObject getCase(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String case_name = "";
            String accepter = "";
            String moveEffice = "";
            String aging = "";
            String evaluate = "";
            String status = "";
            String type = "";
            String start_time = "";
            String end_time = "";
            String query_type = "";
            String form = "";
            String unit = "";
            String create_time_start = "";
            String create_time_end = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " a.id='" + id + "' and ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " a.unit='" + unit + "' and ";
            }

            if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
                accepter = data.getString("accepter");
                String accs[] = accepter.split(",");
                String s = "(";
                for (int a = 0; a < accs.length; a++) {
                    s = s + "a.accepter like '%" + accs[a] + "%' or ";
                }
                s = s.substring(0, s.length() - 3) + ")";
                sql = sql + s + " and ";
            }
            if (data.containsKey("moveEffice") && data.getString("moveEffice").length() > 0) {
                moveEffice = data.getString("moveEffice");
                sql = sql + " a.moveEffice='" + moveEffice + "' and ";
            }
            if (data.containsKey("aging") && data.getString("aging").length() > 0) {
                aging = data.getString("aging");
                sql = sql + " a.aging='" + aging + "' and ";
            }
            if (data.containsKey("evaluate") && data.getString("evaluate").length() > 0) {
                evaluate = data.getString("evaluate");
                sql = sql + " a.evaluate='" + evaluate + "' and ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " a.type='" + type + "' and ";
            }
            if (data.containsKey("form") && data.getString("form").length() > 0) {
                form = data.getString("form");
                sql = sql + " a.form='" + form + "' and ";
            }
            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getString("status");
                sql = sql + " a.status='" + status + "' and ";
            }
            if (data.containsKey("query_type") && data.getString("query_type").length() > 0) {
                query_type = data.getString("query_type");

            }

            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time").replace("|", " ");
                if (query_type.equals("2")) {
                    sql = sql + "b.start_date>='" + start_time + "' and ";
                }

            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time").replace("|", " ");
                if (query_type.equals("2")) {
                    sql = sql + "b.start_date<='" + end_time + "' and ";
                }

            }
            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
                create_time_start = data.getString("create_time_start");
                sql = sql + "a.create_time >= '" + create_time_start + "' and ";
            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end");
                sql = sql + "a.create_time <= '" + create_time_end + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sql_count = "";
            String sqls = "";


            if (data.containsKey("case_name") && data.getString("case_name").length() > 0) {
                case_name = data.getString("case_name");
                if ("1".equals(query_type)) {
                    sql = sql + " decode(a.case_name,'" + RIUtil.enTitle + "') like '%" + case_name + "%' and ";
                } else if ("2".equals(query_type)) {
                    sql = sql + " decode(b.obj_name,'" + RIUtil.enName + "') like '%" + case_name + "%' and ";
                } else {
                    String s =
                            "select id from `user` where name like '%" + case_name +
                                    "%'";
                    List<JSONObject> l = mysql.query(s);
                    if (l.size() > 0) {
                        String acc = "( ";
                        for (int i = 0; i < l.size(); i++) {
                            acc = acc + " accepter like '%" + l.get(i).getString("id") + "%' or ";
                        }
                        acc = acc.substring(0, acc.length() - 2) + ") and ";
                        sql = sql + acc;
                    }
                }
            }
            if ("1".equals(query_type)) {

                sqls = "select a.*,decode(case_name,'" + RIUtil.enTitle + "') as case_name from case_info a where " + "1=1" + " and " + sql + " a.isdelete=1 order by a.create_time desc limit " + limit + " offset" + " " + limit * (page - 1);
                sql_count = "select count(a.id) as count from case_info a where 1=1 and " + sql + " a.isdelete=1 ";
            } else if ("2".equals(query_type)) {

                sqls = "select a.*,decode(case_name,'" + RIUtil.enTitle + "') as case_name from " + "case_info a left" +
                        " join case_object b on a.id=b.case_id where b.id is not null and " + sql + " a.isdelete =1 " +
                        "GROUP BY a.id order by a.create_time desc limit " + limit + " offset " + limit * (page - 1);
                sql_count =
                        "select count(a.id) as count from case_info a left join case_object b on a.id=b.case_id " +
                                "where b.id is not null and " + sql + " a.isdelete=1 group by a.id";
                logger.warn(sqls);
                logger.warn(sql_count);
            } else {

                sqls = "select a.*,decode(case_name,'" + RIUtil.enTitle + "') as case_name from case_info a where " + "1=1" + " and " + sql + " a.isdelete=1  order by a.create_time desc limit " + limit + " " + "offset " + limit * (page - 1);
                sql_count = "select count(a.id) as count from case_info a where 1=1 and " + sql + " a.isdelete=1 ";
                logger.warn(sqls);
                logger.warn(sql_count);

            }
            //  String sqls = "select a.*,decode(case_name,'" + RIUtil.enTitle + "') as case_name from case_info a
            //  left join case_step b on a.id=b.case_id where 1=1 and " + sql + " a.isdelete=1 GROUP BY a.id   limit
            //  " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();

            list = mysql.query(sqls);
            // logger.warn(list.toString());
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                if (query_type.equals("2")) {
                    int count = 0;

                    List<JSONObject> cs = mysql.query(sql_count);

                    back.put("count", cs.size());
                } else {
                    back.put("count", mysql.query_count(sql_count));
                }
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(448005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private static Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String accepter = one.getString("accepter");

            one.put("accepter", RIUtil.UseridToNames(RIUtil.StringToList(accepter)));
            String create_user = one.getString("create_user");
            try {
                one.put("create_user", RIUtil.users.get(create_user));
            } catch (Exception ex) {
            }
            String id = one.getString("id");
            //String sql = "select a.id,a.case_id,decode(obj_name,'" + RIUtil.enName + "') as obj_name,b.step_name,b
            // .index_no from case_object a LEFT JOIN case_step_model b on a.current_index=b.index_no where a
            // .case_id='" + id + "' and a.isdelete=1";
            //嫌疑人
            JSONObject s = new JSONObject();
            s.put("case_id", id);
            s.put("limit", 1000);
            JSONArray objs = new JSONArray();
            JSONObject objBack = CaseObjectController.getCaseObject(s);
            if (objBack.containsKey("data") && objBack.getString("data").length() > 0) {
                objs = objBack.getJSONArray("data");
            }
            one.put("objs", RelaInfo_objs(objs, mysql));
            //one.put("objs", mysql.query(sql));
            String sql = "select *,decode(list_name,'" + RIUtil.enTitle + "') as list_name from case_list where " +
                    "case_id='" + id + "' and isdelete=1";
            one.put("list", mysql.query(sql));

            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private static JSONObject updateCase(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String case_name = "";
            String accepter = "";
            String moveEffice = "";
            String aging = "";
            String evaluate = "";
            String status = "";
            String type = "";
            String create_time = "";
            String opt_user = "";
            String finish_time = "";
            String form = "";
            String cycle = "0";
            List<String> logs = new ArrayList<>();
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(448010);
            }

            String s =
                    "select *,decode(case_name,'" + RIUtil.enTitle + "') as case_name from case_info where id='" + id + "'";
            List<JSONObject> list = mysql.query(s);
            JSONObject old = list.get(0);
            if (data.containsKey("case_name") && data.getString("case_name").length() > 0) {
                case_name = data.getString("case_name");
                sql = sql + " case_name=encode('" + case_name + "','" + RIUtil.enTitle + "') , ";
                if (!old.getString("case_name").equals(case_name)) {
                    logs.add("案件名称：" + old.getString("case_name") + "->" + case_name);
                }

            }
            if (data.containsKey("accepter") && data.getString("accepter").length() > 0) {
                accepter = data.getString("accepter");
                sql = sql + " accepter='" + accepter + "' , ";
                if (!old.getString("accepter").equals(accepter)) {
                    HashMap<String, String> olds = RIUtil.StringToList(old.getString("accepter"));
                    String strOld = "";
                    HashMap<String, String> nns = RIUtil.StringToList(accepter);
                    for (Map.Entry<String, String> o : olds.entrySet()) {
                        String u = o.getKey();
                        strOld = strOld + RIUtil.IdToName(u, mysql, " name",
                                "user") + ",";
                    }
                    String strNew = "";
                    for (Map.Entry<String, String> o : nns.entrySet()) {
                        String u = o.getKey();
                        strNew = strNew + RIUtil.IdToName(u, mysql, " name",
                                "user") + ",";
                    }

                    logs.add("主办民警：" + strOld + "->" + strNew);
                }
            }
            if (data.containsKey("moveEffice") && data.getString("moveEffice").length() > 0) {
                moveEffice = data.getString("moveEffice");
                sql = sql + " moveEffice='" + moveEffice + "' , ";
                logs.add("更新移诉率：" + moveEffice);
            }
            if (data.containsKey("form") && data.getString("form").length() > 0) {
                form = data.getString("form");
                sql = sql + " form='" + form + "' , ";

            }
            if (data.containsKey("cycle") && data.getString("cycle").length() > 0) {
                cycle = data.getString("cycle");
                sql = sql + " cycle='" + cycle + "' , ";

            }
            if (data.containsKey("aging") && data.getString("aging").length() > 0) {
                aging = data.getString("aging");
                sql = sql + " aging='" + aging + "' , ";
                logs.add("更新时效：" + aging);
            }
            if (data.containsKey("evaluate") && data.getString("evaluate").length() > 0) {
                evaluate = data.getString("evaluate");
                sql = sql + " evaluate='" + evaluate + "' , ";
                logs.add("更新评价：" + evaluate);
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql = sql + " type='" + type + "' , ";
                logs.add("更新案件类型：" + type);
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(448010);
            }
            if (data.containsKey("status") && data.getString("status").length() > 0) {
                status = data.getString("status");
                sql = sql + " status='" + status + "' , ";
                if ("1".equals(status)) {
                    logs.add("案件更新为完成");
                }
            }


            String sqls = "update case_info set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);

            addLogs(logs, mysql, id, "", "", "更新", opt_user);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(448003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private static JSONObject deleteCase(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id").replace(",", "','");
        } else {
            return ErrNo.set(448008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(448008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update case_info set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id in ('" + id + "')";
            mysql.update(sqls);
            sqls = "update case_list set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where case_id in ('" + id + "')";
            mysql.update(sqls);
            sqls = "update case_log set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where case_id in ('" + id + "')";
            mysql.update(sqls);
            sqls = "update case_step set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where case_id in ('" + id + "')";
            mysql.update(sqls);
            sqls = "update case_object set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where case_id in ('" + id + "')";
            mysql.update(sqls);


            addLogs(new ArrayList<>(), mysql, id, "", "", "删除案件", opt_user);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(448007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

}
