
package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController

public class PushWorkController {
    private static Logger logger = LoggerFactory.getLogger(PushWorkController.class);

    @RequestMapping(method = {RequestMethod.POST}, path = {"/push_work"})
    @PassToken
    public JSONObject get_pushwork(TNOAHttpRequest request) throws Exception {

        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            String opt = data.getString("opt");
            if (opt.equals("get_core_index")) {
                return getCoreIndex(data);
            } else if (opt.equals("update_core_index")) {
                return updateCoreIndex(data, request.getRemoteAddr());
            } else if (opt.equals("get_base_check")) {
                return getBaseCheck(data);
            } else if (opt.equals("update_base_check")) {
                return updateBaseCheck(data, request.getRemoteAddr());
            } else if (opt.equals("get_process")) {
                return getProcess(data);
            } else if (opt.equals("update_process")) {
                return updateProcess(data, request.getRemoteAddr());
            } else if (opt.equals("get_push_log")) {
                return getPushLog(data);
            } else if (opt.equals("get_boost")) {
                return getBoost(data);
            } else if (opt.equals("update_boost")) {
                return updateBoost(data, request.getRemoteAddr());
            } else if (opt.equals("get_base")) {
                return getPushBasic(data);
            } else if (opt.equals("update_base")) {
                return updatePushBasic(data, request.getRemoteAddr());
            } else {
                return ErrNo.set(449009);
            }
        } else {
            return ErrNo.set(449009);
        }

    }

    //******GET*******
    private JSONObject getPushBasic(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(455004);
            }
            String sqls = "select * from push_basic where id='" + id + "' and isdelete=1";
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);

            back.put("data", RelaInfo(list, mysql));

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(455005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    //******UPDATE*******
    private JSONObject updatePushBasic(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String kygs_mb = "";
            String kygs_wc = "";
            String kygs_wcl = "";
            String dzyj_las = "";
            String dzyj_21las = "";
            String dzyj_tb = "";
            String dkxd_yxkhs = "";
            String dkxd_hcs = "";
            String dkxd_hcl = "";
            String xzzt_df = "";
            String xzzt_pm = "";
            String dzajxdl = "";
            String szapksayytql = "";
            String szapkjxl = "";
            String ycs = "";
            String sw = "";
            String hm = "";
            String zw = "";
            String dna = "";
            String bzdz = "";
            String jszy = "";
            String llgk = "";
            String lgzd = "";
            String sqmj = "";
            String xfaq = "";
            String hzwr = "";
            String jdyzb_wc = "";
            String jdyzb_mb = "";
            String zazd = "";
            String dwxx = "";
            String mjjbz = "";
            String qzefd = "";
            String ldrkxxcjdjl = "";
            String mdjffxhjjsfkl = "";
            String mdjffxhjfkdbl = "";
            String ewmmp_wc = "";
            String ewmmp_mb = "";
            String sc_wc = "";
            String sc_mb = "";
            String sd_wc = "";
            String sd_mb = "";
            String nb_mb = "";
            String nb_wc = "";
            String cyry = "";
            String sjjb_mb = "";
            String sjjb_wc = "";
            String jzqb_mb = "";
            String jzqbx_wc = "";
            String ydzx_mb = "";
            String ydzx_wc = "";
            String ydzx_wcl = "";
            String wlsl_mb = "";
            String wlsl_wc = "";
            String ywl_mb = "";
            String ywl_wc = "";
            String wlhc_mb = "";
            String wlhc_wc = "";
            String xzjl_mb = "";
            String xzjl_wc = "";
            String xzcj_wc = "";
            String xzkb_wc = "";
            String zbqb_mb = "";
            String zbqb_wc = "";
            String zbqb_wcl = "";
            String qbxshc_xfs = "";
            String qbxshc_wcs = "";
            String qbxshc_rws = "";
            String qbxxbs_wcs = "";
            String myd_wc = "";
            String zgxx_df = "";
            String zgxx_pm = "";
            String xwmt_df = "";
            String xwmt_pm = "";
            String dwzw_df = "";
            String dwzw_pm = "";
            String wzxx_df = "";
            String wzxx_pm = "";
            String jbly_wc = "";
            String opt_user = "";

            JSONObject old = new JSONObject();
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(454006);
            }
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

                String s = "select * from push_basic where id='" + id + "'";
                List<JSONObject> ll = mysql.query(s);

                if (ll.size() > 0) {
                    old = ll.get(0);
                } else {
                    s = "INSERT INTO `push_basic`  VALUES " +
                            "('" + id + "', '','','','','','','','','',''," +
                            "'','','','','','','','','',''," +
                            "'','','','','','','','','',''," +
                            "'','','','','','','','','',''," +
                            "'','','','','','','','','',''," +
                            "'','','','','','','','','',''," +
                            "'','','','','','','','','',''," +
                            "'','','','','','','','','1');";
                    mysql.update(s);
                    s = "select * from push_boost where id='" + id + "'";
                    ll = mysql.query(s);
                    old = ll.get(0);
                }

            } else {
                id = new SimpleDateFormat("yyyy").format(new Date());
                String s = "INSERT INTO `push_basic`  VALUES " +
                        "('" + id + "', " +
                        "'','','','','','','','','',''," +
                        "'','','','','','','','','',''," +
                        "'','','','','','','','','',''," +
                        "'','','','','','','','','',''," +
                        "'','','','','','','','','',''," +
                        "'','','','','','','','','',''," +
                        "'','','','','','','','','',''," +
                        "'','','','','','','','','1');";
                mysql.update(s);
                s = "select * from push_basic where id='" + id + "'";
                List<JSONObject> ll = mysql.query(s);
                old = ll.get(0);
            }
            if (data.containsKey("kygs_mb") && data.getString("kygs_mb").length() > 0) {
                kygs_mb = data.getString("kygs_mb");
                if (!old.getString("kygs_mb").equals(kygs_mb)) {
                    sql = sql + " kygs_mb='" + kygs_mb + "' , ";
                    writeLog(mysql, old.getString("kygs_mb") + "->" + kygs_mb, "kygs_mb", opt_user);
                }
            }
            if (data.containsKey("kygs_wc") && data.getString("kygs_wc").length() > 0) {
                kygs_wc = data.getString("kygs_wc");
                if (!old.getString("kygs_wc").equals(kygs_wc)) {
                    sql = sql + " kygs_wc='" + kygs_wc + "' , ";
                    writeLog(mysql, old.getString("kygs_wc") + "->" + kygs_wc, "kygs_wc", opt_user);
                }
            }
            if (data.containsKey("kygs_wcl") && data.getString("kygs_wcl").length() > 0) {
                kygs_wcl = data.getString("kygs_wcl");
                if (!old.getString("kygs_wcl").equals(kygs_wcl)) {
                    sql = sql + " kygs_wcl='" + kygs_wcl + "' , ";
                    writeLog(mysql, old.getString("kygs_wcl") + "->" + kygs_wcl, "kygs_wcl", opt_user);
                }
            }
            if (data.containsKey("dzyj_las") && data.getString("dzyj_las").length() > 0) {
                dzyj_las = data.getString("dzyj_las");
                if (!old.getString("dzyj_las").equals(dzyj_las)) {
                    sql = sql + " dzyj_las='" + dzyj_las + "' , ";
                    writeLog(mysql, old.getString("dzyj_las") + "->" + dzyj_las, "dzyj_las", opt_user);
                }
            }
            if (data.containsKey("dzyj_21las") && data.getString("dzyj_21las").length() > 0) {
                dzyj_21las = data.getString("dzyj_21las");
                if (!old.getString("dzyj_21las").equals(dzyj_21las)) {
                    sql = sql + " dzyj_21las='" + dzyj_21las + "' , ";
                    writeLog(mysql, old.getString("dzyj_21las") + "->" + dzyj_21las, "dzyj_21las", opt_user);
                }
            }
            if (data.containsKey("dzyj_tb") && data.getString("dzyj_tb").length() > 0) {
                dzyj_tb = data.getString("dzyj_tb");
                if (!old.getString("dzyj_tb").equals(dzyj_tb)) {
                    sql = sql + " dzyj_tb='" + dzyj_tb + "' , ";
                    writeLog(mysql, old.getString("dzyj_tb") + "->" + dzyj_tb, "dzyj_tb", opt_user);
                }
            }
            if (data.containsKey("dkxd_yxkhs") && data.getString("dkxd_yxkhs").length() > 0) {
                dkxd_yxkhs = data.getString("dkxd_yxkhs");
                if (!old.getString("dkxd_yxkhs").equals(dkxd_yxkhs)) {
                    sql = sql + " dkxd_yxkhs='" + dkxd_yxkhs + "' , ";
                    writeLog(mysql, old.getString("dkxd_yxkhs") + "->" + dkxd_yxkhs, "dkxd_yxkhs", opt_user);
                }
            }
            if (data.containsKey("dkxd_hcs") && data.getString("dkxd_hcs").length() > 0) {
                dkxd_hcs = data.getString("dkxd_hcs");
                if (!old.getString("dkxd_hcs").equals(dkxd_hcs)) {
                    sql = sql + " dkxd_hcs='" + dkxd_hcs + "' , ";
                    writeLog(mysql, old.getString("dkxd_hcs") + "->" + dkxd_hcs, "dkxd_hcs", opt_user);
                }
            }
            if (data.containsKey("dkxd_hcl") && data.getString("dkxd_hcl").length() > 0) {
                dkxd_hcl = data.getString("dkxd_hcl");
                if (!old.getString("dkxd_hcl").equals(dkxd_hcl)) {
                    sql = sql + " dkxd_hcl='" + dkxd_hcl + "' , ";
                    writeLog(mysql, old.getString("dkxd_hcl") + "->" + dkxd_hcl, "dkxd_hcl", opt_user);
                }
            }
            if (data.containsKey("xzzt_df") && data.getString("xzzt_df").length() > 0) {
                xzzt_df = data.getString("xzzt_df");
                if (!old.getString("xzzt_df").equals(xzzt_df)) {
                    sql = sql + " xzzt_df='" + xzzt_df + "' , ";
                    writeLog(mysql, old.getString("xzzt_df") + "->" + xzzt_df, "xzzt_df", opt_user);
                }
            }
            if (data.containsKey("xzzt_pm") && data.getString("xzzt_pm").length() > 0) {
                xzzt_pm = data.getString("xzzt_pm");
                if (!old.getString("xzzt_pm").equals(xzzt_pm)) {
                    sql = sql + " xzzt_pm='" + xzzt_pm + "' , ";
                    writeLog(mysql, old.getString("xzzt_pm") + "->" + xzzt_pm, "xzzt_pm", opt_user);
                }
            }
            if (data.containsKey("dzajxdl") && data.getString("dzajxdl").length() > 0) {
                dzajxdl = data.getString("dzajxdl");
                if (!old.getString("dzajxdl").equals(dzajxdl)) {
                    sql = sql + " dzajxdl='" + dzajxdl + "' , ";
                    writeLog(mysql, old.getString("dzajxdl") + "->" + dzajxdl, "dzajxdl", opt_user);
                }
            }
            if (data.containsKey("szapksayytql") && data.getString("szapksayytql").length() > 0) {
                szapksayytql = data.getString("szapksayytql");
                if (!old.getString("szapksayytql").equals(szapksayytql)) {
                    sql = sql + " szapksayytql='" + szapksayytql + "' , ";
                    writeLog(mysql, old.getString("szapksayytql") + "->" + szapksayytql, "szapksayytql", opt_user);
                }
            }
            if (data.containsKey("szapkjxl") && data.getString("szapkjxl").length() > 0) {
                szapkjxl = data.getString("szapkjxl");
                if (!old.getString("szapkjxl").equals(szapkjxl)) {
                    sql = sql + " szapkjxl='" + szapkjxl + "' , ";
                    writeLog(mysql, old.getString("szapkjxl") + "->" + szapkjxl, "szapkjxl", opt_user);
                }
            }
            if (data.containsKey("ycs") && data.getString("ycs").length() > 0) {
                ycs = data.getString("ycs");
                if (!old.getString("ycs").equals(ycs)) {
                    sql = sql + " ycs='" + ycs + "' , ";
                    writeLog(mysql, old.getString("ycs") + "->" + ycs, "ycs", opt_user);
                }
            }
            if (data.containsKey("sw") && data.getString("sw").length() > 0) {
                sw = data.getString("sw");
                if (!old.getString("sw").equals(sw)) {
                    sql = sql + " sw='" + sw + "' , ";
                    writeLog(mysql, old.getString("sw") + "->" + sw, "sw", opt_user);
                }
            }
            if (data.containsKey("hm") && data.getString("hm").length() > 0) {
                hm = data.getString("hm");
                if (!old.getString("hm").equals(hm)) {
                    sql = sql + " hm='" + hm + "' , ";
                    writeLog(mysql, old.getString("hm") + "->" + hm, "hm", opt_user);
                }
            }
            if (data.containsKey("zw") && data.getString("zw").length() > 0) {
                zw = data.getString("zw");
                if (!old.getString("zw").equals(zw)) {
                    sql = sql + " zw='" + zw + "' , ";
                    writeLog(mysql, old.getString("zw") + "->" + zw, "zw", opt_user);
                }
            }
            if (data.containsKey("dna") && data.getString("dna").length() > 0) {
                dna = data.getString("dna");
                if (!old.getString("dna").equals(dna)) {
                    sql = sql + " dna='" + dna + "' , ";
                    writeLog(mysql, old.getString("dna") + "->" + dna, "dna", opt_user);
                }
            }
            if (data.containsKey("bzdz") && data.getString("bzdz").length() > 0) {
                bzdz = data.getString("bzdz");
                if (!old.getString("bzdz").equals(bzdz)) {
                    sql = sql + " bzdz='" + bzdz + "' , ";
                    writeLog(mysql, old.getString("bzdz") + "->" + bzdz, "bzdz", opt_user);
                }
            }
            if (data.containsKey("jszy") && data.getString("jszy").length() > 0) {
                jszy = data.getString("jszy");
                if (!old.getString("jszy").equals(jszy)) {
                    sql = sql + " jszy='" + jszy + "' , ";
                    writeLog(mysql, old.getString("jszy") + "->" + jszy, "jszy", opt_user);
                }
            }
            if (data.containsKey("llgk") && data.getString("llgk").length() > 0) {
                llgk = data.getString("llgk");
                if (!old.getString("llgk").equals(llgk)) {
                    sql = sql + " llgk='" + llgk + "' , ";
                    writeLog(mysql, old.getString("llgk") + "->" + llgk, "llgk", opt_user);
                }
            }
            if (data.containsKey("lgzd") && data.getString("lgzd").length() > 0) {
                lgzd = data.getString("lgzd");
                if (!old.getString("lgzd").equals(lgzd)) {
                    sql = sql + " lgzd='" + lgzd + "' , ";
                    writeLog(mysql, old.getString("lgzd") + "->" + lgzd, "lgzd", opt_user);
                }
            }
            if (data.containsKey("sqmj") && data.getString("sqmj").length() > 0) {
                sqmj = data.getString("sqmj");
                if (!old.getString("sqmj").equals(sqmj)) {
                    sql = sql + " sqmj='" + sqmj + "' , ";
                    writeLog(mysql, old.getString("sqmj") + "->" + sqmj, "sqmj", opt_user);
                }
            }
            if (data.containsKey("xfaq") && data.getString("xfaq").length() > 0) {
                xfaq = data.getString("xfaq");
                if (!old.getString("xfaq").equals(xfaq)) {
                    sql = sql + " xfaq='" + xfaq + "' , ";
                    writeLog(mysql, old.getString("xfaq") + "->" + xfaq, "xfaq", opt_user);
                }
            }
            if (data.containsKey("hzwr") && data.getString("hzwr").length() > 0) {
                hzwr = data.getString("hzwr");
                if (!old.getString("hzwr").equals(hzwr)) {
                    sql = sql + " hzwr='" + hzwr + "' , ";
                    writeLog(mysql, old.getString("hzwr") + "->" + hzwr, "hzwr", opt_user);
                }
            }
            if (data.containsKey("jdyzb_mb") && data.getString("jdyzb_mb").length() > 0) {
                jdyzb_mb = data.getString("jdyzb_mb");
                if (!old.getString("jdyzb_mb").equals(jdyzb_mb)) {
                    sql = sql + " jdyzb_mb='" + jdyzb_mb + "' , ";
                    writeLog(mysql, old.getString("jdyzb_mb") + "->" + jdyzb_mb, "jdyzb_mb", opt_user);
                }
            }
            if (data.containsKey("jdyzb_wc") && data.getString("jdyzb_wc").length() > 0) {
                jdyzb_wc = data.getString("jdyzb_wc");
                if (!old.getString("jdyzb_wc").equals(jdyzb_wc)) {
                    sql = sql + " jdyzb_wc='" + jdyzb_wc + "' , ";
                    writeLog(mysql, old.getString("jdyzb_wc") + "->" + jdyzb_wc, "jdyzb_wc", opt_user);
                }
            }
            if (data.containsKey("zazd") && data.getString("zazd").length() > 0) {
                zazd = data.getString("zazd");
                if (!old.getString("zazd").equals(zazd)) {
                    sql = sql + " zazd='" + zazd + "' , ";
                    writeLog(mysql, old.getString("zazd") + "->" + zazd, "zazd", opt_user);
                }
            }
            if (data.containsKey("dwxx") && data.getString("dwxx").length() > 0) {
                dwxx = data.getString("dwxx");
                if (!old.getString("dwxx").equals(dwxx)) {
                    sql = sql + " dwxx='" + dwxx + "' , ";
                    writeLog(mysql, old.getString("dwxx") + "->" + dwxx, "dwxx", opt_user);
                }
            }
            if (data.containsKey("mjjbz") && data.getString("mjjbz").length() > 0) {
                mjjbz = data.getString("mjjbz");
                if (!old.getString("mjjbz").equals(mjjbz)) {
                    sql = sql + " mjjbz='" + mjjbz + "' , ";
                    writeLog(mysql, old.getString("mjjbz") + "->" + mjjbz, "mjjbz", opt_user);
                }
            }
            if (data.containsKey("qzefd") && data.getString("qzefd").length() > 0) {
                qzefd = data.getString("qzefd");
                if (!old.getString("qzefd").equals(qzefd)) {
                    sql = sql + " qzefd='" + qzefd + "' , ";
                    writeLog(mysql, old.getString("qzefd") + "->" + qzefd, "qzefd", opt_user);
                }
            }
            if (data.containsKey("ldrkxxcjdjl") && data.getString("ldrkxxcjdjl").length() > 0) {
                ldrkxxcjdjl = data.getString("ldrkxxcjdjl");
                if (!old.getString("ldrkxxcjdjl").equals(ldrkxxcjdjl)) {
                    sql = sql + " ldrkxxcjdjl='" + ldrkxxcjdjl + "' , ";
                    writeLog(mysql, old.getString("ldrkxxcjdjl") + "->" + ldrkxxcjdjl, "ldrkxxcjdjl", opt_user);
                }
            }
            if (data.containsKey("ldrkxxcjdjl") && data.getString("ldrkxxcjdjl").length() > 0) {
                ldrkxxcjdjl = data.getString("ldrkxxcjdjl");
                if (!old.getString("ldrkxxcjdjl").equals(ldrkxxcjdjl)) {
                    sql = sql + " ldrkxxcjdjl='" + ldrkxxcjdjl + "' , ";
                    writeLog(mysql, old.getString("ldrkxxcjdjl") + "->" + ldrkxxcjdjl, "ldrkxxcjdjl", opt_user);
                }
            }
            if (data.containsKey("mdjffxhjfkdbl") && data.getString("mdjffxhjfkdbl").length() > 0) {
                mdjffxhjfkdbl = data.getString("mdjffxhjfkdbl");
                if (!old.getString("mdjffxhjfkdbl").equals(mdjffxhjfkdbl)) {
                    sql = sql + " mdjffxhjfkdbl='" + mdjffxhjfkdbl + "' , ";
                    writeLog(mysql, old.getString("mdjffxhjfkdbl") + "->" + mdjffxhjfkdbl, "mdjffxhjfkdbl", opt_user);
                }
            }
            if (data.containsKey("ewmmp_wc") && data.getString("ewmmp_wc").length() > 0) {
                ewmmp_wc = data.getString("ewmmp_wc");
                if (!old.getString("ewmmp_wc").equals(ewmmp_wc)) {
                    sql = sql + " ewmmp_wc='" + ewmmp_wc + "' , ";
                    writeLog(mysql, old.getString("ewmmp_wc") + "->" + ewmmp_wc, "ewmmp_wc", opt_user);
                }
            }
            if (data.containsKey("ewmmp_mb") && data.getString("ewmmp_mb").length() > 0) {
                ewmmp_mb = data.getString("ewmmp_mb");
                if (!old.getString("ewmmp_mb").equals(ewmmp_mb)) {
                    sql = sql + " ewmmp_mb='" + ewmmp_mb + "' , ";
                    writeLog(mysql, old.getString("ewmmp_mb") + "->" + ewmmp_mb, "ewmmp_mb", opt_user);
                }
            }
            if (data.containsKey("sc_mb") && data.getString("sc_mb").length() > 0) {
                sc_mb = data.getString("sc_mb");
                if (!old.getString("sc_mb").equals(sc_mb)) {
                    sql = sql + " sc_mb='" + sc_mb + "' , ";
                    writeLog(mysql, old.getString("sc_mb") + "->" + sc_mb, "sc_mb", opt_user);
                }
            }
            if (data.containsKey("sc_wc") && data.getString("sc_wc").length() > 0) {
                sc_wc = data.getString("sc_wc");
                if (!old.getString("sc_wc").equals(sc_wc)) {
                    sql = sql + " sc_wc='" + sc_wc + "' , ";
                    writeLog(mysql, old.getString("sc_wc") + "->" + sc_wc, "sc_wc", opt_user);
                }
            }
            if (data.containsKey("sd_wc") && data.getString("sd_wc").length() > 0) {
                sd_wc = data.getString("sd_wc");
                if (!old.getString("sd_wc").equals(sd_wc)) {
                    sql = sql + " sd_wc='" + sd_wc + "' , ";
                    writeLog(mysql, old.getString("sd_wc") + "->" + sd_wc, "sd_wc", opt_user);
                }
            }

            if (data.containsKey("sd_mb") && data.getString("sd_mb").length() > 0) {
                sd_mb = data.getString("sd_mb");
                if (!old.getString("sd_mb").equals(sd_mb)) {
                    sql = sql + " sd_mb='" + sd_mb + "' , ";
                    writeLog(mysql, old.getString("sd_mb") + "->" + sd_mb, "sd_mb", opt_user);
                }
            }
            if (data.containsKey("nb_wc") && data.getString("nb_wc").length() > 0) {
                nb_wc = data.getString("nb_wc");
                if (!old.getString("nb_wc").equals(nb_wc)) {
                    sql = sql + " nb_wc='" + nb_wc + "' , ";
                    writeLog(mysql, old.getString("nb_wc") + "->" + nb_wc, "nb_wc", opt_user);
                }
            }

            if (data.containsKey("nb_mb") && data.getString("nb_mb").length() > 0) {
                nb_mb = data.getString("nb_mb");
                if (!old.getString("nb_mb").equals(nb_mb)) {
                    sql = sql + "nb_mb='" + nb_mb + "' , ";
                    writeLog(mysql, old.getString("nb_mb") + "->" + nb_mb, "nb_mb", opt_user);
                }
            }
            if (data.containsKey("cyry") && data.getString("cyry").length() > 0) {
                cyry = data.getString("cyry");
                if (!old.getString("cyry").equals(cyry)) {
                    sql = sql + " cyry='" + cyry + "' , ";
                    writeLog(mysql, old.getString("cyry") + "->" + cyry, "cyry", opt_user);
                }
            }
            if (data.containsKey("sjjb_mb") && data.getString("sjjb_mb").length() > 0) {
                sjjb_mb = data.getString("sjjb_mb");
                if (!old.getString("sjjb_mb").equals(sjjb_mb)) {
                    sql = sql + " sjjb_mb='" + sjjb_mb + "' , ";
                    writeLog(mysql, old.getString("sjjb_mb") + "->" + sjjb_mb, "sjjb_mb", opt_user);
                }
            }
            if (data.containsKey("sjjb_wc") && data.getString("sjjb_wc").length() > 0) {
                sjjb_wc = data.getString("sjjb_wc");
                if (!old.getString("sjjb_wc").equals(sjjb_wc)) {
                    sql = sql + " sjjb_wc='" + sjjb_wc + "' , ";
                    writeLog(mysql, old.getString("sjjb_wc") + "->" + sjjb_wc, "sjjb_wc", opt_user);
                }
            }
            if (data.containsKey("jzqb_mb") && data.getString("jzqb_mb").length() > 0) {
                jzqb_mb = data.getString("jzqb_mb");
                if (!old.getString("jzqb_mb").equals(jzqb_mb)) {
                    sql = sql + " jzqb_mb='" + jzqb_mb + "' , ";
                    writeLog(mysql, old.getString("jzqb_mb") + "->" + jzqb_mb, "jzqb_mb", opt_user);
                }
            }
            if (data.containsKey("jzqbx_wc") && data.getString("jzqbx_wc").length() > 0) {
                jzqbx_wc = data.getString("jzqbx_wc");
                if (!old.getString("jzqbx_wc").equals(jzqbx_wc)) {
                    sql = sql + " jzqbx_wc='" + jzqbx_wc + "' , ";
                    writeLog(mysql, old.getString("jzqbx_wc") + "->" + jzqbx_wc, "jzqbx_wc", opt_user);
                }
            }
            if (data.containsKey("ydzx_mb") && data.getString("ydzx_mb").length() > 0) {
                ydzx_mb = data.getString("ydzx_mb");
                if (!old.getString("ydzx_mb").equals(ydzx_mb)) {
                    sql = sql + " ydzx_mb='" + ydzx_mb + "' , ";
                    writeLog(mysql, old.getString("ydzx_mb") + "->" + ydzx_mb, "ydzx_mb", opt_user);
                }
            }
            if (data.containsKey("ydzx_wc") && data.getString("ydzx_wc").length() > 0) {
                ydzx_wc = data.getString("ydzx_wc");
                if (!old.getString("ydzx_wc").equals(ydzx_wc)) {
                    sql = sql + " ydzx_wc='" + ydzx_wc + "' , ";
                    writeLog(mysql, old.getString("ydzx_wc") + "->" + ydzx_wc, "ydzx_wc", opt_user);
                }
            }
            if (data.containsKey("ydzx_wcl") && data.getString("ydzx_wcl").length() > 0) {
                ydzx_wcl = data.getString("ydzx_wcl");
                if (!old.getString("ydzx_wcl").equals(ydzx_wcl)) {
                    sql = sql + " ydzx_wcl='" + ydzx_wcl + "' , ";
                    writeLog(mysql, old.getString("ydzx_wcl") + "->" + ydzx_wcl, "ydzx_wcl", opt_user);
                }
            }
            if (data.containsKey("wlsl_mb") && data.getString("wlsl_mb").length() > 0) {
                wlsl_mb = data.getString("wlsl_mb");
                if (!old.getString("wlsl_mb").equals(wlsl_mb)) {
                    sql = sql + " wlsl_mb='" + wlsl_mb + "' , ";
                    writeLog(mysql, old.getString("wlsl_mb") + "->" + wlsl_mb, "wlsl_mb", opt_user);
                }
            }
            if (data.containsKey("wlsl_wc") && data.getString("wlsl_wc").length() > 0) {
                wlsl_wc = data.getString("wlsl_wc");
                if (!old.getString("wlsl_wc").equals(wlsl_wc)) {
                    sql = sql + " wlsl_wc='" + wlsl_wc + "' , ";
                    writeLog(mysql, old.getString("wlsl_wc") + "->" + wlsl_wc, "wlsl_wc", opt_user);
                }
            }
            if (data.containsKey("ywl_mb") && data.getString("ywl_mb").length() > 0) {
                ywl_mb = data.getString("ywl_mb");
                if (!old.getString("ywl_mb").equals(ywl_mb)) {
                    sql = sql + " ywl_mb='" + ywl_mb + "' , ";
                    writeLog(mysql, old.getString("ywl_mb") + "->" + ywl_mb, "ywl_mb", opt_user);
                }
            }
            if (data.containsKey("ywl_wc") && data.getString("ywl_wc").length() > 0) {
                ywl_wc = data.getString("ywl_wc");
                if (!old.getString("ywl_wc").equals(ywl_wc)) {
                    sql = sql + " ywl_wc='" + ywl_wc + "' , ";
                    writeLog(mysql, old.getString("ywl_wc") + "->" + ywl_wc, "ywl_wc", opt_user);
                }
            }
            if (data.containsKey("wlhc_mb") && data.getString("wlhc_mb").length() > 0) {
                wlhc_mb = data.getString("wlhc_mb");
                if (!old.getString("wlhc_mb").equals(wlhc_mb)) {
                    sql = sql + " wlhc_mb='" + wlhc_mb + "' , ";
                    writeLog(mysql, old.getString("wlhc_mb") + "->" + wlhc_mb, "wlhc_mb", opt_user);
                }
            }
            if (data.containsKey("wlhc_wc") && data.getString("wlhc_wc").length() > 0) {
                wlhc_wc = data.getString("wlhc_wc");
                if (!old.getString("wlhc_wc").equals(wlhc_wc)) {
                    sql = sql + " wlhc_wc='" + wlhc_wc + "' , ";
                    writeLog(mysql, old.getString("wlhc_wc") + "->" + wlhc_wc, "wlhc_wc", opt_user);
                }
            }
            if (data.containsKey("xzjl_mb") && data.getString("xzjl_mb").length() > 0) {
                xzjl_mb = data.getString("xzjl_mb");
                if (!old.getString("xzjl_mb").equals(xzjl_mb)) {
                    sql = sql + " xzjl_mb='" + xzjl_mb + "' , ";
                    writeLog(mysql, old.getString("xzjl_mb") + "->" + xzjl_mb, "xzjl_mb", opt_user);
                }
            }
            if (data.containsKey("xzjl_wc") && data.getString("xzjl_wc").length() > 0) {
                xzjl_wc = data.getString("xzjl_wc");
                if (!old.getString("xzjl_wc").equals(xzjl_wc)) {
                    sql = sql + " xzjl_wc='" + xzjl_wc + "' , ";
                    writeLog(mysql, old.getString("xzjl_wc") + "->" + xzjl_wc, "xzjl_wc", opt_user);
                }
            }
            if (data.containsKey("xzcj_wc") && data.getString("xzcj_wc").length() > 0) {
                xzcj_wc = data.getString("xzcj_wc");
                if (!old.getString("xzcj_wc").equals(xzcj_wc)) {
                    sql = sql + " xzcj_wc='" + xzcj_wc + "' , ";
                    writeLog(mysql, old.getString("xzcj_wc") + "->" + xzcj_wc, "xzcj_wc", opt_user);
                }
            }
            if (data.containsKey("xzkb_wc") && data.getString("xzkb_wc").length() > 0) {
                xzkb_wc = data.getString("xzkb_wc");
                if (!old.getString("xzkb_wc").equals(xzkb_wc)) {
                    sql = sql + " xzkb_wc='" + xzkb_wc + "' , ";
                    writeLog(mysql, old.getString("xzkb_wc") + "->" + xzkb_wc, "xzkb_wc", opt_user);
                }
            }
            if (data.containsKey("zbqb_mb") && data.getString("zbqb_mb").length() > 0) {
                zbqb_mb = data.getString("zbqb_mb");
                if (!old.getString("zbqb_mb").equals(zbqb_mb)) {
                    sql = sql + " zbqb_mb='" + zbqb_mb + "' , ";
                    writeLog(mysql, old.getString("zbqb_mb") + "->" + zbqb_mb, "zbqb_mb", opt_user);
                }
            }
            if (data.containsKey("zbqb_wc") && data.getString("zbqb_wc").length() > 0) {
                zbqb_wc = data.getString("zbqb_wc");
                if (!old.getString("zbqb_wc").equals(zbqb_wc)) {
                    sql = sql + " zbqb_wc='" + zbqb_wc + "' , ";
                    writeLog(mysql, old.getString("zbqb_wc") + "->" + zbqb_wc, "zbqb_wc", opt_user);
                }
            }
            if (data.containsKey("zbqb_wcl") && data.getString("zbqb_wcl").length() > 0) {
                zbqb_wcl = data.getString("zbqb_wcl");
                if (!old.getString("zbqb_wcl").equals(zbqb_wcl)) {
                    sql = sql + " zbqb_wcl='" + zbqb_wcl + "' , ";
                    writeLog(mysql, old.getString("zbqb_wcl") + "->" + zbqb_wcl, "zbqb_wcl", opt_user);
                }
            }
            if (data.containsKey("qbxshc_xfs") && data.getString("qbxshc_xfs").length() > 0) {
                qbxshc_xfs = data.getString("qbxshc_xfs");
                if (!old.getString("qbxshc_xfs").equals(qbxshc_xfs)) {
                    sql = sql + " qbxshc_xfs='" + qbxshc_xfs + "' , ";
                    writeLog(mysql, old.getString("qbxshc_xfs") + "->" + qbxshc_xfs, "qbxshc_xfs", opt_user);
                }
            }
            if (data.containsKey("qbxshc_wcs") && data.getString("qbxshc_wcs").length() > 0) {
                qbxshc_wcs = data.getString("qbxshc_wcs");
                if (!old.getString("qbxshc_wcs").equals(qbxshc_wcs)) {
                    sql = sql + " qbxshc_wcs='" + qbxshc_wcs + "' , ";
                    writeLog(mysql, old.getString("qbxshc_wcs") + "->" + qbxshc_wcs, "qbxshc_wcs", opt_user);
                }
            }
            if (data.containsKey("qbxshc_rws") && data.getString("qbxshc_rws").length() > 0) {
                qbxshc_rws = data.getString("qbxshc_rws");
                if (!old.getString("qbxshc_rws").equals(qbxshc_rws)) {
                    sql = sql + " qbxshc_rws='" + qbxshc_rws + "' , ";
                    writeLog(mysql, old.getString("qbxshc_rws") + "->" + qbxshc_rws, "qbxshc_rws", opt_user);
                }
            }
            if (data.containsKey("qbxxbs_wcs") && data.getString("qbxxbs_wcs").length() > 0) {
                qbxxbs_wcs = data.getString("qbxxbs_wcs");
                if (!old.getString("qbxxbs_wcs").equals(qbxxbs_wcs)) {
                    sql = sql + " qbxxbs_wcs='" + qbxxbs_wcs + "' , ";
                    writeLog(mysql, old.getString("qbxxbs_wcs") + "->" + qbxxbs_wcs, "qbxxbs_wcs", opt_user);
                }
            }
            if (data.containsKey("myd_wc") && data.getString("myd_wc").length() > 0) {
                myd_wc = data.getString("myd_wc");
                if (!old.getString("myd_wc").equals(myd_wc)) {
                    sql = sql + " myd_wc='" + myd_wc + "' , ";
                    writeLog(mysql, old.getString("myd_wc") + "->" + myd_wc, "myd_wc", opt_user);
                }
            }
            if (data.containsKey("zgxx_df") && data.getString("zgxx_df").length() > 0) {
                zgxx_df = data.getString("zgxx_df");
                if (!old.getString("zgxx_df").equals(zgxx_df)) {
                    sql = sql + " zgxx_df='" + zgxx_df + "' , ";
                    writeLog(mysql, old.getString("zgxx_df") + "->" + zgxx_df, "zgxx_df", opt_user);
                }
            }
            if (data.containsKey("zgxx_pm") && data.getString("zgxx_pm").length() > 0) {
                zgxx_pm = data.getString("zgxx_pm");
                if (!old.getString("zgxx_pm").equals(zgxx_pm)) {
                    sql = sql + " zgxx_pm='" + zgxx_pm + "' , ";
                    writeLog(mysql, old.getString("zgxx_pm") + "->" + zgxx_pm, "zgxx_pm", opt_user);
                }
            }
            if (data.containsKey("xwmt_df") && data.getString("xwmt_df").length() > 0) {
                xwmt_df = data.getString("xwmt_df");
                if (!old.getString("xwmt_df").equals(xwmt_df)) {
                    sql = sql + " xwmt_df='" + xwmt_df + "' , ";
                    writeLog(mysql, old.getString("xwmt_df") + "->" + xwmt_df, "xwmt_df", opt_user);
                }
            }
            if (data.containsKey("xwmt_pm") && data.getString("xwmt_pm").length() > 0) {
                xwmt_pm = data.getString("xwmt_pm");
                if (!old.getString("xwmt_pm").equals(xwmt_pm)) {
                    sql = sql + " xwmt_pm='" + xwmt_pm + "' , ";
                    writeLog(mysql, old.getString("xwmt_pm") + "->" + xwmt_pm, "xwmt_pm", opt_user);
                }
            }
            if (data.containsKey("dwzw_df") && data.getString("dwzw_df").length() > 0) {
                dwzw_df = data.getString("dwzw_df");
                if (!old.getString("dwzw_df").equals(dwzw_df)) {
                    sql = sql + " dwzw_df='" + dwzw_df + "' , ";
                    writeLog(mysql, old.getString("dwzw_df") + "->" + dwzw_df, "dwzw_df", opt_user);
                }
            }
            if (data.containsKey("dwzw_pm") && data.getString("dwzw_pm").length() > 0) {
                dwzw_pm = data.getString("dwzw_pm");
                if (!old.getString("dwzw_pm").equals(dwzw_pm)) {
                    sql = sql + " dwzw_pm='" + dwzw_pm + "' , ";
                    writeLog(mysql, old.getString("dwzw_pm") + "->" + dwzw_pm, "dwzw_pm", opt_user);
                }
            }
            if (data.containsKey("wzxx_df") && data.getString("wzxx_df").length() > 0) {
                wzxx_df = data.getString("wzxx_df");
                if (!old.getString("wzxx_df").equals(wzxx_df)) {
                    sql = sql + " wzxx_df='" + wzxx_df + "' , ";
                    writeLog(mysql, old.getString("wzxx_df") + "->" + wzxx_df, "wzxx_df", opt_user);
                }
            }
            if (data.containsKey("wzxx_pm") && data.getString("wzxx_pm").length() > 0) {
                wzxx_pm = data.getString("wzxx_pm");
                if (!old.getString("wzxx_pm").equals(wzxx_pm)) {
                    sql = sql + " wzxx_pm='" + wzxx_pm + "' , ";
                    writeLog(mysql, old.getString("wzxx_pm") + "->" + wzxx_pm, "wzxx_pm", opt_user);
                }
            }
            if (data.containsKey("jbly_wc") && data.getString("jbly_wc").length() > 0) {
                jbly_wc = data.getString("jbly_wc");
                if (!old.getString("jbly_wc").equals(jbly_wc)) {
                    sql = sql + " jbly_wc='" + jbly_wc + "' , ";
                    writeLog(mysql, old.getString("jbly_wc") + "->" + jbly_wc, "jbly_wc", opt_user);
                }
            }

            String sqls = "update push_basic set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(455003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private JSONObject getBoost(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";

            String id = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

            } else {
                return ErrNo.set(454006);

            }

            String sqls = "select * from push_boost where id='" + id + "'";
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);

            back.put("data", RelaInfo(list, mysql));

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(454005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private Object RelaInfo(List<JSONObject> list, InfoModelHelper mysql) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private JSONObject updateBoost(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String xzks_xdmb = "";
            String xzks_wc = "";
            String xzks_wcl = "";
            String xzks_pm = "";
            String gszs_xdmb = "";
            String gszs_wc = "";
            String gszs_wcl = "";
            String gszs_pm = "";
            String sqgs_xdmb = "";
            String sqgs_wc = "";
            String sqgs_wcl = "";
            String sqgs_pm = "";
            String sdgs_xdmb = "";
            String sdgs_wc = "";
            String sdgs_wcl = "";
            String sdgs_pm = "";
            String xdryccxdmb = "";
            String xdryccwc = "";
            String xdryccwcl = "";
            String xdryccpm = "";
            String gszs_mb = "";
            String gszs_wc1 = "";
            String ctqcgs_mb = "";
            String ctqcgs_wc = "";
            String zpgs_mb = "";
            String zpgs_wc = "";
            String hdcgs_mb = "";
            String hdcgs_wc = "";
            String wlfzgs_mb = "";
            String wlfzgs_wc = "";
            String jzgs_mb = "";
            String jzgs_wc = "";
            String zscqgs_mb = "";
            String zscqgs_wc = "";
            String sfajbl_mb = "";
            String sfajbl_wc = "";
            String zadc_mb = "";
            String zadc_wc = "";
            String mbzf = "";
            String zdf = "";
            String wcl = "";
            String ctqc = "";
            String sqgs = "";
            String jhqz = "";
            String sygs = "";
            String zsjgs = "";
            String hzgs = "";
            String swgs = "";
            String cdgs = "";
            String xdrycc = "";
            String sxgs = "";
            String gszs = "";
            String fhgbj = "";
            String opt_user = "";

            JSONObject old = new JSONObject();
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(454006);
            }
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

                String s = "select * from push_boost where id='" + id + "'";
                List<JSONObject> ll = mysql.query(s);

                if (ll.size() > 0) {
                    old = ll.get(0);
                } else {
                    s = "INSERT INTO `push_boost`  VALUES " +
                            "('" + id + "', '','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','1');";
                    mysql.update(s);
                    s = "select * from push_boost where id='" + id + "'";
                    ll = mysql.query(s);
                    old = ll.get(0);
                }

            } else {
                id = new SimpleDateFormat("yyyy").format(new Date());
                String s = "INSERT INTO `push_boost`  VALUES " +
                        "('" + id + "','', '','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','1');";
                mysql.update(s);
                s = "select * from push_boost where id='" + id + "'";
                List<JSONObject> ll = mysql.query(s);
                old = ll.get(0);
            }
            if (data.containsKey("xdrycc") && data.getString("xdrycc").length() > 0) {
                xdrycc = data.getString("xdrycc");
                if (!old.getString("xdrycc").equals(xdrycc)) {
                    sql = sql + " xdrycc='" + xdrycc + "' , ";
                    writeLog(mysql, old.getString("xdrycc") + "->" + xdrycc, "xdrycc", opt_user);
                }
            }
            if (data.containsKey("fhgbj") && data.getString("fhgbj").length() > 0) {
                fhgbj = data.getString("fhgbj");
                if (!old.getString("fhgbj").equals(fhgbj)) {
                    sql = sql + "fhgbj='" + fhgbj + "' , ";
                    writeLog(mysql, old.getString("fhgbj") + "->" + fhgbj, "fhgbj", opt_user);
                }
            }

            if (data.containsKey("sxgs") && data.getString("sxgs").length() > 0) {
                sxgs = data.getString("sxgs");
                if (!old.getString("sxgs").equals(sxgs)) {
                    sql = sql + "sxgs='" + sxgs + "' , ";
                    writeLog(mysql, old.getString("sxgs") + "->" + sxgs, "sxgs", opt_user);
                }
            }
            if (data.containsKey("gszs") && data.getString("gszs").length() > 0) {
                gszs = data.getString("gszs");
                if (!old.getString("gszs").equals(gszs)) {
                    sql = sql + "gszs='" + gszs + "' , ";
                    writeLog(mysql, old.getString("gszs") + "->" + gszs, "gszs", opt_user);
                }
            }
            if (data.containsKey("xzks_xdmb") && data.getString("xzks_xdmb").length() > 0) {
                xzks_xdmb = data.getString("xzks_xdmb");
                if (!old.getString("xzks_xdmb").equals(xzks_xdmb)) {
                    sql = sql + " xzks_xdmb='" + xzks_xdmb + "' , ";
                    writeLog(mysql, old.getString("xzks_xdmb") + "->" + xzks_xdmb, "xzks_xdmb", opt_user);
                }
            }
            if (data.containsKey("xzks_wc") && data.getString("xzks_wc").length() > 0) {
                xzks_wc = data.getString("xzks_wc");
                if (!old.getString("xzks_wc").equals(xzks_wc)) {
                    sql = sql + " xzks_wc='" + xzks_wc + "' , ";
                    writeLog(mysql, old.getString("xzks_wc") + "->" + xzks_wc, "xzks_wc", opt_user);
                }
            }
            if (data.containsKey("xzks_wcl") && data.getString("xzks_wcl").length() > 0) {
                xzks_wcl = data.getString("xzks_wcl");
                if (!old.getString("xzks_wcl").equals(xzks_wcl)) {
                    sql = sql + " xzks_wcl='" + xzks_wcl + "' , ";
                    writeLog(mysql, old.getString("xzks_wcl") + "->" + xzks_wcl, "xzks_wcl", opt_user);
                }
            }
            if (data.containsKey("xzks_pm") && data.getString("xzks_pm").length() > 0) {
                xzks_pm = data.getString("xzks_pm");
                if (!old.getString("xzks_pm").equals(xzks_pm)) {
                    sql = sql + " xzks_pm='" + xzks_pm + "' , ";
                    writeLog(mysql, old.getString("xzks_pm") + "->" + xzks_pm, "xzks_pm", opt_user);
                }
            }
            if (data.containsKey("gszs_xdmb") && data.getString("gszs_xdmb").length() > 0) {
                gszs_xdmb = data.getString("gszs_xdmb");
                if (!old.getString("gszs_xdmb").equals(gszs_xdmb)) {
                    sql = sql + " gszs_xdmb='" + gszs_xdmb + "' , ";
                    writeLog(mysql, old.getString("gszs_xdmb") + "->" + gszs_xdmb, "gszs_xdmb", opt_user);
                }
            }
            if (data.containsKey("gszs_wc") && data.getString("gszs_wc").length() > 0) {
                gszs_wc = data.getString("gszs_wc");
                if (!old.getString("gszs_wc").equals(gszs_wc)) {
                    sql = sql + " gszs_wc='" + gszs_wc + "' , ";
                    writeLog(mysql, old.getString("gszs_wc") + "->" + gszs_wc, "gszs_wc", opt_user);
                }
            }
            if (data.containsKey("gszs_wcl") && data.getString("gszs_wcl").length() > 0) {
                gszs_wcl = data.getString("gszs_wcl");
                if (!old.getString("gszs_wcl").equals(gszs_wcl)) {
                    sql = sql + " gszs_wcl='" + gszs_wcl + "' , ";
                    writeLog(mysql, old.getString("gszs_wcl") + "->" + gszs_wcl, "gszs_wcl", opt_user);
                }
            }
            if (data.containsKey("gszs_pm") && data.getString("gszs_pm").length() > 0) {
                gszs_pm = data.getString("gszs_pm");
                if (!old.getString("gszs_pm").equals(gszs_pm)) {
                    sql = sql + " gszs_pm='" + gszs_pm + "' , ";
                    writeLog(mysql, old.getString("gszs_pm") + "->" + gszs_pm, "gszs_pm", opt_user);
                }
            }
            if (data.containsKey("sqgs_xdmb") && data.getString("sqgs_xdmb").length() > 0) {
                sqgs_xdmb = data.getString("sqgs_xdmb");
                if (!old.getString("sqgs_xdmb").equals(sqgs_xdmb)) {
                    sql = sql + " sqgs_xdmb='" + sqgs_xdmb + "' , ";
                    writeLog(mysql, old.getString("sqgs_xdmb") + "->" + sqgs_xdmb, "sqgs_xdmb", opt_user);
                }
            }
            if (data.containsKey("sqgs_wc") && data.getString("sqgs_wc").length() > 0) {
                sqgs_wc = data.getString("sqgs_wc");
                if (!old.getString("sqgs_wc").equals(sqgs_wc)) {
                    sql = sql + " sqgs_wc='" + sqgs_wc + "' , ";
                    writeLog(mysql, old.getString("sqgs_wc") + "->" + sqgs_wc, "sqgs_wc", opt_user);
                }
            }
            if (data.containsKey("sqgs_wcl") && data.getString("sqgs_wcl").length() > 0) {
                sqgs_wcl = data.getString("sqgs_wcl");
                if (!old.getString("sqgs_wcl").equals(sqgs_wcl)) {
                    sql = sql + " sqgs_wcl='" + sqgs_wcl + "' , ";
                    writeLog(mysql, old.getString("sqgs_wcl") + "->" + sqgs_wcl, "sqgs_wcl", opt_user);
                }
            }
            if (data.containsKey("sqgs_pm") && data.getString("sqgs_pm").length() > 0) {
                sqgs_pm = data.getString("sqgs_pm");
                if (!old.getString("sqgs_pm").equals(sqgs_pm)) {
                    sql = sql + " sqgs_pm='" + sqgs_pm + "' , ";
                    writeLog(mysql, old.getString("sqgs_pm") + "->" + sqgs_pm, "sqgs_pm", opt_user);
                }
            }
            if (data.containsKey("sdgs_xdmb") && data.getString("sdgs_xdmb").length() > 0) {
                sdgs_xdmb = data.getString("sdgs_xdmb");
                if (!old.getString("sdgs_xdmb").equals(sdgs_xdmb)) {
                    sql = sql + " sdgs_xdmb='" + sdgs_xdmb + "' , ";
                    writeLog(mysql, old.getString("sdgs_xdmb") + "->" + sdgs_xdmb, "sdgs_xdmb", opt_user);
                }
            }
            if (data.containsKey("sdgs_wc") && data.getString("sdgs_wc").length() > 0) {
                sdgs_wc = data.getString("sdgs_wc");
                if (!old.getString("sdgs_wc").equals(sdgs_wc)) {
                    sql = sql + " sdgs_wc='" + sdgs_wc + "' , ";
                    writeLog(mysql, old.getString("sdgs_wc") + "->" + sdgs_wc, "sdgs_wc", opt_user);
                }
            }
            if (data.containsKey("sdgs_wcl") && data.getString("sdgs_wcl").length() > 0) {
                sdgs_wcl = data.getString("sdgs_wcl");
                if (!old.getString("sdgs_wcl").equals(sdgs_wcl)) {
                    sql = sql + " sdgs_wcl='" + sdgs_wcl + "' , ";
                    writeLog(mysql, old.getString("sdgs_wcl") + "->" + sdgs_wcl, "sdgs_wcl", opt_user);
                }
            }
            if (data.containsKey("sdgs_pm") && data.getString("sdgs_pm").length() > 0) {
                sdgs_pm = data.getString("sdgs_pm");
                if (!old.getString("sdgs_pm").equals(sdgs_pm)) {
                    sql = sql + " sdgs_pm='" + sdgs_pm + "' , ";
                    writeLog(mysql, old.getString("sdgs_pm") + "->" + sdgs_pm, "sdgs_pm", opt_user);
                }
            }
            if (data.containsKey("xdryccxdmb") && data.getString("xdryccxdmb").length() > 0) {
                xdryccxdmb = data.getString("xdryccxdmb");
                if (!old.getString("xdryccxdmb").equals(xdryccxdmb)) {
                    sql = sql + " xdryccxdmb='" + xdryccxdmb + "' , ";
                    writeLog(mysql, old.getString("xdryccxdmb") + "->" + xdryccxdmb, "xdryccxdmb", opt_user);
                }
            }
            if (data.containsKey("xdryccwc") && data.getString("xdryccwc").length() > 0) {
                xdryccwc = data.getString("xdryccwc");
                if (!old.getString("xdryccwc").equals(xdryccwc)) {
                    sql = sql + " xdryccwc='" + xdryccwc + "' , ";
                    writeLog(mysql, old.getString("xdryccwc") + "->" + xdryccwc, "xdryccwc", opt_user);
                }
            }
            if (data.containsKey("xdryccwcl") && data.getString("xdryccwcl").length() > 0) {
                xdryccwcl = data.getString("xdryccwcl");
                if (!old.getString("xdryccwcl").equals(xdryccwcl)) {
                    sql = sql + " xdryccwcl='" + xdryccwcl + "' , ";
                    writeLog(mysql, old.getString("xdryccwcl") + "->" + xdryccwcl, "xdryccwcl", opt_user);
                }
            }
            if (data.containsKey("xdryccpm") && data.getString("xdryccpm").length() > 0) {
                xdryccpm = data.getString("xdryccpm");
                if (!old.getString("xdryccpm").equals(xdryccpm)) {
                    sql = sql + " xdryccpm='" + xdryccpm + "' , ";
                    writeLog(mysql, old.getString("xdryccpm") + "->" + xdryccpm, "xdryccpm", opt_user);
                }
            }
            if (data.containsKey("gszs_mb") && data.getString("gszs_mb").length() > 0) {
                gszs_mb = data.getString("gszs_mb");
                if (!old.getString("gszs_mb").equals(gszs_mb)) {
                    sql = sql + " gszs_mb='" + gszs_mb + "' , ";
                    writeLog(mysql, old.getString("gszs_mb") + "->" + gszs_mb, "gszs_mb", opt_user);
                }
            }
            if (data.containsKey("gszs_wc1") && data.getString("gszs_wc1").length() > 0) {
                gszs_wc1 = data.getString("gszs_wc1");
                if (!old.getString("gszs_wc1").equals(gszs_wc1)) {
                    sql = sql + " gszs_wc1='" + gszs_wc1 + "' , ";
                    writeLog(mysql, old.getString("gszs_wc1") + "->" + gszs_wc1, "gszs_wc1", opt_user);
                }
            }
            if (data.containsKey("ctqcgs_mb") && data.getString("ctqcgs_mb").length() > 0) {
                ctqcgs_mb = data.getString("ctqcgs_mb");
                if (!old.getString("ctqcgs_mb").equals(ctqcgs_mb)) {
                    sql = sql + " ctqcgs_mb='" + ctqcgs_mb + "' , ";
                    writeLog(mysql, old.getString("ctqcgs_mb") + "->" + ctqcgs_mb, "ctqcgs_mb", opt_user);
                }
            }
            if (data.containsKey("ctqcgs_wc") && data.getString("ctqcgs_wc").length() > 0) {
                ctqcgs_wc = data.getString("ctqcgs_wc");
                if (!old.getString("ctqcgs_wc").equals(ctqcgs_wc)) {
                    sql = sql + " ctqcgs_wc='" + ctqcgs_wc + "' , ";
                    writeLog(mysql, old.getString("ctqcgs_wc") + "->" + ctqcgs_wc, "ctqcgs_wc", opt_user);
                }
            }
            if (data.containsKey("zpgs_mb") && data.getString("zpgs_mb").length() > 0) {
                zpgs_mb = data.getString("zpgs_mb");
                if (!old.getString("zpgs_mb").equals(zpgs_mb)) {
                    sql = sql + " zpgs_mb='" + zpgs_mb + "' , ";
                    writeLog(mysql, old.getString("zpgs_mb") + "->" + zpgs_mb, "zpgs_mb", opt_user);
                }
            }
            if (data.containsKey("zpgs_wc") && data.getString("zpgs_wc").length() > 0) {
                zpgs_wc = data.getString("zpgs_wc");
                if (!old.getString("zpgs_wc").equals(zpgs_wc)) {
                    sql = sql + " zpgs_wc='" + zpgs_wc + "' , ";
                    writeLog(mysql, old.getString("zpgs_wc") + "->" + zpgs_wc, "zpgs_wc", opt_user);
                }
            }
            if (data.containsKey("hdcgs_mb") && data.getString("hdcgs_mb").length() > 0) {
                hdcgs_mb = data.getString("hdcgs_mb");
                if (!old.getString("hdcgs_mb").equals(hdcgs_mb)) {
                    sql = sql + " hdcgs_mb='" + hdcgs_mb + "' , ";
                    writeLog(mysql, old.getString("hdcgs_mb") + "->" + hdcgs_mb, "hdcgs_mb", opt_user);
                }
            }
            if (data.containsKey("hdcgs_wc") && data.getString("hdcgs_wc").length() > 0) {
                hdcgs_wc = data.getString("hdcgs_wc");
                if (!old.getString("hdcgs_wc").equals(hdcgs_wc)) {
                    sql = sql + " hdcgs_wc='" + hdcgs_wc + "' , ";
                    writeLog(mysql, old.getString("hdcgs_wc") + "->" + hdcgs_wc, "hdcgs_wc", opt_user);
                }
            }
            if (data.containsKey("wlfzgs_mb") && data.getString("wlfzgs_mb").length() > 0) {
                wlfzgs_mb = data.getString("wlfzgs_mb");
                if (!old.getString("wlfzgs_mb").equals(wlfzgs_mb)) {
                    sql = sql + " wlfzgs_mb='" + wlfzgs_mb + "' , ";
                    writeLog(mysql, old.getString("wlfzgs_mb") + "->" + wlfzgs_mb, "wlfzgs_mb", opt_user);
                }
            }
            if (data.containsKey("wlfzgs_wc") && data.getString("wlfzgs_wc").length() > 0) {
                wlfzgs_wc = data.getString("wlfzgs_wc");
                if (!old.getString("wlfzgs_wc").equals(wlfzgs_wc)) {
                    sql = sql + " wlfzgs_wc='" + wlfzgs_wc + "' , ";
                    writeLog(mysql, old.getString("wlfzgs_wc") + "->" + wlfzgs_wc, "wlfzgs_wc", opt_user);
                }
            }
            if (data.containsKey("jzgs_mb") && data.getString("jzgs_mb").length() > 0) {
                jzgs_mb = data.getString("jzgs_mb");
                if (!old.getString("jzgs_mb").equals(jzgs_mb)) {
                    sql = sql + " jzgs_mb='" + jzgs_mb + "' , ";
                    writeLog(mysql, old.getString("jzgs_mb") + "->" + jzgs_mb, "jzgs_mb", opt_user);
                }
            }
            if (data.containsKey("jzgs_wc") && data.getString("jzgs_wc").length() > 0) {
                jzgs_wc = data.getString("jzgs_wc");
                if (!old.getString("jzgs_wc").equals(jzgs_wc)) {
                    sql = sql + " jzgs_wc='" + jzgs_wc + "' , ";
                    writeLog(mysql, old.getString("jzgs_wc") + "->" + jzgs_wc, "jzgs_wc", opt_user);
                }
            }
            if (data.containsKey("zscqgs_mb") && data.getString("zscqgs_mb").length() > 0) {
                zscqgs_mb = data.getString("zscqgs_mb");
                if (!old.getString("zscqgs_mb").equals(zscqgs_mb)) {
                    sql = sql + " zscqgs_mb='" + zscqgs_mb + "' , ";
                    writeLog(mysql, old.getString("zscqgs_mb") + "->" + zscqgs_mb, "zscqgs_mb", opt_user);
                }
            }
            if (data.containsKey("zscqgs_wc") && data.getString("zscqgs_wc").length() > 0) {
                zscqgs_wc = data.getString("zscqgs_wc");
                if (!old.getString("zscqgs_wc").equals(zscqgs_wc)) {
                    sql = sql + " zscqgs_wc='" + zscqgs_wc + "' , ";
                    writeLog(mysql, old.getString("zscqgs_wc") + "->" + zscqgs_wc, "zscqgs_wc", opt_user);
                }
            }
            if (data.containsKey("sfajbl_mb") && data.getString("sfajbl_mb").length() > 0) {
                sfajbl_mb = data.getString("sfajbl_mb");
                if (!old.getString("sfajbl_mb").equals(sfajbl_mb)) {
                    sql = sql + " sfajbl_mb='" + sfajbl_mb + "' , ";
                    writeLog(mysql, old.getString("sfajbl_mb") + "->" + sfajbl_mb, "sfajbl_mb", opt_user);
                }
            }
            if (data.containsKey("sfajbl_wc") && data.getString("sfajbl_wc").length() > 0) {
                sfajbl_wc = data.getString("sfajbl_wc");
                if (!old.getString("sfajbl_wc").equals(sfajbl_wc)) {
                    sql = sql + " sfajbl_wc='" + sfajbl_wc + "' , ";
                    writeLog(mysql, old.getString("sfajbl_wc") + "->" + sfajbl_wc, "sfajbl_wc", opt_user);
                }
            }
            if (data.containsKey("zadc_mb") && data.getString("zadc_mb").length() > 0) {
                zadc_mb = data.getString("zadc_mb");
                if (!old.getString("zadc_mb").equals(zadc_mb)) {
                    sql = sql + " zadc_mb='" + zadc_mb + "' , ";
                    writeLog(mysql, old.getString("zadc_mb") + "->" + zadc_mb, "zadc_mb", opt_user);
                }
            }
            if (data.containsKey("zadc_wc") && data.getString("zadc_wc").length() > 0) {
                zadc_wc = data.getString("zadc_wc");
                if (!old.getString("zadc_wc").equals(zadc_wc)) {
                    sql = sql + " zadc_wc='" + zadc_wc + "' , ";
                    writeLog(mysql, old.getString("zadc_wc") + "->" + zadc_wc, "zadc_wc", opt_user);
                }
            }
            if (data.containsKey("mbzf") && data.getString("mbzf").length() > 0) {
                mbzf = data.getString("mbzf");
                if (!old.getString("mbzf").equals(mbzf)) {
                    sql = sql + " mbzf='" + mbzf + "' , ";
                    writeLog(mysql, old.getString("mbzf") + "->" + mbzf, "mbzf", opt_user);
                }
            }
            if (data.containsKey("zdf") && data.getString("zdf").length() > 0) {
                zdf = data.getString("zdf");
                if (!old.getString("zdf").equals(zdf)) {
                    sql = sql + " zdf='" + zdf + "' , ";
                    writeLog(mysql, old.getString("zdf") + "->" + zdf, "zdf", opt_user);
                }
            }
            if (data.containsKey("wcl") && data.getString("wcl").length() > 0) {
                wcl = data.getString("wcl");
                if (!old.getString("wcl").equals(wcl)) {
                    sql = sql + " wcl='" + wcl + "' , ";
                    writeLog(mysql, old.getString("wcl") + "->" + wcl, "wcl", opt_user);
                }
            }
            if (data.containsKey("ctqc") && data.getString("ctqc").length() > 0) {
                ctqc = data.getString("ctqc");
                if (!old.getString("ctqc").equals(ctqc)) {
                    sql = sql + " ctqc='" + ctqc + "' , ";
                    writeLog(mysql, old.getString("ctqc") + "->" + ctqc, "ctqc", opt_user);
                }
            }
            if (data.containsKey("sqgs") && data.getString("sqgs").length() > 0) {
                sqgs = data.getString("sqgs");
                if (!old.getString("sqgs").equals(sqgs)) {
                    sql = sql + " sqgs='" + sqgs + "' , ";
                    writeLog(mysql, old.getString("sqgs") + "->" + sqgs, "sqgs", opt_user);
                }
            }
            if (data.containsKey("jhqz") && data.getString("jhqz").length() > 0) {
                jhqz = data.getString("jhqz");
                if (!old.getString("jhqz").equals(jhqz)) {
                    sql = sql + " jhqz='" + jhqz + "' , ";
                    writeLog(mysql, old.getString("jhqz") + "->" + jhqz, "jhqz", opt_user);
                }
            }
            if (data.containsKey("sygs") && data.getString("sygs").length() > 0) {
                sygs = data.getString("sygs");
                if (!old.getString("sygs").equals(sygs)) {
                    sql = sql + " sygs='" + sygs + "' , ";
                    writeLog(mysql, old.getString("sygs") + "->" + sygs, "sygs", opt_user);
                }
            }
            if (data.containsKey("zsjgs") && data.getString("zsjgs").length() > 0) {
                zsjgs = data.getString("zsjgs");
                if (!old.getString("zsjgs").equals(zsjgs)) {
                    sql = sql + " zsjgs='" + zsjgs + "' , ";
                    writeLog(mysql, old.getString("zsjgs") + "->" + zsjgs, "zsjgs", opt_user);
                }
            }
            if (data.containsKey("hzgs") && data.getString("hzgs").length() > 0) {
                hzgs = data.getString("hzgs");
                if (!old.getString("hzgs").equals(hzgs)) {
                    sql = sql + " hzgs='" + hzgs + "' , ";
                    writeLog(mysql, old.getString("hzgs") + "->" + hzgs, "hzgs", opt_user);
                }
            }
            if (data.containsKey("swgs") && data.getString("swgs").length() > 0) {
                swgs = data.getString("swgs");
                if (!old.getString("swgs").equals(swgs)) {
                    sql = sql + " swgs='" + swgs + "' , ";
                    writeLog(mysql, old.getString("swgs") + "->" + swgs, "swgs", opt_user);
                }
            }
            if (data.containsKey("cdgs") && data.getString("cdgs").length() > 0) {
                cdgs = data.getString("cdgs");
                if (!old.getString("cdgs").equals(cdgs)) {
                    sql = sql + " cdgs='" + cdgs + "' , ";
                    writeLog(mysql, old.getString("cdgs") + "->" + cdgs, "cdgs", opt_user);
                }
            }


            String sqls = "update push_boost set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(454003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject getPushLog(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";

            String id = "";
            String colName = "";
            String start_time = "";
            String end_time = "";
            String user = "";
            int limit = 20;
            int page = 1;

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + "a.id='" + id + "' and";
            }
            if (data.containsKey("colName") && data.getString("colName").length() > 0) {
                colName = data.getString("colName");
                sql = sql + "a.colName like '%" + colName + "%' and";
            }

            if (data.containsKey("user") && data.getString("user").length() > 0) {
                user = data.getString("user");
                sql = sql + "a.opt_user='" + user + "' and";
            }
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0 && data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                start_time = data.getString("start_time");
                end_time = data.getString("end_time");
                sql = sql + "(a.time>='" + start_time + "' and a.time<='" + end_time + "') and";
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            String sqls = "select a.*, name " +
                    "from push_log a left join user b on a.opt_user=b.id " +
                    "where 1=1 and " + sql + " 1=1 order by time desc  limit " + limit + " offset " + limit * (page - 1);
            logger.warn(sqls);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaLog(list));
                back.put("count", mysql.query_count("select count(id) as count " +
                        "from push_log a where 1=1 and " + sql + " 1=1"));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(449005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static List<JSONObject> RelaLog(List<JSONObject> list) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String colName = one.getString("colName");
            if (colName.equals("ci:week_score")) {
                one.put("colNames", "核心：本周得分");
            } else if (colName.equals("ci:week_rank")) {
                one.put("colNames", "核心：本周排名");
            } else if (colName.equals("ci:month_score")) {
                one.put("colNames", "核心：本月得分");
            } else if (colName.equals("ci:month_rakn")) {
                one.put("colNames", "核心：本月排名");
            } else if (colName.equals("ci:year_score")) {
                one.put("colNames", "核心：全年得分");
            } else if (colName.equals("ci:year_rank")) {
                one.put("colNames", "核心：排名");
            } else if (colName.equals("ci:ys_f")) {
                one.put("colNames", "核心：移诉完成");
            } else if (colName.equals("ci:ys_s")) {
                one.put("colNames", "核心：移诉得分");
            } else if (colName.equals("ci:ys_g")) {
                one.put("colNames", "核心：移诉目标");
            } else if (colName.equals("ci:sw_f")) {
                one.put("colNames", "核心：涉危公诉完成");
            } else if (colName.equals("ci:sw_s")) {
                one.put("colNames", "核心：涉危公诉得分");
            } else if (colName.equals("ci:sw_g")) {
                one.put("colNames", "核心：涉危公诉目标");
            } else if (colName.equals("ci:qc_f")) {
                one.put("colNames", "核心：传统侵财公诉完成");
            } else if (colName.equals("ci:qc_s")) {
                one.put("colNames", "核心：传统侵财公诉得分");
            } else if (colName.equals("ci:qc_g")) {
                one.put("colNames", "核心：传统侵财公诉目标");
            } else if (colName.equals("ci:sd_f")) {
                one.put("colNames", "核心：涉毒公诉完成");
            } else if (colName.equals("ci:sd_s")) {
                one.put("colNames", "核心：涉毒公诉得分");
            } else if (colName.equals("ci:sd_g")) {
                one.put("colNames", "核心：涉毒公诉目标");
            } else if (colName.equals("ci:qc5_f")) {
                one.put("colNames", "核心：传统侵财判五完成");
            } else if (colName.equals("ci:qc5_s")) {
                one.put("colNames", "核心：传统侵财判五得分");
            } else if (colName.equals("ci:qc5_g")) {
                one.put("colNames", "核心：传统侵财判五目标");
            } else if (colName.equals("ci:xc_f")) {
                one.put("colNames", "核心：吸毒人员查出完成");
            } else if (colName.equals("ci:xd_s")) {
                one.put("colNames", "核心：吸毒人员查出得分");
            } else if (colName.equals("ci:xd_g")) {
                one.put("colNames", "核心：吸毒人员查出目标");
            } else if (colName.equals("ci:zp_f")) {
                one.put("colNames", "核心：诈骗公诉完成");
            } else if (colName.equals("ci:zp_s")) {
                one.put("colNames", "核心：诈骗公诉得分");
            } else if (colName.equals("ci:zp_g")) {
                one.put("colNames", "核心：诈骗公诉目标");
            } else if (colName.equals("ci:hdc_f")) {
                one.put("colNames", "核心：黄赌娼公诉完成");
            } else if (colName.equals("ci:hdc_s")) {
                one.put("colNames", "核心：黄赌娼公诉得分");
            } else if (colName.equals("ci:hdc_g")) {
                one.put("colNames", "核心：黄赌娼公诉目标");
            } else if (colName.equals("ci:jwhl_f")) {
                one.put("colNames", "核心：境外回流公诉完成");
            } else if (colName.equals("ci:jwhl_s")) {
                one.put("colNames", "核心：境外回流公诉得分");
            } else if (colName.equals("ci:jwhl_g")) {
                one.put("colNames", "核心：境外回流公诉目标");
            } else if (colName.equals("ci:wl_f")) {
                one.put("colNames", "核心：网络犯罪公诉完成");
            } else if (colName.equals("ci:wl_s")) {
                one.put("colNames", "核心：网络犯罪公诉得分");
            } else if (colName.equals("ci:wl_g")) {
                one.put("colNames", "核心：网络犯罪公诉目标");
            } else if (colName.equals("ci:he_f")) {
                one.put("colNames", "核心：黑恶判决数完成");
            } else if (colName.equals("ci:he_s")) {
                one.put("colNames", "核心：黑恶判决数得分");
            } else if (colName.equals("ci:he_g")) {
                one.put("colNames", "核心：黑恶判决数目标");
            } else if (colName.equals("ci:bx_f")) {
                one.put("colNames", "核心：帮信公诉完成");
            } else if (colName.equals("ci:bx_s")) {
                one.put("colNames", "核心：帮信公诉得分");
            } else if (colName.equals("ci:bx_g")) {
                one.put("colNames", "核心：帮信公诉目标");
            } else if (colName.equals("ci:sq_f")) {
                one.put("colNames", "核心：涉枪公诉完成");
            } else if (colName.equals("ci:sq_s")) {
                one.put("colNames", "核心：涉枪公诉得分");
            } else if (colName.equals("ci:sq_g")) {
                one.put("colNames", "核心：涉枪公诉目标");
            } else if (colName.equals("ci:jz_f")) {
                one.put("colNames", "核心：经侦公诉完成");
            } else if (colName.equals("ci:jz_s")) {
                one.put("colNames", "核心：经侦公诉得分");
            } else if (colName.equals("ci:jz_g")) {
                one.put("colNames", "核心：经侦公诉目标");
            } else if (colName.equals("ci:jh_f")) {
                one.put("colNames", "核心：缴获枪支完成");
            } else if (colName.equals("ci:jh_s")) {
                one.put("colNames", "核心：缴获枪支得分");
            } else if (colName.equals("ci:jh_g")) {
                one.put("colNames", "核心：缴获枪支目标");
            } else if (colName.equals("ci:zs_f")) {
                one.put("colNames", "核心：侵犯知识产权公诉完成");
            } else if (colName.equals("ci:zs_s")) {
                one.put("colNames", "核心：侵犯知识产权公诉得分");
            } else if (colName.equals("ci:zs_g")) {
                one.put("colNames", "核心：侵犯知识产权公诉目标");
            } else if (colName.equals("ci:sp_f")) {
                one.put("colNames", "核心：食品公诉完成");
            } else if (colName.equals("ci:sp_s")) {
                one.put("colNames", "核心：食品公诉得分");
            } else if (colName.equals("ci:sp_g")) {
                one.put("colNames", "核心：食品公诉目标");
            } else if (colName.equals("ci:sx_f")) {
                one.put("colNames", "核心：涉邪公诉完成");
            } else if (colName.equals("ci:sx_s")) {
                one.put("colNames", "核心：涉邪公诉得分");
            } else if (colName.equals("ci:sx_g")) {
                one.put("colNames", "核心：涉邪公诉目标");
            } else if (colName.equals("ci:gbj_f")) {
                one.put("colNames", "核心：妨害国边境完成");
            } else if (colName.equals("ci:gbj_s")) {
                one.put("colNames", "核心：妨害国边境得分");
            } else if (colName.equals("ci:gbj_g")) {
                one.put("colNames", "核心：妨害国边境目标");
            } else if (colName.equals("ci:yp_f")) {
                one.put("colNames", "核心：药品公诉完成");
            } else if (colName.equals("ci:yp_s")) {
                one.put("colNames", "核心：药品公诉得分");
            } else if (colName.equals("ci:yp_g")) {
                one.put("colNames", "核心：药品公诉目标");
            } else if (colName.equals("ci:hj_f")) {
                one.put("colNames", "核心：环境公诉完成");
            } else if (colName.equals("ci:hj_s")) {
                one.put("colNames", "核心：环境公诉得分");
            } else if (colName.equals("ci:hj_g")) {
                one.put("colNames", "核心：环境公诉目标");
            } else if (colName.equals("ci:fsa_f")) {
                one.put("colNames", "核心：三非案完成");
            } else if (colName.equals("ci:fsa_s")) {
                one.put("colNames", "核心：三非案得分");
            } else if (colName.equals("ci:fsa_g")) {
                one.put("colNames", "核心：三非案目标");
            } else if (colName.equals("ci:zy_f")) {
                one.put("colNames", "核心：资源公诉完成");
            } else if (colName.equals("ci:zy_s")) {
                one.put("colNames", "核心：资源公诉得分");
            } else if (colName.equals("ci:zy_g")) {
                one.put("colNames", "核心：资源公诉目标");
            } else if (colName.equals("ci:sj_f")) {
                one.put("colNames", "核心：制售假完成");
            } else if (colName.equals("ci:sj_s")) {
                one.put("colNames", "核心：制售假得分");
            } else if (colName.equals("ci:sj_g")) {
                one.put("colNames", "核心：制售假目标");
            } else if (colName.equals("ci:za_f")) {
                one.put("colNames", "核心：治安打处完成");
            } else if (colName.equals("ci:za_s")) {
                one.put("colNames", "核心：治安打处得分");
            } else if (colName.equals("ci:za_g")) {
                one.put("colNames", "核心：治安打处目标");
            } else if (colName.equals("bc:kqy_g")) {
                one.put("colNames", "基础：跨区域公诉目标");
            } else if (colName.equals("bc:kqy_f")) {
                one.put("colNames", "基础：跨区域公诉完成");
            } else if (colName.equals("bc:kqy_fq")) {
                one.put("colNames", "基础：跨区域公诉完成率");
            } else if (colName.equals("bc:dxwl_c")) {
                one.put("colNames", "基础：电信网络诈骗案件压降今年");
            } else if (colName.equals("bc:dxwl_l")) {
                one.put("colNames", "基础：电信网络诈骗案件压降去年");
            } else if (colName.equals("bc:dxwl_t")) {
                one.put("colNames", "基础：电信网络诈骗案件压降同比");
            } else if (colName.equals("bc:dk_hl")) {
                one.put("colNames", "基础：断卡行动涉案银行卡开户数");
            } else if (colName.equals("bc:dk_c")) {
                one.put("colNames", "基础：断卡行动核查数");
            } else if (colName.equals("bc:dk_q")) {
                one.put("colNames", "基础：断卡行动核查率");
            } else if (colName.equals("bc:xzzt_c")) {
                one.put("colNames", "基础：刑侦追逃得分");
            } else if (colName.equals("bc:zxxt_r")) {
                one.put("colNames", "基础：刑侦追逃排名");
            } else if (colName.equals("bc:dxwl_xkl")) {
                one.put("colNames", "基础：电信网络诈骗案件现场勘查质量电诈案件现勘率");
            } else if (colName.equals("bc:dxwl_tql")) {
                one.put("colNames", "基础：电信网络诈骗案件现场勘查质量涉诈APK、涉案语音提取率");
            } else if (colName.equals("bc:dxwl_jxl")) {
                one.put("colNames", "基础：电信网络诈骗案件现场勘查质量涉诈 APK解析率");
            } else if (colName.equals("bc:rsry_cj")) {
                one.put("colNames", "基础：入所人员基础信息采集质量应采数");
            } else if (colName.equals("bc:rsry_sw")) {
                one.put("colNames", "基础：入所人员基础信息采集质量声纹实际");
            } else if (colName.equals("bc:rsry_hm")) {
                one.put("colNames", "基础：入所人员基础信息采集质量虹膜实际");
            } else if (colName.equals("bc:rsry_zw")) {
                one.put("colNames", "基础：入所人员基础信息采集质量指纹实际");
            } else if (colName.equals("bc:rsry_dna")) {
                one.put("colNames", "基础：入所人员基础信息采集质量DNA 实际");
            } else if (colName.equals("bc:bzdz_g")) {
                one.put("colNames", "基础：标准地址准确采集关联目标关联率");
            } else if (colName.equals("bc:bzdz_f")) {
                one.put("colNames", "基础：标准地址准确采集关联完成率");
            } else if (colName.equals("bc:js_g")) {
                one.put("colNames", "基础：监所在押人员漏采率目标漏采率");
            } else if (colName.equals("bc:js_f")) {
                one.put("colNames", "基础：监所在押人员漏采率完成率");
            } else if (colName.equals("bc:ll_g")) {
                one.put("colNames", "基础：六类管控对象列管率目标列管率");
            } else if (colName.equals("bc:ll_f")) {
                one.put("colNames", "基础：六类管控对象列管率完成率");
            } else if (colName.equals("bc:lgzd_g")) {
                one.put("colNames", "基础：列管重点人员管控率目标");
            } else if (colName.equals("bc:lgzd_f")) {
                one.put("colNames", "基础：列管重点人员管控率完成率");
            } else if (colName.equals("bc:pa_g")) {
                one.put("colNames", "基础：“平安前哨”达标建设目标数");
            } else if (colName.equals("bc:pa_f")) {
                one.put("colNames", "基础：“平安前哨”达标建设完成数");
            } else if (colName.equals("bc:sqzf_g")) {
                one.put("colNames", "基础：社区民警走访率上门率");
            } else if (colName.equals("bc:sqzf_f")) {
                one.put("colNames", "基础：社区民警走访率完成率");
            } else if (colName.equals("bc:xfaq_g")) {
                one.put("colNames", "基础：消防安全监督检查率目标值");
            } else if (colName.equals("bc:xfaq_f")) {
                one.put("colNames", "基础：消防安全监督检查率完成率");
            } else if (colName.equals("bc:hzrw_g")) {
                one.put("colNames", "基础：监管单位火灾亡人事故目标值");
            } else if (colName.equals("bc:hzrw_r")) {
                one.put("colNames", "基础：监管单位火灾亡人事故实际数");
            } else if (colName.equals("bc:jd_g")) {
                one.put("colNames", "基础：剧毒、易制爆储存场所目标数");
            } else if (colName.equals("bc:jd_f")) {
                one.put("colNames", "基础：剧毒、易制爆储存场所完成数");
            } else if (colName.equals("bc:zazd_g")) {
                one.put("colNames", "基础：治安重点单位安防达标率目标值");
            } else if (colName.equals("bc:zaad_f")) {
                one.put("colNames", "基础：治安重点单位安防达标率完成率");
            } else if (colName.equals("bc:zaff_g")) {
                one.put("colNames", "基础：治安防范达标率目标值");
            } else if (colName.equals("bc:zaff_f")) {
                one.put("colNames", "基础：治安防范达标率完成率");
            } else if (colName.equals("bc:sqjbz_f")) {
                one.put("colNames", "基础：社区民警进班子目标率");
            } else if (colName.equals("bc:sqjbz_g")) {
                one.put("colNames", "基础：社区民警进班子完成率");
            } else if (colName.equals("bc:qz_g")) {
                one.put("colNames", "基础：群租房房东、二房东目标");
            } else if (colName.equals("bc:qz_f")) {
                one.put("colNames", "基础：群租房房东、二房东完成率");
            } else if (colName.equals("bc:ldrk_g")) {
                one.put("colNames", "基础：流动人口信息采集登记率目标");
            } else if (colName.equals("bc:ldrk_f")) {
                one.put("colNames", "基础：流动人口信息采集登记率完成");
            } else if (colName.equals("bc:jffk_g")) {
                one.put("colNames", "基础：矛盾纠纷风险化解反馈率目标");
            } else if (colName.equals("bc:jffk_f")) {
                one.put("colNames", "基础：矛盾纠纷风险化解反馈率完成");
            } else if (colName.equals("bc:jfhj_g")) {
                one.put("colNames", "基础：矛盾纠纷风险化解化解率目标");
            } else if (colName.equals("bc:jfhj_f")) {
                one.put("colNames", "基础：矛盾纠纷风险化解化解率完成");
            } else if (colName.equals("bc:yslr_g")) {
                one.put("colNames", "基础：打造一流营商环境平台录入目标");
            } else if (colName.equals("bc:yslr_f")) {
                one.put("colNames", "基础：打造一流营商环境平台录入完成");
            } else if (colName.equals("bc:ysjy_g")) {
                one.put("colNames", "基础：打造一流营商环境案件压降目标");
            } else if (colName.equals("bc:ysjy_f")) {
                one.put("colNames", "基础：打造一流营商环境案件压降完成");
            } else if (colName.equals("bc:ysnb_g")) {
                one.put("colNames", "基础：打造一流营商环境内保行政安检目标");
            } else if (colName.equals("bc:ysnb_f")) {
                one.put("colNames", "基础：打造一流营商环境内保行政安检完成");
            } else if (colName.equals("bc:scza_g")) {
                one.put("colNames", "基础：涉娼治安处罚目标");
            } else if (colName.equals("bc:scza_f")) {
                one.put("colNames", "基础：涉娼治安处罚完成");
            } else if (colName.equals("bc:sdza_g")) {
                one.put("colNames", "基础：涉赌治安处罚目标数");
            } else if (colName.equals("bc:sdza_f")) {
                one.put("colNames", "基础：涉赌治安处罚完成");
            } else if (colName.equals("bc:jb_g")) {
                one.put("colNames", "基础：收缴假币目标");
            } else if (colName.equals("bc:jb_f")) {
                one.put("colNames", "基础：收缴假币完成");
            } else if (colName.equals("bc:jz_g")) {
                one.put("colNames", "基础：经侦情报目标");
            } else if (colName.equals("bc:jz_f")) {
                one.put("colNames", "基础：经侦情报完成");
            } else if (colName.equals("bc:wlsl_g")) {
                one.put("colNames", "基础：网络四类目标");
            } else if (colName.equals("bc:wlsl_f")) {
                one.put("colNames", "基础：网络四类完成");
            } else if (colName.equals("bc:yw_g")) {
                one.put("colNames", "基础：义务类目标");
            } else if (colName.equals("bc:yw_f")) {
                one.put("colNames", "基础：义务类完成");
            } else if (colName.equals("bc:wlhc_g")) {
                one.put("colNames", "基础：网络灰产目标");
            } else if (colName.equals("bc:wlhc_f")) {
                one.put("colNames", "基础：网络灰产完成");
            } else if (colName.equals("bc:xzjl_g")) {
                one.put("colNames", "基础：行政拘留目标");
            } else if (colName.equals("bc:xzjl_f")) {
                one.put("colNames", "基础：行政拘留完成");
            } else if (colName.equals("bc:xzcj_g")) {
                one.put("colNames", "基础：行政查结目标");
            } else if (colName.equals("bc:xzcj_f")) {
                one.put("colNames", "基础：行政查结完成");
            } else if (colName.equals("bc:das_c")) {
                one.put("colNames", "基础：案管室排名本月");
            } else if (colName.equals("bc:das_l")) {
                one.put("colNames", "基础：案管室排名全年");
            } else if (colName.equals("bc:xscz_p")) {
                one.put("colNames", "基础：情报线索核查处置下发数");
            } else if (colName.equals("bc:xscz_f")) {
                one.put("colNames", "基础：情报线索核查处置完成数");
            } else if (colName.equals("bc:qbsz_g")) {
                one.put("colNames", "基础：情报信息报送质态任务数");
            } else if (colName.equals("bc:qbsz_f")) {
                one.put("colNames", "基础：情报信息报送质态完成数");
            } else if (colName.equals("bc:tn_g")) {
                one.put("colNames", "基础：全警体能目标");
            } else if (colName.equals("bc:tn_f")) {
                one.put("colNames", "基础：全警体能达标率");
            } else if (colName.equals("bc:zgdy_g")) {
                one.put("colNames", "基础：政工信息调研目标");
            } else if (colName.equals("bc:zgdy_y")) {
                one.put("colNames", "基础：政工信息调研录用");
            } else if (colName.equals("bc:dwzw_c")) {
                one.put("colNames", "基础：党委政务得分");
            } else if (colName.equals("bc:dwzw_r")) {
                one.put("colNames", "基础：党委政务排名");
            } else if (colName.equals("bc:wzxx_c")) {
                one.put("colNames", "基础：网站信息得分");
            } else if (colName.equals("bc:wzxx_r")) {
                one.put("colNames", "基础：网站信息排名");
            } else if (colName.equals("bc:jbly_g")) {
                one.put("colNames", "基础：天宁公安简报录用目标");
            } else if (colName.equals("bc:jbly_f")) {
                one.put("colNames", "基础：天宁公安简报录用完成");
            } else if (colName.equals("ps:ysdd")) {
                one.put("colNames", "建设进度：一室多队");
            } else if (colName.equals("ps:llpb")) {
                one.put("colNames", "建设进度：力量配备");
            } else if (colName.equals("ps:qs_g")) {
                one.put("colNames", "建设进度：签收反馈及时率目标");
            } else if (colName.equals("ps:qs_f")) {
                one.put("colNames", "建设进度：签收反馈及时率完成");
            } else if (colName.equals("ps:sqmj_g")) {
                one.put("colNames", "建设进度：社区民警占比目标");
            } else if (colName.equals("ps:sqmj_f")) {
                one.put("colNames", "建设进度：社区民警占比完成");
            } else if (colName.equals("ps:bazx")) {
                one.put("colNames", "建设进度：办案管理中心等级");
            } else if (colName.equals("ps:das")) {
                one.put("colNames", "建设进度：档案室星级");
            } else if (colName.equals("ps:dq_c")) {
                one.put("colNames", "建设进度：盗窃警情数量");
            } else if (colName.equals("ps:dq_tb")) {
                one.put("colNames", "建设进度：盗窃警情同比");
            } else if (colName.equals("ps:qz_c")) {
                one.put("colNames", "建设进度：诈骗警情数量");
            } else if (colName.equals("ps:qz_tb")) {
                one.put("colNames", "建设进度：诈骗警情同比");
            } else if (colName.equals("ps:sc_c")) {
                one.put("colNames", "建设进度：涉娼警情数量");
            } else if (colName.equals("ps:sc_tb")) {
                one.put("colNames", "建设进度：涉娼警情同比");
            } else if (colName.equals("ps:sd_c")) {
                one.put("colNames", "建设进度：涉赌警情数量");
            } else if (colName.equals("ps:sd_tb")) {
                one.put("colNames", "建设进度：涉赌警情同比");
            } else if (colName.equals("ps:zh_bz")) {
                one.put("colNames", "建设进度：娼赌警情转化比比值");
            } else if (colName.equals("ps:zh_tb")) {
                one.put("colNames", "建设进度：娼赌警情转化比同比");
            } else if (colName.equals("xzks_xdmb")) {
                one.put("colNames", "新抓可诉,行动目标");
            } else if (colName.equals("xzks_wc")) {
                one.put("colNames", "新抓可诉,完成");
            } else if (colName.equals("xzks_wcl")) {
                one.put("colNames", "新抓可诉,完成率");
            } else if (colName.equals("xzks_pm")) {
                one.put("colNames", "新抓可诉,排名");
            } else if (colName.equals("gszs_xdmb")) {
                one.put("colNames", "公诉总数,行动目标");
            } else if (colName.equals("gszs_wc")) {
                one.put("colNames", "公诉总数,完成");
            } else if (colName.equals("gszs_wcl")) {
                one.put("colNames", "公诉总数,完成率");
            } else if (colName.equals("gszs_pm")) {
                one.put("colNames", "公诉总数,排名");
            } else if (colName.equals("sqgs_xdmb")) {
                one.put("colNames", "涉枪公诉,行动目标");
            } else if (colName.equals("sqgs_wc")) {
                one.put("colNames", "涉枪公诉,完成");
            } else if (colName.equals("sqgs_wcl")) {
                one.put("colNames", "涉枪公诉,完成率");
            } else if (colName.equals("sqgs_pm")) {
                one.put("colNames", "涉枪公诉,排名");
            } else if (colName.equals("sdgs_xdmb")) {
                one.put("colNames", "涉毒公诉,行动目标");
            } else if (colName.equals("sdgs_wc")) {
                one.put("colNames", "涉毒公诉,完成");
            } else if (colName.equals("sdgs_wcl")) {
                one.put("colNames", "涉毒公诉,完成率");
            } else if (colName.equals("sdgs_pm")) {
                one.put("colNames", "涉毒公诉,排名");
            } else if (colName.equals("xdryccxdmb")) {
                one.put("colNames", "吸毒人员查处行动目标");
            } else if (colName.equals("xdryccwc")) {
                one.put("colNames", "吸毒人员查处完成");
            } else if (colName.equals("xdryccwcl")) {
                one.put("colNames", "吸毒人员查处完成率");
            } else if (colName.equals("xdryccpm")) {
                one.put("colNames", "吸毒人员查处排名");
            } else if (colName.equals("gszs_mb")) {
                one.put("colNames", "公诉总数,目标");
            } else if (colName.equals("gszs_wc1")) {
                one.put("colNames", "公诉总数,完成");
            } else if (colName.equals("ctqcgs_mb")) {
                one.put("colNames", "传统侵财公诉,目标");
            } else if (colName.equals("ctqcgs_wc")) {
                one.put("colNames", "传统侵财公诉,完成");
            } else if (colName.equals("zpgs_mb")) {
                one.put("colNames", "诈骗公诉,目标");
            } else if (colName.equals("zpgs_wc")) {
                one.put("colNames", "诈骗公诉,完成");
            } else if (colName.equals("hdcgs_mb")) {
                one.put("colNames", "黄赌娼公诉,目标");
            } else if (colName.equals("hdcgs_wc")) {
                one.put("colNames", "黄赌娼公诉,完成");
            } else if (colName.equals("wlfzgs_mb")) {
                one.put("colNames", "网络犯罪公诉,目标");
            } else if (colName.equals("wlfzgs_wc")) {
                one.put("colNames", "网络犯罪公诉,完成");
            } else if (colName.equals("jzgs_mb")) {
                one.put("colNames", "经侦公诉,目标");
            } else if (colName.equals("jzgs_wc")) {
                one.put("colNames", "经侦公诉,完成");
            } else if (colName.equals("zscqgs_mb")) {
                one.put("colNames", "知识产权公诉,目标");
            } else if (colName.equals("zscqgs_wc")) {
                one.put("colNames", "知识产权公诉,完成");
            } else if (colName.equals("sfajbl_mb")) {
                one.put("colNames", "三非案件办理,目标");
            } else if (colName.equals("sfajbl_wc")) {
                one.put("colNames", "三非案件办理,完成");
            } else if (colName.equals("zadc_mb")) {
                one.put("colNames", "治安打处,目标");
            } else if (colName.equals("zadc_wc")) {
                one.put("colNames", "治安打处,完成");
            } else if (colName.equals("mbzf")) {
                one.put("colNames", "目标总分");
            } else if (colName.equals("zdf")) {
                one.put("colNames", "总得分");
            } else if (colName.equals("wcl")) {
                one.put("colNames", "完成率");
            } else if (colName.equals("ctqc")) {
                one.put("colNames", "传统侵财");
            } else if (colName.equals("sqgs")) {
                one.put("colNames", "涉枪公诉");
            } else if (colName.equals("jhqz")) {
                one.put("colNames", "缴获枪支");
            } else if (colName.equals("sygs")) {
                one.put("colNames", "食药公诉");
            } else if (colName.equals("zsjgs")) {
                one.put("colNames", "制售假公诉");
            } else if (colName.equals("hzgs")) {
                one.put("colNames", "环资公诉");
            } else if (colName.equals("swgs")) {
                one.put("colNames", "涉危公诉");
            } else if (colName.equals("cdgs")) {
                one.put("colNames", "涉毒公诉");
            } else if (colName.equals("xdrycc")) {
                one.put("colNames", "吸毒人员查处");
            } else if (colName.equals("sxgs")) {
                one.put("colNames", "涉邪公诉");
            } else if (colName.equals("gszs")) {
                one.put("colNames", "公诉总数（超）");
            } else if (colName.equals("kygs_mb")) {
                one.put("colNames", "跨区域公诉,目标");
            } else if (colName.equals("kygs_wc")) {
                one.put("colNames", "跨区域公诉,完成");
            } else if (colName.equals("kygs_wcl")) {
                one.put("colNames", "跨区域公诉,完成率");
            } else if (colName.equals("dzyj_las")) {
                one.put("colNames", "电信网络诈骗案件压降,立案数");
            } else if (colName.equals("dzyj_21las")) {
                one.put("colNames", "电信网络诈骗案件压降,2021年立案数");
            } else if (colName.equals("dzyj_tb")) {
                one.put("colNames", "电信网络诈骗案件压降,同比");
            } else if (colName.equals("dkxd_yxkhs")) {
                one.put("colNames", "断卡行动,涉案银行卡开户数");
            } else if (colName.equals("dkxd_hcs")) {
                one.put("colNames", "断卡行动,核查数");
            } else if (colName.equals("dkxd_hcl")) {
                one.put("colNames", "断卡行动,核查率");
            } else if (colName.equals("xzzt_df")) {
                one.put("colNames", "刑侦追逃,得分");
            } else if (colName.equals("xzzt_pm")) {
                one.put("colNames", "刑侦追逃,排名");
            } else if (colName.equals("dzajxdl")) {
                one.put("colNames", "电诈案件现动率,");
            } else if (colName.equals("szapksayytql")) {
                one.put("colNames", "涉诈APK涉案语音提取率,");
            } else if (colName.equals("szapkjxl")) {
                one.put("colNames", "涉诈APK解析率,");
            } else if (colName.equals("ycs")) {
                one.put("colNames", "应采数,");
            } else if (colName.equals("sw")) {
                one.put("colNames", "声纹实际采集率,");
            } else if (colName.equals("hm")) {
                one.put("colNames", "虹膜实际采生率,");
            } else if (colName.equals("zw")) {
                one.put("colNames", "指纹实际采集率 ,");
            } else if (colName.equals("dna")) {
                one.put("colNames", "DNA实际采集丰,");
            } else if (colName.equals("bzdz")) {
                one.put("colNames", "标准地址准确采集关联,");
            } else if (colName.equals("jszy")) {
                one.put("colNames", "监所在押入员漏采率,");
            } else if (colName.equals("llgk")) {
                one.put("colNames", "六类管控对象列管率,");
            } else if (colName.equals("lgzd")) {
                one.put("colNames", "列管重点人员管控率,");
            } else if (colName.equals("sqmj")) {
                one.put("colNames", "社区民警走访率,");
            } else if (colName.equals("xfaq")) {
                one.put("colNames", "消防安全监督检查率,");
            } else if (colName.equals("hzwr")) {
                one.put("colNames", "监管单位火灾亡人事放,");
            } else if (colName.equals("jdyzb_wc")) {
                one.put("colNames", "剧毒、易制爆储存场所防范达标率,完成");
            } else if (colName.equals("jdyzb_mb")) {
                one.put("colNames", "剧毒、易制爆储存场所防范达标率,目标");
            } else if (colName.equals("zazd")) {
                one.put("colNames", "治安重点单位安防达标率,");
            } else if (colName.equals("dwxx")) {
                one.put("colNames", "单位信息采集,");
            } else if (colName.equals("mjjbz")) {
                one.put("colNames", "社区民警进班子,");
            } else if (colName.equals("qzefd")) {
                one.put("colNames", "群租房房东、二房东微安居申报率,");
            } else if (colName.equals("ldrkxxcjdjl")) {
                one.put("colNames", "流动人口信息采集登记率,");
            } else if (colName.equals("ldrkxxcjdjl")) {
                one.put("colNames", "矛盾纠纷风险化解及时反馈率,");
            } else if (colName.equals("mdjffxhjfkdbl")) {
                one.put("colNames", "矛盾纠纷风险化解反馈达标率,");
            } else if (colName.equals("ewmmp_wc")) {
                one.put("colNames", "二维码门牌建设,完成");
            } else if (colName.equals("ewmmp_mb")) {
                one.put("colNames", "二维码门牌建设,目标");
            } else if (colName.equals("sc_wc")) {
                one.put("colNames", "涉娼治安处罚,完成");
            } else if (colName.equals("sc_mb")) {
                one.put("colNames", "涉娼治安处罚,目标");
            } else if (colName.equals("sd_mb")) {
                one.put("colNames", "涉赌治安处罚,目标");
            } else if (colName.equals("sd_wc")) {
                one.put("colNames", "涉赌治安处罚,完成");
            } else if (colName.equals("nb_mb")) {
                one.put("colNames", "内保行政案件办理,目标");
            } else if (colName.equals("nb_wc")) {
                one.put("colNames", "内保行政案件办理,完成");
            } else if (colName.equals("cyry")) {
                one.put("colNames", "从业人员信急采集,");
            } else if (colName.equals("sjjb_mb")) {
                one.put("colNames", "收缴假币,目标");
            } else if (colName.equals("sjjb_wc")) {
                one.put("colNames", "收缴假币,完成");
            } else if (colName.equals("jzqb_mb")) {
                one.put("colNames", "经侦情报信息,目标");
            } else if (colName.equals("jzqbx_wc")) {
                one.put("colNames", "经侦情报信息,完成");
            } else if (colName.equals("ydzx_mb")) {
                one.put("colNames", "云端专项,目标");
            } else if (colName.equals("ydzx_wc")) {
                one.put("colNames", "云端专项,完成");
            } else if (colName.equals("ydzx_wcl")) {
                one.put("colNames", "云端专项,完成率");
            } else if (colName.equals("wlsl_mb")) {
                one.put("colNames", "网络四类,目标");
            } else if (colName.equals("wlsl_wc")) {
                one.put("colNames", "网络四类,完成");
            } else if (colName.equals("ywl_mb")) {
                one.put("colNames", "义务类,目标");
            } else if (colName.equals("ywl_wc")) {
                one.put("colNames", "义务类,完成");
            } else if (colName.equals("wlhc_mb")) {
                one.put("colNames", "网络灰产,目标");
            } else if (colName.equals("wlhc_wc")) {
                one.put("colNames", "网络灰产,完成");
            } else if (colName.equals("xzjl_mb")) {
                one.put("colNames", "行政拘留,目标");
            } else if (colName.equals("xzjl_wc")) {
                one.put("colNames", "行政拘留,完成");
            } else if (colName.equals("xzcj_wc")) {
                one.put("colNames", "行政查结,完成");
            } else if (colName.equals("xzkb_wc")) {
                one.put("colNames", "行政快办,完成");
            } else if (colName.equals("zbqb_mb")) {
                one.put("colNames", "政保情报信息,目标");
            } else if (colName.equals("zbqb_wc")) {
                one.put("colNames", "政保情报信息,完成");
            } else if (colName.equals("zbqb_wcl")) {
                one.put("colNames", "政保情报信息,完成率");
            } else if (colName.equals("qbxshc_xfs")) {
                one.put("colNames", "情报线索核查处置质效,下发数");
            } else if (colName.equals("qbxshc_wcs")) {
                one.put("colNames", "情报线索核查处置质效,完成数");
            } else if (colName.equals("qbxshc_rws")) {
                one.put("colNames", "情报线索核查处置质效,任务数");
            } else if (colName.equals("qbxxbs_wcs")) {
                one.put("colNames", "情报信息报送质态,完成数");
            } else if (colName.equals("myd_wc")) {
                one.put("colNames", "民意诉求满意度,完成");
            } else if (colName.equals("zgxx_df")) {
                one.put("colNames", "政工信息调研,得分");
            } else if (colName.equals("zgxx_pm")) {
                one.put("colNames", "政工信息调研,排名");
            } else if (colName.equals("xwmt_df")) {
                one.put("colNames", "新闻媒体宣传,得分");
            } else if (colName.equals("xwmt_pm")) {
                one.put("colNames", "新闻媒体宣传,排名");
            } else if (colName.equals("dwzw_df")) {
                one.put("colNames", "党委政务信息,得分");
            } else if (colName.equals("dwzw_pm")) {
                one.put("colNames", "党委政务信息,排名");
            } else if (colName.equals("wzxx_df")) {
                one.put("colNames", "网站信息,得分");
            } else if (colName.equals("wzxx_pm")) {
                one.put("colNames", "网站信息,排名");
            } else if (colName.equals("jbly_wc")) {
                one.put("colNames", "天宁公安简报录用,完成");
            }
            back.add(one);
        }

        return back;
    }


    private static JSONObject getCoreIndex(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";

            String id = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                id = new SimpleDateFormat("yyyy").format(new Date());
            }
            sql = sql + " id='" + id + "' and ";

            String sqls = "select * from core_index where 1=1 and " + sql + " isdelete=1";
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            back.put("data", list);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(449005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    //******UPDATE*******
    private static JSONObject updateCoreIndex(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String week_score = "";
            String week_rank = "";
            String month_score = "";
            String month_rakn = "";
            String year_score = "";
            String year_rank = "";
            String ys_f = "";
            String ys_s = "";
            String ys_g = "";
            String sw_f = "";
            String sw_s = "";
            String sw_g = "";
            String qc_f = "";
            String qc_s = "";
            String qc_g = "";
            String sd_f = "";
            String sd_s = "";
            String sd_g = "";
            String qc5_f = "";
            String qc5_s = "";
            String qc5_g = "";
            String xc_f = "";
            String xd_s = "";
            String xd_g = "";
            String zp_f = "";
            String zp_s = "";
            String zp_g = "";
            String hdc_f = "";
            String hdc_s = "";
            String hdc_g = "";
            String jwhl_f = "";
            String jwhl_s = "";
            String jwhl_g = "";
            String wl_f = "";
            String wl_s = "";
            String wl_g = "";
            String he_f = "";
            String he_s = "";
            String he_g = "";
            String bx_f = "";
            String bx_s = "";
            String bx_g = "";
            String sq_f = "";
            String sq_s = "";
            String sq_g = "";
            String jz_f = "";
            String jz_s = "";
            String jz_g = "";
            String jh_f = "";
            String jh_s = "";
            String jh_g = "";
            String zs_f = "";
            String zs_s = "";
            String zs_g = "";
            String sp_f = "";
            String sp_s = "";
            String sp_g = "";
            String sx_f = "";
            String sx_s = "";
            String sx_g = "";
            String gbj_f = "";
            String gbj_s = "";
            String gbj_g = "";
            String yp_f = "";
            String yp_s = "";
            String yp_g = "";
            String hj_f = "";
            String hj_s = "";
            String hj_g = "";
            String fsa_f = "";
            String fsa_s = "";
            String fsa_g = "";
            String zy_f = "";
            String zy_s = "";
            String zy_g = "";
            String sj_f = "";
            String sj_s = "";
            String sj_g = "";
            String za_f = "";
            String za_s = "";
            String za_g = "";
            String opt_user = "";
            JSONObject old = new JSONObject();
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(449004);
            }
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

                String s = "select * from core_index where id='" + id + "'";
                List<JSONObject> ll = mysql.query(s);

                if (ll.size() > 0) {
                    old = ll.get(0);
                } else {
                    s = "INSERT INTO `core_index`  VALUES ('" + id + "', '','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','', '1','','');";
                    mysql.update(s);
                    s = "select * from core_index where id='" + id + "'";
                    ll = mysql.query(s);
                    old = ll.get(0);
                }

            } else {
                id = new SimpleDateFormat("yyyy").format(new Date());
                String s = "INSERT INTO `core_index`  VALUES ('" + id + "', '','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','', '1','','');";
                mysql.update(s);
                s = "select * from core_index where id='" + id + "'";
                List<JSONObject> ll = mysql.query(s);
                old = ll.get(0);
            }
            if (data.containsKey("week_score") && data.getString("week_score").length() > 0) {
                week_score = data.getString("week_score");

                if (!old.getString("week_score").equals(week_score)) {
                    sql = sql + " week_score='" + week_score + "' , ";
                    writeLog(mysql, old.getString("week_score") + "->" + week_score, "ci:week_score", opt_user);
                }
            }
            if (data.containsKey("week_rank") && data.getString("week_rank").length() > 0) {
                week_rank = data.getString("week_rank");
                if (!old.getString("week_rank").equals(week_rank)) {
                    sql = sql + " week_rank='" + week_rank + "' , ";
                    writeLog(mysql, old.getString("week_rank") + "->" + week_rank, "ci:week_rank", opt_user);
                }
            }
            if (data.containsKey("month_score") && data.getString("month_score").length() > 0) {
                month_score = data.getString("month_score");
                if (!old.getString("month_score").equals(month_score)) {
                    sql = sql + " month_score='" + month_score + "' , ";
                    writeLog(mysql, old.getString("month_score") + "->" + month_score, "ci:month_score", opt_user);
                }
            }
            if (data.containsKey("month_rakn") && data.getString("month_rakn").length() > 0) {
                month_rakn = data.getString("month_rakn");
                if (!old.getString("month_rakn").equals(month_rakn)) {
                    sql = sql + " month_rakn='" + month_rakn + "' , ";
                    writeLog(mysql, old.getString("month_rakn") + "->" + month_rakn, "ci:month_rakn", opt_user);
                }
            }
            if (data.containsKey("year_score") && data.getString("year_score").length() > 0) {
                year_score = data.getString("year_score");
                if (!old.getString("year_score").equals(year_score)) {
                    sql = sql + " year_score='" + year_score + "' , ";
                    writeLog(mysql, old.getString("year_score") + "->" + year_score, "ci:year_score", opt_user);
                }
            }
            if (data.containsKey("year_rank") && data.getString("year_rank").length() > 0) {
                year_rank = data.getString("year_rank");
                if (!old.getString("year_rank").equals(year_rank)) {
                    sql = sql + " year_rank='" + year_rank + "' , ";
                    writeLog(mysql, old.getString("year_rank") + "->" + year_rank, "ci:year_rank", opt_user);
                }
            }
            if (data.containsKey("ys_f") && data.getString("ys_f").length() > 0) {
                ys_f = data.getString("ys_f");
                if (!old.getString("ys_f").equals(ys_f)) {
                    sql = sql + " ys_f='" + ys_f + "' , ";
                    writeLog(mysql, old.getString("ys_f") + "->" + ys_f, "ci:ys_f", opt_user);
                }
            }
            if (data.containsKey("ys_s") && data.getString("ys_s").length() > 0) {
                ys_s = data.getString("ys_s");
                if (!old.getString("ys_s").equals(ys_s)) {
                    sql = sql + " ys_s='" + ys_s + "' , ";
                    writeLog(mysql, old.getString("ys_s") + "->" + ys_s, "ci:ys_s", opt_user);
                }
            }
            if (data.containsKey("ys_g") && data.getString("ys_g").length() > 0) {
                ys_g = data.getString("ys_g");
                if (!old.getString("ys_g").equals(ys_g)) {
                    sql = sql + " ys_g='" + ys_g + "' , ";
                    writeLog(mysql, old.getString("ys_g") + "->" + ys_g, "ci:ys_g", opt_user);
                }
            }
            if (data.containsKey("sw_f") && data.getString("sw_f").length() > 0) {
                sw_f = data.getString("sw_f");
                if (!old.getString("sw_f").equals(sw_f)) {
                    sql = sql + " sw_f='" + sw_f + "' , ";
                    writeLog(mysql, old.getString("sw_f") + "->" + sw_f, "ci:sw_f", opt_user);
                }
            }
            if (data.containsKey("sw_s") && data.getString("sw_s").length() > 0) {
                sw_s = data.getString("sw_s");
                if (!old.getString("sw_s").equals(sw_s)) {
                    sql = sql + " sw_s='" + sw_s + "' , ";
                    writeLog(mysql, old.getString("sw_s") + "->" + sw_s, "ci:sw_s", opt_user);
                }
            }
            if (data.containsKey("sw_g") && data.getString("sw_g").length() > 0) {
                sw_g = data.getString("sw_g");
                if (!old.getString("sw_g").equals(sw_g)) {
                    sql = sql + " sw_g='" + sw_g + "' , ";
                    writeLog(mysql, old.getString("sw_g") + "->" + sw_g, "ci:sw_g", opt_user);
                }
            }
            if (data.containsKey("qc_f") && data.getString("qc_f").length() > 0) {
                qc_f = data.getString("qc_f");
                if (!old.getString("qc_f").equals(qc_f)) {
                    sql = sql + " qc_f='" + qc_f + "' , ";
                    writeLog(mysql, old.getString("qc_f") + "->" + qc_f, "ci:qc_f", opt_user);
                }
            }
            if (data.containsKey("qc_s") && data.getString("qc_s").length() > 0) {
                qc_s = data.getString("qc_s");
                if (!old.getString("qc_s").equals(qc_s)) {
                    sql = sql + " qc_s='" + qc_s + "' , ";
                    writeLog(mysql, old.getString("qc_s") + "->" + qc_s, "ci:qc_s", opt_user);
                }
            }
            if (data.containsKey("qc_g") && data.getString("qc_g").length() > 0) {
                qc_g = data.getString("qc_g");
                if (!old.getString("qc_g").equals(qc_g)) {
                    sql = sql + " qc_g='" + qc_g + "' , ";
                    writeLog(mysql, old.getString("qc_g") + "->" + qc_g, "ci:qc_g", opt_user);
                }
            }
            if (data.containsKey("sd_f") && data.getString("sd_f").length() > 0) {
                sd_f = data.getString("sd_f");
                if (!old.getString("sd_f").equals(sd_f)) {
                    sql = sql + " sd_f='" + sd_f + "' , ";
                    writeLog(mysql, old.getString("sd_f") + "->" + sd_f, "ci:sd_f", opt_user);
                }
            }
            if (data.containsKey("sd_s") && data.getString("sd_s").length() > 0) {
                sd_s = data.getString("sd_s");
                if (!old.getString("sd_s").equals(sd_s)) {
                    sql = sql + " sd_s='" + sd_s + "' , ";
                    writeLog(mysql, old.getString("sd_s") + "->" + sd_s, "ci:sd_s", opt_user);
                }
            }
            if (data.containsKey("sd_g") && data.getString("sd_g").length() > 0) {
                sd_g = data.getString("sd_g");
                if (!old.getString("sd_g").equals(sd_g)) {
                    sql = sql + " sd_g='" + sd_g + "' , ";
                    writeLog(mysql, old.getString("sd_g") + "->" + sd_g, "ci:sd_g", opt_user);
                }
            }
            if (data.containsKey("qc5_f") && data.getString("qc5_f").length() > 0) {
                qc5_f = data.getString("qc5_f");
                if (!old.getString("qc5_f").equals(qc5_f)) {
                    sql = sql + " qc5_f='" + qc5_f + "' , ";
                    writeLog(mysql, old.getString("qc5_f") + "->" + qc5_f, "ci:qc5_f", opt_user);
                }
            }
            if (data.containsKey("qc5_s") && data.getString("qc5_s").length() > 0) {
                qc5_s = data.getString("qc5_s");
                if (!old.getString("qc5_s").equals(qc5_s)) {
                    sql = sql + " qc5_s='" + qc5_s + "' , ";
                    writeLog(mysql, old.getString("qc5_s") + "->" + qc5_s, "ci:qc5_s", opt_user);
                }
            }
            if (data.containsKey("qc5_g") && data.getString("qc5_g").length() > 0) {
                qc5_g = data.getString("qc5_g");
                if (!old.getString("qc5_g").equals(qc5_g)) {
                    sql = sql + " qc5_g='" + qc5_g + "' , ";
                    writeLog(mysql, old.getString("qc5_g") + "->" + qc5_g, "ci:qc5_g", opt_user);
                }
            }
            if (data.containsKey("xc_f") && data.getString("xc_f").length() > 0) {
                xc_f = data.getString("xc_f");
                if (!old.getString("xc_f").equals(xc_f)) {
                    sql = sql + " xc_f='" + xc_f + "' , ";
                    writeLog(mysql, old.getString("xc_f") + "->" + xc_f, "ci:xc_f", opt_user);
                }
            }
            if (data.containsKey("xd_s") && data.getString("xd_s").length() > 0) {
                xd_s = data.getString("xd_s");
                if (!old.getString("xd_s").equals(xd_s)) {
                    sql = sql + " xd_s='" + xd_s + "' , ";
                    writeLog(mysql, old.getString("xd_s") + "->" + xd_s, "ci:xd_s", opt_user);
                }
            }
            if (data.containsKey("xd_g") && data.getString("xd_g").length() > 0) {
                xd_g = data.getString("xd_g");
                if (!old.getString("xd_g").equals(xd_g)) {
                    sql = sql + " xd_g='" + xd_g + "' , ";
                    writeLog(mysql, old.getString("xd_g") + "->" + xd_g, "ci:xd_g", opt_user);
                }
            }
            if (data.containsKey("zp_f") && data.getString("zp_f").length() > 0) {
                zp_f = data.getString("zp_f");
                if (!old.getString("zp_f").equals(zp_f)) {
                    sql = sql + " zp_f='" + zp_f + "' , ";
                    writeLog(mysql, old.getString("zp_f") + "->" + zp_f, "ci:zp_f", opt_user);
                }
            }
            if (data.containsKey("zp_s") && data.getString("zp_s").length() > 0) {
                zp_s = data.getString("zp_s");
                if (!old.getString("zp_s").equals(zp_s)) {
                    sql = sql + " zp_s='" + zp_s + "' , ";
                    writeLog(mysql, old.getString("zp_s") + "->" + zp_s, "ci:zp_s", opt_user);
                }
            }
            if (data.containsKey("zp_g") && data.getString("zp_g").length() > 0) {
                zp_g = data.getString("zp_g");
                if (!old.getString("zp_g").equals(zp_g)) {
                    sql = sql + " zp_g='" + zp_g + "' , ";
                    writeLog(mysql, old.getString("zp_g") + "->" + zp_g, "ci:zp_g", opt_user);
                }
            }
            if (data.containsKey("hdc_f") && data.getString("hdc_f").length() > 0) {
                hdc_f = data.getString("hdc_f");
                if (!old.getString("hdc_f").equals(hdc_f)) {
                    sql = sql + " hdc_f='" + hdc_f + "' , ";
                    writeLog(mysql, old.getString("hdc_f") + "->" + hdc_f, "ci:hdc_f", opt_user);
                }
            }
            if (data.containsKey("hdc_s") && data.getString("hdc_s").length() > 0) {
                hdc_s = data.getString("hdc_s");
                if (!old.getString("hdc_s").equals(hdc_s)) {
                    sql = sql + " hdc_s='" + hdc_s + "' , ";
                    writeLog(mysql, old.getString("hdc_s") + "->" + hdc_s, "ci:hdc_s", opt_user);
                }
            }
            if (data.containsKey("hdc_g") && data.getString("hdc_g").length() > 0) {
                hdc_g = data.getString("hdc_g");
                if (!old.getString("hdc_g").equals(hdc_g)) {
                    sql = sql + " hdc_g='" + hdc_g + "' , ";
                    writeLog(mysql, old.getString("hdc_g") + "->" + hdc_g, "ci:hdc_g", opt_user);
                }
            }
            if (data.containsKey("jwhl_f") && data.getString("jwhl_f").length() > 0) {
                jwhl_f = data.getString("jwhl_f");
                if (!old.getString("jwhl_f").equals(jwhl_f)) {
                    sql = sql + " jwhl_f='" + jwhl_f + "' , ";
                    writeLog(mysql, old.getString("jwhl_f") + "->" + jwhl_f, "ci:jwhl_f", opt_user);
                }
            }
            if (data.containsKey("jwhl_s") && data.getString("jwhl_s").length() > 0) {
                jwhl_s = data.getString("jwhl_s");
                if (!old.getString("jwhl_s").equals(jwhl_s)) {
                    sql = sql + " jwhl_s='" + jwhl_s + "' , ";
                    writeLog(mysql, old.getString("jwhl_s") + "->" + jwhl_s, "ci:jwhl_s", opt_user);
                }
            }
            if (data.containsKey("jwhl_g") && data.getString("jwhl_g").length() > 0) {
                jwhl_g = data.getString("jwhl_g");
                if (!old.getString("jwhl_g").equals(jwhl_g)) {
                    sql = sql + " jwhl_g='" + jwhl_g + "' , ";
                    writeLog(mysql, old.getString("jwhl_g") + "->" + jwhl_g, "ci:jwhl_g", opt_user);
                }
            }
            if (data.containsKey("wl_f") && data.getString("wl_f").length() > 0) {
                wl_f = data.getString("wl_f");
                if (!old.getString("wl_f").equals(wl_f)) {
                    sql = sql + " wl_f='" + wl_f + "' , ";
                    writeLog(mysql, old.getString("wl_f") + "->" + wl_f, "ci:wl_f", opt_user);
                }
            }
            if (data.containsKey("wl_s") && data.getString("wl_s").length() > 0) {
                wl_s = data.getString("wl_s");
                if (!old.getString("wl_s").equals(wl_s)) {
                    sql = sql + " wl_s='" + wl_s + "' , ";
                    writeLog(mysql, old.getString("wl_s") + "->" + wl_s, "ci:wl_s", opt_user);
                }
            }
            if (data.containsKey("wl_g") && data.getString("wl_g").length() > 0) {
                wl_g = data.getString("wl_g");
                if (!old.getString("wl_g").equals(wl_g)) {
                    sql = sql + " wl_g='" + wl_g + "' , ";
                    writeLog(mysql, old.getString("wl_g") + "->" + wl_g, "ci:wl_g", opt_user);
                }
            }
            if (data.containsKey("he_f") && data.getString("he_f").length() > 0) {
                he_f = data.getString("he_f");
                if (!old.getString("he_f").equals(he_f)) {
                    sql = sql + " he_f='" + he_f + "' , ";
                    writeLog(mysql, old.getString("he_f") + "->" + he_f, "ci:he_f", opt_user);
                }
            }
            if (data.containsKey("he_s") && data.getString("he_s").length() > 0) {
                he_s = data.getString("he_s");
                if (!old.getString("he_s").equals(he_s)) {
                    sql = sql + " he_s='" + he_s + "' , ";
                    writeLog(mysql, old.getString("he_s") + "->" + he_s, "ci:he_s", opt_user);
                }
            }
            if (data.containsKey("he_g") && data.getString("he_g").length() > 0) {
                he_g = data.getString("he_g");
                if (!old.getString("he_g").equals(he_g)) {
                    sql = sql + " he_g='" + he_g + "' , ";
                    writeLog(mysql, old.getString("he_g") + "->" + he_g, "ci:he_g", opt_user);
                }
            }
            if (data.containsKey("bx_f") && data.getString("bx_f").length() > 0) {
                bx_f = data.getString("bx_f");
                if (!old.getString("bx_f").equals(bx_f)) {
                    sql = sql + " bx_f='" + bx_f + "' , ";
                    writeLog(mysql, old.getString("bx_f") + "->" + bx_f, "ci:bx_f", opt_user);
                }
            }
            if (data.containsKey("bx_s") && data.getString("bx_s").length() > 0) {
                bx_s = data.getString("bx_s");
                if (!old.getString("bx_s").equals(bx_s)) {
                    sql = sql + " bx_s='" + bx_s + "' , ";
                    writeLog(mysql, old.getString("bx_s") + "->" + bx_s, "ci:bx_s", opt_user);
                }
            }
            if (data.containsKey("bx_g") && data.getString("bx_g").length() > 0) {
                bx_g = data.getString("bx_g");
                if (!old.getString("bx_g").equals(bx_g)) {
                    sql = sql + " bx_g='" + bx_g + "' , ";
                    writeLog(mysql, old.getString("bx_g") + "->" + bx_g, "ci:bx_g", opt_user);
                }
            }
            if (data.containsKey("sq_f") && data.getString("sq_f").length() > 0) {
                sq_f = data.getString("sq_f");
                if (!old.getString("sq_f").equals(sq_f)) {
                    sql = sql + " sq_f='" + sq_f + "' , ";
                    writeLog(mysql, old.getString("sq_f") + "->" + sq_f, "ci:sq_f", opt_user);
                }
            }
            if (data.containsKey("sq_s") && data.getString("sq_s").length() > 0) {
                sq_s = data.getString("sq_s");
                if (!old.getString("sq_s").equals(sq_s)) {
                    sql = sql + " sq_s='" + sq_s + "' , ";
                    writeLog(mysql, old.getString("sq_s") + "->" + sq_s, "ci:sq_s", opt_user);
                }
            }
            if (data.containsKey("sq_g") && data.getString("sq_g").length() > 0) {
                sq_g = data.getString("sq_g");
                if (!old.getString("sq_g").equals(sq_g)) {
                    sql = sql + " sq_g='" + sq_g + "' , ";
                    writeLog(mysql, old.getString("sq_g") + "->" + sq_g, "ci:sq_g", opt_user);
                }
            }
            if (data.containsKey("jz_f") && data.getString("jz_f").length() > 0) {
                jz_f = data.getString("jz_f");
                if (!old.getString("jz_f").equals(jz_f)) {
                    sql = sql + " jz_f='" + jz_f + "' , ";
                    writeLog(mysql, old.getString("jz_f") + "->" + jz_f, "ci:jz_f", opt_user);
                }
            }
            if (data.containsKey("jz_s") && data.getString("jz_s").length() > 0) {
                jz_s = data.getString("jz_s");
                if (!old.getString("jz_s").equals(jz_s)) {
                    sql = sql + " jz_s='" + jz_s + "' , ";
                    writeLog(mysql, old.getString("jz_s") + "->" + jz_s, "ci:jz_s", opt_user);
                }
            }
            if (data.containsKey("jz_g") && data.getString("jz_g").length() > 0) {
                jz_g = data.getString("jz_g");
                if (!old.getString("jz_g").equals(jz_g)) {
                    sql = sql + " jz_g='" + jz_g + "' , ";
                    writeLog(mysql, old.getString("jz_g") + "->" + jz_g, "ci:jz_g", opt_user);
                }
            }
            if (data.containsKey("jh_f") && data.getString("jh_f").length() > 0) {
                jh_f = data.getString("jh_f");
                if (!old.getString("jh_f").equals(jh_f)) {
                    sql = sql + " jh_f='" + jh_f + "' , ";
                    writeLog(mysql, old.getString("jh_f") + "->" + jh_f, "ci:jh_f", opt_user);
                }
            }
            if (data.containsKey("jh_s") && data.getString("jh_s").length() > 0) {
                jh_s = data.getString("jh_s");
                if (!old.getString("jh_s").equals(jh_s)) {
                    sql = sql + " jh_s='" + jh_s + "' , ";
                    writeLog(mysql, old.getString("jh_s") + "->" + jh_s, "ci:jh_s", opt_user);
                }
            }
            if (data.containsKey("jh_g") && data.getString("jh_g").length() > 0) {
                jh_g = data.getString("jh_g");
                if (!old.getString("jh_g").equals(jh_g)) {
                    sql = sql + " jh_g='" + jh_g + "' , ";
                    writeLog(mysql, old.getString("jh_g") + "->" + jh_g, "ci:jh_g", opt_user);
                }
            }
            if (data.containsKey("zs_f") && data.getString("zs_f").length() > 0) {
                zs_f = data.getString("zs_f");
                if (!old.getString("zs_f").equals(zs_f)) {
                    sql = sql + " zs_f='" + zs_f + "' , ";
                    writeLog(mysql, old.getString("zs_f") + "->" + zs_f, "ci:zs_f", opt_user);
                }
            }
            if (data.containsKey("zs_s") && data.getString("zs_s").length() > 0) {
                zs_s = data.getString("zs_s");
                if (!old.getString("zs_s").equals(zs_s)) {
                    sql = sql + " zs_s='" + zs_s + "' , ";
                    writeLog(mysql, old.getString("zs_s") + "->" + zs_s, "ci:zs_s", opt_user);
                }
            }
            if (data.containsKey("zs_g") && data.getString("zs_g").length() > 0) {
                zs_g = data.getString("zs_g");
                if (!old.getString("zs_g").equals(zs_g)) {
                    sql = sql + " zs_g='" + zs_g + "' , ";
                    writeLog(mysql, old.getString("zs_g") + "->" + zs_g, "ci:zs_g", opt_user);
                }
            }
            if (data.containsKey("sp_f") && data.getString("sp_f").length() > 0) {
                sp_f = data.getString("sp_f");
                if (!old.getString("sp_f").equals(sp_f)) {
                    sql = sql + " sp_f='" + sp_f + "' , ";
                    writeLog(mysql, old.getString("sp_f") + "->" + sp_f, "ci:sp_f", opt_user);
                }
            }
            if (data.containsKey("sp_s") && data.getString("sp_s").length() > 0) {
                sp_s = data.getString("sp_s");
                if (!old.getString("sp_s").equals(sp_s)) {
                    sql = sql + " sp_s='" + sp_s + "' , ";
                    writeLog(mysql, old.getString("sp_s") + "->" + sp_s, "ci:sp_s", opt_user);
                }
            }
            if (data.containsKey("sp_g") && data.getString("sp_g").length() > 0) {
                sp_g = data.getString("sp_g");
                if (!old.getString("sp_g").equals(sp_g)) {
                    sql = sql + " sp_g='" + sp_g + "' , ";
                    writeLog(mysql, old.getString("sp_g") + "->" + sp_g, "ci:sp_g", opt_user);
                }
            }
            if (data.containsKey("sx_f") && data.getString("sx_f").length() > 0) {
                sx_f = data.getString("sx_f");
                if (!old.getString("sx_f").equals(sx_f)) {
                    sql = sql + " sx_f='" + sx_f + "' , ";
                    writeLog(mysql, old.getString("sx_f") + "->" + sx_f, "ci:sx_f", opt_user);
                }
            }
            if (data.containsKey("sx_s") && data.getString("sx_s").length() > 0) {
                sx_s = data.getString("sx_s");
                if (!old.getString("sx_s").equals(sx_s)) {
                    sql = sql + " sx_s='" + sx_s + "' , ";
                    writeLog(mysql, old.getString("sx_s") + "->" + sx_s, "ci:sx_s", opt_user);
                }
            }
            if (data.containsKey("sx_g") && data.getString("sx_g").length() > 0) {
                sx_g = data.getString("sx_g");
                if (!old.getString("sx_g").equals(sx_g)) {
                    sql = sql + " sx_g='" + sx_g + "' , ";
                    writeLog(mysql, old.getString("sx_g") + "->" + sx_g, "ci:sx_g", opt_user);
                }
            }
            if (data.containsKey("gbj_f") && data.getString("gbj_f").length() > 0) {
                gbj_f = data.getString("gbj_f");
                if (!old.getString("gbj_f").equals(gbj_f)) {
                    sql = sql + " gbj_f='" + gbj_f + "' , ";
                    writeLog(mysql, old.getString("gbj_f") + "->" + gbj_f, "ci:gbj_f", opt_user);
                }
            }
            if (data.containsKey("gbj_s") && data.getString("gbj_s").length() > 0) {
                gbj_s = data.getString("gbj_s");
                if (!old.getString("gbj_s").equals(gbj_s)) {
                    sql = sql + " gbj_s='" + gbj_s + "' , ";
                    writeLog(mysql, old.getString("gbj_s") + "->" + gbj_s, "ci:gbj_s", opt_user);
                }
            }
            if (data.containsKey("gbj_g") && data.getString("gbj_g").length() > 0) {
                gbj_g = data.getString("gbj_g");
                if (!old.getString("gbj_g").equals(gbj_g)) {
                    sql = sql + " gbj_g='" + gbj_g + "' , ";
                    writeLog(mysql, old.getString("gbj_g") + "->" + gbj_g, "ci:gbj_g", opt_user);
                }
            }
            if (data.containsKey("yp_f") && data.getString("yp_f").length() > 0) {
                yp_f = data.getString("yp_f");
                if (!old.getString("yp_f").equals(yp_f)) {
                    sql = sql + " yp_f='" + yp_f + "' , ";
                    writeLog(mysql, old.getString("yp_f") + "->" + yp_f, "ci:yp_f", opt_user);
                }
            }
            if (data.containsKey("yp_s") && data.getString("yp_s").length() > 0) {
                yp_s = data.getString("yp_s");
                if (!old.getString("yp_s").equals(yp_s)) {
                    sql = sql + " yp_s='" + yp_s + "' , ";
                    writeLog(mysql, old.getString("yp_s") + "->" + yp_s, "ci:yp_s", opt_user);
                }
            }
            if (data.containsKey("yp_g") && data.getString("yp_g").length() > 0) {
                yp_g = data.getString("yp_g");
                if (!old.getString("yp_g").equals(yp_g)) {
                    sql = sql + " yp_g='" + yp_g + "' , ";
                    writeLog(mysql, old.getString("yp_g") + "->" + yp_g, "ci:yp_g", opt_user);
                }
            }
            if (data.containsKey("hj_f") && data.getString("hj_f").length() > 0) {
                hj_f = data.getString("hj_f");
                if (!old.getString("hj_f").equals(hj_f)) {
                    sql = sql + " hj_f='" + hj_f + "' , ";
                    writeLog(mysql, old.getString("hj_f") + "->" + hj_f, "ci:hj_f", opt_user);
                }
            }
            if (data.containsKey("hj_s") && data.getString("hj_s").length() > 0) {
                hj_s = data.getString("hj_s");
                if (!old.getString("hj_s").equals(hj_s)) {
                    sql = sql + " hj_s='" + hj_s + "' , ";
                    writeLog(mysql, old.getString("hj_s") + "->" + hj_s, "ci:hj_s", opt_user);
                }
            }
            if (data.containsKey("hj_g") && data.getString("hj_g").length() > 0) {
                hj_g = data.getString("hj_g");
                if (!old.getString("hj_g").equals(hj_g)) {
                    sql = sql + " hj_g='" + hj_g + "' , ";
                    writeLog(mysql, old.getString("hj_g") + "->" + hj_g, "ci:hj_g", opt_user);
                }
            }
            if (data.containsKey("fsa_f") && data.getString("fsa_f").length() > 0) {
                fsa_f = data.getString("fsa_f");
                if (!old.getString("fsa_f").equals(fsa_f)) {
                    sql = sql + " fsa_f='" + fsa_f + "' , ";
                    writeLog(mysql, old.getString("fsa_f") + "->" + fsa_f, "ci:fsa_f", opt_user);
                }
            }
            if (data.containsKey("fsa_s") && data.getString("fsa_s").length() > 0) {
                fsa_s = data.getString("fsa_s");
                if (!old.getString("fsa_s").equals(fsa_s)) {
                    sql = sql + " fsa_s='" + fsa_s + "' , ";
                    writeLog(mysql, old.getString("fsa_s") + "->" + fsa_s, "ci:fsa_s", opt_user);
                }
            }
            if (data.containsKey("fsa_g") && data.getString("fsa_g").length() > 0) {
                fsa_g = data.getString("fsa_g");
                if (!old.getString("fsa_g").equals(fsa_g)) {
                    sql = sql + " fsa_g='" + fsa_g + "' , ";
                    writeLog(mysql, old.getString("fsa_g") + "->" + fsa_g, "ci:fsa_g", opt_user);
                }
            }
            if (data.containsKey("zy_f") && data.getString("zy_f").length() > 0) {
                zy_f = data.getString("zy_f");
                if (!old.getString("zy_f").equals(zy_f)) {
                    sql = sql + " zy_f='" + zy_f + "' , ";
                    writeLog(mysql, old.getString("zy_f") + "->" + zy_f, "ci:zy_f", opt_user);
                }
            }
            if (data.containsKey("zy_s") && data.getString("zy_s").length() > 0) {
                zy_s = data.getString("zy_s");
                if (!old.getString("zy_s").equals(zy_s)) {
                    sql = sql + " zy_s='" + zy_s + "' , ";
                    writeLog(mysql, old.getString("zy_s") + "->" + zy_s, "ci:zy_s", opt_user);
                }
            }
            if (data.containsKey("zy_g") && data.getString("zy_g").length() > 0) {
                zy_g = data.getString("zy_g");
                if (!old.getString("zy_g").equals(zy_g)) {
                    sql = sql + " zy_g='" + zy_g + "' , ";
                    writeLog(mysql, old.getString("zy_g") + "->" + zy_g, "ci:zy_g", opt_user);
                }
            }
            if (data.containsKey("sj_f") && data.getString("sj_f").length() > 0) {
                sj_f = data.getString("sj_f");
                if (!old.getString("sj_f").equals(sj_f)) {
                    sql = sql + " sj_f='" + sj_f + "' , ";
                    writeLog(mysql, old.getString("sj_f") + "->" + sj_f, "ci:sj_f", opt_user);
                }
            }
            if (data.containsKey("sj_s") && data.getString("sj_s").length() > 0) {
                sj_s = data.getString("sj_s");
                if (!old.getString("sj_s").equals(sj_s)) {
                    sql = sql + " sj_s='" + sj_s + "' , ";
                    writeLog(mysql, old.getString("sj_s") + "->" + sj_s, "ci:sj_s", opt_user);
                }
            }
            if (data.containsKey("sj_g") && data.getString("sj_g").length() > 0) {
                sj_g = data.getString("sj_g");
                if (!old.getString("sj_g").equals(sj_g)) {
                    sql = sql + " sj_g='" + sj_g + "' , ";
                    writeLog(mysql, old.getString("sj_g") + "->" + sj_g, "ci:sj_g", opt_user);
                }
            }
            if (data.containsKey("za_f") && data.getString("za_f").length() > 0) {
                za_f = data.getString("za_f");
                if (!old.getString("za_f").equals(za_f)) {
                    sql = sql + " za_f='" + za_f + "' , ";
                    writeLog(mysql, old.getString("za_f") + "->" + za_f, "ci:za_f", opt_user);
                }
            }
            if (data.containsKey("za_s") && data.getString("za_s").length() > 0) {
                za_s = data.getString("za_s");
                if (!old.getString("za_s").equals(za_s)) {
                    sql = sql + " za_s='" + za_s + "' , ";
                    writeLog(mysql, old.getString("za_s") + "->" + za_s, "ci:za_s", opt_user);
                }
            }
            if (data.containsKey("za_g") && data.getString("za_g").length() > 0) {
                za_g = data.getString("za_g");
                if (!old.getString("za_g").equals(za_g)) {
                    sql = sql + " za_g='" + za_g + "' , ";
                    writeLog(mysql, old.getString("za_g") + "->" + za_g, "ci:za_g", opt_user);
                }
            }


            String sqls = "update core_index set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(449003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private static JSONObject getBaseCheck(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                id = new SimpleDateFormat("yyyy").format(new Date());
            }
            sql = sql + " id='" + id + "' and ";

            String sqls = "select * from base_check where 1=1 and " + sql + " isdelete=1  ";
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);

            back.put("data", list);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(450005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    //******UPDATE*******
    private static JSONObject updateBaseCheck(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String kqy_g = "";
            String kqy_f = "";
            String kqy_fq = "";
            String dxwl_c = "";
            String dxwl_l = "";
            String dxwl_t = "";
            String dk_hl = "";
            String dk_c = "";
            String dk_q = "";
            String xzzt_c = "";
            String zxxt_r = "";
            String dxwl_xkl = "";
            String dxwl_tql = "";
            String dxwl_jxl = "";
            String rsry_cj = "";
            String rsry_sw = "";
            String rsry_hm = "";
            String rsry_zw = "";
            String rsry_dna = "";
            String bzdz_g = "";
            String bzdz_f = "";
            String js_g = "";
            String js_f = "";
            String ll_g = "";
            String ll_f = "";
            String lgzd_g = "";
            String lgzd_f = "";
            String pa_g = "";
            String pa_f = "";
            String sqzf_g = "";
            String sqzf_f = "";
            String xfaq_g = "";
            String xfaq_f = "";
            String hzrw_g = "";
            String hzrw_r = "";
            String jd_g = "";
            String jd_f = "";
            String zazd_g = "";
            String zaad_f = "";
            String zaff_g = "";
            String zaff_f = "";
            String sqjbz_f = "";
            String sqjbz_g = "";
            String qz_g = "";
            String qz_f = "";
            String ldrk_g = "";
            String ldrk_f = "";
            String jffk_g = "";
            String jffk_f = "";
            String jfhj_g = "";
            String jfhj_f = "";
            String yslr_g = "";
            String yslr_f = "";
            String ysjy_g = "";
            String ysjy_f = "";
            String ysnb_g = "";
            String ysnb_f = "";
            String scza_g = "";
            String scza_f = "";
            String sdza_g = "";
            String sdza_f = "";
            String jb_g = "";
            String jb_f = "";
            String jz_g = "";
            String jz_f = "";
            String wlsl_g = "";
            String wlsl_f = "";
            String yw_g = "";
            String yw_f = "";
            String wlhc_g = "";
            String wlhc_f = "";
            String xzjl_g = "";
            String xzjl_f = "";
            String xzcj_g = "";
            String xzcj_f = "";
            String das_c = "";
            String das_l = "";
            String xscz_p = "";
            String xscz_f = "";
            String qbsz_g = "";
            String qbsz_f = "";
            String tn_g = "";
            String tn_f = "";
            String zgdy_g = "";
            String zgdy_y = "";
            String dwzw_c = "";
            String dwzw_r = "";
            String wzxx_c = "";
            String wzxx_r = "";
            String jbly_g = "";
            String jbly_f = "";

            String opt_user = "";

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(450004);
            }
            JSONObject old = new JSONObject();
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                String s = "select * from base_check where id='" + id + "'";
                List<JSONObject> ll = mysql.query(s);

                if (ll.size() > 0) {
                    old = ll.get(0);
                } else {
                    s = "INSERT INTO `base_check`  VALUES ('" + id + "', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1');";
                    mysql.update(s);
                    s = "select * from base_check where id='" + id + "'";
                    ll = mysql.query(s);
                    old = ll.get(0);
                }
            } else {
                id = new SimpleDateFormat("yyyy").format(new Date());
                String s = "INSERT INTO `base_check`  VALUES ('" + id + "', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1');";
                mysql.update(s);
                s = "select * from base_check where id='" + id + "'";
                List<JSONObject> ll = mysql.query(s);
                old = ll.get(0);
            }
            if (data.containsKey("kqy_g") && data.getString("kqy_g").length() > 0) {
                kqy_g = data.getString("kqy_g");
                if (!old.getString("kqy_g").equals(kqy_g)) {
                    sql = sql + " kqy_g='" + kqy_g + "' , ";
                    writeLog(mysql, old.getString("kqy_g") + "->" + kqy_g, "bc:kqy_g", opt_user);
                }
            }
            if (data.containsKey("kqy_f") && data.getString("kqy_f").length() > 0) {
                kqy_f = data.getString("kqy_f");
                if (!old.getString("kqy_f").equals(kqy_f)) {
                    sql = sql + " kqy_f='" + kqy_f + "' , ";
                    writeLog(mysql, old.getString("kqy_f") + "->" + kqy_f, "bc:kqy_f", opt_user);
                }
            }
            if (data.containsKey("kqy_fq") && data.getString("kqy_fq").length() > 0) {
                kqy_fq = data.getString("kqy_fq");
                if (!old.getString("kqy_fq").equals(kqy_fq)) {
                    sql = sql + " kqy_fq='" + kqy_fq + "' , ";
                    writeLog(mysql, old.getString("kqy_fq") + "->" + kqy_fq, "bc:kqy_fq", opt_user);
                }
            }
            if (data.containsKey("dxwl_c") && data.getString("dxwl_c").length() > 0) {
                dxwl_c = data.getString("dxwl_c");
                if (!old.getString("dxwl_c").equals(dxwl_c)) {
                    sql = sql + " dxwl_c='" + dxwl_c + "' , ";
                    writeLog(mysql, old.getString("dxwl_c") + "->" + dxwl_c, "bc:dxwl_c", opt_user);
                }
            }
            if (data.containsKey("dxwl_l") && data.getString("dxwl_l").length() > 0) {
                dxwl_l = data.getString("dxwl_l");
                if (!old.getString("dxwl_l").equals(dxwl_l)) {
                    sql = sql + " dxwl_l='" + dxwl_l + "' , ";
                    writeLog(mysql, old.getString("dxwl_l") + "->" + dxwl_l, "bc:dxwl_l", opt_user);
                }
            }
            if (data.containsKey("dxwl_t") && data.getString("dxwl_t").length() > 0) {
                dxwl_t = data.getString("dxwl_t");
                if (!old.getString("dxwl_t").equals(dxwl_t)) {
                    sql = sql + " dxwl_t='" + dxwl_t + "' , ";
                    writeLog(mysql, old.getString("dxwl_t") + "->" + dxwl_t, "bc:dxwl_t", opt_user);
                }
            }
            if (data.containsKey("dk_hl") && data.getString("dk_hl").length() > 0) {
                dk_hl = data.getString("dk_hl");
                if (!old.getString("dk_hl").equals(dk_hl)) {
                    sql = sql + " dk_hl='" + dk_hl + "' , ";
                    writeLog(mysql, old.getString("dk_hl") + "->" + dk_hl, "bc:dk_hl", opt_user);
                }
            }
            if (data.containsKey("dk_c") && data.getString("dk_c").length() > 0) {
                dk_c = data.getString("dk_c");
                if (!old.getString("dk_c").equals(dk_c)) {
                    sql = sql + " dk_c='" + dk_c + "' , ";
                    writeLog(mysql, old.getString("dk_c") + "->" + dk_c, "bc:dk_c", opt_user);
                }
            }
            if (data.containsKey("dk_q") && data.getString("dk_q").length() > 0) {
                dk_q = data.getString("dk_q");
                if (!old.getString("dk_q").equals(dk_q)) {
                    sql = sql + " dk_q='" + dk_q + "' , ";
                    writeLog(mysql, old.getString("dk_q") + "->" + dk_q, "bc:dk_q", opt_user);
                }
            }
            if (data.containsKey("xzzt_c") && data.getString("xzzt_c").length() > 0) {
                xzzt_c = data.getString("xzzt_c");
                if (!old.getString("xzzt_c").equals(xzzt_c)) {
                    sql = sql + " xzzt_c='" + xzzt_c + "' , ";
                    writeLog(mysql, old.getString("xzzt_c") + "->" + xzzt_c, "bc:xzzt_c", opt_user);
                }
            }
            if (data.containsKey("zxxt_r") && data.getString("zxxt_r").length() > 0) {
                zxxt_r = data.getString("zxxt_r");
                if (!old.getString("zxxt_r").equals(zxxt_r)) {
                    sql = sql + " zxxt_r='" + zxxt_r + "' , ";
                    writeLog(mysql, old.getString("zxxt_r") + "->" + zxxt_r, "bc:zxxt_r", opt_user);
                }
            }
            if (data.containsKey("dxwl_xkl") && data.getString("dxwl_xkl").length() > 0) {
                dxwl_xkl = data.getString("dxwl_xkl");
                if (!old.getString("dxwl_xkl").equals(dxwl_xkl)) {
                    sql = sql + " dxwl_xkl='" + dxwl_xkl + "' , ";
                    writeLog(mysql, old.getString("dxwl_xkl") + "->" + dxwl_xkl, "bc:dxwl_xkl", opt_user);
                }
            }
            if (data.containsKey("dxwl_tql") && data.getString("dxwl_tql").length() > 0) {
                dxwl_tql = data.getString("dxwl_tql");
                if (!old.getString("dxwl_tql").equals(dxwl_tql)) {
                    sql = sql + " dxwl_tql='" + dxwl_tql + "' , ";
                    writeLog(mysql, old.getString("dxwl_tql") + "->" + dxwl_tql, "bc:dxwl_tql", opt_user);
                }
            }
            if (data.containsKey("dxwl_jxl") && data.getString("dxwl_jxl").length() > 0) {
                dxwl_jxl = data.getString("dxwl_jxl");
                if (!old.getString("dxwl_jxl").equals(dxwl_jxl)) {
                    sql = sql + " dxwl_jxl='" + dxwl_jxl + "' , ";
                    writeLog(mysql, old.getString("dxwl_jxl") + "->" + dxwl_jxl, "bc:dxwl_jxl", opt_user);
                }
            }
            if (data.containsKey("rsry_cj") && data.getString("rsry_cj").length() > 0) {
                rsry_cj = data.getString("rsry_cj");
                if (!old.getString("rsry_cj").equals(rsry_cj)) {
                    sql = sql + " rsry_cj='" + rsry_cj + "' , ";
                    writeLog(mysql, old.getString("rsry_cj") + "->" + rsry_cj, "bc:rsry_cj", opt_user);
                }
            }
            if (data.containsKey("rsry_sw") && data.getString("rsry_sw").length() > 0) {
                rsry_sw = data.getString("rsry_sw");
                if (!old.getString("rsry_sw").equals(rsry_sw)) {
                    sql = sql + " rsry_sw='" + rsry_sw + "' , ";
                    writeLog(mysql, old.getString("rsry_sw") + "->" + rsry_sw, "bc:rsry_sw", opt_user);
                }
            }
            if (data.containsKey("rsry_hm") && data.getString("rsry_hm").length() > 0) {
                rsry_hm = data.getString("rsry_hm");
                if (!old.getString("rsry_hm").equals(rsry_hm)) {
                    sql = sql + " rsry_hm='" + rsry_hm + "' , ";
                    writeLog(mysql, old.getString("rsry_hm") + "->" + rsry_hm, "bc:rsry_hm", opt_user);
                }
            }
            if (data.containsKey("rsry_zw") && data.getString("rsry_zw").length() > 0) {
                rsry_zw = data.getString("rsry_zw");
                if (!old.getString("rsry_zw").equals(rsry_zw)) {
                    sql = sql + " rsry_zw='" + rsry_zw + "' , ";
                    writeLog(mysql, old.getString("rsry_zw") + "->" + rsry_zw, "bc:rsry_zw", opt_user);
                }
            }
            if (data.containsKey("rsry_dna") && data.getString("rsry_dna").length() > 0) {
                rsry_dna = data.getString("rsry_dna");
                if (!old.getString("rsry_dna").equals(rsry_dna)) {
                    sql = sql + " rsry_dna='" + rsry_dna + "' , ";
                    writeLog(mysql, old.getString("rsry_dna") + "->" + rsry_dna, "bc:rsry_dna", opt_user);
                }
            }
            if (data.containsKey("bzdz_g") && data.getString("bzdz_g").length() > 0) {
                bzdz_g = data.getString("bzdz_g");
                if (!old.getString("bzdz_g").equals(bzdz_g)) {
                    sql = sql + " bzdz_g='" + bzdz_g + "' , ";
                    writeLog(mysql, old.getString("bzdz_g") + "->" + bzdz_g, "bc:bzdz_g", opt_user);
                }
            }
            if (data.containsKey("bzdz_f") && data.getString("bzdz_f").length() > 0) {
                bzdz_f = data.getString("bzdz_f");
                if (!old.getString("bzdz_f").equals(bzdz_f)) {
                    sql = sql + " bzdz_f='" + bzdz_f + "' , ";
                    writeLog(mysql, old.getString("bzdz_f") + "->" + bzdz_f, "bc:bzdz_f", opt_user);
                }
            }
            if (data.containsKey("js_g") && data.getString("js_g").length() > 0) {
                js_g = data.getString("js_g");
                if (!old.getString("js_g").equals(js_g)) {
                    sql = sql + " js_g='" + js_g + "' , ";
                    writeLog(mysql, old.getString("js_g") + "->" + js_g, "bc:js_g", opt_user);
                }
            }
            if (data.containsKey("js_f") && data.getString("js_f").length() > 0) {
                js_f = data.getString("js_f");
                if (!old.getString("js_f").equals(js_f)) {
                    sql = sql + " js_f='" + js_f + "' , ";
                    writeLog(mysql, old.getString("js_f") + "->" + js_f, "bc:js_f", opt_user);
                }
            }
            if (data.containsKey("ll_g") && data.getString("ll_g").length() > 0) {
                ll_g = data.getString("ll_g");
                if (!old.getString("ll_g").equals(ll_g)) {
                    sql = sql + " ll_g='" + ll_g + "' , ";
                    writeLog(mysql, old.getString("ll_g") + "->" + ll_g, "bc:ll_g", opt_user);
                }
            }
            if (data.containsKey("ll_f") && data.getString("ll_f").length() > 0) {
                ll_f = data.getString("ll_f");
                if (!old.getString("ll_f").equals(ll_f)) {
                    sql = sql + " ll_f='" + ll_f + "' , ";
                    writeLog(mysql, old.getString("ll_f") + "->" + ll_f, "bc:ll_f", opt_user);
                }
            }
            if (data.containsKey("lgzd_g") && data.getString("lgzd_g").length() > 0) {
                lgzd_g = data.getString("lgzd_g");
                if (!old.getString("lgzd_g").equals(lgzd_g)) {
                    sql = sql + " lgzd_g='" + lgzd_g + "' , ";
                    writeLog(mysql, old.getString("lgzd_g") + "->" + lgzd_g, "bc:lgzd_g", opt_user);
                }
            }
            if (data.containsKey("lgzd_f") && data.getString("lgzd_f").length() > 0) {
                lgzd_f = data.getString("lgzd_f");
                if (!old.getString("lgzd_f").equals(lgzd_f)) {
                    sql = sql + " lgzd_f='" + lgzd_f + "' , ";
                    writeLog(mysql, old.getString("lgzd_f") + "->" + lgzd_f, "bc:lgzd_f", opt_user);
                }
            }
            if (data.containsKey("pa_g") && data.getString("pa_g").length() > 0) {
                pa_g = data.getString("pa_g");
                if (!old.getString("pa_g").equals(pa_g)) {
                    sql = sql + " pa_g='" + pa_g + "' , ";
                    writeLog(mysql, old.getString("pa_g") + "->" + pa_g, "bc:pa_g", opt_user);
                }
            }
            if (data.containsKey("pa_f") && data.getString("pa_f").length() > 0) {
                pa_f = data.getString("pa_f");
                if (!old.getString("pa_f").equals(pa_f)) {
                    sql = sql + " pa_f='" + pa_f + "' , ";
                    writeLog(mysql, old.getString("pa_f") + "->" + pa_f, "bc:pa_f", opt_user);
                }
            }
            if (data.containsKey("sqzf_g") && data.getString("sqzf_g").length() > 0) {
                sqzf_g = data.getString("sqzf_g");
                if (!old.getString("sqzf_g").equals(sqzf_g)) {
                    sql = sql + " sqzf_g='" + sqzf_g + "' , ";
                    writeLog(mysql, old.getString("sqzf_g") + "->" + sqzf_g, "bc:sqzf_g", opt_user);
                }
            }
            if (data.containsKey("sqzf_f") && data.getString("sqzf_f").length() > 0) {
                sqzf_f = data.getString("sqzf_f");
                if (!old.getString("sqzf_f").equals(sqzf_f)) {
                    sql = sql + " sqzf_f='" + sqzf_f + "' , ";
                    writeLog(mysql, old.getString("sqzf_f") + "->" + sqzf_f, "bc:sqzf_f", opt_user);
                }
            }
            if (data.containsKey("xfaq_g") && data.getString("xfaq_g").length() > 0) {
                xfaq_g = data.getString("xfaq_g");
                if (!old.getString("xfaq_g").equals(xfaq_g)) {
                    sql = sql + " xfaq_g='" + xfaq_g + "' , ";
                    writeLog(mysql, old.getString("xfaq_g") + "->" + xfaq_g, "bc:xfaq_g", opt_user);
                }
            }
            if (data.containsKey("xfaq_f") && data.getString("xfaq_f").length() > 0) {
                xfaq_f = data.getString("xfaq_f");
                if (!old.getString("xfaq_f").equals(xfaq_f)) {
                    sql = sql + " xfaq_f='" + xfaq_f + "' , ";
                    writeLog(mysql, old.getString("xfaq_f") + "->" + xfaq_f, "bc:xfaq_f", opt_user);
                }
            }
            if (data.containsKey("hzrw_g") && data.getString("hzrw_g").length() > 0) {
                hzrw_g = data.getString("hzrw_g");
                if (!old.getString("hzrw_g").equals(hzrw_g)) {
                    sql = sql + " hzrw_g='" + hzrw_g + "' , ";
                    writeLog(mysql, old.getString("hzrw_g") + "->" + hzrw_g, "bc:hzrw_g", opt_user);
                }
            }
            if (data.containsKey("hzrw_r") && data.getString("hzrw_r").length() > 0) {
                hzrw_r = data.getString("hzrw_r");
                if (!old.getString("hzrw_r").equals(hzrw_r)) {
                    sql = sql + " hzrw_r='" + hzrw_r + "' , ";
                    writeLog(mysql, old.getString("hzrw_r") + "->" + hzrw_r, "bc:hzrw_r", opt_user);
                }
            }
            if (data.containsKey("jd_g") && data.getString("jd_g").length() > 0) {
                jd_g = data.getString("jd_g");
                if (!old.getString("jd_g").equals(jd_g)) {
                    sql = sql + " jd_g='" + jd_g + "' , ";
                    writeLog(mysql, old.getString("jd_g") + "->" + jd_g, "bc:jd_g", opt_user);
                }
            }
            if (data.containsKey("jd_f") && data.getString("jd_f").length() > 0) {
                jd_f = data.getString("jd_f");
                if (!old.getString("jd_f").equals(jd_f)) {
                    sql = sql + " jd_f='" + jd_f + "' , ";
                    writeLog(mysql, old.getString("jd_f") + "->" + jd_f, "bc:jd_f", opt_user);
                }
            }
            if (data.containsKey("zazd_g") && data.getString("zazd_g").length() > 0) {
                zazd_g = data.getString("zazd_g");
                if (!old.getString("zazd_g").equals(zazd_g)) {
                    sql = sql + " zazd_g='" + zazd_g + "' , ";
                    writeLog(mysql, old.getString("zazd_g") + "->" + zazd_g, "bc:zazd_g", opt_user);
                }
            }
            if (data.containsKey("zaad_f") && data.getString("zaad_f").length() > 0) {
                zaad_f = data.getString("zaad_f");
                if (!old.getString("zaad_f").equals(zaad_f)) {
                    sql = sql + " zaad_f='" + zaad_f + "' , ";
                    writeLog(mysql, old.getString("zaad_f") + "->" + zaad_f, "bc:zaad_f", opt_user);
                }
            }
            if (data.containsKey("zaff_g") && data.getString("zaff_g").length() > 0) {
                zaff_g = data.getString("zaff_g");
                if (!old.getString("zaff_g").equals(zaff_g)) {
                    sql = sql + " zaff_g='" + zaff_g + "' , ";
                    writeLog(mysql, old.getString("zaff_g") + "->" + zaff_g, "bc:zaff_g", opt_user);
                }
            }
            if (data.containsKey("zaff_f") && data.getString("zaff_f").length() > 0) {
                zaff_f = data.getString("zaff_f");
                if (!old.getString("zaff_f").equals(zaff_f)) {
                    sql = sql + " zaff_f='" + zaff_f + "' , ";
                    writeLog(mysql, old.getString("zaff_f") + "->" + zaff_f, "bc:zaff_f", opt_user);
                }
            }
            if (data.containsKey("sqjbz_f") && data.getString("sqjbz_f").length() > 0) {
                sqjbz_f = data.getString("sqjbz_f");
                if (!old.getString("sqjbz_f").equals(sqjbz_f)) {
                    sql = sql + " sqjbz_f='" + sqjbz_f + "' , ";
                    writeLog(mysql, old.getString("sqjbz_f") + "->" + sqjbz_f, "bc:sqjbz_f", opt_user);
                }
            }
            if (data.containsKey("sqjbz_g") && data.getString("sqjbz_g").length() > 0) {
                sqjbz_g = data.getString("sqjbz_g");
                if (!old.getString("sqjbz_g").equals(sqjbz_g)) {
                    sql = sql + " sqjbz_g='" + sqjbz_g + "' , ";
                    writeLog(mysql, old.getString("sqjbz_g") + "->" + sqjbz_g, "bc:sqjbz_g", opt_user);
                }
            }
            if (data.containsKey("qz_g") && data.getString("qz_g").length() > 0) {
                qz_g = data.getString("qz_g");
                if (!old.getString("qz_g").equals(qz_g)) {
                    sql = sql + " qz_g='" + qz_g + "' , ";
                    writeLog(mysql, old.getString("qz_g") + "->" + qz_g, "bc:qz_g", opt_user);
                }
            }
            if (data.containsKey("qz_f") && data.getString("qz_f").length() > 0) {
                qz_f = data.getString("qz_f");
                if (!old.getString("qz_f").equals(qz_f)) {
                    sql = sql + " qz_f='" + qz_f + "' , ";
                    writeLog(mysql, old.getString("qz_f") + "->" + qz_f, "bc:qz_f", opt_user);
                }
            }
            if (data.containsKey("ldrk_g") && data.getString("ldrk_g").length() > 0) {
                ldrk_g = data.getString("ldrk_g");
                if (!old.getString("ldrk_g").equals(ldrk_g)) {
                    sql = sql + " ldrk_g='" + ldrk_g + "' , ";
                    writeLog(mysql, old.getString("ldrk_g") + "->" + ldrk_g, "bc:ldrk_g", opt_user);
                }
            }
            if (data.containsKey("ldrk_f") && data.getString("ldrk_f").length() > 0) {
                ldrk_f = data.getString("ldrk_f");
                if (!old.getString("ldrk_f").equals(ldrk_f)) {
                    sql = sql + " ldrk_f='" + ldrk_f + "' , ";
                    writeLog(mysql, old.getString("ldrk_f") + "->" + ldrk_f, "bc:ldrk_f", opt_user);
                }
            }
            if (data.containsKey("jffk_g") && data.getString("jffk_g").length() > 0) {
                jffk_g = data.getString("jffk_g");
                if (!old.getString("jffk_g").equals(jffk_g)) {
                    sql = sql + " jffk_g='" + jffk_g + "' , ";
                    writeLog(mysql, old.getString("jffk_g") + "->" + jffk_g, "bc:jffk_g", opt_user);
                }
            }
            if (data.containsKey("jffk_f") && data.getString("jffk_f").length() > 0) {
                jffk_f = data.getString("jffk_f");
                if (!old.getString("jffk_f").equals(jffk_f)) {
                    sql = sql + " jffk_f='" + jffk_f + "' , ";
                    writeLog(mysql, old.getString("jffk_f") + "->" + jffk_f, "bc:jffk_f", opt_user);
                }
            }
            if (data.containsKey("jfhj_g") && data.getString("jfhj_g").length() > 0) {
                jfhj_g = data.getString("jfhj_g");
                if (!old.getString("jfhj_g").equals(jfhj_g)) {
                    sql = sql + " jfhj_g='" + jfhj_g + "' , ";
                    writeLog(mysql, old.getString("jfhj_g") + "->" + jfhj_g, "bc:jfhj_g", opt_user);
                }
            }
            if (data.containsKey("jfhj_f") && data.getString("jfhj_f").length() > 0) {
                jfhj_f = data.getString("jfhj_f");
                if (!old.getString("jfhj_f").equals(jfhj_f)) {
                    sql = sql + " jfhj_f='" + jfhj_f + "' , ";
                    writeLog(mysql, old.getString("jfhj_f") + "->" + jfhj_f, "bc:jfhj_f", opt_user);
                }
            }
            if (data.containsKey("yslr_g") && data.getString("yslr_g").length() > 0) {
                yslr_g = data.getString("yslr_g");
                if (!old.getString("yslr_g").equals(yslr_g)) {
                    sql = sql + " yslr_g='" + yslr_g + "' , ";
                    writeLog(mysql, old.getString("yslr_g") + "->" + yslr_g, "bc:yslr_g", opt_user);
                }
            }
            if (data.containsKey("yslr_f") && data.getString("yslr_f").length() > 0) {
                yslr_f = data.getString("yslr_f");
                if (!old.getString("yslr_f").equals(yslr_f)) {
                    sql = sql + " yslr_f='" + yslr_f + "' , ";
                    writeLog(mysql, old.getString("yslr_f") + "->" + yslr_f, "bc:yslr_f", opt_user);
                }
            }
            if (data.containsKey("ysjy_g") && data.getString("ysjy_g").length() > 0) {
                ysjy_g = data.getString("ysjy_g");
                if (!old.getString("ysjy_g").equals(ysjy_g)) {
                    sql = sql + " ysjy_g='" + ysjy_g + "' , ";
                    writeLog(mysql, old.getString("ysjy_g") + "->" + ysjy_g, "bc:ysjy_g", opt_user);
                }
            }
            if (data.containsKey("ysjy_f") && data.getString("ysjy_f").length() > 0) {
                ysjy_f = data.getString("ysjy_f");
                if (!old.getString("ysjy_f").equals(ysjy_f)) {
                    sql = sql + " ysjy_f='" + ysjy_f + "' , ";
                    writeLog(mysql, old.getString("ysjy_f") + "->" + ysjy_f, "bc:ysjy_f", opt_user);
                }
            }
            if (data.containsKey("ysnb_g") && data.getString("ysnb_g").length() > 0) {
                ysnb_g = data.getString("ysnb_g");
                if (!old.getString("ysnb_g").equals(ysnb_g)) {
                    sql = sql + " ysnb_g='" + ysnb_g + "' , ";
                    writeLog(mysql, old.getString("ysnb_g") + "->" + ysnb_g, "bc:ysnb_g", opt_user);
                }
            }
            if (data.containsKey("ysnb_f") && data.getString("ysnb_f").length() > 0) {
                ysnb_f = data.getString("ysnb_f");
                if (!old.getString("ysnb_f").equals(ysnb_f)) {
                    sql = sql + " ysnb_f='" + ysnb_f + "' , ";
                    writeLog(mysql, old.getString("ysnb_f") + "->" + ysnb_f, "bc:ysnb_f", opt_user);
                }
            }
            if (data.containsKey("scza_g") && data.getString("scza_g").length() > 0) {
                scza_g = data.getString("scza_g");
                if (!old.getString("scza_g").equals(scza_g)) {
                    sql = sql + " scza_g='" + scza_g + "' , ";
                    writeLog(mysql, old.getString("scza_g") + "->" + scza_g, "bc:scza_g", opt_user);
                }
            }
            if (data.containsKey("scza_f") && data.getString("scza_f").length() > 0) {
                scza_f = data.getString("scza_f");
                if (!old.getString("scza_f").equals(scza_f)) {
                    sql = sql + " scza_f='" + scza_f + "' , ";
                    writeLog(mysql, old.getString("scza_f") + "->" + scza_f, "bc:scza_f", opt_user);
                }
            }
            if (data.containsKey("sdza_g") && data.getString("sdza_g").length() > 0) {
                sdza_g = data.getString("sdza_g");
                if (!old.getString("sdza_g").equals(sdza_g)) {
                    sql = sql + " sdza_g='" + sdza_g + "' , ";
                    writeLog(mysql, old.getString("sdza_g") + "->" + sdza_g, "bc:sdza_g", opt_user);
                }
            }
            if (data.containsKey("sdza_f") && data.getString("sdza_f").length() > 0) {
                sdza_f = data.getString("sdza_f");
                if (!old.getString("sdza_f").equals(sdza_f)) {
                    sql = sql + " sdza_f='" + scza_f + "' , ";
                    writeLog(mysql, old.getString("sdza_f") + "->" + sdza_f, "bc:sdza_f", opt_user);
                }
            }
            if (data.containsKey("jb_g") && data.getString("jb_g").length() > 0) {
                jb_g = data.getString("jb_g");
                if (!old.getString("jb_g").equals(jb_g)) {
                    sql = sql + " jb_g='" + jb_g + "' , ";
                    writeLog(mysql, old.getString("jb_g") + "->" + jb_g, "bc:jb_g", opt_user);
                }
            }
            if (data.containsKey("jb_f") && data.getString("jb_f").length() > 0) {
                jb_f = data.getString("jb_f");
                if (!old.getString("jb_f").equals(jb_f)) {
                    sql = sql + " jb_f='" + jb_f + "' , ";
                    writeLog(mysql, old.getString("jb_f") + "->" + jb_f, "bc:jb_f", opt_user);
                }
            }
            if (data.containsKey("jz_g") && data.getString("jz_g").length() > 0) {
                jz_g = data.getString("jz_g");
                if (!old.getString("jz_g").equals(jz_g)) {
                    sql = sql + " jz_g='" + jz_g + "' , ";
                    writeLog(mysql, old.getString("jz_g") + "->" + jz_g, "bc:jz_g", opt_user);
                }
            }
            if (data.containsKey("jz_f") && data.getString("jz_f").length() > 0) {
                jz_f = data.getString("jz_f");
                if (!old.getString("jz_f").equals(jz_f)) {
                    sql = sql + " jz_f='" + jz_f + "' , ";
                    writeLog(mysql, old.getString("jz_f") + "->" + jz_f, "bc:jz_f", opt_user);
                }
            }
            if (data.containsKey("wlsl_g") && data.getString("wlsl_g").length() > 0) {
                wlsl_g = data.getString("wlsl_g");
                if (!old.getString("wlsl_g").equals(wlsl_g)) {
                    sql = sql + " wlsl_g='" + wlsl_g + "' , ";
                    writeLog(mysql, old.getString("wlsl_g") + "->" + wlsl_g, "bc:wlsl_g", opt_user);
                }
            }
            if (data.containsKey("wlsl_f") && data.getString("wlsl_f").length() > 0) {
                wlsl_f = data.getString("wlsl_f");
                if (!old.getString("wlsl_f").equals(wlsl_f)) {
                    sql = sql + " wlsl_f='" + wlsl_f + "' , ";
                    writeLog(mysql, old.getString("wlsl_f") + "->" + wlsl_f, "bc:wlsl_f", opt_user);
                }
            }
            if (data.containsKey("yw_g") && data.getString("yw_g").length() > 0) {
                yw_g = data.getString("yw_g");
                if (!old.getString("yw_g").equals(yw_g)) {
                    sql = sql + " yw_g='" + yw_g + "' , ";
                    writeLog(mysql, old.getString("yw_g") + "->" + yw_g, "bc:yw_g", opt_user);
                }
            }
            if (data.containsKey("yw_f") && data.getString("yw_f").length() > 0) {
                yw_f = data.getString("yw_f");
                if (!old.getString("yw_f").equals(yw_f)) {
                    sql = sql + " yw_f='" + yw_f + "' , ";
                    writeLog(mysql, old.getString("yw_f") + "->" + yw_f, "bc:yw_f", opt_user);
                }
            }
            if (data.containsKey("wlhc_g") && data.getString("wlhc_g").length() > 0) {
                wlhc_g = data.getString("wlhc_g");
                if (!old.getString("wlhc_g").equals(wlhc_g)) {
                    sql = sql + " wlhc_g='" + wlhc_g + "' , ";
                    writeLog(mysql, old.getString("wlhc_g") + "->" + wlhc_g, "bc:wlhc_g", opt_user);
                }
            }
            if (data.containsKey("wlhc_f") && data.getString("wlhc_f").length() > 0) {
                wlhc_f = data.getString("wlhc_f");
                if (!old.getString("wlhc_f").equals(wlhc_f)) {
                    sql = sql + " wlhc_f='" + wlhc_f + "' , ";
                    writeLog(mysql, old.getString("wlhc_f") + "->" + wlhc_f, "bc:wlhc_f", opt_user);
                }
            }
            if (data.containsKey("xzjl_g") && data.getString("xzjl_g").length() > 0) {
                xzjl_g = data.getString("xzjl_g");
                if (!old.getString("xzjl_g").equals(xzjl_g)) {
                    sql = sql + " xzjl_g='" + xzjl_g + "' , ";
                    writeLog(mysql, old.getString("xzjl_g") + "->" + xzjl_g, "bc:xzjl_g", opt_user);
                }
            }
            if (data.containsKey("xzjl_f") && data.getString("xzjl_f").length() > 0) {
                xzjl_f = data.getString("xzjl_f");
                if (!old.getString("xzjl_f").equals(xzjl_f)) {
                    sql = sql + " xzjl_f='" + xzjl_f + "' , ";
                    writeLog(mysql, old.getString("xzjl_f") + "->" + xzjl_f, "bc:xzjl_f", opt_user);
                }
            }
            if (data.containsKey("xzcj_g") && data.getString("xzcj_g").length() > 0) {
                xzcj_g = data.getString("xzcj_g");
                if (!old.getString("xzcj_g").equals(xzcj_g)) {
                    sql = sql + " xzcj_g='" + xzcj_g + "' , ";
                    writeLog(mysql, old.getString("xzcj_g") + "->" + xzcj_g, "bc:xzcj_g", opt_user);
                }
            }
            if (data.containsKey("xzcj_f") && data.getString("xzcj_f").length() > 0) {
                xzcj_f = data.getString("xzcj_f");
                if (!old.getString("xzcj_f").equals(xzcj_f)) {
                    sql = sql + " xzcj_f='" + xzcj_f + "' , ";
                    writeLog(mysql, old.getString("xzcj_f") + "->" + xzcj_f, "bc:xzcj_f", opt_user);
                }
            }
            if (data.containsKey("das_c") && data.getString("das_c").length() > 0) {
                das_c = data.getString("das_c");
                if (!old.getString("das_c").equals(das_c)) {
                    sql = sql + " das_c='" + das_c + "' , ";
                    writeLog(mysql, old.getString("das_c") + "->" + das_c, "bc:das_c", opt_user);
                }
            }
            if (data.containsKey("das_l") && data.getString("das_l").length() > 0) {
                das_l = data.getString("das_l");
                if (!old.getString("das_l").equals(das_l)) {
                    sql = sql + " das_l='" + das_l + "' , ";
                    writeLog(mysql, old.getString("das_l") + "->" + das_l, "bc:das_l", opt_user);
                }
            }
            if (data.containsKey("xscz_p") && data.getString("xscz_p").length() > 0) {
                xscz_p = data.getString("xscz_p");
                if (!old.getString("xscz_p").equals(xscz_p)) {
                    sql = sql + " xscz_p='" + xscz_p + "' , ";
                    writeLog(mysql, old.getString("xscz_p") + "->" + xscz_p, "bc:xscz_p", opt_user);
                }
            }
            if (data.containsKey("xscz_f") && data.getString("xscz_f").length() > 0) {
                xscz_f = data.getString("xscz_f");
                if (!old.getString("xscz_f").equals(xscz_f)) {
                    sql = sql + " xscz_f='" + xscz_f + "' , ";
                    writeLog(mysql, old.getString("xscz_f") + "->" + xscz_f, "bc:xscz_f", opt_user);
                }
            }
            if (data.containsKey("qbsz_g") && data.getString("qbsz_g").length() > 0) {
                qbsz_g = data.getString("qbsz_g");
                if (!old.getString("qbsz_g").equals(qbsz_g)) {
                    sql = sql + " qbsz_g='" + qbsz_g + "' , ";
                    writeLog(mysql, old.getString("qbsz_g") + "->" + qbsz_g, "bc:qbsz_g", opt_user);
                }
            }
            if (data.containsKey("qbsz_f") && data.getString("qbsz_f").length() > 0) {
                qbsz_f = data.getString("qbsz_f");
                if (!old.getString("qbsz_f").equals(qbsz_f)) {
                    sql = sql + " qbsz_f='" + qbsz_f + "' , ";
                    writeLog(mysql, old.getString("qbsz_f") + "->" + qbsz_f, "bc:qbsz_f", opt_user);
                }
            }
            if (data.containsKey("tn_g") && data.getString("tn_g").length() > 0) {
                tn_g = data.getString("tn_g");
                if (!old.getString("tn_g").equals(tn_g)) {
                    sql = sql + " tn_g='" + tn_g + "' , ";
                    writeLog(mysql, old.getString("tn_g") + "->" + tn_g, "bc:tn_g", opt_user);
                }
            }
            if (data.containsKey("tn_f") && data.getString("tn_f").length() > 0) {
                tn_f = data.getString("tn_f");
                if (!old.getString("tn_f").equals(tn_f)) {
                    sql = sql + " tn_f='" + tn_f + "' , ";
                    writeLog(mysql, old.getString("tn_f") + "->" + tn_f, "bc:tn_f", opt_user);
                }
            }
            if (data.containsKey("zgdy_g") && data.getString("zgdy_g").length() > 0) {
                zgdy_g = data.getString("zgdy_g");
                if (!old.getString("zgdy_g").equals(zgdy_g)) {
                    sql = sql + " zgdy_g='" + zgdy_g + "' , ";
                    writeLog(mysql, old.getString("zgdy_g") + "->" + zgdy_g, "bc:zgdy_g", opt_user);
                }
            }
            if (data.containsKey("zgdy_y") && data.getString("zgdy_y").length() > 0) {
                zgdy_y = data.getString("zgdy_y");
                if (!old.getString("zgdy_y").equals(zgdy_y)) {
                    sql = sql + " zgdy_y='" + zgdy_y + "' , ";
                    writeLog(mysql, old.getString("zgdy_y") + "->" + zgdy_y, "bc:zgdy_y", opt_user);
                }
            }
            if (data.containsKey("dwzw_c") && data.getString("dwzw_c").length() > 0) {
                dwzw_c = data.getString("dwzw_c");
                if (!old.getString("dwzw_c").equals(dwzw_c)) {
                    sql = sql + " dwzw_c='" + dwzw_c + "' , ";
                    writeLog(mysql, old.getString("dwzw_c") + "->" + dwzw_c, "bc:dwzw_c", opt_user);
                }
            }
            if (data.containsKey("dwzw_r") && data.getString("dwzw_r").length() > 0) {
                dwzw_r = data.getString("dwzw_r");
                if (!old.getString("dwzw_r").equals(dwzw_r)) {
                    sql = sql + " dwzw_r='" + dwzw_r + "' , ";
                    writeLog(mysql, old.getString("dwzw_r") + "->" + dwzw_r, "bc:dwzw_r", opt_user);
                }
            }
            if (data.containsKey("wzxx_c") && data.getString("wzxx_c").length() > 0) {
                wzxx_c = data.getString("wzxx_c");
                if (!old.getString("wzxx_c").equals(wzxx_c)) {
                    sql = sql + " wzxx_c='" + wzxx_c + "' , ";
                    writeLog(mysql, old.getString("wzxx_c") + "->" + wzxx_c, "bc:wzxx_c", opt_user);
                }
            }
            if (data.containsKey("wzxx_r") && data.getString("wzxx_r").length() > 0) {
                wzxx_r = data.getString("wzxx_r");
                if (!old.getString("wzxx_r").equals(wzxx_r)) {
                    sql = sql + " wzxx_r='" + wzxx_r + "' , ";
                    writeLog(mysql, old.getString("wzxx_r") + "->" + wzxx_r, "bc:wzxx_r", opt_user);
                }
            }
            if (data.containsKey("jbly_g") && data.getString("jbly_g").length() > 0) {
                jbly_g = data.getString("jbly_g");
                if (!old.getString("jbly_g").equals(jbly_g)) {
                    sql = sql + " jbly_g='" + jbly_g + "' , ";
                    writeLog(mysql, old.getString("jbly_g") + "->" + jbly_g, "bc:jbly_g", opt_user);
                }
            }
            if (data.containsKey("jbly_f") && data.getString("jbly_f").length() > 0) {
                jbly_f = data.getString("jbly_f");
                if (!old.getString("jbly_f").equals(jbly_f)) {
                    sql = sql + " jbly_f='" + jbly_f + "' , ";
                    writeLog(mysql, old.getString("jbly_f") + "->" + jbly_f, "bc:jbly_f", opt_user);
                }
            }
            String sqls = "update base_check set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(450003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private JSONObject getProcess(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";

            String id = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }

            String sqls = "select * from process where 1=1 and " + sql + " isdelete=1";
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);

            back.put("data", list);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(451005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }


    //******UPDATE*******
    private JSONObject updateProcess(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String ysdd = "";
            String llpb = "";
            String qs_g = "";
            String qs_f = "";
            String sqmj_g = "";
            String sqmj_f = "";
            String bazx = "";
            String das = "";
            String dq_c = "";
            String dq_tb = "";
            String qz_c = "";
            String qz_tb = "";
            String sc_c = "";
            String sc_tb = "";
            String sd_c = "";
            String sd_tb = "";
            String zh_bz = "";
            String zh_tb = "";
            String opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(451004);
            }
            JSONObject old = new JSONObject();
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");

                String s = "select * from process where id='" + id + "'";
                List<JSONObject> ll = mysql.query(s);

                if (ll.size() > 0) {
                    old = ll.get(0);
                } else {
                    s = "INSERT INTO `process`  VALUES ('" + id + "', '', '', '', '', '', '', '', '', '', '', '', '', '', '',  '', '', '', '', '1');";
                    mysql.update(s);
                    s = "select * from process where id='" + id + "'";
                    ll = mysql.query(s);
                    old = ll.get(0);
                }

            } else {
                id = new SimpleDateFormat("yyyy").format(new Date());
                String s = "INSERT INTO `process`  VALUES ('" + id + "', '', '', '', '', '', '', '', '', '', '', '', '', '', '',  '', '', '', '', '1');";
                mysql.update(s);
                s = "select * from process where id='" + id + "'";
                List<JSONObject> ll = mysql.query(s);
                old = ll.get(0);
            }
            if (data.containsKey("ysdd") && data.getString("ysdd").length() > 0) {
                ysdd = data.getString("ysdd");
                if (!old.getString("ysdd").equals(ysdd)) {
                    sql = sql + " ysdd='" + ysdd + "' , ";
                    writeLog(mysql, old.getString("ysdd") + "->" + ysdd, "ps:ysdd", opt_user);
                }
            }
            if (data.containsKey("llpb") && data.getString("llpb").length() > 0) {
                llpb = data.getString("llpb");
                if (!old.getString("llpb").equals(llpb)) {
                    sql = sql + " llpb='" + llpb + "' , ";
                    writeLog(mysql, old.getString("llpb") + "->" + llpb, "ps:llpb", opt_user);
                }
            }
            if (data.containsKey("qs_g") && data.getString("qs_g").length() > 0) {
                qs_g = data.getString("qs_g");
                if (!old.getString("qs_g").equals(qs_g)) {
                    sql = sql + " qs_g='" + qs_g + "' , ";
                    writeLog(mysql, old.getString("qs_g") + "->" + qs_g, "ps:qs_g", opt_user);
                }
            }
            if (data.containsKey("qs_f") && data.getString("qs_f").length() > 0) {
                qs_f = data.getString("qs_f");
                if (!old.getString("qs_f").equals(qs_f)) {
                    sql = sql + " qs_f='" + qs_f + "' , ";
                    writeLog(mysql, old.getString("qs_f") + "->" + qs_f, "ps:qs_f", opt_user);
                }
            }
            if (data.containsKey("sqmj_g") && data.getString("sqmj_g").length() > 0) {
                sqmj_g = data.getString("sqmj_g");
                if (!old.getString("sqmj_g").equals(sqmj_g)) {
                    sql = sql + " sqmj_g='" + sqmj_g + "' , ";
                    writeLog(mysql, old.getString("sqmj_g") + "->" + sqmj_g, "ps:sqmj_g", opt_user);
                }
            }
            if (data.containsKey("sqmj_f") && data.getString("sqmj_f").length() > 0) {
                sqmj_f = data.getString("sqmj_f");
                if (!old.getString("sqmj_f").equals(sqmj_f)) {
                    sql = sql + " sqmj_f='" + sqmj_f + "' , ";
                    writeLog(mysql, old.getString("sqmj_f") + "->" + sqmj_f, "ps:sqmj_f", opt_user);
                }
            }
            if (data.containsKey("bazx") && data.getString("bazx").length() > 0) {
                bazx = data.getString("bazx");
                if (!old.getString("bazx").equals(bazx)) {
                    sql = sql + " bazx='" + bazx + "' , ";
                    writeLog(mysql, old.getString("bazx") + "->" + bazx, "ps:bazx", opt_user);
                }
            }
            if (data.containsKey("das") && data.getString("das").length() > 0) {
                das = data.getString("das");
                if (!old.getString("das").equals(das)) {
                    sql = sql + " das='" + das + "' , ";
                    writeLog(mysql, old.getString("das") + "->" + das, "ps:das", opt_user);
                }
            }
            if (data.containsKey("dq_c") && data.getString("dq_c").length() > 0) {
                dq_c = data.getString("dq_c");
                if (!old.getString("dq_c").equals(dq_c)) {
                    sql = sql + " dq_c='" + dq_c + "' , ";
                    writeLog(mysql, old.getString("dq_c") + "->" + dq_c, "ps:dq_c", opt_user);
                }
            }
            if (data.containsKey("dq_tb") && data.getString("dq_tb").length() > 0) {
                dq_tb = data.getString("dq_tb");
                if (!old.getString("dq_tb").equals(dq_tb)) {
                    sql = sql + " dq_tb='" + dq_tb + "' , ";
                    writeLog(mysql, old.getString("dq_tb") + "->" + dq_tb, "ps:dq_tb", opt_user);
                }
            }
            if (data.containsKey("qz_c") && data.getString("qz_c").length() > 0) {
                qz_c = data.getString("qz_c");
                if (!old.getString("qz_c").equals(qz_c)) {
                    sql = sql + " qz_c='" + qz_c + "' , ";
                    writeLog(mysql, old.getString("qz_c") + "->" + qz_c, "ps:qz_c", opt_user);
                }
            }
            if (data.containsKey("qz_tb") && data.getString("qz_tb").length() > 0) {
                qz_tb = data.getString("qz_tb");
                if (!old.getString("qz_tb").equals(qz_tb)) {
                    sql = sql + " qz_tb='" + qz_tb + "' , ";
                    writeLog(mysql, old.getString("qz_tb") + "->" + qz_tb, "ps:qz_tb", opt_user);
                }
            }
            if (data.containsKey("sc_c") && data.getString("sc_c").length() > 0) {
                sc_c = data.getString("sc_c");
                if (!old.getString("sc_c").equals(sc_c)) {
                    sql = sql + " sc_c='" + sc_c + "' , ";
                    writeLog(mysql, old.getString("sc_c") + "->" + sc_c, "ps:sc_c", opt_user);
                }
            }
            if (data.containsKey("sc_tb") && data.getString("sc_tb").length() > 0) {
                sc_tb = data.getString("sc_tb");
                if (!old.getString("sc_tb").equals(sc_tb)) {
                    sql = sql + " sc_tb='" + sc_tb + "' , ";
                    writeLog(mysql, old.getString("sc_tb") + "->" + sc_tb, "ps:sc_tb", opt_user);
                }
            }
            if (data.containsKey("sd_c") && data.getString("sd_c").length() > 0) {
                sd_c = data.getString("sd_c");
                if (!old.getString("sd_c").equals(sd_c)) {
                    sql = sql + " sd_c='" + sd_c + "' , ";
                    writeLog(mysql, old.getString("sd_c") + "->" + sd_c, "ps:sd_c", opt_user);
                }
            }
            if (data.containsKey("sd_tb") && data.getString("sd_tb").length() > 0) {
                sd_tb = data.getString("sd_tb");
                if (!old.getString("sd_tb").equals(sd_tb)) {
                    sql = sql + " sd_tb='" + sd_tb + "' , ";
                    writeLog(mysql, old.getString("sd_tb") + "->" + sd_tb, "ps:sd_tb", opt_user);
                }
            }
            if (data.containsKey("zh_bz") && data.getString("zh_bz").length() > 0) {
                zh_bz = data.getString("zh_bz");
                if (!old.getString("zh_bz").equals(zh_bz)) {
                    sql = sql + " zh_bz='" + zh_bz + "' , ";
                    writeLog(mysql, old.getString("zh_bz") + "->" + zh_bz, "ps:zh_bz", opt_user);
                }
            }
            if (data.containsKey("zh_tb") && data.getString("zh_tb").length() > 0) {
                zh_tb = data.getString("zh_tb");
                if (!old.getString("zh_tb").equals(zh_tb)) {
                    sql = sql + " zh_tb='" + zh_tb + "' , ";
                    writeLog(mysql, old.getString("zh_tb") + "->" + zh_tb, "ps:zh_tb", opt_user);
                }
            }

            String sqls = "update process set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(451003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    public static void writeLog(InfoModelHelper mysql, String change, String colName, String opt_user) throws Exception {
        String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String sql = "insert push_log(colName,upContent,opt_user,time) values('" + colName + "','" + change + "','" + opt_user + "','" + time + "');";
        mysql.update(sql);
    }


}

