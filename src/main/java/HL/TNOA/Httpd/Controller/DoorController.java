package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.InfoModel.OracleModelHelper;
import HL.TNOA.Lib.InfoModel.OracleModelPool;
import HL.TNOA.wechat.HttpConnection;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static HL.TNOA.Httpd.Controller.JQController.StringToURL;

@RestController
public class DoorController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/door"})
    @PassToken
    public JSONObject get_door(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        InfoModelHelper mysql1 = null;
        try {


            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("task_type_7")) {
                    return getTaskType7(data);
                } else if (opt.equals("task_trend_7")) {
                    return getTaskTrend7(data);
                } else if (opt.equals("get_task")) {
                    return getTaskList(data);
                } else if (opt.equals("get_calendar")) {
                    mysql1 = request.openInfoImpl();
                    return GetCalendar(data, mysql1);
                } else if (opt.equals("get_calendar_list")) {
                    mysql1 = request.openInfoImpl();
                    return GetCalendarList(data, mysql1);
                } else if (opt.equals("get_sydw_location")) {
                    return GetSYDWLoaction(data);

                } else if (opt.equals("get_sydw_det")) {
                    return GetSYDWDet(data);
                } else if (opt.equals("jq_type_mdjf")) {
                    mysql1 = request.openInfoImpl();
                    return GetMdjf(data, mysql1);
                } else if (opt.equals("get_jq_location")) {
                    return GetJQLocation(data);
                } else if (opt.equals("get_jq_det")) {
                    return GetJQDet(data);
                } else if (opt.equals("get_syrk")) {

                    return GetSYRK(data);
                } else if (opt.equals("get_syfw")) {
                    //mysql = request.openInfoImpl();
                    return GetSYFW(data);
                } else if (opt.equals("get_syfw_gl")) {
                    //   mysql = request.openInfoImpl();
                    return GetSYFWGL(data, request);
                } else if (opt.equals("get_jq_type")) {
                    return GetJQType(data);
                } else if (opt.equals("get_jq_xl")) {
                    return getJQXL(data);
                } else if (opt.equals("get_jq_xxl")) {
                    return getJQXXL(data);
                } else if (opt.equals("rk_type")) {
                    return getRKType(data);
                } else if (opt.equals("search_address")) {
                    return searchAddress(data);
                } else if (opt.equals("search_zh")) {
                    return searchZH(data, request);
                } else if (opt.equals("get_login_count")) {
                    return GetLoginCount(data);
                } else if (opt.equals("get_device_list")) {
                    return getDeviceList(data);
                } else if (opt.equals("get_device_detail")) {
                    return getDeviceDetail(data);
                } else if (opt.equals("get_dict_tree")) {
                    return getDictTree(data);
                } else if (opt.equals("get_wyf_location")) {
                    return getWYFLocation(data);
                } else {
                    return ErrNo.set(501003);
                }
            } else {
                return ErrNo.set(501003);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        }
    }

    private JSONObject getWYFLocation(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;
        String unit = "";
        String usql = "";

        try {
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                return ErrNo.set(501001);
            }


            String type = RIUtil.dicts.get(unit).getString("type");
            if (type.equals("23") || type.equals("24") || type.equals("28")) {
                usql = " and fj = '" + unit.substring(0, 6) + "000000'";

            } else if (type.equals("25")) {
                usql = " and pcs = '" + unit.substring(0, 8) + "0000'";

            } else if (type.equals("26")) {
                usql = " and zrq = '" + unit + "'";

            } else {


            }

            ora_hl = new OracleHelper("ora_hl");

            String sql = "select bh,fyid,dz,dzzbx,dzzby from wyf_jbxx where dzzbx is not null " + usql;
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            if (list.size() > 0) {
                back.put("data", list);
            } else {
                back.put("data", new ArrayList<>());
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            ora_hl.close();

        }
    }


    private JSONObject getDictTree(JSONObject data) {
        MysqlHelper mysql = null;
        GaussHelper gs_hl = null;
        String sql = "";

        JSONObject back = ErrNo.set(0);
        List<JSONObject> resList = new ArrayList<>();
        List<JSONObject> personList = new ArrayList<>();
        List<JSONObject> roomList = new ArrayList<>();
        List<JSONObject> addressList = new ArrayList<>();
        List<JSONObject> unitList = new ArrayList<>();

        String unit = "";

        OracleHelper ora_gl = null;

        String usql = "";
        //高斯
        String gsUsql = "";
        String type = "";
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            type = RIUtil.dicts.get(unit).getString("type");
            if (type.equals("23") || type.equals("24") || type.equals("28")) {
                usql = "sszrq like '" + unit.substring(0, 6) + "%'";
                gsUsql = "\"SSZRQ\" like '" + unit.substring(0, 6) + "%'";
            } else if (type.equals("25")) {
                usql = "sszrq like '" + unit.substring(0, 8) + "%'";
                gsUsql = "\"SSZRQ\" like '" + unit.substring(0, 8) + "%'";
            } else if (type.equals("26")) {
                usql = "sszrq = '" + unit + "'";
                gsUsql = "\"SSZRQ\" = '" + unit + "'";
            } else {
                usql = " 1=1 ";
                gsUsql = "1=1";
            }
        } else {
            return ErrNo.set(501001);
        }
        try {

            mysql = new MysqlHelper("mysql");
            ora_gl = new OracleHelper("ora_bk_gl");
            gs_hl = new GaussHelper("gauss_hl");

            sql = "select count(id) as count,JCLX_XL from czqj_ybds.tb_data_watch_point where " + usql + " group by " + "JCLX_XL";
            HashMap<String, String> xls = new HashMap<>();
            List<JSONObject> list = ora_gl.query(sql);
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    JSONObject one = list.get(i);
                    xls.put(one.getString("JCLX_XL"), one.getString("COUNT"));
                }
            }
            HashMap<String, String> gsXls = new HashMap<>();
            List<JSONObject> gsList = new ArrayList<>();
            if (!gsUsql.contains("1=1")) {

                sql = "select count(qjjc.qjjc_dw1.\"ID\") as \"COUNT\",\"JCLX_XL\" from qjjc.qjjc_dw1 where " + gsUsql + " group by " + " \"JCLX_XL\" ";

                gsList = gs_hl.query(sql);
                if (list.size() > 0) {
                    for (int i = 0; i < gsList.size(); i++) {
                        JSONObject one = gsList.get(i);
//                    gsXls.put(one.getString("JCLX_XL"), one.getString("COUNT"));
                        gsXls.put(one.getString("JCLX_XL"), one.getString("COUNT"));
                    }
                }
            } else {
                sql = "select content from temp where id=1";
                String con = mysql.query_one(sql, "content");
                JSONArray ll = JSONArray.parseArray(con);
                if (ll.size() > 0) {
                    for (int i = 0; i < ll.size(); i++) {
                        JSONObject one = ll.getJSONObject(i);
//                    gsXls.put(one.getString("JCLX_XL"), one.getString("COUNT"));
                        gsXls.put(one.getString("JCLX_XL"), one.getString("COUNT"));
                    }
                }
            }


            sql = "select *, dict_name as label, dict_name as value from dict where isdelete = 1 and  type = 162 ";
            logger.warn(sql);
            roomList = mysql.query(sql);
            for (JSONObject room : roomList) {
                String roomId = room.getString("id");
                String dictName = room.getString("dict_name");
                //排除 id = 160-2002的 已批复创建的地名未关联标准地址的
                sql = "select *, dict_name as label, dict_name as value from dict where isdelete = 1  " + " and type "
                        + "= 160 and id != '160-2002' and father_id = '" + roomId + "' ";
                logger.warn(sql);
                List<JSONObject> roomChildList = mysql.query(sql);
                for (JSONObject one : roomChildList) {
                    String roomName = one.getString("dict_name");
                    String roomIdd = one.getString("id");
                    String id = roomIdd.replace("160-", "");
                    String count = "0";
                    if (xls.containsKey(id)) {
                        count = xls.get(id);
                    }
                    one.put("count", count);
                    one.put("key", roomIdd);
                    one.put("label", roomName);

                }
                room.put("children", roomChildList);
                room.put("label", dictName);
                room.put("key", roomId);
            }

            sql = "select *, dict_name as label, dict_name as value from dict where isdelete = 1 and  type = 163 ";
            logger.warn(sql);
            personList = mysql.query(sql);
            for (JSONObject person : personList) {

                String personId = person.getString("id");
                String dictName = person.getString("dict_name");
                sql = "select *, dict_name as label, dict_name as value from dict where isdelete = 1 " + " and type " + "=" + " 160 and father_id = '" + personId + "' ";
                List<JSONObject> personChildList = mysql.query(sql);
                for (JSONObject one : personChildList) {
                    String personName = one.getString("dict_name");
                    String personIdd = one.getString("id");
                    String id = personIdd.replace("160-", "");
                    String count = "0";
                    if (xls.containsKey(id)) {
                        count = xls.get(id);
                    }
                    one.put("count", count);
                    one.put("key", personIdd);
                    one.put("label", personName);
                }
                person.put("children", personChildList);
                person.put("label", dictName);
                person.put("key", personId);
            }


            sql = "select *, dict_name as label, dict_name as value from dict where isdelete = 1 and  type = 161 ";
            logger.warn(sql);
            addressList = mysql.query(sql);
            for (JSONObject address : addressList) {

                String addressId = address.getString("id");
                String dictName = address.getString("dict_name");
                sql = "select *, dict_name as label, dict_name as value from dict where isdelete = 1  " + " and type "
                        + "= 160 and father_id = '" + addressId + "' ";
                List<JSONObject> addressChildList = mysql.query(sql);
                for (JSONObject one : addressChildList) {
                    String addressName = one.getString("dict_name");
                    String addressIdd = one.getString("id");
                    String id = addressIdd.replace("160-", "");
                    String count = "0";
                    if (xls.containsKey(id)) {
                        count = xls.get(id);
                    }
                    one.put("count", count);
                    one.put("key", addressIdd);
                    one.put("label", addressName);
                }
                address.put("children", addressChildList);
                address.put("label", dictName);
                address.put("key", addressId);
            }

            sql = "select *, dict_name as label, dict_name as value from dict where isdelete = 1 and  type = 164 ";
            logger.warn(sql);
            unitList = mysql.query(sql);
            for (JSONObject units : unitList) {

                String unitsId = units.getString("id");
                String dict_name = units.getString("dict_name");
                sql = "select *, dict_name as label, dict_name as value from dict where isdelete = 1  " + " and type "
                        + "= 160 and father_id = '" + unitsId + "' ";
                logger.warn(sql);
                List<JSONObject> unitChildList = mysql.query(sql);
                for (JSONObject one : unitChildList) {
                    String unitsName = one.getString("dict_name");
                    String unitsIdd = one.getString("id");
                    String id = unitsIdd.replace("160-", "");
                    String count = "0";
                    if (gsXls.containsKey(id)) {
                        count = gsXls.get(id);
                    }
                    one.put("count", count);
                    one.put("key", unitsIdd);
                    one.put("label", unitsName);
                }
                units.put("children", unitChildList);
                units.put("label", dict_name);
                units.put("key", unitsId);
            }


            JSONObject personJson = new JSONObject();
            personJson.put("label", "人");
            personJson.put("key", "人");
            personJson.put("children", personList);

            JSONObject roomJson = new JSONObject();
            roomJson.put("label", "房");
            roomJson.put("key", "房");
            roomJson.put("children", roomList);

            JSONObject addressJson = new JSONObject();
            addressJson.put("label", "地");
            addressJson.put("key", "地");
            addressJson.put("children", addressList);

            JSONObject unitsJson = new JSONObject();
            unitsJson.put("label", "单位");
            unitsJson.put("key", "单位");
            unitsJson.put("children", unitList);

            resList.add(personJson);
            resList.add(roomJson);
//            resList.add(addressJson);
            resList.add(unitsJson);
            back.put("data", resList);

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 501002, Lib.getTrace(e));
        } finally {
            if (mysql != null) {
                mysql.close();
            }
            ora_gl.close();
        }
        return back;
    }

    private JSONObject GetSYFWGL(JSONObject data, TNOAHttpRequest request) {
        String url = "";
        if (data.containsKey("url") && data.getString("url").length() > 0) {
            url = data.getString("url");
        } else {
            return ErrNo.set(501001);
        }
        String token = request.getHeader("token");

        String tokenName = "X-Access-Token";
        logger.warn(url);
        String ret = HttpConnection.http_get_x(url, tokenName, token);
        if (ret.length() > 100) {
            System.out.println(ret.substring(0, 100));
        } else {
            System.out.println(ret);
        }
        try {
            JSONObject back = JSONObject.parseObject(ret);
            return back;
        } catch (Exception ex) {
            return ErrNo.set(null, 501002, Lib.getTrace(ex));
        }


    }

    private JSONObject getDeviceDetail(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper zxqc = null;
        String sql = "";
        String device_id = "";
        List<JSONObject> device_detail = new ArrayList<>();
        try {
            zxqc = new MysqlHelper("mysql_zxqc");
            if (data.containsKey("device_id") && data.getString("device_id").length() > 0) {
                device_id = data.getString("device_id");
                sql = " and deviceId = '" + device_id + "' ";
            } else {
                return ErrNo.set(2);
            }

            String sqls = "select * from zfjly_device_info where 1=1 " + sql;
            device_detail = zxqc.query(sqls);
            if (device_detail.size() == 1) {
                back.put("data", device_detail.get(0));
            } else {
                return ErrNo.set(2);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            zxqc.close();
        }
        return back;
    }

    private JSONObject getDeviceList(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper zxqc = null;
        String unit = "";
        String sql = " ";
        String status_name = "";
        String type = "";
        List<JSONObject> device_list = new ArrayList<>();
        int count = -1;
        try {
            zxqc = new MysqlHelper("mysql_zxqc");
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject done = RIUtil.dicts.get(unit);
                System.out.println(done);

                try {
                    type = done.getString("type");
                } catch (Exception ex) {
                    type = "21";
                }
                String father_id = done.getString("father_id");
                if (father_id.length() < 5) {
                    father_id = done.getString("id");
                }
                String id = done.getString("id");

                if (type.equals("23")) {
                    sql = sql + " and (org like '" + unit.substring(0, 6) + "%') ";
                } else if (type.equals("25") || type.equals("26")) {
                    sql = sql + " and (org='" + unit.substring(0, 8) + "0000')  ";
                } else if (type.equals("24")) {
                    sql = sql + " and org like '" + father_id.substring(0, 6) + "%' ";
                } else if (type.equals("28")) {
                    unit = unit.substring(0, 6);
                    sql = sql + " and org like '" + unit + "%' ";
                }
            }

//            if (data.containsKey("status_name") && data.getString("status_name").length() > 0) {
//                status_name = data.getString("status_name");
//                sql += " and status_name = '" + status_name + "' ";
//            }

            //警车1  电台3  执法16
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                sql += " and type = '" + type + "' ";
            }

            String sqls =
                    "select deviceId, lat, lng, type from zfjly_device_info " + "where 1=1 and status_name = " +
                            "'在线' " + sql + " ";
            device_list = zxqc.query(sqls);

            String count_sql = "select count(deviceId) as count from zfjly_device_info " + "where 1=1 and " +
                    "status_name" + " = '在线' " + sql + " ";
            count = zxqc.query_count(count_sql);

            back.put("data", device_list);
            back.put("count", count);
        } catch (Exception e) {
            logger.warn(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            zxqc.close();
        }
        return back;
    }

    private JSONObject GetLoginCount(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        try {
            back.put("data", RIUtil.login_times);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 505002, Lib.getTrace(ex));

        }
    }

    private JSONObject searchZH(JSONObject data, TNOAHttpRequest request) {
        // logger.warn(data.toString());
        String token = String.valueOf(request.getHeaders("token"));
        JSONObject back = ErrNo.set(0);

        int limit = 20;
        int page = 1;
        String query = "";
        String type = "";
        String q = "";
        String opt_user = "";

        String police_id = "";
        String white = "";
        String bt = "";
        try {

            if (data.containsKey("keyword") && data.getString("keyword").length() > 0) {
                query = data.getString("keyword");
                String[] qs = query.split("\\ ");
                if (qs.length < 2) {
                    bt = "q=bt:%22" + StringToURL(query) + "%22";
                } else {
                    String one = qs[0];
                    String two = qs[1];
                    if (allCN(one)) {
                        bt = "q=bt:%22" + StringToURL(one) + "%22%20AND%20zy:" + StringToURL(two);
                    } else {
                        if (allCN(two)) {
                            bt = "q=bt:%22" + StringToURL(two) + "%22%20AND%20zy:" + StringToURL(one);
                        } else {
                            bt = "q=bt:%22" + StringToURL(one) + "%22%20AND%20zy:" + StringToURL(two);
                        }
                    }
                }

            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type").replace(",", "||");
            } else {
                type = "全部";
            }
            if (!type.equals("全部")) {
                q = "&fq=dl:" + StringToURL(type);

            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
                try {


                    JSONObject uone = RIUtil.users.get(opt_user);
                    white = TNOAConf.get("role", "white");
                    police_id = uone.getString("police_id");
                } catch (Exception ex) {

                }
            }
            HashMap<String, String> polices = new HashMap<>();
            if (!white.contains(police_id) || type.equals("全部") || type.equals("人口")) {
                polices = RIUtil.GetPolices();
            }
            boolean isSearch = true;
            if (query.length() == 18 || query.length() == 11) {
                if (polices.containsKey(query)) {
                    logger.warn(query);
                    isSearch = false;
                }
            }

            if (isSearch) {
                String url =
                        TNOAConf.get("Httpd", "zhcx_solr") + bt + "&start=" + (page - 1) * limit + "&rows=" + limit + "&wt=json" + q + "&hl.fl=zy,bt&hl.simple.post=" + StringToURL("</font>") + "&hl" + ".simple.pre=" + StringToURL("<font color='red'>") + "&hl=on";
                // logger.warn(url);
                String ret = HttpConnection.post(url, new JSONObject());
                // System.out.println(ret);
                JSONObject rrr = JSONObject.parseObject(ret);
                try {
                    JSONObject res = rrr.getJSONObject("response");
                    JSONObject hls = rrr.getJSONObject("highlighting");
                    int count = res.getInteger("numFound");
                    if (count > 0) {
                        back.put("data", DealResults(res, polices, hls));
                        back.put("count", count);
                    } else {
                        back.put("data", new JSONArray());
                        back.put("count", 0);
                    }

                } catch (Exception ex) {
                    logger.error(Lib.getTrace(ex));
                    back.put("data", new JSONArray());
                    back.put("count", 0);
                }
            } else {
                back.put("data", new JSONArray());
                back.put("count", 0);
            }

            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 505002, Lib.getTrace(ex));

        } finally {

        }

    }

    private boolean allCN(String one) {

        for (int i = 0; i < one.length(); i++) {
            if (Character.isDigit(one.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    private JSONArray DealResults(JSONObject res, HashMap<String, String> polices, JSONObject hls) {
        JSONArray back = new JSONArray();
        JSONArray docs = res.getJSONArray("docs");

        for (int i = 0; i < docs.size(); i++) {
            JSONObject one = docs.getJSONObject(i);
            System.out.println(one);
            String zy = one.getString("zy");
            System.out.println(zy);
            if (zy.contains("身份证号")) {
                String iddd = zy.split("\\身份证号：")[1];
                String idnum = iddd.split("\\<")[0];
                if (!polices.containsKey(idnum)) {
                    String telll = zy.split("\\联系电话：")[1];
                    String tel = telll.split("\\<")[0];
                    if (!polices.containsKey(tel)) {
                        String id = one.getString("id");
                        JSONObject hlone = hls.getJSONObject(id);
                        if (hlone.getString("bt") != null) {
                            one.put("bt", hlone.getString("bt"));
                        } else {
                            one.put("bt", one.getString("bt"));
                        }
//                        if (hlone.getString("zy") != null) {
//                            one.put("zy", hlone.getString("zy"));
//                        } else {
//                            one.put("zy", one.getString("zy"));
//                        }
                        one.put("zy", one.getString("zy"));
                        back.add(one);
                    } else {
                        System.out.println(tel);
                    }
                } else {
                    System.out.println(idnum);
                }
            } else {
                String id = one.getString("id");
                JSONObject hlone = hls.getJSONObject(id);
                if (hlone.getString("bt") != null) {
                    one.put("bt", hlone.getString("bt"));
                } else {
                    one.put("bt", one.getString("bt"));
                }
                if (hlone.getString("zy") != null) {
                    one.put("zy", hlone.getString("zy"));
                } else {
                    one.put("zy", one.getString("zy"));
                }

                back.add(one);
            }
        }


        return back;
    }


    private JSONObject GetCalendarList(JSONObject data, InfoModelHelper mysql) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;

        try {
            ora_hl = new OracleHelper("oracle");
            String user_id = "";
            String date = "";
            String id_num = "";
            int page = 1;
            int limit = 20;
            String job = "";

            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
                id_num = RIUtil.users.get(user_id).getString("id_num");

            } else {
                return ErrNo.set(501001);
            }
            if (data.containsKey("job_name") && data.getString("job_name").length() > 0) {
                job = data.getString("job_name");


            } else {
                return ErrNo.set(501001);
            }

            String unit = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                System.out.println(RIUtil.dicts.get(unit));
                int type = RIUtil.dicts.get(unit).getInteger("type");
                System.out.println(type);
                if (!unit.endsWith("0000") && !unit.startsWith("320400")) {
                    unit = unit.substring(0, 8) + "0000";
                }

                if (type == 28 || type == 24) {
                    unit = unit.substring(0, 6) + "000000";
                }
                if (unit.startsWith("320400")) {
                    unit = "320400000000";
                }

            } else {
                return ErrNo.set(501001);
            }

            String rec_id = GetQQbUserId(unit, id_num, job);
            if (data.containsKey("date") && data.getString("date").length() > 0) {
                date = data.getString("date");

            } else {
                return ErrNo.set(501001);
            }


            JSONArray dets = new JSONArray();
            String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());


            long n = System.currentTimeMillis();

            JSONObject one = new JSONObject();
            one.put("date", date);
            String sql = "";

            long d = RIUtil.dateToStamp(date + " 00:00:00");
            List<JSONObject> finish = new ArrayList<>();
            List<JSONObject> unFinsih = new ArrayList<>();

            if (rec_id.length() > 2) {

                if (today.equals(date))//今天
                {
                    //已完成
                    finish = GetFinishList(date, rec_id, ora_hl, limit, page);
                    //当天要逾期的
                    unFinsih = GetUNFinishList(date, rec_id, ora_hl, limit, page);

                } else if (!today.equals(date) && d < n) {//今天往前
                    //已完成
                    finish = GetFinishList(date, rec_id, ora_hl, limit, page);


                } else if (!today.equals(date) && d > n) {//今天往后
                    //当天要逾期的
                    unFinsih = GetUNFinishList(date, rec_id, ora_hl, limit, page);
                }

            }
            JSONObject qqb = new JSONObject();
            qqb.put("finish", finish);
            qqb.put("unfiish", unFinsih);
            one.put("web_task", qqb);

            sql = "select id,father_id,decode(title,'1t2i3t4l5e') as title,level,finished,'事项' as type,end_time " +
                    "from task where " + "isdelete=1 and (accepter like '%" + user_id + "%' or checked like " + "'%" + user_id + "%' or " + "finished " + "like '%" + user_id + "%') and " + "START_TIME<='" + date + " " + "00:00:00' and END_TIME<='" + date + " 23:59:59' " + "UNION " + "select id,father_id,decode" + "(title," + "'1t2i3t4l5e') as title,isTop,readed,'公告' as type," + "check_time from notice " + "where " + "isdelete=1 and (reading like '%" + user_id + "%' " + "or readed like '%" + user_id + "%') " + "and  " + "notice_time<='" + date + " 00:00" + ":00' and check_time>='" + date + " 23:59:59'";

            //  logger.warn(sql);

            List<JSONObject> h5s = mysql.query(sql);
            if (h5s.size() > 0) {
                one.put("h5_task", RealInfo(h5s, user_id));

            } else {
                one.put("h5_task", new JSONArray());
            }

            //安保

            sql = "select id,'' as father_id,title,level,'' as isFin,'' as type,end_time,START_TIME,remark from" + " "
                    + "anbao_info where " + "isdelete=1 and 1=1 and START_TIME<='" + date + " 00:00:00' " + "and " +
                    "end_time>='" + date + " 23:59:59' order by level";
            // System.out.println(sql);
            List<JSONObject> abs = mysql.query(sql);
            if (abs.size() > 0) {
                one.put("ab", abs);

            } else {
                one.put("ab", new JSONArray());
            }

            //备忘录

            sql = "select id,'' as father_id,title,level,'' as isFin,'' as type,end_time,START_TIME,remark from " +
                    "memo where " + "isdelete=1 and create_user='" + user_id + "' and START_TIME<='" + date + " " +
                    "00:00" + ":00' and " + "end_time>='" + date + " 23:59:59' order by level";
            //  System.out.println(sql);
            List<JSONObject> memos = mysql.query(sql);
            if (memos.size() > 0) {
                one.put("memo", memos);

            } else {
                one.put("memo", new JSONArray());
            }
            dets.add(one);
            back.put("data", dets);


            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 501002, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
            InfoModelPool.putModel(mysql);
        }

    }

    private List<JSONObject> GetFinishList(String date, String rec_id, OracleHelper ora_hl, int limit, int page) {
        //已完成

        try {
            String sql = "select * from QQB_TASK_OBJECT where EXECUTORIDS like '%" + rec_id + "%' " + "and " +
                    "STATUS in(1,3)  and  FINISH_TIME like '%" + date + "%' and DELETED=0 order by FINISH_TIME " +
                    "desc OFFSET " + (page - 1) * limit + " ROWS FETCH NEXT " + limit + " ROWS ONLY ";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            return list;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return new ArrayList<>();
        }
    }

    private List<JSONObject> GetUNFinishList(String date, String rec_id, OracleHelper ora_hl, int limit, int page) {
        //已完成

        try {
            String sql = "select * from QQB_TASK_OBJECT where EXECUTORIDS like '%" + rec_id + "%' " + "and " +
                    "STATUS =2  and   DELETED=0  order by WHEN_FINISH " + " OFFSET " + (page - 1) * limit + " " +
                    "ROWS " + "FETCH NEXT " + limit + " ROWS ONLY ";
            logger.warn(sql);
            List<JSONObject> list = ora_hl.query(sql);
            return list;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return new ArrayList<>();
        }
    }

    private JSONObject searchAddress(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_gl = null;
        String address = "";
        String dm = "";

        String sql = "  1=1 and ";
        try {
            ora_gl = new OracleHelper("ora_gl");
            if (data.containsKey("address") && data.getString("address").length() > 0) {
                address = data.getString("address");
                sql = sql + " DZ like '%" + address + "%' and ";
            }

            if (data.containsKey("dm") && data.getString("dm").length() > 0) {
                dm = data.getString("dm");
                JSONObject done = RIUtil.dicts.get(dm);
                String type = done.getString("type");
                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    dm = dm.substring(0, 4);
                } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                    dm = dm.substring(0, 6);

                } else if (type.equals("25")) {
                    dm = dm.substring(0, 8);
                } else if (type.equals("26")) {

                }
                sql = sql + " HJZRQ like '%" + dm + "%' and ";
            }

            String sqls =
                    "select FDDZ,DZZBX,DZZBY,DZ,DZID from CZQJ_YBDS.ADDRESS_INFO where " + sql + " SWZSH='本号' " +
                            "and " + "DZZT=1 ";
            logger.warn(sqls);
            List<JSONObject> list = ora_gl.query(sqls);
            if (list.size() > 0) {
                back.put("data", list);

            } else {
                back.put("data", new ArrayList<>());
            }
            return back;

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 505002, Lib.getTrace(e));
        } finally {
            ora_gl.close();
        }


    }


    private JSONObject getRKType(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";
        String sqlT = " and 1=1 ";

        OracleHelper ora_gl = null;
        if (data.containsKey("unit") && data.getString("unit").length() > 0) {
            unit = data.getString("unit");
            JSONObject uone = RIUtil.dicts.get(unit);
            String type = uone.getString("type");

            if (type.equals("21") || type.equals("22") || type.equals("27")) {
                sqlT = " and JGBM LIKE '3204%'";
            } else if (type.equals("23") || type.equals("24") || type.equals("28")) {
                sqlT = " and JGBM LIKE '" + unit.substring(0, 6) + "%'";
            } else if (type.equals("25")) {
                sqlT = " and JGBM LIKE '" + unit.substring(0, 8) + "%'";
            } else if (type.equals("26")) {
                sqlT = " and JGBM ='" + unit + "'";
            }
        } else {
            return ErrNo.set(501001);
        }
        try {
            String tjrq = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            tjrq = RIUtil.GetNextDate(tjrq, -1);
            tjrq = tjrq.substring(0, 10).replace("-", "");
            ora_gl = new OracleHelper("ora_bk_gl");
            String sql = "select sum(SYRKTOTAL) as RK_TOTAL,sum(HJRKTOTAL) as RK_HJ,sum(JZRKTOTAL) as RK_JZ,SUM" +
                    "(LDRK_HJS) " + "AS RK_LD  from " + "CZQJ_YBDS.TB_REPORT_SYRK where TJRQ='" + tjrq + "' " + sqlT;
            logger.warn(sql);
            List<JSONObject> list = ora_gl.query(sql);
            if (list.size() > 0) {
                back.put("data", list.get(0));
            } else {
                back.put("data", new JSONObject());
            }
            return back;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 501002, Lib.getTrace(ex));
        } finally {
            ora_gl.close();
        }


    }

    private JSONObject getJQXL(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";

        String sqlT = " and 1=1";

        OracleModelHelper oracle = null;
        String type50 = "";
        try {
            oracle = OracleModelPool.getModel();
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                System.out.println(uone);
                String type = uone.getString("type");
                String father_id = uone.getString("father_id");
                if (type.equals("21") || type.equals("22") || type.equals("27")) {

                } else if (type.equals("23")) {
                    sqlT = " and FJ='" + unit + "'";
                } else if (type.equals("24")) {
                    sqlT = " and FJ='" + father_id + "'";
                } else if (type.equals("25")) {
                    sqlT = " and PCS='" + unit + "'";
                } else if (type.equals("26")) {
                    sqlT = " and ( PCS='" + father_id + "' or ZRQ='" + unit + "')";
                } else if (type.equals("28")) {
                    unit = unit.substring(0, 6) + "000000";
                    sqlT = " and FJ='" + unit + "'";
                }
            }
            if (data.containsKey("type50") && data.getString("type50").length() > 0) {
                type50 = data.getString("type50");

            } else {
                return ErrNo.set(501001);
            }

            String sql = "select TYPE52,count(TYPE52) as count from DSJ_JQ where TYPE50='" + type50 + "' " + sqlT +
                    " " + "group by " + "TYPE52 order by count desc ";

            System.out.println(sql);
            List<JSONObject> list = oracle.query(sql);
            if (list.size() > 0) {
                back.put("data", RelaInfo_jqType(list));

            } else {
                back.put("data", new JSONArray());

            }

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 501002, Lib.getTrace(ex));
        } finally {
            OracleModelPool.putModel(oracle);
        }

    }

    private JSONObject getJQXXL(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String unit = "";

        String sqlT = " and 1=1";

        OracleModelHelper oracle = null;
        String type52 = "";
        try {
            oracle = OracleModelPool.getModel();
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                String type = uone.getString("type");
                String father_id = uone.getString("father_id");
                if (type.equals("21") || type.equals("22") || type.equals("27")) {

                } else if (type.equals("23")) {
                    sqlT = " and FJ='" + unit + "'";
                } else if (type.equals("24")) {
                    sqlT = " and FJ='" + father_id + "'";
                } else if (type.equals("25")) {
                    sqlT = " and PCS='" + unit + "'";
                } else if (type.equals("26")) {
                    sqlT = " and PCS='" + father_id + "'";
                } else if (type.equals("28")) {
                    unit = unit.substring(0, 6) + "000000";
                    sqlT = " and FJ='" + unit + "'";
                }
            }
            if (data.containsKey("type52") && data.getString("type52").length() > 0) {
                type52 = data.getString("type52").replace(",", "','");

            } else {
                return ErrNo.set(501001);
            }

            String sql = "select CJLB,count(CJLB) as count from DSJ_JQ where TYPE52 in('" + type52 + "') " + sqlT +
                    " " + "group by " + "CJLB order by count desc ";

            System.out.println(sql);
            List<JSONObject> list = oracle.query(sql);
            if (list.size() > 0) {
                back.put("data", RelaInfo_jqType(list));

            } else {
                back.put("data", new JSONArray());

            }

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 501002, Lib.getTrace(ex));
        } finally {
            OracleModelPool.putModel(oracle);
        }

    }

    private JSONObject GetJQType(JSONObject data) {
        System.out.println(data);
        JSONObject back = ErrNo.set(0);
        String unit = "";

        String sqlT = " and 1=1";

        OracleModelHelper oracle = null;
        try {
            oracle = OracleModelPool.getModel();
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                System.out.println(uone);
                String type = uone.getString("type");
                String father_id = uone.getString("father_id");
                if (type.equals("21") || type.equals("22") || type.equals("27")) {

                } else if (type.equals("23")) {
                    sqlT = " and FJ='" + unit + "'";
                } else if (type.equals("24")) {
                    sqlT = " and FJ='" + father_id + "'";
                } else if (type.equals("25")) {
                    sqlT = " and PCS='" + unit + "'";
                } else if (type.equals("26")) {
                    sqlT = " and PCS='" + father_id + "'";
                } else if (type.equals("28")) {
                    unit = unit.substring(0, 6) + "000000";
                    sqlT = " and fj='" + unit + "'";
                }
            }

            String sql = "select TYPE50,count(TYPE50) as count from DSJ_JQ where 1=1 " + sqlT + " group by TYPE50";

            System.out.println(sql);
            List<JSONObject> list = oracle.query(sql);
            if (list.size() > 0) {
                back.put("data", RelaInfo_jqType(list));

            } else {
                back.put("data", new JSONArray());

            }

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            OracleModelPool.putModel(oracle);
        }

    }

    private Object RelaInfo_jqType(List<JSONObject> list) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);

            if (one.containsKey("TYPE50")) {
                String type50 = one.getString("TYPE50");
                System.out.println(type50);
                try {
                    String typeName = RIUtil.dicts.get(type50).getString("dict_name");
                    one.put("TYPE_NAME", typeName);
                } catch (Exception ex) {

                }
            }
            if (one.containsKey("TYPE52")) {
                String type50 = one.getString("TYPE52");
                String typeName = RIUtil.dicts.get(type50).getString("dict_name");
                one.put("TYPE_NAME", typeName);
            }
            if (one.containsKey("CJLB")) {
                String type50 = one.getString("CJLB");
                String typeName = RIUtil.dicts.get(type50).getString("dict_name");
                one.put("TYPE_NAME", typeName);
            }

            back.add(one);

        }
        return back;
    }


    private JSONObject GetSYRK(JSONObject data) {
        logger.warn(data.toString());

        String type = "";
        JSONObject back = ErrNo.set(0);
        String sql = " and 1=1 ";
        String unit = "320400000000";


        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");


            }

            String lx = RIUtil.dicts.get(unit).getString("type");
            if (lx.equals("21") || lx.equals("22") || lx.equals("27")) {

            } else if (lx.equals("23") || lx.equals("24") || lx.equals("28")) {
                sql = sql + " and  FJ='" + unit + "' ";

            } else if (lx.equals("25")) {
                sql = sql + " and PCS='" + unit + "' ";

            } else if (lx.equals("26")) {
                sql = sql + " and ZRQ='" + unit + "' ";

            }

            String sqls = " ";
            String sqlCount = "";
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");

            } else {
                return ErrNo.set(501001);
            }
            if (type.equals("11")) {//户籍
                sqls = " select X,Y,HJ as COUNT from HL.MH_SYRK where HJ>0 " + sql;
                sqlCount = " select sum(HJ) as COUNT from HL.MH_SYRK where 1=1 " + sql;
            } else if (type.equals("13")) {//寄住
                sqls = " select X,Y,JZ as COUNT from HL.MH_SYRK where JZ>0 " + sql;
                sqlCount = " select sum(JZ) as COUNT from HL.MH_SYRK where 1=1 " + sql;
            } else if (type.equals("12")) {//流动
                sqls = " select X,Y,LD as COUNT from HL.MH_SYRK where LD>0 " + sql;
                sqlCount = " select sum(LD) as COUNT from HL.MH_SYRK where 1=1 " + sql;
            } else if (type.equals("14")) {//人户一致
                sqls = " select X,Y,YZ as COUNT from HL.MH_SYRK where YZ>0 " + sql;
                sqlCount = " select sum(YZ) as COUNT from HL.MH_SYRK where 1=1 " + sql;
            } else if (type.equals("10")) {//tot
                sqls = " select X,Y,RK as COUNT from HL.MH_SYRK where RK>0 " + sql;
                sqlCount = " select sum(RK) as COUNT from HL.MH_SYRK where 1=1 " + sql;
            } else if (type.equals("20")) {//房屋tot
                sqls = " select X,Y,FW as COUNT from HL.MH_SYRK where FW>0 " + sql;
                sqlCount = " select sum(FW) as COUNT from HL.MH_SYRK where 1=1 " + sql;
            } else if (type.equals("21")) {//房屋群租
                sqls = " select X,Y,QZ as COUNT from HL.MH_SYRK where QZ>0 " + sql;
                sqlCount = " select sum(QZ) as COUNT from HL.MH_SYRK where 1=1 " + sql;
            } else if (type.equals("22")) {//房屋出租
                sqls = " select X,Y,CZ as COUNT from HL.MH_SYRK where CZ>0 " + sql;
                sqlCount = " select sum(CZ) as COUNT from HL.MH_SYRK where 1=1 " + sql;
            } else if (type.equals("23")) {//房屋自购
                sqls = " select X,Y,ZG as COUNT from HL.MH_SYRK where ZG>0 " + sql;
                sqlCount = " select sum(ZG) as COUNT from HL.MH_SYRK where 1=1 " + sql;
            } else if (type.equals("24")) {//房屋空户
                sqls = " select X,Y,KZ as COUNT from HL.MH_SYRK where KZ>0 " + sql;
                sqlCount = " select sum(KZ) as COUNT from HL.MH_SYRK where 1=1 " + sql;
            } else {
                return ErrNo.set(501001);
            }

            logger.warn(sqls);
            List<JSONObject> list = ora_hl.query(sqls);

            int count = ora_hl.query_count(sqlCount);
            back.put("count", count);
            if (list.size() > 0) {

                back.put("data", list);

            } else {
                back.put("data", new JSONArray());
            }
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }

    }

    private JSONObject GetSYFW(JSONObject data) {
        logger.warn(data.toString());

        String type = "";
        JSONObject back = ErrNo.set(0);
        String sql = " and 1=1 ";
        String unit = "320400000000";


        GaussHelper gs_hl = null;
        try {
            gs_hl = new GaussHelper("gauss_hl");

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");


            }

            String lx = RIUtil.dicts.get(unit).getString("type");
            if (lx.equals("21") || lx.equals("22") || lx.equals("27")) {

            } else if (lx.equals("23") || lx.equals("24") || lx.equals("28")) {
                sql = sql + " and  FJDM='" + unit + "' ";

            } else if (lx.equals("25")) {
                sql = sql + " and PCSDM='" + unit + "' ";

            } else if (lx.equals("26")) {
                sql = sql + " and HJZRQ='" + unit + "' ";

            }

            String sqls = " ";

            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");

            } else {
                return ErrNo.set(501001);
            }
            if (type.equals("20")) {//房屋tot
                sqls = " select a.id,dzzbx as X    ,dzzby AS Y from qjjc.yw_syfw a left join qjjc.address_info b  on" + " a.dzxx_id=b.dzid " + " where 1=1 " + sql;

            } else if (type.equals("21")) {//房屋群租
                sqls = " select a.id,dzzbx as X    ,dzzby AS Y from qjjc.yw_syfw a left join qjjc.address_info b  on" + " a.dzxx_id=b.dzid where fwzt=0 and sfqz=1 and czjz_pdbs=1 " + sql;

            } else if (type.equals("22")) {//房屋出租
                sqls = " select a.id,dzzbx as X    ,dzzby AS Y from qjjc.yw_syfw a left join qjjc.address_info b  on" + " a.dzxx_id=b.dzid where  fwzt=0  and czjz_pdbs=1 " + sql;

            } else if (type.equals("23")) {//房屋自购
                sqls = " select a.id,dzzbx as X    ,dzzby AS Y from qjjc.yw_syfw a left join qjjc.address_info b  on" + " a.dzxx_id=b.dzid where fwzt=0 and zflx='3' and czjz_pdbs=1 " + sql;
            } else if (type.equals("24")) {//房屋空户
                sqls = " select a.id,dzzbx as X    ,dzzby AS Y from qjjc.yw_syfw a left join qjjc.address_info b  on" + " a.dzxx_id=b.dzid  where fwzt=0 and (jzrs=0 or jzrs is null) and czjz_pdbs=1 " + sql;
            } else {
                return ErrNo.set(501001);
            }

            logger.warn(sqls);
            List<JSONObject> list = gs_hl.query(sqls);

            int count = list.size();
            back.put("count", count);
            if (list.size() > 0) {

                back.put("data", list);

            } else {
                back.put("data", new JSONArray());
            }
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            gs_hl.close();
        }

    }


    private JSONObject GetJQDet(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleModelHelper oracle = null;
        try {
            oracle = OracleModelPool.getModel();
            String jjbh = "";
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");
            } else {
                return ErrNo.set(501001);
            }


            String sql = "select * from HL.DSJ_JQ where JJBH='" + jjbh + "'";
            List<JSONObject> list = oracle.query(sql);
            if (list.size() > 0) {
                back.put("data", RelaInfo_dicts(list));
            } else {
                back.put("data", new JSONArray());
            }
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            OracleModelPool.putModel(oracle);
        }


    }


    private JSONObject GetJQLocation(JSONObject data) {
        logger.warn(data.toString());
        String dm = "";

        String type50 = "";

        JSONObject back = ErrNo.set(0);
        String sql = "1=1 ";

        OracleModelHelper oracle = null;
        try {
            oracle = OracleModelPool.getModel();
            if (data.containsKey("dm") && data.getString("dm").length() > 0) {
                dm = data.getString("dm");
                JSONObject done = RIUtil.dicts.get(dm);
                System.out.println(done);

                String type = "";
                try {
                    type = done.getString("type");
                } catch (Exception ex) {
                    type = "21";
                }
                String father_id = done.getString("father_id");
                if (father_id.length() < 5) {
                    father_id = done.getString("id");
                }
                String id = done.getString("id");


                if (type.equals("21") || type.equals("22") || type.equals("27")) {

                } else if (type.equals("23")) {
                    sql = sql + " and FJ='" + dm + "' ";
                } else if (type.equals("25")) {
                    sql = sql + " and PCS='" + dm + "' ";
                } else if (type.equals("24")) {
                    sql = sql + " and FJ='" + father_id + "' ";
                } else if (type.equals("26")) {
                    sql = sql + " and ZRQ='" + dm + "' ";
                } else if (type.equals("28")) {
                    dm = dm.substring(0, 6) + "000000";
                    sql = sql + " and FJ='" + dm + "' ";
                }


                //  sql = sql + " and (CHJDW_GAJGJGDM in('" + dm + "') or FJ in ('" + dm + "') or PCS in ('" + dm
                //  + "')
                //  )  ";
            }


            if (data.containsKey("type50") && data.getString("type50").length() > 2) {
                type50 = data.getString("type50").replace(",", "','");
                sql = sql + " and (CJLB in ('" + type50 + "') or TYPE50 in ('" + type50 + "')) ";
            }


            String sqls = "select JJBH ,LAT,LNG from HL.DSJ_JQ where " + sql + " and SFGLDZ_PDBZ=1";
            logger.warn(sqls);
            List<JSONObject> list = oracle.query(sqls);
            sqls = "select count(JJBH) as count from DSJ_JQ where  SFGLDZ_PDBZ=1";
            int count = oracle.query_count(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo_latlng(list));


            } else {
                back.put("data", new JSONArray());

            }
            back.put("count", count);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            oracle.close();
        }

    }

    private List<JSONObject> RelaInfo_latlng(List<JSONObject> list) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String lat = one.getString("LAT");
            String lng = one.getString("LNG");
            one.put("YZB", lat);
            one.put("XZB", lng);
            one.remove("LAT");
            one.remove("LNG");
            back.add(one);
        }
        return back;
    }

    private List<JSONObject> RelaInfo_syrk(List<JSONObject> list) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String lat = one.getString("lat");
            String lng = one.getString("lng");
            one.put("YZB", lat);
            one.put("XZB", lng);
            one.remove("lat");
            one.remove("lng");

            if (one.containsKey("sj_person")) {
                one.put("count", one.getString("sj_person"));
                one.remove("sj_person");
            }
            if (one.containsKey("hj_person")) {
                one.put("count", one.getString("hj_person"));
                one.remove("hj_person");
            }
            if (one.containsKey("ld_person")) {
                one.put("count", one.getString("ld_person"));
                one.remove("ld_person");
            }

            if (one.containsKey("rhbyz")) {
                one.put("count", one.getString("rhbyz"));
                one.remove("rhbyz");
            }
            if (one.containsKey("rhyz")) {
                one.put("count", one.getString("rhyz"));
                one.remove("rhyz");
            }
            if (one.containsKey("jz")) {
                one.put("count", one.getString("jz"));
                one.remove("jz");
            }
            if (one.containsKey("zk")) {
                one.put("count", one.getString("zk"));
                one.remove("zk");
            }
            back.add(one);
        }
        return back;
    }

    private JSONObject GetMdjf(JSONObject data, InfoModelHelper mysql) {
        JSONObject back = ErrNo.set(0);
        String orgSql = " and 1=1";
        try {
            if (data.containsKey("org_id") && data.getString("org_id").length() > 0) {
                String orgId = data.getString("org_id");
                String type = RIUtil.dicts.get(orgId).getString("type");

                if (type.equals("21") || type.equals("22") || type.equals("27")) {
                    orgSql = " and 1=1";
                } else if (type.equals("24") || type.equals("25") || type.equals("26")) {
                    orgSql = " and CJDW='" + orgId + "'";
                } else if (type.equals("23") || type.equals("28")) {
                    orgSql = " and CJDW like '%" + orgId.substring(0, 6) + "%'";
                }
            }

            String sql = "select count(JJBH) as count,CJLB from DSJ_JQ where CJLB like '%50-08%' " + orgSql + " " +
                    "group" + " " + "by " + "CJLB order by  count desc limit 5";

            List<JSONObject> list = mysql.query(sql);
            if (list.size() > 0) {
                back.put("data", RelaInfo_dicts(list));
            } else {
                back.put("data", new JSONArray());
            }
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private List<JSONObject> RelaInfo_dicts(List<JSONObject> list) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String CJLB = one.getString("CJLB");
            String name = RIUtil.dicts.get(CJLB).getString("dict_name");
            one.put("name", name);
            back.add(one);
        }
        return back;
    }

    private JSONObject GetSYDWLoaction(JSONObject data) {
        logger.warn(data.toString());
        String type = "";

        JSONObject back = ErrNo.set(0);
        String sql = " ";

        String unit = "320400000000";

        GaussHelper gs_hl = null;
        OracleHelper ora_bt = null;
        try {
            gs_hl = new GaussHelper("gauss_hl");
            ora_bt = new OracleHelper("ora_bk_bt");
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");


            }

            String lx = RIUtil.dicts.get(unit).getString("type");
            if (lx.equals("21") || lx.equals("22") || lx.equals("27")) {

            } else if (lx.equals("23") || lx.equals("24") || lx.equals("28")) {
                sql = sql + " and  SSFXJ='" + unit.substring(0, 6) + "000000' ";

            } else if (lx.equals("25")) {
                sql = sql + " and SSPCS='" + unit.substring(0, 8) + "0000' ";

            } else if (lx.equals("26")) {
                sql = sql + " and SSZRQ='" + unit + "' ";

            }

            String sqls = "";

            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
                if (!type.equals("20")) {
                    sql = sql + " and GLBM LIKE '%" + type + "%' ";

                    sqls = "select JGBH ,XZB AS X,YZB AS Y,hyxl , dwmc as \"NAME\" from qjjc.CZJG_JBXX A " +
                            "LEFT" + " JOIN " + "qjjc" + ".CZJG_JZGL B ON " + "A" + ".JGBH=B" + ".GLJGBH " + "WHERE " +
                            "YYZT='00' AND XZB IS " + "NOT NULL and A.SFST='1' " + sql;
                    logger.warn(sqls);
                    List<JSONObject> list = gs_hl.query(sqls);
                    if (list.size() > 0) {


                        back.put("data", RelaInfo_dwDL(list, type));
                        back.put("count", list.size());

                    } else {
                        back.put("data", new JSONArray());
                        back.put("count", 0);
                    }
                } else {
                    sqls = "select a.JLBH as JGBH ,XZB AS X,YZB AS Y,'屏' as \"HYDL\", ywdw as \"NAME\" FROM " +
                            "CZJG_GGDZP A " + "left" + " join" + " " + "CZJG_JZGL B on A.YWDWJGBH=B.GLJGBH where XZB "
                            + "IS NOT NULL" + sql;

                    logger.warn(sqls);
                    List<JSONObject> list = ora_bt.query(sqls);
                    if (list.size() > 0) {
                        back.put("data", RelaInfo_dwDL(list, type));
                        back.put("count", list.size());

                    } else {
                        back.put("data", new JSONArray());
                        back.put("count", 0);
                    }

                }
            }


            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 501002, Lib.getTrace(ex));
        } finally {
            if (gs_hl != null) {
                gs_hl.close();
            }

            if (ora_bt != null) {
                ora_bt.close();
            }
        }

    }

    private JSONObject GetSYDWDet(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_bt = null;
        try {
            ora_bt = new OracleHelper("ora_bk_bt");
            String jlbh = "";
            String unit = "320400000000";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            }


            JSONObject det = RIUtil.dicts.get(unit);
            String type = det.getString("type");
            String sql = "";
            if (type.equals("23") || type.equals("24") || type.equals("28")) {
                sql = " and a.ssfxj='" + unit.substring(0, 6) + "000000'";
            } else if (type.equals("25")) {
                sql = " and a.sspcs='" + unit.substring(0, 8) + "0000'";
            } else if (type.equals("26")) {
                sql = " and a.sszrq='" + unit.substring(0, 6) + "000000'";
            }


            if (data.containsKey("jgbh") && data.getString("jgbh").length() > 0) {
                jlbh = data.getString("jgbh");
            } else {
                return ErrNo.set(501001);
            }


            String sqls = "select * from JWRY_DBA.V_SYDW where JGBH='" + jlbh + "'";
            List<JSONObject> list = ora_bt.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo_dw(list, ora_bt));
            } else {
                back.put("data", new JSONArray());
            }
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 501002, Lib.getTrace(ex));
        } finally {
            ora_bt.close();
        }


    }

    private List<JSONObject> RelaInfo_dw(List<JSONObject> list, OracleHelper ora_bt) {
        HashMap<String, JSONObject> djjds = new HashMap<>();
        try {

            String sql = "select jgbh,djjdjws,djjdfjs from jwry_dba.czjg_tzhy where type='01' and jgbh is not " +
                    "null" + "  and " + "(djjdjws is not null or djjdfjs is not null)";
            logger.warn(sql);
            List<JSONObject> dets = ora_bt.query(sql);

            for (int a = 0; a < dets.size(); a++) {
                JSONObject one = dets.get(a);
                String jgbh = one.getString("JGBH");
                djjds.put(jgbh, one);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        }
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String GLBM = one.getString("GLBM").replace(",", ",74-");
            GLBM = "74-" + GLBM;
            logger.warn(GLBM);
            one.put("GLBM", RIUtil.RealDictNameList(RIUtil.StringToList(GLBM)));

            if (one.containsKey("HYDL")) {
                String hydl = one.getString("HYDL");
                one.put("HYDL", RIUtil.RealDictNameList(RIUtil.StringToList(hydl)));

            } else {
                one.put("HYDL", new JSONObject());
            }
            if (one.containsKey("HYXL")) {
                String hydl = one.getString("HYXL");
                one.put("HYXL", RIUtil.RealDictNameList(RIUtil.StringToList(hydl)));

            } else {
                one.put("HYXL", new JSONObject());
            }
            if (one.containsKey("HYXXL")) {
                String hydl = one.getString("HYXXL");
                one.put("HYXXL", RIUtil.RealDictNameList(RIUtil.StringToList(hydl)));

            } else {
                one.put("HYXXL", new JSONObject());
            }
            one.put("SSFXJ", RIUtil.dicts.get(one.getString("SSFXJ")));
            one.put("SSPCS", RIUtil.dicts.get(one.getString("SSPCS")));
            one.put("SSZRQ", RIUtil.dicts.get(one.getString("SSZRQ")));

            String jgbh = one.getString("JGBH");
            if (djjds.containsKey(jgbh)) {
                one.putAll(djjds.get(jgbh));
            }


            back.add(one);

        }
        return back;
    }

    private List<JSONObject> RelaInfo_dwDL(List<JSONObject> list, String type) {
        HashMap<String, JSONObject> djjds = new HashMap<>();
        if (type.equals("06")) {
            OracleHelper ora_bt = null;

            try {
                ora_bt = new OracleHelper("ora_bk_bt");
                String sql =
                        "select jgbh,djjdjws,djjdfjs from jwry_dba.czjg_tzhy where type='01' and jgbh is not " +
                                "null" + "  and " + "(djjdjws is not null or djjdfjs is not null)";
                logger.warn(sql);
                List<JSONObject> dets = ora_bt.query(sql);

                for (int a = 0; a < dets.size(); a++) {
                    JSONObject one = dets.get(a);
                    String jgbh = one.getString("JGBH");
                    djjds.put(jgbh, one);
                }
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            } finally {
                ora_bt.close();
            }


        }


        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);


            if (one.containsKey("hyxl") && one.getString("hyxl").length() > 0) {

                try {
                    String hydl = one.getString("hyxl");

                    hydl = hydl.split(",")[0];
                    String hydls = RIUtil.dicts.get(hydl).getString("dict_name");
                    if (hydls.startsWith("涉")) {
                        hydls = hydls.replace("涉", "");
                    }

                    one.put("HYDL", hydls.substring(0, 2));

                } catch (Exception ex) {
                    // logger.error(Lib.getTrace(ex));
                    one.put("HYDL", "无");
                }
            } else {

                if (one.containsKey("HYDL") && one.getString("HYDL").length() > 0) {

                } else {
                    one.put("HYDL", "无");
                }
            }
            if (one.containsKey("x")) {
                one.put("X", one.getString("x"));
                one.put("Y", one.getString("y"));

                one.remove("x");
                one.remove("y");
            }
            String jgbh = one.getString("jgbh");
            if (djjds.containsKey(jgbh)) {
                one.putAll(djjds.get(jgbh));
            }

            back.add(one);


        }
        return back;
    }

    private JSONObject GetCalendar(JSONObject data, InfoModelHelper mysql) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;

        try {
            ora_hl = new OracleHelper("oracle");
            String user_id = "";
            String qqb_id = "";
            String START_TIME = "";
            String end_time = "";
            String usql = " and  1=1";
            String id_num = "";
            String job = "";

            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
                id_num = RIUtil.users.get(user_id).getString("id_num");

            } else {
                return ErrNo.set(501001);
            }
            if (data.containsKey("job_name") && data.getString("job_name").length() > 0) {
                job = data.getString("job_name");


            } else {
                return ErrNo.set(501001);
            }

            String unit = "";
            String anbao_unit = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                anbao_unit = unit;
                System.out.println(RIUtil.dicts.get(unit));
                int type = RIUtil.dicts.get(unit).getInteger("type");
                System.out.println(type);
                if (!unit.endsWith("0000") && !unit.startsWith("320400")) {
                    unit = unit.substring(0, 8) + "0000";
                }

                if (type == 28) {
                    unit = unit.substring(0, 6) + "000000";
                }
                if (unit.startsWith("320400")) {
                    unit = "320400000000";
                }


                if (type == 21 || type == 22 || type == 27) {
                    usql = " and unit='3204'  ";
                } else if (type == 23 || type == 28 || type == 24) {
                    usql = " and  (unit='3204' or  unit='" + unit.substring(0, 6) + "' )  ";
                } else {
                    usql = " and (unit='3204' or  unit='" + unit.substring(0, 6) + "' or unit ='" + unit.substring(0,
                            8) + "' ) " + " ";
                }

            } else {
                return ErrNo.set(501001);
            }

            String rec_id = GetQQbUserId(unit, id_num, job);
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                START_TIME = data.getString("start_time");

            } else {
                return ErrNo.set(501001);
            }
            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time");
            } else {
                return ErrNo.set(501001);
            }

            String date = START_TIME;


            JSONArray dets = new JSONArray();
            String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

            String now = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            long n = System.currentTimeMillis();
            String sql =
                    "select STATUS,WHEN_FINISH,FINISH_TIME from QQB_TASK_OBJECT where EXECUTORIDS like '%" + rec_id + "%' and ((WHEN_FINISH>='" + START_TIME + "' AND WHEN_FINISH<='" + end_time + "') OR " + "(FINISH_TIME" + ">='" + START_TIME + "' AND FINISH_TIME<='" + end_time + "')  OR STATUS=2) and " + "DELETED=0";
            List<JSONObject> list = ora_hl.query(sql);

            while (!date.equals(end_time)) {
                long d = RIUtil.dateToStamp(date + " 00:00:00");

                JSONObject one = new JSONObject();
                one.put("date", date);

                JSONObject qqb = GetCounts(date, list, today, n);
                one.put("web_task", qqb);


                sql = "select id,father_id,decode(title,'1t2i3t4l5e') as title,level,finished,'事项' as type," +
                        "end_time" + " " + "from " + "task where " + "isdelete=1 and (accepter like '%" + user_id +
                        "%' or" + " checked like " + "'%" + user_id + "%' or " + "finished " + "like '%" + user_id +
                        "%') and " + "START_TIME<='" + date + " 00:00:00' and end_time>='" + date + " 23:59" + ":59" + "'" + " " + "UNION " + "select id,father_id,decode(title,'1t2i3t4l5e') as title," + "isTop," + "readed," + "'公告'" + " as type," + "check_time from notice " + "where " + "isdelete=1 and " + "(reading" + " like '%" + user_id + "%' " + "or readed like '%" + user_id + "%') " + "and  " + "notice_time<='" + date + " 00:00" + ":00' and check_time<='" + date + " 23:59:59'";

                //  logger.warn(sql);

                List<JSONObject> h5s = mysql.query(sql);
                if (h5s.size() > 0) {
                    one.put("h5_task", RealInfo(h5s, user_id));

                } else {
                    one.put("h5_task", new JSONArray());
                }

                //安保

                sql = "select id,'' as father_id,title,level,'' as isFin,'' as type,end_time,START_TIME,remark " +
                        "from" + " " + "anbao_info where " + "isdelete=1 and 1=1 and start_time<='" + date + " " +
                        "23:59" + ":59' " + "and " + "end_time>='" + date + " 00:00:00' " + usql + " order by " +
                        "level";
                // System.out.println(sql);
                List<JSONObject> abs = mysql.query(sql);
                if (abs.size() > 0) {
                    one.put("ab", abs);

                } else {
                    one.put("ab", new JSONArray());
                }

                //备忘录

                sql = "select id,'' as father_id,title,level,'' as isFin,'' as type,end_time,START_TIME,remark " +
                        "from " + "memo where " + "isdelete=1 and create_user='" + user_id + "' and " + "start_time" + "<='" + date + " 23:59:59' and end_time>='" + date + " 00:00:00' order" + " by level";
                // System.out.println(sql);
                List<JSONObject> memos = mysql.query(sql);
                if (memos.size() > 0) {
                    one.put("memo", memos);

                } else {
                    one.put("memo", new JSONArray());
                }
                dets.add(one);
                date = RIUtil.GetNextDate(date, 1);
                // System.out.println(date);
            }

            JSONObject one = new JSONObject();
            one.put("date", date);
            JSONObject qqb = GetCounts(date, list, today, n);
            one.put("web_task", qqb);

            sql = "select id,father_id,decode(title,'1t2i3t4l5e') as title,level,finished,'事项' as type,end_time " +
                    "from task where " + "isdelete=1 and (accepter like '%" + user_id + "%' or checked like " + "'%" + user_id + "%' or " + "finished " + "like '%" + user_id + "%') and " + "START_TIME<='" + date + " " + "00:00:00' and END_TIME<='" + date + " 23:59:59' " + "UNION " + "select id,father_id,decode" + "(title," + "'1t2i3t4l5e') as title,isTop,readed,'公告' as type," + "check_time from notice " + "where " + "isdelete=1 and (reading like '%" + user_id + "%' " + "or readed like '%" + user_id + "%') " + "and  " + "notice_time<='" + date + " 00:00" + ":00' and check_time>='" + date + " 23:59:59'";

            //  logger.warn(sql);

            List<JSONObject> h5s = mysql.query(sql);
            if (h5s.size() > 0) {
                one.put("h5_task", RealInfo(h5s, user_id));

            } else {
                one.put("h5_task", new JSONArray());
            }

            //安保

            sql = "select id,'' as father_id,title,level,'' as isFin,'' as type,end_time,START_TIME,remark from" + " "
                    + "anbao_info where " + "isdelete=1 and 1=1 and START_TIME<='" + date + " 23:59:59' " + "and " +
                    "end_time>='" + date + " 00:00:00' " + usql + " order by level";
            // System.out.println(sql);
            List<JSONObject> abs = mysql.query(sql);
            if (abs.size() > 0) {
                one.put("ab", abs);

            } else {
                one.put("ab", new JSONArray());
            }

            //备忘录

            sql = "select id,'' as father_id,title,level,'' as isFin,'' as type,end_time,START_TIME,remark from " +
                    "memo where " + "isdelete=1 and create_user='" + user_id + "' and START_TIME<='" + date + " " +
                    "23:59" + ":59' and " + "end_time>='" + date + " 00:00:00' order by level";
            //  System.out.println(sql);
            List<JSONObject> memos = mysql.query(sql);
            if (memos.size() > 0) {
                one.put("memo", memos);

            } else {
                one.put("memo", new JSONArray());
            }
            dets.add(one);
            back.put("data", dets);


            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
            InfoModelPool.putModel(mysql);
        }

    }

    private JSONObject GetCounts(String date, List<JSONObject> list, String today, long n) throws Exception {
        int fin = 0;
        int unfin = 0;
        int near = 0;
        int delay = 0;
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            String status = one.getString("STATUS");
            String whenFin = one.getString("WHEN_FINISH");
            String finTime = one.getString("FINISH_TIME");
            long when = 9999999999999l;
            try {
                when = RIUtil.dateToStamp(whenFin);
            } catch (Exception ex) {

            }

            if (finTime.contains(date)) {
                fin++;
            }
            if (date.equals(today)) {
                if (status.equals("2") && when > n) {
                    unfin++;
                }
                if (status.equals("2") && when < n) {
                    delay++;
                }
                if (status.equals("2") && whenFin.contains(date)) {
                    near++;
                }
            }


        }
        JSONObject det = new JSONObject();
        det.put("fin", fin);
        det.put("unfin", unfin);
        det.put("near", near);
        det.put("delay", delay);
        return det;
    }

    private int GetDelay(String date, String rec_id, OracleHelper ora_hl) {
        //逾期
        try {
            String sql =
                    "select count(id) as count from QQB_TASK_OBJECT where EXECUTORIDS like '%" + rec_id + "%' " +
                            "and " + "STATUS=2  and  WHEN_FINISH<='" + date + " 00:00:00' and DELETED=0";

            int count = ora_hl.query_count(sql);
            return count;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return 0;
        }
    }

    private int GetUnFinish(String date, String rec_id, OracleHelper ora_hl) {
        //未完成(大于今天）
        try {
            String sql =
                    "select count(id) as count from QQB_TASK_OBJECT where EXECUTORIDS like '%" + rec_id + "%' " +
                            "and " + "STATUS=2  and  WHEN_FINISH>='" + date + " 23:59:59' and DELETED=0";
            logger.warn(sql);
            int count = ora_hl.query_count(sql);
            return count;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return 0;
        }
    }

    private int GetNear(String date, String rec_id, OracleHelper ora_hl) {
        //临期
        try {
            String sql =
                    "select count(OBJECT_ID) as count from QQB_TASK_OBJECT where EXECUTORIDS like '%" + rec_id + "%' "
                            + "and " + "STATUS=2  and  WHEN_FINISH like '%" + date + "%' and DELETED=0";

            logger.warn(sql);
            int count = ora_hl.query_count(sql);
            return count;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return 0;
        }
    }

    private int GetFinish(String date, String rec_id, OracleHelper ora_hl) {
        //已完成
        try {
            String sql =
                    "select count(OBJECT_ID) as count from QQB_TASK_OBJECT where EXECUTORIDS like '%" + rec_id + "%' "
                            + "and " + "STATUS in(1,3)  and  FINISH_TIME like '%" + date + "%' and DELETED=0";

            int count = ora_hl.query_count(sql);
            return count;


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return 0;
        }
    }

    private List<JSONObject> RealInfo(List<JSONObject> h5s, String user_id) {
        List<JSONObject> ret = new ArrayList<>();

        for (int i = 0; i < h5s.size(); i++) {
            JSONObject one = h5s.get(i);
            if (one.containsKey("FINISH_TIME") && one.getString("FINISH_TIME").length() > 0) {

                String finTime = one.getString("FINISH_TIME");
                if (finTime.length() > 0) {
                    one.put("isFin", 1);
                } else {

                    one.put("isFin", 0);
                }

            } else if (one.containsKey("finished") && one.getString("finished").length() > 0) {

                String fin = one.getString("finished");
                if (fin.contains(user_id)) {
                    one.put("isFin", 1);
                } else {

                    one.put("isFin", 0);
                }

            } else {
                one.put("isFin", 0);
            }
            ret.add(one);
        }
        return ret;
    }

    private JSONObject getTaskList(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("oracle");
            String user_id = "";
            String usql = "   1=1 ";
            String job = "";

            String id_num = "";

            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
                id_num = RIUtil.users.get(user_id).getString("id_num");

            } else {
                return ErrNo.set(501001);
            }
            if (data.containsKey("job_name") && data.getString("job_name").length() > 0) {
                job = data.getString("job_name");

            } else {
                return ErrNo.set(501001);
            }

            String unit = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject uone = RIUtil.dicts.get(unit);
                String type = uone.getString("type");
                if (type.equals("24") || type.equals("28")) {
                    unit = unit.substring(0, 6) + "000000";
                } else {
                    if (!unit.endsWith("0000") && !unit.startsWith("320400")) {
                        unit = unit.substring(0, 8) + "0000";
                    }
                }

                if (unit.startsWith("320400")) {
                    unit = "320400000000";
                }

            } else {
                return ErrNo.set(501001);
            }

            String rec_id = GetQQbUserId(unit, id_num, job);
            int isFin = 0;

            if (data.containsKey("isFin") && data.getString("isFin").length() > 0) {
                isFin = data.getInteger("isFin");


            }
            if (isFin == 0) {
                usql = "( STATUS =0 or status=2 or status=4 )";
            } else {
                usql = " STATUS IN (1,3)";
            }

            String sql = "";

            if (rec_id.length() > 2) {
                sql = "select TASK_ID,WHEN_CREATE,STATUS,WHEN_FINISH,TEMPLATE_ID,TASK_NAME,NAME,DELIVERY_WAY, LEVELS,"
                        + "ID,STATE,ACCESS_TYPE,CONTROL_ID," + "PID,OBJECT_NAME,IMPORTANT_TYPE, TIME_MODEL, " +
                        "TASK_DETAIL_TYPE, TASK_LINK_ID from " + "QQB_TASK_OBJECT3  where " + "(STATUS=2 or status=4)"
                        + " AND  INSTR(EXECUTORIDS,'" + rec_id + "') >0 AND  DELETED=0 ORDER BY " + "IMPORTANT_TYPE " + "DESC, " + "WHEN_FINISH ";
                System.out.println(sql);


                List<JSONObject> list = ora_hl.query(sql);
                if (list.size() > 0) {
                    back.put("data", qqbTaskInfo(list));

                } else {
                    back.put("data", new JSONArray());
                }
            } else {

                back.put("data", new JSONArray());
            }

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }

    }

    private List<JSONObject> qqbTaskInfo(List<JSONObject> list) throws Exception {
        List<JSONObject> back = new ArrayList<>();
        HashMap<String, String> qqbs = new HashMap<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            try {


                long create = System.currentTimeMillis();
                long end = 999999999999L;
                try {
                    end = RIUtil.dateToStamp(one.getString("WHEN_FINISH"));
                } catch (Exception ex) {

                }
                if (end - create < 0) {
                    one.put("FIN_TYPE", "逾期未完成");
                } else {
                    if (end != 9999999999999L) {
                        long bt = end - create;
                        bt = bt / (1000 * 60 * 60);
                        if (bt > 24) {
                            one.put("FIN_TYPE", bt / 24 + "天");

                        } else {
                            one.put("FIN_TYPE", bt + "小时");
                        }
                    } else {
                        one.put("FIN_TYPE", "");
                    }

                }


                back.add(one);
            } catch (Exception ex) {
                logger.warn(one.toString());
            }

        }
        return back;
    }

    private List<JSONObject> realInfo_cyc(List<JSONObject> list, MysqlHelper my_qqb) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            int time_model = one.getInteger("TIME_MODEL");
            if (time_model == 2) {
                String task_id = one.getString("TASK_ID");
                String start_time = one.getString("START_DATE").substring(0, 10);

                String sql = "select ID from TASK where PID='" + task_id + "' and START_TIME like '%" + start_time +
                        "%'";
                String tid = my_qqb.query_one(sql, "ID");
                one.put("TID", tid);
            }
            back.add(one);

        }
        return back;
    }

    private JSONObject getTaskTrend7(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper my_qqb = null;

        try {
            my_qqb = new MysqlHelper("mysql_qqb_task");
            String user_id = "";
            String id_num = "";

            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
                id_num = RIUtil.users.get(user_id).getString("id_num");

            } else {
                return ErrNo.set(501001);
            }

            String unit = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                if (!unit.endsWith("0000") && !unit.startsWith("320400")) {
                    unit = unit.substring(0, 8) + "0000";
                }

                if (unit.startsWith("320400")) {
                    unit = "320400000000";
                }

            } else {
                return ErrNo.set(501001);
            }

            String rec_id = GetQQbUserId(unit, id_num, "");
            if (rec_id.length() > 0) {

                String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

                JSONArray dets = new JSONArray();
                for (int i = 0; i < 7; i++) {
                    JSONObject one = new JSONObject();
                    one.put("date", today);
                    String sql = "";

                    sql = "select count(ID) as count from TASK_DETAIL  where START_DATE<='" + today + " " + "00" +
                            ":00:00" + "'" + " and END_DATE>='" + today + " 23:59:59' and RECEIVE_OBJECT_ID ='" + rec_id + "'and  isnull(FINISH_TIME) ";


                    int count = my_qqb.query_count(sql);
                    one.put("unfin", count);
                    sql = "select count(id) as count from TASK_DETAIL  where START_DATE<='" + today + " " + "00" +
                            ":00" + ":00" + "'" + " and END_DATE>='" + today + " 23:59:59' and RECEIVE_OBJECT_ID " +
                            "='" + rec_id + "'and  LENGTH(FINISH_TIME)>5 ";
                    count = my_qqb.query_count(sql);
                    one.put("fine", count);


                    sql = "select count(id) as count from TASK_DETAIL where START_DATE<='" + today + " " + "00:00" +
                            ":00" + "'" + " and END_DATE>='" + today + " 23:59:59' and RECEIVE_OBJECT_ID ='" + rec_id + "' " + "and 1=1";
                    count = my_qqb.query_count(sql);
                    one.put("total", count);


                    dets.add(one);


                    today = RIUtil.GetNextDate(today, -1);


                }
                back.put("data", dets);
            } else {
                back.put("data", new JSONArray());
            }

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            my_qqb.close();
        }

    }

    private JSONObject getTaskType7(JSONObject data) {
        JSONObject back = ErrNo.set(0);

        MysqlHelper my_qqb = null;
        try {


            String user_id = "";
            String id_num = "";

            if (data.containsKey("user_id") && data.getString("user_id").length() > 0) {
                user_id = data.getString("user_id");
                id_num = RIUtil.users.get(user_id).getString("id_num");

            } else {
                return ErrNo.set(501001);
            }

            String unit = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                if (!unit.endsWith("0000") && !unit.startsWith("320400")) {
                    unit = unit.substring(0, 8) + "0000";
                }

                if (unit.startsWith("320400")) {
                    unit = "320400000000";
                }

            } else {
                return ErrNo.set(501001);
            }

            String rec_id = GetQQbUserId(unit, id_num, "");
            if (rec_id.length() > 0) {
                String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
                String ex7 = RIUtil.GetNextDate(today, -7);
                my_qqb = new MysqlHelper("mysql_qqb_task");

                String sql =
                        "select b.TYPE,count(a.id) as count from TASK_DETAIL a left join TASK b on a.TASK_ID=b" +
                                ".ID " + "where " + "a.RECEIVE_OBJECT_ID='" + rec_id + "' and a.START_DATE>='" + ex7 + " " + "00:00:00' and  a" + ".DELETED=0";
                logger.warn(sql);

                List<JSONObject> list = my_qqb.query(sql);

                if (list.size() > 0) {
                    back.put("data", RelaInfo_qqbType(list));

                } else {
                    back.put("data", new JSONArray());
                }
            } else {
                back.put("data", new JSONArray());
            }

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            try {
                my_qqb.close();
            } catch (Exception ex) {

            }
        }


    }

    private String GetQQbUserId(String unit, String id_num, String job) {
        MysqlHelper my_qqb = null;
        if (id_num.length() > 2) {

            try {
                my_qqb = new MysqlHelper("mysql_qqb_user");
                String sql =
                        "select ID from SYS_AUTH_USER where IDCARD_NO='" + id_num + "' and DEPT_CODE ='" + unit + "' "
                                + "and DELETED=0 and DUTY='" + job + "'and type>0";
                logger.warn(sql);

                String rec_id = my_qqb.query_one(sql, "ID");
                return rec_id;
            } catch (Exception ex) {
                logger.warn(Lib.getTrace(ex));
                return "";
            } finally {
                my_qqb.close();
            }
        } else {
            return "";
        }

    }

    private Object RelaInfo_qqbType(List<JSONObject> list) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            try {
                String CJLB = "62-" + one.getString("TYPE");
                String name = RIUtil.dicts.get(CJLB).getString("dict_name");
                one.put("dict_name", name);
            } catch (Exception ex) {
                one.put("dict_name", "其他");
                System.out.println(one);
            }

            back.add(one);
        }
        return back;
    }


}
