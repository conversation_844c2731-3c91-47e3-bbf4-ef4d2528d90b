package HL.TNOA.Httpd.Controller;

import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil;
import HL.TNOA.wechat.HttpConnection;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CaseObjectController {
    private static Logger logger = LoggerFactory.getLogger(CaseObjectController.class);

    public static JSONObject createCaseObject(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String case_id = "";
            String obj_name = "";
            String img = "";
            String mark = "";
            String id_num = "";
            String tele = "";
            String unit = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "1";
            if (data.containsKey("case_id") && data.getString("case_id").length() > 0) {
                case_id = data.getString("case_id");
            } else {
                return ErrNo.set(457002);
            }
            if (data.containsKey("obj_name") && data.getString("obj_name").length() > 0) {
                obj_name = data.getString("obj_name");
            } else {
                return ErrNo.set(457002);
            }
            if (data.containsKey("img") && data.getString("img").length() > 0) {
                img = data.getString("img");
            }
            if (data.containsKey("mark") && data.getString("mark").length() > 0) {
                mark = data.getString("mark");
            }
            if (data.containsKey("id_num") && data.getString("id_num").length() > 0) {
                id_num = data.getString("id_num");
            }
            if (data.containsKey("tele") && data.getString("tele").length() > 0) {
                tele = data.getString("tele");
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            }

            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
                if (unit.length() == 0) {
                    unit = RIUtil.users.get(create_user).getString("unit");
                }
            } else {
                return ErrNo.set(457002);
            }

            String sqls = "insert case_object " +
                    "(case_id,obj_name,img,mark," +
                    "id_num,tele," +
                    "create_user,create_time,isdelete,unit)" +
                    "values('" + case_id + "',encode('" + obj_name + "','" + RIUtil.enName + "'),'" + img + "',encode" +
                    "('" + mark + "','" + RIUtil.enContent + "')," +
                    "encode('" + id_num + "','" + RIUtil.enNum + "'),encode('" + tele + "','" + RIUtil.enTele + "'),'"
                    + create_user + "','" + create_time + "','" + isdelete + "','" + unit + "')";
            mysql.update(sqls);
            sqls = "select id from case_object where case_id='" + case_id + "' and decode(obj_name,'" + RIUtil.enName + "')='" + obj_name + "' and create_time='" + create_time + "'";
            String obj_id = mysql.query_one(sqls, "id");
            logger.warn(sqls);
            List<String> list = new ArrayList<>();
            list.add("添加对象：" + obj_name);

            CaseController.addLogs(list, mysql, case_id, "101", obj_id, "添加对象", create_user);

            //获取下一步
            JSONObject datas = new JSONObject();
            String type = RIUtil.IdToName(case_id, mysql, "type", "case_info");
            JSONObject next = GetNextStep(mysql, "101", type);
            datas.put("next", next);
            data.put("values", new ArrayList<>());
            back.put("data", datas);
            sqls = "update case_object set current_index='" + next.getString("index_no") + "' where id='" + obj_id + "' and case_id='" + case_id + "'";
            mysql.update(sqls);

            //停止待侦循环
            sqls = "update case_info set cycle=0 , status=0 where id='" + case_id + "' ";
            mysql.update(sqls);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(457001);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    public static JSONObject GetNextStep(InfoModelHelper mysql, String index, String select_id) throws Exception {
        String next_step = "";
        String sql = "";
        if ("101".equals(index)) {
            next_step = select_id;
        } else {
            sql = "select * from case_step_model where index_no='" + index + "'";
            List<JSONObject> list = mysql.query(sql);
            JSONObject one = list.get(0);
            String select = one.getString("selects");
            if (select.length() > 5) {
                JSONArray selects = JSONArray.parseArray(select);
                for (int i = 0; i < selects.size(); i++) {
                    JSONObject o = selects.getJSONObject(i);

                    if (select_id.contains(o.getString("select_id"))) {

                        next_step = o.getString("next_step");
                        break;
                    }

                }

            } else {
                next_step = one.getString("next_step");
            }

        }
        sql = "select * from case_step_model where index_no='" + next_step + "'";
        List<JSONObject> steps = mysql.query(sql);
        if (steps.size() > 0) {
            return steps.get(0);
        } else {
            return new JSONObject();
        }
    }

    //******GET*******
    public static JSONObject getCaseObject(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String case_id = "";
            String obj_name = "";

            String id_num = "";
            String tele = "";
            String unit = "";

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                sql = sql + " unit='" + unit + "' and ";
            }
            if (data.containsKey("case_id") && data.getString("case_id").length() > 0) {
                case_id = data.getString("case_id");
                sql = sql + " case_id='" + case_id + "' and ";
            }
            if (data.containsKey("obj_name") && data.getString("obj_name").length() > 0) {
                obj_name = data.getString("obj_name");
                sql = sql + " decode(obj_name,'" + RIUtil.enName + "') like '%" + obj_name + "%' and ";
            }

            if (data.containsKey("id_num") && data.getString("id_num").length() > 0) {
                id_num = data.getString("id_num");
                sql = sql + " decode(id_num,'" + RIUtil.enNum + "') like '%" + id_num + "%' and ";

            }
            if (data.containsKey("tele") && data.getString("tele").length() > 0) {
                tele = data.getString("tele");
                sql = sql + " decode(tele,'" + RIUtil.enTele + "')='%" + tele + "%' and ";
            }


            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select *,decode(obj_name,'" + RIUtil.enName + "') as obj_name,decode(tele,'" + RIUtil.enTele +
                            "') as tele," +
                            "decode(id_num,'" + RIUtil.enNum + "') as id_num,decode(mark,'" + RIUtil.enContent + "') " +
                            "as mark " +
                            " from case_object where 1=1 and " + sql + " isdelete=1  limit " + limit + " offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfo(list, mysql));
                sqls = "select id from case_object where 1=1 and " + sql + " isdelete=1; ";
                list = mysql.query(sqls);
                back.put("count", list.size());
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(457005);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*****relainfo************
    private static List<JSONObject> RelaInfo(List<JSONObject> list, InfoModelHelper mysql) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
           /* String current_index = one.getString("current_index");
            JSONObject s = new JSONObject();
            s.put("case_id", one.getString("case_id"));
            s.put("index", current_index);
            JSONObject b = CaseStepDetail.getCaseStep(s);
            JSONObject indexs = new JSONObject();
            if (b.containsKey("data") && b.getString("data").length() > 0) {
                indexs = b.getJSONObject("data");
            }
            one.put("current_index", indexs);*/
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    public static JSONObject updateCaseObject(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String case_id = "";
            String obj_name = "";
            String img = "";
            String mark = "";
            String id_num = "";
            String tele = "";

            String opt_user = "";
            List<String> logs = new ArrayList<>();
            JSONObject old = new JSONObject();
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                String s = "select id,case_id,decode(obj_name,'" + RIUtil.enName + "') as obj_name," +
                        "decode(tele,'" + RIUtil.enTele + "') as tele ,decode(id_num,'" + RIUtil.enNum + "') as " +
                        "id_num," +
                        "decode(mark,'" + RIUtil.enContent + "') as mark,img" +
                        " from case_object where id='" + id + "'";
                logger.warn(s);
                List<JSONObject> list = mysql.query(s);
                old = list.get(0);
                logger.warn(old.toString());
            } else {
                return ErrNo.set(457004);
            }

            if (data.containsKey("obj_name") && data.getString("obj_name").length() > 0) {
                obj_name = data.getString("obj_name");
                if (!old.getString("obj_name").equals(obj_name)) {
                    sql = sql + " obj_name=encode('" + obj_name + "','" + RIUtil.enName + "'), ";
                    logs.add("修改对象姓名：" + old.getString("obj_name") + "->" + obj_name);
                }
            }
            if (data.containsKey("img") && data.getString("img").length() > 0) {
                img = data.getString("img");
                sql = sql + " img='" + img + "', ";
            }
            if (data.containsKey("mark") && data.getString("mark").length() > 0) {
                mark = data.getString("mark");
                if (!old.getString("mark").equals(mark)) {
                    sql = sql + "mark=encode('" + mark + "','" + RIUtil.enContent + "'), ";
                    logs.add("修改备注");
                }
            }
            if (data.containsKey("id_num") && data.getString("id_num").length() > 0) {
                id_num = data.getString("id_num");
                sql = sql + " id_num=encode('" + id_num + "','" + RIUtil.enNum + "'), ";
            }
            if (data.containsKey("tele") && data.getString("tele").length() > 0) {
                tele = data.getString("tele");
                if (!old.getString("tele").equals(tele)) {
                    sql = sql + " tele=encode('" + tele + "','" + RIUtil.enTele + "'), ";
                    logger.warn(sql);
                    logs.add("修改电话号码：" + old.getString("tele") + "->" + tele);
                }
            }


            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            }
            String sqls = "update case_object set " + sql + " isdelete=1  where id='" + id + "'";
            mysql.update(sqls);

            CaseController.addLogs(logs, mysql, old.getString("case_id"), "101", id, "修改对象信息", opt_user);


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(457003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    public static JSONObject deleteCaseObject(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(457008);
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(457008);
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String case_id = RIUtil.IdToName(id, mysql, "case_id", "case_object");
            String sqls =
                    "update case_object set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            sqls = "update case_log set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where case_id in ('" + id + "') and obj_id='" + id + "'";
            mysql.update(sqls);
            sqls = "update case_step set isdelete =2,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where case_id in ('" + id + "') and obj_id='" + id + "'";
            mysql.update(sqls);

            sqls = "select case_id,decode(obj_name,'" + RIUtil.enName + "') as obj_name from case_object where id='" + id + "'";
            List<JSONObject> list = mysql.query(sqls);
            JSONObject one = list.get(0);


            List<String> logs = new ArrayList<>();
            logs.add("删除对象：" + one.getString("obj_name"));
            CaseController.addLogs(logs, mysql, one.getString("case_id"), "101", id, "删除对象", opt_user);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(457007);
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

    public static JSONObject getCaseObjectByUrl(JSONObject data, String remoteAddr) {
        JSONObject back = ErrNo.set(0);
        InfoModelHelper mysql = null;
        InfoModelHelper mysql2 = null;
        try {
            mysql = InfoModelPool.getModel();
            String sql = "select id,case_number from case_info ";
            List<JSONObject> query = mysql.query(sql);
            for (int i = 0; i < query.size(); i++) {
                JSONObject object = query.get(i);
                String id = object.getString("id");
                String case_number = object.getString("case_number");
                int page = 1;
                String condition = " AJBH='" + case_number + "'";
                while (true) {
                    JSONObject one = new JSONObject();
                    JSONObject two = new JSONObject();
                    JSONObject three = new JSONObject();
                    one.put("fwbh", "S-320400230000-0100-00008");
                    one.put("lx", "02");
                    one.put("ds", "3204");
                    one.put("type", "0");
                    two.put("condition", condition);
                    two.put("requiredItems", "AJBH,GMSFHM,XM,LXDH");
                    two.put("pageSize", "10");
                    two.put("pageNum", String.valueOf(page));
                    one.put("required", two);
                    three.put("dyrdwbm", "320402580000");
                    three.put("dyrdwmc", "翠竹派出所");
                    three.put("dyrip", "***********");
                    three.put("dyyy", "智慧派出所打处功能案件调用");
                    three.put("dyrlxfs", "13951212059");
                    three.put("dyrxm", "易鑫");
                    three.put("dyrgmsfhm", "362201199505150613");
                    one.put("realInfo", three);
                    String post = HttpConnection.post("http://************:18888/serverInterface/dataserver" +
                            "/getParamAndContent", one);
                    logger.warn(post);
                    JSONObject url = JSONObject.parseObject(post);
                    String total = url.getString("total");
                    JSONArray results = url.getJSONArray("results");
                    mysql2 = InfoModelPool.getModel();
                    if (results.size() > 0) {
                        for (int j = 0; j < results.size(); j++) {
                            JSONObject js = results.getJSONObject(j);
                            String id_card = js.getString("GMSFHM");
                            String phone = js.getString("LXDH");
                            String name = js.getString("XM");
                            sql = "insert into case_object (case_id,obj_name,tele,id_num) values('" + id + "',encode" +
                                    "('" + name + "','" + RIUtil.enName + "')," +
                                    "encode('" + phone + "','" + RIUtil.enTele + "'),encode('" + id_card + "','" + RIUtil.enNum + "'))";
                            logger.warn(sql);
                            mysql2.update(sql);
                        }
                    }
                    logger.warn(total);
                    logger.warn(String.valueOf(page));
                    InfoModelPool.putModel(mysql2);
                    Integer integer = Integer.valueOf(total);
                    if (integer != 10) {
                        break;
                    }
                    page++;
                }
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(1);
        } finally {
            InfoModelPool.putModel(mysql);
            InfoModelPool.putModel(mysql2);
        }
        return back;
    }


}
