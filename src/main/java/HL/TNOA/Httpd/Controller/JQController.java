package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.OBS.ObsServer;
import HL.TNOA.Httpd.Utils.KdVideoUtil;
import HL.TNOA.Httpd.Utils.TrustAllCerts;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.*;
import HL.TNOA.Lib.Export.ExportInterface;
import HL.TNOA.Lib.Export.ExportXlsxHelper;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.InfoModel.OracleModelHelper;
import HL.TNOA.Lib.InfoModel.OracleModelPool;
import HL.TNOA.wechat.HttpConnection;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mysql.cj.util.StringUtils;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.eclipse.jetty.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import java.io.File;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
public class JQController {
    private static Logger logger = LoggerFactory.getLogger(JQController.class);
    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping(method = {RequestMethod.POST}, path = {"/jq"})
    public JSONObject get_jq(TNOAHttpRequest request) throws Exception {

        String opt = "";
        JSONObject data = request.getRequestParams();
        logger.warn("jq->" + data);
        InfoModelHelper mysql = null;
        try {
            mysql = request.openInfoImpl();
            if (data.containsKey("opt") && data.getString("opt").length() > 0) {
                opt = data.getString("opt");
                if (opt.equals("search_jq_no_address")) {
                    return searchJQNoAddress(data);
                } else if (opt.equals("search_jq_for_ding")) {

                    return searchJQNoAddress(data);
                } else if (opt.equals("search_address_info")) {
                    return searchAddressInfo(data, request);
                } else if (opt.equals("get_jq")) {
                    return GetJQByJJBH(data, request);
                } else if (opt.equals("update_jq_address")) {
                    return updateJQAddress(data);
                } else if (opt.equals("update_bq")) {
                    return updateBQ(data);
                } else if (opt.equals("get_jq_type_tree")) {
                    return GetJQTypeTree(data, mysql);
                } else if (opt.equals("get_jq_type_tree_old")) {
                    return GetJQTypeTreeOld(data, mysql);
                } else if (opt.equals("update_jq_ry")) {
                    return UpdateJQRY(data);
                } else if (opt.equals("delete_jq_ry")) {
                    return DeleteJQRY(data);
                } else if (opt.equals("create_rw_dw")) {
                    return CreateRWDW(data);//XXX
                } else if (opt.equals("search_dw_dzid")) {
                    return SearchDWAsDzid(data);
                } else if (opt.equals("search_by_sfz")) {
                    return SearchBySFZ(data, request);
                } else if (opt.equals("check_jqbz")) {
                    return CheckJQBZ(data, request);
                } else if (opt.equals("get_jq_for_bt")) {
                    return getJqForBt(data, request);
                } else if (opt.equals("update_mark_type")) {
                    return UpdateMarkType(data);
                } else if (opt.equals("get_aj_info")) {
                    return GetAJInfo(data);
                } else if (opt.equals("get_aj_for_bt")) {
                    return GetAJForBt(data);
                } else if (opt.equals("get_jq_video")) {
                    return GetJQVideo(data);
                } else if (opt.equals("update_jqbz")) {
                    return updateJQBZ(data);
                } else if (opt.equals("get_bz_det")) {
                    return getBZDet(data);
                } else {
                    return ErrNo.set(505003);
                }
            } else {
                return ErrNo.set(505003);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(505003);
        } finally {
            InfoModelPool.putModel(mysql);
        }
    }

    private JSONObject getBZDet(JSONObject data) {

        JSONObject back = ErrNo.set(0);
        OracleHelper ora_hl = null;
        try {
            ora_hl = new OracleHelper("ora_hl");
            String jjbh = "";
            //处警接警编号－接警编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {


                jjbh = data.getString("jjbh");
            } else {
                return ErrNo.set(null, 2, "缺少jjbh");
            }

            String sql =
                    "select bzzt,addressM,resultM,timeM,toolM,reasonM,mark from dsj_jq " + "where " + "jjbh='" + jjbh + "'";
            List<JSONObject> dets = ora_hl.query(sql);
            if (dets.size() > 0) {
                back.put("data", RelaBzDet(dets.get(0), ora_hl));

            } else {
                back.put("data", new JSONObject());
            }

            return back;

        } catch (Exception ex) {

            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            ora_hl.close();
        }

    }

    private JSONObject RelaBzDet(JSONObject one, OracleHelper jdbc) {


        if (one.getIntValue("mark") == 0) {
            String jjbh = one.getString("jjbh");
            String sql = "select cjlb,cjsj_time,sfcs,fsyy from dsj_jq where jjbh='" + jjbh + "'";
            JSONObject det = queryOne(sql, jdbc);
            //地址 -》事发场所 74
            List<String> addressMs = new ArrayList<>();
            try {

                String sfcs = det.getString("SFCS");
                sfcs = RIUtil.dicts.get("74-" + sfcs).getString("dict_name").replace("其他", "");

                String id = GetDictIdByNameType(3, sfcs);
                addressMs.add(id);
            } catch (Exception ex) {

            }
//发生原因 77
            List<String> reasonM = new ArrayList<>();
            try {

                String fsyy = det.getString("FSYY");

                if (fsyy.startsWith("0")) {
                    fsyy = fsyy.substring(1);
                }
                fsyy = RIUtil.dicts.get("77-" + fsyy).getString("dict_name").replace("其他", "");

                String id = GetDictIdByNameType(8, fsyy);
                reasonM.add(id);
            } catch (Exception ex) {
                System.out.println(Lib.getTrace(ex));
            }
//手段514
            List<String> toolM = new ArrayList<>();
            try {

                String cjlb = det.getString("CJLB");

                String cjlbN = RIUtil.dicts.get("51-" + cjlb).getString("dict_name").replace("其他", "");
                logger.warn(cjlbN);

                String id = GetDictIdByNameType(9, cjlbN);
                if (id == null || id.length() == 0) {
                    String cjfid = RIUtil.dicts.get("51-" + cjlb).getString("father_id");
                    String cjfidName = RIUtil.dicts.get(cjfid).getString("dict_name");
                    logger.warn(cjfidName);
                    id = GetDictIdByNameType(9, cjfidName);
                }
                toolM.add(id);


            } catch (Exception ex) {
                System.out.println(Lib.getTrace(ex));

            }

            //
            String id = "33827D2519694C74B32F06418175E42B";
            try {
                String cjsj = det.getString("CJSJ01");

                try {
                    String hh = cjsj.substring(8, 10);
                    int h = Integer.parseInt(hh);

                    if (h < 6) {
                        id = "D8480F9C31754C51B40C6F21A36ADB6F";
                    } else if (h >= 6 && h < 12) {
                        id = "DE5156E24F6F4512AB0D575D2FDB1C99";
                    } else if (h >= 12 && h < 18) {
                        id = "C4F9800366AC41C2BB8D49F80E84FC4C";
                    } else if (h >= 18 && h < 24) {
                        id = "4B820D3577EC42A6BF67DD1E8F52CAD6";
                    } else {
                        id = "33827D2519694C74B32F06418175E42B";
                    }
                } catch (Exception ex) {

                }


            } catch (Exception ex) {

            }

            one.put("addressM", addressMs);
            one.put("reasonM", reasonM);
            one.put("toolM", toolM);
            one.put("timeM", id);


        }


        if (one.containsKey("ADDRESSM") && one.getString("ADDRESSM") != null && one.getString("ADDRESSM").length() > 0) {
            try {
                String addMs = one.getString("ADDRESSM");

                one.put("_addressM", RIUtil.RealDictNameList(RIUtil.StringToList(addMs)));
                one.put("addressM", RIUtil.HashToList(RIUtil.StringToList(one.getString("addressM"))));
            } catch (Exception ex) {
                one.put("_addressM", new JSONArray());
                one.put("addressM", new ArrayList<>());
            }
        } else {
            one.put("_addressM", new JSONArray());
            one.put("addressM", new ArrayList<>());
        }

        if (one.containsKey("RESULTM") && one.getString("RESULTM") != null && one.getString("RESULTM").length() > 0) {
            try {
                one.put("_resultM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("RESULTM"))));
                one.put("resultM", RIUtil.HashToList(RIUtil.StringToList(one.getString("RESULTM"))));
            } catch (Exception ex) {
                one.put("_resultM", new JSONArray());
                one.put("resultM", new ArrayList<>());
            }
        } else {
            one.put("_resultM", new JSONArray());
            one.put("resultM", new ArrayList<>());
        }
        if (one.containsKey("TIMEM") && one.getString("TIMEM") != null && one.getString("TIMEM").length() > 0) {
            try {
                one.put("_timeM", RIUtil.dicts.get(one.getString("TIMEM")));

            } catch (Exception ex) {
                one.put("_timeM", new JSONObject());
            }
        } else {
            one.put("_timeM", new JSONObject());
        }

        if (one.containsKey("TOOLM") && one.getString("TOOLM") != null && one.getString("TOOLM").length() > 0) {
            try {
                one.put("_toolM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("TOOLM"))));
                one.put("toolM", RIUtil.HashToList(RIUtil.StringToList(one.getString("TOOLM"))));
            } catch (Exception ex) {
                one.put("_toolM", new JSONArray());
                one.put("toolM", new ArrayList<>());
            }
        } else {
            one.put("_toolM", new JSONArray());
            one.put("toolM", new ArrayList<>());
        }
        if (one.containsKey("REASONM") && one.getString("REASONM") != null && one.getString("REASONM").length() > 0) {
            try {
                one.put("_reasonM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("REASONM"))));
                one.put("reasonM", RIUtil.HashToList(RIUtil.StringToList(one.getString("REASONM"))));
            } catch (Exception ex) {
                one.put("_reasonM", new JSONArray());
                one.put("reasonM", new ArrayList<>());
            }
        } else {
            one.put("_reasonM", new JSONArray());
            one.put("reasonM", new ArrayList<>());
        }


        return one;

    }

    private JSONObject updateJQBZ_person(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper jdbcTemplate = null;
        OracleHelper ora_hl = null;
        try {
            String sql = "";
            String jjbh = "";
            String vals = "";
            String bzs = "";
            String gmsfhm = "";
            String personM = "";
            //警情编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");
                sql = sql + " jjbh='" + jjbh + "' , ";

            } else {
                return ErrNo.set(null, 2, "缺少jjbh");
            }

            if (data.containsKey("gmsfhm") && data.getString("gmsfhm").length() > 0) {
                gmsfhm = data.getString("gmsfhm");
                sql = sql + " gmsfhm='" + gmsfhm + "' , ";

            } else {
                return ErrNo.set(null, 2, "缺少gmsfhm");
            }

            //人员标签
            if (data.containsKey("personM") && data.getString("personM").length() > 0) {
                personM = data.getString("personM");


                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(personM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    JSONArray nexts = RIUtil.GetDictByFather(id);
                    if (nexts.size() > 1) {
                        return ErrNo.set(null, 2, "未选择到最底层标签");
                    }
                    if (!bzs.contains(id)) {
                        bzs = bzs + id + ",";
                    }
                    while (id.length() > 0) {
                        String fid = RIUtil.dicts.get(id).getString("father_id");

                        try {
                            id = RIUtil.dicts.get(fid).getString("id");
                            if (!bzs.contains(id)) {
                                bzs = bzs + id + ",";
                            }
                        } catch (Exception ex) {
                            id = "";
                        }

                    }

                }
                System.out.println(bzs);

            }

            String unit = data.getString("unit");
            if (unit.startsWith("320412")) {
                try {
                    jdbcTemplate = new MysqlHelper("wjjq");
                    String create_user = data.getString("opt_user");
                    String create_time = data.getString("create_time");
                    String sqls =
                            "update wjsc_jq_sjxx set personMs='" + bzs + "',personM='" + personM + "' where " + "jjbh" +
                                    "='" + jjbh + "' and " + "gmsfhm='" + gmsfhm + "'";
                    logger.warn(sqls);
                    jdbcTemplate.update(sqls);

                    if (vals.length() > 2) {
                        vals = vals.substring(0, vals.length() - 1);
                    }
                    sqls = "insert into jq_bz(bzr,bz_time,his_type,jjbh,personM) " + "values('" + data.getString(
                            "opt_user") + "'," + "'" + create_time + "','3','" + jjbh + "','" + personM + "')";
                    logger.warn(sqls);
                    jdbcTemplate.update(sqls);
                } catch (Exception ex) {
                    logger.error(Lib.getTrace(ex));
                }
            }

            ora_hl = new OracleHelper("ora_hl");
            String sqls =
                    "update JQ_SJRY set personMs='" + bzs + "',personM='" + personM + "' where jjbh='" + jjbh + "' " + "and "
                            + "gmsfhm='" + gmsfhm + "'";
            logger.warn(sqls);
            ora_hl.update(sqls);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            if (jdbcTemplate != null) {
                jdbcTemplate.close();
            }
            if (ora_hl != null) {
                ora_hl.close();
            }
        }
    }


    private JSONObject updateJQBZ(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        MysqlHelper jdbcTemplate = null;
        OracleHelper ora_hl = null;
        try {
            String sql = "";
            String jjbh = "";
            String vals = "";
            String bzs = "";

            //警情编号
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");
                sql = sql + " jjbh='" + jjbh + "' , ";

            } else {
                return ErrNo.set(null, 2, "缺少jjbh");

            }
            //地址标签
            if (data.containsKey("addressM") && data.getString("addressM").length() > 0) {
                String addressM = data.getString("addressM");
                sql = sql + " addressM='" + addressM + "' , ";

                vals = vals + "'" + addressM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(addressM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    JSONArray nexts = RIUtil.GetDictByFather(id);
                    if (nexts.size() > 1) {
                        return ErrNo.set(null, 2, "未选择到最底层标签");
                    }
                    bzs = bzs + id + ",";
                    while (id.length() > 0) {
                        String fid = RIUtil.dicts.get(id).getString("father_id");

                        try {
                            id = RIUtil.dicts.get(fid).getString("id");
                            bzs = bzs + id + ",";
                        } catch (Exception ex) {
                            id = "";
                        }

                    }

                }
                System.out.println(bzs);

            }
            //人员标签
            if (data.containsKey("personM") && data.getString("personM").length() > 0) {
                String personM = data.getString("personM");
                sql = sql + " personM='" + personM + "' , ";
                vals = vals + "'" + personM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(personM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    bzs = bzs + id + ",";
                    while (id.length() > 0) {
                        String fid = RIUtil.dicts.get(id).getString("father_id");
                        try {
                            id = RIUtil.dicts.get(fid).getString("id");
                            bzs = bzs + id + ",";
                        } catch (Exception ex) {
                            id = "";
                        }

                    }

                }
                System.out.println(bzs);
            }
            //结果标签
            if (data.containsKey("resultM") && data.getString("resultM").length() > 0) {
                String resultM = data.getString("resultM");
                sql = sql + " resultM='" + resultM + "' , ";
                vals = vals + "'" + resultM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(resultM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    JSONArray nexts = RIUtil.GetDictByFather(id);
                    if (nexts.size() > 1) {
                        return ErrNo.set(null, 2, "未选择到最底层标签");
                    }
                    bzs = bzs + id + ",";
                    while (id.length() > 0) {
                        String fid = RIUtil.dicts.get(id).getString("father_id");

                        try {
                            id = RIUtil.dicts.get(fid).getString("id");
                            bzs = bzs + id + ",";
                        } catch (Exception ex) {
                            id = "";
                        }

                    }

                }
                System.out.println(bzs);
            }
            //时间标签
            if (data.containsKey("timeM") && data.getString("timeM").length() > 0) {
                String timeM = data.getString("timeM");
                sql = sql + " timeM='" + timeM + "' , ";
                vals = vals + "'" + timeM + "',";
                bzs = bzs + timeM + ",";
            } else {
                vals = vals + "'',";

            }
            //手段标签
            if (data.containsKey("toolM") && data.getString("toolM").length() > 0) {
                String toolM = data.getString("toolM");
                sql = sql + " toolM='" + toolM + "' , ";
                vals = vals + "'" + toolM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(toolM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    JSONArray nexts = RIUtil.GetDictByFather(id);
                    if (nexts.size() > 1) {
                        return ErrNo.set(null, 2, "未选择到最底层标签");
                    }
                    bzs = bzs + id + ",";
                    while (id.length() > 0) {
                        String fid = RIUtil.dicts.get(id).getString("father_id");

                        try {
                            id = RIUtil.dicts.get(fid).getString("id");
                            bzs = bzs + id + ",";
                        } catch (Exception ex) {
                            id = "";
                        }

                    }

                }
                System.out.println(bzs);

            }
            //原因标签
            if (data.containsKey("reasonM") && data.getString("reasonM").length() > 0) {
                String reasonM = data.getString("reasonM");
                sql = sql + " reasonM='" + reasonM + "' , ";
                vals = vals + "'" + reasonM + "',";

                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(reasonM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    JSONArray nexts = RIUtil.GetDictByFather(id);
                    if (nexts.size() > 1) {
                        return ErrNo.set(null, 2, "未选择到最底层标签");
                    }
                    bzs = bzs + id + ",";
                    while (id.length() > 0) {
                        String fid = RIUtil.dicts.get(id).getString("father_id");

                        try {
                            id = RIUtil.dicts.get(fid).getString("id");
                            bzs = bzs + id + ",";
                        } catch (Exception ex) {
                            id = "";
                        }

                    }

                }
                System.out.println(bzs);

            }
// - 未标注 0
//  - 已标注审核通过 1
//  - 已标注待审核 2
//  - 已标注审核未通过 3
            String unit = data.getString("unit");
            if (unit.startsWith("320412")) {
                try {
                    jdbcTemplate = new MysqlHelper("wjjq");
                    String sqls = "update wjsc_jq_cjxx set " + sql + " bzzt=2,mark=1,spjg=0,unit='" + data.getString(
                            "unit") + "'," + "bzr='" + data.getString("opt_user") + "',bz_time='" + new SimpleDateFormat("yyyy-MM" + "-dd " + "HH:mm:ss").format(new Date()) + "',bzrxm='" + data.getString("name") + "'," + "jqbz='" + bzs + "' where jjbh='" + jjbh + "'";
                    logger.warn(sqls);
                    jdbcTemplate.update(sqls);

                    if (vals.length() > 2) {
                        vals = vals.substring(0, vals.length() - 1);
                    }
                    sqls =
                            "insert into jq_bz(bzr,bz_time,his_type,jjbh,addressM,resultM,timeM,toolM," + "reasonM) " +
                                    "values('" + data.getString("opt_user") + "'," + "'" + new SimpleDateFormat("yyyy" + "-MM-dd " + "HH" + ":mm:ss").format(new Date()) + "','1','" + jjbh + "'," + vals + ")";
                    logger.warn(sqls);
                    jdbcTemplate.update(sqls);
                } catch (Exception ex) {
                    logger.error(Lib.getTrace(ex));
                }
            }

            ora_hl = new OracleHelper("ora_hl");
            String sqls = "update dsj_jq set " + sql + " bzzt=2,mark=1,bzr='" + data.getString("opt_user") + "'," +
                    "bz_time='" + new SimpleDateFormat("yyyy-MM" + "-dd " + "HH:mm:ss").format(new Date()) + "'," + "jqbz='" + bzs + "' where jjbh='" + jjbh + "'";
            logger.warn(sqls);
            ora_hl.update(sqls);


            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            if (jdbcTemplate != null) {
                jdbcTemplate.close();
            }
            if (ora_hl != null) {
                ora_hl.close();
            }
        }
    }

    private JSONObject GetJQVideo(JSONObject data) {
        OracleHelper ora = null;
        JSONObject back = ErrNo.set(0);
        int page = 1;
        int limit = 20;
        String jjbh = "";
        SSLSocketFactory ssfFactory = null;
        try {
            ora = new OracleHelper("ora_hl");
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");
            } else {
                return ErrNo.set(null, 2, "缺少接警编号");
            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");

            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");

            }

            String sql = "select cjsj01 from dsj_jq where jjbh='" + jjbh + "'";
            String cjsj = ora.query_one(sql, "CJSJ01");

            long sj = RIUtil.dateToStamp(cjsj) - (1000 * 60 * 60 * 24 * 10);
            cjsj = RIUtil.stampToTime(sj);

            String today = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

//            String url = TNOAConf.get("HttpServ", "kd_vidio_old") + "?caseEventIds=" + jjbh + "&startTime=" + cjsj +
//                    "&endTime=" + today + "&pageSize=100&pageNo=0";
//            logger.warn(url);
//
//            SSLContext sc = SSLContext.getInstance("TLS");
//            sc.init(null, new TrustManager[]{new TrustAllCerts()}, new SecureRandom());
//            ssfFactory = sc.getSocketFactory();
//
//            OkHttpClient client =
//                    new OkHttpClient().newBuilder().sslSocketFactory(ssfFactory, new TrustAllCerts()).build();
//            Request request = new Request.Builder().url(url).method("GET", null).build();
//            Response res = client.newCall(request).execute();
//            String rets = res.body().string();
            String rets = KdVideoUtil.getViidVideo(jjbh, cjsj, today);
            JSONObject ress = JSONObject.parseObject(rets);

            if (ress.containsKey("code") && ress.getInteger("code") == 0 && ress.containsKey("result")) {

                JSONObject result = ress.getJSONObject("result");
                int total = result.getInteger("total");
                if (total > 0) {

                    JSONArray datas = result.getJSONArray("data");
                    List<JSONObject> list = new ArrayList<>();
                    for (int i = 0; i < datas.size(); i++) {
                        JSONObject one = datas.getJSONObject(i);
                        list.add(one);
                    }

                    list = GroupByListAsLimit(list, "beginTime", page, limit);

                    back.put("data", list);
                    back.put("count", total);


                } else {
                    back.put("data", new JSONArray());
                    back.put("count", 0);
                }


            } else {
                return ErrNo.set(null, 2, rets);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return ErrNo.set(null, 2, Lib.getTrace(e));
        } finally {
            ora.close();

        }
        return back;
    }

    private static List<JSONObject> GroupByListAsLimit(List<JSONObject> list, String col, int page, int limit) {
        List<JSONObject> back = new ArrayList<>();

        int count = list.size();


        int start = (page - 1) * limit;
        int end = start + limit;
        if (end > count) {
            end = count;
        }
        logger.warn(start + "-->" + end);


        Collections.sort(list, (JSONObject o1, JSONObject o2) -> {

            String czsja = o1.getString(col);
            long a = 0;
            try {
                a = RIUtil.dateToStamp(czsja);
            } catch (Exception e) {
                e.printStackTrace();
            }
            String czsjb = o2.getString(col);
            long b = 0;
            try {
                b = RIUtil.dateToStamp(czsjb);
            } catch (Exception e) {
                e.printStackTrace();
            }

            // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
            if (a > b) {  //降序排列，升序改成a>b
                return 1;
            } else if (a == b) {
                return 0;
            } else {
                return -1;
            }
        });

        for (int m = start; m < end; m++) {
            try {
                JSONObject one = list.get(m);
                back.add(one);
            } catch (Exception ex) {

            }
        }
        return back;

    }

    private JSONObject GetAJForBt(JSONObject data) {
        OracleModelHelper ora = null;
        JSONObject back = ErrNo.set(0);
        int page = 1;
        int limit = 20;
        String jgbh = "";
        String unit = "";
        List<JSONObject> jq_list = new ArrayList<>();
        String sql = " ";
        int count = -1;

        try {
            ora = OracleModelPool.getModel();

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject done = RIUtil.dicts.get(unit);

                String type = "";
                try {
                    type = done.getString("type");
                } catch (Exception ex) {
                    type = "21";
                }
                String father_id = done.getString("father_id");
                if (father_id.length() < 5) {
                    father_id = done.getString("id");
                }
                String id = done.getString("id");

                if (type.equals("23")) {
                    sql = sql + " and (CHJDW_GAJGJGDM like '" + unit.substring(0, 6) + "%') ";
                } else if (type.equals("25") || type.equals("26")) {
                    sql = sql + " and (CHJDW_GAJGJGDM='" + unit.substring(0, 8) + "0000') ";
                } else if (type.equals("24")) {
                    sql = sql + " and CHJDW_GAJGJGDM like '" + father_id.substring(0, 6) + "%' ";
                } else if (type.equals("28")) {
                    unit = unit.substring(0, 6);
                    sql = sql + " and CHJDW_GAJGJGDM like '" + unit + "%' ";
                }
            }

            if (data.containsKey("jgbh") && data.getString("jgbh").length() > 0) {
                jgbh = data.getString("jgbh");
                sql += " and JGBH = '" + jgbh + "' ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            sql += " and JGBH like 'JG%' ";
            String sqls =
                    "select * from HL.V_JQ_AJ_JG  where 1=1 " + sql + " " + "order by SLSJ DESC " + "OFFSET " + (page - 1) * limit + " " + "ROWS FETCH NEXT" + " " + limit + " " + "ROWS ONLY ";
            logger.warn(sqls);
            jq_list = ora.query(sqls);
            //关联机构信息

            if (jq_list.size() > 0) {
                back.put("data", RelaDWInfo(jq_list));
            } else {
            }

            sqls = "select count(*) as count " + " from HL.V_JQ_AJ_JG where 1=1  " + sql;
            logger.warn(sqls);
            count = ora.query_count(sqls);
            back.put("data", jq_list);
            back.put("count", count);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            ora.close();

        }
        return back;
    }

    private JSONObject GetAJInfo(JSONObject data) {
        OracleHelper ora_hl = null;
        JSONObject back = ErrNo.set(0);
        String ajbh = "";
        String mark = "";
        String ajmc = "";
        String jyaq = "";
        String ssdw = "";
        String sql = "";
        int page = 1;
        int limit = 20;
        try {
            ora_hl = new OracleHelper("ora_hl");
            if (data.containsKey("ajbh") && data.getString("ajbh").length() > 0) {
                ajbh = data.getString("ajbh");
                if (ajbh.startsWith("J")) {
                    sql = sql + " and  ajbh like '" + ajbh + "%' ";
                } else if (ajbh.startsWith("3204")) {
                    sql = sql + " and  ajbh like 'J" + ajbh + "%' ";
                } else {
                    sql = sql + " and  instr(ajbh,'" + ajbh + "')>0 ";
                }
            }
            if (data.containsKey("ajmc") && data.getString("ajmc").length() > 0) {
                ajmc = data.getString("ajmc");
                sql = sql + " and  instr(ajmc,'" + ajmc + "')>0 ";
            }
            if (data.containsKey("jyaq") && data.getString("jyaq").length() > 0) {
                jyaq = data.getString("jyaq");
                sql = sql + " and  instr(jyaq,'" + jyaq + "') >0";
            }
            if (data.containsKey("ssdw") && data.getString("ssdw").length() > 0) {
                ssdw = data.getString("ssdw").substring(0, 8);

                sql = sql + " and  ajsszrq like '" + ssdw + "%' ";
            }
            if (data.containsKey("slsj_start") && data.getString("slsj_start").length() > 0) {
                String slsj_start = data.getString("slsj_start");

                sql = sql + " and  slsj >= '" + slsj_start + "' ";
            }
            if (data.containsKey("slsj_end") && data.getString("slsj_end").length() > 0) {
                String slsj_end = data.getString("slsj_end");

                sql = sql + " and  slsj <= '" + slsj_end + "' ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            String sqls =
                    "select * from JQAJ_AJ where 1=1 " + sql + " OFFSET " + (page - 1) * limit + " " + "ROWS " + "FETCH " +
                            "NEXT" + " " + limit + " ROWS ONLY";
            logger.warn(sqls);
            List<JSONObject> list = ora_hl.query(sqls);
            if (list.size() > 0) {
                back.put("data", list);

            } else {
                back.put("data", new JSONArray());
            }
            sqls = "select count(AJBH) AS COUNT from JQAJ_AJ where 1=1 " + sql;
            int count = ora_hl.query_count(sqls);
            back.put("count", count);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(505002);
        } finally {
            ora_hl.close();
        }
    }

    private JSONObject UpdateMarkType(JSONObject data) {
        OracleHelper ora_hl = null;
        JSONObject back = ErrNo.set(0);
        String jjbh = "";
        String mark = "";
        String opt_user = "";
        String real_opt_user = "";
        try {
            ora_hl = new OracleHelper("ora_hl");
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");
            } else {
                return ErrNo.set(505001);
            }
            opt_user = data.getString("opt_user");
            real_opt_user = data.getString("real_opt_user");


            String sql = "select HCZT from HL.DSJ_JQ WHERE  JJBH='" + jjbh + "'";
            String hczt = ora_hl.query_one(sql, "HCZT");
            if (hczt.equals("3")) {
                hczt = "4";
            } else {
                hczt = "2";
            }

            sql = "update HL.DSJ_JQ  set SFGLDZ_PDBZ=4,HCZT='" + hczt + "'," + "GLDZ_DJSJ='" + new SimpleDateFormat(
                    "yyyy-MM-dd HH:mm:ss").format(new Date()) + "',GLDZ_DJBGSJ" + "='" + opt_user + "," + real_opt_user + "' " + "where JJBH='" + jjbh + "'";

            logger.warn(sql);

            ora_hl.update(sql);

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(505002);
        } finally {
            ora_hl.close();
        }
    }

    private JSONObject getJqForBt(JSONObject data, TNOAHttpRequest request) {
        OracleModelHelper ora = null;
        JSONObject back = ErrNo.set(0);
        int page = 1;
        int limit = 20;
        String jgbh = "";
        String unit = "";
        List<JSONObject> jq_list = new ArrayList<>();
        String sql = " ";
        int count = -1;

        try {
            ora = OracleModelPool.getModel();

            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject done = RIUtil.dicts.get(unit);

                String type = "";
                try {
                    type = done.getString("type");
                } catch (Exception ex) {
                    type = "21";
                }
                String father_id = done.getString("father_id");
                if (father_id.length() < 5) {
                    father_id = done.getString("id");
                }
                String id = done.getString("id");

                if (type.equals("23")) {
                    sql = sql + " and (CHJDW_GAJGJGDM like '" + unit.substring(0, 6) + "%') ";
                } else if (type.equals("25") || type.equals("26")) {
                    sql = sql + " and (CHJDW_GAJGJGDM='" + unit.substring(0, 8) + "0000') ";
                } else if (type.equals("24")) {
                    sql = sql + " and CHJDW_GAJGJGDM like '" + father_id.substring(0, 6) + "%' ";
                } else if (type.equals("28")) {
                    unit = unit.substring(0, 6);
                    sql = sql + " and CHJDW_GAJGJGDM like '" + unit + "%' ";
                }
            }

            if (data.containsKey("jgbh") && data.getString("jgbh").length() > 0) {
                jgbh = data.getString("jgbh");
                sql += " and JGBH = '" + jgbh + "' ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            sql += " and JGBH like 'JG%' ";
            String sqls =
                    "select jq.JJBH,jq.CJSJ01,jq.CJLB,jq.CHJDW_GAJGJGDM,jq.CJDZ_DZMC,jq.JJSJ,jq.BJNR,jq.JGBH, " + "jq" +
                            ".CJRHZXS, aj.* " + "from HL.DSJ_JQ  jq " + "left join HL.JQAJ_AJ aj on jq.JJBH = aj" + ".AJBH" + " " +
                            "where 1=1 and (jq.JGBH is not null or jq.JGBH != '') and jq.type = '1' " + sql + " " + "order by jq" +
                            ".CJSJ01 DESC " + "OFFSET " + (page - 1) * limit + " " + "ROWS FETCH " + "NEXT" + " " + limit + " " +
                            "ROWS ONLY ";
            logger.warn(sqls);
            jq_list = ora.query(sqls);
            //关联机构信息

            if (jq_list.size() > 0) {
                back.put("data", RelaDWInfo(jq_list));
            } else {
            }

            sqls =
                    "select count(*) as count " + " from HL.DSJ_JQ where 1=1 and (JGBH is not null or JGBH != '') " + sql;
            logger.warn(sqls);
            count = ora.query_count(sqls);
            back.put("data", jq_list);
            back.put("count", count);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            ora.close();

        }
        return back;
    }

    private List<JSONObject> RelaDWInfo(List<JSONObject> jq_list) {
        List<JSONObject> back = new ArrayList<>();
        OracleHelper ora_bt = null;
        try {

            ora_bt = new OracleHelper("ora_bt");

            for (JSONObject one : jq_list) {
                String JGBH = one.getString("JGBH");
                String cjdw = one.getString("CHJDW_GAJGJGDM");
                try {
                    String cjdwmc = RIUtil.dicts.get(cjdw).getString("dict_name");
                    one.put("CJDWMC", cjdwmc);
                } catch (Exception ex) {

                }
                String cjlb = one.getString("CJLB");
                try {
                    String CJLBMC = RIUtil.dicts.get(cjlb).getString("dict_name");
                    one.put("CJLBMC", CJLBMC);
                } catch (Exception ex) {

                }
                List<JSONObject> hy_details = new ArrayList<>();
                String sqls = "select a.DWMC,b.HYXL,b.YYZT,b.GLBM,a.DWDZMC from CZJG_JBXX a LEFT JOIN CZJG_JZGL b ON "
                        + "a" + ".JGBH=b.GLJGBH where JGBH='" + JGBH + "'";
                hy_details = ora_bt.query(sqls);

                if (hy_details.size() > 0) {
                    one.putAll(hy_details.get(0));
                } else {
                }
                back.add(one);
            }
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return jq_list;
        } finally {
            ora_bt.close();
        }
    }

    private JSONObject CheckJQBZ(JSONObject data, TNOAHttpRequest request) {

        JSONObject back = ErrNo.set(0);
        InfoModelHelper mysql = null;
        MysqlHelper my_sso = null;
        OracleHelper ora_hl = null;

        try {
            mysql = request.openInfoImpl();
            my_sso = new MysqlHelper("mysql_sso");
            ora_hl = new OracleHelper("ora_hl");

            String opt_user = "";
            String hczt = "";
            String jjbh = "";
            if (data.containsKey("hczt") && data.getString("hczt").length() > 0) {
                hczt = data.getString("hczt");
                if (hczt.equals("2") || hczt.equals("4")) {
                    return ErrNo.set(505001);
                }
            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh").trim();
            } else {
                return ErrNo.set(505001);
            }
            String real_opt_user = "";
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("real_opt_user") && data.getString("real_opt_user").length() > 0) {
                real_opt_user = data.getString("real_opt_user");
                String sql = "select id from user where id_num='" + real_opt_user + "'";
                real_opt_user = mysql.query_one(sql, "id");

            } else {
                return ErrNo.set(505001);
            }

            String cwyy = "";
            if (data.containsKey("cwyy") && data.getString("cwyy").length() > 0) {
                cwyy = data.getString("cwyy");
            } else {
                if (hczt.equals("3")) {
                    return ErrNo.set(505001);
                }
            }

            String remark = "";
            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
            } else {

            }

            String sql = "select ipaddr from user_log where id_card='" + real_opt_user + "' order by time desc limit 1";
            logger.warn(sql);
            String ipp = my_sso.query_one(sql, "ipaddr");


            String sqlS =
                    "update \"HL\".\"DSJ_JQ\" set HCZT='" + hczt + "',HCSJ='" + new SimpleDateFormat("yyyy-MM" + "-dd" + " " + "HH:mm:ss").format(new Date()) + "'," + "HCR_YHM='" + opt_user + "',HCR_IPDZ='" + ipp + "' " + "where " + "JJBH='" + jjbh + "'";
            logger.warn(sqlS);
            ora_hl.update(sqlS);
            if (hczt.equals("3")) {
                JSONObject det = new JSONObject();
                det.put("JJBH", jjbh);
                det.put("ID", String.valueOf(UUID.randomUUID()));
                det.put("CWYY", cwyy);
                det.put("REMARK", remark);
                det.put("OPT_USER", opt_user);
                det.put("REAL_OPT_USER", real_opt_user);
                det.put("TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                RIUtil.JsonInsert_ora(det, "JQ_SH_HIS", ora_hl);
            }

            return back;
        } catch (Exception ex) {
            logger.warn(Lib.getTrace(ex));
            return ErrNo.set(505002);


        } finally {
            my_sso.close();
            InfoModelPool.putModel(mysql);
            ora_hl.close();
        }


    }

    private JSONObject SearchBySFZ(JSONObject data, TNOAHttpRequest request) {
        String token = request.getHeader("token");
        System.out.println("token->" + token);
        JSONObject back = ErrNo.set(0);
        String sfz = "";
        if (data.containsKey("sfz") && data.getString("sfz").length() > 0) {
            sfz = data.getString("sfz");

        } else {
            return ErrNo.set(505001);
        }
        try {
            String url = TNOAConf.get("Httpd", "gl_sfzcx");
            logger.warn(url);

            JSONObject d = new JSONObject();
            d.put("zjhm", sfz);
            String ret = HttpConnection.post_tokenX(url, token, d);

            back.put("data", ret);
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(505002);
        }
        return back;


    }

    private JSONObject SearchDWAsDzid(JSONObject data) {
        OracleHelper ora = null;
        JSONObject back = ErrNo.set(0);
        String dzid = "";
        int page = 1;
        int limit = 20;
        String dwsql = " and 1=1 ";
        try {
            if (data.containsKey("dzid") && data.getString("dzid").length() > 0) {
                dzid = data.getString("dzid");

            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("dwmc") && data.getString("dwmc").length() > 0) {
                String dwmc = data.getString("dwmc");
                dwsql = " and DWMC like '%" + dwmc + "%'";

            }

            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }
            ora = new OracleHelper("ora_bt");
            String sql =
                    "select * from V_DW_ZT_FJ where DZID='" + dzid + "' " + dwsql + "  OFFSET " + (page - 1) * limit + " " + "ROWS FETCH NEXT" + " " + limit + " " + "ROWS ONLY";
            logger.warn(sql);
            List<JSONObject> list = ora.query(sql);
            sql = "select count(JGBH) as count from V_DW_ZT_FJ where DZID='" + dzid + "' " + dwsql;
            int count = ora.query_count(sql);
            back.put("count", count);
            if (list.size() > 0) {

                back.put("data", list);

            } else {
                back.put("data", new JSONArray());
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(505002);

        } finally {

            ora.close();
        }

        return back;
    }

    private JSONObject CreateRWDW(JSONObject data) {
        MysqlHelper qqb_op = null;
        OracleHelper ora = null;
        OracleHelper ora_gl = null;
        JSONObject back = ErrNo.set(0);
        String xsnr = "";
        String jjbh = "";
        String dzid = "";
        String type = "";
        try {
            if (data.containsKey("xsnr") && data.getString("xsnr").length() > 0) {
                xsnr = data.getString("xsnr");

            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");

            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("dzid") && data.getString("dzid").length() > 0) {
                dzid = data.getString("dzid");

            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");

            } else {
                return ErrNo.set(505001);
            }


            qqb_op = new MysqlHelper("mysql_qqb_OP");
            ora = new OracleHelper("oracle");
            ora_gl = new OracleHelper("ora_gl");
            String sql = "select DMDM,DSDZID,DZZBX,DZZBY,HJZRQ,HJZRQMC,PCSDM,PCSMC,FJDM,FJMC,DZ from CZQJ_YBDS" +
                    ".ADDRESS_INFO " + "where" + " " + "DZID='" + dzid + "'";
            logger.warn(sql);
            List<JSONObject> list = ora_gl.query(sql);
            if (list.size() > 0) {
                JSONObject one = list.get(0);
                String dm = one.getString("DMDM");
                String dsdz = one.getString("DSDZID");
                String lat = one.getString("DZZBY");
                String lng = one.getString("DZZBX");

                sql =
                        "update HL.DSJ_JQ  set XQ='" + dm + "',DZXX_XXZJBH='" + dsdz + "',LAT='" + lat + "',LNG='" + lng +
                                "',SFGLDZ_PDBZ=1,JGBH='',TYPE='" + type + "' where JJBH='" + jjbh + "'";

                logger.warn(sql);
                ora.update(sql);
                JSONObject det = new JSONObject();
                String dz = one.getString("DZ");
                det.put("ZRQ", one.getString("HJZRQ"));
                det.put("ZRQMC", one.getString("HJZRQMC"));
                det.put("PCSDM", one.getString("PCSDM"));
                det.put("PCSMC", one.getString("PCSMC"));
                det.put("FJDM", one.getString("FJDM"));
                det.put("FJMC", one.getString("FJMC"));
                det.put("XSNR", xsnr);
                det.put("JJBH", jjbh);
                det.put("DZID", dzid);
                det.put("DZMC", dz);
                det.put("ADD_TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

                sql = "select ID from H_DWXZ where JJBH='" + jjbh + "'";
                String id = qqb_op.query_one(sql, "ID");
                if (id.length() > 0) {
                    back = new JSONObject();
                    back.put("errno", 505005);
                    back.put("error", "该警情已下发单位新增任务");
                } else {
                    RIUtil.JsonInsert_mysql(det, "H_DWXZ", qqb_op);
                }
            } else {
                return ErrNo.set(505004);
            }


        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(505002);

        } finally {
            qqb_op.close();
            ora.close();
            ora_gl.close();
        }

        return back;
    }

    private JSONObject GetJQByJJBH(JSONObject data, TNOAHttpRequest request) {
        OracleModelHelper ora = null;
        JSONObject back = ErrNo.set(0);

        String JJBH = "";
        try {
            ora = OracleModelPool.getModel();

            if (data.containsKey("JJBH") && data.getString("JJBH").length() > 0) {
                JJBH = data.getString("JJBH");

            } else {
                return ErrNo.set(505001);
            }

            String sqls = "select * from DSJ_JQ where JJBH='" + JJBH + "'";
            logger.warn(sqls);
            List<JSONObject> list = ora.query(sqls);
            if (list.size() > 0) {
                back.put("data", RealJQINfo(list, ora));
            } else {
                back.put("data", new JSONArray());
            }

            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(505002);

        } finally {
            OracleModelPool.putModel(ora);
        }


    }

    private JSONObject UpdateJQRY(JSONObject data) {

        JSONObject back = ErrNo.set(0);
        OracleHelper ora = null;
        OracleHelper ora_bk_gl = null;
        OracleHelper ora_bt = null;
        String JJBH = "";
        String GMSFHM = "";
        String XM = "";
        String ID = "";
        String RYLX = "";
        String RYLB = "";
        String JGBH = "";

        try {
            ora = new OracleHelper("ora_hl");
            ora_bk_gl = new OracleHelper("ora_gl");
            ora_bt = new OracleHelper("ora_bt");
            if (data.containsKey("JGBH") && data.getString("JGBH").length() > 0) {
                JGBH = data.getString("JGBH");
            }
            if (data.containsKey("JJBH") && data.getString("JJBH").length() > 0) {
                JJBH = data.getString("JJBH");

            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("GMSFHM") && data.getString("GMSFHM").length() > 0) {
                GMSFHM = data.getString("GMSFHM");

            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("XM") && data.getString("XM").length() > 0) {
                XM = data.getString("XM");
            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("ID") && data.getString("ID").length() > 0) {
                ID = data.getString("ID");
            }

            String personM = "";
            String bzs = "";
            if (data.containsKey("personM") && data.getString("personM").length() > 0) {
                personM = data.getString("personM");


                List<String> addss = RIUtil.HashToList(RIUtil.StringToList(personM));
                for (int i = 0; i < addss.size(); i++) {
                    String id = addss.get(i);
                    JSONArray nexts = RIUtil.GetDictByFather(id);
                    if (nexts.size() > 1) {
                        return ErrNo.set(null, 2, "未选择到最底层标签");
                    }
                    if (!bzs.contains(id)) {
                        bzs = bzs + id + ",";
                    }
                    while (id.length() > 0) {
                        String fid = RIUtil.dicts.get(id).getString("father_id");

                        try {
                            id = RIUtil.dicts.get(fid).getString("id");
                            if (!bzs.contains(id)) {
                                bzs = bzs + id + ",";
                            }
                        } catch (Exception ex) {
                            id = "";
                        }

                    }

                }
                System.out.println(bzs);

            }


            String opt_user = data.getString("opt_user");
            String unit = data.getString("unit");
            data.remove("opt");
            data.remove("unit");
            data.remove("opt_user");
            data.remove("real_opt_user");
            data.remove("img");
            data.remove("address");
            data.remove("phone");
            data.remove("X-Real-IP");
            data.put("PERSONM", personM);
            data.remove("personM");
            data.put("PERSONMS", bzs);
            data.put("JGBH", JGBH);
            if (StringUtil.isNotBlank(JGBH)) {
                String sql = "select jgbh,dwmc from JWRY_DBA.CZJG_JBXX where jgbh = '" + JGBH + "' ";
                logger.warn(sql);
                List<JSONObject> dws = ora_bt.query(sql);
                if (!dws.isEmpty()) {
                    data.put("DWMC", dws.get(0).getString("DWMC"));
                }
            }
            if (ID.length() > 0) {
                //UPDATE
                System.out.println(data);
                RIUtil.UpdateSql_ID_ora(data, "JQ_SJRY", ID, ora, "ID");

            } else {
                //INSERT
                ID = String.valueOf(UUID.randomUUID());
                data.put("ID", ID);

                String sql = "select ID from CZJG_YBDS.YW_SYRK where GMSFHM='" + GMSFHM + "'";
                String rkid = ora_bk_gl.query_one(sql, "ID");
                data.put("SYRKID", rkid);

                sql = "select CJLB FROM DSJ_JQ WHERE JJBH='" + JJBH + "'";
                String CJLB = ora.query_one(sql, "CJLB");
                data.put("CJLB", CJLB);

                System.out.println(data);
                RIUtil.JsonInsert_ora(data, "JQ_SJRY", ora);

            }


            MysqlHelper jdbcTemplate = null;

            if (unit.startsWith("320412")) {
                try {
                    jdbcTemplate = new MysqlHelper("wjjq");

                    String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                    String sqls =
                            "update wjsc_jq_sjxx set personMs='" + bzs + "',personM='" + personM + "' where " + "jjbh" +
                                    "='" + JJBH + "' and " + "gmsfhm='" + GMSFHM + "'";
                    logger.warn(sqls);
                    jdbcTemplate.update(sqls);


                    sqls = "insert into jq_bz(bzr,bz_time,his_type,jjbh,personM) " + "values('" + opt_user + "'," +
                            "'" + create_time + "','3','" + JJBH + "','" + personM + "')";
                    logger.warn(sqls);
                    jdbcTemplate.update(sqls);
                } catch (Exception ex) {
                    logger.error(Lib.getTrace(ex));
                } finally {
                    jdbcTemplate.close();
                }
            }

            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(505002);
        } finally {
            ora.close();
            ;
            ora_bk_gl.close();
        }
    }

    private JSONObject DeleteJQRY(JSONObject data) {

        JSONObject back = ErrNo.set(0);
        OracleModelHelper ora = null;
        String ID = "";
        String opt_user = "";
        try {
            ora = OracleModelPool.getModel();
            if (data.containsKey("ID") && data.getString("ID").length() > 0) {
                ID = data.getString("ID");
            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(505001);
            }

            String sql = "update JQ_SJRY set DELETED=1 , DELETE_USER='" + opt_user + "' where ID='" + ID + "'";
            ora.update(sql);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(505002);
        } finally {
            OracleModelPool.putModel(ora);
        }
    }

    private JSONObject GetJQTypeTreeOld(JSONObject data, InfoModelHelper mysql) {

        JSONObject back = ErrNo.set(0);
        try {
            JSONArray type50 = RIUtil.GetDictByType(50);
            List<JSONObject> fifty = new ArrayList<>();
            for (int i = 0; i < type50.size(); i++) {
                JSONObject fif = type50.getJSONObject(i);
                fifty.add(fif);
            }

            Collections.sort(fifty, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("index_no");
                    b = o2.getInteger("index_no");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });

            JSONArray type521 = RIUtil.GetDictByType(52);
            List<JSONObject> fifty2 = new ArrayList<>();
            for (int i = 0; i < type521.size(); i++) {
                JSONObject fif = type521.getJSONObject(i);
                fifty2.add(fif);
            }

            Collections.sort(fifty2, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("index_no");
                    b = o2.getInteger("index_no");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });

            JSONArray type54 = RIUtil.GetDictByType(54);
            JSONArray rest = new JSONArray();
            JSONArray t50 = new JSONArray();
            for (int i = 0; i < fifty.size(); i++) {
                JSONObject one50 = fifty.get(i);

                String id = one50.getString("id");
                if (!id.equals("50-030000")) {
                    JSONArray t52 = new JSONArray();
                    for (int a = 0; a < fifty2.size(); a++) {
                        JSONObject one52 = fifty2.get(a);
                        if (one52.getString("father_id").equals(id)) {
                            String id52 = one52.getString("id");

                            JSONArray t54 = new JSONArray();

                            for (int b = 0; b < type54.size(); b++) {
                                JSONObject one54 = type54.getJSONObject(b);
                                if (one54.getString("father_id").equals(id52)) {

                                    t54.add(one54);
                                }
                            }

                            one52.put("dets", t54);
                            t52.add(one52);
                        }

                    }

                    one50.put("dets", t52);
                    t50.add(one50);
                }

            }
            rest.add(t50);
            back.put("data", rest);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(505002);
        }

    }

    private JSONObject GetJQTypeTree(JSONObject data, InfoModelHelper mysql) {

        JSONObject back = ErrNo.set(0);
        try {
            JSONArray type50 = RIUtil.GetDictByType(511);
            List<JSONObject> fifty = new ArrayList<>();
            for (int i = 0; i < type50.size(); i++) {
                JSONObject fif = type50.getJSONObject(i);
                fifty.add(fif);
            }

            Collections.sort(fifty, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("index_no");
                    b = o2.getInteger("index_no");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });

            JSONArray type521 = RIUtil.GetDictByType(512);
            List<JSONObject> fifty2 = new ArrayList<>();
            for (int i = 0; i < type521.size(); i++) {
                JSONObject fif = type521.getJSONObject(i);
                fifty2.add(fif);
            }

            Collections.sort(fifty2, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("index_no");
                    b = o2.getInteger("index_no");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });

            JSONArray type54 = RIUtil.GetDictByType(513);
            List<JSONObject> fifty4 = new ArrayList<>();
            for (int i = 0; i < type54.size(); i++) {
                JSONObject fif = type54.getJSONObject(i);
                fifty4.add(fif);
            }

            Collections.sort(fifty4, (JSONObject o1, JSONObject o2) -> {
                //转成JSON对象中保存的值类型
                int a = 0;
                int b = 0;

                try {
                    a = o1.getInteger("index_no");
                    b = o2.getInteger("index_no");
                } catch (Exception ex) {

                }

                // 如果a, b数据类型为int，可直接 return a - b ;(升序，降序为 return b - a;)
                if (a > b) {  //降序排列，升序改成a>b
                    return 1;
                } else if (a == b) {
                    return 0;
                } else {
                    return -1;
                }
            });
            JSONArray type56 = RIUtil.GetDictByType(514);

            JSONArray rest = new JSONArray();
            JSONArray t50 = new JSONArray();
            for (int i = 0; i < fifty.size(); i++) {
                JSONObject one50 = fifty.get(i);

                String id = one50.getString("id");
                if (!id.equals("51-03000000")) {
                    JSONArray t52 = new JSONArray();
                    for (int a = 0; a < fifty2.size(); a++) {
                        JSONObject one52 = fifty2.get(a);
                        if (one52.getString("father_id").equals(id)) {
                            String id52 = one52.getString("id");

                            JSONArray t54 = new JSONArray();

                            for (int b = 0; b < type54.size(); b++) {
                                JSONObject one54 = type54.getJSONObject(b);
                                if (one54.getString("father_id").equals(id52)) {
                                    String id54 = one54.getString("id");

                                    JSONArray t56 = new JSONArray();

                                    for (int c = 0; c < type56.size(); c++) {
                                        JSONObject one56 = type56.getJSONObject(c);
                                        if (one56.getString("father_id").equals(id54)) {

                                            t56.add(one56);
                                        }
                                    }

                                    one54.put("dets", t56);
                                    t54.add(one54);
                                }
                            }

                            one52.put("dets", t54);
                            t52.add(one52);
                        }

                    }

                    one50.put("dets", t52);
                    t50.add(one50);
                }

            }
            rest.add(t50);
            back.put("data", rest);
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(505002);
        }

    }


    private JSONObject updateBQ(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String JJBH = "";


        String bq = "";
        String remark = "";
        String sql = "";
        OracleModelHelper ora = null;
        try {
            ora = OracleModelPool.getModel();
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                JJBH = data.getString("jjbh");
            } else {
                return ErrNo.set(505001);
            }

            if (data.containsKey("bq") && data.getString("bq").length() > 0) {
                bq = data.getString("bq");
                sql = sql + " BQ='" + bq + "',";
            }
            if (data.containsKey("remark") && data.getString("remark").length() > 0) {
                remark = data.getString("remark");
                sql = sql + " REMARK='" + remark + "',";
            } else {

            }

            if (sql.length() > 2) {
                sql = sql.substring(0, sql.length() - 1);
            } else {
                return ErrNo.set(505001);
            }


            String sqls = "update HL.DSJ_JQ  set " + sql + " where JJBH='" + JJBH + "'";

            logger.warn(sqls);

            ora.update(sqls);


            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(505002);

        } finally {
            OracleModelPool.putModel(ora);
        }


    }

    private JSONObject updateJQAddress(JSONObject data) {
        JSONObject back = ErrNo.set(0);
        String JJBH = "";
        String dzid = "";
        String lat = "";
        String lng = "";
        String jgid = "";
        String type = "";

        String from = "";
        String road = "-1";
        OracleHelper ora = null;
        OracleHelper ora_gl = null;
        try {
            ora = new OracleHelper("ora_hl");
            ora_gl = new OracleHelper("ora_gl");
            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                JJBH = data.getString("jjbh");
            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("dzid") && data.getString("dzid").length() > 0) {
                dzid = data.getString("dzid");
            } else {
                return ErrNo.set(505001);
            }
            if (data.containsKey("lat") && data.getString("lat").length() > 0) {
                lat = data.getString("lat");
            }
            if (data.containsKey("lng") && data.getString("lng").length() > 0) {
                lng = data.getString("lng");
            }
            if (data.containsKey("jgid") && data.getString("jgid").length() > 0) {
                jgid = data.getString("jgid");
            }
            if (data.containsKey("road") && data.getString("road").length() > 0) {
                road = data.getString("road");
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
            } else {
                return ErrNo.set(505001);
            }
            String opt_user = data.getString("opt_user");
            String real_opt_user = data.getString("real_opt_user");
            String unit = data.getString("unit");

            if (dzid.startsWith("FF") || dzid.startsWith("B2")) {
                String sql = "select BZDZ_ID from CZQJ_YBDS.ADDRESS_FB_INFO WHERE ID='" + dzid + "'";
                logger.warn(sql);
                String did = ora_gl.query_one(sql, "BZDZ_ID");
                logger.warn(did);

                if (did.length() == 0) {
                    dzid = dzid;
                } else {
                    dzid = did;
                }
            }

            if (data.containsKey("from") && data.getString("from").length() > 0) {
                from = data.getString("from");
                if (from.equals("baotong")) {
                    String sql = "select jjbh from dsj_jq where jjbh='" + JJBH + "'";
                    String bh = ora.query_one(sql, "JJBH");
                    if (bh.length() == 0) {
                        return ErrNo.set(505010);
                    }
                }
            }
            String sql = "select DMDM,DSDZID,HJZRQ from CZQJ_YBDS.ADDRESS_INFO where DZID='" + dzid + "'";
            logger.warn(sql);
            List<JSONObject> list = ora_gl.query(sql);
            if (list.size() > 0) {
                JSONObject one = list.get(0);
                String dm = one.getString("DMDM");
                String dsdz = one.getString("DSDZID");
                String zrq = one.getString("HJZRQ");

                sql = "select HCZT from HL.DSJ_JQ WHERE  JJBH='" + JJBH + "'";
                String hczt = ora.query_one(sql, "HCZT");
                if (hczt.equals("3") || hczt.equals("4")) {
                    hczt = "4";
                } else if (hczt.equals("1")) {
                    hczt = "1";
                } else {
                    hczt = "2";
                }

                sql =
                        "update HL.DSJ_JQ  set XQ='" + dm + "',ZRQ='" + zrq + "',DZXX_XXZJBH='" + dsdz + "',LAT='" + lat +
                                "'," + "LNG='" + lng + "',SFGLDZ_PDBZ=1,JGBH='" + jgid + "',TYPE='" + type + "'," + "HCZT='" + hczt + "'," + "GLDZ_DJSJ='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "'," + "GLDZ_DJBGSJ='" + opt_user + "," + real_opt_user + "',ROADS='" + road + "' where " + "JJBH='" + JJBH + "'";

                logger.warn(sql);

                ora.update(sql);
                String finalType = type;
                String finalJgid = jgid;
                String finalJJBH = JJBH;
                if ((StrUtil.isNotBlank(zrq) && zrq.startsWith("320412")) || unit.startsWith("320412")) {
                    ThreadUtil.execAsync(() -> {
                        logger.info("异步修改武进警情:{}", finalJJBH);
                        updateWjJq(dm, finalType, finalJgid, dsdz, finalJJBH);
                    });
                }
                if ((StrUtil.isNotBlank(zrq) && zrq.startsWith("320411")) || unit.startsWith("320411")) {
                    String finalLat = lat;
                    String finalLng = lng;
                    String finalZrq = zrq;
                    ThreadUtil.execAsync(() -> {
                        logger.info("异步修改新北警情:{}", finalJJBH);
                        updateXbJq(dm, finalType, finalJgid, dsdz, finalJJBH, finalLat, finalLng,zrq);
                    });
                }


            } else {
                return ErrNo.set(505004);
            }


            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            logger.error("错误请求：{}", data);
            logger.error(ex.getMessage(), ex);
            return ErrNo.set(505002);

        } finally {
            ora.close();
            ora_gl.close();
        }


    }

    // 更新武进警情
    private void updateWjJq(String dm, String type, String jgid, String dsdz, String JJBH) {
        String sql;
        MysqlHelper wjjq = null;
        try {
            wjjq = new MysqlHelper("wjjq");
            sql =
                    "update wjsc_jq_cjxx set xq='" + dm + "',dz_type='" + type + "',jgbh='" + jgid + "'," +
                            "dsdz='" + dsdz + "' where jjbh='" + JJBH + "'";
            logger.warn(sql);
            wjjq.update(sql);

            sql =
                    "replace into jqbz_tmp(jjbh,xq,dz_type,jgbh,dsdz) values('" + JJBH + "','" + dm + "'," + "'" + type + "'," + "'" + jgid + "','" + dsdz + "')";

            logger.warn(sql);
            wjjq.update(sql);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            wjjq.close();
        }
    }

    private void updateXbJq(String dm, String type, String jgid, String dsdz, String JJBH, String lat, String lng,
                            String zrq) {
        String sql;
        MysqlHelper mysql = null;
        try {
            mysql = new MysqlHelper("xbjq");
            sql = "update wjsc_jq_cjxx set xq='" + dm + "',dz_type='" + type + "',jgbh='" + jgid + "'," +
                    "dsdz='" + dsdz + "', " + "xzb =  '" + lng + "', yzb = '" + lat + "', zrq = '" + zrq + "'  where jjbh='" + JJBH + "'";
            logger.warn(sql);
            mysql.update(sql);

//            sql =
//                    "replace into jqbz_tmp(jjbh,xq,dz_type,jgbh,dsdz) values('" + JJBH + "','" + dm + "'," + "'" + type + "'," + "'" + jgid + "','" + dsdz + "')";

//            logger.warn(sql);
//            mysql.update(sql);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            mysql.close();
        }
    }

    private JSONObject searchAddressInfo(JSONObject data, TNOAHttpRequest request) {

        String token = request.getHeader("token");
        System.out.println("token->" + token);
        JSONObject back = ErrNo.set(0);

        int limit = 20;
        int page = 1;
        String query = "";
        String type = "1";
        String search_scope = "";//全市查询
        int search_jq = 0;//精确查询
        OracleHelper ora_gl = null;
        OracleHelper ora_bt = null;
        String id = "";

        try {
            ora_gl = new OracleHelper("ora_gl");
            ora_bt = new OracleHelper("ora_bt");

            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            }

            if (data.containsKey("query") && data.getString("query").length() > 0) {
                query = data.getString("query");
            } else {
                if (StringUtil.isBlank(id)) return ErrNo.set(505001);
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                type = data.getString("type");
            }
            if (data.containsKey("search_scope") && data.getString("search_scope").length() > 0) {
                search_scope = data.getString("search_scope");
            }
            if (data.containsKey("search_jq") && data.getString("search_jq").length() > 0) {
                search_jq = data.getInteger("search_jq");
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }
            String unit = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            } else {
                unit = "320400000000";
            }
            JSONObject orgs = RIUtil.dicts.get(unit);
//            System.out.println(orgs);


            String t = orgs.getString("type");
            String url = "";
            if (type.equals("1")) {
                if (StringUtil.isNotBlank(id)) {
                    JSONArray jsonArray = new JSONArray();
                    String sql =
                            "select DWMC,DZID,XZB,YZB,DWDZMC,SJJYMC from JWRY_DBA.CZJG_JBXX where JGBH='" + id + "'";
                    logger.warn(sql);
                    List<JSONObject> dw = ora_bt.query(sql);
                    for (JSONObject jsonObject : dw) {
                        JSONObject json = new JSONObject();
                        json.put("x", jsonObject.getString("XZB"));
                        json.put("y", jsonObject.getString("YZB"));
                        json.put("dzid", jsonObject.getString("DZID"));
                        json.put("num", id);
                        json.put("mc",
                                jsonObject.getString("DWMC") + "[" + jsonObject.getString("SJJYMC") + "  地址:" + jsonObject.getString("DWDZMC") + "]");
                        jsonArray.add(json);
                    }
                    back.put("data", jsonArray);
                    back.put("count", jsonArray.size());
                } else {
                    //单位
                    String fq = "";

                    if (search_jq == 0) {
                        if (search_scope.equals("2")) {
                            logger.warn("t--->" + t);
                            if (t.equals("23") || t.equals("24")) {
                                fq = "&fq=ssfj:" + StringToURL(unit.substring(0, 6)) + "000000";
                            } else if (t.equals("25") || t.equals("28") || t.equals("26")) {
                                fq = "&fq=sspcs:" + StringToURL(unit.substring(0, 8)) + "0000";
                            }
                            logger.warn(fq);
                        }
                        url =
                                TNOAConf.get("Httpd", "zhcx_solr") + "q=bt:" + StringToURL(query) + "%20OR%20zy:" + StringToURL(query) + "&start=" + (page - 1) * limit + "&rows=" + limit + "&wt=json&fq=dl:" + StringToURL("单位") + fq;

                        logger.warn(url);
                        String ret = HttpConnection.post(url, new JSONObject());
                        try {
                            logger.warn("===========");
                            JSONObject rrr = JSONObject.parseObject(ret);

                            JSONObject res = rrr.getJSONObject("response");
                            int count = res.getInteger("numFound");
                            logger.warn(String.valueOf(count));
                            if (count > 0) {
                                logger.warn("===========");
                                back.put("data", RelaInfoToD(res.getJSONArray("docs"), 1));
                                back.put("count", count);
                            } else {
                                back.put("data", new JSONArray());
                                back.put("count", 0);
                            }

                        } catch (Exception ex) {
                            back.put("data", new JSONArray());
                            back.put("count", 0);
                        }
                    } else {
                        if (search_scope.equals("2")) {
                            logger.warn("t--->" + t);
                            if (t.equals("23") || t.equals("24")) {
                                fq = " and SSFXJ ='" + unit.substring(0, 6) + "000000'";
                            } else if (t.equals("25") || t.equals("28") || t.equals("26")) {
                                fq = "and SSPCS='" + unit.substring(0, 8) + "0000' ";
                            }

                        }


                        String sqls =
                                "select jgbh,dwmc,sjjymc,dwdzmc,xzb,yzb,yyzt,dzid from JWRY_DBA.CZJG_JBXX A " + "LEFT " +
                                        "JOIN " + "JWRY_DBA.CZJG_JZGL B ON A.JGBH=B.GLJGBH WHERE (INSTR(DWMC,'" + query + "')" + ">0" +
                                        " OR INSTR" + "(sjjymc,'" + query + "')>0 OR INSTR(dwdzmc,'" + query + "')>0" + "  " + ") " + fq + " order by yyzt" + " " + " OFFSET " + (page - 1) * limit + " " + "ROWS " + "FETCH" + " " + "NEXT " + limit + " ROWS " + "ONLY";
                        logger.warn(sqls);

                        List<JSONObject> dws = ora_bt.query(sqls);
                        logger.warn(dws.size() + "-->");


                        try {

                            if (dws.size() > 0) {
                                back.put("data", RelaInfoToD(dws, 1));
                                sqls =
                                        "select count(jgbh) as count from JWRY_DBA.CZJG_JBXX A LEFT " + "JOIN" + " " +
                                                "JWRY_DBA.CZJG_JZGL B ON A.JGBH=B.GLJGBH WHERE (INSTR(DWMC,'" + query + "')>0" + " " + "OR " + "INSTR" + "(sjjymc,'" + query + "')>0 OR INSTR(dwdzmc,'" + query + "')>0  " + ") " + fq;
                                int count = ora_bt.query_count(sqls);
                                back.put("count", count);
                            } else {
                                back.put("data", new JSONArray());
                                back.put("count", 0);
                            }

                        } catch (Exception ex) {
                            back.put("data", new JSONArray());
                            back.put("count", 0);
                        }

                    }
                }

            } else if (type.equals("2")) {//标准地址
                logger.warn("unit:" + unit);

                if (search_jq == 0) {
                    if (search_scope.equals("2")) {
                        if (t.equals("23") || t.equals("24") || t.equals("28")) {
                            unit = unit.substring(0, 6) + "000000";
                        } else if (t.equals("25") || t.equals("26")) {
                            unit = unit.substring(0, 8) + "0000";
                        } else {
                            unit = "320400000000";
                        }
                        // logger.warn("t--->" + t);
                    } else {
                        unit = "320400000000";
                    }
                    url = TNOAConf.get("Httpd", "gl_bzdz") + "?dzjsmc=" + query + "&dzzt=1&hjzrq=" + unit;
                    String tokenName = "X-Access-Token";
                    String ret = HttpConnection.http_get_x(url, tokenName, token);
                    if (ret.length() > 0) {
//                    System.out.println("->" + ret);
                        JSONObject rett = JSONObject.parseObject(ret);
                        if (rett.getBoolean("success")) {
                            JSONObject res = rett.getJSONObject("result");
                            try {
                                JSONArray docs = res.getJSONArray("records");

                                back.put("data", RelaInfoToD(docs, 2));
                                back.put("count", docs.size());
                            } catch (Exception ex) {
                                System.out.println(res);
                                back.put("data", new JSONArray());
                                back.put("count", 0);
                            }
                        } else {
                            back.put("data", new JSONArray());
                            back.put("count", 0);
                        }
                    } else {
                        back.put("data", new JSONArray());
                        back.put("count", 0);
                    }

                } else {
                    String fq = "";
                    if (search_scope.equals("2")) {
                        logger.warn("t--->" + t);

                        if (t.equals("23") || t.equals("24")) {
                            fq = " and FJDM ='" + unit.substring(0, 6) + "000000'";
                        } else if (t.equals("25") || t.equals("28") || t.equals("26")) {
                            fq = "and PCSDM='" + unit.substring(0, 8) + "0000' ";
                        }

                    }

                    String sql =
                            "select  dzid,dz,dzzbx,dzzby from czqj_ybds.address_info where dz like '%" + query + "%'" + fq + " OFFSET " + (page - 1) * limit + " " + "ROWS " + "FETCH " + "NEXT " + limit + " ROWS ONLY";


                    logger.warn(sql);
                    List<JSONObject> list = ora_gl.query(sql);
                    if (list.size() > 0) {
                        JSONArray dets = new JSONArray();

                        for (int i = 0; i < list.size(); i++) {
                            JSONObject one = list.get(i);
                            JSONObject det = new JSONObject();
                            det.put("mc", one.getString("DZ"));
                            det.put("dzid", one.getString("DZID"));
                            det.put("num", one.getString("DZID"));
                            det.put("x", one.getString("DZZBX"));
                            det.put("y", one.getString("DZZBY"));
                            dets.add(det);
                        }

                        back.put("data", dets);
                        sql =
                                "select  count(1) as count from czqj_ybds.address_info where dz like '%" + query + "%'" + fq;
                        back.put("count", ora_gl.query_count(sql));

                    } else {
                        back.put("data", new JSONArray());
                        back.put("count", 0);
                    }

                }

            } else if (type.equals("3"))//建筑工地
            {
                String usql = " ";
                logger.warn("t--->" + t);
                if (search_scope.equals("2")) {
                    if (t.equals("23") || t.equals("24")) {
                        usql = " and SSFXJ='" + unit.substring(0, 6) + "000000'";
                    } else if (t.equals("25") || t.equals("28") || t.equals("26")) {
                        usql = " and SSPCS='" + unit.substring(0, 8) + "0000'";
                    } else {
                        usql = " ";
                    }
                }
                String sql =
                        "select JGBH,GDDZSM,JD,WD,DZBH,GDMC from JWRY_DBA.CZJG_JZGD_JBXX where GDMC like '%" + query + "%'" + " or GDDZSM like '%" + query + "%' " + usql + " OFFSET " + (page - 1) * limit + " " + "ROWS " + "FETCH NEXT " + limit + " ROWS ONLY";
                logger.warn(sql);
                List<JSONObject> list = ora_bt.query(sql);
                if (list.size() > 0) {

                    back.put("data", RelaInfoToD(list, 3));
                } else {

                    back.put("data", new JSONArray());
                }

                sql = "select count(JGBH) as count from JWRY_DBA.CZJG_JZGD_JBXX where GDMC like " + "'%" + query +
                        "%' or GDDZSM like '%" + query + "%' " + usql;
                int count = ora_bt.query_count(sql);
                back.put("count", count);
            } else if (type.equals("4"))//重要目标--》桥梁
            {
                String usql = " ";
                if (search_scope.equals("2")) {
                    if (t.equals("23") || t.equals("24")) {
                        usql = " and SSFXJ='" + unit.substring(0, 6) + "000000'";
                    } else if (t.equals("25") || t.equals("28") || t.equals("26")) {
                        usql = " and SSPCS='" + unit.substring(0, 8) + "0000'";
                    } else {
                        usql = " ";
                    }
                }
                String sql =
                        "select JGBH,DZBH,ZYMBQC,XZB,YZB from JWRY_DBA.CZJG_ZYMB_JBXX where (ZYMBQC like '%" + query + "%'" + " " + "or XXDZ like '%" + query + "%') AND ZYMBDL=45 " + usql + " OFFSET " + (page - 1) * limit + " ROWS FETCH NEXT " + limit + "ROWS ONLY";
                logger.warn(sql);
                List<JSONObject> list = ora_bt.query(sql);

                if (list.size() > 0) {

                    back.put("data", RelaInfoToD(list, 4));
                } else {

                    back.put("data", new JSONArray());
                }
                sql =
                        "select count(JGBH) as count from JWRY_DBA.CZJG_ZYMB_JBXX where (ZYMBQC " + "like '%" + query + "%'" + " " + "or XXDZ like '%" + query + "%') AND ZYMBDL=45 " + usql;
                int count = ora_bt.query_count(sql);
                back.put("count", count);
            } else if (type.equals("5"))//重要目标--》船舶
            {

                String usql = " ";
                if (search_scope.equals("2")) {
                    if (t.equals("23") || t.equals("24")) {
                        usql = " and SSFXJ='" + unit.substring(0, 6) + "000000'";
                    } else if (t.equals("25") || t.equals("28") || t.equals("26")) {
                        usql = " and SSPCS='" + unit.substring(0, 8) + "0000'";
                    } else {
                        usql = " ";
                    }
                }

                String sql =
                        "select JGBH,DZBH,CBMC,XZB,YZB from JWRY_DBA.CZJG_ZYMB_JBXX where (ZYMBQC like " + "'%" + query +
                                "%' or XXDZ like '%" + query + "%' or CBMC like '%" + query + "%') AND" + " " + "ZYMBFL=3 " + usql + "OFFSET " + (page - 1) * limit + " " + "ROWS FETCH NEXT " + limit + "ROWS ONLY";
                logger.warn(sql);
                List<JSONObject> list = ora_bt.query(sql);
                if (list.size() > 0) {

                    back.put("data", RelaInfoToD(list, 4));
                } else {

                    back.put("data", new JSONArray());
                }
                sql =
                        "select count(JGBH) as count from JWRY_DBA.CZJG_ZYMB_JBXX where (ZYMBQC " + "like '%" + query + "%'" + " " + "or XXDZ like '%" + query + "%') AND ZYMBFL=3 ";
                int count = ora_bt.query_count(sql);
                back.put("count", count);
            } else if (type.equals("6") || type.equals("7") || type.equals("8") || type.equals("9") || type.equals(
                    "10") || type.equals("11") || type.equals("12") || type.equals("13") || type.equals("14") || type.equals("15") || type.equals("16") || type.equals("17") || type.equals("18") || type.equals("99") || type.equals("19") || type.equals("20") || type.equals("21") || type.equals("22") || type.equals("23") || type.equals("24") || type.equals("25") || type.equals("26") || type.equals("27"))//非标--》车库
            {
                String usql = " ";
                if (search_scope.equals("2")) {
                    if (t.equals("23") || t.equals("24")) {
                        usql = " and SJGSDWDM like '%" + unit.substring(0, 6) + "%'";
                    } else if (t.equals("25") || t.equals("28") || t.equals("26")) {
                        usql = " and SJGSDWDM like '%" + unit.substring(0, 8) + "%'";
                    } else {
                        usql = " ";
                    }
                }
                String typesql = "";
                if (type.equals("6")) {
                    typesql = " AND ( DZLX='04' OR DZLX='05' ) ";
                } else if (type.equals("7")) {
                    typesql = " AND  DZLX='06'  ";
                } else if (type.equals("8")) {
                    typesql = " AND  DZLX='08'  ";
                } else if (type.equals("9")) {
                    typesql = " AND  DZLX='10'  ";
                } else if (type.equals("10")) {
                    typesql = " AND  DZLX='13'  ";
                } else if (type.equals("11")) {
                    typesql = " AND  DZLX='14'  ";
                } else if (type.equals("12")) {
                    typesql = " AND  DZLX='15'  ";
                } else if (type.equals("13")) {
                    typesql = " AND  DZLX='16'  ";
                } else if (type.equals("14")) {
                    typesql = " AND  DZLX='C1'  ";
                } else if (type.equals("15")) {
                    typesql = " AND  DZLX='C2'  ";
                } else if (type.equals("16")) {
                    typesql = " AND  DZLX='C3'  ";
                } else if (type.equals("17")) {
                    typesql = " AND  DZLX='C4'  ";
                } else if (type.equals("18")) {
                    typesql = " AND  DZLX='C5'  ";
                } else if (type.equals("19")) {
                    typesql = " AND  DZLX='01'  "; //楼房
                } else if (type.equals("20")) {
                    typesql = " AND  DZLX='02'  "; //门面房
                } else if (type.equals("21")) {
                    typesql = " AND  DZLX='03'  "; //平房（简易房）
                } else if (type.equals("22")) {
                    typesql = " AND  DZLX='07'  "; //工地工棚
                } else if (type.equals("23")) {
                    typesql = " AND  DZLX='09'  "; //楼顶建筑
                } else if (type.equals("24")) {
                    typesql = " AND  DZLX='11'  "; //固定住家船
                } else if (type.equals("25")) {
                    typesql = " AND  DZLX='12'  "; //桥梁
                } else if (type.equals("26")) {
                    typesql = " AND  DZLX='C6'  "; //旅遊景區
                } else if (type.equals("27")) {
                    typesql = " AND  DZLX='C7'  "; //高速服務區
                } else {
                    typesql = " AND  DZLX='99'  "; //其它
                }

                String sql = "select ID,BZDZ_ID,DZMC,DZZBX,DZZBY,DZBM,DZBM,DZZT from CZQJ_YBDS.ADDRESS_FB_INFO  " +
                        "where" + " " + "(DZMC like '%" + query + "%' OR DZBM like '%" + query + "%') " + typesql + " " + usql + " " + "OFFSET " + (page - 1) * limit + " " + "ROWS FETCH NEXT " + limit + "ROWS " + "ONLY";
                logger.warn(sql);
                List<JSONObject> list = ora_gl.query(sql);

                if (list.size() > 0) {

                    back.put("data", RelaInfoToD(list, 6));
                } else {

                    back.put("data", new JSONArray());
                }
                sql = "select count(ID) as count from CZQJ_YBDS.ADDRESS_FB_INFO where (DZMC " + "like '%" + query +
                        "%'" + " OR DZBM like '%" + query + "%')  " + typesql + usql;
                int count = ora_gl.query_count(sql);
                back.put("count", count);
            }
            return back;
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));

            return ErrNo.set(505002);

        } finally {
            ora_gl.close();
            ora_bt.close();

        }

    }

    private JSONArray RelaInfoToD(JSONArray docs, int type) {

        JSONArray back = new JSONArray();
        OracleHelper ora_bt = null;
        try {
            ora_bt = new OracleHelper("ora_bt");
            for (int i = 0; i < docs.size(); i++) {
                JSONObject one = docs.getJSONObject(i);
                JSONObject det = new JSONObject();
                if (type == 1 || type == 0) {   //单位 solr
                    det.put("num", one.getString("id"));
                    det.put("y", one.getString("y"));
                    det.put("x", one.getString("x"));
                    String dzid = one.getString("dzid");
                    String x = one.getString("x");
                    String y = one.getString("y");
                    String zy = one.getString("zy");
                    String sjzt = one.getString("sjzt");

                    String[] zys = zy.split("<br ");
                    String bm = "";
                    if (zy.contains("实际经营名称")) {
                        bm = zys[2].replace("/>实际经营名称：", "");
                    }
                    if (one.getString("dzid").length() == 0 || one.getString("x") == null || one.getString("x").length() == 0) {
                        String sql =
                                "select DZID,XZB,YZB from JWRY_DBA.CZJG_JBXX WHERE JGBH='" + one.getString("id") + "'";
                        dzid = ora_bt.query_one(sql, "DZID");
                        x = ora_bt.query_one(sql, "XZB");
                        y = ora_bt.query_one(sql, "YZB");

                    }

                    det.put("dzid", dzid);
                    det.put("x", x);
                    det.put("y", y);
                    det.put("mc", sjzt + "-" + one.getString("bt") + "[" + bm + zys[0].replace("单位地址", " 地址") + "]");
                } else if (type == 2)//标准地址
                {
                    det.put("num", one.getString("dzid"));
                    det.put("y", one.getString("dzzby"));
                    det.put("x", one.getString("dzzbx"));
                    det.put("dzid", one.getString("dzid"));
                    det.put("mc", one.getString("dz"));
                }
                back.add(det);

            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        } finally {
            ora_bt.close();
        }

        return back;
    }

    private List<JSONObject> RelaInfoToD(List<JSONObject> docs, int type) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < docs.size(); i++) {
            JSONObject one = docs.get(i);
            JSONObject det = new JSONObject();
            if (type == 3) {   //建筑工地
                det.put("num", one.getString("JGBH"));
                det.put("y", one.getString("WD"));
                det.put("x", one.getString("JD"));
                det.put("dzid", one.getString("DZBH"));
                det.put("mc", one.getString("GDMC") + "[" + one.getString("GDDZSM") + "]");
            } else if (type == 4)//Z重要目标
            {
                det.put("num", one.getString("JGBH"));
                det.put("y", one.getString("YZB"));
                det.put("x", one.getString("XZB"));
                det.put("dzid", one.getString("DZBH"));
                if (one.containsKey("ZYMBQC")) {
                    det.put("mc", one.getString("ZYMBQC"));
                } else {
                    det.put("mc", one.getString("CBMC"));
                }
            } else if (type == 6)//Z重要目标
            {
                det.put("num", one.getString("ID"));
                det.put("y", one.getString("DZZBY"));
                det.put("x", one.getString("DZZBX"));
                det.put("dzid", one.getString("BZDZ_ID"));
                int zt = one.getInteger("DZZT");
                String dzzt = "";
                if (zt == 2) {
                    dzzt = "无效";
                } else {
                    dzzt = "有效";
                }


                det.put("mc", dzzt + "-" + one.getString("DZMC") + "[" + one.getString("DZBM") + "]");
            } else if (type == 1)//单位精确查询
            {
                det.put("num", one.getString("JGBH"));
                det.put("y", one.getString("YZB"));
                det.put("x", one.getString("XZB"));
                det.put("dzid", one.getString("DZID"));
                String zt = one.getString("YYZT");
                String dzzt = "";
                if (zt.equals("00")) {
                    dzzt = "营业";
                } else if (zt.equals("01")) {
                    dzzt = "注销";
                } else if (zt.equals("03")) {
                    dzzt = "停业";
                } else {
                    dzzt = "";
                }
                String bm = "";

                try {
                    bm = one.getString("SJJYMC");
                    if (bm == null) {
                        bm = "";
                    }
                } catch (Exception ex) {

                }
                String dz = "";

                try {
                    dz = one.getString("DWDZMC");
                    if (dz == null) {
                        dz = "";
                    }
                } catch (Exception ex) {

                }

                det.put("mc", dzzt + "-" + one.getString("DWMC") + "[" + bm + " 地址:" + dz + "]");
                logger.warn(det.toString());
            }
            back.add(det);

        }


        return back;
    }


    static String StringToURL(String s) {

        s = s.replace("（", "").replace("(", "").replace("）", "").replace(")", "");

        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);

            byte[] b;
            try {
                b = String.valueOf(c).getBytes("utf-8");
            } catch (Exception ex) {
                System.out.println(ex);
                b = new byte[0];
            }
            for (int j = 0; j < b.length; j++) {
                int k = b[j];
                if (k < 0) k += 256;
                sb.append("%" + Integer.toHexString(k).toUpperCase());
            }

        }
        return sb.toString();

    }

    public static JSONObject searchJQNoAddress(JSONObject data) {
        OracleModelHelper ora = null;
        JSONObject back = ErrNo.set(0);
        int page = 1;
        int limit = 20;
        String unit = "";
        String sql = " and 1=1 ";
        String start_time = "";
        String end_time = "";
        String cjlb = " and 1=1 ";
        String jjbh = " and 1=1 ";
        String fscs = "";
        int isMark = 99;
        String markSql = " and 1=1 ";
        String hczt = "";
        String mg = "";
        String dmdm = "";
        String jgbh = "";
        String gmsfhm = "";
        OracleHelper ora_gl = null;
        OracleHelper ora_bt = null;
        String hcsj_start = "";
        String hcsj_end = "";
        String bjfs = "";
        String jqs = "";
        int isExp = 0;
        int file_id = -1;

        String hyxl = "";
        String bq = "";
        String xq = "";
        int isSat = 0;
        String cjnr = "";
        String searchType = "1";

        try {
            ora = OracleModelPool.getModel();
            ora_gl = new OracleHelper("ora_gl");
            ora_bt = new OracleHelper("ora_bt");

            if (data.containsKey("searchType") && data.getString("searchType").length() > 0) {
                searchType = data.getString("searchType");
            }

            if (data.containsKey("isSat") && data.getString("isSat").length() > 0) {
                isSat = data.getInteger("isSat");

            }
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
                JSONObject done = RIUtil.dicts.get(unit);

                // logger.warn(done.toString());
                String type = "";
                try {
                    type = done.getString("type");
                } catch (Exception ex) {
                    type = "21";
                }
                String father_id = done.getString("father_id");
                if (father_id.length() < 5) {
                    father_id = done.getString("id");
                }
                String id = done.getString("id");


                //同时可查该派出所所属分局的巡特警大队的警情，溧阳倒数第五，六位是28 常州市是16

                if (type.equals("23")) {
                    sql = sql + " and (CHJDW_GAJGJGDM like '" + unit.substring(0, 6) + "%') ";
                } else if (type.equals("25") || type.equals("26")) {
                    if (isSat == 1 && type.equals("25")) {
                        sql = sql + " and (CHJDW_GAJGJGDM like '%" + unit.substring(0, 8) + "%') ";
                    } else if (isSat == 1 && type.equals("26")) {
                        sql = sql + " and (ZRQ='" + unit + "') ";
                    } else {
                        sql = sql + " and (CHJDW_GAJGJGDM like '%" + unit.substring(0, 8) + "%' or " +
                                "CHJDW_GAJGJGDM" + " like " + "'" + unit.substring(0, 6) + "16" + "%' or " + "CHJDW_GAJGJGDM "
                                + "like '" + unit.substring(0, 6) + "28" + "%') ";
                    }
                } else if (type.equals("24")) {
                    sql = sql + " and CHJDW_GAJGJGDM like '" + father_id.substring(0, 6) + "%' ";
                } else if (type.equals("28")) {
                    unit = unit.substring(0, 6);
                    sql = sql + " and CHJDW_GAJGJGDM like '" + unit + "%' ";
                }
            }

            if (data.containsKey("hczt") && data.getString("hczt").length() > 0) {
                hczt = data.getString("hczt");
                sql = sql + " and HCZT='" + hczt + "' ";
            }

            if (data.containsKey("xq") && data.getString("xq").length() > 0) {
                xq = data.getString("xq");
                sql = sql + " and xq='" + xq + "' ";
            }
            if (data.containsKey("bq") && data.getString("bq").length() > 0) {
                bq = data.getString("bq").replace(",", "','");
                sql = sql + " and BQ in('" + bq + "') ";
            }
            if (data.containsKey("hyxl") && data.getString("hyxl").length() > 0) {
                hyxl = data.getString("hyxl");
                sql = sql + " and HYXL ='" + hyxl + "' ";
            }

            if (data.containsKey("jqs") && data.getString("jqs").length() > 0) {
                jqs = data.getString("jqs");
                String newJqs = "";
                String[] jqsArr = jqs.split(",");
                for (String s : jqsArr) {
                    s = "'" + s + "'";
                    newJqs += s + ",";
                }
                newJqs = newJqs.substring(0, newJqs.length() - 1);
                jqs = newJqs;
                sql = sql + " and JJBH in(" + jqs + ") ";
            }

            if (data.containsKey("hcsj_start") && data.getString("hcsj_start").length() > 0) {
                hcsj_start = data.getString("hcsj_start");
                sql = sql + " and HCSJ>='" + hcsj_start + "' ";
            }
            if (data.containsKey("hcsj_end") && data.getString("hcsj_end").length() > 0) {
                hcsj_end = data.getString("hcsj_end");
                sql = sql + " and HCSJ<='" + hcsj_end + "' ";
            }
            if (data.containsKey("gmsfhm") && data.getString("gmsfhm").length() > 0) {
                gmsfhm = data.getString("gmsfhm");
                sql = sql + " and SJRY='%" + gmsfhm + "%' ";
            }
            if (data.containsKey("mg") && data.getString("mg").length() > 0) {
                mg = data.getString("mg");
                sql = sql + " and MG='" + mg + "' ";
            }
            if (data.containsKey("jgbh") && data.getString("jgbh").length() > 0) {
                jgbh = data.getString("jgbh");
                sql = sql + " and JGBH='" + jgbh + "' ";
            }
            if (data.containsKey("type") && data.getString("type").length() > 0) {
                String type = data.getString("type");
                sql = sql + " and TYPE='" + type + "' ";
            }
            if (data.containsKey("bjfs") && data.getString("bjfs").length() > 0) {
                bjfs = data.getString("bjfs");
                sql = sql + " and BJFS in (" + bjfs + ") ";
            }
            if (data.containsKey("cjnr") && data.getString("cjnr").length() > 0) {
                cjnr = data.getString("cjnr");
                sql = sql + " and CLJG like '%" + cjnr + "%' ";
            }

            if (data.containsKey("dmdm") && data.getString("dmdm").length() > 0) {
                dmdm = data.getString("dmdm");


                sql = "select DSDZID from CZQJ_YBDS.ADDRESS_info where dmdm='" + dmdm + "'";

                String dz = "";
                List<JSONObject> dzs = ora_gl.query(sql);
                if (dzs.size() > 0) {
                    sql = "";
                    for (int i = 0; i < dzs.size(); i++) {
                        System.out.println(dzs.get(i));
                        dz = dz + " dzxx_xxzjbh='" + dzs.get(i).getString("DSDZID") + "' or ";
                    }
                    sql = sql + " and (" + dz.substring(0, dz.length() - 3) + ") ";
                }
            }

            if (data.containsKey("end_time") && data.getString("end_time").length() > 0) {
                end_time = data.getString("end_time") + " 23:59:59";
            } else {
                end_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            }
            if (data.containsKey("start_time") && data.getString("start_time").length() > 0) {
                start_time = data.getString("start_time") + " 00:00:00";
            } else {
                start_time = RIUtil.GetNextDateTime(end_time, -30);
            }

            if (data.containsKey("jjbh") && data.getString("jjbh").length() > 0) {
                jjbh = data.getString("jjbh");

                if (jjbh.startsWith("J") && jjbh.length() == 20) {
                    jjbh = " and JJBH = '" + jjbh + "' ";
                } else {
                    jjbh = " and JJBH like '%" + jjbh + "%' ";
                }

            }
            if (data.containsKey("isMark") && data.getString("isMark").length() > 0) {
                isMark = data.getInteger("isMark");
            }
            String cjlbSql = "";

            if (data.containsKey("cjlb") && data.getString("cjlb").length() > 0) {
                cjlb = data.getString("cjlb");
                if (cjlb.startsWith("50-") || cjlb.startsWith("51-")) {
                    String[] lbs = cjlb.split(",");
                    String sq = "";
                    for (int i = 0; i < lbs.length; i++) {
                        String lb = lbs[i];
                        if (lb.endsWith("000000")) {
                            sq = sq + " type50='" + lb + "' or ";
                        } else if (lb.endsWith("0000")) {
                            sq = sq + " type52='" + lb + "' or ";
                        } else if (lb.endsWith("00")) {
                            sq = sq + " type54='" + lb + "' or ";
                        } else {
                            sq = sq + " CJLB='" + lb + "' or ";
                        }
                    }


                    cjlbSql = " and (" + sq.substring(0, sq.length() - 3) + ") ";

                } else {

                    isMark = -1;
                    if (cjlb.equals("MG")) {
                        cjlbSql = cjlbSql + " and MG=1 ";
                    } else if (cjlb.equals("WFFZ")) {
                        cjlbSql = cjlbSql + " and CJLB LIKE '50-01%'";
                    } else if (cjlb.equals("ZA")) {
                        cjlbSql = cjlbSql + " and CJLB LIKE '50-02%'";
                    } else if (cjlb.equals("RSDQ")) {
                        cjlbSql = cjlbSql + " and (CJLB= '50-020208' OR CJLB='50-011701')";
                    } else if (cjlb.equals("DQDDC")) {
                        cjlbSql = cjlbSql + " and (CJLB= '50-020209' OR CJLB='50-011709')";
                    } else if (cjlb.equals("HZ")) {
                        cjlbSql = cjlbSql + " and CJLB LIKE '50-04%'";
                    } else if (cjlb.equals("DZ")) {
                        cjlbSql = cjlbSql + "  and (CJLB= '50-011805' OR CJLB='50-011806' OR CJLB='50-011807')";
                    } else if (cjlb.equals("SC")) {
                        cjlbSql = cjlbSql + "  and (CJLB LIKE  '50-0121%' OR CJLB='50-0217%')";
                    } else if (cjlb.equals("SD")) {
                        cjlbSql = cjlbSql + " and CJLB LIKE '50-0215%'";
                    } else if (cjlb.equals("CD")) {
                        cjlbSql = cjlbSql + "  and (CJLB LIKE  '50-0121%' OR CJLB like '50-0217%' or CJLB LIKE " +
                                "'50-0215%')";
                    } else if (cjlb.equals("YX")) {

                    } else if (cjlb.equals("QZ")) {
                        cjlbSql = cjlbSql + " and CJLB LIKE '50-05%'";
                    } else if (cjlb.equals("JB")) {
                        cjlbSql = cjlbSql + " and CJLB LIKE '50-06%'";
                    } else if (cjlb.equals("JF")) {
                        cjlbSql = cjlbSql + " and CJLB LIKE '50-08%'";
                    }
                }

            }
            if (data.containsKey("fscs") && data.getString("fscs").length() > 0) {
                fscs = data.getString("fscs");
                fscs = " and  CJDZ_DZMC like '%" + fscs + "%' ";

            }

            if (data.containsKey("isExp")) {
                isExp = data.getInteger("isExp");
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            if (isMark >= 0) {
                if (isMark == 0) {
                    markSql = " and (  SFGLDZ_PDBZ is null or SFGLDZ_PDBZ=0 )  ";
                } else if (isMark == 1) {
                    markSql = " and SFGLDZ_PDBZ=1  ";
                } else if (isMark == 3) {
                    markSql =
                            " and (  SFGLDZ_PDBZ is null or SFGLDZ_PDBZ=0 )  AND  CJSJ01 < to_char(SYSDATE - 7, " + "'YYYY" + "-MM-DD HH24:MM:SS')  ";
                } else if (isMark == 2) {
                    markSql = " and SFGLDZ_PDBZ=2  ";
                } else if (isMark == 4) {
                    markSql = " and SFGLDZ_PDBZ=4  ";
                } else if (isMark == 5) {
                    markSql = " and SFGLDZ_PDBZ=1 and (SUBSTR(CHJDW_GAJGJGDM,1,8)!=SUBSTR(ZRQ,1,8) and (SUBSTR" +
                            "(CHJDW_GAJGJGDM,6,2)!=16 OR SUBSTR(CHJDW_GAJGJGDM,6,2)!=28)) ";
                }
            }
            if (data.containsKey("page") && data.getString("page").length() > 0) {
                page = data.getInteger("page");
            }

            String sqls = "select JJBH,CJSJ01,CJLB,HCZT,SFGLDZ_PDBZ,CHJDW_GAJGJGDM,SFDD,CJDZ_DZMC,JJSJ," + "BJNR," +
                    "TYPE,CLJG,ZRQ," + "SSXXQK, LAT, LNG,BJLX,JGBH " + "from" + " HL" + ".DSJ_JQ" + " where" + " 1=1 " + " " + markSql + sql + " and " + "(CJSJ01" + ">='" + start_time + "' " + "and " + "CJSJ01<='" + end_time + "')  " + "" + jjbh + cjlbSql + fscs + " order " + "by cjsj01 desc ";

            if (isExp != 1) {
                sqls += " OFFSET" + " " + (page - 1) * limit + " " + "ROWS FETCH NEXT" + " " + limit + " " + "ROWS " + "ONLY";
            }
            logger.warn(sqls);
            List<JSONObject> list = ora.query(sqls);
            List<JSONObject> realList = RealJQINfoList(list, ora);
            if (list.size() > 0) {
//                for (JSONObject jsonObject : realList) {
//                    if (jsonObject.containsKey("JGBH") && !ObjectUtil.isEmpty(jsonObject.get("JGBH"))){
//                        String s = "select * from JWRY_DBA.CZJG_JBXX where JGBH = '"+jsonObject.getString("JGBH")+"'";
//                        List<JSONObject> query = ora_bt.query(s);
//                        if (!query.isEmpty()){
//                            jsonObject.put("DWMC",query.get(0).getString("DWMC")+"["+query.get(0).getString
//                            ("SJJYMC")+"]");
//                            jsonObject.put("TYXYDM",jsonObject.getString("TYXYDM"));
//                        }
//                    }
//                }
                back.put("data", realList);
                //back.put("data", list);
            } else {
                back.put("data", new JSONArray());
            }
            sqls =
                    "select count(JJBH) as count from HL.DSJ_JQ where 1=1  " + markSql + sql + " and " + "(CJSJ01>='" + start_time + "' and CJSJ01<='" + end_time + "')  " + jjbh + cjlbSql + fscs + "";
            logger.warn(sqls);
            int count = ora.query_count(sqls);
            back.put("count", count);

            if (isExp == 1) {
                JSONObject excel = new JSONObject();
                excel.put("files", realList);
                logger.warn("开始导出");
                if (data.containsKey("isDing") && data.getString("isDing").length() > 0) {
                    file_id = ExportThree(excel.getJSONArray("files"), searchType);
                } else {
                    file_id = ExportTwo(excel.getJSONArray("files"), searchType);

                }
            }
            back.put("file_id", file_id);
            return back;

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));

        } finally {
            OracleModelPool.putModel(ora);
            ora_gl.close();
        }


    }


    private static int ExportTwo(JSONArray datas, String searchType) {

        //处理数据，把处警单位和处警类别提出来
        for (int i = 0; i < datas.size(); i++) {
            JSONObject one = datas.getJSONObject(i);
            //logger.warn(one.toString());

            String CJLX = "";
            String CJDW = "";
            String SSXXQK = one.getString("SSXXQK");

            try {
                CJLX = one.getJSONObject("CJLB").getString("dict_name");
            } catch (Exception ex) {
            }
            try {
                CJDW = one.getJSONObject("CHJDW_GAJGJGDM").getString("dict_name");
            } catch (Exception ex) {

            }
            String HCZT_DM = one.getString("HCZT");
            String HCZT = "";
            switch (HCZT_DM) {
                case "1":
                    HCZT = "已核查";
                    break;
                case "2":
                    HCZT = "未核查";
                    break;
                case "3":
                    HCZT = "已核查未通过";
                    break;
                case "4":
                    HCZT = "已核查待整改";
                    break;
                default:
                    HCZT = "";
            }
            String SFGL_DM = one.getString("SFGLDZ_PDBZ");
            String SFGL = "";
            switch (SFGL_DM) {
                case "1":
                    SFGL = "已关联";
                    break;
                case "3":
                    SFGL = "七天未关联";
                    break;
                case "2":
                    SFGL = "无需关联";
                    break;
                default:
                    SFGL = "未关联";
            }

            one.put("CJDW", CJDW);
            one.put("CJLX", CJLX);
            one.put("HCZT", HCZT);
            one.put("SFGL", SFGL);
            one.put("SSXXQK", SSXXQK);
        }

        String FileName = "警情" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        header.add("JJBH");
        headername.put("JJBH", "接警编号");
        header.add("CJDW");
        headername.put("CJDW", "处警单位");
        header.add("CJSJ01");
        headername.put("CJSJ01", "处警时间");
        header.add("CJLX");
        headername.put("CJLX", "处警类型");
        header.add("CLJG");
        headername.put("CLJG", "处理结果");
        header.add("CJDZ_DZMC");
        headername.put("CJDZ_DZMC", "发生地点");
        header.add("HCZT");
        headername.put("HCZT", "核查状态");
        header.add("SFGL");
        headername.put("SFGL", "是否关联");
//        header.add("DWMC");
//        headername.put("DWMC", "单位名称");
//        header.add("TYXYDM");
//        headername.put("TYXYDM", "统一信用代码");
        logger.warn(searchType);
        if ("4".equals(searchType)) {
            header.add("SSXXQK");
            headername.put("SSXXQK", "损失情况");
        }


        ExportInterface exporthelper = null;

        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) +
                            "/";
            String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;


            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
//            for (int i = 0; i < datas.size(); i++) {
//                JSONObject jsonObject = datas.getJSONObject(i);
//                JSONArray jsonArray = jsonObject.getJSONArray("real_all");
            exporthelper.write_data(datas);
//            }

            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql = "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath +
                        "','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");

                logger.warn(endPath + "-->" + String.valueOf(new File(endPath).length()));
                logger.warn("id->" + id);

                if (exporthelper != null) {
                    try {
                        exporthelper.close();
                        exporthelper = null;
                    } catch (Exception ex) {

                    }
                }
               /* BashExecutor bash = new BashExecutor();
                String cmd = "cp -r " + endPath + " " + TNOAConf.get("file", "bk_path");
                logger.warn(cmd);
                bash.exec(cmd, -1, true);*/
                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
                logger.warn(obsFileName + "-->" + ret);
                return id;
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return 0;
        } finally {

            try {
                if (exporthelper != null) {
                    try {
                        exporthelper.close();
                    } catch (Exception ex) {

                    }
                }
            } catch (Exception ex) {

            }
        }
    }

    private static int ExportThree(JSONArray datas, String searchType) {

        //处理数据，把处警单位和处警类别提出来
        for (int i = 0; i < datas.size(); i++) {
            JSONObject one = datas.getJSONObject(i);
            //logger.warn(one.toString());

            String CJLX = "";
            String CJDW = "";
            String SSXXQK = one.getString("SSXXQK");

            try {
                CJLX = one.getJSONObject("CJLB").getString("dict_name");
            } catch (Exception ex) {
            }
            try {
                CJDW = one.getJSONObject("CHJDW_GAJGJGDM").getString("dict_name");
            } catch (Exception ex) {

            }
            String HCZT_DM = one.getString("HCZT");
            String HCZT = "";
            switch (HCZT_DM) {
                case "1":
                    HCZT = "已核查";
                    break;
                case "2":
                    HCZT = "未核查";
                    break;
                case "3":
                    HCZT = "已核查未通过";
                    break;
                case "4":
                    HCZT = "已核查待整改";
                    break;
                default:
                    HCZT = "";
            }
            String SFGL_DM = one.getString("SFGLDZ_PDBZ");
            String SFGL = "";
            switch (SFGL_DM) {
                case "1":
                    SFGL = "已关联";
                    break;
                case "3":
                    SFGL = "七天未关联";
                    break;
                case "2":
                    SFGL = "无需关联";
                    break;
                default:
                    SFGL = "未关联";
            }

            one.put("CJDW", CJDW);
            one.put("CJLX", CJLX);
            one.put("HCZT", HCZT);
            one.put("SFGL", SFGL);
            one.put("SSXXQK", SSXXQK);
        }

        String FileName = "警情" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        File file = new File(TNOAConf.get("file", "img_path") + FileName);
        List<String> header = new ArrayList<>();
        HashMap<String, String> headername = new HashMap<>();
        header.add("JJBH");
        headername.put("JJBH", "接警编号");
        header.add("CJDW");
        headername.put("CJDW", "处警单位");
        header.add("CJSJ01");
        headername.put("CJSJ01", "处警时间");
        header.add("CJLX");
        headername.put("CJLX", "处警类型");
        header.add("CLJG");
        headername.put("CLJG", "处理结果");
        header.add("CJDZ_DZMC");
        headername.put("CJDZ_DZMC", "发生地点");
        header.add("HCZT");
        headername.put("HCZT", "核查状态");
        header.add("SFGL");
        headername.put("SFGL", "是否关联");
        logger.warn(searchType);
        if ("4".equals(searchType)) {
            header.add("SSXXQK");
            headername.put("SSXXQK", "损失情况");
        }


        ExportInterface exporthelper = null;

        logger.warn(String.valueOf(datas));
        try {
            String filePath =
                    new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) +
                            "/";
            String endPath = TNOAConf.get("file", "img_path") + filePath + FileName;


            exporthelper = new ExportXlsxHelper();
            exporthelper.init(TNOAConf.get("file", "img_path") + filePath + FileName);
            exporthelper.write_head(header, headername);
//            for (int i = 0; i < datas.size(); i++) {
//                JSONObject jsonObject = datas.getJSONObject(i);
//                JSONArray jsonArray = jsonObject.getJSONArray("real_all");
            exporthelper.write_data(datas);
//            }

            InfoModelHelper info = InfoModelPool.getModel();
            try {
                String sql = "insert into upload (nas_id,file_path,file_name,type) values" + " ('1','" + filePath +
                        "','" + FileName + "',999)";
                info.update(sql);
                sql = "select * from upload where file_name='" + FileName + "'";
                List<JSONObject> result = info.query(sql);
                JSONObject xJsonObject = result.get(0);
                int id = xJsonObject.getIntValue("id");
                try {
                    if (exporthelper != null) {
                        try {
                            exporthelper.close();
                        } catch (Exception ex) {

                        }

                    }
                } catch (Exception ex) {

                }


                logger.warn("id->" + id);
               /* BashExecutor bash = new BashExecutor();
                String cmd = "cp -r " + endPath + " " + TNOAConf.get("file", "bk_path");
                logger.warn(cmd);
                bash.exec(cmd, -1, true);*/
                String endPoint = "http://10.34.251.34:50101";
                String ak = "Q7D3OUVIRBEZEB4RWVSJ";
                String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
                String bucketName = "obs-qjjc-tyyh";
                ObsServer obsServ = new ObsServer();
                String obsFileName = "hl/" + filePath + FileName;
                System.out.println(obsFileName);
                boolean ret = obsServ.upload(endPoint, ak, sk, bucketName, obsFileName, endPath);
                logger.warn(obsFileName + "-->" + ret);
                return id;
            } finally {
                InfoModelPool.putModel(info);
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return 0;
        } finally {

            try {
                if (exporthelper != null) {
                    try {
                        exporthelper.close();
                    } catch (Exception ex) {

                    }
                }
            } catch (Exception ex) {

            }
        }
    }

    private static List<JSONObject> RealJQINfoList(List<JSONObject> list, OracleModelHelper ora) throws Exception {
        List<JSONObject> back = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {

            JSONObject one = list.get(i);
            String CJLB = one.getString("CJLB");

            String CHJDW_GAJGJGDM = one.getString("CHJDW_GAJGJGDM").substring(0, 8) + "0000";
            try {
                String zrq = one.getString("ZRQ").substring(0, 8);
                String dw = CHJDW_GAJGJGDM.substring(0, 8);
                String cjbm = dw.substring(6, 8);

                if (!zrq.equals(dw) && (!cjbm.equals("16") || !cjbm.equals("28"))) {
                    one.put("isFBS", 1);
                } else {
                    one.put("isFBS", 0);
                }

            } catch (Exception ex) {
                one.put("isFBS", 0);
            }
            one.put("CJLB", RIUtil.dicts.get(CJLB));

            JSONObject cjdw = RIUtil.dicts.get(CHJDW_GAJGJGDM);
            try {
                cjdw.remove("dets");
            } catch (Exception e) {

            }

            one.put("CHJDW_GAJGJGDM", cjdw);


            back.add(one);
        }
        return back;
    }


    private List<JSONObject> RealJQINfo(List<JSONObject> list, OracleModelHelper ora) throws Exception {
        List<JSONObject> back = new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {

            JSONObject one = list.get(i);
            String CJLB = one.getString("CJLB");
            String BJLX = one.getString("BJLX");
            String CHJDW_GAJGJGDM = one.getString("CHJDW_GAJGJGDM");
            String JJDW_GAJGJGDM = one.getString("JJDW_GAJGJGDM");
            String TYPE = one.getString("TYPE");
            one.put("CJLB", RIUtil.dicts.get(CJLB));
            one.put("BJLX", RIUtil.dicts.get(BJLX));


            String labels = one.getString("LABEL").replace("\\|", ",");
            if (labels != null && labels.length() > 0) {
                logger.warn(labels);
                one.put("LABEL", RIUtil.RealDictNameList(RIUtil.StringToList(labels)));
            } else {
                one.put("LABEL", new JSONArray());
            }


            JSONObject cjdw = RIUtil.dicts.get(CHJDW_GAJGJGDM);
            try {
                cjdw.remove("dets");
            } catch (Exception e) {

            }
            JSONObject jjdw = RIUtil.dicts.get(JJDW_GAJGJGDM);
            try {
                jjdw.remove("dets");
            } catch (Exception ex) {

            }
            one.put("CHJDW_GAJGJGDM", cjdw);
            one.put("JJDW_GAJGJGDM", jjdw);

            String sql = "select * from JQ_SJRY WHERE JJBH='" + one.getString("JJBH") + "' and DELETED=0";
            List<JSONObject> ll = ora.query(sql);
            if (ll.size() > 0) {
                one.put("SJRY", RelaInfoRY(ll));
            } else {
                one.put("SJRY", new JSONArray());
            }
            OracleHelper ora_gl = null;
            OracleHelper ora_bt = null;
            int fb = 0;
            try {
                ora_gl = new OracleHelper("ora_gl");
                ora_bt = new OracleHelper("ora_bt");
                if (one.containsKey("JGBH") && one.getString("JGBH").length() > 0) {
                    String jgbh = one.getString("JGBH");
                    String dwmc = "";

                    if (TYPE.equals("1")) { //单位 ora_bk_bt
//                        sql = "select DWMC from HL.CZJG_JBXX where JGBH='" + jgbh + "'";
                        sql = "select DWMC,SJJYMC from JWRY_DBA.CZJG_JBXX where JGBH='" + jgbh + "'";
                        logger.warn(sql);
                        dwmc = ora_bt.query_one(sql, "DWMC");
                        String sjjymc = "";
                        String yyzt = "";
                        try {
                            sjjymc = ora_bt.query_one(sql, "SJJYMC");
                            //   yyzt= ora_bt.query_one(sql,"YYZT");
                            logger.warn(sjjymc);
                            if (sjjymc == null) {
                                sjjymc = "";
                            }

                        } catch (Exception ex) {

                        }
                        if (sjjymc != null & sjjymc.length() > 1) {
                            dwmc = dwmc + "[" + sjjymc + "]";
                        }


                    } else if (TYPE.equals("3")) { //建筑工地 ora_bk_bt
                        sql = "select GDMC from JWRY_DBA.CZJG_JZGD_JBXX where JGBH = '" + jgbh + "' ";
                        dwmc = ora_bt.query_one(sql, "GDMC");
                    } else if (TYPE.equals("4") || TYPE.equals("5")) { //重要目标 桥梁 ora_bk_bt
                        sql = "select ZYMBQC from JWRY_DBA.CZJG_ZYMB_JBXX where JGBH = '" + jgbh + "'";
                        dwmc = ora_bt.query_one(sql, "ZYMBQC");
                    } else {// ora_bk_gl
                        sql = "select DZMC,DZBM from CZQJ_YBDS.ADDRESS_FB_INFO where ID = '" + jgbh + "' ";
                        dwmc = ora_gl.query_one(sql, "DZMC");
                        String sjjymc = "";
                        String dzzt = "";
                        try {
                            sjjymc = ora_gl.query_one(sql, "DZBM");
                            //dzzt= ora_gl.query_one(sql,"DZZT");
                            logger.warn(sjjymc);
                            if (sjjymc == null) {
                                sjjymc = "";
                            }

                        } catch (Exception ex) {

                        }
                        if (sjjymc.length() > 1) {
                            dwmc = dwmc + "[" + sjjymc + "]";
                        }

                        fb = 1;
                    }
                    logger.warn(sql);
                    logger.warn(dwmc);
                    //如果都查不到去标准地址里查
                    if (StringUtils.isNullOrEmpty(dwmc)) {
                        sql = "select DZ from CZQJ_YBDS.ADDRESS_INFO where DZID='" + jgbh + "'";
                        dwmc = ora_gl.query_one(sql, "DZ");
                    }
                    one.put("DWMC", dwmc);
                } else {
                    one.put("DWMC", "");
                }
            } catch (Exception ex) {
                logger.warn(Lib.getTrace(ex));
                one.put("DWMC", "");
            } finally {
                ora_gl.close();
                ora_bt.close();
            }

            if (one.containsKey("DZXX_XXZJBH") && one.getString("DZXX_XXZJBH").length() > 0) {
                String dzxxbh = one.getString("DZXX_XXZJBH");
                try {
                    ora_gl = new OracleHelper("ora_gl");
                    System.out.println(ora_gl);
                    sql = "select DZ,DZID, DZZBX,DZZBY from CZQJ_YBDS.ADDRESS_INFO where DSDZID='" + dzxxbh + "'";
                    logger.warn(sql);
                    String DZ = ora_gl.query_one(sql, "DZ");
                    String DZID = ora_gl.query_one(sql, "DZID");
                    one.put("DZMC", DZ);
                    one.put("DZID", DZID);
                    if (fb == 1) {
                        one.put("DZZBX", ora_gl.query_one(sql, "DZZBX"));
                        one.put("DZZBY", ora_gl.query_one(sql, "DZZBY"));
                    }
                } catch (Exception ex) {
                    logger.warn(Lib.getTrace(ex));
                    one.put("DZMC", "");
                    one.put("DZID", "");
                } finally {
                    ora_gl.close();
                }
            } else {
                one.put("DZMC", "");
                one.put("DZID", "");
            }

            sql = "select * from JQ_SH_HIS where JJBH='" + one.getString("JJBH") + "'";
            List<JSONObject> hcs = ora.query(sql);
            if (hcs.size() > 0) {
                one.put("HC_HIS", RelaHc(hcs));
            } else {
                one.put("HC_HIS", new ArrayList<>());
            }


            if (one.containsKey("BQ") && one.getString("BQ").length() > 0) {
                // System.out.println(RIUtil.dicts.get(one.getString("BQ")));
                one.put("BQ", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("BQ"))));
            } else {
                one.put("BQ", new JSONArray());
            }


            if (one.getIntValue("mark") == 0) {

                //地址 -》事发场所 74
                List<String> addressMs = new ArrayList<>();
                try {

                    String sfcs = one.getString("CJSFCS");
                    sfcs = RIUtil.dicts.get("74-" + sfcs).getString("dict_name").replace("其他", "");

                    String id = GetDictIdByNameType(13, sfcs);
                    addressMs.add(id);
                } catch (Exception ex) {

                }
//发生原因 77
                List<String> reasonM = new ArrayList<>();
                try {

                    String fsyy = one.getString("FSYY");
                    if (fsyy != null && fsyy.length() > 0) {

                        if (fsyy.startsWith("0")) {
                            fsyy = fsyy.substring(1);
                        }
                        fsyy = RIUtil.dicts.get("77-" + fsyy).getString("dict_name").replace("其他", "");
                        System.out.println(fsyy);

                        String id = GetDictIdByNameType(18, fsyy);
                        reasonM.add(id);
                    }
                } catch (Exception ex) {
                    System.out.println(one);
                    System.out.println(Lib.getTrace(ex));
                }
//手段514
                List<String> toolM = new ArrayList<>();
                try {


                    String cjlbN = RIUtil.dicts.get(CJLB).getString("dict_name").replace("其他", "");
                    logger.warn(cjlbN);

                    String id = GetDictIdByNameType(19, cjlbN);
                    if (id == null || id.length() == 0) {
                        String cjfid = RIUtil.dicts.get(CJLB).getString("father_id");
                        String cjfidName = RIUtil.dicts.get(cjfid).getString("dict_name");
                        logger.warn(cjfidName);
                        id = GetDictIdByNameType(19, cjfidName);
                    }
                    toolM.add(id);


                } catch (Exception ex) {
                    System.out.println(Lib.getTrace(ex));

                }

                //
                String id = "33827D2519694C74B32F06418175E42B";
                try {
                    String cjsj = one.getString("CJSJ01");

                    try {
                        String hh = cjsj.substring(8, 10);
                        int h = Integer.parseInt(hh);

                        if (h < 6) {
                            id = "D8480F9C31754C51B40C6F21A36ADB6F";
                        } else if (h >= 6 && h < 12) {
                            id = "DE5156E24F6F4512AB0D575D2FDB1C99";
                        } else if (h >= 12 && h < 18) {
                            id = "C4F9800366AC41C2BB8D49F80E84FC4C";
                        } else if (h >= 18 && h < 24) {
                            id = "4B820D3577EC42A6BF67DD1E8F52CAD6";
                        } else {
                            id = "33827D2519694C74B32F06418175E42B";
                        }
                    } catch (Exception ex) {

                    }


                } catch (Exception ex) {

                }

                one.put("ADDRESSM", addressMs);
                one.put("REASONM", reasonM);
                one.put("TOOLM", toolM);
                one.put("TIMEM", id);

                System.out.println(one);

            }


            if (one.containsKey("ADDRESSM") && one.getString("ADDRESSM") != null && one.getString("ADDRESSM").length() > 0) {
                try {
                    String addMs = one.getString("ADDRESSM");

                    one.put("_addressM", RIUtil.RealDictNameList(RIUtil.StringToList(addMs)));
                    one.put("addressM", RIUtil.HashToList(RIUtil.StringToList(one.getString("ADDRESSM"))));
                } catch (Exception ex) {
                    one.put("_addressM", new JSONArray());
                    one.put("addressM", new ArrayList<>());
                }
            } else {
                one.put("_addressM", new JSONArray());
                one.put("addressM", new ArrayList<>());
            }

            if (one.containsKey("RESULTM") && one.getString("RESULTM") != null && one.getString("RESULTM").length() > 0) {
                try {
                    one.put("_resultM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("RESULTM"))));
                    one.put("resultM", RIUtil.HashToList(RIUtil.StringToList(one.getString("RESULTM"))));
                } catch (Exception ex) {
                    one.put("_resultM", new JSONArray());
                    one.put("resultM", new ArrayList<>());
                }
            } else {
                one.put("_resultM", new JSONArray());
                one.put("resultM", new ArrayList<>());
            }
            if (one.containsKey("TIMEM") && one.getString("TIMEM") != null && one.getString("TIMEM").length() > 0) {
                try {
                    one.put("_timeM", RIUtil.dicts.get(one.getString("TIMEM")));

                } catch (Exception ex) {
                    one.put("_timeM", new JSONObject());
                }
            } else {
                one.put("_timeM", new JSONObject());
            }

            if (one.containsKey("TOOLM") && one.getString("TOOLM") != null && one.getString("TOOLM").length() > 0) {
                try {
                    one.put("_toolM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("TOOLM"))));
                    one.put("toolM", RIUtil.HashToList(RIUtil.StringToList(one.getString("TOOLM"))));
                } catch (Exception ex) {
                    one.put("_toolM", new JSONArray());
                    one.put("toolM", new ArrayList<>());
                }
            } else {
                one.put("_toolM", new JSONArray());
                one.put("toolM", new ArrayList<>());
            }
            if (one.containsKey("REASONM") && one.getString("REASONM") != null && one.getString("REASONM").length() > 0) {
                try {
                    one.put("_reasonM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("REASONM"))));
                    one.put("reasonM", RIUtil.HashToList(RIUtil.StringToList(one.getString("REASONM"))));
                } catch (Exception ex) {
                    one.put("_reasonM", new JSONArray());
                    one.put("reasonM", new ArrayList<>());
                }
            } else {
                one.put("_reasonM", new JSONArray());
                one.put("reasonM", new ArrayList<>());
            }
            back.add(one);
        }
        return back;
    }

    private List<JSONObject> RelaHc(List<JSONObject> hcs) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < hcs.size(); i++) {
            JSONObject one = hcs.get(i);
            String opt_user = one.getString("OPT_USER");
            String real_opt_user = one.getString("REAL_OPT_USER");
            String cwyy = one.getString("CWYY");

            one.put("CWYY", RIUtil.RealDictNameList(RIUtil.StringToList(cwyy)));
            one.put("OPT_USER", RIUtil.users.get(opt_user));
            one.put("REAL_OPT_USER", RIUtil.users.get(real_opt_user));

            back.add(one);
        }
        return back;
    }

    private List<JSONObject> RelaInfoRY(List<JSONObject> ll) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < ll.size(); i++) {
            JSONObject one = ll.get(i);
            if (one.containsKey("RYLX") && one.getString("RYLX").length() > 0) {
                one.put("RYLX", RIUtil.dicts.get(one.getString("RYLX")));
            } else {
                one.put("RYLX", new JSONObject());
            }
            if (one.containsKey("RYBQ") && one.getString("RYBQ").length() > 0) {
                one.put("RYBQ", RIUtil.dicts.get(one.getString("RYBQ")));
            } else {
                one.put("RYBQ", new JSONObject());
            }

            //标签
            if (one.containsKey("PERSONM") && one.getString("PERSONM") != null && one.getString("PERSONM").length() > 2) {
                try {
                    one.put("_personM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("PERSONM"))));
                    one.put("personM", RIUtil.HashToList(RIUtil.StringToList(one.getString("PERSONM"))));
                } catch (Exception ex) {
                    one.put("_personM", new JSONArray());
                    one.put("personM", new ArrayList<>());
                }
            } else {
                if (one.containsKey("PERSONMS") && one.getString("PERSONMS") != null && one.getString("PERSONMS").length() > 2) {
                    try {
                        one.put("_personM", RIUtil.RealDictNameList(RIUtil.StringToList(one.getString("PERSONMS"))));
                        one.put("personM", RIUtil.HashToList(RIUtil.StringToList(one.getString("PERSONMS"))));
                    } catch (Exception ex) {
                        one.put("_personM", new JSONArray());
                        one.put("personM", new ArrayList<>());
                    }
                } else {


                    one.put("_personM", RIUtil.RealDictNameList(RIUtil.StringToList("304EC96312434E0BB8D206C5A6E86F42"
                    )));
                    List<String> pM = new ArrayList<>();
                    pM.add("304EC96312434E0BB8D206C5A6E86F42");
                    one.put("personM", pM);
                }
            }
            back.add(one);
        }
        return back;
    }

    public static String GetDictIdByNameType(int type, String dict_name) {

        for (Map.Entry<String, JSONObject> one : RIUtil.dicts.entrySet()) {
            try {
                int t = one.getValue().getIntValue("type");
                String dname = one.getValue().getString("dict_name");
                if (type == t && dname.equals(dict_name)) {
                    return one.getKey();
                }


            } catch (Exception ex) {

            }
        }


        return "";
    }

    public static JSONObject queryOne(String sql, OracleHelper jdbcTemplate) {
        JSONObject back = new JSONObject();
        try {


            List<JSONObject> ret = jdbcTemplate.query(sql);
            if (ret.size() > 0) {

                JSONObject one = ret.get(0);
                for (Map.Entry<String, Object> o : one.entrySet()) {

                    String val = "";
                    try {
                        val = (String) o.getValue();
                        if (val == null) {
                            val = "";
                        }
                    } catch (Exception ex) {

                    }
                    one.put(o.getKey(), val);
                }
                back = one;

            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }
        return back;
    }
}
