package HL.TNOA.Httpd.Controller;


import HL.TNOA.Httpd.javajwt.PassToken;
import HL.TNOA.Httpd.javajwt.TNOAHttpRequest;
import HL.TNOA.Lib.ErrNo;
import HL.TNOA.Lib.InfoModel.InfoModelHelper;
import HL.TNOA.Lib.InfoModel.InfoModelPool;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.RIUtil1;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RestController
public class DingTempController {
    private Logger logger = LoggerFactory.getLogger(getClass().getName());

    //private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @RequestMapping(method = {RequestMethod.POST}, path = {"/ding_temp"})
    @PassToken
    public JSONObject get_ding_temp(TNOAHttpRequest request) throws Exception {
        logger.warn("ding_temp--->" + request.getRequestParams().toString());
        String opt = "";
        JSONObject data = request.getRequestParams();
        if (data.containsKey("opt") && data.getString("opt").length() > 0) {
            opt = data.getString("opt");
            if (opt.equals("get_ding_temp")) {
                return getDingTemp(data);
            } else if (opt.equals("create_ding_temp")) {
                return createDingTemp(data, request.getRemoteAddr());
            } else if (opt.equals("update_ding_temp")) {
                return updateDingTemp(data, request.getRemoteAddr());
            } else if (opt.equals("delete_ding_temp")) {
                return deleteDingTemp(data, request.getRemoteAddr());
            } else if (opt.equals("get_ding_temp_group")) {
                return getDingTempGroup(data);
            } else if (opt.equals("create_ding_temp_group")) {
                return createDingTempGroup(data, request.getRemoteAddr());
            } else if (opt.equals("update_ding_temp_group")) {
                return updateDingTempGroup(data, request.getRemoteAddr());
            } else if (opt.equals("delete_ding_temp_group")) {
                return deleteDingTempGroup(data, request.getRemoteAddr());
            } else {
                return ErrNo.set(null, 2, "OPT错误");
            }
        } else {
            return ErrNo.set(null, 2, "OPT错误");
        }
    }

    //******CREATE*******
    private JSONObject createDingTemp(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String form_config = "";
            String group_id = "";
            String isStop = "";
            String form_name = "";
            String create_user = "";
            String description = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "0";
            if (data.containsKey("form_config") && data.getString("form_config").length() > 0) {
                form_config = data.getString("form_config");
            } else {
                return ErrNo.set(null, 2, "缺少参数：form_config");
            }
            if (data.containsKey("group_id") && data.getString("group_id").length() > 0) {
                group_id = data.getString("group_id");
            } else {
                return ErrNo.set(null, 2, "缺少参数：group_id");
            }
            if (data.containsKey("form_name") && data.getString("form_name").length() > 0) {
                form_name = data.getString("form_name");
            } else {
                return ErrNo.set(null, 2, "缺少参数：form_name");
            }
            if (data.containsKey("description") && data.getString("description").length() > 0) {
                description = data.getString("description");
            } else {
                //return ErrNo.set(null, 2, "缺少参数：form_name");
            }
            if (data.containsKey("isStop") && data.getString("isStop").length() > 0) {
                isStop = data.getString("isStop");
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                return ErrNo.set(null, 2, "缺少参数：opt_user");
            }
            String sqls =
                    "insert ding_temp (form_config,group_id,isStop,create_user,create_time,isdelete,form_name," +
                            "description)" + "values" + "('" + form_config + "','" + group_id + "','" + isStop + "'," +
                            "'" + create_user + "','" + create_time + "','" + isdelete + "','" + form_name + "','" + description + "')";
            mysql.update(sqls);

            sqls = "select id from ding_temp where create_user='" + create_user + "' and create_time='" + create_time + "' and " +
                    "form_name='" + form_name + "'";
            String id = mysql.query_one(sqls, "id");
            back.put("data", id);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private JSONObject getDingTemp(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String form_config = "";
            String group_id = "";
            String isStop = "";
            String create_user = "";
            String create_time_start = "";
            String create_time_end = "";
            String query = "";
            String unit = "";
            if (data.containsKey("unit") && data.getString("unit").length() > 0) {
                unit = data.getString("unit");
            }
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("form_config") && data.getString("form_config").length() > 0) {
                form_config = data.getString("form_config");
                sql = sql + " form_config='" + form_config + "' and ";
            }
            if (data.containsKey("query") && data.getString("query").length() > 0) {
                query = data.getString("query");
                sql = sql + " (form_name like '%" + query + "%' or description like '" + query + "') and ";
            }
            if (data.containsKey("group_id") && data.getString("group_id").length() > 0) {
                group_id = data.getString("group_id");
                sql = sql + " group_id='" + group_id + "' and ";
            }
            if (data.containsKey("isStop") && data.getString("isStop").length() > 0) {
                isStop = data.getString("isStop");
                sql = sql + " isStop='" + isStop + "' and ";
            }
            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' and ";
            }
            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
                create_time_start = data.getString("create_time_start");
                sql = sql + "create_time>='" + create_time_start + "' and ";
            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end");
                sql = sql + "create_time<='" + create_time_end + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select * from ding_temp where 1=1 and " + sql + " isdelete=0  ";
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            InfoModelHelper finalMysql = mysql;
            try {
                if (list.size() > 0) {
                    String finalUnit = unit;
                    list = list.stream().filter(temp ->{
                        String s = "select * from user where id_num = '"+ temp.getString("create_user") +"'";
                        try {
                            List<JSONObject> query1 = finalMysql.query(s);
                            if (query1.size() > 0 && query1.get(0).getString("unit").contains(finalUnit.substring(0,6))){
                                return true;
                            }
                            else {
                                return false;
                            }
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }).collect(Collectors.toList());

                    back.put("count", list.size());
                    list= list.stream().skip((page-1)*limit).limit(limit).
                            collect(Collectors.toList());
                    back.put("data", RelaInfo(list));

                } else {
                    back.put("data", list);
                    back.put("count", 0);
                }
            } finally {
                InfoModelPool.putModel(finalMysql);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);

        }
        return back;
    }

    //*****relainfo************
    private Object RelaInfo(List<JSONObject> list) {
        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            one.put("create_user", RIUtil1.users1.get(one.getString("create_user")));
            back.add(one);
        }
        return back;
    }

    //******UPDATE*******
    private JSONObject updateDingTemp(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String form_config = "";
            String group_id = "";
            String isStop = "";
            String opt_user = "";
            String form_name = "";
            String description = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(null, 2, "缺少参数：id");
            }

            if (data.containsKey("form_config") && data.getString("form_config").length() > 0) {
                form_config = data.getString("form_config");
                sql = sql + " form_config='" + form_config + "' , ";
            }
            if (data.containsKey("form_name") && data.getString("form_name").length() > 0) {
                form_name = data.getString("form_name");
                sql = sql + " form_name='" + form_name + "' , ";
            }
            if (data.containsKey("description")) {
                description = data.getString("description");
                sql = sql + " description='" + description + "' , ";
            }
            if (data.containsKey("group_id") && data.getString("group_id").length() > 0) {
                group_id = data.getString("group_id");
                sql = sql + " group_id='" + group_id + "' , ";
            }
            if (data.containsKey("isStop") && data.getString("isStop").length() > 0) {
                isStop = data.getString("isStop");
                sql = sql + " isStop='" + isStop + "' , ";
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(null, 2, "缺少参数：opt_user");
            }
            String sqls = "update ding_temp set " + sql + " isdelete=0  where id='" + id + "'";
            mysql.update(sqls);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private JSONObject deleteDingTemp(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(null, 2, "缺少参数：id");
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(null, 2, "缺少参数：opt_user");
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update ding_temp set isdelete =1,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

    //******CREATE*******
    private JSONObject createDingTempGroup(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String group_name = "";
            String description = "";
            String create_user = "";
            String create_time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            String isdelete = "0";
            if (data.containsKey("group_name") && data.getString("group_name").length() > 0) {
                group_name = data.getString("group_name");
            } else {
                return ErrNo.set(null, 2, "缺少参数：group_name");
            }
            if (data.containsKey("description") && data.getString("description").length() > 0) {
                description = data.getString("description");
            } else {
                return ErrNo.set(null, 2, "缺少参数：description");
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                create_user = data.getString("opt_user");
            } else {
                return ErrNo.set(null, 2, "缺少参数：opt_user");
            }
            String sqls =
                    "insert ding_temp_group (group_name,description,create_user,create_time,isdelete)values('" + group_name + "','" + description + "','" + create_user + "','" + create_time + "','" + isdelete + "')";
            mysql.update(sqls);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //******GET*******
    private JSONObject getDingTempGroup(JSONObject data) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            int limit = 20;
            int page = 1;
            String id = "";
            String query = "";
            String description = "";
            String create_user = "";
            String create_time_start = "";
            String create_time_end = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
                sql = sql + " id='" + id + "' and ";
            }
            if (data.containsKey("query") && data.getString("query").length() > 0) {
                query = data.getString("query");
                sql = sql + " (group_name like '%" + query + "%' or description like '%" + query + "%') and  ";
            }

            if (data.containsKey("create_user") && data.getString("create_user").length() > 0) {
                create_user = data.getString("create_user");
                sql = sql + " create_user='" + create_user + "' and ";
            }
            if (data.containsKey("create_time_start") && data.getString("create_time_start").length() > 0) {
                create_time_start = data.getString("create_time_start");
                sql = sql + "create_time>='" + create_time_start + "' and ";
            }
            if (data.containsKey("create_time_end") && data.getString("create_time_end").length() > 0) {
                create_time_end = data.getString("create_time_end");
                sql = sql + "create_time<='" + create_time_end + "' and ";
            }
            if (data.containsKey("limit") && data.getString("limit").length() > 0) {
                limit = data.getInteger("limit");
            }
            if (data.containsKey("page") && data.getString("limit").length() > 0) {
                page = data.getInteger("page");
            }
            String sqls =
                    "select * from ding_temp_group where 1=1 and " + sql + " isdelete=0  limit " + limit + " " +
                            "offset " + limit * (page - 1);
            List<JSONObject> list = new ArrayList<>();
            list = mysql.query(sqls);
            if (list.size() > 0) {
                back.put("data", RelaInfoGroup(list, mysql));
                sqls = "select count(id) as count from ding_temp_group where 1=1 and " + sql + " isdelete=0; ";
                back.put("count", mysql.query_count(sqls));
            } else {
                back.put("data", list);
                back.put("count", 0);
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    private Object RelaInfoGroup(List<JSONObject> list, InfoModelHelper mysql) {

        List<JSONObject> back = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONObject one = list.get(i);
            one.put("create_user", RIUtil1.users1.get(one.getString("create_user")));
            String id = one.getString("id");
            List<JSONObject> ll = new ArrayList<>();
            try {
                String sql = "select * from ding_temp where group_id='" + id + "' and isdelete=0;";
                logger.warn(sql);
                ll = mysql.query(sql);
                if (ll.size() > 0) {
                    one.put("dets", RelaInfo(ll));
                } else {
                    one.put("dets", new JSONArray());
                }


            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            }
            back.add(one);
        }
        return back;

    }


    //******UPDATE*******
    private JSONObject updateDingTempGroup(JSONObject data, String remoteAddr) {
        InfoModelHelper mysql = null;
        JSONObject back = ErrNo.set(0);
        try {
            mysql = InfoModelPool.getModel();
            String sql = "";
            String id = "";
            String group_name = "";
            String description = "";
            String opt_user = "";
            if (data.containsKey("id") && data.getString("id").length() > 0) {
                id = data.getString("id");
            } else {
                return ErrNo.set(null, 2, "缺少参数：id");
            }

            if (data.containsKey("group_name") && data.getString("group_name").length() > 0) {
                group_name = data.getString("group_name");
                sql = sql + " group_name='" + group_name + "' , ";
            }
            if (data.containsKey("description")) {
                description = data.getString("description");
                sql = sql + " description='" + description + "' , ";
            }
            if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
                opt_user = data.getString("opt_user");
            } else {
                return ErrNo.set(null, 2, "缺少参数：opt_user");
            }
            String sqls = "update ding_temp_group set " + sql + " isdelete=0  where id='" + id + "'";
            mysql.update(sqls);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return back;
    }

    //*********delete**********
    private JSONObject deleteDingTempGroup(JSONObject data, String remoteAddr) {
        String id = "";
        String opt_user = "";
        if (data.containsKey("id") && data.getString("id").length() > 0) {
            id = data.getString("id");
        } else {
            return ErrNo.set(null, 2, "缺少参数：id");
        }
        if (data.containsKey("opt_user") && data.getString("opt_user").length() > 0) {
            opt_user = data.getString("opt_user");
        } else {
            return ErrNo.set(null, 2, "缺少参数：opt_user");
        }
        InfoModelHelper mysql = null;
        try {
            mysql = InfoModelPool.getModel();
            String sqls =
                    "update ding_temp_group set isdelete =1,delete_user='" + opt_user + "',delete_time='" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "' where id='" + id + "'";
            mysql.update(sqls);
            sqls =
                    "update ding_temp set isdelete =1 where group_id='" + id + "'";
            mysql.update(sqls);

        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
            return ErrNo.set(null, 2, Lib.getTrace(ex));
        } finally {
            InfoModelPool.putModel(mysql);
        }
        return ErrNo.set(0);
    }

}
