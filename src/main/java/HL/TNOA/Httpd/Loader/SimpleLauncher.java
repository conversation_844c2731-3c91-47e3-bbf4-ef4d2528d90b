package HL.TNOA.Httpd.Loader;

import HL.TNOA.Httpd.Loader.archive.Archive;

import java.util.ArrayList;
import java.util.List;

public class SimpleLauncher extends Launcher {

	public static void main(String[] args) throws Exception {
		SimpleLauncher launcher = new SimpleLauncher();
		launcher.launch(args);
	}

	@Override
	protected String getMainClass() throws Exception {
		throw new IllegalStateException(
				"No 'Start-Class' manifest entry specified in " + this);
	}

	@Override
	protected String getMainClass(String mainClass) throws Exception {
		if (mainClass == null) {
			throw new IllegalStateException(
					"No 'Start-Class' manifest entry specified in " + this);
		}
		return mainClass;
	}

	@Override
	protected List<Archive> getClassPathArchives() throws Exception {
		List<Archive> archives = new ArrayList<>();
		return archives;
	}

}
