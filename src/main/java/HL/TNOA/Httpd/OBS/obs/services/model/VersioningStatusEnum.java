/**
 * Copyright 2019 Huawei Technologies Co.,Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License.  You may obtain a copy of the
 * License at
 * 
 * http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

package HL.TNOA.Httpd.OBS.obs.services.model;

/**
 * Versioning status
 *
 */
public enum VersioningStatusEnum {

    /**
     * Suspended
     */
    SUSPENDED("Suspended"),

    /**
     * Enabled
     */
    ENABLED("Enabled");

    private String code;

    private VersioningStatusEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static VersioningStatusEnum getValueFromCode(String code) {
        for (VersioningStatusEnum val : VersioningStatusEnum.values()) {
            if (val.code.equals(code)) {
                return val;
            }
        }
        return null;
    }

}
