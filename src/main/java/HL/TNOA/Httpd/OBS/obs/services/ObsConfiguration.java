package HL.TNOA.Httpd.OBS.obs.services;

import HL.TNOA.Httpd.OBS.obs.services.model.AuthTypeEnum;
import HL.TNOA.Httpd.OBS.obs.services.model.HttpProtocolTypeEnum;
import okhttp3.Dispatcher;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.TrustManagerFactory;

public class ObsConfiguration implements Cloneable {
  private int connectionTimeout = 60000;
  
  private int maxConnections = 1000;
  
  private int maxErrorRetry = 3;
  
  private int socketTimeout = 60000;
  
  private int endpointHttpPort = 80;
  
  private int endpointHttpsPort = 443;
  
  private boolean httpsOnly = true;
  
  private String endPoint = "";
  
  private boolean pathStyle = false;
  
  private boolean validateCertificate = false;
  
  private boolean verifyResponseContentType = true;
  
  private boolean isStrictHostnameVerification = false;
  
  private int uploadStreamRetryBufferSize = -1;
  
  private int socketWriteBufferSize = -1;
  
  private int socketReadBufferSize = -1;
  
  private int readBufferSize = -1;
  
  private int writeBufferSize = -1;
  
  private int idleConnectionTime = 30000;
  
  private int maxIdleConnections = 1000;
  
  private AuthTypeEnum authType = AuthTypeEnum.OBS;
  
  private boolean keepAlive = true;
  
  private String signatString = "";
  
  private String defaultBucketLocation = "";
  
  private boolean authTypeNegotiation = true;
  
  private boolean cname = false;
  
  private String delimiter = "/";
  
  private HttpProtocolTypeEnum httpProtocolType = HttpProtocolTypeEnum.HTTP1_1;
  
  private String xmlDocumentBuilderFactoryClass = "com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl";
  
  private int localAuthTypeCacheCapacity = 50;
  
  private HttpProxyConfiguration httpProxy;
  
  private KeyManagerFactory keyManagerFactory;
  
  private TrustManagerFactory trustManagerFactory;
  
  private int bufferSize;
  
  private boolean isNio;
  
  private boolean useReaper;
  
  private int connectionRequestTimeout;
  
  private String sslProvider;
  
  public String getDelimiter() {
    return this.delimiter;
  }
  
  public void setDelimiter(String delimiter) {
    this.delimiter = delimiter;
  }
  
  @Deprecated
  public String getSignatString() {
    return this.signatString;
  }
  
  @Deprecated
  public void setSignatString(String signatString) {
    this.signatString = signatString;
  }
  
  @Deprecated
  public String getDefaultBucketLocation() {
    return this.defaultBucketLocation;
  }
  
  @Deprecated
  public void setDefaultBucketLocation(String defaultBucketLocation) {
    this.defaultBucketLocation = defaultBucketLocation;
  }
  
  @Deprecated
  public int getBufferSize() {
    return this.bufferSize;
  }
  
  @Deprecated
  public void setBufferSize(int bufferSize) {
    this.bufferSize = bufferSize;
  }
  
  public boolean isDisableDnsBucket() {
    return isPathStyle();
  }
  
  public void setDisableDnsBucket(boolean disableDns) {
    setPathStyle(disableDns);
  }
  
  public int getSocketReadBufferSize() {
    return this.socketReadBufferSize;
  }
  
  public void setSocketReadBufferSize(int socketReadBufferSize) {
    this.socketReadBufferSize = socketReadBufferSize;
  }
  
  public int getSocketWriteBufferSize() {
    return this.socketWriteBufferSize;
  }
  
  public void setSocketWriteBufferSize(int socketWriteBufferSize) {
    this.socketWriteBufferSize = socketWriteBufferSize;
  }
  
  @Deprecated
  public int getConnectionRequestTimeout() {
    return this.connectionRequestTimeout;
  }
  
  @Deprecated
  public void setConnectionRequestTimeout(int connectionRequestTimeout) {
    this.connectionRequestTimeout = connectionRequestTimeout;
  }
  
  @Deprecated
  public void disableNio() {
    this.isNio = false;
  }
  
  @Deprecated
  public void enableNio() {
    this.isNio = true;
  }
  
  @Deprecated
  public boolean isNio() {
    return this.isNio;
  }
  
  @Deprecated
  public boolean isUseReaper() {
    return this.useReaper;
  }
  
  @Deprecated
  public void setUseReaper(boolean useReaper) {
    this.useReaper = useReaper;
  }
  
  public KeyManagerFactory getKeyManagerFactory() {
    return this.keyManagerFactory;
  }
  
  public void setKeyManagerFactory(KeyManagerFactory keyManagerFactory) {
    this.keyManagerFactory = keyManagerFactory;
  }
  
  public TrustManagerFactory getTrustManagerFactory() {
    return this.trustManagerFactory;
  }
  
  public void setTrustManagerFactory(TrustManagerFactory trustManagerFactory) {
    this.trustManagerFactory = trustManagerFactory;
  }
  
  public boolean isStrictHostnameVerification() {
    return this.isStrictHostnameVerification;
  }
  
  public void setIsStrictHostnameVerification(boolean isStrictHostnameVerification) {
    this.isStrictHostnameVerification = isStrictHostnameVerification;
  }
  
  public boolean isPathStyle() {
    return this.pathStyle;
  }
  
  public void setPathStyle(boolean pathStyle) {
    this.pathStyle = pathStyle;
  }
  
  public int getConnectionTimeout() {
    return this.connectionTimeout;
  }
  
  public void setConnectionTimeout(int connectionTimeout) {
    this.connectionTimeout = connectionTimeout;
  }
  
  public int getMaxConnections() {
    return this.maxConnections;
  }
  
  public void setMaxConnections(int maxConnections) {
    this.maxConnections = maxConnections;
  }
  
  public int getMaxErrorRetry() {
    return this.maxErrorRetry;
  }
  
  public void setMaxErrorRetry(int maxErrorRetry) {
    this.maxErrorRetry = maxErrorRetry;
  }
  
  public int getSocketTimeout() {
    return this.socketTimeout;
  }
  
  public void setSocketTimeout(int socketTimeout) {
    this.socketTimeout = socketTimeout;
  }
  
  public String getEndPoint() {
    if (this.endPoint == null || this.endPoint.trim().equals(""))
      throw new IllegalArgumentException("EndPoint is not set"); 
    return this.endPoint.trim();
  }
  
  public void setEndPoint(String endPoint) {
    this.endPoint = endPoint;
  }
  
  public int getEndpointHttpPort() {
    return this.endpointHttpPort;
  }
  
  public void setEndpointHttpPort(int endpointHttpPort) {
    this.endpointHttpPort = endpointHttpPort;
  }
  
  public int getEndpointHttpsPort() {
    return this.endpointHttpsPort;
  }
  
  public void setEndpointHttpsPort(int endpointHttpsPort) {
    this.endpointHttpsPort = endpointHttpsPort;
  }
  
  public void setHttpsOnly(boolean httpsOnly) {
    this.httpsOnly = httpsOnly;
  }
  
  public boolean isHttpsOnly() {
    return this.httpsOnly;
  }
  
  public ObsConfiguration clone() throws CloneNotSupportedException {
    return (ObsConfiguration)super.clone();
  }
  
  public HttpProxyConfiguration getHttpProxy() {
    return this.httpProxy;
  }
  
  public void setHttpProxy(HttpProxyConfiguration httpProxy) {
    this.httpProxy = httpProxy;
  }
  
  @Deprecated
  public void setHttpProxy(String proxyAddr, int proxyPort, String userName, String password, String domain) {
    this.httpProxy = new HttpProxyConfiguration(proxyAddr, proxyPort, userName, password, domain);
  }
  
  public void setHttpProxy(String proxyAddr, int proxyPort, String userName, String password) {
    this.httpProxy = new HttpProxyConfiguration(proxyAddr, proxyPort, userName, password, null);
  }
  
  @Deprecated
  public void setUploadStreamRetryBufferSize(int uploadStreamRetryBufferSize) {
    this.uploadStreamRetryBufferSize = uploadStreamRetryBufferSize;
  }
  
  @Deprecated
  public int getUploadStreamRetryBufferSize() {
    return this.uploadStreamRetryBufferSize;
  }
  
  public boolean isValidateCertificate() {
    return this.validateCertificate;
  }
  
  public void setValidateCertificate(boolean validateCertificate) {
    this.validateCertificate = validateCertificate;
  }
  
  public boolean isVerifyResponseContentType() {
    return this.verifyResponseContentType;
  }
  
  public void setVerifyResponseContentType(boolean verifyResponseContentType) {
    this.verifyResponseContentType = verifyResponseContentType;
  }
  
  public int getReadBufferSize() {
    return this.readBufferSize;
  }
  
  public void setReadBufferSize(int readBufferSize) {
    this.readBufferSize = readBufferSize;
  }
  
  public int getWriteBufferSize() {
    return this.writeBufferSize;
  }
  
  public void setWriteBufferSize(int writeBufferSize) {
    this.writeBufferSize = writeBufferSize;
  }
  
  public int getIdleConnectionTime() {
    return this.idleConnectionTime;
  }
  
  public void setIdleConnectionTime(int idleConnectionTime) {
    this.idleConnectionTime = idleConnectionTime;
  }
  
  public int getMaxIdleConnections() {
    return this.maxIdleConnections;
  }
  
  public void setMaxIdleConnections(int maxIdleConnections) {
    this.maxIdleConnections = maxIdleConnections;
  }
  
  public AuthTypeEnum getAuthType() {
    return this.authType;
  }
  
  public void setAuthType(AuthTypeEnum authType) {
    this.authType = authType;
  }
  
  public boolean isKeepAlive() {
    return this.keepAlive;
  }
  
  public void setKeepAlive(boolean keepAlive) {
    this.keepAlive = keepAlive;
  }
  
  public boolean isAuthTypeNegotiation() {
    return this.authTypeNegotiation;
  }
  
  public void setAuthTypeNegotiation(boolean authTypeNegotiation) {
    this.authTypeNegotiation = authTypeNegotiation;
  }
  
  public boolean isCname() {
    return this.cname;
  }
  
  public void setCname(boolean cname) {
    this.cname = cname;
  }
  
  public String getSslProvider() {
    return this.sslProvider;
  }
  
  public void setSslProvider(String sslProvider) {
    this.sslProvider = sslProvider;
  }
  
  public HttpProtocolTypeEnum getHttpProtocolType() {
    return this.httpProtocolType;
  }
  
  public void setHttpProtocolType(HttpProtocolTypeEnum httpProtocolType) {
    this.httpProtocolType = httpProtocolType;
  }
  
  public String getXmlDocumentBuilderFactoryClass() {
    return this.xmlDocumentBuilderFactoryClass;
  }
  
  public void setXmlDocumentBuilderFactoryClass(String xmlDocumentBuilderFactoryClass) {
    this.xmlDocumentBuilderFactoryClass = xmlDocumentBuilderFactoryClass;
  }
  
  public int getLocalAuthTypeCacheCapacity() {
    return this.localAuthTypeCacheCapacity;
  }
  
  public void setLocalAuthTypeCacheCapacity(int localAuthTypeCacheCapacity) {
    this.localAuthTypeCacheCapacity = localAuthTypeCacheCapacity;
  }
  
  public Dispatcher getHttpDispatcher() {
    return null;
  }
}
