/**
* Copyright 2019 Huawei Technologies Co.,Ltd.
* Licensed under the Apache License, Version 2.0 (the "License"); you may not use
* this file except in compliance with the License.  You may obtain a copy of the
* License at
* 
* http://www.apache.org/licenses/LICENSE-2.0
* 
* Unless required by applicable law or agreed to in writing, software distributed
* under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
* CONDITIONS OF ANY KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations under the License.
**/

package HL.TNOA.Httpd.OBS.obs.services.model;

import java.util.ArrayList;
import java.util.List;

/**
 * Response to a request for listing buckets
 *
 */
public class ListBucketsResult extends HeaderResponse {
    private List<ObsBucket> buckets;

    private Owner owner;

    public ListBucketsResult(List<ObsBucket> buckets, Owner owner) {
        this.buckets = buckets;
        this.owner = owner;
    }

    public List<ObsBucket> getBuckets() {
        if (buckets == null) {
            buckets = new ArrayList<ObsBucket>();
        }
        return buckets;
    }

    public Owner getOwner() {
        return owner;
    }

    @Override
    public String toString() {
        return "ListBucketsResult [buckets=" + buckets + ", owner=" + owner + "]";
    }

}
