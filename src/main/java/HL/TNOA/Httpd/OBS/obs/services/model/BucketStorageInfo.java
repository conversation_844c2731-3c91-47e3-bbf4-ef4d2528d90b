/**
* Copyright 2019 Huawei Technologies Co.,Ltd.
* Licensed under the Apache License, Version 2.0 (the "License"); you may not use
* this file except in compliance with the License.  You may obtain a copy of the
* License at
* 
* http://www.apache.org/licenses/LICENSE-2.0
* 
* Unless required by applicable law or agreed to in writing, software distributed
* under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
* CONDITIONS OF ANY KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations under the License.
**/

package HL.TNOA.Httpd.OBS.obs.services.model;

/**
 * Bucket storage information
 */
public class BucketStorageInfo extends HeaderResponse {
    private long size;

    private long objectNum;

    /**
     * Obtain the bucket quota (in bytes).
     * 
     * @return Bucket size
     */
    public long getSize() {
        return size;
    }

    /**
     * Set the bucket size (in bytes).
     * 
     * @param storageSize
     *            Bucket size
     */
    public void setSize(long storageSize) {
        this.size = storageSize;
    }

    /**
     * Obtain the number of objects in the bucket.
     * 
     * @return Number of objects in the bucket
     */
    public long getObjectNumber() {
        return objectNum;
    }

    /**
     * Set the number of objects in the bucket.
     * 
     * @param objectNumber
     *            Number of objects in the bucket
     */
    public void setObjectNumber(long objectNumber) {
        this.objectNum = objectNumber;
    }

    @Override
    public String toString() {
        return "BucketStorageInfo [size=" + size + ", objectNum=" + objectNum + "]";
    }

}
