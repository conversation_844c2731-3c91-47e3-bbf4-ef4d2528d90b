/**
 * Copyright 2019 Huawei Technologies Co.,Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License.  You may obtain a copy of the
 * License at
 * 
 * http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

package HL.TNOA.Httpd.OBS.obs.services.model;

import java.util.Date;


public class V4TemporarySignatureRequest extends TemporarySignatureRequest {

    public V4TemporarySignatureRequest() {
    }

    public V4TemporarySignatureRequest(HttpMethodEnum method, long expires) {
        super(method, null, null, null, expires);
    }

    public V4TemporarySignatureRequest(HttpMethodEnum method, String bucketName, String objectKey,
            SpecialParamEnum specialParam, long expires) {
        super(method, bucketName, objectKey, specialParam, expires, null);
    }

    public V4TemporarySignatureRequest(HttpMethodEnum method, String bucketName, String objectKey,
            SpecialParamEnum specialParam, long expires, Date requestDate) {
        super(method, bucketName, objectKey, specialParam, expires, requestDate);
    }

}
