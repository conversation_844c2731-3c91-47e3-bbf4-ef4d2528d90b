package HL.TNOA.Httpd.OBS.obs.services;

import HL.TNOA.Httpd.OBS.obs.log.ILogger;
import HL.TNOA.Httpd.OBS.obs.log.InterfaceLogBean;
import HL.TNOA.Httpd.OBS.obs.log.LoggerBuilder;
import HL.TNOA.Httpd.OBS.obs.services.exception.ObsException;
import HL.TNOA.Httpd.OBS.obs.services.internal.ObsProperties;
import HL.TNOA.Httpd.OBS.obs.services.internal.ObsService;
import HL.TNOA.Httpd.OBS.obs.services.internal.ServiceException;
import HL.TNOA.Httpd.OBS.obs.services.internal.security.ProviderCredentials;
import HL.TNOA.Httpd.OBS.obs.services.internal.utils.AccessLoggerUtils;
import HL.TNOA.Httpd.OBS.obs.services.internal.utils.ServiceUtils;
import HL.TNOA.Httpd.OBS.obs.services.internal.xml.OBSXMLBuilder;
import HL.TNOA.Httpd.OBS.obs.services.model.*;
import com.jamesmurty.utils.BaseXMLBuilder;

import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public abstract class AbstractClient extends ObsService implements Closeable, IObsClient, IFSClient {
  private static final ILogger ILOG = LoggerBuilder.getLogger(AbstractClient.class);
  
  static {
    BaseXMLBuilder.failIfExternalEntityParsingCannotBeConfigured = false;
  }
  
  protected void init(String accessKey, String secretKey, String securityToken, ObsConfiguration config) {
    InterfaceLogBean reqBean = new InterfaceLogBean("ObsClient", config.getEndPoint(), "");
    ProviderCredentials credentials = new ProviderCredentials(accessKey, secretKey, securityToken);
    ObsProperties obsProperties = ServiceUtils.changeFromObsConfiguration(config);
    credentials.setAuthType(config.getAuthType());
    credentials.setLocalAuthTypeCacheCapacity(config.getLocalAuthTypeCacheCapacity());
    this.obsProperties = obsProperties;
    this.credentials = credentials;
    this.keyManagerFactory = config.getKeyManagerFactory();
    this.trustManagerFactory = config.getTrustManagerFactory();
    if (isAuthTypeNegotiation())
      getProviderCredentials().setIsAuthTypeNegotiation(true); 
    initHttpClient(config.getHttpDispatcher());
    OBSXMLBuilder.setXmlDocumentBuilderFactoryClass(config.getXmlDocumentBuilderFactoryClass());
    reqBean.setRespTime(new Date());
    reqBean.setResultCode("0");
    if (ILOG.isInfoEnabled())
      ILOG.info(reqBean); 
    if (ILOG.isWarnEnabled()) {
      String ep;
      StringBuilder sb = new StringBuilder("[OBS SDK Version=");
      sb.append("3.22.12");
      sb.append("];");
      sb.append("[Endpoint=");
      if (getHttpsOnly()) {
        ep = "https://" + getEndpoint() + ":" + getHttpsPort() + "/";
      } else {
        ep = "http://" + getEndpoint() + ":" + getHttpPort() + "/";
      } 
      sb.append(ep);
      sb.append("];");
      sb.append("[Access Mode=");
      sb.append(isPathStyle() ? "Path" : "Virtul Hosting");
      sb.append("]");
      ILOG.warn(sb);
    } 
  }
  
  @Deprecated
  public String createSignedUrl(HttpMethodEnum method, String bucketName, String objectKey, SpecialParamEnum specialParam, Date expiryTime, Map<String, String> headers, Map<String, Object> queryParams) throws ObsException {
    return createSignedUrl(method, bucketName, objectKey, specialParam, (expiryTime == null) ? 
        300L : ((expiryTime.getTime() - System.currentTimeMillis()) / 1000L), 
        headers, queryParams);
  }
  
  @Deprecated
  public String createSignedUrl(HttpMethodEnum method, String bucketName, String objectKey, SpecialParamEnum specialParam, long expires, Map<String, String> headers, Map<String, Object> queryParams) {
    TemporarySignatureRequest request = new TemporarySignatureRequest();
    request.setMethod(method);
    request.setBucketName(bucketName);
    request.setObjectKey(objectKey);
    request.setSpecialParam(specialParam);
    request.setHeaders(headers);
    request.setQueryParams(queryParams);
    if (expires > 0L)
      request.setExpires(expires); 
    return createTemporarySignature(request).getSignedUrl();
  }
  
  @Deprecated
  public V4TemporarySignatureResponse createV4TemporarySignature(V4TemporarySignatureRequest request) {
    ServiceUtils.assertParameterNotNull(request, "V4TemporarySignatureRequest is null");
    InterfaceLogBean reqBean = new InterfaceLogBean("createV4TemporarySignature", getEndpoint(), "");
    try {
      TemporarySignatureResponse response = createV4TemporarySignature((TemporarySignatureRequest)request);
      V4TemporarySignatureResponse res = new V4TemporarySignatureResponse(response.getSignedUrl());
      res.getActualSignedRequestHeaders().putAll(response.getActualSignedRequestHeaders());
      return res;
    } catch (Exception e) {
      reqBean.setRespTime(new Date());
      if (ILOG.isErrorEnabled())
        ILOG.error(reqBean); 
      throw new ObsException(e.getMessage(), e);
    } 
  }
  
  @Deprecated
  public V4PostSignatureResponse createV4PostSignature(String acl, String contentType, long expires, String bucketName, String objectKey) throws ObsException {
    V4PostSignatureRequest request = new V4PostSignatureRequest(expires, new Date(), bucketName, objectKey);
    request.getFormParams().put("acl", acl);
    request.getFormParams().put("content-type", contentType);
    return createV4PostSignature(request);
  }
  
  @Deprecated
  public V4PostSignatureResponse createV4PostSignature(long expires, String bucketName, String objectKey) throws ObsException {
    V4PostSignatureRequest request = new V4PostSignatureRequest(expires, new Date(), bucketName, objectKey);
    return createV4PostSignature(request);
  }
  
  @Deprecated
  public V4PostSignatureResponse createV4PostSignature(V4PostSignatureRequest request) throws ObsException {
    ServiceUtils.assertParameterNotNull(request, "V4PostSignatureRequest is null");
    InterfaceLogBean reqBean = new InterfaceLogBean("createV4PostSignature", getEndpoint(), "");
    return (V4PostSignatureResponse)createPostSignature((PostSignatureRequest)request, reqBean, true);
  }
  
  public TemporarySignatureResponse createTemporarySignature(TemporarySignatureRequest request) {
    ServiceUtils.assertParameterNotNull(request, "TemporarySignatureRequest is null");
    InterfaceLogBean reqBean = new InterfaceLogBean("createTemporarySignature", getEndpoint(), "");
    try {
      return (getProviderCredentials().getLocalAuthType(request.getBucketName()) == AuthTypeEnum.V4) ? 
        createV4TemporarySignature(request) : createTemporarySignatureResponse((AbstractTemporarySignatureRequest)request);
    } catch (Exception e) {
      reqBean.setRespTime(new Date());
      if (ILOG.isErrorEnabled())
        ILOG.error(reqBean); 
      throw new ObsException(e.getMessage(), e);
    } 
  }
  
  public TemporarySignatureResponse createGetTemporarySignature(String bucketName, String objectKey, String prefix, Date expiryDate, Map<String, String> headers, Map<String, Object> queryParams) {
    try {
      PolicyTempSignatureRequest request = createPolicyGetRequest(bucketName, objectKey, prefix, headers, 
          queryParams);
      request.setExpiryDate(expiryDate);
      return createTemporarySignatureResponse((AbstractTemporarySignatureRequest)request);
    } catch (Exception e) {
      throw new ObsException(e.getMessage(), e);
    } 
  }
  
  public TemporarySignatureResponse createGetTemporarySignature(String bucketName, String objectKey, String prefix, long expires, Map<String, String> headers, Map<String, Object> queryParams) {
    try {
      PolicyTempSignatureRequest request = createPolicyGetRequest(bucketName, objectKey, prefix, headers, 
          queryParams);
      request.setExpires(expires);
      return createTemporarySignatureResponse((AbstractTemporarySignatureRequest)request);
    } catch (Exception e) {
      throw new ObsException(e.getMessage(), e);
    } 
  }
  
  public PostSignatureResponse createPostSignature(String acl, String contentType, long expires, String bucketName, String objectKey) throws ObsException {
    PostSignatureRequest request = new PostSignatureRequest(expires, new Date(), bucketName, objectKey);
    request.getFormParams().put(
        (getProviderCredentials().getLocalAuthType(bucketName) == 
        AuthTypeEnum.V4) ? "acl" : getIHeaders(bucketName).aclHeader(), acl);
    request.getFormParams().put("Content-Type", contentType);
    return createPostSignature(request);
  }
  
  public PostSignatureResponse createPostSignature(long expires, String bucketName, String objectKey) throws ObsException {
    PostSignatureRequest request = new PostSignatureRequest(expires, new Date(), bucketName, objectKey);
    return createPostSignature(request);
  }
  
  public PostSignatureResponse createPostSignature(PostSignatureRequest request) throws ObsException {
    ServiceUtils.assertParameterNotNull(request, "PostSignatureRequest is null");
    InterfaceLogBean reqBean = new InterfaceLogBean("createPostSignature", getEndpoint(), "");
    return createPostSignature(request, reqBean, 
        (getProviderCredentials().getLocalAuthType(request.getBucketName()) == AuthTypeEnum.V4));
  }
  
  protected abstract class ActionCallbackWithResult<T> {
    public abstract T action() throws ServiceException;
    
    void authTypeNegotiate(String bucketName) throws ServiceException {
      AuthTypeEnum authTypeEnum = (AuthTypeEnum)AbstractClient.this.getProviderCredentials()
        .getLocalAuthType().get(bucketName);
      if (authTypeEnum == null) {
        authTypeEnum = AbstractClient.this.getApiVersion(bucketName);
        AbstractClient.this.getProviderCredentials().setLocalAuthType(bucketName, authTypeEnum);
      } 
    }
  }
  
  protected <T> T doActionWithResult(String action, String bucketName, ActionCallbackWithResult<T> callback) throws ObsException {
    if (!isCname())
      ServiceUtils.assertParameterNotNull(bucketName, "bucketName is null"); 
    InterfaceLogBean reqBean = new InterfaceLogBean(action, getEndpoint(), "");
    try {
      long start = System.currentTimeMillis();
      if (isAuthTypeNegotiation())
        callback.authTypeNegotiate(bucketName); 
      T ret = callback.action();
      reqBean.setRespTime(new Date());
      reqBean.setResultCode("0");
      if (ILOG.isInfoEnabled()) {
        ILOG.info(reqBean);
        ILOG.info("ObsClient [" + action + "] cost " + (System.currentTimeMillis() - start) + " ms");
      } 
      return ret;
    } catch (ServiceException e) {
      ObsException ex = ServiceUtils.changeFromServiceException(e);
      if (ex.getResponseCode() >= 400 && ex.getResponseCode() < 500) {
        if (ILOG.isWarnEnabled()) {
          reqBean.setRespTime(new Date());
          reqBean.setResultCode(String.valueOf(e.getResponseCode()));
          ILOG.warn(reqBean);
        } 
      } else if (ILOG.isErrorEnabled()) {
        reqBean.setRespTime(new Date());
        reqBean.setResultCode(String.valueOf(ex.getResponseCode()));
        ILOG.error(reqBean);
      } 
      throw ex;
    } finally {
      AccessLoggerUtils.printLog();
    } 
  }
  
  public void refresh(String accessKey, String secretKey, String securityToken) {
    ProviderCredentials credentials = new ProviderCredentials(accessKey, secretKey, securityToken);
    credentials.setIsAuthTypeNegotiation(this.credentials.getIsAuthTypeNegotiation());
    credentials.setAuthType(this.credentials.getAuthType());
    credentials.setLocalAuthType(this.credentials.getLocalAuthType());
    setProviderCredentials(credentials);
  }
  
  public void close() throws IOException {
    shutdown();
  }
  
  public String base64Md5(InputStream is, long length, long offset) throws NoSuchAlgorithmException, IOException {
    return ServiceUtils.toBase64(ServiceUtils.computeMD5Hash(is, length, offset));
  }
  
  public String base64Md5(InputStream is) throws NoSuchAlgorithmException, IOException {
    return ServiceUtils.toBase64(ServiceUtils.computeMD5Hash(is));
  }
  
  private PolicyTempSignatureRequest createPolicyGetRequest(String bucketName, String objectKey, String prefix, Map<String, String> headers, Map<String, Object> queryParams) {
    PolicyTempSignatureRequest request = new PolicyTempSignatureRequest(HttpMethodEnum.GET, bucketName, objectKey);
    List<PolicyConditionItem> conditions = new ArrayList<>();
    PolicyConditionItem keyCondition = 
      new PolicyConditionItem(PolicyConditionItem.ConditionOperator.STARTS_WITH, 
        "key", prefix);
    String bucket = isCname() ? getEndpoint() : bucketName;
    PolicyConditionItem bucketCondition = 
      new PolicyConditionItem(PolicyConditionItem.ConditionOperator.EQUAL, 
        "bucket", bucket);
    conditions.add(keyCondition);
    conditions.add(bucketCondition);
    request.setConditions(conditions);
    request.setHeaders(headers);
    request.setQueryParams(queryParams);
    return request;
  }
  
  private PostSignatureResponse createPostSignature(PostSignatureRequest request, InterfaceLogBean reqBean, boolean isV4) {
    try {
      PostSignatureResponse response = createPostSignatureResponse(request, 
          isV4);
      reqBean.setRespTime(new Date());
      reqBean.setResultCode("0");
      if (ILOG.isInfoEnabled())
        ILOG.info(reqBean); 
      return response;
    } catch (Exception e) {
      reqBean.setRespTime(new Date());
      if (ILOG.isErrorEnabled())
        ILOG.error(reqBean); 
      throw new ObsException(e.getMessage(), e);
    } 
  }
}
