/**
 * Copyright 2019 Huawei Technologies Co.,Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License.  You may obtain a copy of the
 * License at
 * 
 * http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software distributed
 * under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
 * CONDITIONS OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

package HL.TNOA.Httpd.OBS.obs.services.model.select;

import HL.TNOA.Httpd.OBS.obs.services.internal.xml.OBSXMLBuilder;

/**
 * JSON input serialization format
 */
public class JsonInput extends XmlSerialization {
    private String type;

    /**
     * The type of JSON input
     * 
     * Default is LINES
     * 
     * @param type
     *      JSON file format type
     * 
     * @return Self
     */
    public JsonInput withType(JsonType type) {
        this.type = type.toString();
        return this;
    }

    /**
     * Formats the input settings into the XML request
     * 
     * @param xmlBuilder
     *              The xml serializer
     */
    @Override
    public void appendToXml(OBSXMLBuilder xmlBuilder) {
        OBSXMLBuilder jsonBuilder = xmlBuilder.elem("JSON");
        if (type != null) {
            jsonBuilder.elem("Type").text(type);
        }
    }
}
