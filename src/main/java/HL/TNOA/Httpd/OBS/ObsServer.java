package HL.TNOA.Httpd.OBS;



import HL.TNOA.Httpd.OBS.obs.services.ObsClient;
import HL.TNOA.Httpd.OBS.obs.services.model.*;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;

public class ObsServer {
    public static void main(String[] args) {
        String endPoint = "http://10.34.251.34:50101";
        String ak = "Q7D3OUVIRBEZEB4RWVSJ";
        String sk = "DufsrTprQLUgXgKQoRQXVZdugEHhPO70JXfaxn0I";
        String bucketName = "obs-qjjc-tyyh";
        String filePathName = "hl/login.png";
        upload(endPoint, ak, sk, bucketName, filePathName, "login.png");
        //showList(ak, sk, endPoint, bucketName);
    }

    public static void deleteFile(String ak, String sk, String endPoint, String bucketName, String filePName) {
        ObsClient obsClient = new ObsClient(ak, sk, endPoint);
        obsClient.deleteObject(bucketName, filePName);
        showList(ak, sk, endPoint, bucketName);
    }

    public static void download(String endPoint, String ak, String sk, String bucketName, String filePName) {
        ObsClient obsClient = new ObsClient(ak, sk, endPoint);
        ObsObject obsObj = obsClient.getObject(bucketName, filePName);
        System.out.println("Obj Content:");
        InputStream input = obsObj.getObjectContent();
        byte[] b = new byte[1024];
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            int len;
            while ((len = input.read(b)) != -1) bos.write(b, 0, len);
            System.out.println(new String(bos.toByteArray()));
            bos.close();
            input.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void SetWeb(String endPoint, String ak, String sk) {
        ObsClient obsClient = new ObsClient(ak, sk, endPoint);
        WebsiteConfiguration config = new WebsiteConfiguration();
        RedirectAllRequest redirectAll = new RedirectAllRequest();
        redirectAll.setHostName("www.example.com");
        redirectAll.setRedirectProtocol(ProtocolEnum.HTTP);
        config.setRedirectAllRequestsTo(redirectAll);
        obsClient.setBucketWebsite("hl-czoa", config);
    }

    public static boolean upload(String endPoint, String ak, String sk, String bucketName, String filePName,
                                 String fileName) {
        ObsClient obsClient = new ObsClient(ak, sk, endPoint);
        obsClient.putObject(bucketName, filePName, new File(fileName));
        obsClient.setObjectAcl(bucketName, filePName, AccessControlList.REST_CANNED_PUBLIC_READ);
        System.out.println("upload success-->");
        if (checkUp(ak, sk, endPoint, bucketName, filePName)) {
            return true;
        } else {
            return false;

        }
    }

    public static boolean checkUp(String ak, String sk, String endPoint, String bucketName, String filepName) {
        ObsClient obsClient = new ObsClient(ak, sk, endPoint);
        ObjectListing result = obsClient.listObjects(bucketName);
        for (ObsObject obsObj : result.getObjects()) {
            String kobj = obsObj.getObjectKey();
            if (kobj.equals(filepName)) {
                return true;
            }
        }
        return false;
    }

    public static void showList(String ak, String sk, String endPoint, String bucketName) {
        ObsClient obsClient = new ObsClient(ak, sk, endPoint);
        ObjectListing result = obsClient.listObjects(bucketName);
        for (ObsObject obsObj : result.getObjects()) {
            System.out.println(obsObj.getObjectKey());
            System.out.println(obsObj.getOwner());
        }
        BucketStorageInfo sInfo = obsClient.getBucketStorageInfo(bucketName);
        System.out.println("+ sInfo.getObjectNumber()+");
        System.out.println("+ sInfo.getSize()+");
        BucketQuota quota = obsClient.getBucketQuota(bucketName);
        System.out.println("+ quota.getBucketQuota()+");
    }
}
