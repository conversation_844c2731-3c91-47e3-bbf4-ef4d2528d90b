package HL.TNOA.Httpd.task;

import HL.TNOA.Crontab.Crontabs;
import HL.TNOA.Crontab.KillMysqlConn;
import HL.TNOA.Httpd.initBasicInfo;
import HL.TNOA.Httpd.initBasicInfo1;
import HL.TNOA.Lib.TNOAConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RunAfterStart {

    @EventListener(ApplicationReadyEvent.class)
    public void runAfterStart() {

        String enabledCron = TNOAConf.get("CrontabServ", "start");
        if ("true".equals(enabledCron)) {
            log.warn("启动定时任务");
            Thread thread = new Thread(new Crontabs(), "corn-task");
            thread.start();
            new Thread(new KillMysqlConn(),"关闭mysql超时连接").start();
        }

        log.info("定时任务启动结束");
    }

}
