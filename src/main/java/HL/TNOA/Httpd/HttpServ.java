package HL.TNOA.Httpd;


import HL.TNOA.Httpd.Loader.SimpleLauncher;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Start.HeartBeat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HttpServ {
    private static Logger logger = LoggerFactory.getLogger(HttpServ.class);

    public static void main(String[] args) {
        try {
            new Thread(new initBasicInfo()).start();
            new Thread(new initBasicInfo1()).start();
            HeartBeat heartbeat = new HeartBeat();
            heartbeat.heartbeat_init(HttpServ.class.getName());


            SimpleLauncher launcher = new SimpleLauncher();
            launcher.launch(new String[]{Httpsd.class.getName()});

           // new Thread(new ProxyServer()).start();


            while (true) {
                heartbeat.heartbeat();
                Lib.sleep(100);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }
    }
}
