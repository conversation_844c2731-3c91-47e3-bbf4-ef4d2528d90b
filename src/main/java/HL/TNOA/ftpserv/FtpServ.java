package HL.TNOA.ftpserv;

import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.TNOAConf;
import HL.TNOA.Start.HeartBeat;
import org.apache.ftpserver.DataConnectionConfigurationFactory;
import org.apache.ftpserver.FtpServer;
import org.apache.ftpserver.FtpServerFactory;
import org.apache.ftpserver.ftplet.Authority;
import org.apache.ftpserver.listener.ListenerFactory;
import org.apache.ftpserver.ssl.SslConfigurationFactory;
import org.apache.ftpserver.usermanager.impl.BaseUser;
import org.apache.ftpserver.usermanager.impl.WritePermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class FtpServ {
    private static Logger logger = LoggerFactory.getLogger(FtpServ.class);

    public static void main(String[] args) {


        try {
            HeartBeat heartbeat = new HeartBeat();
            heartbeat.heartbeat_init(FtpServ.class.getName());

            FtpServ serv = new FtpServ();
            serv.start();

            new Thread(new SendBKFiles()).start();
            new Thread(new MoveBKFiles()).start();

            while (true) {
                heartbeat.heartbeat();
                Lib.sleep(100);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }

    }

    public void start() throws Exception {
        // 创建ftp根目录
        File file;
        if (TNOAConf.get("ftpServ", "ftpHome") == null)
            file = new File(System.getProperty("user.dir") + "/" + "/ftpHome");
        else
            file = new File(TNOAConf.get("ftpServ", "ftpHome"));

        if (!file.isDirectory()) {
            file.delete();
        }
        if (!file.exists()) {
            file.mkdir();
        }

        // 关闭ipv6
        // cat /proc/sys/net/ipv6/conf/all/disable_ipv6
        // 在 /etc/sysctl.conf 增加下面几行，并重启。
        // #disable IPv6
        // net.ipv6.TNOAConf.all.disable_ipv6 = 1
        // net.ipv6.TNOAConf.default.disable_ipv6 = 1
        // net.ipv6.TNOAConf.lo.disable_ipv6 = 1
        // sysctl -p

        FtpServerFactory serverFactory = new FtpServerFactory();
        ListenerFactory listener = new ListenerFactory();
        int port;
        if (TNOAConf.get("ftpServ", "ftpPort") == null) {
            listener.setPort(56721);
            port = 56721;
        } else {
            listener.setPort(Integer.parseInt(TNOAConf.get("ftpServ", "ftpPort")));
            port = TNOAConf.getInt("ftpServ", "ftpPort");
        }


        DataConnectionConfigurationFactory dataconnect = new DataConnectionConfigurationFactory();
        if (TNOAConf.get("ftpServ", "PassivePorts") == null) {
            dataconnect.setPassivePorts("56722-56723");
        } else {
            dataconnect.setPassivePorts(TNOAConf.get("ftpServ", "PassivePorts"));
        }
        dataconnect.setIdleTime(60);

        listener.setDataConnectionConfiguration(dataconnect.createDataConnectionConfiguration());
        if (TNOAConf.get("ftpServ", "isFtps").equals("1")) {
            SslConfigurationFactory ssl = new SslConfigurationFactory();
            //java/jdk-*/bin/
            // keytool -genkey -alias ftp.keystore -keyalg RSA -validity 20000 -keystore
            // c:\opt\ftp.keystore
            // keytool -importkeystore -srckeystore c:\opt\ftp.keystore -destkeystore c:\opt\ftp.keystore
            // -deststoretype pkcs12
            ssl.setKeystoreFile(new File(TNOAConf.get("ftpServ", "keyPath")));
            ssl.setKeystorePassword(TNOAConf.get("ftpServ", "pwd"));
            listener.setSslConfiguration(ssl.createSslConfiguration());
            listener.setImplicitSsl(true);
        }
        serverFactory.addListener("default", listener.createListener());

        BaseUser user = new BaseUser();
        if (TNOAConf.get("ftpServ", "ftpUser") == null)
            user.setName("ffuser");
        else
            user.setName(TNOAConf.get("ftpServ", "ftpUser"));

        if (TNOAConf.get("ftpServ", "ftpPass") == null)
            user.setPassword("ff123");
        else
            user.setPassword(TNOAConf.get("ftpServ", "ftpPass"));

        if (TNOAConf.get("ftpServ", "ftpHome") == null)
            user.setHomeDirectory(System.getProperty("user.dir") + "/" + "ftpHome");
        else
            user.setHomeDirectory(System.getProperty("user.dir") + "/" + TNOAConf.get("ftpHome"));

        System.out.println("FTP_PORT " + port);
        System.out.println("FTP_urser " + user.getName());
        System.out.println("FTP_home " + user.getHomeDirectory());

        List<Authority> authorities = new ArrayList<Authority>();
        authorities.add(new WritePermission());
        user.setAuthorities(authorities);

        serverFactory.getUserManager().save(user);

        FtpServer server = serverFactory.createServer();
        server.start();
    }
}
