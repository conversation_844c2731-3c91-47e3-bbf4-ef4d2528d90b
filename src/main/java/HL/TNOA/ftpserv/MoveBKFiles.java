package HL.TNOA.ftpserv;

import HL.TNOA.Lib.BashExecutor;
import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.TNOAConf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;

public class MoveBKFiles implements Runnable {
    private static Logger logger = LoggerFactory.getLogger(MoveBKFiles.class);
    private static HashMap<String, String> bigs = new HashMap<>();

    @Override
    public void run() {


        while (true) {
            try {
                File file = null;
                String ftpFile = "";
                if (TNOAConf.get("ftpServ", "ftpHome") == null) {
                    ftpFile = System.getProperty("user.dir") + "/" + "/ftpHome/";
                    file = new File(System.getProperty("user.dir") + "/" + "/ftpHome");
                } else {
                    ftpFile = TNOAConf.get("ftpServ", "ftpHome");
                    file = new File(TNOAConf.get("ftpServ", "ftpHome"));
                }
                String ypaht =
                        new SimpleDateFormat("yyyy").format(new Date()) + "/" + new SimpleDateFormat("MM").format(new Date()) + "/";

                String[] files = file.list();
                if (files.length > 0) {
                    for (String name : files) {

                        if (name.endsWith(".zip")) {

                            String fileName = FileDeal.zipToFile(ftpFile + name,
                                    TNOAConf.get("file", "img_path") + ypaht);
                            logger.warn(fileName + "-size->" + new File(fileName).length());

                            new File(ftpFile + name).delete();


                        } else if (name.endsWith(".uzip")) {
                        } else {
                            BashExecutor bash = new BashExecutor();


                            String cmd = "mv " + ftpFile + name + " " + TNOAConf.get("file", "img_path") + ypaht;
                            logger.warn(cmd);
                            bash.exec(cmd, -1, true);
                            new File(ftpFile + name).delete();
                        }


                    }

                }
                Thread.sleep(10 * 1000);
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            }
        }

    }
}
