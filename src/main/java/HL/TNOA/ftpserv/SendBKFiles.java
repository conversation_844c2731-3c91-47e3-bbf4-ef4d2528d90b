package HL.TNOA.ftpserv;

import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.TNOAConf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;

public class
SendBKFiles implements Runnable {
    private static Logger logger = LoggerFactory.getLogger(SendBKFiles.class);

    @Override
    public void run() {
        String bkPath = TNOAConf.get("file", "bk_path");

        while (true) {
            try {
                File file = new File(bkPath);
                String[] files = file.list();
                if (files.length > 0) {
                    for (String name : files) {

                        String fpath = bkPath + name;
                        logger.warn(fpath + "-size->" + new File(fpath).length());


                        String zipFile = FileDeal.fileZip(bkPath, name);

                        new File(fpath).delete();

                        sendFtp(zipFile);


                    }

                }
                Thread.sleep(10 * 1000);
            } catch (Exception ex) {
                logger.error(Lib.getTrace(ex));
            }
        }


    }

    public static void sendFtp(String filename) {
        logger.warn(filename);


        String[] ftplist = TNOAConf.get("ftpServ", "bkPath").split(",");

        for (int f = 0; f < ftplist.length; f++) {
            String one = ftplist[f];

            try {

                String url = TNOAConf.get(one, "FtpAddr");
                int port = TNOAConf.getInt(one, "FtpPort");
                String user = TNOAConf.get(one, "FtpUser");
                String pass = TNOAConf.get(one, "FtpPass");
                String home = TNOAConf.get(one, "FtpHome");


                int j = 0;


                for (j = 0; j < 3; j++) {
                    if (FtpHelper.uploadFile2(url, port, user, pass, home, filename, ".zip")) {
                        logger.warn("[" + url + "] success " + filename);

                        break;
                    }
                }

                if (j == 3) {
                    logger.warn("[" + url + "] fail " + filename);
                    continue;
                }


            } catch (Exception e) {
                logger.error(Lib.getTrace(e));
            }
        }


        try {
            if (new File(filename).exists()) {
                new File(filename).delete();
            }
        } catch (Exception ex) {
            logger.error(Lib.getTrace(ex));
        }

    }

}

