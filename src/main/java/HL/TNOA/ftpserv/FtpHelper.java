package HL.TNOA.ftpserv;

import HL.TNOA.Lib.Lib;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.commons.net.ftp.FTPSClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;

public class FtpHelper {
    private static Logger logger = LoggerFactory.getLogger(FtpHelper.class);
    private static int ftptimeout = 0;

    static {
        ftptimeout = 60 * 1000;
    }

    public static boolean uploadFile(String url, int port, String username, String password, String path,
                                     String filename) {
        boolean success = false;
        FTPClient ftp = new FTPClient();
        FileInputStream input;
        String name = "";
        try {
            input = new FileInputStream(filename);
            try {
                int reply;
                ftp.setConnectTimeout(1000 * ftptimeout);
                ftp.setDataTimeout(1000 * ftptimeout);
                ftp.setDefaultTimeout(1000 * ftptimeout);

                ftp.connect(url, port);
                ftp.login(username, password);
                reply = ftp.getReplyCode();
                if (!FTPReply.isPositiveCompletion(reply)) {
                    ftp.disconnect();
                    return success;
                }

                String[] arr = filename.split("/");
                name = arr[arr.length - 1];

                if (path.equals("/")) {
                    ftp.changeWorkingDirectory(path);
                } else {
                    String[] patharr = path.split("/");
                    for (String one : patharr) {
                        if (one.length() == 0) {
                            continue;
                        }
                        ftp.changeWorkingDirectory(one);
                    }
                }
                ftp.setControlEncoding("UTF-8");
                // 二进制
                ftp.setFileType(FTP.BINARY_FILE_TYPE);
                // 被动模式
                ftp.enterLocalPassiveMode();
                // 传输方式为流
                ftp.setFileTransferMode(FTP.STREAM_TRANSFER_MODE);
                logger.warn(name);
                success = ftp.storeFile(name, input);
                ftp.logout();
            } finally {
                input.close();
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (Exception ioe) {
                    logger.error(Lib.getTrace(ioe));
                }
            }
        }
        return success;
    }

    // 改名
    public static boolean uploadFile2(String url, int port, String username, String password, String path,
                                      String filename, String end) {
        boolean success = false;
        FTPClient ftp = new FTPClient();
        FileInputStream input;
        String name = "";
        try {
            input = new FileInputStream(filename);
            try {
                int reply;
                ftp.setConnectTimeout(1000 * ftptimeout);
                ftp.setDataTimeout(1000 * ftptimeout);
                ftp.setDefaultTimeout(1000 * ftptimeout);
                ftp.connect(url, port);
                ftp.login(username, password);
                reply = ftp.getReplyCode();

                if (!FTPReply.isPositiveCompletion(reply)) {
                    ftp.disconnect();
                    return success;
                }
                if (path.equals("/")) {
                    ftp.changeWorkingDirectory(path);
                } else {
                    String[] patharr = path.split("/");
                    for (String one : patharr) {
                        if (one.length() == 0) {
                            continue;
                        }
                        ftp.changeWorkingDirectory(one);
                    }
                }
                ftp.setControlEncoding("UTF-8");
                ftp.setFileType(FTP.BINARY_FILE_TYPE);
                String[] arr = filename.split("/");
                name = arr[arr.length - 1];
                ftp.enterLocalPassiveMode();
                success = ftp.storeFile(name, input);

                // 改名
                ftp.rename(name, name.split("\\.")[0] + end);
                ftp.logout();
            } finally {
                input.close();
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (Exception ioe) {
                    logger.error(Lib.getTrace(ioe));
                }
            }
        }
        return success;
    }

    // 改名
    public static boolean uploadFile3(String url, int port, String username, String password, String path,
                                      String filename) {
        boolean success = false;
        FTPClient ftp = new FTPClient();
        FileInputStream input;
        String name = "";
        try {
            input = new FileInputStream(filename);
            try {
                int reply;
                ftp.setConnectTimeout(1000 * ftptimeout);
                ftp.setDataTimeout(1000 * ftptimeout);
                ftp.setDefaultTimeout(1000 * ftptimeout);
                ftp.connect(url, port);
                ftp.login(username, password);
                reply = ftp.getReplyCode();

                if (!FTPReply.isPositiveCompletion(reply)) {
                    ftp.disconnect();
                    return success;
                }
                if (path.equals("/")) {
                    ftp.changeWorkingDirectory(path);
                } else {
                    String[] patharr = path.split("/");
                    for (String one : patharr) {
                        if (one.length() == 0) {
                            continue;
                        }
                        ftp.changeWorkingDirectory(one);
                    }
                }
                ftp.setControlEncoding("UTF-8");
                ftp.setFileType(FTP.BINARY_FILE_TYPE);
                String[] arr = filename.split("/");
                name = arr[arr.length - 1];
                ftp.enterLocalPassiveMode();
                success = ftp.storeFile(name, input);

                // 改名
                ftp.rename(name, name.split("\\.")[0] + ".txt");
                ftp.logout();
            } finally {
                input.close();
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (Exception ioe) {
                    logger.error(Lib.getTrace(ioe));
                }
            }
        }
        return success;
    }


    //ftps
    public static boolean uploadFileOfFtps(String url, int port, String username, String password, String path,
                                           String filename) {
        boolean success = false;
//		FTPSClient ftp = new FTPSClient("TLS", true);
        FTPSClient ftp = new FTPSClient();
        FileInputStream input;
        String name = "";
        try {
            input = new FileInputStream(filename);
            try {
                int reply;
                ftp.setConnectTimeout(1000 * ftptimeout);
                ftp.setDataTimeout(1000 * ftptimeout);
                ftp.setDefaultTimeout(1000 * ftptimeout);
                ftp.connect(url, port);
                ftp.login(username, password);

                // 二进制
                ftp.setFileType(FTP.BINARY_FILE_TYPE);
                // 被动模式
                ftp.enterLocalPassiveMode();
                ftp.execPROT("P");

                reply = ftp.getReplyCode();

                if (!FTPReply.isPositiveCompletion(reply)) {
                    ftp.disconnect();
                    return success;
                }

                String[] arr = filename.split("/");
                name = arr[arr.length - 1];

                if (path.equals("/")) {
                    ftp.changeWorkingDirectory(path);
                } else {
                    String[] patharr = path.split("/");
                    for (String one : patharr) {
                        if (one.length() == 0) {
                            continue;
                        }
                        ftp.changeWorkingDirectory(one);
                    }
                }
                ftp.setControlEncoding("UTF-8");
//				// 二进制
//				ftp.setFileType(FTP.BINARY_FILE_TYPE);
//				// 被动模式
//				ftp.enterLocalPassiveMode();
//				// 传输方式为流
//				ftp.setFileTransferMode(FTP.STREAM_TRANSFER_MODE);
//				ftp.execPROT("P");

                success = ftp.storeFile(name, input);
                ftp.logout();
            } finally {
                input.close();
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (Exception ioe) {
                    logger.error(Lib.getTrace(ioe));
                }
            }
        }
        return success;
    }

}
