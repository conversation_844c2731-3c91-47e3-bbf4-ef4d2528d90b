package HL.TNOA.ftpserv;

import HL.TNOA.Lib.Lib;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

public class FileDeal {
    private static Logger logger = LoggerFactory.getLogger(FileDeal.class);

    // sourcepath->��Ҫѹ����Ŀ¼
    // filename->ѹ��֮����ļ�Ŀ¼
    // dstpath->ѹ���ļ���ŵ�Ŀ¼
    public static boolean fileToZip(String sourcepath, String filename) {
        boolean flag = false;
        BufferedInputStream bis = null;
        ZipOutputStream zos = null;

        try {
            File sourceFile = new File(sourcepath);
            if (!sourceFile.exists()) {
                logger.warn(sourcepath + " is not exist!");
                return false;
            }

            File zipFile = new File(filename);
            if (zipFile.exists()) {
                logger.warn(zipFile + " is already exist!");
            } else {
                if (sourceFile.isDirectory()) {
                    File[] sourceFiles = sourceFile.listFiles();
                    if (sourceFiles == null || sourceFiles.length < 1) {
                        logger.warn("zip file is empty!");
                    } else {
                        zos = new ZipOutputStream(new BufferedOutputStream(new FileOutputStream(zipFile)));
                        byte[] bufs = new byte[1024 * 10];
                        for (int i = 0; i < sourceFiles.length; i++) {
                            try {
                                // 创建ZIP实体，并添加进压缩包
                                ZipEntry zipEntry = new ZipEntry(sourceFiles[i].getName());
                                zos.putNextEntry(zipEntry);
                                // 读取待压缩的文件并写进压缩包�?
                                bis = new BufferedInputStream(new FileInputStream(sourceFiles[i]), 1024 * 10);
                                int read = 0;
                                while ((read = bis.read(bufs, 0, 1024 * 10)) != -1) {
                                    zos.write(bufs, 0, read);
                                }
                            } finally {
                                if (bis != null) {
                                    bis.close();
                                }
                            }
                        }
                        flag = true;
                    }
                } else {
                    zos = new ZipOutputStream(new BufferedOutputStream(new FileOutputStream(zipFile)));
                    byte[] bufs = new byte[1024 * 10];
                    try {
                        // 创建ZIP实体，并添加进压缩包
                        ZipEntry zipEntry = new ZipEntry(sourceFile.getName());
                        zos.putNextEntry(zipEntry);
                        // 读取待压缩的文件并写进压缩包�?
                        bis = new BufferedInputStream(new FileInputStream(sourceFile), 1024 * 10);
                        int read = 0;
                        while ((read = bis.read(bufs, 0, 1024 * 10)) != -1) {
                            zos.write(bufs, 0, read);
                        }
                    } finally {
                        if (bis != null) {
                            bis.close();
                        }
                    }
                    flag = true;
                }

            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            try {
                if (zos != null) {
                    zos.close();
                }
            } catch (IOException e) {
                logger.error(Lib.getTrace(e));
            }
        }
        return flag;
    }

    // 解压
    public static String zipToFile(String zip_path, String unzip_path) {
        ZipInputStream zin = null;
        BufferedInputStream bis = null;
        ZipEntry entry;
        String fileName = "";
        try {
            zin = new ZipInputStream(new FileInputStream(zip_path));
            bis = new BufferedInputStream(zin);
            File Fout = null;
            while ((entry = zin.getNextEntry()) != null && !entry.isDirectory()) {
                Fout = new File(unzip_path, entry.getName());
                fileName = entry.getName();
                if (!Fout.exists()) {
                    (new File(Fout.getParent())).mkdirs();
                }
                FileOutputStream out = null;
                BufferedOutputStream bos = null;
                try {
                    out = new FileOutputStream(Fout);
                    bos = new BufferedOutputStream(out);
                    int b;
                    while ((b = bis.read()) != -1) {
                        bos.write(b);
                    }
                } finally {
                    bos.close();
                    out.close();
                }
                // logger.warn(Fout + "解压成功");
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return "";
        } finally {
            try {
                bis.close();
                zin.close();
            } catch (IOException e) {
                logger.error(Lib.getTrace(e));
            }
        }
        return fileName;
    }

    // 删除目录文件
    public static boolean deleteDir(String dir) {
        boolean flag = false;
        try {
            File file = new File(dir);
            if (!file.exists()) {
                return flag;
            }
            if (!file.isDirectory()) {
                return flag;
            }
            File[] files = file.listFiles();
            for (int i = 0; i < files.length; i++) {
                if (files[i].isFile()) {
                    files[i].delete();
                }
                if (files[i].isDirectory()) {
                    deleteDir(files[i].getPath());
                    files[i].delete();
                }
                flag = true;
            }
            logger.warn("-del.dir->" + dir);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        }
        return flag;
    }

    // 拷贝文件
    public static boolean copyfile(String oldpath, String topath) {
        BufferedReader br = null;
        BufferedWriter bw = null;
        boolean flag = false;
        try {
            File oldfile = new File(oldpath);
            String[] str = oldpath.split("/");
            String name = str[str.length - 1];
            br = new BufferedReader(new InputStreamReader(new FileInputStream(oldfile), "utf-8"));
            bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(new File(topath + name)), "utf-8"));
            String line = "";
            while ((line = br.readLine()) != null) {
                bw.write(line);
                bw.newLine();
                bw.flush();
            }
            flag = true;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    logger.error(Lib.getTrace(e));
                }
            }
            if (bw != null) {
                try {
                    bw.close();
                } catch (IOException e) {
                    logger.error(Lib.getTrace(e));
                }
            }
        }
        return flag;
    }

    // 拷贝文件+改名
    public static boolean copyfile2(String oldpath, String topath) {
        BufferedReader br = null;
        BufferedWriter bw = null;
        boolean flag = false;
        String newname = "";
        String orgname = "";
        try {
            File oldfile = new File(oldpath);
            String[] str = oldpath.split("/");
            String name = str[str.length - 1];
            newname = topath + name + ".tmp";
            orgname = topath + name;
            br = new BufferedReader(new InputStreamReader(new FileInputStream(oldfile), "utf-8"));
            bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(new File(newname)), "utf-8"));
            String line = "";
            while ((line = br.readLine()) != null) {
                bw.write(line);
                bw.newLine();
                bw.flush();
            }
            flag = true;
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
                if (bw != null) {
                    bw.close();
                }
                new File(newname).renameTo(new File(orgname));
            } catch (IOException e) {
                logger.error(Lib.getTrace(e));
            }
        }
        return flag;
    }

    // 获取文件行数
    public static int count(String path) {
        int num = 0;
        BufferedReader br = null;
        try {
            File file = new File(path);
            br = new BufferedReader(new FileReader(file));
            String line = "";
            while ((line = br.readLine()) != null && line.length() > 0) {
                num++;
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    logger.error(Lib.getTrace(e));
                }
            }
        }
        return num;
    }

    // 文件zip压缩，绝对路�?
    public static String fileZip2(String sourcename) {
        String zipfile = "";
        String endname = "";
        ZipOutputStream zos = null;
        int BUFFER = 2048;
        BufferedInputStream bis = null;
        try {
            zipfile = sourcename.split("\\.")[0] + ".zip";
            zos = new ZipOutputStream(new FileOutputStream(new File(zipfile)));
            ZipEntry entry = new ZipEntry(sourcename.substring(sourcename.lastIndexOf("/") + 1));
            zos.putNextEntry(entry);
            bis = new BufferedInputStream(new FileInputStream(new File(sourcename)));
            int count;
            byte data[] = new byte[BUFFER];
            while ((count = bis.read(data, 0, BUFFER)) != -1) {
                zos.write(data, 0, count);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            try {
                if (zos != null) {
                    zos.closeEntry();
                    zos.flush();
                    zos.close();
                }
                if (bis != null) {
                    bis.close();
                }

                long filelength = new File(zipfile).length();
                endname = zipfile.split("\\.")[0] + "_" + filelength + ".zip";
                new File(zipfile).renameTo(new File(endname));
            } catch (IOException e) {
                logger.error(Lib.getTrace(e));
            }
        }
        return endname;
    }

    // 文件zip压缩，相对路�?+改名
    public static String fileZip3(String sourcename) {
        String zipfile = "";
        String endname = "";
        ZipOutputStream zos = null;
        int BUFFER = 2048;
        BufferedInputStream bis = null;
        try {
            zipfile = "." + sourcename.split("\\.")[1] + ".zip";
            zos = new ZipOutputStream(new FileOutputStream(new File(zipfile)));
            ZipEntry entry = new ZipEntry(sourcename.substring(sourcename.lastIndexOf("/") + 1));
            zos.putNextEntry(entry);
            bis = new BufferedInputStream(new FileInputStream(new File(sourcename)));
            int count;
            byte data[] = new byte[BUFFER];
            while ((count = bis.read(data, 0, BUFFER)) != -1) {
                zos.write(data, 0, count);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            try {
                if (zos != null) {
                    zos.closeEntry();
                    zos.flush();
                    zos.close();
                }
                if (bis != null) {
                    bis.close();
                }

                long filelength = new File(zipfile).length();
                endname = "." + zipfile.split("\\.")[1] + "_" + filelength + ".zip.tmp";
                new File(zipfile).renameTo(new File(endname));
            } catch (IOException e) {
                logger.error(Lib.getTrace(e));
            }
        }
        return endname;
    }

    // 文件zip压缩,相对路径
    public static String fileZip(String path, String name) {
        logger.warn(path + name);
        String zipfile = "";
        String endname = "";
        String sourcename = path + name;
        ZipOutputStream zos = null;
        int BUFFER = 2048;
        BufferedInputStream bis = null;
        try {
            if (!new File(path + name).exists()) {
                return endname;
            }

            zipfile = System.currentTimeMillis() + ".uzip";

            zos = new ZipOutputStream(new FileOutputStream(new File(zipfile)));
            ZipEntry entry = new ZipEntry(sourcename.substring(sourcename.lastIndexOf("/") + 1));
            zos.putNextEntry(entry);
            bis = new BufferedInputStream(new FileInputStream(new File(sourcename)));
            int count;
            byte data[] = new byte[BUFFER];
            while ((count = bis.read(data, 0, BUFFER)) != -1) {
                zos.write(data, 0, count);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            try {
                if (zos != null) {
                    zos.closeEntry();
                    zos.flush();
                    zos.close();
                }
                if (bis != null) {
                    bis.close();
                }


                logger.warn(zipfile);
              /*  if (zipfile.startsWith(".")) {
                    endname = "." + zipfile.split("\\.")[1] + "_" + filelength + ".zip";
                } else {
                    endname = zipfile.split("\\.")[0] + "_" + filelength + ".zip";
                }*/
                new File(zipfile).renameTo(new File(endname));
                endname = zipfile;
            } catch (IOException e) {
                logger.error(Lib.getTrace(e));
            }
        }
        return endname;
    }

    // 文件zip压缩,相对路径
    public static String fileZip1(String sourcename) {
        String zipfile = "";
        String endname = "";
        ZipOutputStream zos = null;
        int BUFFER = 2048;
        BufferedInputStream bis = null;
        try {
            zipfile = "." + sourcename.split("\\.")[1] + ".zip";
            zos = new ZipOutputStream(new FileOutputStream(new File(zipfile)));
            ZipEntry entry = new ZipEntry(sourcename.substring(sourcename.lastIndexOf("/") + 1));
            zos.putNextEntry(entry);
            bis = new BufferedInputStream(new FileInputStream(new File(sourcename)));
            int count;
            byte data[] = new byte[BUFFER];
            while ((count = bis.read(data, 0, BUFFER)) != -1) {
                zos.write(data, 0, count);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            try {
                if (zos != null) {
                    zos.closeEntry();
                    zos.flush();
                    zos.close();
                }
                if (bis != null) {
                    bis.close();
                }

                long filelength = new File(zipfile).length();
                endname = "." + zipfile.split("\\.")[1] + "_" + filelength + ".zip";
                new File(zipfile).renameTo(new File(endname));
            } catch (IOException e) {
                logger.error(Lib.getTrace(e));
            }
        }
        return endname;
    }

    // 文件zip解压
    public static String fileunZip(String sourcename) {
        String txtfile = "";
        ZipInputStream zis = null;
        int BUFFER = 2048;
        BufferedOutputStream bos = null;
        String basepath = "";
        try {
            File ff = new File(sourcename);
            basepath = ff.getParent();
            zis = new ZipInputStream(new FileInputStream(new File(sourcename)));
            ZipEntry entry = null;
            while ((entry = zis.getNextEntry()) != null) {
                txtfile =
                        new File(basepath).getPath() + File.separator + entry.getName().substring(entry.getName().lastIndexOf("/") + 1);
                if (!entry.isDirectory()) {
                    try {
                        bos = new BufferedOutputStream(new FileOutputStream(new File(txtfile)));
                        int count;
                        byte data[] = new byte[BUFFER];
                        while ((count = zis.read(data, 0, BUFFER)) != -1) {
                            bos.write(data, 0, count);
                        }
                    } finally {
                        if (bos != null) {
                            bos.close();
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            try {
                if (zis != null) {
                    zis.closeEntry();
                    zis.close();
                }
            } catch (IOException e) {
                logger.error(Lib.getTrace(e));
            }
        }
        return txtfile;
    }

    // 文件zip压缩,相对路径
    public static String EconomicXtZip(String sourcename) {
        String zipfile = "";
        String endname = "";
        ZipOutputStream zos = null;
        int BUFFER = 2048;
        BufferedInputStream bis = null;
        try {
            zipfile = "." + sourcename.split("\\.")[1] + ".zip";
            zos = new ZipOutputStream(new FileOutputStream(new File(zipfile)));
            ZipEntry entry = new ZipEntry(sourcename.substring(sourcename.lastIndexOf("/") + 1));
            zos.putNextEntry(entry);
            bis = new BufferedInputStream(new FileInputStream(new File(sourcename)));
            int count;
            byte data[] = new byte[BUFFER];
            while ((count = bis.read(data, 0, BUFFER)) != -1) {
                zos.write(data, 0, count);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            try {
                if (zos != null) {
                    zos.closeEntry();
                    zos.flush();
                    zos.close();
                }
                if (bis != null) {
                    bis.close();
                }

                endname = "." + zipfile.split("\\.")[1] + ".zip";
                new File(zipfile).renameTo(new File(endname));
            } catch (IOException e) {
                logger.error(Lib.getTrace(e));
            }
        }
        return endname;
    }

    // 压缩 全路�?
    public static String fileZip_border(String sourcename) {
        // logger.warn("FILE.DEAL.sourcename" + sourcename);
        String zipfile = "";
        String endname = "";
        ZipOutputStream zos = null;
        int BUFFER = 2048;
        BufferedInputStream bis = null;
        try {
            if (!new File(sourcename).exists()) {
                return endname;
            }

            if (sourcename.contains("txt")) {
                zipfile = sourcename.replace("txt", "zip");
            } else {
                return endname;
            }

            // logger.warn("FILE.DEAL.zipfile" + zipfile);
            zos = new ZipOutputStream(new FileOutputStream(new File(zipfile)));
            ZipEntry entry = new ZipEntry(sourcename.substring(sourcename.lastIndexOf("/") + 1));
            zos.putNextEntry(entry);
            bis = new BufferedInputStream(new FileInputStream(new File(sourcename)));
            int count;
            byte data[] = new byte[BUFFER];
            while ((count = bis.read(data, 0, BUFFER)) != -1) {
                zos.write(data, 0, count);
            }
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
        } finally {
            try {
                if (zos != null) {
                    zos.closeEntry();
                    zos.flush();
                    zos.close();
                }
                if (bis != null) {
                    bis.close();
                }

                long filelength = new File(zipfile).length();
                endname = zipfile.replace(".zip", "_" + filelength + ".zip");
                new File(zipfile).renameTo(new File(endname));
            } catch (IOException e) {
                logger.error(Lib.getTrace(e));
            }
        }
        return endname;
    }

    public static void renameFile(String newName, String oldName) {
        File oldFile = new File(oldName);
        File newFile = new File(newName);
        if (oldFile.exists() && oldFile.isFile()) {
            oldFile.renameTo(newFile);
        }
    }
}