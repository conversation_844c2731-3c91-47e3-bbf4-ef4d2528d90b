package HL.TNOA.Check;


import HL.TNOA.Lib.Lib;
import HL.TNOA.Lib.ShellExec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

public class CheckRedis {
    private static Logger logger = LoggerFactory.getLogger(CheckRedis.class.getName());

    public static int check_redis(int port) {
        for (int _i = 0; _i < 3; _i++) {
            try {
                if (Lib.getOs() == Lib.Linux) {
                    //进程
                    String cmd = "ps -ef|grep redis|grep -v grep|grep " + port;
                    String sshlogs = ShellExec.exec(cmd, 5, false);
                    if (sshlogs.trim().length() == 0) {
                        logger.error("redis " + port + " process is not start");
                        Lib.sleep(1);
                        continue;
                    }
                    //端口
//                    cmd = "sudo netstat -ntlp|grep redis|grep " + port;
//                    sshlogs = ShellExec.exec(cmd, 5, false);
//                    if (sshlogs.trim().length() == 0) {
//                        logger.error("redis " + port + " port is not start");
//                        CommunityConf.sleep(1);
//                        continue;
//                    }
                }
                //连接性测试
                Jedis redis = new Jedis("127.0.0.1", port, 2000);
                redis.close();

                return 0;
            } catch (Exception e) {
                logger.error(Lib.getTrace(e));
            }
            Lib.sleep(1);
        }
        return -1;
    }

    public static int start_redis(int port) {
        try {
            logger.error("start redis " + port);
            //查看进程是否存在，存在kill
            String cmd = "ps -ef|grep -v grep|grep redis|grep " + port + "|awk '{print $2}'";
            String sshlogs = ShellExec.exec(cmd, 10, false);
            if (sshlogs.length() > 0) {
                for (String _kill : sshlogs.trim().split("\n")) {
                    if (_kill.trim().length() == 0) {
                        continue;
                    }
                    cmd = "kill -9 " + _kill;
                    ShellExec.exec(cmd, 5, false);
                }
            }

            String redisconf = System.getProperty("user.dir") + "/config/redis_30001.conf";
            String redisexe = System.getProperty("user.dir") + "/config/redis-server";

            logger.warn("ls " + redisconf);
            sshlogs = ShellExec.exec("ls " + redisconf, 5, false);
            logger.warn(sshlogs);
            if (sshlogs.indexOf(ShellExec.nonexist) > 0) {
                logger.error("redis " + port + " config " + redisconf + " file is not exist");
                return -1;
            }

            sshlogs = ShellExec.exec("ls " + redisexe, 5, false);
            if (sshlogs.indexOf(ShellExec.nonexist) > 0) {
                logger.error("redis " + port + " config " + redisexe + " file is not exist");
                return -1;
            }
            //启动
            cmd = redisexe + " " + redisconf;
            logger.warn(cmd);
            sshlogs = ShellExec.exec(cmd, 10, false);
            if (sshlogs.trim().length() > 0) {
                logger.error(sshlogs);
                return -1;
            }

            return check_redis(port);
        } catch (Exception e) {
            logger.error(Lib.getTrace(e));
            return -1;
        }
    }

}
